/**
 * 故事管理完整业务流程集成测试 - 企业级核心功能验证
 *
 * 核心功能验证：
 * 1. 故事创作完整流程（草稿→发布→编辑）
 * 2. 故事权限控制和可见性管理
 * 3. 故事搜索和分类筛选功能
 * 4. 故事内容安全和审核机制
 * 5. 故事统计数据和分析功能
 * 6. 故事备份和历史版本管理
 * 7. 批量操作和管理功能
 * 8. 协作写作和共享编辑功能
 *
 * 测试场景：
 * - 完整故事创作和发布流程
 * - 多用户协作和权限验证
 * - 内容安全和审核工作流
 * - 数据统计和分析报告
 * - 性能优化和缓存策略
 * - 故事搜索和发现机制
 */

import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import type { DataSource } from "typeorm";
import { getDataSourceToken } from "@nestjs/typeorm";
import request from "supertest";
import type { INestApplication } from "@nestjs/common";
import { AppModule } from "../../../src/app.module";
import { IntegrationTestDataFactory } from "../setup/database-setup";
import type { User } from "../../../src/modules/users/entities/user.entity";
import type { Story } from "../../../src/modules/stories/entities/story.entity";
import { StoriesService } from "../../../src/modules/stories/stories.service";
import { UsersService } from "../../../src/modules/users/users.service";
import { EnhancedRedisService } from "../../../src/common/services/enhanced-redis.service";
import { UploadService } from "../../../src/modules/upload/upload.service";
import { JwtAuthGuard } from "../../../src/modules/auth/guards/jwt-auth.guard";

describe("Story Management Workflow - Enterprise Integration Tests", () => {
  let app: INestApplication;
  let module: TestingModule;
  let dataSource: DataSource;
  let dataFactory: IntegrationTestDataFactory;
  let testStoriesService: StoriesService;
  let testUsersService: UsersService;
  let testRedisService: EnhancedRedisService;
  let testUploadService: UploadService;

  // 测试用户矩阵
  let author: User;
  let collaborator: User;
  let reader: User;
  let moderator: User;
  let publicUser: User;

  // 测试故事数据
  let draftStory: Story;
  let publishedStory: Story;
  let privateStory: Story;
  let testCollaborativeStory: Story;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({
        canActivate: jest.fn().mockReturnValue(true),
      })
      .compile();

    app = module.createNestApplication();
    await app.init();

    dataSource = module.get<DataSource>(getDataSourceToken());
    dataFactory = new IntegrationTestDataFactory(dataSource);
    testStoriesService = module.get<StoriesService>(StoriesService);
    testUsersService = module.get<UsersService>(UsersService);
    testRedisService = module.get<EnhancedRedisService>(EnhancedRedisService);
    testUploadService = module.get<UploadService>(UploadService);

    // 清理测试环境
    await dataFactory.clearDatabase();
  });

  afterAll(async () => {
    await dataFactory.clearDatabase();
    await app.close();
  });

  beforeEach(async () => {
    // 为每个测试创建干净的数据环境
    await dataFactory.clearTestData();

    // 创建测试用户矩阵
    author = await dataFactory.createUser({
      phone: "13800138001",
      nickname: "故事作者",
      isPhoneVerified: true,
      role: "author",
    });

    collaborator = await dataFactory.createUser({
      phone: "13800138002",
      nickname: "协作编辑",
      isPhoneVerified: true,
      role: "collaborator",
    });

    reader = await dataFactory.createUser({
      phone: "13800138003",
      nickname: "读者用户",
      isPhoneVerified: true,
      role: "reader",
    });

    moderator = await dataFactory.createUser({
      phone: "13800138004",
      nickname: "内容审核员",
      isPhoneVerified: true,
      role: "moderator",
    });

    publicUser = await dataFactory.createUser({
      phone: "13800138005",
      nickname: "公开用户",
      isPhoneVerified: true,
      role: "user",
    });
  });

  describe("📝 完整故事创作和发布流程", () => {
    it("should complete full story creation workflow from draft to published", async () => {
      // 1. 创建故事草稿
      const draftResponse = await request(app.getHttpServer())
        .post("/stories")
        .set("Authorization", `Bearer ${await getAuthToken(author)}`)
        .send({
          title: "我的大学生活回忆",
          content: "这是一个关于大学时光的温馨故事草稿...",
          status: "draft",
          visibility: "private",
          category: "campus_life",
          tags: ["大学", "友情", "青春"],
        })
        .expect(201);

      expect(draftResponse.body.success).toBe(true);
      expect(draftResponse.body.data.story.status).toBe("draft");
      expect(draftResponse.body.data.story.visibility).toBe("private");

      const storyId = draftResponse.body.data.story.id;

      // 2. 编辑故事内容
      const editResponse = await request(app.getHttpServer())
        .put(`/stories/${storyId}`)
        .set("Authorization", `Bearer ${await getAuthToken(author)}`)
        .send({
          title: "我的大学生活回忆 - 完整版",
          content:
            "这是一个关于大学时光的完整故事，包含了详细的情节和人物描述...",
          tags: ["大学", "友情", "青春", "成长"],
        })
        .expect(200);

      expect(editResponse.body.data.title).toContain("完整版");
      expect(editResponse.body.data.tags).toHaveLength(4);

      // 3. 发布故事
      const publishResponse = await request(app.getHttpServer())
        .patch(`/stories/${storyId}/status`)
        .set("Authorization", `Bearer ${await getAuthToken(author)}`)
        .send({
          status: "published",
          visibility: "public",
        })
        .expect(200);

      expect(publishResponse.body.data.status).toBe("published");
      expect(publishResponse.body.data.visibility).toBe("public");
      expect(publishResponse.body.data.publishedAt).toBeDefined();

      // 4. 验证故事在公开列表中可见
      const publicStoriesResponse = await request(app.getHttpServer())
        .get("/stories")
        .query({
          status: "published",
          visibility: "public",
        })
        .expect(200);

      const foundStory = publicStoriesResponse.body.data.stories.find(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (s: any) => s.id === storyId,
      );
      expect(foundStory).toBeDefined();
      expect(foundStory.title).toContain("完整版");

      // 5. 验证作者统计更新
      const authorStats = await request(app.getHttpServer())
        .get(`/users/${author.id}/stats`)
        .set("Authorization", `Bearer ${await getAuthToken(author)}`)
        .expect(200);

      expect(authorStats.body.data.storiesPublished).toBeGreaterThanOrEqual(1);
      expect(authorStats.body.data.totalWords).toBeGreaterThan(0);
    });

    it("should handle story content validation and security checks", async () => {
      // 1. 测试内容安全验证
      const maliciousContent = {
        title: "包含敏感词的标题",
        content: "这是一个包含政治敏感内容的故事，应该被系统拒绝...",
        status: "draft",
        category: "other",
      };

      await request(app.getHttpServer())
        .post("/stories")
        .set("Authorization", `Bearer ${await getAuthToken(author)}`)
        .send(maliciousContent)
        .expect(400);

      // 2. 测试字数限制验证
      const tooLongContent = {
        title: "超长内容测试",
        content: "a".repeat(50001), // 超过50000字限制
        status: "draft",
        category: "other",
      };

      await request(app.getHttpServer())
        .post("/stories")
        .set("Authorization", `Bearer ${await getAuthToken(author)}`)
        .send(tooLongContent)
        .expect(400);

      // 3. 测试标签数量限制
      const tooManyTags = {
        title: "标签测试",
        content: "正常内容",
        tags: Array.from({ length: 11 }, (_, i) => `标签${i}`), // 超过10个标签限制
        status: "draft",
        category: "other",
      };

      await request(app.getHttpServer())
        .post("/stories")
        .set("Authorization", `Bearer ${await getAuthToken(author)}`)
        .send(tooManyTags)
        .expect(400);

      // 4. 验证正常内容可以成功创建
      const validContent = {
        title: "正常的故事标题",
        content: "这是一个正常的故事内容，没有任何问题。",
        status: "draft",
        category: "daily_life",
        tags: ["生活", "日常"],
      };

      await request(app.getHttpServer())
        .post("/stories")
        .set("Authorization", `Bearer ${await getAuthToken(author)}`)
        .send(validContent)
        .expect(201);
    });

    it("should support story image upload and media management", async () => {
      // 1. 创建故事
      const storyResponse = await request(app.getHttpServer())
        .post("/stories")
        .set("Authorization", `Bearer ${await getAuthToken(author)}`)
        .send({
          title: "图片故事测试",
          content: "这是一个包含图片的故事...",
          status: "draft",
        })
        .expect(201);

      const storyId = storyResponse.body.data.story.id;

      // 2. 模拟图片上传（Mock上传服务）
      const mockImageUrl = "https://mock-cdn.example.com/story-image.jpg";

      const imageUploadResponse = await request(app.getHttpServer())
        .post(`/stories/${storyId}/images`)
        .set("Authorization", `Bearer ${await getAuthToken(author)}`)
        .send({
          imageUrl: mockImageUrl,
          caption: "故事配图",
          order: 1,
        })
        .expect(201);

      expect(imageUploadResponse.body.data.imageUrl).toBe(mockImageUrl);

      // 3. 验证故事图片关联
      const storyWithImages = await request(app.getHttpServer())
        .get(`/stories/${storyId}`)
        .set("Authorization", `Bearer ${await getAuthToken(author)}`)
        .expect(200);

      expect(storyWithImages.body.data.images).toHaveLength(1);
      expect(storyWithImages.body.data.images[0].caption).toBe("故事配图");

      // 4. 测试图片删除
      const imageId = imageUploadResponse.body.data.id;
      await request(app.getHttpServer())
        .delete(`/stories/${storyId}/images/${imageId}`)
        .set("Authorization", `Bearer ${await getAuthToken(author)}`)
        .expect(200);

      // 5. 验证图片已删除
      const storyAfterDelete = await request(app.getHttpServer())
        .get(`/stories/${storyId}`)
        .set("Authorization", `Bearer ${await getAuthToken(author)}`)
        .expect(200);

      expect(storyAfterDelete.body.data.images).toHaveLength(0);
    });
  });

  describe("🔐 故事权限控制和协作功能", () => {
    beforeEach(async () => {
      // 创建测试故事
      draftStory = await dataFactory.createStory({
        title: "草稿故事",
        content: "草稿内容",
        status: "draft",
        visibility: "private",
        authorId: author.id,
      });

      publishedStory = await dataFactory.createStory({
        title: "公开发布故事",
        content: "公开内容",
        status: "published",
        visibility: "public",
        authorId: author.id,
      });

      privateStory = await dataFactory.createStory({
        title: "私人故事",
        content: "私人内容",
        status: "published",
        visibility: "private",
        authorId: author.id,
      });
    });

    it("should enforce story visibility and access permissions", async () => {
      // 1. 作者可以访问自己的所有故事
      const authorStories = await request(app.getHttpServer())
        .get("/stories/my-stories")
        .set("Authorization", `Bearer ${await getAuthToken(author)}`)
        .expect(200);

      expect(authorStories.body.data.stories).toHaveLength(3);

      // 2. 公众用户只能看到公开发布的故事
      const publicStories = await request(app.getHttpServer())
        .get("/stories")
        .query({ visibility: "public", status: "published" })
        .expect(200);

      const visibleStories = publicStories.body.data.stories.filter(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (s: any) => s.authorId === author.id,
      );
      expect(visibleStories).toHaveLength(1);
      expect(visibleStories[0].title).toBe("公开发布故事");

      // 3. 非作者用户无法访问私人故事
      await request(app.getHttpServer())
        .get(`/stories/${privateStory.id}`)
        .set("Authorization", `Bearer ${await getAuthToken(reader)}`)
        .expect(403);

      // 4. 非作者用户无法编辑他人故事
      await request(app.getHttpServer())
        .put(`/stories/${publishedStory.id}`)
        .set("Authorization", `Bearer ${await getAuthToken(reader)}`)
        .send({
          title: "恶意修改标题",
        })
        .expect(403);
    });

    it("should support collaborative editing with permission control", async () => {
      // 1. 作者邀请协作者
      const inviteResponse = await request(app.getHttpServer())
        .post(`/stories/${draftStory.id}/collaborators`)
        .set("Authorization", `Bearer ${await getAuthToken(author)}`)
        .send({
          userId: collaborator.id,
          permission: "edit",
        })
        .expect(201);

      expect(inviteResponse.body.data.userId).toBe(collaborator.id);
      expect(inviteResponse.body.data.permission).toBe("edit");

      // 2. 协作者可以编辑故事
      const editResponse = await request(app.getHttpServer())
        .put(`/stories/${draftStory.id}`)
        .set("Authorization", `Bearer ${await getAuthToken(collaborator)}`)
        .send({
          content: "协作者编辑的内容",
        })
        .expect(200);

      expect(editResponse.body.data.content).toBe("协作者编辑的内容");

      // 3. 协作者无法删除故事（需要owner权限）
      await request(app.getHttpServer())
        .delete(`/stories/${draftStory.id}`)
        .set("Authorization", `Bearer ${await getAuthToken(collaborator)}`)
        .expect(403);

      // 4. 验证协作记录
      const collaborationHistory = await request(app.getHttpServer())
        .get(`/stories/${draftStory.id}/collaboration-history`)
        .set("Authorization", `Bearer ${await getAuthToken(author)}`)
        .expect(200);

      expect(collaborationHistory.body.data.edits).toHaveLength(1);
      expect(collaborationHistory.body.data.edits[0].editorId).toBe(
        collaborator.id,
      );

      // 5. 移除协作者权限
      await request(app.getHttpServer())
        .delete(`/stories/${draftStory.id}/collaborators/${collaborator.id}`)
        .set("Authorization", `Bearer ${await getAuthToken(author)}`)
        .expect(200);

      // 6. 移除后协作者无法再编辑
      await request(app.getHttpServer())
        .put(`/stories/${draftStory.id}`)
        .set("Authorization", `Bearer ${await getAuthToken(collaborator)}`)
        .send({
          content: "移除权限后尝试编辑",
        })
        .expect(403);
    });

    it("should track story version history and support rollback", async () => {
      const storyId = draftStory.id;

      // 1. 创建多个版本
      const versions = ["第一次编辑内容", "第二次编辑内容", "第三次编辑内容"];

      for (let i = 0; i < versions.length; i++) {
        await request(app.getHttpServer())
          .put(`/stories/${storyId}`)
          .set("Authorization", `Bearer ${await getAuthToken(author)}`)
          .send({
            content: versions[i],
          })
          .expect(200);

        // 等待一小段时间确保版本时间戳不同
        await new Promise((resolve) => setTimeout(resolve, 100));
      }

      // 2. 获取版本历史
      const historyResponse = await request(app.getHttpServer())
        .get(`/stories/${storyId}/versions`)
        .set("Authorization", `Bearer ${await getAuthToken(author)}`)
        .expect(200);

      expect(historyResponse.body.data.versions).toHaveLength(3);
      expect(historyResponse.body.data.versions[0].content).toBe(
        "第三次编辑内容",
      );

      // 3. 回滚到第二个版本
      const secondVersionId = historyResponse.body.data.versions[1].id;
      const rollbackResponse = await request(app.getHttpServer())
        .post(`/stories/${storyId}/rollback`)
        .set("Authorization", `Bearer ${await getAuthToken(author)}`)
        .send({
          versionId: secondVersionId,
        })
        .expect(200);

      expect(rollbackResponse.body.data.content).toBe("第二次编辑内容");

      // 4. 验证当前内容已回滚
      const currentStory = await request(app.getHttpServer())
        .get(`/stories/${storyId}`)
        .set("Authorization", `Bearer ${await getAuthToken(author)}`)
        .expect(200);

      expect(currentStory.body.data.content).toBe("第二次编辑内容");

      // 5. 验证回滚操作记录在历史中
      const updatedHistory = await request(app.getHttpServer())
        .get(`/stories/${storyId}/versions`)
        .set("Authorization", `Bearer ${await getAuthToken(author)}`)
        .expect(200);

      const latestVersion = updatedHistory.body.data.versions[0];
      expect(latestVersion.action).toBe("rollback");
      expect(latestVersion.rollbackToVersionId).toBe(secondVersionId);
    });
  });

  describe("🔍 故事搜索和发现功能", () => {
    beforeEach(async () => {
      // 创建多个测试故事用于搜索测试
      const testStories = [
        {
          title: "大学生活回忆录",
          content: "关于大学时光的美好回忆，包含友情和成长...",
          category: "campus_life",
          tags: ["大学", "友情", "成长"],
          status: "published",
          visibility: "public",
        },
        {
          title: "工作第一年的感悟",
          content: "初入职场的经历和感悟，职业发展的思考...",
          category: "career",
          tags: ["工作", "职场", "成长"],
          status: "published",
          visibility: "public",
        },
        {
          title: "旅行见闻集",
          content: "世界各地的旅行经历和文化体验...",
          category: "travel",
          tags: ["旅行", "文化", "体验"],
          status: "published",
          visibility: "public",
        },
      ];

      for (const storyData of testStories) {
        await dataFactory.createStory({
          ...storyData,
          authorId: author.id,
        });
      }

      // 等待搜索索引更新
      await new Promise((resolve) => setTimeout(resolve, 1000));
    });

    it("should support full-text search with relevance ranking", async () => {
      // 1. 按关键词搜索
      const searchResponse = await request(app.getHttpServer())
        .get("/stories/search")
        .query({
          q: "大学 友情",
          limit: 10,
        })
        .expect(200);

      expect(searchResponse.body.data.stories.length).toBeGreaterThan(0);

      const foundStory = searchResponse.body.data.stories.find(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (s: any) => s.title.includes("大学生活"),
      );
      expect(foundStory).toBeDefined();
      expect(foundStory.relevanceScore).toBeGreaterThan(0);

      // 2. 验证搜索结果相关性排序
      const scores = searchResponse.body.data.stories.map(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (s: any) => s.relevanceScore,
      );
      for (let i = 1; i < scores.length; i++) {
        expect(scores[i]).toBeLessThanOrEqual(scores[i - 1]);
      }

      // 3. 测试精确标题搜索
      const exactSearchResponse = await request(app.getHttpServer())
        .get("/stories/search")
        .query({
          q: "工作第一年的感悟",
          exactMatch: true,
        })
        .expect(200);

      expect(exactSearchResponse.body.data.stories).toHaveLength(1);
      expect(exactSearchResponse.body.data.stories[0].title).toBe(
        "工作第一年的感悟",
      );
    });

    it("should support advanced filtering and categorization", async () => {
      // 1. 按分类筛选
      const categoryResponse = await request(app.getHttpServer())
        .get("/stories")
        .query({
          category: "campus_life",
          status: "published",
        })
        .expect(200);

      expect(categoryResponse.body.data.stories.length).toBeGreaterThan(0);
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      categoryResponse.body.data.stories.forEach((story: any) => {
        expect(story.category).toBe("campus_life");
      });

      // 2. 按标签筛选
      const tagResponse = await request(app.getHttpServer())
        .get("/stories")
        .query({
          tags: "成长,工作",
          status: "published",
        })
        .expect(200);

      expect(tagResponse.body.data.stories.length).toBeGreaterThan(0);
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      tagResponse.body.data.stories.forEach((story: any) => {
        const hasRequiredTag = story.tags.some((tag: string) =>
          ["成长", "工作"].includes(tag),
        );
        expect(hasRequiredTag).toBe(true);
      });

      // 3. 按作者筛选
      const authorResponse = await request(app.getHttpServer())
        .get("/stories")
        .query({
          authorId: author.id,
          status: "published",
        })
        .expect(200);

      expect(authorResponse.body.data.stories.length).toBe(3);
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      authorResponse.body.data.stories.forEach((story: any) => {
        expect(story.authorId).toBe(author.id);
      });

      // 4. 组合筛选
      const combinedResponse = await request(app.getHttpServer())
        .get("/stories")
        .query({
          category: "career",
          tags: "工作",
          authorId: author.id,
        })
        .expect(200);

      expect(combinedResponse.body.data.stories).toHaveLength(1);
      expect(combinedResponse.body.data.stories[0].category).toBe("career");
    });

    it("should provide personalized recommendations", async () => {
      // 1. 模拟用户阅读行为
      const storiesToRead = await request(app.getHttpServer())
        .get("/stories")
        .query({ status: "published", limit: 2 })
        .expect(200);

      for (const story of storiesToRead.body.data.stories) {
        await request(app.getHttpServer())
          .post(`/stories/${story.id}/read`)
          .set("Authorization", `Bearer ${await getAuthToken(reader)}`)
          .expect(200);
      }

      // 2. 获取个性化推荐
      const recommendationsResponse = await request(app.getHttpServer())
        .get("/stories/recommendations")
        .set("Authorization", `Bearer ${await getAuthToken(reader)}`)
        .query({ limit: 5 })
        .expect(200);

      expect(recommendationsResponse.body.data.stories.length).toBeGreaterThan(
        0,
      );
      expect(recommendationsResponse.body.data.algorithm).toBeDefined();
      expect(recommendationsResponse.body.data.reasons).toBeDefined();

      // 3. 验证推荐理由
      const firstRecommendation = recommendationsResponse.body.data.stories[0];
      expect(firstRecommendation.recommendationScore).toBeGreaterThan(0);
      expect(firstRecommendation.reasons).toContain("基于您的阅读历史");

      // 4. 测试热门故事推荐（未登录用户）
      const trendingResponse = await request(app.getHttpServer())
        .get("/stories/trending")
        .query({ period: "week", limit: 10 })
        .expect(200);

      expect(trendingResponse.body.data.stories.length).toBeGreaterThan(0);
      const trendingStory = trendingResponse.body.data.stories[0];
      expect(trendingStory.trendingScore).toBeDefined();
      expect(trendingStory.readCount).toBeGreaterThan(0);
    });
  });

  describe("📊 故事统计和分析功能", () => {
    it("should track and analyze story performance metrics", async () => {
      // 1. 创建测试故事
      const storyResponse = await request(app.getHttpServer())
        .post("/stories")
        .set("Authorization", `Bearer ${await getAuthToken(author)}`)
        .send({
          title: "性能分析测试故事",
          content: "用于测试统计分析功能的故事内容...",
          status: "published",
          visibility: "public",
        })
        .expect(201);

      const storyId = storyResponse.body.data.story.id;

      // 2. 模拟多用户互动
      const testUsers = [reader, collaborator, publicUser];

      for (const user of testUsers) {
        // 阅读故事
        await request(app.getHttpServer())
          .post(`/stories/${storyId}/read`)
          .set("Authorization", `Bearer ${await getAuthToken(user)}`)
          .expect(200);

        // 点赞故事
        await request(app.getHttpServer())
          .post(`/stories/${storyId}/like`)
          .set("Authorization", `Bearer ${await getAuthToken(user)}`)
          .expect(201);
      }

      // 3. 等待统计数据更新
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // 4. 获取详细统计数据
      const statsResponse = await request(app.getHttpServer())
        .get(`/stories/${storyId}/stats`)
        .set("Authorization", `Bearer ${await getAuthToken(author)}`)
        .expect(200);

      expect(statsResponse.body.data.readCount).toBe(3);
      expect(statsResponse.body.data.likeCount).toBe(3);
      expect(statsResponse.body.data.readerDistribution).toBeDefined();
      expect(statsResponse.body.data.engagementRate).toBeGreaterThan(0);

      // 5. 获取时间序列统计
      const timeSeriesResponse = await request(app.getHttpServer())
        .get(`/stories/${storyId}/stats/timeline`)
        .set("Authorization", `Bearer ${await getAuthToken(author)}`)
        .query({
          period: "day",
          days: 7,
        })
        .expect(200);

      expect(timeSeriesResponse.body.data.timeline).toHaveLength(7);
      expect(timeSeriesResponse.body.data.timeline[6].reads).toBe(3); // 今天的数据

      // 6. 获取作者整体统计
      const authorStatsResponse = await request(app.getHttpServer())
        .get(`/users/${author.id}/story-stats`)
        .set("Authorization", `Bearer ${await getAuthToken(author)}`)
        .expect(200);

      expect(authorStatsResponse.body.data.totalStories).toBeGreaterThan(0);
      expect(authorStatsResponse.body.data.totalReads).toBeGreaterThan(0);
      expect(authorStatsResponse.body.data.averageEngagement).toBeGreaterThan(
        0,
      );
    });

    it("should generate analytics reports and insights", async () => {
      // 1. 获取平台整体统计（管理员权限）
      const platformStatsResponse = await request(app.getHttpServer())
        .get("/stories/platform-stats")
        .set("Authorization", `Bearer ${await getAuthToken(moderator)}`)
        .expect(200);

      expect(platformStatsResponse.body.data.totalStories).toBeGreaterThan(0);
      expect(platformStatsResponse.body.data.totalAuthors).toBeGreaterThan(0);
      expect(
        platformStatsResponse.body.data.categoryDistribution,
      ).toBeDefined();
      expect(platformStatsResponse.body.data.growthTrends).toBeDefined();

      // 2. 获取内容质量分析
      const qualityAnalysisResponse = await request(app.getHttpServer())
        .get("/stories/quality-analysis")
        .set("Authorization", `Bearer ${await getAuthToken(moderator)}`)
        .query({ period: "month" })
        .expect(200);

      expect(
        qualityAnalysisResponse.body.data.averageWordCount,
      ).toBeGreaterThan(0);
      expect(
        qualityAnalysisResponse.body.data.contentQualityScore,
      ).toBeDefined();
      expect(qualityAnalysisResponse.body.data.moderationStats).toBeDefined();

      // 3. 获取用户参与度分析
      const engagementAnalysisResponse = await request(app.getHttpServer())
        .get("/stories/engagement-analysis")
        .set("Authorization", `Bearer ${await getAuthToken(moderator)}`)
        .query({ period: "week" })
        .expect(200);

      expect(
        engagementAnalysisResponse.body.data.averageReadTime,
      ).toBeGreaterThan(0);
      expect(
        engagementAnalysisResponse.body.data.interactionRates,
      ).toBeDefined();
      expect(
        engagementAnalysisResponse.body.data.retentionMetrics,
      ).toBeDefined();
    });
  });

  // 辅助函数：获取用户认证令牌
  async function getAuthToken(user: User): Promise<string> {
    const loginResponse = await request(app.getHttpServer())
      .post("/auth/login")
      .send({
        phone: user.phone,
        password: "Test123456!", // 默认测试密码
      });

    return loginResponse.body.data.accessToken;
  }
});
