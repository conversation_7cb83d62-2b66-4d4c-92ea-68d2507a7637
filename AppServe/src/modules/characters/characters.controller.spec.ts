import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { BadRequestException, ForbiddenException } from "@nestjs/common";
import { CharactersController } from "./characters.controller";
import { CharactersService } from "./characters.service";
import type {
  CreateCharacterDto,
  UpdateCharacterDto,
  CharacterQueryDto,
  CharacterResponseDto,
  CharacterStatisticsDto,
  ValidateCharacterNameDto,
} from "./dto";
import type { AuthRequest } from "../../common/types/request.types";
import { PaginatedResponse } from "../../common/dto/paginated-response.dto";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import type { MultipartFile } from "@fastify/multipart";

describe("CharactersController", () => {
  let controller: CharactersController;
  let mockCharactersService: jest.Mocked<CharactersService>;

  // Mock数据 - 完整的User实体mock
  const mockUser = {
    id: "user-1",
    phone: "13800138000",
    email: "<EMAIL>",
    passwordHash: "hashedpassword",
    userNumber: "100001",
    nickname: "测试用户",
    username: "testuser",
    avatarUrl: "",
    birthDate: new Date(),
    bio: "测试用户简介",
    aiQuotaRemaining: 3,
    aiQuotaResetDate: new Date(),
    profileDisplaySettings: {
      showBirthday: false,
      showBio: true,
      showStatistics: true,
      showCharacters: true,
      showTimeline: true,
    },
    anomalyStatus: "normal" as const,
    status: "active",
    isActive: true,
    lastLoginAt: new Date(),
    lastLoginIp: "127.0.0.1",
    failedLoginAttempts: 0,
    lockedUntil: null,
    anomalyWarningCount: 0,
    anomalyRestrictionCount: 0,
    lightingRestrictedUntil: null,
    lastInvalidLightingAttempt: null,
    dailyLightingAttempts: 0,
    lightingAttemptResetDate: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
    // 关联关系
    stories: [],
    characters: [],
    lightings: [],
    receivedLightings: [],
    relationships: [],
    relatedRelationships: [],
    shares: [],
    notifications: [],
    refreshTokens: [],
    // 方法
    isLocked: () => false,
    shouldLockAccount: () => false,
    isLightingRestricted: () => false,
    isSuspended: () => false,
    canApplyLighting: () => true,
    shouldResetDailyAttempts: () => false,
    shouldTriggerWarning: () => false,
    shouldTriggerRestriction: () => false,
    shouldTriggerSuspension: () => false,
  };
  const mockAuthRequest = { user: mockUser } as AuthRequest;

  const mockCharacter = {
    id: "character-1",
    name: "张三",
    description: "一个测试角色",
    gender: "男",
    relationship: "朋友",
    customRelationship: "",
    creatorId: "user-1",
    avatarUrl: "",
    isLighted: false,
    lightingCount: 0,
    lighterUserId: null,
    firstLightedAt: null,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    creator: mockUser,
    lighterUser: null,
    isLightedByUser: false,
    calculateRelationshipStats: jest.fn(),
    canBeLightedBy: jest.fn(),
    getLightingStatusDescription: jest.fn(),
    stories: [],
    lightings: [],
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } as any;

  const mockCharacterResponse: CharacterResponseDto = {
    id: "character-1",
    name: "张三",
    description: "一个测试角色",
    avatarUrl: "",
    gender: "男",
    relationship: "朋友",
    customRelationship: "",
    isLighted: false,
    lightingCount: 0,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    creator: {
      id: "user-1",
      nickname: "测试用户",
      avatar: "",
      userNumber: "100001",
    },
    storiesCount: 0,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } as any;

  const mockPaginatedResponse = new PaginatedResponse(
    [mockCharacterResponse],
    1,
    1,
    10,
  );

  const mockStatistics: CharacterStatisticsDto = {
    totalCharacters: 10,
    lightedCharacters: 3,
    unlightedCharacters: 7,
    totalLightings: 5,
    lightingRate: 30.0,
    relationshipDistribution: {
      朋友: 4,
      家人: 3,
      同事: 3,
    },
    genderDistribution: {
      男: 6,
      女: 4,
    },
  };

  beforeEach(async () => {
    const mockServiceMethods = {
      createCharacter: jest.fn(),
      updateCharacter: jest.fn(),
      deleteCharacter: jest.fn(),
      findById: jest.fn(),
      getUserCharacters: jest.fn(),
      getAccessibleCharacters: jest.fn(),
      searchCharacters: jest.fn(),
      getPopularCharacters: jest.fn(),
      getUserCharacterStatistics: jest.fn(),
      validateCharacterName: jest.fn(),
      updateLightingStatus: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [CharactersController],
      providers: [
        {
          provide: CharactersService,
          useValue: mockServiceMethods,
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({ canActivate: jest.fn(() => true) })
      .compile();

    controller = module.get<CharactersController>(CharactersController);
    mockCharactersService = module.get(
      CharactersService,
    ) as jest.Mocked<CharactersService>;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("createCharacter", () => {
    it("应该成功创建人物", async () => {
      const createDto: CreateCharacterDto = {
        name: "李四",
        description: "新角色描述",
        gender: "女",
        relationship: "同事",
        customRelationship: "",
        avatarUrl: "",
      };

      mockCharactersService.createCharacter.mockResolvedValue(mockCharacter);

      const result = await controller.createCharacter(
        mockAuthRequest,
        createDto,
      );

      expect(mockCharactersService.createCharacter).toHaveBeenCalledWith(
        "user-1",
        createDto,
      );
      expect(result.success).toBe(true);
      expect(result.message).toBe("人物创建成功");
      expect(result.statusCode).toBe(201);
      expect(result.data).toBeInstanceOf(Object);
    });

    it("应该处理创建错误", async () => {
      const createDto: CreateCharacterDto = {
        name: "李四",
        description: "新角色描述",
        gender: "女",
        relationship: "同事",
        customRelationship: "",
        avatarUrl: "",
      };

      mockCharactersService.createCharacter.mockRejectedValue(
        new Error("创建失败"),
      );

      await expect(
        controller.createCharacter(mockAuthRequest, createDto),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe("updateCharacter", () => {
    it("应该成功更新人物", async () => {
      const updateDto: UpdateCharacterDto = {
        name: "张三（更新版）",
        description: "更新的描述",
      };

      mockCharactersService.updateCharacter.mockResolvedValue(mockCharacter);

      const result = await controller.updateCharacter(
        mockAuthRequest,
        "character-1",
        updateDto,
      );

      expect(mockCharactersService.updateCharacter).toHaveBeenCalledWith(
        "character-1",
        updateDto,
        "user-1",
      );
      expect(result.success).toBe(true);
      expect(result.message).toBe("人物更新成功");
      expect(result.statusCode).toBe(200);
    });

    it("应该传递权限错误", async () => {
      const updateDto: UpdateCharacterDto = { name: "更新名称" };

      mockCharactersService.updateCharacter.mockRejectedValue(
        new ForbiddenException("无权限"),
      );

      await expect(
        controller.updateCharacter(mockAuthRequest, "character-1", updateDto),
      ).rejects.toThrow(ForbiddenException);
    });

    it("应该处理其他更新错误", async () => {
      const updateDto: UpdateCharacterDto = { name: "更新名称" };

      mockCharactersService.updateCharacter.mockRejectedValue(
        new Error("更新失败"),
      );

      await expect(
        controller.updateCharacter(mockAuthRequest, "character-1", updateDto),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe("deleteCharacter", () => {
    it("应该成功删除人物", async () => {
      mockCharactersService.deleteCharacter.mockResolvedValue(undefined);

      await controller.deleteCharacter(mockAuthRequest, "character-1");

      expect(mockCharactersService.deleteCharacter).toHaveBeenCalledWith(
        "character-1",
        "user-1",
      );
    });

    it("应该传递权限错误", async () => {
      mockCharactersService.deleteCharacter.mockRejectedValue(
        new ForbiddenException("无权限"),
      );

      await expect(
        controller.deleteCharacter(mockAuthRequest, "character-1"),
      ).rejects.toThrow(ForbiddenException);
    });

    it("应该处理其他删除错误", async () => {
      mockCharactersService.deleteCharacter.mockRejectedValue(
        new Error("删除失败"),
      );

      await expect(
        controller.deleteCharacter(mockAuthRequest, "character-1"),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe("getCharacterById", () => {
    it("应该成功获取人物详情", async () => {
      mockCharactersService.findById.mockResolvedValue(mockCharacter);

      const result = await controller.getCharacterById(
        mockAuthRequest,
        "character-1",
      );

      expect(mockCharactersService.findById).toHaveBeenCalledWith(
        "character-1",
      );
      expect(result.success).toBe(true);
      expect(result.message).toBe("获取人物成功");
      expect(result.statusCode).toBe(200);
    });

    it("应该处理获取错误", async () => {
      mockCharactersService.findById.mockRejectedValue(new Error("人物不存在"));

      await expect(
        controller.getCharacterById(mockAuthRequest, "character-1"),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe("getCharacters", () => {
    it("应该获取用户自己的人物", async () => {
      const query: CharacterQueryDto = {
        creatorId: "user-1",
        page: 1,
        limit: 10,
      };

      mockCharactersService.getUserCharacters.mockResolvedValue(
        mockPaginatedResponse,
      );

      const result = await controller.getCharacters(mockAuthRequest, query);

      expect(mockCharactersService.getUserCharacters).toHaveBeenCalledWith(
        "user-1",
        query,
      );
      expect(result.success).toBe(true);
      expect(result.message).toBe("获取人物列表成功");
      expect(result.data).toBe(mockPaginatedResponse);
    });

    it("应该获取其他用户的可访问人物", async () => {
      const query: CharacterQueryDto = {
        creatorId: "other-user",
        page: 1,
        limit: 10,
      };

      mockCharactersService.getAccessibleCharacters.mockResolvedValue(
        mockPaginatedResponse,
      );

      await controller.getCharacters(mockAuthRequest, query);

      expect(
        mockCharactersService.getAccessibleCharacters,
      ).toHaveBeenCalledWith(query, "user-1");
    });

    it("应该获取可访问的人物列表（无指定创建者）", async () => {
      const query: CharacterQueryDto = { page: 1, limit: 10 };

      mockCharactersService.getAccessibleCharacters.mockResolvedValue(
        mockPaginatedResponse,
      );

      await controller.getCharacters(mockAuthRequest, query);

      expect(
        mockCharactersService.getAccessibleCharacters,
      ).toHaveBeenCalledWith(query, "user-1");
    });
  });

  describe("getMyCharacters", () => {
    it("应该获取我的人物列表", async () => {
      const query: CharacterQueryDto = { page: 1, limit: 10 };

      mockCharactersService.getUserCharacters.mockResolvedValue(
        mockPaginatedResponse,
      );

      const result = await controller.getMyCharacters(mockAuthRequest, query);

      expect(mockCharactersService.getUserCharacters).toHaveBeenCalledWith(
        "user-1",
        query,
      );
      expect(result.success).toBe(true);
      expect(result.message).toBe("获取我的人物列表成功");
      expect(result.data).toBe(mockPaginatedResponse);
    });
  });

  describe("searchCharacters", () => {
    it("应该成功搜索人物", async () => {
      const query: CharacterQueryDto = {
        search: "张三",
        page: 1,
        limit: 10,
      };

      mockCharactersService.searchCharacters.mockResolvedValue(
        mockPaginatedResponse,
      );

      const result = await controller.searchCharacters(mockAuthRequest, query);

      expect(mockCharactersService.searchCharacters).toHaveBeenCalledWith(
        query,
        "user-1",
      );
      expect(result.success).toBe(true);
      expect(result.message).toBe("搜索人物成功");
      expect(result.data).toBe(mockPaginatedResponse);
    });
  });

  describe("getPopularCharacters", () => {
    it("应该获取热门人物", async () => {
      const query: CharacterQueryDto = { page: 1, limit: 10 };

      mockCharactersService.getPopularCharacters.mockResolvedValue(
        mockPaginatedResponse,
      );

      const result = await controller.getPopularCharacters(
        mockAuthRequest,
        query,
      );

      expect(mockCharactersService.getPopularCharacters).toHaveBeenCalledWith(
        query,
      );
      expect(result.success).toBe(true);
      expect(result.message).toBe("获取热门人物成功");
      expect(result.data).toBe(mockPaginatedResponse);
    });
  });

  describe("getCharacterStatistics", () => {
    it("应该获取人物统计数据", async () => {
      mockCharactersService.getUserCharacterStatistics.mockResolvedValue(
        mockStatistics,
      );

      const result = await controller.getCharacterStatistics(mockAuthRequest);

      expect(
        mockCharactersService.getUserCharacterStatistics,
      ).toHaveBeenCalledWith("user-1");
      expect(result.success).toBe(true);
      expect(result.message).toBe("获取人物统计成功");
      expect(result.data).toBe(mockStatistics);
    });
  });

  describe("validateCharacterName", () => {
    it("应该验证人物名称可用", async () => {
      const validateDto: ValidateCharacterNameDto = { name: "新角色" };

      mockCharactersService.validateCharacterName.mockResolvedValue(true);

      const result = await controller.validateCharacterName(
        mockAuthRequest,
        validateDto,
      );

      expect(mockCharactersService.validateCharacterName).toHaveBeenCalledWith(
        "user-1",
        "新角色",
      );
      expect(result.success).toBe(true);
      expect(result.message).toBe("名称验证完成");
      expect(result.data.isAvailable).toBe(true);
    });

    it("应该验证人物名称不可用", async () => {
      const validateDto: ValidateCharacterNameDto = { name: "已存在角色" };

      mockCharactersService.validateCharacterName.mockResolvedValue(false);

      const result = await controller.validateCharacterName(
        mockAuthRequest,
        validateDto,
      );

      expect(result.data.isAvailable).toBe(false);
    });
  });

  describe("updateLightingStatus", () => {
    it("应该成功更新点亮状态", async () => {
      mockCharactersService.updateLightingStatus.mockResolvedValue(undefined);

      const result = await controller.updateLightingStatus(
        mockAuthRequest,
        "character-1",
      );

      expect(mockCharactersService.updateLightingStatus).toHaveBeenCalledWith(
        "character-1",
      );
      expect(result.success).toBe(true);
      expect(result.message).toBe("点亮状态更新成功");
      expect(result.data).toBeNull();
    });
  });

  describe("uploadCharacterAvatar", () => {
    it("应该成功上传头像", async () => {
      const mockFile = {
        type: "image",
        mimetype: "image/jpeg",
        fieldname: "avatar",
        filename: "test.jpg",
        encoding: "7bit",
        toBuffer: jest.fn().mockResolvedValue(Buffer.alloc(1024)), // 1KB文件
        file: {} as NodeJS.ReadableStream,
        fields: {},
      };

      mockCharactersService.updateCharacter.mockResolvedValue(mockCharacter);

      const result = await controller.uploadCharacterAvatar(
        mockAuthRequest,
        "character-1",
        mockFile as unknown as MultipartFile,
      );

      expect(result.success).toBe(true);
      expect(result.message).toBe("头像上传成功");
      expect(result.data.avatarUrl).toBeDefined();
      expect(mockCharactersService.updateCharacter).toHaveBeenCalled();
    });

    it("应该在没有文件时抛出错误", async () => {
      await expect(
        controller.uploadCharacterAvatar(
          mockAuthRequest,
          "character-1",
          null as unknown as MultipartFile,
        ),
      ).rejects.toThrow(BadRequestException);
    });

    it("应该在文件类型不支持时抛出错误", async () => {
      const mockFile = {
        type: "text",
        mimetype: "text/plain",
        fieldname: "avatar",
        filename: "test.txt",
        encoding: "7bit",
        toBuffer: jest.fn().mockResolvedValue(Buffer.alloc(1024)),
        file: {} as NodeJS.ReadableStream,
      };

      await expect(
        controller.uploadCharacterAvatar(
          mockAuthRequest,
          "character-1",
          mockFile as unknown as MultipartFile,
        ),
      ).rejects.toThrow(BadRequestException);
    });

    it("应该在文件过大时抛出错误", async () => {
      const mockFile = {
        type: "image",
        mimetype: "image/jpeg",
        fieldname: "avatar",
        filename: "large.jpg",
        encoding: "7bit",
        toBuffer: jest.fn().mockResolvedValue(Buffer.alloc(3 * 1024 * 1024)), // 3MB文件
        file: {} as NodeJS.ReadableStream,
      };

      await expect(
        controller.uploadCharacterAvatar(
          mockAuthRequest,
          "character-1",
          mockFile as unknown as MultipartFile,
        ),
      ).rejects.toThrow(BadRequestException);
    });

    it("应该传递权限错误", async () => {
      const mockFile = {
        type: "image",
        mimetype: "image/jpeg",
        fieldname: "avatar",
        filename: "test.jpg",
        encoding: "7bit",
        toBuffer: jest.fn().mockResolvedValue(Buffer.alloc(1024)),
        file: {} as NodeJS.ReadableStream,
      };

      mockCharactersService.updateCharacter.mockRejectedValue(
        new ForbiddenException("无权限"),
      );

      await expect(
        controller.uploadCharacterAvatar(
          mockAuthRequest,
          "character-1",
          mockFile as unknown as MultipartFile,
        ),
      ).rejects.toThrow(ForbiddenException);
    });
  });

  it("应该正确注入CharactersService", () => {
    expect(controller).toBeDefined();
    expect(mockCharactersService).toBeDefined();
  });
});
