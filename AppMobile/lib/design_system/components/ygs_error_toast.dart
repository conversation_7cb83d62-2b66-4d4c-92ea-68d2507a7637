import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../tokens/ygs_colors.dart';
import '../tokens/ygs_typography.dart';

/// YGS全局错误提示组件 - 顶部展示
/// 基于YGS设计系统风格，支持动画和自动消失
class YgsErrorToast extends StatefulWidget {
  const YgsErrorToast({
    required this.message,
    this.duration = const Duration(seconds: 4),
    this.onDismiss,
    super.key,
  });

  /// 错误消息文本
  final String message;
  
  /// 显示持续时间，默认4秒
  final Duration duration;
  
  /// 消失回调
  final VoidCallback? onDismiss;

  @override
  State<YgsErrorToast> createState() => _YgsErrorToastState();
}

class _YgsErrorToastState extends State<YgsErrorToast>
    with SingleTickerProviderStateMixin {
  
  late AnimationController _animationController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _startShowAnimation();
    _scheduleHide();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _initAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -1.0), // 从顶部滑入
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeIn,
    ));
  }

  void _startShowAnimation() {
    _animationController.forward();
  }

  void _scheduleHide() {
    Future.delayed(widget.duration, () {
      if (mounted) {
        _hide();
      }
    });
  }

  void _hide() {
    _animationController.reverse().then((_) {
      if (mounted) {
        widget.onDismiss?.call();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: SafeArea(
        child: SlideTransition(
          position: _slideAnimation,
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.h),
              child: _buildToastCard(),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildToastCard() {
    return Row(
      mainAxisSize: MainAxisSize.min, // 关键：让Row收缩到内容大小
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          height: 80.h,
          padding: EdgeInsets.symmetric(horizontal: 40.w), // 增加左右空白
          decoration: BoxDecoration(
            color: YgsColors.backgroundCard, // 白色背景
            borderRadius: BorderRadius.circular(40.r), // 圆形圆角
            boxShadow: [
              BoxShadow(
                color: YgsColors.divider.withValues(alpha: 0.6),
                blurRadius: 20.r,
                offset: Offset(0, 8.h),
                spreadRadius: 4.r,
              ),
            ],
          ),
          child: Center(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 错误图标
                Icon(
                  Icons.error_outline_rounded,
                  size: 32.w,
                  color: YgsColors.errorPrimary,
                ),
                
                SizedBox(width: 12.w), // 图标和文字间距
                
                // 错误文字
                Text(
                  widget.message,
                  style: YgsTypography.bodyMedium.copyWith(
                    fontSize: 28.sp,
                    fontWeight: FontWeight.w500,
                    color: YgsColors.errorPrimary, // 使用主题错误色作为字体颜色
                    height: 1.2,
                    decoration: TextDecoration.none, // 移除任何文字装饰
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

/// YGS全局错误提示管理器
class YgsErrorToastManager {
  static OverlayEntry? _currentToast;

  /// 显示错误提示
  static void show(
    BuildContext context, {
    required String message,
    Duration duration = const Duration(seconds: 4),
  }) {
    // 先隐藏当前提示
    hide();

    final overlay = Overlay.of(context);
    _currentToast = OverlayEntry(
      builder: (context) => YgsErrorToast(
        message: message,
        duration: duration,
        onDismiss: () {
          hide();
        },
      ),
    );

    overlay.insert(_currentToast!);
  }

  /// 隐藏当前错误提示
  static void hide() {
    _currentToast?.remove();
    _currentToast = null;
  }
}