import 'package:freezed_annotation/freezed_annotation.dart';

part 'auth_response_models.freezed.dart';
part 'auth_response_models.g.dart';

/// 认证响应
@freezed
class AuthResponse with _$AuthResponse {
  const factory AuthResponse({
    required String accessToken,
    required String refreshToken,
    required User user,
    required int expiresIn,
  }) = _AuthResponse;

  factory AuthResponse.fromJson(Map<String, dynamic> json) =>
      _$AuthResponseFromJson(json);
}

/// 用户信息
@freezed
class User with _$User {
  const factory User({
    required String id,
    required int userNumber,
    required String username,
    String? email,
    String? phone,
    String? avatar,
    String? bio,
    DateTime? birthday,
    required DisplaySettings displaySettings,
    required DateTime createdAt,
    required DateTime updatedAt,
    required UserStats stats,
  }) = _User;

  factory User.fromJson(Map<String, dynamic> json) =>
      _$UserFromJson(json);
}

/// 用户展示设置
@freezed
class DisplaySettings with _$DisplaySettings {
  const factory DisplaySettings({
    @Default(true) bool showThemes,
    @Default(true) bool showTimeline,
    @Default(true) bool showReferences,
    @Default(true) bool showCharacters,
    @Default(true) bool showLightedCharacters,
    @Default(true) bool showBirthday,
    @Default(true) bool showBio,
    @Default(true) bool showStatistics,
  }) = _DisplaySettings;

  factory DisplaySettings.fromJson(Map<String, dynamic> json) =>
      _$DisplaySettingsFromJson(json);
}

/// 用户统计数据
@freezed
class UserStats with _$UserStats {
  const factory UserStats({
    @Default(0) int storiesCount,
    @Default(0) int charactersCount,
    @Default(0) int lightedCharactersCount,
    @Default(0) int referencesCount,
    @Default(0) int followersCount,
    @Default(0) int followingCount,
    @Default(0) int friendsCount,
  }) = _UserStats;

  factory UserStats.fromJson(Map<String, dynamic> json) =>
      _$UserStatsFromJson(json);
}

/// Token刷新响应
@freezed
class RefreshTokenResponse with _$RefreshTokenResponse {
  const factory RefreshTokenResponse({
    required String accessToken,
    required int expiresIn,
  }) = _RefreshTokenResponse;

  factory RefreshTokenResponse.fromJson(Map<String, dynamic> json) =>
      _$RefreshTokenResponseFromJson(json);
}