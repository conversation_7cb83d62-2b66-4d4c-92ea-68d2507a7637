import {
  IsString,
  IsEnum,
  IsOptional,
  IsInt,
  IsArray,
  IsUUID,
  MaxLength,
  MinLength,
  Min,
  ValidateNested,
} from "class-validator";
import { Type } from "class-transformer";
import { GroupType, GroupVisibility } from "../entities/friend-group.entity";

class GroupPermissionsDto {
  @IsOptional()
  @Type(() => Boolean)
  canViewProfile?: boolean = true;

  @IsOptional()
  @Type(() => Boolean)
  canViewStories?: boolean = true;

  @IsOptional()
  @Type(() => Boolean)
  canComment?: boolean = true;

  @IsOptional()
  @Type(() => Boolean)
  canShare?: boolean = true;

  @IsOptional()
  @Type(() => Boolean)
  canMessage?: boolean = true;
}

/**
 * 创建好友分组DTO
 */
export class CreateFriendGroupDto {
  @IsString({
    message: "分组名称必须是字符串",
  })
  @MinLength(1, {
    message: "分组名称不能为空",
  })
  @MaxLength(50, {
    message: "分组名称不能超过50个字符",
  })
  name: string;

  @IsOptional()
  @IsString({
    message: "分组描述必须是字符串",
  })
  @MaxLength(200, {
    message: "分组描述不能超过200个字符",
  })
  description?: string;

  @IsOptional()
  @IsEnum(GroupType, {
    message: "分组类型必须是有效的选项",
  })
  groupType?: GroupType = GroupType.CUSTOM;

  @IsOptional()
  @IsEnum(GroupVisibility, {
    message: "分组可见性必须是有效的选项",
  })
  visibility?: GroupVisibility = GroupVisibility.PRIVATE;

  @IsOptional()
  @IsString({
    message: "颜色标签必须是字符串",
  })
  @MaxLength(50, {
    message: "颜色标签不能超过50个字符",
  })
  colorTag?: string;

  @IsOptional()
  @IsInt({
    message: "排序权重必须是整数",
  })
  @Type(() => Number)
  @Min(0, {
    message: "排序权重不能为负数",
  })
  sortOrder?: number = 0;

  @IsOptional()
  @ValidateNested()
  @Type(() => GroupPermissionsDto)
  permissions?: GroupPermissionsDto;

  @IsOptional()
  @IsArray({
    message: "初始成员列表必须是数组",
  })
  @IsUUID("4", {
    each: true,
    message: "成员ID必须是有效的UUID格式",
  })
  initialMembers?: string[];

  @IsOptional()
  @Type(() => Object)
  metadata?: Record<string, unknown>;
}
