import {
  IsOptional,
  IsEnum,
  IsString,
  IsArray,
  IsBoolean,
  IsDateString,
} from "class-validator";
import { Transform, Type } from "class-transformer";
import {
  EventType,
  EventStatus,
  EventVisibility,
  EventImportance,
} from "../entities/timeline-event.entity";
import { PaginationQueryDto } from "../../../common/dto/pagination-query.dto";

/**
 * 时间线事件查询DTO
 */
export class TimelineQueryDto extends PaginationQueryDto {
  @IsOptional()
  @IsEnum(EventType, {
    message: "事件类型必须是有效的选项",
  })
  eventType?: EventType;

  @IsOptional()
  @IsEnum(EventStatus, {
    message: "事件状态必须是有效的选项",
  })
  status?: EventStatus = EventStatus.ACTIVE;

  @IsOptional()
  @IsEnum(EventVisibility, {
    message: "可见性设置必须是有效的选项",
  })
  visibility?: EventVisibility;

  @IsOptional()
  @IsEnum(EventImportance, {
    message: "重要程度必须是有效的选项",
  })
  minImportance?: EventImportance;

  @IsOptional()
  @IsArray({
    message: "标签筛选必须是数组",
  })
  @IsString({
    each: true,
    message: "每个标签必须是字符串",
  })
  tags?: string[];

  @IsOptional()
  @IsBoolean({
    message: "里程碑筛选必须是布尔值",
  })
  @Type(() => Boolean)
  isMilestone?: boolean;

  @IsOptional()
  @IsBoolean({
    message: "置顶筛选必须是布尔值",
  })
  @Type(() => Boolean)
  isPinned?: boolean;

  @IsOptional()
  @IsDateString(
    {},
    {
      message: "开始日期必须是有效的日期格式",
    },
  )
  startDate?: string;

  @IsOptional()
  @IsDateString(
    {},
    {
      message: "结束日期必须是有效的日期格式",
    },
  )
  endDate?: string;

  @IsOptional()
  @IsString({
    message: "搜索关键词必须是字符串",
  })
  search?: string;

  @IsOptional()
  @Transform(({ value }: { value: string }) => value?.toLowerCase())
  @IsString({
    message: "排序字段必须是字符串",
  })
  sortBy?: string = "eventDate";

  @IsOptional()
  @Transform(({ value }: { value: string }) => value?.toUpperCase())
  @Type(() => String)
  sortOrder?: "ASC" | "DESC" = "DESC";
}
