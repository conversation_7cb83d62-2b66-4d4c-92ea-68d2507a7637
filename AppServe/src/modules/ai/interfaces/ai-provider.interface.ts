/**
 * AI服务提供商接口
 * 定义AI服务的标准接口，支持多种AI服务提供商的接入
 *
 * 设计模式：策略模式
 * 目的：解耦AI服务的具体实现，支持后续替换不同的AI服务提供商
 */

/**
 * AI生成请求参数接口
 */
export interface AIGenerateRequest {
  /** 用户输入内容 */
  prompt: string;
  /** 最大生成长度 */
  maxLength?: number;
  /** 生成温度（创造性参数） */
  temperature?: number;
  /** 生成模型名称 */
  model?: string;
}

/**
 * AI生成响应结果接口
 */
export interface AIGenerateResponse {
  /** 生成的内容 */
  content: string;
  /** 输入令牌数 */
  promptTokens: number;
  /** 输出令牌数 */
  completionTokens: number;
  /** 总令牌数 */
  totalTokens: number;
  /** 生成模型 */
  model: string;
  /** 生成时间戳 */
  timestamp: Date;
}

/**
 * AI服务提供商接口
 * 所有AI服务提供商实现都必须遵循此接口
 */
export interface AIProvider {
  /** 服务提供商名称 */
  readonly name: string;

  /** 服务提供商版本 */
  readonly version: string;

  /** 是否可用 */
  isAvailable(): Promise<boolean>;

  /**
   * 生成文本内容
   * @param request 生成请求参数
   * @returns 生成响应结果
   */
  generateText(request: AIGenerateRequest): Promise<AIGenerateResponse>;

  /**
   * 生成标题
   * @param keywords 关键词数组
   * @returns 生成的标题
   */
  generateTitle(keywords: string[]): Promise<string>;

  /**
   * 生成故事内容
   * @param prompt 故事提示
   * @returns 生成的故事内容
   */
  generateStoryContent(prompt: string): Promise<string>;

  /**
   * 检查服务健康状态
   * @returns 健康状态信息
   */
  healthCheck(): Promise<{
    status: "healthy" | "degraded" | "unhealthy";
    latency: number;
    message?: string;
  }>;
}

/**
 * AI服务提供商配置接口
 */
export interface AIProviderConfig {
  /** API密钥 */
  apiKey: string;
  /** API端点 */
  apiEndpoint: string;
  /** 请求超时时间（毫秒） */
  timeout: number;
  /** 重试次数 */
  retries: number;
  /** 默认模型 */
  defaultModel: string;
  /** 是否启用 */
  enabled: boolean;
}
