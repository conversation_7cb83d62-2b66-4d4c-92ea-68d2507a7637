/**
 * 简化版API端点E2E测试
 *
 * 这个测试专注于验证API的基本可访问性和响应格式
 * 避免复杂的数据库依赖和类型检查问题
 */

import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import type { INestApplication } from "@nestjs/common";
import * as request from "supertest";
import { HealthController } from "../../src/modules/health/health.controller";
import { HealthService } from "../../src/modules/health/health.service";

describe("Simple API Endpoints (E2E)", () => {
  let app: INestApplication;

  beforeAll(async () => {
    // 创建最小化的测试模块，只包含健康检查
    const moduleFixture: TestingModule = await Test.createTestingModule({
      controllers: [HealthController],
      providers: [
        {
          provide: HealthService,
          useValue: {
            getHealth: jest.fn().mockResolvedValue({
              status: "ok",
              timestamp: new Date().toISOString(),
              uptime: process.uptime(),
            }),
            getDetailedHealth: jest.fn().mockResolvedValue({
              status: "ok",
              timestamp: new Date().toISOString(),
              uptime: process.uptime(),
              services: {
                database: { status: "ok", responseTime: 10 },
                redis: { status: "ok", responseTime: 5 },
              },
              system: {
                memory: { used: 100, total: 1000, percent: 10 },
                cpu: { percent: 15 },
              },
            }),
            getReadiness: jest.fn().mockResolvedValue({
              status: "ready",
              checks: {
                database: true,
                redis: true,
                migrations: true,
              },
            }),
            getLiveness: jest.fn().mockResolvedValue({
              status: "alive",
              timestamp: new Date().toISOString(),
            }),
          },
        },
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    if (app) {
      await app.close();
    }
  });

  describe("🔍 健康检查端点验证", () => {
    it("GET /health should return basic health status", async () => {
      const response = await request(app.getHttpServer())
        .get("/health")
        .expect(200);

      expect(response.body).toHaveProperty("status");
      expect(response.body.status).toBe("ok");
      expect(response.body).toHaveProperty("timestamp");
      expect(response.body).toHaveProperty("uptime");
      expect(typeof response.body.uptime).toBe("number");
    });

    it("GET /health/detailed should return detailed health information", async () => {
      const response = await request(app.getHttpServer())
        .get("/health/detailed")
        .expect(200);

      expect(response.body).toHaveProperty("status");
      expect(response.body).toHaveProperty("services");
      expect(response.body).toHaveProperty("system");
      expect(response.body.services).toHaveProperty("database");
      expect(response.body.services).toHaveProperty("redis");
      expect(response.body.system).toHaveProperty("memory");
      expect(response.body.system).toHaveProperty("cpu");
    });

    it("GET /health/ready should return readiness status", async () => {
      const response = await request(app.getHttpServer())
        .get("/health/ready")
        .expect(200);

      expect(response.body).toHaveProperty("status");
      expect(response.body.status).toBe("ready");
      expect(response.body).toHaveProperty("checks");
      expect(response.body.checks).toHaveProperty("database");
      expect(response.body.checks).toHaveProperty("redis");
    });

    it("GET /health/live should return liveness status", async () => {
      const response = await request(app.getHttpServer())
        .get("/health/live")
        .expect(200);

      expect(response.body).toHaveProperty("status");
      expect(response.body.status).toBe("alive");
      expect(response.body).toHaveProperty("timestamp");
    });
  });

  describe("🔧 HTTP基础特性验证", () => {
    it("should set correct content-type headers", async () => {
      const response = await request(app.getHttpServer())
        .get("/health")
        .expect(200);

      expect(response.headers["content-type"]).toMatch(/application\/json/);
    });

    it("should handle CORS preflight requests", async () => {
      const response = await request(app.getHttpServer())
        .options("/health")
        .expect(200);

      // CORS头应该被设置
      expect(response.headers).toBeDefined();
    });

    it("should return 404 for non-existent endpoints", async () => {
      await request(app.getHttpServer())
        .get("/non-existent-endpoint")
        .expect(404);
    });

    it("should handle method not allowed", async () => {
      await request(app.getHttpServer()).post("/health").expect(405);
    });
  });

  describe("⚡ 性能和响应时间验证", () => {
    it("should respond to health check within 1 second", async () => {
      const startTime = Date.now();

      await request(app.getHttpServer()).get("/health").expect(200);

      const responseTime = Date.now() - startTime;
      expect(responseTime).toBeLessThan(1000);
    });

    it("should handle concurrent requests efficiently", async () => {
      const concurrentRequests = Array.from({ length: 10 }, () =>
        request(app.getHttpServer()).get("/health").expect(200),
      );

      const startTime = Date.now();
      const responses = await Promise.all(concurrentRequests);
      const totalTime = Date.now() - startTime;

      // 10个并发请求应该在2秒内完成
      expect(totalTime).toBeLessThan(2000);

      // 所有响应都应该成功
      responses.forEach((response) => {
        expect(response.body.status).toBe("ok");
      });
    });
  });

  describe("🛡️ 安全性基础验证", () => {
    it("should not expose sensitive information in health endpoint", async () => {
      const response = await request(app.getHttpServer())
        .get("/health")
        .expect(200);

      const responseString = JSON.stringify(response.body).toLowerCase();

      // 确保不包含敏感词汇
      expect(responseString).not.toMatch(
        /password|secret|key|token|credential/,
      );
    });

    it("should handle malformed requests gracefully", async () => {
      // 发送带有超长URL的请求
      const longPath = "/health/" + "a".repeat(1000);

      const response = await request(app.getHttpServer()).get(longPath);

      // 应该返回404而不是500
      expect([404, 413, 414]).toContain(response.status);
    });

    it("should handle invalid characters in URL", async () => {
      const response = await request(app.getHttpServer())
        .get("/health/%00%01%02")
        .expect(404);

      expect(response.body).toBeDefined();
    });
  });

  describe("📊 响应格式标准化验证", () => {
    it("should return consistent timestamp format", async () => {
      const response = await request(app.getHttpServer())
        .get("/health")
        .expect(200);

      expect(response.body.timestamp).toMatch(
        /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/,
      );
    });

    it("should return numeric values in correct format", async () => {
      const response = await request(app.getHttpServer())
        .get("/health")
        .expect(200);

      expect(typeof response.body.uptime).toBe("number");
      expect(response.body.uptime).toBeGreaterThan(0);
    });

    it("should maintain consistent error response format", async () => {
      const response = await request(app.getHttpServer())
        .get("/non-existent")
        .expect(404);

      expect(response.body).toHaveProperty("statusCode");
      expect(response.body).toHaveProperty("message");
      expect(response.body.statusCode).toBe(404);
    });
  });

  describe("🧪 边界条件和鲁棒性测试", () => {
    it("should handle rapid successive requests", async () => {
      const requests = [];

      for (let i = 0; i < 20; i++) {
        requests.push(request(app.getHttpServer()).get("/health"));
      }

      const responses = await Promise.allSettled(requests);

      // 大多数请求应该成功
      const successCount = responses.filter(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (r) => r.status === "fulfilled" && (r.value as any).status === 200,
      ).length;

      expect(successCount).toBeGreaterThan(15); // 至少75%成功
    });

    it("should maintain functionality under stress", async () => {
      // 模拟轻微的负载测试
      const startTime = Date.now();
      const requests = Array.from({ length: 50 }, (_, i) =>
        request(app.getHttpServer())
          .get("/health")
          .then((res) => ({
            index: i,
            status: res.status,
            time: Date.now() - startTime,
          })),
      );

      const results = await Promise.all(requests);

      // 验证所有请求都成功
      const successfulRequests = results.filter((r) => r.status === 200);
      expect(successfulRequests.length).toBe(50);

      // 验证最后一个请求在合理时间内完成
      const lastRequestTime = Math.max(...results.map((r) => r.time));
      expect(lastRequestTime).toBeLessThan(5000); // 5秒内
    });
  });
});
