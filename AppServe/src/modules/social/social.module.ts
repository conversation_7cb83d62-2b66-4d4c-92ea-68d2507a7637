import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { SocialController } from "./social.controller";
import { SocialService } from "./social.service";
import { UserFollow } from "./entities/user-follow.entity";
import { FriendGroup } from "./entities/friend-group.entity";
import { FriendGroupMember } from "./entities/friend-group-member.entity";
import { User } from "../users/entities/user.entity";
import { AuthModule } from "../auth/auth.module";

@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserFollow,
      FriendGroup,
      FriendGroupMember,
      User,
    ]),
    AuthModule,
  ],
  controllers: [SocialController],
  providers: [SocialService],
  exports: [SocialService],
})
export class SocialModule {}
