#!/bin/bash

# ===========================================
# 有故事 APP - 构建清理脚本
# ===========================================

echo "🧹 开始清理构建产物和缓存文件..."

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 清理 Android 构建文件
echo -e "${YELLOW}🤖 清理 Android 构建文件...${NC}"
rm -rf android/build
rm -rf android/app/build
rm -rf android/.gradle
rm -rf android/app/.cxx
find . -name "*.apk" -not -path "./node_modules/*" -delete 2>/dev/null || true
find . -name "*.aab" -not -path "./node_modules/*" -delete 2>/dev/null || true

# 清理 iOS 构建文件
echo -e "${YELLOW}🍎 清理 iOS 构建文件...${NC}"
rm -rf ios/build
rm -rf ios/DerivedData

# 清理日志和临时文件
echo -e "${YELLOW}📝 清理日志和临时文件...${NC}"
find . -name "*.log" -not -path "./node_modules/*" -delete 2>/dev/null || true
find . -name "*.tmp" -not -path "./node_modules/*" -delete 2>/dev/null || true
find . -name ".DS_Store" -delete 2>/dev/null || true

# 清理 Metro 缓存
echo -e "${YELLOW}🔄 清理 Metro 缓存...${NC}"
npx react-native start --reset-cache --non-persistent 2>/dev/null || true

echo -e "${GREEN}🎉 清理完成！项目已经清理干净。${NC}"
