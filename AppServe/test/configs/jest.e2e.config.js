/**
 * 企业级端到端测试配置
 * 
 * 设计原则:
 * 1. 完整应用环境，真实HTTP请求测试
 * 2. 关键用户场景覆盖，验证完整业务价值
 * 3. 性能和稳定性验证，企业级服务质量
 * 4. 严格代码规范，确保测试代码质量
 */

const path = require('path');
const baseConfig = require('../../jest.config');

module.exports = {
  ...baseConfig,
  displayName: 'E2E Tests',
  rootDir: path.resolve(__dirname, '../..'),
  testMatch: [
    '<rootDir>/test/e2e/**/*.e2e.spec.ts'
  ],
  setupFilesAfterEnv: [
    '<rootDir>/test/e2e/setup/global-setup.ts',
    '<rootDir>/test/e2e/setup/app-setup.ts'
  ],
  globalSetup: '<rootDir>/test/e2e/setup/e2e-global-setup.ts',
  globalTeardown: '<rootDir>/test/e2e/setup/e2e-global-teardown.ts',
  testEnvironment: 'node',
  maxWorkers: 1, // 串行执行，避免端口冲突
  testTimeout: 120000, // 2分钟超时，适应完整应用启动
  
  // E2E测试覆盖率目标90%+
  coverageThreshold: {
    global: {
      branches: 90,
      functions: 95,
      lines: 90,
      statements: 90,
    },
  },
  
  // E2E测试覆盖范围 - 关注关键用户流程
  collectCoverageFrom: [
    'src/modules/**/**.controller.ts',
    'src/modules/**/**.service.ts',
    'src/common/guards/*.ts',
    'src/common/filters/*.ts',
    '!src/**/*.spec.ts',
    '!src/**/*.interface.ts',
    '!src/**/*.dto.ts',
  ],
  
  // 模块映射
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@test/(.*)$': '<rootDir>/test/$1',
    '^@e2e/(.*)$': '<rootDir>/test/e2e/$1',
  },
  
  // 详细报告
  verbose: true,
  collectCoverage: false, // E2E主要关注功能验证，覆盖率由集成测试保证
  coverageDirectory: '<rootDir>/coverage/e2e',
};