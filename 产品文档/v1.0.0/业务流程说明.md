# 有故事 - 业务流程说明 V1.0.0

## 📋 文档信息

**文档版本**: V1.0.0  
**创建时间**: 2025-07-22  
**更新时间**: 2025-07-22  
**维护团队**: YGS Development Team  
**文档类型**: 业务流程说明（详细实现指南）  
**关联文档**: 产品功能清单V1.0.0.md  
**基于分析**: 现有功能基础 + V1.0.0完整规划

---

## 🎯 业务流程总览

本文档详细描述YGS平台V1.0.0的核心业务流程，重点说明复杂和关键的业务逻辑实现。按重要性和复杂度分为：

### 🔴 核心创新流程（已完成）
1. **人物点亮系统完整流程** - 平台核心价值，复杂度最高
2. **双权限体系验证流程** - 6层故事权限 + 8项主页控制
3. **企业级认证安全流程** - 45+14天Token轮换机制

### 🟡 重要业务流程（部分完成）
4. **故事生命周期管理流程** - 创作→发布→权限控制→互动
5. **AI智能内容生成流程** - 配额管理+成本控制

### 🟠 待开发核心流程（未完成）
6. **故事引用系统流程** - 点亮用户引用管理
7. **内容互动系统流程** - 评论、收藏、分享、举报
8. **社交关系管理流程** - 关注、好友、分组权限传递

---

## 🎭 人物点亮系统完整流程详解 ✅ 已完成

### 📋 核心概念说明

#### 三个核心概念的关系
- **人物集**: 故事作者创建的所有人物档案（包括已点亮和未点亮）
- **点亮集**: 人物用户成功点亮的所有人物档案
- **点亮关系**: 连接故事、人物、用户的多对多关系

### 🔄 完整点亮流程（10个关键阶段）

## 阶段一：前置条件准备 ✅ 已实现

### 1. 故事作者创建故事和人物
```
故事作者 → 创建故事 → 添加人物到故事中
```
- **故事内容**: 包含具体的人物角色（1-100个人物）
- **人物信息**: 姓名（1-20字符）、关系、头像、描述
- **状态**: 人物初始状态为"未点亮"（isLighted=false）
- **权限设置**: 故事可设置6层访问权限
- **API接口**: `POST /stories` + `POST /characters`

### 2. 人物用户发现故事
```
人物用户 → 浏览故事 → 发现自己被描述在故事中
```
- **权限验证**: 用户需要有故事的查看权限（6层权限验证）
- **身份识别**: 用户认为故事中某个人物是自己
- **API接口**: `GET /stories/{id}` (含权限验证)

---

## 阶段二：点亮申请流程 ✅ 已实现

### 3. 人物用户提交点亮申请 ⭐ 核心业务逻辑
```
POST /stories/{storyId}/characters/{characterId}/light-request
```

**申请条件验证（企业级4层验证）**:
1. ✅ **人物存在验证**: 验证人物确实存在于指定故事中
2. ✅ **身份验证**: 验证申请者手机号与注册手机号一致（安全关键）
3. ✅ **重复申请防护**: 检查同一人物同一用户只能申请一次
4. ✅ **关系确认**: 展示当前关系设定，用户确认无误

**申请创建（原子操作）**:
```typescript
interface LightRequestCreation {
  requestId: string;           // UUID生成
  storyId: string;            // 故事ID
  characterId: string;        // 人物ID
  requesterId: string;        // 申请者ID
  storyAuthorId: string;      // 故事作者ID
  phoneVerification: string;   // 脱敏手机号
  message?: string;           // 申请留言
  status: 'pending';          // 初始状态
  expiresAt: Date;           // 7天后过期
  createdAt: Date;           // 申请时间
}
```

**业务规则实现**:
- **有效期管理**: 7天自动过期（定时清理任务）
- **脱敏显示**: 手机号显示为"138****8000"格式
- **分布式锁**: 防止并发重复申请
- **事务保证**: 申请创建的原子性

### 4. 系统通知故事作者 ✅ 已实现
```
系统 → 推送通知 → 故事作者
```
- **通知内容**: "用户XXX申请点亮故事《XXX》中的人物XXX"
- **显示信息**: 申请者昵称、人物信息、申请留言、剩余处理时间
- **操作选项**: 确认/拒绝按钮
- **API接口**: `POST /notifications` (自动触发)

---

## 阶段三：作者处理申请 ✅ 已实现

### 5. 故事作者查看申请列表
```
GET /light-requests?status=pending&sortBy=createdAt&sortOrder=DESC
```

**申请列表展示（企业级UI设计）**:
```typescript
interface LightRequestDisplay {
  id: string;
  requester: {
    nickname: string;         // 申请者昵称
    userNumber: string;      // 7位有故事号
    avatar: string;          // 头像URL
  };
  character: {
    name: string;            // 人物姓名
    relationship: string;    // 设定关系
    avatar?: string;         // 人物头像
  };
  story: {
    title: string;          // 故事标题
    id: string;             // 故事ID
  };
  phoneVerification: string; // 脱敏手机号
  message?: string;          // 申请留言
  timeRemaining: string;     // "6天23小时"格式
  createdAt: Date;          // 申请时间
}
```

### 6A. 确认点亮（正面流程）⭐ 核心技术实现
```
PUT /light-requests/{requestId}/confirm
```

**高并发安全处理（企业级实现）**:
```typescript
async function confirmLightRequest(requestId: string, confirmerId: string): Promise<void> {
  // 1. 获取分布式锁 - 防止并发确认（Redis实现）
  const lockKey = `light_request:${requestId}`;
  const lock = await this.redisService.acquireLock(lockKey, 30000);
  
  if (!lock) {
    throw new ConflictException('申请正在处理中，请稍后重试');
  }
  
  try {
    // 2. 开启数据库事务 - 保证原子性
    await this.dataSource.transaction(async manager => {
      // 3. 乐观锁检查 - 验证申请状态未变更
      const request = await manager.findOne(LightRequest, {
        where: { id: requestId, status: 'pending' },
        relations: ['story', 'character', 'requester'],
        lock: { mode: 'pessimistic_write' } // 悲观锁加强保护
      });
      
      if (!request) {
        throw new NotFoundException('申请不存在或已被处理');
      }
      
      // 4. 验证权限 - 确保只有故事作者可以确认
      if (request.story.authorId !== confirmerId) {
        throw new ForbiddenException('无权限处理该申请');
      }
      
      // 5. 检查申请是否过期
      if (request.expiresAt < new Date()) {
        throw new BadRequestException('申请已过期');
      }
      
      // 6. 原子状态更新
      await manager.update(LightRequest, requestId, {
        status: 'confirmed',
        processedAt: new Date(),
        confirmedBy: confirmerId
      });
      
      // 7. 创建人物点亮关系记录
      const lighting = await manager.save(CharacterLighting, {
        characterId: request.character.id,
        storyId: request.story.id,
        lighterUserId: request.requester.id,
        creatorUserId: request.story.authorId,
        requestId: request.id,
        status: 'active',
        confirmedAt: new Date()
      });
      
      // 8. 更新人物点亮状态（聚合计算）
      await this.updateCharacterLightingStatus(request.character.id, manager);
      
      // 9. 更新统计计数
      await manager.increment(Character, 
        { id: request.character.id }, 
        'lightingCount', 
        1
      );
      
      // 10. 记录业务日志
      await this.auditService.logLightingConfirmation({
        requestId,
        characterId: request.character.id,
        storyId: request.story.id,
        lighterUserId: request.requester.id,
        confirmerId
      });
    });
    
    // 11. 事务成功后的异步处理
    await this.handlePostConfirmationTasks(request);
    
  } finally {
    // 12. 释放分布式锁
    await this.redisService.releaseLock(lockKey, lock);
  }
}

// 事务外异步处理任务
private async handlePostConfirmationTasks(request: LightRequest): Promise<void> {
  try {
    // 发送正面通知给申请者
    await this.notificationService.sendPositiveNotification(
      request.requester.id,
      `您的点亮申请已被确认`,
      `恭喜！您成功点亮了故事《${request.story.title}》中的人物"${request.character.name}"`
    );
    
    // 清理相关缓存
    await this.cacheService.invalidateUserCache(request.requester.id);
    await this.cacheService.invalidateCharacterCache(request.character.id);
    
    // 触发推荐算法更新
    await this.recommendationService.updateUserProfile(request.requester.id);
    
  } catch (error) {
    // 异步任务失败不影响主流程，只记录日志
    this.logger.error('Post-confirmation tasks failed', error);
  }
}
```

### 6B. 拒绝申请（情绪保护机制）✅ 已实现
```
PUT /light-requests/{requestId}/reject
```

**情绪保护机制（产品创新点）**:
```typescript
async function rejectLightRequest(requestId: string, rejecterId: string): Promise<void> {
  await this.dataSource.transaction(async manager => {
    // 1. 验证和更新申请状态
    const request = await manager.findOne(LightRequest, {
      where: { id: requestId, status: 'pending' }
    });
    
    if (!request) {
      throw new NotFoundException('申请不存在或已被处理');
    }
    
    // 2. 更新为拒绝状态
    await manager.update(LightRequest, requestId, {
      status: 'rejected',
      processedAt: new Date(),
      rejectedBy: rejecterId
    });
    
    // 3. ❌ 关键：不发送拒绝通知给申请者
    // 这是情绪保护机制的核心 - 冷处理负面结果
    
    // 4. 只记录内部日志用于数据分析
    await this.auditService.logLightingRejection({
      requestId,
      rejectedBy: rejecterId,
      reason: 'user_rejected' // 区分用户拒绝和系统过期
    });
  });
  
  // 5. 后台静默处理
  await this.handleRejectionCleanup(requestId);
}
```

---

## 阶段四：状态更新和权限获得 ✅ 已实现

### 7. 复杂状态计算逻辑 ⭐ 技术难点

#### 人物点亮状态聚合计算
```typescript
async function updateCharacterLightingStatus(characterId: string, manager?: EntityManager): Promise<void> {
  const em = manager || this.dataSource.manager;
  
  // 1. 查询该人物在所有故事中的活跃点亮关系
  const activeLightings = await em.find(CharacterLighting, {
    where: { 
      characterId, 
      status: 'active' 
    },
    relations: ['story']
  });
  
  // 2. 状态计算规则
  const isLighted = activeLightings.length > 0;
  const lightingCount = activeLightings.length;
  
  // 3. 聚合更新人物状态
  await em.update(Character, characterId, {
    isLighted,
    lightingCount,
    lastLightedAt: isLighted ? new Date() : null
  });
  
  // 4. 如果是首次点亮，更新首次点亮信息
  if (isLighted && activeLightings.length === 1) {
    const firstLighting = activeLightings[0];
    await em.update(Character, characterId, {
      firstLightedAt: firstLighting.confirmedAt,
      firstLighterUserId: firstLighting.lighterUserId
    });
  }
  
  // 5. 缓存更新
  await this.cacheService.setCharacterLightingStatus(characterId, {
    isLighted,
    lightingCount,
    lightings: activeLightings.map(l => ({
      storyId: l.storyId,
      lighterUserId: l.lighterUserId,
      confirmedAt: l.confirmedAt
    }))
  });
}
```

#### 用户点亮集管理
```typescript
async function updateUserLightedCollection(userId: string): Promise<LightedCharacter[]> {
  // 1. 查询用户点亮的所有人物（跨故事聚合）
  const lightedCharacters = await this.lightingRepository
    .createQueryBuilder('lighting')
    .leftJoinAndSelect('lighting.character', 'character')
    .leftJoinAndSelect('lighting.story', 'story')
    .leftJoinAndSelect('story.author', 'author')
    .where('lighting.lighterUserId = :userId', { userId })
    .andWhere('lighting.status = :status', { status: 'active' })
    .orderBy('lighting.confirmedAt', 'DESC')
    .getMany();
  
  // 2. 按人物聚合（一个人物可能在多个故事中被点亮）
  const characterGroups = groupBy(lightedCharacters, 'character.id');
  
  // 3. 构建点亮集展示数据
  const lightedCollection = Object.values(characterGroups).map(lightings => {
    const character = lightings[0].character;
    const stories = lightings.map(l => l.story);
    
    return {
      character: {
        id: character.id,
        name: character.name,
        avatar: character.avatar,
        relationship: character.relationship
      },
      authorInfo: {
        id: stories[0].author.id,
        nickname: stories[0].author.nickname,
        userNumber: stories[0].author.userNumber
      },
      lightingInfo: {
        storyCount: stories.length,
        stories: stories.map(s => ({
          id: s.id,
          title: s.title,
          coverImage: s.coverImageUrl
        })),
        firstLightedAt: lightings.sort((a, b) => 
          a.confirmedAt.getTime() - b.confirmedAt.getTime()
        )[0].confirmedAt
      }
    };
  });
  
  return lightedCollection;
}
```

### 8. 点亮权益获得详解 ✅ 已实现

**点亮成功后用户获得的具体权益**:

#### 1. 故事引用权限（待开发API）
```typescript
// 点亮后自动获得引用权限检查
async function canReferenceStory(userId: string, storyId: string): Promise<boolean> {
  const lighting = await this.lightingRepository.findOne({
    where: {
      lighterUserId: userId,
      storyId: storyId,
      status: 'active'
    }
  });
  
  return !!lighting; // 有点亮关系即可引用
}
```

#### 2. 特殊访问权限
```typescript
// 对"仅人物可见"故事的永久访问权
async function hasCharacterAccess(userId: string, storyId: string): Promise<boolean> {
  if (story.permissionLevel !== 'characters_only') {
    return false; // 只对最高权限故事生效
  }
  
  const lighting = await this.lightingRepository.findOne({
    where: {
      lighterUserId: userId,
      storyId: storyId,
      status: 'active'
    }
  });
  
  return !!lighting; // 点亮用户获得永久访问权
}
```

#### 3. 高亮显示标识
```typescript
// 前端展示逻辑
interface CharacterDisplayInfo {
  id: string;
  name: string;
  avatar: string;
  relationship: string;
  isLighted: boolean;          // 是否被点亮
  isHighlighted: boolean;      // 是否高亮显示
  lightingBadge?: {            // 点亮徽章
    type: 'lighted';
    count: number;             // 被点亮次数
    latestLighter?: string;    // 最新点亮者
  };
}
```

#### 4. 点亮集个人展示
- **第一层列表**: 显示点亮人物名字、故事用户名字、出现故事数量
- **第二层详情**: 显示完整人物信息和相关故事卡片列表

---

## 阶段五：后续管理和维护 ✅ 已实现

### 9. 取消点亮（复杂状态管理）⭐ 技术难点
```
DELETE /characters/{characterId}/light (从特定故事取消)
DELETE /characters/{characterId}/lighting/{lightingId} (取消特定关系)
```

**复杂逻辑处理（多故事状态重新计算）**:
```typescript
async function cancelLighting(
  characterId: string, 
  storyId: string, 
  operatorId: string
): Promise<void> {
  const lockKey = `character_lighting:${characterId}`;
  const lock = await this.redisService.acquireLock(lockKey, 30000);
  
  try {
    await this.dataSource.transaction(async manager => {
      // 1. 验证取消权限（故事作者或点亮用户）
      const lighting = await manager.findOne(CharacterLighting, {
        where: { characterId, storyId, status: 'active' },
        relations: ['story', 'character']
      });
      
      if (!lighting) {
        throw new NotFoundException('点亮关系不存在');
      }
      
      const canCancel = lighting.story.authorId === operatorId || 
                       lighting.lighterUserId === operatorId;
      if (!canCancel) {
        throw new ForbiddenException('无权限取消该点亮关系');
      }
      
      // 2. 软删除点亮关系
      await manager.update(CharacterLighting, lighting.id, {
        status: 'cancelled',
        cancelledAt: new Date(),
        cancelledBy: operatorId
      });
      
      // 3. 重新计算人物整体点亮状态（关键步骤）
      const remainingLightings = await manager.find(CharacterLighting, {
        where: { characterId, status: 'active' }
      });
      
      // 4. 状态更新（可能从已点亮变为未点亮）
      const isLighted = remainingLightings.length > 0;
      await manager.update(Character, characterId, {
        isLighted,
        lightingCount: remainingLightings.length,
        lastCancelledAt: new Date()
      });
      
      // 5. 如果完全取消，清理首次点亮信息
      if (!isLighted) {
        await manager.update(Character, characterId, {
          firstLightedAt: null,
          firstLighterUserId: null
        });
      }
      
      // 6. 级联处理：如果有引用，标记引用失效（待开发）
      await this.handleLightingCancellationEffect(lighting, manager);
      
      // 7. 记录取消日志
      await this.auditService.logLightingCancellation({
        lightingId: lighting.id,
        characterId,
        storyId,
        cancelledBy: operatorId,
        reason: operatorId === lighting.lighterUserId ? 'user_cancelled' : 'author_cancelled'
      });
    });
    
  } finally {
    await this.redisService.releaseLock(lockKey, lock);
  }
}
```

### 10. 自动化维护任务 ✅ 已实现

#### 申请过期自动处理
```typescript
// 定时任务：每小时检查过期申请
@Cron('0 * * * *') // 每小时执行
async function cleanupExpiredRequests(): Promise<void> {
  const now = new Date();
  
  // 1. 查找过期申请
  const expiredRequests = await this.lightRequestRepository.find({
    where: {
      status: 'pending',
      expiresAt: LessThan(now)
    }
  });
  
  if (expiredRequests.length === 0) {
    return;
  }
  
  // 2. 批量更新状态
  await this.lightRequestRepository.update(
    { id: In(expiredRequests.map(r => r.id)) },
    { 
      status: 'expired',
      processedAt: now
    }
  );
  
  // 3. 记录清理日志
  this.logger.log(`清理过期点亮申请 ${expiredRequests.length} 条`);
  
  // 4. 通知监控系统
  await this.monitoringService.recordMetric('expired_light_requests', expiredRequests.length);
}
```

### 11. 已点亮人物的新故事通知流程 ✅ 已设计

#### 新故事发布时的通知机制
```typescript
// 新故事发布时检测已点亮人物
async function handleNewStoryPublished(
  storyId: string,
  authorId: string
): Promise<void> {
  // 1. 获取故事中的所有人物
  const storyCharacters = await this.storyCharacterRepository.find({
    where: { storyId },
    relations: ['character', 'character.lightings']
  });
  
  // 2. 找出已被点亮的人物
  const lightedCharacters = storyCharacters.filter(sc => 
    sc.character.lightings.some(l => l.status === 'active')
  );
  
  if (lightedCharacters.length === 0) {
    return;
  }
  
  // 3. 为每个已点亮的人物发送通知
  for (const sc of lightedCharacters) {
    const activeLightings = sc.character.lightings.filter(
      l => l.status === 'active' && l.creatorUserId === authorId
    );
    
    for (const lighting of activeLightings) {
      // 4. 创建确认通知
      await this.createLightingConfirmationNotification({
        userId: lighting.lighterUserId,
        characterId: sc.character.id,
        storyId: storyId,
        authorId: authorId,
        type: 'existing_character_new_story'
      });
    }
  }
}

// 处理用户确认的快速点亮
async function handleQuickLightingConfirmation(
  userId: string,
  characterId: string,
  storyId: string
): Promise<void> {
  return this.dataSource.transaction(async manager => {
    // 1. 验证用户已在其他故事点亮该人物
    const existingLighting = await manager.findOne(CharacterLighting, {
      where: {
        lighterUserId: userId,
        characterId: characterId,
        status: 'active'
      }
    });
    
    if (!existingLighting) {
      throw new BadRequestException('您未点亮过该人物');
    }
    
    // 2. 检查新故事中是否已有点亮关系
    const newStoryLighting = await manager.findOne(CharacterLighting, {
      where: {
        lighterUserId: userId,
        characterId: characterId,
        storyId: storyId
      }
    });
    
    if (newStoryLighting) {
      throw new ConflictException('您已在该故事中点亮此人物');
    }
    
    // 3. 直接创建点亮关系（跳过作者审批）
    const lighting = await manager.save(CharacterLighting, {
      characterId,
      storyId,
      lighterUserId: userId,
      creatorUserId: existingLighting.creatorUserId,
      status: 'active',
      confirmedAt: new Date(),
      confirmedBy: 'system_auto_approved',
      requestId: null // 无需申请记录
    });
    
    // 4. 更新人物点亮计数
    await manager.increment(Character, 
      { id: characterId }, 
      'lightingCount', 
      1
    );
    
    // 5. 发送成功通知
    await this.notificationService.sendPositiveNotification(
      userId,
      '点亮确认成功',
      `您已成功在新故事中延续点亮关系`
    );
  });
}

#### 数据一致性维护
```typescript
// 定时任务：每天检查数据一致性
@Cron('0 2 * * *') // 每天凌晨2点执行
async function syncLightingStatus(): Promise<void> {
  // 1. 检查人物点亮状态与实际点亮关系的一致性
  const characters = await this.characterRepository.find();
  
  for (const character of characters) {
    const actualLightings = await this.lightingRepository.count({
      where: { characterId: character.id, status: 'active' }
    });
    
    const recordedStatus = character.isLighted;
    const actualStatus = actualLightings > 0;
    
    // 2. 修复不一致状态
    if (recordedStatus !== actualStatus) {
      await this.characterRepository.update(character.id, {
        isLighted: actualStatus,
        lightingCount: actualLightings
      });
      
      this.logger.warn(`修复人物点亮状态不一致: ${character.id}`);
    }
  }
}
```

---

## 🔐 双权限体系验证流程详解 ✅ 已完成

### 📋 权限体系架构

YGS平台采用**双权限体系**，分离管理不同层面的权限控制：

#### 1. 故事访问权限（6层控制）
- **CHARACTERS_ONLY**: 仅故事中人物可见（最高权限）
- **PRIVATE**: 私密（仅自己可见）  
- **FRIENDS**: 好友可见
- **FOLLOWERS**: 关注者可见
- **GROUP_VISIBLE**: 分组可见
- **PUBLIC**: 公开（默认权限）

#### 2. 个人主页展示控制（8项模块开关）
- showThemes: 主题模块展示开关
- showTimeline: 时间线模块展示开关  
- showReferences: 引用模块展示开关
- showCharacters: 人物集模块展示开关
- showLightedCharacters: 点亮集模块展示开关
- showBirthday: 生日信息展示开关
- showBio: 个人简介展示开关
- showStatistics: 统计信息展示开关

### 🔄 故事访问权限验证流程

#### 权限验证算法（优先级递减）
```typescript
class StoryPermissionValidator {
  /**
   * 故事访问权限验证核心算法
   * @param story 故事对象
   * @param currentUser 当前访问用户
   * @param socialRelation 社交关系信息
   */
  static async validateStoryAccess(
    story: Story, 
    currentUser: User, 
    socialRelation?: SocialRelationship
  ): Promise<AccessResult> {
    
    // 0. 作者永远可以访问自己的故事
    if (story.authorId === currentUser.id) {
      return {
        allowed: true,
        reason: 'AUTHOR_ACCESS',
        level: 'OWNER'
      };
    }
    
    // 1. 最高优先级：仅故事中人物可见
    if (story.permissionLevel === StoryPermissionLevel.CHARACTERS_ONLY) {
      const hasLighting = await this.checkCharacterLighting(story.id, currentUser.id);
      return {
        allowed: hasLighting,
        reason: hasLighting ? 'CHARACTER_LIGHTED' : 'NOT_CHARACTER_IN_STORY',
        level: 'CHARACTERS_ONLY'
      };
    }
    
    // 2. 私密：仅作者可见
    if (story.permissionLevel === StoryPermissionLevel.PRIVATE) {
      return {
        allowed: false,
        reason: 'PRIVATE_STORY',
        level: 'PRIVATE'
      };
    }
    
    // 3. 好友可见
    if (story.permissionLevel === StoryPermissionLevel.FRIENDS) {
      const isFriend = await this.checkFriendship(story.authorId, currentUser.id);
      return {
        allowed: isFriend,
        reason: isFriend ? 'FRIEND_ACCESS' : 'NOT_FRIEND',
        level: 'FRIENDS'
      };
    }
    
    // 4. 关注者可见
    if (story.permissionLevel === StoryPermissionLevel.FOLLOWERS) {
      const isFollower = await this.checkFollowing(currentUser.id, story.authorId);
      return {
        allowed: isFollower,
        reason: isFollower ? 'FOLLOWER_ACCESS' : 'NOT_FOLLOWING',
        level: 'FOLLOWERS'
      };
    }
    
    // 5. 分组可见
    if (story.permissionLevel === StoryPermissionLevel.GROUP_VISIBLE) {
      const inAllowedGroup = await this.checkGroupMembership(
        currentUser.id, 
        story.allowedGroups
      );
      return {
        allowed: inAllowedGroup,
        reason: inAllowedGroup ? 'GROUP_MEMBER' : 'NOT_IN_ALLOWED_GROUP',
        level: 'GROUP_VISIBLE'
      };
    }
    
    // 6. 公开：所有人可见
    return {
      allowed: story.permissionLevel === StoryPermissionLevel.PUBLIC,
      reason: 'PUBLIC_ACCESS',
      level: 'PUBLIC'
    };
  }
  
  // 检查用户是否点亮了故事中的人物
  private static async checkCharacterLighting(
    storyId: string, 
    userId: string
  ): Promise<boolean> {
    const lighting = await this.lightingRepository.findOne({
      where: {
        storyId,
        lighterUserId: userId,
        status: 'active'
      }
    });
    
    return !!lighting;
  }
  
  // 检查好友关系（互相关注）
  private static async checkFriendship(
    authorId: string, 
    userId: string
  ): Promise<boolean> {
    const [following, followedBy] = await Promise.all([
      this.followRepository.findOne({
        where: { followerId: userId, followingId: authorId }
      }),
      this.followRepository.findOne({
        where: { followerId: authorId, followingId: userId }
      })
    ]);
    
    return !!(following && followedBy); // 互相关注才是好友
  }
  
  // 检查关注关系
  private static async checkFollowing(
    followerId: string,
    followingId: string
  ): Promise<boolean> {
    const follow = await this.followRepository.findOne({
      where: { followerId, followingId }
    });
    
    return !!follow;
  }
  
  // 检查分组成员资格
  private static async checkGroupMembership(
    userId: string,
    allowedGroups: string[]
  ): Promise<boolean> {
    if (!allowedGroups || allowedGroups.length === 0) {
      return false;
    }
    
    const membership = await this.groupMemberRepository.findOne({
      where: {
        userId,
        groupId: In(allowedGroups)
      }
    });
    
    return !!membership;
  }
}
```

### 🎭 个人主页权限控制流程

#### 主页展示权限验证
```typescript
class ProfileDisplayController {
  /**
   * 个人主页内容展示过滤
   * @param profileOwner 主页所有者
   * @param visitor 访问者
   */
  async getFilteredProfile(
    profileOwner: User,
    visitor: User
  ): Promise<FilteredProfile> {
    
    const settings = profileOwner.profileDisplaySettings;
    const isOwner = profileOwner.id === visitor.id;
    
    // 主页所有者看到完整信息
    if (isOwner) {
      return this.getFullProfile(profileOwner);
    }
    
    // 根据8项开关过滤内容
    const filteredProfile: FilteredProfile = {
      basicInfo: {
        nickname: profileOwner.nickname,
        userNumber: profileOwner.userNumber,
        avatar: profileOwner.avatarUrl
      }
    };
    
    // 1. 生日信息展示控制
    if (settings.showBirthday) {
      filteredProfile.birthday = profileOwner.birthday;
    }
    
    // 2. 个人简介展示控制
    if (settings.showBio) {
      filteredProfile.bio = profileOwner.bio;
    }
    
    // 3. 主题模块展示控制
    if (settings.showThemes) {
      filteredProfile.themes = await this.getUserThemes(profileOwner.id);
    }
    
    // 4. 时间线模块展示控制（待开发）
    if (settings.showTimeline) {
      filteredProfile.timeline = await this.getUserTimeline(profileOwner.id);
    }
    
    // 5. 引用模块展示控制（待开发）
    if (settings.showReferences) {
      filteredProfile.references = await this.getUserReferences(profileOwner.id);
    }
    
    // 6. 人物集模块展示控制
    if (settings.showCharacters) {
      filteredProfile.characters = await this.getUserCharacters(profileOwner.id);
    }
    
    // 7. 点亮集模块展示控制
    if (settings.showLightedCharacters) {
      filteredProfile.lightedCharacters = await this.getUserLightedCharacters(profileOwner.id);
    }
    
    // 8. 统计信息展示控制
    if (settings.showStatistics) {
      filteredProfile.statistics = await this.getUserStatistics(profileOwner.id);
    }
    
    return filteredProfile;
  }
}
```

---

## 🔑 企业级认证安全流程详解 ✅ 已完成

### 🎯 45分钟+14天Token轮换机制

#### Token生成流程
```typescript
class TokenService {
  /**
   * 生成Token对（Access + Refresh）
   */
  async generateTokenPair(
    user: User, 
    deviceInfo: DeviceInfo
  ): Promise<TokenPair> {
    
    // 1. 生成45分钟访问令牌
    const accessTokenPayload = {
      sub: user.id,                    // 用户ID
      userNumber: user.userNumber,     // 7位有故事号
      deviceId: deviceInfo.fingerprint, // 设备指纹
      type: 'access',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 2700 // 45分钟
    };
    
    const accessToken = this.jwtService.sign(accessTokenPayload);
    
    // 2. 生成14天刷新令牌
    const refreshTokenPayload = {
      sub: user.id,
      deviceId: deviceInfo.fingerprint,
      type: 'refresh',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 1209600 // 14天
    };
    
    const refreshToken = this.jwtService.sign(refreshTokenPayload);
    
    // 3. 存储刷新令牌记录
    await this.saveRefreshToken({
      userId: user.id,
      tokenHash: this.hashToken(refreshToken),
      deviceFingerprint: deviceInfo.fingerprint,
      deviceInfo: deviceInfo,
      expiresAt: new Date(Date.now() + 1209600 * 1000),
      isActive: true
    });
    
    // 4. 限制并发会话（最多3个）
    await this.limitConcurrentSessions(user.id, deviceInfo.fingerprint);
    
    return {
      accessToken,
      refreshToken,
      expiresIn: 2700 // 45分钟（秒）
    };
  }
  
  /**
   * Token轮换刷新
   */
  async refreshTokenPair(refreshToken: string): Promise<TokenPair> {
    // 1. 验证刷新令牌
    let payload: any;
    try {
      payload = this.jwtService.verify(refreshToken);
    } catch (error) {
      throw new UnauthorizedException('刷新令牌无效');
    }
    
    // 2. 查找令牌记录
    const tokenRecord = await this.refreshTokenRepository.findOne({
      where: {
        tokenHash: this.hashToken(refreshToken),
        isActive: true,
        expiresAt: MoreThan(new Date())
      },
      relations: ['user']
    });
    
    if (!tokenRecord) {
      throw new UnauthorizedException('刷新令牌不存在或已过期');
    }
    
    // 3. 验证设备指纹
    if (tokenRecord.deviceFingerprint !== payload.deviceId) {
      throw new UnauthorizedException('设备验证失败');
    }
    
    // 4. 撤销旧的刷新令牌（轮换机制）
    await this.refreshTokenRepository.update(tokenRecord.id, {
      isActive: false,
      revokedAt: new Date()
    });
    
    // 5. 生成新的令牌对
    const deviceInfo: DeviceInfo = {
      fingerprint: tokenRecord.deviceFingerprint,
      ...tokenRecord.deviceInfo
    };
    
    return this.generateTokenPair(tokenRecord.user, deviceInfo);
  }
  
  /**
   * 限制并发会话
   */
  private async limitConcurrentSessions(
    userId: string, 
    deviceFingerprint: string
  ): Promise<void> {
    // 查找用户的所有活跃令牌
    const activeTokens = await this.refreshTokenRepository.find({
      where: { userId, isActive: true },
      order: { createdAt: 'DESC' }
    });
    
    // 保留当前设备和最新的2个会话，撤销其他的
    const tokensToKeep = activeTokens.filter(token => 
      token.deviceFingerprint === deviceFingerprint
    ).slice(0, 1).concat(
      activeTokens.filter(token => 
        token.deviceFingerprint !== deviceFingerprint
      ).slice(0, 2)
    );
    
    const tokensToRevoke = activeTokens.filter(token => 
      !tokensToKeep.includes(token)
    );
    
    if (tokensToRevoke.length > 0) {
      await this.refreshTokenRepository.update(
        { id: In(tokensToRevoke.map(t => t.id)) },
        { 
          isActive: false,
          revokedAt: new Date(),
          revokeReason: 'concurrent_limit_exceeded'
        }
      );
    }
  }
}
```

#### 自动刷新触发机制
```typescript
class AutoRefreshService {
  /**
   * 客户端自动刷新策略
   */
  shouldRefreshToken(accessToken: string): boolean {
    try {
      const payload = this.jwtService.decode(accessToken) as any;
      if (!payload || !payload.exp) {
        return true; // 无效令牌，需要刷新
      }
      
      const currentTime = Math.floor(Date.now() / 1000);
      const expirationTime = payload.exp;
      const timeUntilExpiry = expirationTime - currentTime;
      
      // 过期前10分钟（600秒）开始刷新
      return timeUntilExpiry <= 600;
      
    } catch (error) {
      return true; // 解析失败，需要刷新
    }
  }
  
  /**
   * 前端刷新时机
   */
  getRefreshTriggers(): RefreshTrigger[] {
    return [
      {
        type: 'PREVENTIVE',
        description: '访问令牌过期前10分钟自动刷新',
        interval: 60000 // 每分钟检查一次
      },
      {
        type: 'ON_REQUEST',  
        description: '每次API调用前检查Token状态',
        trigger: 'before_api_call'
      },
      {
        type: 'APP_RESUME',
        description: 'App从后台恢复时检查Token',
        trigger: 'app_state_change'
      },
      {
        type: 'PERIODIC',
        description: '每小时定期检查Token状态',
        interval: 3600000 // 1小时
      }
    ];
  }
}
```

---

## 📖 故事生命周期管理流程详解 ✅ 已完成

### 🔄 故事完整生命周期

#### 故事状态流转图
```
草稿(DRAFT) → 发布(PUBLISHED) → 归档(ARCHIVED)
     ↓              ↓               ↓
   删除(DELETED) ← 删除(DELETED) ← 删除(DELETED)
```

### 📝 故事创建流程（企业级内容安全）

```typescript
class StoryCreationService {
  /**
   * 故事创建完整流程
   */
  async createStory(
    authorId: string,
    createStoryDto: CreateStoryDto
  ): Promise<Story> {
    
    // 1. 前置验证
    await this.validateStoryCreation(authorId, createStoryDto);
    
    return this.dataSource.transaction(async manager => {
      // 2. AI内容安全预检
      const safetyResult = await this.contentSecurityService.checkContent(
        createStoryDto.content,
        'story'
      );
      
      if (!safetyResult.isPass && safetyResult.riskLevel === 'HIGH') {
        throw new BadRequestException(
          `内容违规：${safetyResult.violations.join(', ')}`
        );
      }
      
      // 3. 图片处理和验证
      const processedImages = await this.processStoryImages(
        createStoryDto.images
      );
      
      // 4. 创建故事记录
      const story = await manager.save(Story, {
        authorId,
        title: createStoryDto.title,
        content: createStoryDto.content,
        images: processedImages,
        themeId: createStoryDto.themeId,
        storyDate: createStoryDto.storyDate,
        location: createStoryDto.location,
        permissionLevel: createStoryDto.permissionLevel || 'public',
        allowedGroups: createStoryDto.allowedGroups,
        allowComments: createStoryDto.allowComments ?? true,
        allowLikes: createStoryDto.allowLikes ?? true,
        allowSharing: createStoryDto.allowSharing ?? true,
        status: StoryStatus.DRAFT, // 初始状态为草稿
        aiSafetyScore: safetyResult.score
      });
      
      // 5. 处理人物关联
      if (createStoryDto.characterIds?.length > 0) {
        await this.linkCharactersToStory(
          story.id, 
          createStoryDto.characterIds, 
          manager
        );
      }
      
      // 6. 内容审核分流
      await this.handleContentReview(story, safetyResult, manager);
      
      // 7. 记录创作日志
      await this.auditService.logStoryCreation({
        storyId: story.id,
        authorId,
        contentLength: createStoryDto.content.length,
        imageCount: processedImages.length,
        safetyScore: safetyResult.score
      });
      
      return story;
    });
  }
  
  /**
   * 内容审核分流处理
   */
  private async handleContentReview(
    story: Story,
    safetyResult: ContentSafetyResult,
    manager: EntityManager
  ): Promise<void> {
    
    if (safetyResult.riskLevel === 'LOW') {
      // 低风险内容：直接通过，可发布
      await manager.update(Story, story.id, {
        reviewStatus: 'approved',
        reviewedAt: new Date()
      });
      
    } else if (safetyResult.riskLevel === 'MEDIUM') {
      // 中风险内容：进入人工审核队列
      await manager.save(StoryReview, {
        storyId: story.id,
        reviewType: 'human_review',
        status: 'pending',
        aiScore: safetyResult.score,
        flaggedReasons: safetyResult.violations
      });
      
      // 通知审核团队
      await this.notificationService.notifyReviewTeam(story.id);
      
    } else {
      // 高风险内容：自动拒绝
      await manager.update(Story, story.id, {
        reviewStatus: 'rejected',
        reviewedAt: new Date(),
        rejectionReason: `AI安全检测不通过：${safetyResult.violations.join(', ')}`
      });
    }
  }
}
```

### 📤 故事发布流程

```typescript
class StoryPublishService {
  /**
   * 故事发布流程
   */
  async publishStory(storyId: string, authorId: string): Promise<void> {
    const story = await this.storyRepository.findOne({
      where: { id: storyId, authorId },
      relations: ['characters']
    });
    
    if (!story) {
      throw new NotFoundException('故事不存在');
    }
    
    if (story.status === StoryStatus.PUBLISHED) {
      throw new BadRequestException('故事已发布');
    }
    
    if (story.reviewStatus === 'rejected') {
      throw new BadRequestException('故事审核未通过，无法发布');
    }
    
    await this.dataSource.transaction(async manager => {
      // 1. 更新故事状态
      await manager.update(Story, storyId, {
        status: StoryStatus.PUBLISHED,
        publishedAt: new Date()
      });
      
      // 2. 触发相关业务逻辑
      await Promise.all([
        // 更新作者统计
        this.updateAuthorStatistics(authorId, 'story_published'),
        
        // 推送给关注者
        this.pushToFollowers(authorId, story),
        
        // 更新推荐算法数据
        this.recommendationService.updateContentPool(story),
        
        // 如果有人物，通知可能的点亮用户
        this.notifyPotentialLighters(story)
      ]);
      
      // 3. 记录发布日志
      await this.auditService.logStoryPublishing({
        storyId,
        authorId,
        publishedAt: new Date(),
        characterCount: story.characters?.length || 0
      });
    });
  }
}
```

---

## 🤖 AI智能内容生成流程详解 ✅ 已完成

### 🎯 配额管理和成本控制

#### AI配额检查和扣减流程
```typescript
class AiQuotaService {
  /**
   * 检查并扣减AI配额（原子操作）
   */
  async checkAndConsumeQuota(
    userId: string,
    quotaType: 'title' | 'content' = 'content'
  ): Promise<QuotaCheckResult> {
    
    const today = this.getToday();
    const quotaKey = `ai_quota:${userId}:${today}`;
    
    // 1. 原子操作：检查并递增使用次数（Redis）
    const currentUsage = await this.redisService.incr(quotaKey);
    
    // 2. 首次使用设置过期时间
    if (currentUsage === 1) {
      const endOfDay = this.getEndOfDay();
      await this.redisService.expireAt(quotaKey, endOfDay);
    }
    
    // 3. 获取用户配额限制
    const userQuota = await this.getUserQuotaLimit(userId);
    
    // 4. 检查是否超出配额
    if (currentUsage > userQuota.dailyLimit) {
      // 超出配额，回滚操作
      await this.redisService.decr(quotaKey);
      
      return {
        allowed: false,
        reason: 'QUOTA_EXCEEDED',
        usage: currentUsage - 1,
        limit: userQuota.dailyLimit,
        resetAt: this.getTomorrowStart()
      };
    }
    
    // 5. 记录使用日志
    await this.logAiUsage(userId, quotaType, currentUsage);
    
    return {
      allowed: true,
      reason: 'QUOTA_AVAILABLE',
      usage: currentUsage,
      limit: userQuota.dailyLimit,
      remaining: userQuota.dailyLimit - currentUsage
    };
  }
  
  /**
   * 获取用户配额限制（会员体系预留）
   */
  private async getUserQuotaLimit(userId: string): Promise<UserQuotaLimit> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      select: ['membershipLevel', 'aiQuotaOverride']
    });
    
    // 会员体系配额设计
    const quotaLimits = {
      'free': 5,        // 普通用户：5次/天
      'premium': 50,    // 会员用户：50次/天（预留）
      'vip': 200        // VIP用户：200次/天（预留）
    };
    
    const membershipLevel = user?.membershipLevel || 'free';
    const baseLimit = quotaLimits[membershipLevel] || quotaLimits.free;
    
    // 管理员可以单独设置用户配额
    const finalLimit = user?.aiQuotaOverride || baseLimit;
    
    return {
      dailyLimit: finalLimit,
      membershipLevel,
      hasOverride: !!user?.aiQuotaOverride
    };
  }
}
```

#### AI内容生成流程
```typescript
class AiContentGenerationService {
  /**
   * 生成故事内容
   */
  async generateStoryContent(
    userId: string,
    prompt: GenerateContentDto
  ): Promise<AiGeneratedContent> {
    
    // 1. 配额检查和扣减
    const quotaResult = await this.quotaService.checkAndConsumeQuota(
      userId, 
      'content'
    );
    
    if (!quotaResult.allowed) {
      throw new TooManyRequestsException(
        `AI配额已用完，明日${quotaResult.resetAt}重置`
      );
    }
    
    // 2. 输入内容安全检查
    const safetyCheck = await this.contentSecurityService.checkPrompt(
      prompt.prompt
    );
    
    if (!safetyCheck.isPass) {
      // 配额回滚（输入不安全）
      await this.quotaService.refundQuota(userId);
      throw new BadRequestException('输入内容包含敏感信息');
    }
    
    // 3. 调用AI服务生成内容
    const startTime = Date.now();
    let generatedContent: string;
    let tokenUsage: TokenUsage;
    
    try {
      const aiResult = await this.aiProviderService.generateContent({
        prompt: prompt.prompt,
        maxTokens: prompt.maxLength || 1000,
        temperature: 0.7,
        model: 'gpt-3.5-turbo'
      });
      
      generatedContent = aiResult.content;
      tokenUsage = aiResult.usage;
      
    } catch (error) {
      // AI调用失败，回滚配额
      await this.quotaService.refundQuota(userId);
      throw new InternalServerErrorException('AI服务暂时不可用');
    }
    
    const processingTime = Date.now() - startTime;
    
    // 4. 输出内容安全检查
    const outputSafetyCheck = await this.contentSecurityService.checkContent(
      generatedContent,
      'ai_generated'
    );
    
    if (!outputSafetyCheck.isPass) {
      // 生成内容不安全，不扣减配额但记录
      await this.quotaService.refundQuota(userId);
      await this.logUnsafeGeneration(userId, prompt, generatedContent);
      throw new BadRequestException('生成内容不符合社区规范，请调整提示词');
    }
    
    // 5. 记录生成日志和统计
    await Promise.all([
      this.logAiGeneration({
        userId,
        promptTokens: tokenUsage.promptTokens,
        completionTokens: tokenUsage.completionTokens,
        totalTokens: tokenUsage.totalTokens,
        processingTimeMs: processingTime,
        success: true
      }),
      
      this.saveGeneratedContent({
        userId,
        contentType: 'story_content',
        originalPrompt: prompt.prompt,
        generatedContent,
        safetyScore: outputSafetyCheck.score
      })
    ]);
    
    return {
      content: generatedContent,
      usage: {
        current: quotaResult.usage,
        remaining: quotaResult.remaining,
        limit: quotaResult.limit,
        resetAt: quotaResult.resetAt
      },
      metadata: {
        tokenUsage,
        processingTimeMs: processingTime,
        safetyScore: outputSafetyCheck.score
      }
    };
  }
}
```

---

## 📋 待开发业务流程设计

以下业务流程尚未实现，但已完成详细的流程设计：

## 🔗 故事引用系统流程详解 ❌ 待开发

### 📋 业务概述
允许点亮用户将自己被点亮的故事引用到个人引用集，实现内容的个性化管理和展示。

### 🔄 完整引用流程设计

#### 1. 引用资格验证流程
```typescript
// 待开发：引用权限检查
async function canReferenceStory(
  userId: string, 
  storyId: string
): Promise<ReferenceEligibility> {
  
  // 1. 检查点亮关系
  const lighting = await this.lightingRepository.findOne({
    where: {
      lighterUserId: userId,
      storyId: storyId,
      status: 'active'
    },
    relations: ['story', 'character']
  });
  
  if (!lighting) {
    return {
      eligible: false,
      reason: 'NOT_LIGHTED_CHARACTER',
      message: '只有故事中的点亮人物可以引用该故事'
    };
  }
  
  // 2. 检查是否已引用
  const existingReference = await this.referenceRepository.findOne({
    where: { userId, storyId }
  });
  
  if (existingReference) {
    return {
      eligible: false,
      reason: 'ALREADY_REFERENCED',
      message: '您已经引用过这个故事'
    };
  }
  
  // 3. 检查原故事状态
  if (lighting.story.status !== StoryStatus.PUBLISHED) {
    return {
      eligible: false,
      reason: 'STORY_NOT_PUBLISHED',
      message: '只能引用已发布的故事'
    };
  }
  
  return {
    eligible: true,
    lighting: {
      characterId: lighting.character.id,
      characterName: lighting.character.name,
      relationship: lighting.character.relationship,
      lightedAt: lighting.confirmedAt
    }
  };
}
```

#### 2. 创建引用流程设计
```
POST /stories/{storyId}/reference
```

**引用创建业务逻辑**:
```typescript
// 待开发：创建故事引用
async function createStoryReference(
  userId: string,
  storyId: string,
  createDto: CreateReferenceDto
): Promise<StoryReference> {
  
  return this.dataSource.transaction(async manager => {
    // 1. 权限验证
    const eligibility = await this.canReferenceStory(userId, storyId);
    if (!eligibility.eligible) {
      throw new ForbiddenException(eligibility.message);
    }
    
    // 2. 创建引用记录
    const reference = await manager.save(StoryReference, {
      userId,
      storyId,
      characterId: eligibility.lighting.characterId,
      category: createDto.category || '默认分类',
      tags: createDto.tags || [],
      note: createDto.note,
      sortOrder: await this.getNextSortOrder(userId),
      isStoryDeleted: false
    });
    
    // 3. 更新故事引用统计
    await manager.increment(Story, { id: storyId }, 'referenceCount', 1);
    
    // 4. 记录引用日志
    await this.auditService.logStoryReference({
      referenceId: reference.id,
      userId,
      storyId,
      characterId: eligibility.lighting.characterId,
      action: 'created'
    });
    
    return reference;
  });
}
```

#### 3. 引用集查询流程设计
```
GET /users/{userId}/references?category=&status=&sortBy=&page=1&limit=20
```

**引用集展示逻辑**:
```typescript
// 待开发：获取用户引用集
async function getUserReferences(
  userId: string,
  query: ReferenceQueryDto
): Promise<PaginatedReferences> {
  
  const queryBuilder = this.referenceRepository
    .createQueryBuilder('ref')
    .leftJoinAndSelect('ref.story', 'story')
    .leftJoinAndSelect('ref.character', 'character')
    .leftJoinAndSelect('story.author', 'author')
    .where('ref.userId = :userId', { userId });
  
  // 1. 分类筛选
  if (query.category) {
    queryBuilder.andWhere('ref.category = :category', { 
      category: query.category 
    });
  }
  
  // 2. 状态筛选
  if (query.status === 'active') {
    queryBuilder.andWhere('ref.isStoryDeleted = false');
  } else if (query.status === 'broken') {
    queryBuilder.andWhere('ref.isStoryDeleted = true');
  }
  
  // 3. 排序
  const sortBy = query.sortBy || 'createdAt';
  const sortOrder = query.sortOrder || 'DESC';
  queryBuilder.orderBy(`ref.${sortBy}`, sortOrder);
  
  // 4. 分页
  const [references, total] = await queryBuilder
    .skip((query.page - 1) * query.limit)
    .take(query.limit)
    .getManyAndCount();
  
  // 5. 构建展示数据
  const items = references.map(ref => ({
    id: ref.id,
    category: ref.category,
    tags: ref.tags,
    note: ref.note,
    createdAt: ref.createdAt,
    character: {
      id: ref.character.id,
      name: ref.character.name,
      relationship: ref.character.relationship
    },
    story: ref.isStoryDeleted ? null : {
      id: ref.story.id,
      title: ref.story.title,
      coverImage: ref.story.coverImageUrl,
      author: {
        nickname: ref.story.author.nickname,
        userNumber: ref.story.author.userNumber
      }
    },
    status: ref.isStoryDeleted ? 'broken' : 'active'
  }));
  
  return {
    items,
    total,
    page: query.page,
    limit: query.limit,
    hasNext: (query.page * query.limit) < total
  };
}
```

#### 4. 引用失效处理流程设计
```typescript
// 待开发：故事删除时的级联处理
async function handleStoryDeletion(storyId: string): Promise<void> {
  // 1. 标记相关引用为失效（软标记，不删除）
  await this.referenceRepository.update(
    { storyId },
    { 
      isStoryDeleted: true,
      storyDeletedAt: new Date()
    }
  );
  
  // 2. 不主动通知用户（避免负面情绪）
  // 用户下次查看引用集时会看到失效提示
  
  // 3. 记录失效日志
  const affectedReferences = await this.referenceRepository.count({
    where: { storyId }
  });
  
  await this.auditService.logReferenceInvalidation({
    storyId,
    affectedCount: affectedReferences,
    reason: 'story_deleted'
  });
}
```

---

## 💬 内容互动系统流程详解 ❌ 待开发

### 📋 评论系统设计

#### 评论创建流程设计
```
POST /comments
```

**多级评论业务逻辑**:
```typescript
// 待开发：创建评论
async function createComment(
  userId: string,
  createDto: CreateCommentDto
): Promise<Comment> {
  
  return this.dataSource.transaction(async manager => {
    // 1. 验证评论权限
    const story = await manager.findOne(Story, {
      where: { id: createDto.storyId },
      select: ['id', 'authorId', 'allowComments', 'permissionLevel']
    });
    
    if (!story) {
      throw new NotFoundException('故事不存在');
    }
    
    if (!story.allowComments) {
      throw new ForbiddenException('该故事不允许评论');
    }
    
    // 2. 验证故事访问权限
    const hasAccess = await this.storyPermissionService.validateAccess(
      story,
      { id: userId }
    );
    
    if (!hasAccess.allowed) {
      throw new ForbiddenException('无权限评论该故事');
    }
    
    // 3. 处理多级回复
    let parentComment = null;
    if (createDto.parentId) {
      parentComment = await manager.findOne(Comment, {
        where: { id: createDto.parentId, storyId: createDto.storyId }
      });
      
      if (!parentComment) {
        throw new NotFoundException('回复的评论不存在');
      }
      
      // 限制回复层级（最多3级）
      const replyLevel = await this.calculateReplyLevel(createDto.parentId);
      if (replyLevel >= 3) {
        throw new BadRequestException('回复层级过深，请回复上级评论');
      }
    }
    
    // 4. 内容安全检查
    const safetyResult = await this.contentSecurityService.checkContent(
      createDto.content,
      'comment'
    );
    
    if (!safetyResult.isPass) {
      throw new BadRequestException('评论内容不符合社区规范');
    }
    
    // 5. 创建评论
    const comment = await manager.save(Comment, {
      storyId: createDto.storyId,
      userId,
      parentId: createDto.parentId,
      content: createDto.content,
      safetyScore: safetyResult.score,
      likeCount: 0,
      replyCount: 0
    });
    
    // 6. 更新统计数据
    await Promise.all([
      // 更新故事评论数
      manager.increment(Story, { id: createDto.storyId }, 'commentCount', 1),
      
      // 更新父评论回复数
      parentComment && manager.increment(
        Comment, 
        { id: parentComment.id }, 
        'replyCount', 
        1
      )
    ]);
    
    // 7. 发送通知（正面通知策略）
    await this.sendCommentNotifications(comment, story, parentComment);
    
    return comment;
  });
}
```

### 📋 收藏系统设计

#### 收藏管理流程设计
```typescript
// 待开发：收藏故事
async function bookmarkStory(
  userId: string,
  storyId: string,
  category?: string
): Promise<Bookmark> {
  
  return this.dataSource.transaction(async manager => {
    // 1. 验证故事存在和访问权限
    const story = await manager.findOne(Story, {
      where: { id: storyId }
    });
    
    if (!story) {
      throw new NotFoundException('故事不存在');
    }
    
    const hasAccess = await this.storyPermissionService.validateAccess(
      story,
      { id: userId }
    );
    
    if (!hasAccess.allowed) {
      throw new ForbiddenException('无权限收藏该故事');
    }
    
    // 2. 检查是否已收藏
    const existing = await manager.findOne(Bookmark, {
      where: { userId, storyId }
    });
    
    if (existing) {
      throw new ConflictException('已经收藏过该故事');
    }
    
    // 3. 创建收藏记录
    const bookmark = await manager.save(Bookmark, {
      userId,
      storyId,
      folderName: category || '默认收藏',
      createdAt: new Date()
    });
    
    // 4. 更新用户收藏统计
    await this.updateUserBookmarkStats(userId);
    
    return bookmark;
  });
}
```

### 📋 举报机制设计

#### 内容举报流程设计
```typescript
// 待开发：举报内容
async function reportContent(
  reporterId: string,
  reportDto: CreateReportDto
): Promise<Report> {
  
  return this.dataSource.transaction(async manager => {
    // 1. 验证举报目标存在
    const targetExists = await this.validateReportTarget(
      reportDto.targetType,
      reportDto.targetId
    );
    
    if (!targetExists) {
      throw new NotFoundException('举报目标不存在');
    }
    
    // 2. 检查重复举报（24小时内同一用户同一内容只能举报一次）
    const existingReport = await manager.findOne(Report, {
      where: {
        reporterId,
        targetType: reportDto.targetType,
        targetId: reportDto.targetId,
        createdAt: MoreThan(new Date(Date.now() - 24 * 60 * 60 * 1000))
      }
    });
    
    if (existingReport) {
      throw new ConflictException('您已经举报过该内容');
    }
    
    // 3. 创建举报记录
    const report = await manager.save(Report, {
      reporterId,
      targetType: reportDto.targetType,
      targetId: reportDto.targetId,
      reason: reportDto.reason,
      description: reportDto.description,
      status: 'pending'
    });
    
    // 4. 自动处理逻辑
    await this.handleAutoReportProcessing(report, manager);
    
    // 5. 通知审核团队
    await this.notificationService.notifyModerationTeam(report);
    
    return report;
  });
}
```

---

## 👥 社交关系管理流程详解 ❌ 待开发

### 📋 关注/好友系统设计

#### 关注流程设计
```typescript
// 待开发：关注用户
async function followUser(
  followerId: string,
  followingId: string
): Promise<FollowResult> {
  
  if (followerId === followingId) {
    throw new BadRequestException('不能关注自己');
  }
  
  return this.dataSource.transaction(async manager => {
    // 1. 检查目标用户存在
    const targetUser = await manager.findOne(User, {
      where: { id: followingId },
      select: ['id', 'nickname']
    });
    
    if (!targetUser) {
      throw new NotFoundException('用户不存在');
    }
    
    // 2. 检查是否已关注
    const existingFollow = await manager.findOne(Follow, {
      where: { followerId, followingId }
    });
    
    if (existingFollow) {
      throw new ConflictException('已经关注过该用户');
    }
    
    // 3. 创建关注关系
    const follow = await manager.save(Follow, {
      followerId,
      followingId,
      createdAt: new Date()
    });
    
    // 4. 更新统计数据
    await Promise.all([
      // 更新关注者的关注数
      this.updateUserFollowingCount(followerId, 1),
      
      // 更新被关注者的粉丝数
      this.updateUserFollowerCount(followingId, 1)
    ]);
    
    // 5. 检查是否形成好友关系
    const mutualFollow = await manager.findOne(Follow, {
      where: { followerId: followingId, followingId: followerId }
    });
    
    const becameFriends = !!mutualFollow;
    
    // 6. 发送通知
    await this.notificationService.sendFollowNotification(
      followingId,
      followerId,
      becameFriends
    );
    
    return {
      follow,
      becameFriends
    };
  });
}
```

#### 分组管理流程设计
```typescript
// 待开发：创建用户分组
async function createUserGroup(
  creatorId: string,
  createDto: CreateGroupDto
): Promise<UserGroup> {
  
  return this.dataSource.transaction(async manager => {
    // 1. 检查分组数量限制（最多20个）
    const existingGroups = await manager.count(UserGroup, {
      where: { creatorId }
    });
    
    if (existingGroups >= 20) {
      throw new BadRequestException('最多只能创建20个分组');
    }
    
    // 2. 检查分组名称唯一性
    const existingGroup = await manager.findOne(UserGroup, {
      where: { creatorId, name: createDto.name }
    });
    
    if (existingGroup) {
      throw new ConflictException('分组名称已存在');
    }
    
    // 3. 创建分组
    const group = await manager.save(UserGroup, {
      creatorId,
      name: createDto.name,
      description: createDto.description,
      color: createDto.color || '#1976d2',
      memberCount: 0
    });
    
    // 4. 添加初始成员
    if (createDto.initialMembers?.length > 0) {
      await this.addGroupMembers(
        group.id,
        createDto.initialMembers,
        manager
      );
    }
    
    return group;
  });
}
```

---

## 📊 业务流程监控指标

### 🎯 关键业务指标

#### 人物点亮系统指标
- **申请成功率**: 申请被确认的比例（目标：>60%）
- **申请响应时间**: 从申请到处理的平均时间（目标：<24小时）
- **点亮活跃度**: 用户点亮人物的月活跃率（目标：>20%）
- **情绪保护效果**: 拒绝申请的情绪反馈调查

#### 内容安全指标
- **AI检测准确率**: AI安全检测的准确率（目标：>95%）
- **误杀率**: 正常内容被误判的比例（目标：<2%）
- **人工审核效率**: 人工审核的平均处理时间（目标：<2小时）

#### 技术性能指标
- **API响应时间**: 核心接口响应时间（目标：<200ms）
- **并发处理能力**: 同时处理的点亮确认数量（目标：>1000 TPS）
- **缓存命中率**: Redis缓存命中率（目标：>90%）
- **数据一致性**: 分布式环境下的状态一致性（目标：99.9%）

---

## 📋 总结

本业务流程说明文档详细描述了YGS平台V1.0.0的核心业务逻辑实现，重点包括：

### ✅ 已实现的核心流程
1. **人物点亮系统** - 平台核心创新功能，包含10个关键阶段的完整实现
2. **双权限体系** - 6层故事权限和8项主页控制的精细化验证
3. **企业级认证** - 45+14天Token轮换机制的安全保障
4. **故事生命周期** - 从创作到发布的完整管理流程
5. **AI智能生成** - 配额管理和成本控制的智能化实现

### ❌ 待开发的重要流程
1. **故事引用系统** - 点亮用户的个性化内容管理
2. **内容互动系统** - 评论、收藏、分享、举报的社区互动
3. **社交关系管理** - 关注、好友、分组的社交网络构建

所有待开发流程都已完成详细的设计规划，可直接用于指导后续开发工作。本文档将作为开发团队的权威技术规范，确保实现与产品设计的完全一致。

---

**文档维护**: 产品设计团队  
**最后更新**: 2025-07-22  
**版本**: V1.0.0  
**状态**: 业务流程权威指南