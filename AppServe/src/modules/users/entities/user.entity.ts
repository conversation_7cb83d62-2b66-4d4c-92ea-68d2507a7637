import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  Index,
} from "typeorm";
import { Story } from "../../stories/entities/story.entity";
import { Character } from "../../characters/entities/character.entity";
import { CharacterLighting } from "../../characters/entities/character-lighting.entity";
import { UserRelationship } from "./user-relationship.entity";
import { StoryShare } from "../../stories/entities/story-share.entity";
import { Notification } from "./notification.entity";
import { RefreshToken } from "./refresh-token.entity";

@Entity("users")
@Index(["phone"], { unique: true, where: "phone IS NOT NULL" }) // 稀疏唯一索引
@Index(["email"], { unique: true, where: "email IS NOT NULL" }) // 稀疏唯一索引
@Index(["userNumber"], { unique: true, where: "user_number IS NOT NULL" }) // 6位有故事号索引
@Index(["isActive", "createdAt"]) // 复合索引用于活跃用户查询
@Index(["username"]) // 用户名搜索索引
@Index(["nickname"]) // 昵称搜索索引
export class User {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ unique: true, length: 11, nullable: true, comment: "手机号" })
  phone: string;

  @Column({ unique: true, nullable: true, comment: "邮箱" })
  email: string;

  @Column({ nullable: true, comment: "密码hash" })
  passwordHash: string;

  @Column({
    name: "user_number",
    length: 6,
    unique: true,
    nullable: true,
    comment: "6位有故事号",
  })
  userNumber: string;

  @Column({ length: 50, nullable: false, default: "", comment: "用户昵称" })
  nickname: string;

  @Column({ length: 50, nullable: true, comment: "用户名" })
  username: string;

  @Column({ name: "avatar_url", nullable: true, comment: "头像URL" })
  avatarUrl: string;

  @Column({ name: "birth_date", type: "date", nullable: true, comment: "生日" })
  birthDate: Date;

  @Column({ type: "text", nullable: true, comment: "个人简介" })
  bio: string;

  @Column({ name: "ai_quota_remaining", default: 3, comment: "AI每日剩余配额" })
  aiQuotaRemaining: number;

  @Column({
    name: "ai_quota_reset_date",
    type: "date",
    default: () => "CURRENT_DATE",
    comment: "AI配额重置日期",
  })
  aiQuotaResetDate: Date;

  @Column({
    name: "profile_display_settings",
    type: "jsonb",
    default:
      '{"showBirthday":false,"showBio":true,"showStatistics":true,"showCharacters":true,"showTimeline":true,"showStories":true,"showBookmarks":false,"showFollowList":false,"showReferenceCollections":true}',
    comment: "个人主页展示设置",
  })
  profileDisplaySettings: {
    showBirthday: boolean;
    showBio: boolean;
    showStatistics: boolean;
    showCharacters: boolean;
    showTimeline: boolean;
    showStories: boolean;
    showBookmarks: boolean;
    showFollowList: boolean;
    showReferenceCollections: boolean;
  };

  @Column({
    default: "normal",
    length: 20,
    comment:
      "用户异常状态：normal-正常，warning-警告，restricted-受限，suspended-暂停",
  })
  anomalyStatus: "normal" | "warning" | "restricted" | "suspended";

  @Column({ default: "active", length: 20, comment: "用户状态" })
  status: string;

  @Column({ name: "is_active", default: true, comment: "账户是否激活" })
  isActive: boolean;

  // 验证状态字段
  @Column({
    name: "is_phone_verified",
    default: false,
    comment: "手机号是否已验证",
  })
  isPhoneVerified: boolean;

  @Column({
    name: "phone_verified_at",
    type: "timestamp",
    nullable: true,
    comment: "手机验证时间",
  })
  phoneVerifiedAt: Date | null;

  @Column({
    name: "is_email_verified",
    default: false,
    comment: "邮箱是否已验证",
  })
  isEmailVerified: boolean;

  @Column({
    name: "email_verified_at",
    type: "timestamp",
    nullable: true,
    comment: "邮箱验证时间",
  })
  emailVerifiedAt: Date | null;

  @Column({
    name: "is_identity_verified",
    default: false,
    comment: "身份是否已验证",
  })
  isIdentityVerified: boolean;

  @Column({
    name: "identity_verified_at",
    type: "timestamp",
    nullable: true,
    comment: "身份验证时间",
  })
  identityVerifiedAt: Date | null;

  @Column({
    name: "identity_number",
    type: "varchar",
    nullable: true,
    length: 18,
    comment: "身份证号",
  })
  identityNumber: string | null;

  @Column({
    name: "real_name",
    type: "varchar",
    nullable: true,
    length: 50,
    comment: "真实姓名",
  })
  realName: string | null;

  @Column({ name: "security_level", default: 0, comment: "安全等级" })
  securityLevel: number;

  @Column({ name: "is_vip_user", default: false, comment: "是否VIP用户" })
  isVipUser: boolean;

  @Column({
    name: "last_security_verification",
    type: "timestamp",
    nullable: true,
    comment: "最后安全验证时间",
  })
  lastSecurityVerification: Date | null;

  // 企业级安全字段
  @Column({
    name: "last_login_at",
    type: "timestamp",
    nullable: true,
    comment: "最后登录时间",
  })
  lastLoginAt: Date;

  @Column({
    name: "last_login_ip",
    type: "varchar",
    length: 45,
    nullable: true,
    comment: "最后登录IP",
  })
  lastLoginIp: string | null;

  @Column({
    name: "failed_login_attempts",
    default: 0,
    comment: "连续失败登录次数",
  })
  failedLoginAttempts: number;

  @Column({
    name: "locked_until",
    type: "timestamp",
    nullable: true,
    comment: "账户锁定到期时间",
  })
  lockedUntil: Date | null;

  // 异常行为检测字段
  @Column({
    name: "anomaly_warning_count",
    default: 0,
    comment: "异常警告累计次数",
  })
  anomalyWarningCount: number;

  @Column({
    name: "anomaly_restriction_count",
    default: 0,
    comment: "异常限制累计次数",
  })
  anomalyRestrictionCount: number;

  @Column({
    name: "lighting_restricted_until",
    type: "timestamp",
    nullable: true,
    comment: "点亮申请限制到期时间",
  })
  lightingRestrictedUntil: Date | null;

  @Column({
    name: "last_invalid_lighting_attempt",
    type: "timestamp",
    nullable: true,
    comment: "最后一次无效点亮申请时间",
  })
  lastInvalidLightingAttempt: Date | null;

  @Column({
    name: "daily_lighting_attempts",
    default: 0,
    comment: "今日点亮申请次数",
  })
  dailyLightingAttempts: number;

  @Column({
    name: "lighting_attempt_reset_date",
    type: "date",
    default: () => "CURRENT_DATE",
    comment: "点亮申请计数重置日期",
  })
  lightingAttemptResetDate: Date;

  @CreateDateColumn({ name: "created_at", comment: "创建时间" })
  createdAt: Date;

  @UpdateDateColumn({ name: "updated_at", comment: "更新时间" })
  updatedAt: Date;

  // 关联关系
  @OneToMany(() => Story, (story) => story.user)
  stories: Story[];

  @OneToMany(() => Character, (character) => character.creator)
  characters: Character[];

  @OneToMany(() => CharacterLighting, (lighting) => lighting.lighterUser)
  lightings: CharacterLighting[];

  @OneToMany(() => CharacterLighting, (lighting) => lighting.creatorUser)
  receivedLightings: CharacterLighting[];

  @OneToMany(() => UserRelationship, (relationship) => relationship.user)
  relationships: UserRelationship[];

  @OneToMany(() => UserRelationship, (relationship) => relationship.relatedUser)
  relatedRelationships: UserRelationship[];

  @OneToMany(() => StoryShare, (share) => share.sharer)
  shares: StoryShare[];

  @OneToMany(() => Notification, (notification) => notification.user)
  notifications: Notification[];

  @OneToMany(() => RefreshToken, (token) => token.user)
  refreshTokens: RefreshToken[];

  // 企业级方法
  isLocked(): boolean {
    return this.lockedUntil !== null && this.lockedUntil > new Date();
  }

  shouldLockAccount(): boolean {
    return this.failedLoginAttempts >= 5;
  }

  // 异常状态管理方法
  isLightingRestricted(): boolean {
    return (
      this.lightingRestrictedUntil !== null &&
      this.lightingRestrictedUntil > new Date()
    );
  }

  isSuspended(): boolean {
    return this.anomalyStatus === "suspended";
  }

  canApplyLighting(): boolean {
    return (
      !this.isLightingRestricted() &&
      !this.isSuspended() &&
      this.anomalyStatus !== "restricted"
    );
  }

  shouldResetDailyAttempts(): boolean {
    const today = new Date().toISOString().split("T")[0];
    const resetDate = new Date(this.lightingAttemptResetDate)
      .toISOString()
      .split("T")[0];
    return today !== resetDate;
  }

  shouldTriggerWarning(): boolean {
    // 1小时内无效申请3次或1天内申请20次
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

    if (
      this.lastInvalidLightingAttempt &&
      this.lastInvalidLightingAttempt > oneHourAgo
    ) {
      // 检查1小时内的无效申请
      return true; // 需要在service中检查具体次数
    }

    return this.dailyLightingAttempts >= 20;
  }

  shouldTriggerRestriction(): boolean {
    return this.anomalyWarningCount >= 3;
  }

  shouldTriggerSuspension(): boolean {
    return this.anomalyRestrictionCount >= 2;
  }
}
