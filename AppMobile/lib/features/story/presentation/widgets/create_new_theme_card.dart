import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../design_system/tokens/ygs_colors.dart';
import '../../../../design_system/tokens/ygs_typography.dart';

/// 创建新主题卡片 - 与主题卡片相同尺寸的创建入口
/// 设计风格：简洁现代，突出创建功能，符合YGS设计系统
class CreateNewThemeCard extends StatefulWidget {
  final VoidCallback? onTap;

  const CreateNewThemeCard({
    super.key,
    this.onTap,
  });

  @override
  State<CreateNewThemeCard> createState() => _CreateNewThemeCardState();
}

class _CreateNewThemeCardState extends State<CreateNewThemeCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    setState(() {
      _isPressed = true;
    });
    _animationController.forward();
  }

  void _handleTapUp(TapUpDetails details) {
    setState(() {
      _isPressed = false;
    });
    _animationController.reverse();
  }

  void _handleTapCancel() {
    setState(() {
      _isPressed = false;
    });
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      onTapDown: _handleTapDown,
      onTapUp: _handleTapUp,
      onTapCancel: _handleTapCancel,
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              width: 345.w,
              height: 240.h,
              decoration: BoxDecoration(
                // 基础白色背景
                color: YgsColors.backgroundCard,
                borderRadius: BorderRadius.circular(16.r),
              ),
              clipBehavior: Clip.hardEdge,
              child: Stack(
                children: [
                  // Ellipse 501 - 右上角装饰圆
                  Positioned(
                    top: -40.h,
                    right: -40.w,
                    child: Container(
                      width: 120.w,
                      height: 120.w,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: const Color(0xFFFFE8E8), // 浅粉色
                      ),
                    ),
                  ),

                  // Ellipse 500 - 左下角装饰圆
                  Positioned(
                    bottom: -30.h,
                    left: -30.w,
                    child: Container(
                      width: 100.w,
                      height: 100.w,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: const Color(0xFFF0F8FF), // 浅蓝色
                      ),
                    ),
                  ),

                  // 虚线边框层
                  Positioned.fill(
                    child: CustomPaint(
                      painter: DashedBorderPainter(
                        color: YgsColors.borderPrimary,
                        strokeWidth: 2.w,
                        dashLength: 8.w,
                        dashSpace: 6.w,
                        borderRadius: 16.r,
                      ),
                    ),
                  ),

                  // 主要内容
                  Positioned.fill(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // 加号图标 - 与主题卡片图标大小一致
                  Container(
                    width: 64.w, // 与主题卡片玻璃图标大小一致
                    height: 64.w,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.9),
                      borderRadius: BorderRadius.circular(16.r),
                      border: Border.all(
                        color: const Color(0xFF000000).withValues(alpha: 0.1),
                        width: 1.w,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 8.r,
                          offset: Offset(0, 2.h),
                        ),
                      ],
                    ),
                    child: Icon(
                      Icons.add,
                      size: 32.w, // 增大图标尺寸
                      color: const Color(0xFF151523),
                      weight: 2.0,
                    ),
                  ),
                  
                  SizedBox(height: 24.h),
                  
                  // 主标题 - 与主题卡片字体大小一致
                  Text(
                    '创建新主题',
                    style: TextStyle(
                      fontFamily: 'PingFang SC',
                      fontSize: 30.sp, // 与主题卡片标题大小一致
                      fontWeight: FontWeight.w700, // 与主题卡片字重一致
                      color: const Color(0xFF151523),
                      height: 1.0,
                      letterSpacing: -0.3.w,
                      shadows: [
                        Shadow(
                          offset: Offset(0, 1.h),
                          blurRadius: 2.r,
                          color: Colors.white.withValues(alpha: 0.8),
                        ),
                      ],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

