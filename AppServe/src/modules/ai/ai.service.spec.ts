import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import type { Repository } from "typeorm";
import { ConfigService } from "@nestjs/config";
import { BadRequestException, ForbiddenException } from "@nestjs/common";
import { AiService } from "./ai.service";
import { AiUsageLog } from "./entities/ai-usage-log.entity";
import { User } from "../users/entities/user.entity";
import { EnhancedRedisService } from "../../common/services/enhanced-redis.service";
import { MockAIProvider } from "./providers/mock-ai.provider";

describe("AiService", () => {
  let service: AiService;
  let aiUsageLogRepository: Repository<AiUsageLog>;
  let redisService: EnhancedRedisService;
  let mockAIProvider: MockAIProvider;

  const mockUsageLog = {
    id: "log-123",
    userId: "user-123",
    featureType: "title_generation",
    promptTokens: 10,
    completionTokens: 20,
    totalTokens: 30,
    costEstimate: 0.0003,
    success: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    // 抑制日志输出
    jest.spyOn(console, "log").mockImplementation(() => {});
    jest.spyOn(console, "error").mockImplementation(() => {});
    jest.spyOn(console, "warn").mockImplementation(() => {});

    const mockAiUsageLogRepositoryMethods = {
      create: jest.fn(),
      save: jest.fn(),
      find: jest.fn(),
      findOne: jest.fn(),
    };

    const mockUserRepositoryMethods = {
      findOne: jest.fn(),
    };

    const mockRedisServiceMethods = {
      incr: jest.fn(),
      decr: jest.fn(),
      get: jest.fn(),
      del: jest.fn(),
      expireAt: jest.fn(),
    };

    const mockConfigServiceMethods = {
      get: jest.fn(),
    };

    const mockAIProviderServiceMethods = {
      name: "Mock AI Provider",
      version: "1.0.0",
      generateTitle: jest.fn(),
      generateStoryContent: jest.fn(),
      isAvailable: jest.fn(),
      healthCheck: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AiService,
        {
          provide: getRepositoryToken(AiUsageLog),
          useValue: mockAiUsageLogRepositoryMethods,
        },
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepositoryMethods,
        },
        {
          provide: EnhancedRedisService,
          useValue: mockRedisServiceMethods,
        },
        {
          provide: ConfigService,
          useValue: mockConfigServiceMethods,
        },
        {
          provide: MockAIProvider,
          useValue: mockAIProviderServiceMethods,
        },
      ],
    })
      .setLogger({
        log: jest.fn(),
        error: jest.fn(),
        warn: jest.fn(),
        debug: jest.fn(),
        verbose: jest.fn(),
      })
      .compile();

    service = module.get<AiService>(AiService);
    aiUsageLogRepository = module.get<Repository<AiUsageLog>>(
      getRepositoryToken(AiUsageLog),
    );
    redisService = module.get<EnhancedRedisService>(EnhancedRedisService);
    mockAIProvider = module.get<MockAIProvider>(MockAIProvider);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe("constructor", () => {
    it("should be defined", () => {
      expect(service).toBeDefined();
    });

    it("should initialize with correct DAILY_QUOTA_LIMIT", () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      expect((service as any).DAILY_QUOTA_LIMIT).toBe(3);
    });

    it("should initialize with MOCK_AI_ENABLED true", () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      expect((service as any).MOCK_AI_ENABLED).toBe(true);
    });

    it("should initialize logger correctly", () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      expect((service as any).logger).toBeDefined();
    });
  });

  describe("generateStoryTitle", () => {
    const mockKeywords = ["科幻", "未来", "机器人"];
    const mockTitle = "未来世界的机器人传奇";

    it("should generate story title successfully", async () => {
      // Mock配额检查通过
      (redisService.incr as jest.Mock).mockResolvedValue(1);
      (redisService.expireAt as jest.Mock).mockResolvedValue(true);

      // Mock AI生成
      (mockAIProvider.generateTitle as jest.Mock).mockResolvedValue(mockTitle);

      // Mock日志记录
      (aiUsageLogRepository.create as jest.Mock).mockReturnValue(mockUsageLog);
      (aiUsageLogRepository.save as jest.Mock).mockResolvedValue(mockUsageLog);

      const result = await service.generateStoryTitle("user-123", mockKeywords);

      expect(result).toBe(mockTitle);
      expect(mockAIProvider.generateTitle).toHaveBeenCalledWith(mockKeywords);
      expect(aiUsageLogRepository.save).toHaveBeenCalledWith(mockUsageLog);
    });

    it("should throw ForbiddenException when quota exceeded", async () => {
      // Mock配额已用完
      (redisService.incr as jest.Mock).mockResolvedValue(4);
      (redisService.decr as jest.Mock).mockResolvedValue(3);

      await expect(
        service.generateStoryTitle("user-123", mockKeywords),
      ).rejects.toThrow(ForbiddenException);
      await expect(
        service.generateStoryTitle("user-123", mockKeywords),
      ).rejects.toThrow("今日AI生成配额已用完，请明日再试");

      expect(redisService.decr).toHaveBeenCalled();
    });

    it("should throw BadRequestException for empty keywords", async () => {
      await expect(service.generateStoryTitle("user-123", [])).rejects.toThrow(
        BadRequestException,
      );
      await expect(service.generateStoryTitle("user-123", [])).rejects.toThrow(
        "关键词不能为空",
      );
    });

    it("should throw BadRequestException for too many keywords", async () => {
      const tooManyKeywords = ["1", "2", "3", "4", "5", "6"];

      await expect(
        service.generateStoryTitle("user-123", tooManyKeywords),
      ).rejects.toThrow(BadRequestException);
      await expect(
        service.generateStoryTitle("user-123", tooManyKeywords),
      ).rejects.toThrow("关键词不能超过5个");
    });

    it("should throw BadRequestException for invalid keyword", async () => {
      const invalidKeywords = ["valid", ""];

      await expect(
        service.generateStoryTitle("user-123", invalidKeywords),
      ).rejects.toThrow(BadRequestException);
      await expect(
        service.generateStoryTitle("user-123", invalidKeywords),
      ).rejects.toThrow("关键词不能为空");
    });

    it("should throw BadRequestException for keyword too long", async () => {
      const longKeywords = ["a".repeat(11)];

      await expect(
        service.generateStoryTitle("user-123", longKeywords),
      ).rejects.toThrow(BadRequestException);
      await expect(
        service.generateStoryTitle("user-123", longKeywords),
      ).rejects.toThrow("单个关键词不能超过10个字符");
    });

    it("should refund quota and log failure when AI generation fails", async () => {
      // Mock配额检查通过
      (redisService.incr as jest.Mock).mockResolvedValue(1);
      (redisService.expireAt as jest.Mock).mockResolvedValue(true);

      // Mock AI生成失败
      (mockAIProvider.generateTitle as jest.Mock).mockRejectedValue(
        new Error("AI service error"),
      );

      // Mock配额退还
      (redisService.decr as jest.Mock).mockResolvedValue(0);

      // Mock日志记录
      (aiUsageLogRepository.create as jest.Mock).mockReturnValue({
        ...mockUsageLog,
        success: false,
      });
      (aiUsageLogRepository.save as jest.Mock).mockResolvedValue({
        ...mockUsageLog,
        success: false,
      });

      await expect(
        service.generateStoryTitle("user-123", mockKeywords),
      ).rejects.toThrow(BadRequestException);
      await expect(
        service.generateStoryTitle("user-123", mockKeywords),
      ).rejects.toThrow("标题生成失败，请稍后重试");

      expect(redisService.decr).toHaveBeenCalled();
      expect(aiUsageLogRepository.save).toHaveBeenCalledWith({
        ...mockUsageLog,
        success: false,
      });
    });
  });

  describe("generateStoryContent", () => {
    const mockPrompt = "写一个关于未来机器人的故事";
    const mockContent = "在遥远的未来，有一个机器人...";

    it("should generate story content successfully", async () => {
      // Mock配额检查通过
      (redisService.incr as jest.Mock).mockResolvedValue(1);
      (redisService.expireAt as jest.Mock).mockResolvedValue(true);

      // Mock AI生成
      (mockAIProvider.generateStoryContent as jest.Mock).mockResolvedValue(
        mockContent,
      );

      // Mock日志记录
      (aiUsageLogRepository.create as jest.Mock).mockReturnValue({
        ...mockUsageLog,
        featureType: "content_generation",
      });
      (aiUsageLogRepository.save as jest.Mock).mockResolvedValue({
        ...mockUsageLog,
        featureType: "content_generation",
      });

      const result = await service.generateStoryContent("user-123", mockPrompt);

      expect(result).toBe(mockContent);
      expect(mockAIProvider.generateStoryContent).toHaveBeenCalledWith(
        mockPrompt,
      );
      expect(aiUsageLogRepository.save).toHaveBeenCalled();
    });

    it("should throw BadRequestException for empty prompt", async () => {
      await expect(
        service.generateStoryContent("user-123", ""),
      ).rejects.toThrow(BadRequestException);
      await expect(
        service.generateStoryContent("user-123", ""),
      ).rejects.toThrow("生成提示不能为空");
    });

    it("should throw BadRequestException for prompt too long", async () => {
      const longPrompt = "a".repeat(201);

      await expect(
        service.generateStoryContent("user-123", longPrompt),
      ).rejects.toThrow(BadRequestException);
      await expect(
        service.generateStoryContent("user-123", longPrompt),
      ).rejects.toThrow("生成提示不能超过200个字符");
    });

    it("should throw BadRequestException for sensitive content", async () => {
      const sensitivePrompt = "写一个关于政治的故事";

      await expect(
        service.generateStoryContent("user-123", sensitivePrompt),
      ).rejects.toThrow(BadRequestException);
      await expect(
        service.generateStoryContent("user-123", sensitivePrompt),
      ).rejects.toThrow("提示内容包含敏感词，请修改后重试");
    });
  });

  describe("checkAndConsumeQuota", () => {
    it("should consume quota successfully for first usage", async () => {
      (redisService.incr as jest.Mock).mockResolvedValue(1);
      (redisService.expireAt as jest.Mock).mockResolvedValue(true);

      const result = await service.checkAndConsumeQuota("user-123");

      expect(result).toBe(true);
      expect(redisService.incr).toHaveBeenCalled();
      expect(redisService.expireAt).toHaveBeenCalled();
    });

    it("should consume quota successfully for existing usage", async () => {
      (redisService.incr as jest.Mock).mockResolvedValue(2);

      const result = await service.checkAndConsumeQuota("user-123");

      expect(result).toBe(true);
      expect(redisService.incr).toHaveBeenCalled();
      expect(redisService.expireAt).not.toHaveBeenCalled();
    });

    it("should return false when quota exceeded", async () => {
      (redisService.incr as jest.Mock).mockResolvedValue(4);
      (redisService.decr as jest.Mock).mockResolvedValue(3);

      const result = await service.checkAndConsumeQuota("user-123");

      expect(result).toBe(false);
      expect(redisService.decr).toHaveBeenCalled();
    });

    it("should fallback to allow usage when Redis fails", async () => {
      (redisService.incr as jest.Mock).mockRejectedValue(
        new Error("Redis error"),
      );

      const result = await service.checkAndConsumeQuota("user-123");

      expect(result).toBe(true);
    });

    it("should set correct expiration time for first usage", async () => {
      (redisService.incr as jest.Mock).mockResolvedValue(1);
      (redisService.expireAt as jest.Mock).mockResolvedValue(true);

      const result = await service.checkAndConsumeQuota("user-123");

      expect(result).toBe(true);
      expect(redisService.expireAt).toHaveBeenCalledWith(
        expect.stringContaining("ai_quota:user-123:"),
        expect.any(Number),
      );
    });

    it("should handle quota at exact limit", async () => {
      (redisService.incr as jest.Mock).mockResolvedValue(3);

      const result = await service.checkAndConsumeQuota("user-123");

      expect(result).toBe(true);
      expect(redisService.decr).not.toHaveBeenCalled();
    });

    it("should handle redis network timeout", async () => {
      const timeoutError = new Error("Connection timeout");
      timeoutError.name = "TimeoutError";
      (redisService.incr as jest.Mock).mockRejectedValue(timeoutError);

      const result = await service.checkAndConsumeQuota("user-123");

      expect(result).toBe(true);
    });

    it("should generate correct quota key format", async () => {
      (redisService.incr as jest.Mock).mockResolvedValue(1);
      (redisService.expireAt as jest.Mock).mockResolvedValue(true);

      await service.checkAndConsumeQuota("user-123");

      const today = new Date().toISOString().split("T")[0];
      expect(redisService.incr).toHaveBeenCalledWith(
        `ai_quota:user-123:${today}`,
      );
    });
  });

  describe("getRemainingQuota", () => {
    it("should return remaining quota correctly", async () => {
      (redisService.get as jest.Mock).mockResolvedValue("2");

      const result = await service.getRemainingQuota("user-123");

      expect(result).toBe(1); // 3 - 2 = 1
    });

    it("should return full quota when no usage", async () => {
      (redisService.get as jest.Mock).mockResolvedValue(null);

      const result = await service.getRemainingQuota("user-123");

      expect(result).toBe(3); // DAILY_QUOTA_LIMIT
    });

    it("should return 0 when quota exceeded", async () => {
      (redisService.get as jest.Mock).mockResolvedValue("5");

      const result = await service.getRemainingQuota("user-123");

      expect(result).toBe(0); // Math.max(0, 3 - 5)
    });

    it("should fallback to full quota when Redis fails", async () => {
      (redisService.get as jest.Mock).mockRejectedValue(
        new Error("Redis error"),
      );

      const result = await service.getRemainingQuota("user-123");

      expect(result).toBe(3); // DAILY_QUOTA_LIMIT
    });
  });

  describe("resetDailyQuota", () => {
    it("should reset quota successfully", async () => {
      (redisService.del as jest.Mock).mockResolvedValue(1);

      await service.resetDailyQuota("user-123");

      expect(redisService.del).toHaveBeenCalled();
    });

    it("should throw BadRequestException when reset fails", async () => {
      (redisService.del as jest.Mock).mockRejectedValue(
        new Error("Redis error"),
      );

      await expect(service.resetDailyQuota("user-123")).rejects.toThrow(
        BadRequestException,
      );
      await expect(service.resetDailyQuota("user-123")).rejects.toThrow(
        "配额重置失败",
      );
    });
  });

  describe("getUserUsageStatistics", () => {
    const mockLogs = [
      {
        ...mockUsageLog,
        featureType: "title_generation",
        success: true,
        costEstimate: 0.01,
      },
      {
        ...mockUsageLog,
        featureType: "content_generation",
        success: true,
        costEstimate: 0.02,
      },
      {
        ...mockUsageLog,
        featureType: "title_generation",
        success: false,
        costEstimate: 0.005,
      },
    ];

    it("should return usage statistics correctly", async () => {
      (aiUsageLogRepository.find as jest.Mock).mockResolvedValue(mockLogs);

      const result = await service.getUserUsageStatistics("user-123", 30);

      expect(result).toEqual({
        totalUsage: 3,
        successfulUsage: 2,
        totalCost: expect.closeTo(0.035, 5),
        averageCostPerRequest: expect.closeTo(0.035 / 3, 5),
        usageByFeature: {
          title_generation: 2,
          content_generation: 1,
        },
      });
    });

    it("should handle empty logs", async () => {
      (aiUsageLogRepository.find as jest.Mock).mockResolvedValue([]);

      const result = await service.getUserUsageStatistics("user-123", 30);

      expect(result).toEqual({
        totalUsage: 0,
        successfulUsage: 0,
        totalCost: 0,
        averageCostPerRequest: 0,
        usageByFeature: {},
      });
    });

    it("should throw BadRequestException when query fails", async () => {
      (aiUsageLogRepository.find as jest.Mock).mockRejectedValue(
        new Error("Database error"),
      );

      await expect(
        service.getUserUsageStatistics("user-123", 30),
      ).rejects.toThrow(BadRequestException);
      await expect(
        service.getUserUsageStatistics("user-123", 30),
      ).rejects.toThrow("获取使用统计失败");
    });
  });

  describe("getProviderInfo", () => {
    it("should return provider info correctly", async () => {
      const mockHealthStatus = { status: "healthy", uptime: 12345 };
      (mockAIProvider.healthCheck as jest.Mock).mockResolvedValue(
        mockHealthStatus,
      );
      (mockAIProvider.isAvailable as jest.Mock).mockResolvedValue(true);

      const result = await service.getProviderInfo();

      expect(result).toEqual({
        name: "Mock AI Provider",
        version: "1.0.0",
        isAvailable: true,
        healthStatus: mockHealthStatus,
      });
    });

    it("should handle unavailable provider", async () => {
      const mockHealthStatus = { status: "unhealthy", error: "Service down" };
      (mockAIProvider.healthCheck as jest.Mock).mockResolvedValue(
        mockHealthStatus,
      );
      (mockAIProvider.isAvailable as jest.Mock).mockResolvedValue(false);

      const result = await service.getProviderInfo();

      expect(result).toEqual({
        name: "Mock AI Provider",
        version: "1.0.0",
        isAvailable: false,
        healthStatus: mockHealthStatus,
      });
    });
  });

  describe("switchProvider", () => {
    it("should throw BadRequestException for unimplemented feature", async () => {
      await expect(service.switchProvider("openai")).rejects.toThrow(
        BadRequestException,
      );
      await expect(service.switchProvider("openai")).rejects.toThrow(
        "AI提供商切换功能暂未实现",
      );
    });
  });

  describe("logAiUsage", () => {
    it("should log AI usage successfully", async () => {
      const mockLog = {
        ...mockUsageLog,
        featureType: "title_generation",
        success: true,
      };

      (aiUsageLogRepository.create as jest.Mock).mockReturnValue(mockLog);
      (aiUsageLogRepository.save as jest.Mock).mockResolvedValue(mockLog);

      await service.logAiUsage("user-123", "title_generation", {
        promptTokens: 10,
        completionTokens: 20,
        success: true,
      });

      expect(aiUsageLogRepository.create).toHaveBeenCalledWith({
        userId: "user-123",
        featureType: "title_generation",
        promptTokens: 10,
        completionTokens: 20,
        totalTokens: 30,
        costEstimate: expect.any(Number),
        success: true,
      });
      expect(aiUsageLogRepository.save).toHaveBeenCalledWith(mockLog);
    });

    it("should handle logging failure gracefully", async () => {
      (aiUsageLogRepository.create as jest.Mock).mockImplementation(() => {
        throw new Error("Database error");
      });

      // Should not throw error
      await service.logAiUsage("user-123", "title_generation", {
        promptTokens: 10,
        completionTokens: 20,
        success: true,
      });
    });

    it("should log content_generation feature type correctly", async () => {
      const mockLog = {
        ...mockUsageLog,
        featureType: "content_generation",
        success: true,
      };

      (aiUsageLogRepository.create as jest.Mock).mockReturnValue(mockLog);
      (aiUsageLogRepository.save as jest.Mock).mockResolvedValue(mockLog);

      await service.logAiUsage("user-123", "content_generation", {
        promptTokens: 50,
        completionTokens: 100,
        success: true,
      });

      expect(aiUsageLogRepository.create).toHaveBeenCalledWith({
        userId: "user-123",
        featureType: "content_generation",
        promptTokens: 50,
        completionTokens: 100,
        totalTokens: 150,
        costEstimate: expect.any(Number),
        success: true,
      });
    });

    it("should log content_optimization feature type correctly", async () => {
      const mockLog = {
        ...mockUsageLog,
        featureType: "content_optimization",
        success: false,
      };

      (aiUsageLogRepository.create as jest.Mock).mockReturnValue(mockLog);
      (aiUsageLogRepository.save as jest.Mock).mockResolvedValue(mockLog);

      await service.logAiUsage("user-123", "content_optimization", {
        promptTokens: 30,
        completionTokens: 0,
        success: false,
      });

      expect(aiUsageLogRepository.create).toHaveBeenCalledWith({
        userId: "user-123",
        featureType: "content_optimization",
        promptTokens: 30,
        completionTokens: 0,
        totalTokens: 30,
        costEstimate: expect.any(Number),
        success: false,
      });
    });

    it("should handle save failure gracefully", async () => {
      const mockLog = { ...mockUsageLog };
      (aiUsageLogRepository.create as jest.Mock).mockReturnValue(mockLog);
      (aiUsageLogRepository.save as jest.Mock).mockRejectedValue(
        new Error("Save failed"),
      );

      // Should not throw error
      await service.logAiUsage("user-123", "title_generation", {
        promptTokens: 10,
        completionTokens: 20,
        success: true,
      });

      expect(aiUsageLogRepository.create).toHaveBeenCalled();
      expect(aiUsageLogRepository.save).toHaveBeenCalled();
    });
  });

  describe("refundQuota", () => {
    it("should refund quota successfully", async () => {
      (redisService.decr as jest.Mock).mockResolvedValue(2);

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      await (service as any).refundQuota("user-123");

      const today = new Date().toISOString().split("T")[0];
      expect(redisService.decr).toHaveBeenCalledWith(
        `ai_quota:user-123:${today}`,
      );
    });

    it("should handle refund failure gracefully", async () => {
      (redisService.decr as jest.Mock).mockRejectedValue(
        new Error("Redis error"),
      );

      // Should not throw error
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      await (service as any).refundQuota("user-123");

      expect(redisService.decr).toHaveBeenCalled();
    });
  });

  describe("private methods", () => {
    describe("validateKeywords", () => {
      it("should validate keywords correctly", () => {
        const validKeywords = ["科幻", "未来", "机器人"];

        expect(() => {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          (service as any).validateKeywords(validKeywords);
        }).not.toThrow();
      });

      it("should throw error for empty keywords array", () => {
        expect(() => {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          (service as any).validateKeywords([]);
        }).toThrow("关键词不能为空");
      });

      it("should throw error for too many keywords", () => {
        const tooManyKeywords = ["1", "2", "3", "4", "5", "6"];

        expect(() => {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          (service as any).validateKeywords(tooManyKeywords);
        }).toThrow("关键词不能超过5个");
      });
    });

    describe("validatePrompt", () => {
      it("should validate prompt correctly", () => {
        const validPrompt = "写一个关于未来的故事";

        expect(() => {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          (service as any).validatePrompt(validPrompt);
        }).not.toThrow();
      });

      it("should throw error for empty prompt", () => {
        expect(() => {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          (service as any).validatePrompt("");
        }).toThrow("生成提示不能为空");
      });

      it("should throw error for prompt too long", () => {
        const longPrompt = "a".repeat(201);

        expect(() => {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          (service as any).validatePrompt(longPrompt);
        }).toThrow("生成提示不能超过200个字符");
      });

      it("should throw error for sensitive content", () => {
        expect(() => {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          (service as any).validatePrompt("关于政治的内容");
        }).toThrow("提示内容包含敏感词，请修改后重试");
      });
    });

    describe("estimateTokens", () => {
      it("should estimate tokens correctly", () => {
        const chineseText = "这是一个中文测试";
        const englishText = "This is an English test";
        const mixedText = "这是中英文混合 English mixed text";

        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const chineseTokens = (service as any).estimateTokens(chineseText);
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const englishTokens = (service as any).estimateTokens(englishText);
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const mixedTokens = (service as any).estimateTokens(mixedText);

        expect(chineseTokens).toBeGreaterThan(0);
        expect(englishTokens).toBeGreaterThan(0);
        expect(mixedTokens).toBeGreaterThan(0);
      });
    });

    describe("calculateCostEstimate", () => {
      it("should calculate cost correctly", () => {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const cost = (service as any).calculateCostEstimate(1000);

        expect(cost).toBe(0.01); // 1000 tokens / 1000 * 0.01 = 0.01
      });

      it("should handle small token counts", () => {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const cost = (service as any).calculateCostEstimate(100);

        expect(cost).toBe(0.001); // 100 tokens / 1000 * 0.01 = 0.001
      });

      it("should handle zero tokens", () => {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const cost = (service as any).calculateCostEstimate(0);

        expect(cost).toBe(0);
      });

      it("should handle large token counts", () => {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const cost = (service as any).calculateCostEstimate(50000);

        expect(cost).toBe(0.5); // 50000 tokens / 1000 * 0.01 = 0.5
      });
    });
  });

  describe("Edge Cases and Error Handling", () => {
    it("should handle generateStoryTitle with Redis timeout during quota check", async () => {
      const mockKeywords = ["测试"];
      (redisService.incr as jest.Mock).mockImplementation(() => {
        return new Promise((_, reject) => {
          setTimeout(() => reject(new Error("Timeout")), 50);
        });
      });
      (mockAIProvider.generateTitle as jest.Mock).mockResolvedValue("测试标题");

      const result = await service.generateStoryTitle("user-123", mockKeywords);

      expect(result).toBe("测试标题");
      expect(mockAIProvider.generateTitle).toHaveBeenCalled();
    });

    it("should handle generateStoryContent with AI provider error", async () => {
      const mockPrompt = "写一个故事";
      (redisService.incr as jest.Mock).mockResolvedValue(1);
      (redisService.decr as jest.Mock).mockResolvedValue(0);
      (mockAIProvider.generateStoryContent as jest.Mock).mockRejectedValue(
        new Error("AI provider unavailable"),
      );

      await expect(
        service.generateStoryContent("user-123", mockPrompt),
      ).rejects.toThrow(BadRequestException);

      expect(redisService.decr).toHaveBeenCalled();
    });

    it("should handle concurrent quota requests", async () => {
      (redisService.incr as jest.Mock)
        .mockResolvedValueOnce(1)
        .mockResolvedValueOnce(2)
        .mockResolvedValueOnce(3);

      const promises = [
        service.checkAndConsumeQuota("user-123"),
        service.checkAndConsumeQuota("user-123"),
        service.checkAndConsumeQuota("user-123"),
      ];

      const results = await Promise.all(promises);

      expect(results).toEqual([true, true, true]);
      expect(redisService.incr).toHaveBeenCalledTimes(3);
    });

    it("should handle getUserUsageStatistics with mixed success/failure logs", async () => {
      const mixedLogs = [
        { ...mockUsageLog, success: true, costEstimate: 0.01 },
        { ...mockUsageLog, success: false, costEstimate: 0.005 },
        { ...mockUsageLog, success: true, costEstimate: 0.02 },
      ];

      (aiUsageLogRepository.find as jest.Mock).mockResolvedValue(mixedLogs);

      const result = await service.getUserUsageStatistics("user-123", 7);

      expect(result.totalUsage).toBe(3);
      expect(result.successfulUsage).toBe(2);
      expect(result.totalCost).toBeCloseTo(0.035);
    });

    it("should handle estimateTokens with empty string", () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const tokens = (service as any).estimateTokens("");

      expect(tokens).toBe(0);
    });

    it("should handle estimateTokens with only spaces", () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const tokens = (service as any).estimateTokens("   ");

      expect(tokens).toBe(0);
    });

    it("should handle estimateTokens with special characters", () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const tokens = (service as any).estimateTokens("!@#$%^&*()");

      expect(tokens).toBeGreaterThanOrEqual(0);
    });

    it("should handle validateKeywords with whitespace-only keywords", () => {
      expect(() => {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (service as any).validateKeywords(["valid", "   ", "another"]);
      }).toThrow("关键词不能为空");
    });

    it("should handle validatePrompt with additional sensitive words", () => {
      const sensitiveWords = ["暴力", "血腥", "恐怖"];

      sensitiveWords.forEach((word) => {
        expect(() => {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          (service as any).validatePrompt(`这是包含${word}的内容`);
        }).toThrow("提示内容包含敏感词，请修改后重试");
      });
    });

    it("should handle getRemainingQuota with invalid Redis value", async () => {
      (redisService.get as jest.Mock).mockResolvedValue("invalid");

      const result = await service.getRemainingQuota("user-123");

      expect(result).toBe(3); // Should return full quota when parsing fails (usedCount becomes 0)
    });

    it("should handle resetDailyQuota with specific error message", async () => {
      const customError = new Error("Connection lost");
      (redisService.del as jest.Mock).mockRejectedValue(customError);

      await expect(service.resetDailyQuota("user-123")).rejects.toThrow(
        BadRequestException,
      );
      await expect(service.resetDailyQuota("user-123")).rejects.toThrow(
        "配额重置失败",
      );
    });
  });
});
