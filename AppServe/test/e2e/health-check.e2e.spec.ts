/**
 * 健康检查和基础API端点E2E测试
 *
 * 这个测试文件专注于验证应用的基础健康状态和核心API端点
 * 不依赖复杂的数据库设置，适合快速验证应用启动和基本功能
 */

import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import type { INestApplication } from "@nestjs/common";
import * as request from "supertest";
import { AppModule } from "../../src/app.module";

describe("Health Check and Basic API Endpoints (E2E)", () => {
  let app: INestApplication;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let server: any;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();

    // 使用随机端口避免冲突
    const port = Math.floor(Math.random() * 1000) + 3000;
    await app.listen(port);
    server = app.getHttpServer();
  }, 30000);

  afterAll(async () => {
    if (app) {
      await app.close();
    }
  });

  describe("🔍 应用健康检查", () => {
    it("should return health status", async () => {
      const response = await request(server).get("/health").expect(200);

      expect(response.body).toHaveProperty("status");
      expect(response.body.status).toBe("ok");
      expect(response.body).toHaveProperty("timestamp");
      expect(response.body).toHaveProperty("uptime");
    });

    it("should return application info", async () => {
      const response = await request(server).get("/health/info").expect(200);

      expect(response.body).toHaveProperty("name");
      expect(response.body).toHaveProperty("version");
      expect(response.body).toHaveProperty("environment");
    });

    it("should return ready status", async () => {
      const response = await request(server).get("/health/ready").expect(200);

      expect(response.body).toHaveProperty("status");
      expect(response.body.status).toBe("ready");
    });
  });

  describe("🔒 认证端点基础验证", () => {
    it("should reject unauthenticated requests to protected endpoints", async () => {
      await request(server).get("/users/profile").expect(401);
    });

    it("should return validation error for invalid login request", async () => {
      const response = await request(server)
        .post("/auth/login")
        .send({
          // 空的登录请求应该返回验证错误
        })
        .expect(400);

      expect(response.body).toHaveProperty("message");
    });

    it("should return validation error for invalid registration request", async () => {
      const response = await request(server)
        .post("/auth/register")
        .send({
          // 无效的注册数据
          phone: "invalid",
          password: "123",
        })
        .expect(400);

      expect(response.body).toHaveProperty("message");
    });
  });

  describe("📖 故事API端点基础验证", () => {
    it("should return public stories list", async () => {
      const response = await request(server)
        .get("/stories")
        .query({
          status: "published",
          visibility: "public",
          limit: 5,
        })
        .expect(200);

      expect(response.body).toHaveProperty("success");
      expect(response.body).toHaveProperty("data");
      expect(response.body.data).toHaveProperty("stories");
      expect(Array.isArray(response.body.data.stories)).toBe(true);
    });

    it("should require authentication for story creation", async () => {
      await request(server)
        .post("/stories")
        .send({
          title: "测试故事",
          content: "测试内容",
        })
        .expect(401);
    });

    it("should return 404 for non-existent story", async () => {
      await request(server).get("/stories/non-existent-id").expect(404);
    });
  });

  describe("👤 用户API端点基础验证", () => {
    it("should require authentication for user profile", async () => {
      await request(server).get("/users/profile").expect(401);
    });

    it("should require authentication for user settings", async () => {
      await request(server).get("/users/settings").expect(401);
    });

    it("should return validation error for invalid user search", async () => {
      const response = await request(server)
        .get("/users/search")
        .query({ q: "" }) // 空搜索查询
        .expect(400);

      expect(response.body).toHaveProperty("message");
    });
  });

  describe("🤖 AI服务端点基础验证", () => {
    it("should require authentication for AI services", async () => {
      await request(server)
        .post("/ai/generate-title")
        .send({
          keywords: ["测试"],
        })
        .expect(401);
    });

    it("should require authentication for AI quota check", async () => {
      await request(server).get("/ai/quota").expect(401);
    });
  });

  describe("💡 人物点亮端点基础验证", () => {
    it("should require authentication for lighting requests", async () => {
      await request(server)
        .post("/lighting/request")
        .send({
          characterId: "test-id",
          targetPhone: "13800138000",
        })
        .expect(401);
    });

    it("should require authentication for pending requests", async () => {
      await request(server).get("/lighting/pending-requests").expect(401);
    });
  });

  describe("📊 性能和稳定性验证", () => {
    it("should respond to health check within acceptable time", async () => {
      const startTime = Date.now();

      await request(server).get("/health").expect(200);

      const responseTime = Date.now() - startTime;
      expect(responseTime).toBeLessThan(1000); // 1秒内响应
    });

    it("should handle concurrent requests without errors", async () => {
      const requests = Array.from({ length: 10 }, () =>
        request(server).get("/health").expect(200),
      );

      const responses = await Promise.all(requests);

      responses.forEach((response) => {
        expect(response.body.status).toBe("ok");
      });
    });

    it("should handle malformed requests gracefully", async () => {
      // 发送包含无效JSON的请求
      const response = await request(server)
        .post("/auth/login")
        .set("Content-Type", "application/json")
        .send('{"invalid": json}')
        .expect(400);

      expect(response.body).toHaveProperty("message");
    });
  });

  describe("🔧 API版本和兼容性验证", () => {
    it("should return API version information", async () => {
      const response = await request(server).get("/health/version").expect(200);

      expect(response.body).toHaveProperty("apiVersion");
      expect(response.body).toHaveProperty("compatibilityVersion");
    });

    it("should handle CORS preflight requests", async () => {
      await request(server).options("/health").expect(200);
    });

    it("should set proper security headers", async () => {
      const response = await request(server).get("/health").expect(200);

      // 验证安全头是否存在
      expect(response.headers).toHaveProperty("x-content-type-options");
      expect(response.headers).toHaveProperty("x-frame-options");
    });
  });

  describe("🚨 错误处理和边界情况", () => {
    it("should handle very large request bodies", async () => {
      const largeData = "x".repeat(10000); // 10KB数据

      const response = await request(server).post("/auth/register").send({
        phone: "13800138000",
        password: largeData,
      });

      // 应该返回验证错误而不是服务器错误
      expect([400, 413]).toContain(response.status);
    });

    it("should handle special characters in URLs", async () => {
      const response = await request(server)
        .get("/stories/%E6%B5%8B%E8%AF%95") // URL编码的中文
        .expect(404);

      expect(response.body).toHaveProperty("message");
    });

    it("should rate limit excessive requests", async () => {
      // 快速发送多个请求测试速率限制
      const requests = Array.from({ length: 50 }, () =>
        request(server).get("/health"),
      );

      const responses = await Promise.allSettled(requests);

      // 验证大部分请求成功，但可能有一些被限流
      const successCount = responses.filter(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (r) => r.status === "fulfilled" && (r.value as any).status === 200,
      ).length;

      expect(successCount).toBeGreaterThan(30); // 至少60%的请求成功
    });
  });
});
