import { Module, forwardRef } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { AiService } from "./ai.service";
import { AiController } from "./ai.controller";
import { AiUsageLog } from "./entities/ai-usage-log.entity";
import { User } from "../users/entities/user.entity";
import { CommonModule } from "../../common/common.module";
import { ConfigModule } from "@nestjs/config";
import { MockAIProvider } from "./providers/mock-ai.provider";
import { AuthModule } from "../auth/auth.module";

/**
 * AI服务模块 - v1.0.0
 * 提供AI辅助创作功能，包括标题生成、内容生成和配额管理
 *
 * 核心功能：
 * - AI标题生成（基于关键词）
 * - AI内容生成（基于提示词）
 * - 每日配额管理（3次/天）
 * - 使用统计和成本控制
 *
 * 模块依赖：
 * - TypeORM: 数据库操作
 * - Redis: 配额管理和缓存
 * - ConfigModule: 配置管理
 * - AuthModule: JWT认证
 *
 * 设计特点：
 * - 模拟AI服务，预留真实AI接口
 * - 企业级错误处理和日志记录
 * - 完整的参数验证和安全控制
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([AiUsageLog, User]),
    CommonModule,
    ConfigModule,
    forwardRef(() => AuthModule), // 修复JwtAuthGuard依赖问题
  ],
  controllers: [AiController],
  providers: [AiService, MockAIProvider],
  exports: [AiService],
})
export class AIModule {}
