/**
 * 企业级E2E测试全局设置文件
 *
 * 职责:
 * 1. E2E测试环境初始化
 * 2. 测试数据库和Redis设置
 * 3. 应用启动和配置
 * 4. 企业级错误处理
 */

import * as dotenv from "dotenv";

console.log("🚀 企业级E2E测试全局设置初始化...");

// 加载E2E测试环境配置
dotenv.config({ path: ".env.e2e.test" });

// 确保测试环境
process.env.NODE_ENV = "e2e-test";
process.env.REDIS_PREFIX = "e2e-test:";

// E2E测试安全检查
const testDbName = process.env.DB_NAME || "story_app_test";
if (!testDbName.includes("test")) {
  throw new Error("❌ 安全检查失败: 必须使用测试数据库");
}

console.log(`📊 E2E测试数据库: ${testDbName}`);
console.log("✅ 企业级E2E测试环境设置完成");
