import {
  IsOptional,
  IsString,
  IsDateString,
  IsBoolean,
  MaxLength,
  IsObject,
  ValidateNested,
} from "class-validator";
import { Type } from "class-transformer";

/**
 * 个人主页展示设置DTO
 */
export class ProfileDisplaySettingsDto {
  @IsOptional()
  @IsBoolean()
  showBirthday?: boolean;

  @IsOptional()
  @IsBoolean()
  showBio?: boolean;

  @IsOptional()
  @IsBoolean()
  showStatistics?: boolean;

  @IsOptional()
  @IsBoolean()
  showCharacters?: boolean;

  @IsOptional()
  @IsBoolean()
  showTimeline?: boolean;
}

/**
 * 更新用户信息DTO
 */
export class UpdateUserDto {
  @IsOptional()
  @IsString()
  @MaxLength(50, { message: "昵称不能超过50个字符" })
  nickname?: string;

  @IsOptional()
  @IsString()
  @MaxLength(50, { message: "用户名不能超过50个字符" })
  username?: string;

  @IsOptional()
  @IsString()
  avatarUrl?: string;

  @IsOptional()
  @IsDateString()
  birthDate?: string;

  @IsOptional()
  @IsString()
  @MaxLength(500, { message: "个人简介不能超过500个字符" })
  bio?: string;
}

/**
 * 更新展示设置DTO
 */
export class UpdateDisplaySettingsDto {
  @IsObject()
  @ValidateNested()
  @Type(() => ProfileDisplaySettingsDto)
  profileDisplaySettings: ProfileDisplaySettingsDto;
}

/**
 * 用户统计数据DTO
 */
export class UserStatisticsDto {
  storyCount: number;
  characterCount: number;
  lightingCount: number;
  followingCount: number;
  followersCount: number;
}

/**
 * 用户资料DTO
 */
export class UserProfileDto {
  id: string;
  userNumber?: string;
  nickname: string;
  username?: string;
  avatarUrl?: string;
  birthDate?: Date;
  bio?: string;
  profileDisplaySettings: ProfileDisplaySettingsDto;
  createdAt: Date;
  statistics?: UserStatisticsDto;
}
