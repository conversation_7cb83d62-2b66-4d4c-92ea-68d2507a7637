/**
 * 错误处理相关的类型定义
 * 提供类型安全的错误处理机制
 */

import type { QueryFailedError } from "typeorm";

/**
 * 错误详情接口
 * 用于结构化错误信息
 */
export interface ErrorDetails {
  code?: string;
  constraint?: string;
  table?: string;
  column?: string;
  field?: string;
  value?: unknown;
  originalError?: string;
  stackTrace?: string;
  [key: string]: unknown;
}

/**
 * 应用程序错误接口
 * 标准化的错误响应格式
 */
export interface AppError {
  message: string;
  error?: string;
  statusCode: number;
  details?: ErrorDetails;
  timestamp: string;
  path: string;
}

/**
 * 数据库错误接口
 * TypeORM查询错误的类型定义
 */
export interface DatabaseError extends QueryFailedError {
  constraint?: string;
  table?: string;
  column?: string;
  detail?: string;
  code?: string;
}

/**
 * 验证错误接口
 * 输入验证失败时的错误信息
 */
export interface ValidationError {
  field: string;
  value: unknown;
  constraints: string[];
  children?: ValidationError[];
}

/**
 * HTTP异常响应接口
 * NestJS HttpException的响应格式
 */
export interface HttpExceptionResponse {
  message: string | string[];
  error?: string;
  statusCode: number;
  details?: ErrorDetails;
}

/**
 * 错误上下文接口
 * 用于错误日志记录的上下文信息
 */
export interface ErrorContext {
  userId?: string;
  sessionId?: string;
  requestId?: string;
  userAgent?: string;
  ip?: string;
  method: string;
  url: string;
  body?: Record<string, unknown>;
  query?: Record<string, unknown>;
  params?: Record<string, unknown>;
}
