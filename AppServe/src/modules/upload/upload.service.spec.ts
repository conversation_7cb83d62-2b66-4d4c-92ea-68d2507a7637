import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { ConfigService } from "@nestjs/config";
import {
  BadRequestException,
  InternalServerErrorException,
} from "@nestjs/common";
import { UploadService, UploadType } from "./upload.service";
import type OSS from "ali-oss";

// Mock OSS模块
const mockOssInstance = {
  put: jest.fn(),
  delete: jest.fn(),
  deleteMulti: jest.fn(),
  head: jest.fn(),
  signatureUrl: jest.fn(),
};

jest.mock("ali-oss", () => {
  return jest.fn().mockImplementation(() => mockOssInstance);
});

describe("UploadService", () => {
  let service: UploadService;
  let configService: ConfigService;

  const mockOssConfig = {
    accessKeyId: "test-access-key",
    accessKeySecret: "test-secret-key",
    region: "oss-cn-hangzhou",
    bucket: "test-bucket",
    domain: "https://test-bucket.oss-cn-hangzhou.aliyuncs.com",
    baseDir: "ygs-uploads",
    maxFileSize: 5 * 1024 * 1024, // 5MB
    allowedMimeTypes: ["image/jpeg", "image/png", "image/gif"],
    folders: {
      avatar: "avatars",
      story: "stories",
      character: "characters",
      storyContent: "story-content",
      temp: "temp",
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UploadService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              if (key === "oss") {
                return mockOssConfig;
              }
              return null;
            }),
          },
        },
      ],
    }).compile();

    service = module.get<UploadService>(UploadService);
    configService = module.get<ConfigService>(ConfigService);
  });

  afterEach(() => {
    jest.clearAllMocks();
    // 重置mock实例
    mockOssInstance.put.mockReset();
    mockOssInstance.delete.mockReset();
    mockOssInstance.deleteMulti.mockReset();
    mockOssInstance.head.mockReset();
    mockOssInstance.signatureUrl.mockReset();
  });

  describe("constructor", () => {
    it("should be defined", () => {
      expect(service).toBeDefined();
    });

    it("should throw error when OSS config is missing", () => {
      jest.spyOn(configService, "get").mockImplementation((key: string) => {
        if (key === "oss") {
          return {
            ...mockOssConfig,
            accessKeyId: null,
            accessKeySecret: null,
          };
        }
        return null;
      });

      expect(() => {
        new UploadService(configService);
      }).toThrow("OSS配置缺失: accessKeyId 和 accessKeySecret 是必需的");
    });
  });

  describe("uploadFile", () => {
    const mockBuffer = Buffer.from("test file content");
    const mockOriginalName = "test.jpg";
    const mockMimeType = "image/jpeg";
    const mockUserId = "user-123";

    it("should upload file successfully", async () => {
      const expectedFilename = expect.stringMatching(
        /ygs-uploads\/avatarsuser-123\/\d{13}-[a-f0-9-]{36}\.jpg$/,
      );

      mockOssInstance.put.mockResolvedValue({
        name: "test-filename.jpg",
        url: "https://test-bucket.oss-cn-hangzhou.aliyuncs.com/test-filename.jpg",
        res: { status: 200 },
      } as OSS.PutObjectResult);

      mockOssInstance.signatureUrl.mockReturnValue(
        "https://signed-url.com/test-filename.jpg",
      );

      const result = await service.uploadFile(
        mockBuffer,
        mockOriginalName,
        mockMimeType,
        UploadType.AVATAR,
        mockUserId,
      );

      expect(result).toEqual({
        url: "https://signed-url.com/test-filename.jpg",
        filename: expectedFilename,
        size: mockBuffer.length,
        mimeType: mockMimeType,
      });

      expect(mockOssInstance.put).toHaveBeenCalledWith(
        expectedFilename,
        mockBuffer,
        {
          headers: {
            "Content-Type": mockMimeType,
            "Cache-Control": "public, max-age=31536000",
          },
        },
      );
    });

    it("should throw BadRequestException for invalid file type", async () => {
      await expect(
        service.uploadFile(
          mockBuffer,
          "test.txt",
          "text/plain",
          UploadType.AVATAR,
          mockUserId,
        ),
      ).rejects.toThrow(BadRequestException);
    });

    it("should throw BadRequestException for file too large", async () => {
      const largeBuffer = Buffer.alloc(6 * 1024 * 1024); // 6MB

      await expect(
        service.uploadFile(
          largeBuffer,
          mockOriginalName,
          mockMimeType,
          UploadType.AVATAR,
          mockUserId,
        ),
      ).rejects.toThrow(BadRequestException);
    });

    it("should throw InternalServerErrorException when OSS upload fails", async () => {
      mockOssInstance.put.mockRejectedValue(new Error("OSS upload failed"));

      await expect(
        service.uploadFile(
          mockBuffer,
          mockOriginalName,
          mockMimeType,
          UploadType.AVATAR,
          mockUserId,
        ),
      ).rejects.toThrow(InternalServerErrorException);
    });

    it("should re-throw BadRequestException when validation fails", async () => {
      const badRequestError = new BadRequestException("Invalid file type");
      mockOssInstance.put.mockRejectedValue(badRequestError);

      await expect(
        service.uploadFile(
          mockBuffer,
          mockOriginalName,
          mockMimeType,
          UploadType.AVATAR,
          mockUserId,
        ),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe("uploadFiles", () => {
    const mockFiles = [
      {
        buffer: Buffer.from("file1"),
        originalName: "file1.jpg",
        mimeType: "image/jpeg",
      },
      {
        buffer: Buffer.from("file2"),
        originalName: "file2.png",
        mimeType: "image/png",
      },
    ];

    it("should upload multiple files successfully", async () => {
      mockOssInstance.put.mockResolvedValue({
        name: "test-filename.jpg",
        url: "https://test-bucket.oss-cn-hangzhou.aliyuncs.com/test-filename.jpg",
        res: { status: 200 },
      } as OSS.PutObjectResult);

      mockOssInstance.signatureUrl.mockReturnValue(
        "https://signed-url.com/test-filename.jpg",
      );

      const results = await service.uploadFiles(
        mockFiles,
        UploadType.STORY,
        "user-123",
      );

      expect(results).toHaveLength(2);
      expect(results[0]).toEqual({
        url: "https://signed-url.com/test-filename.jpg",
        filename: expect.stringMatching(
          /ygs-uploads\/storiesuser-123\/\d{13}-[a-f0-9-]{36}\.jpg$/,
        ),
        size: mockFiles[0].buffer.length,
        mimeType: mockFiles[0].mimeType,
      });

      expect(mockOssInstance.put).toHaveBeenCalledTimes(2);
    });

    it("should handle partial upload failures", async () => {
      mockOssInstance.put
        .mockResolvedValueOnce({
          name: "test-filename.jpg",
          url: "https://test-bucket.oss-cn-hangzhou.aliyuncs.com/test-filename.jpg",
          res: { status: 200 },
        } as OSS.PutObjectResult)
        .mockRejectedValueOnce(new Error("Second upload failed"));

      mockOssInstance.signatureUrl.mockReturnValue(
        "https://signed-url.com/test-filename.jpg",
      );

      const results = await service.uploadFiles(
        mockFiles,
        UploadType.STORY,
        "user-123",
      );

      expect(results).toHaveLength(1);
      expect(results[0]).toEqual({
        url: "https://signed-url.com/test-filename.jpg",
        filename: expect.stringMatching(
          /ygs-uploads\/storiesuser-123\/\d{13}-[a-f0-9-]{36}\.jpg$/,
        ),
        size: mockFiles[0].buffer.length,
        mimeType: mockFiles[0].mimeType,
      });
    });
  });

  describe("deleteFile", () => {
    const mockFilename = "test-filename.jpg";

    it("should delete file successfully", async () => {
      mockOssInstance.delete.mockResolvedValue({
        res: { status: 204 },
      } as unknown as OSS.DeleteResult);

      await service.deleteFile(mockFilename);

      expect(mockOssInstance.delete).toHaveBeenCalledWith(mockFilename);
    });

    it("should throw InternalServerErrorException when deletion fails", async () => {
      mockOssInstance.delete.mockRejectedValue(new Error("Delete failed"));

      await expect(service.deleteFile(mockFilename)).rejects.toThrow(
        InternalServerErrorException,
      );
    });
  });

  describe("deleteFiles", () => {
    const mockFilenames = ["file1.jpg", "file2.png"];

    it("should delete multiple files successfully", async () => {
      mockOssInstance.deleteMulti.mockResolvedValue({
        deleted: mockFilenames,
        res: { status: 200 },
      } as unknown as OSS.DeleteMultiResult);

      await service.deleteFiles(mockFilenames);

      expect(mockOssInstance.deleteMulti).toHaveBeenCalledWith(mockFilenames);
    });

    it("should throw InternalServerErrorException when batch deletion fails", async () => {
      mockOssInstance.deleteMulti.mockRejectedValue(
        new Error("Batch delete failed"),
      );

      await expect(service.deleteFiles(mockFilenames)).rejects.toThrow(
        InternalServerErrorException,
      );
    });

    it("should return early when filenames array is empty", async () => {
      await service.deleteFiles([]);

      expect(mockOssInstance.deleteMulti).not.toHaveBeenCalled();
    });
  });

  describe("getFileInfo", () => {
    const mockFilename = "test-filename.jpg";

    it("should get file info successfully", async () => {
      const mockLastModified = new Date();
      const mockHeadResult = {
        res: {
          status: 200,
          headers: {
            "content-length": "1024",
            "content-type": "image/jpeg",
            "last-modified": mockLastModified.toISOString(),
          },
        },
      };

      mockOssInstance.head.mockResolvedValue(
        mockHeadResult as OSS.HeadObjectResult,
      );

      const result = await service.getFileInfo(mockFilename);

      expect(result).toEqual({
        exists: true,
        size: 1024,
        mimeType: "image/jpeg",
        lastModified: new Date(mockLastModified.toISOString()),
      });

      expect(mockOssInstance.head).toHaveBeenCalledWith(mockFilename);
    });

    it("should handle file not found", async () => {
      const notFoundError = new Error("NoSuchKey");
      (notFoundError as Error & { code: string }).code = "NoSuchKey";
      mockOssInstance.head.mockRejectedValue(notFoundError);

      const result = await service.getFileInfo("non-existent-file.jpg");

      expect(result).toEqual({
        exists: false,
      });
    });

    it("should throw InternalServerErrorException for other errors", async () => {
      mockOssInstance.head.mockRejectedValue(new Error("Other error"));

      await expect(service.getFileInfo(mockFilename)).rejects.toThrow(
        InternalServerErrorException,
      );
    });
  });

  describe("generatePresignedUrl", () => {
    const mockFilename = "test-filename.jpg";

    it("should generate presigned URL successfully", async () => {
      const mockSignedUrl = "https://signed-url.com/test-filename.jpg";
      mockOssInstance.signatureUrl.mockReturnValue(mockSignedUrl);

      const result = await service.generatePresignedUrl(mockFilename, 3600);

      expect(result).toBe(mockSignedUrl);
      expect(mockOssInstance.signatureUrl).toHaveBeenCalledWith(mockFilename, {
        expires: 3600,
        method: "PUT",
      });
    });

    it("should throw InternalServerErrorException when URL generation fails", async () => {
      mockOssInstance.signatureUrl.mockImplementation(() => {
        throw new Error("URL generation failed");
      });

      await expect(
        service.generatePresignedUrl(mockFilename, 3600),
      ).rejects.toThrow(InternalServerErrorException);
    });
  });

  describe("validateFile", () => {
    it("should validate file successfully", () => {
      const buffer = Buffer.from("test content");
      const mimeType = "image/jpeg";

      // 这个方法是private的，我们通过调用uploadFile间接测试
      expect(() => {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (service as any).validateFile(buffer, mimeType);
      }).not.toThrow();
    });

    it("should throw BadRequestException for invalid mime type", () => {
      const buffer = Buffer.from("test content");
      const mimeType = "text/plain";

      expect(() => {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (service as any).validateFile(buffer, mimeType);
      }).toThrow(BadRequestException);
    });

    it("should throw BadRequestException for file too large", () => {
      const largeBuffer = Buffer.alloc(6 * 1024 * 1024); // 6MB
      const mimeType = "image/jpeg";

      expect(() => {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (service as any).validateFile(largeBuffer, mimeType);
      }).toThrow(BadRequestException);
    });
  });

  describe("generateFilename", () => {
    it("should generate filename with user ID", () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = (service as any).generateFilename(
        "test.jpg",
        UploadType.AVATAR,
        "user-123",
      );

      expect(result).toMatch(
        /ygs-uploads\/avatarsuser-123\/\d{13}-[a-f0-9-]{36}\.jpg$/,
      );
    });

    it("should generate filename without user ID", () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = (service as any).generateFilename(
        "test.jpg",
        UploadType.TEMP,
      );

      expect(result).toMatch(/ygs-uploads\/temp\/\d{13}-[a-f0-9-]{36}\.jpg$/);
    });

    it("should handle filename without extension", () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = (service as any).generateFilename(
        "test",
        UploadType.AVATAR,
        "user-123",
      );

      expect(result).toMatch(
        /ygs-uploads\/avatarsuser-123\/\d{13}-[a-f0-9-]{36}$/,
      );
    });
  });

  describe("getAccessibleUrl", () => {
    const mockFilename = "test-filename.jpg";

    it("should return CDN URL when CDN domain is configured", async () => {
      jest.spyOn(configService, "get").mockImplementation((key: string) => {
        if (key === "oss") {
          return {
            ...mockOssConfig,
            cdnDomain: "https://cdn.example.com",
          };
        }
        return null;
      });

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = await (service as any).getAccessibleUrl(mockFilename);

      expect(result).toBe(`https://cdn.example.com/${mockFilename}`);
    });

    it("should return signed URL when CDN domain is not configured", async () => {
      const mockSignedUrl = "https://signed-url.com/test-filename.jpg";
      mockOssInstance.signatureUrl.mockReturnValue(mockSignedUrl);

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = await (service as any).getAccessibleUrl(mockFilename);

      expect(result).toBe(mockSignedUrl);
      expect(mockOssInstance.signatureUrl).toHaveBeenCalledWith(mockFilename, {
        expires: 1800,
      });
    });

    it("should return proxy URL when signature URL generation fails", async () => {
      mockOssInstance.signatureUrl.mockImplementation(() => {
        throw new Error("Signature URL generation failed");
      });

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = await (service as any).getAccessibleUrl(mockFilename);

      expect(result).toBe(
        `http://localhost:3000/api/v1/images/test/${mockFilename}`,
      );
    });

    it("should use custom signedUrlExpires when configured", async () => {
      const mockSignedUrl = "https://signed-url.com/test-filename.jpg";
      mockOssInstance.signatureUrl.mockReturnValue(mockSignedUrl);

      jest.spyOn(configService, "get").mockImplementation((key: string) => {
        if (key === "oss") {
          return {
            ...mockOssConfig,
            signedUrlExpires: 3600,
          };
        }
        return null;
      });

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = await (service as any).getAccessibleUrl(mockFilename);

      expect(result).toBe(mockSignedUrl);
      expect(mockOssInstance.signatureUrl).toHaveBeenCalledWith(mockFilename, {
        expires: 3600,
      });
    });
  });

  describe("getPublicUrl", () => {
    const mockFilename = "test-filename.jpg";

    it("should return CDN URL when CDN domain is configured", () => {
      jest.spyOn(configService, "get").mockImplementation((key: string) => {
        if (key === "oss") {
          return {
            ...mockOssConfig,
            cdnDomain: "https://cdn.example.com",
          };
        }
        return null;
      });

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = (service as any).getPublicUrl(mockFilename);

      expect(result).toBe(`https://cdn.example.com/${mockFilename}`);
    });

    it("should return public domain URL when CDN domain is not configured", () => {
      jest.spyOn(configService, "get").mockImplementation((key: string) => {
        if (key === "oss") {
          return {
            ...mockOssConfig,
            publicDomain: "https://public.example.com",
          };
        }
        return null;
      });

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = (service as any).getPublicUrl(mockFilename);

      expect(result).toBe(`https://public.example.com/${mockFilename}`);
    });

    it("should return default domain URL when no custom domains are configured", () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = (service as any).getPublicUrl(mockFilename);

      expect(result).toBe(
        `https://test-bucket.oss-cn-hangzhou.aliyuncs.com/${mockFilename}`,
      );
    });
  });

  describe("getFileExtension", () => {
    it("should extract file extension correctly", () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = (service as any).getFileExtension("test.jpg");
      expect(result).toBe(".jpg");
    });

    it("should return empty string for filename without extension", () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = (service as any).getFileExtension("test");
      expect(result).toBe("");
    });

    it("should handle multiple dots in filename", () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = (service as any).getFileExtension("test.backup.jpg");
      expect(result).toBe(".jpg");
    });
  });

  describe("extractFilenameFromUrl", () => {
    it("should extract filename from valid URL", () => {
      const url = "https://example.com/path/to/file.jpg";
      const result = service.extractFilenameFromUrl(url);

      expect(result).toBe("path/to/file.jpg");
    });

    it("should extract filename from URL with query parameters", () => {
      const url = "https://example.com/path/to/file.jpg?param=value";
      const result = service.extractFilenameFromUrl(url);

      expect(result).toBe("path/to/file.jpg");
    });

    it("should handle URL without leading slash", () => {
      const url = "https://example.com/file.jpg";
      const result = service.extractFilenameFromUrl(url);

      expect(result).toBe("file.jpg");
    });

    it("should return null for invalid URL", () => {
      const invalidUrl = "invalid-url";
      const result = service.extractFilenameFromUrl(invalidUrl);

      expect(result).toBeNull();
    });

    it("should handle empty pathname", () => {
      const url = "https://example.com/";
      const result = service.extractFilenameFromUrl(url);

      expect(result).toBe("");
    });
  });
});
