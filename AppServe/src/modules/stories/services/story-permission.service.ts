import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { Story } from "../entities/story.entity";
import { User } from "../../users/entities/user.entity";
import { Character } from "../../characters/entities/character.entity";
import { CharacterLighting } from "../../characters/entities/character-lighting.entity";
import { UserRelationship } from "../../users/entities/user-relationship.entity";

/**
 * 故事权限验证服务
 * 实现复杂的权限验证逻辑，支持4层权限控制
 */
@Injectable()
export class StoryPermissionService {
  constructor(
    @InjectRepository(Story)
    private readonly storyRepository: Repository<Story>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Character)
    private readonly characterRepository: Repository<Character>,
    @InjectRepository(CharacterLighting)
    private readonly characterLightingRepository: Repository<CharacterLighting>,
    @InjectRepository(UserRelationship)
    private readonly userRelationshipRepository: Repository<UserRelationship>,
  ) {}

  /**
   * 验证故事访问权限
   * @param story 故事实体
   * @param currentUser 当前用户
   * @returns 访问权限验证结果
   */
  async validateAccess(story: Story, currentUser: User): Promise<boolean> {
    // 作者始终有完全访问权限
    if (story.userId === currentUser.id) {
      return true;
    }

    // 根据权限级别进行验证
    switch (story.permissionLevel) {
      case "private":
        return await this.validatePrivateAccess(story, currentUser);

      case "friends":
        return await this.validateFriendsAccess(story, currentUser);

      case "public":
        return await this.validatePublicAccess(story, currentUser);

      case "characters_only":
        return await this.validateCharactersOnlyAccess(story, currentUser);

      default:
        return false;
    }
  }

  /**
   * 验证私密故事访问权限
   * @param story 故事
   * @param currentUser 当前用户
   * @returns 是否有权限
   */
  private async validatePrivateAccess(
    story: Story,
    currentUser: User,
  ): Promise<boolean> {
    // 私密故事只有作者可以访问
    return story.userId === currentUser.id;
  }

  /**
   * 验证好友故事访问权限
   * @param story 故事
   * @param currentUser 当前用户
   * @returns 是否有权限
   */
  private async validateFriendsAccess(
    story: Story,
    currentUser: User,
  ): Promise<boolean> {
    // 检查是否为好友关系
    const friendship = await this.userRelationshipRepository.findOne({
      where: [
        {
          userId: story.userId,
          relatedUserId: currentUser.id,
          status: 1 as const, // CONFIRMED
        },
        {
          userId: currentUser.id,
          relatedUserId: story.userId,
          status: 1 as const, // CONFIRMED
        },
      ],
    });

    return !!friendship;
  }

  /**
   * 验证公开故事访问权限
   * @param story 故事
   * @param currentUser 当前用户
   * @returns 是否有权限
   */
  private async validatePublicAccess(
    _story: Story,
    _currentUser: User,
  ): Promise<boolean> {
    // 公开故事所有人都可以访问
    return true;
  }

  /**
   * 验证人物专享故事访问权限
   * @param story 故事
   * @param currentUser 当前用户
   * @returns 是否有权限
   */
  private async validateCharactersOnlyAccess(
    story: Story,
    currentUser: User,
  ): Promise<boolean> {
    // 检查当前用户是否点亮了故事中的任何人物
    const lighting = await this.characterLightingRepository
      .createQueryBuilder("lighting")
      .innerJoin("lighting.character", "character")
      .innerJoin("character.stories", "story")
      .where("story.id = :storyId", { storyId: story.id })
      .andWhere("lighting.lighterUserId = :userId", { userId: currentUser.id })
      .getOne();

    return !!lighting;
  }

  /**
   * 验证故事编辑权限
   * @param story 故事
   * @param currentUser 当前用户
   * @returns 是否有编辑权限
   */
  async validateEditAccess(story: Story, currentUser: User): Promise<boolean> {
    // 只有作者可以编辑故事
    return story.userId === currentUser.id;
  }

  /**
   * 验证故事删除权限
   * @param story 故事
   * @param currentUser 当前用户
   * @returns 是否有删除权限
   */
  async validateDeleteAccess(
    story: Story,
    currentUser: User,
  ): Promise<boolean> {
    // 只有作者可以删除故事
    return story.userId === currentUser.id;
  }

  /**
   * 验证故事点赞权限
   * @param story 故事
   * @param currentUser 当前用户
   * @returns 是否有点赞权限
   */
  async validateLikeAccess(story: Story, currentUser: User): Promise<boolean> {
    // 首先检查是否有访问权限
    const hasAccess = await this.validateAccess(story, currentUser);
    if (!hasAccess) {
      return false;
    }

    // 检查故事是否允许点赞
    if (!story.allowLikes) {
      return false;
    }

    // 作者不能给自己的故事点赞
    if (story.userId === currentUser.id) {
      return false;
    }

    return true;
  }

  /**
   * 验证故事评论权限
   * @param story 故事
   * @param currentUser 当前用户
   * @returns 是否有评论权限
   */
  async validateCommentAccess(
    story: Story,
    currentUser: User,
  ): Promise<boolean> {
    // 首先检查是否有访问权限
    const hasAccess = await this.validateAccess(story, currentUser);
    if (!hasAccess) {
      return false;
    }

    // 检查故事是否允许评论
    return story.allowComments;
  }

  /**
   * 验证故事分享权限
   * @param story 故事
   * @param currentUser 当前用户
   * @returns 是否有分享权限
   */
  async validateShareAccess(story: Story, currentUser: User): Promise<boolean> {
    // 首先检查是否有访问权限
    const hasAccess = await this.validateAccess(story, currentUser);
    if (!hasAccess) {
      return false;
    }

    // 检查故事是否允许分享
    return story.allowSharing;
  }

  /**
   * 批量验证故事访问权限
   * @param stories 故事数组
   * @param currentUser 当前用户
   * @returns 有权限访问的故事数组
   */
  async filterAccessibleStories(
    stories: Story[],
    currentUser: User,
  ): Promise<Story[]> {
    const accessibleStories: Story[] = [];

    for (const story of stories) {
      const hasAccess = await this.validateAccess(story, currentUser);
      if (hasAccess) {
        accessibleStories.push(story);
      }
    }

    return accessibleStories;
  }

  /**
   * 获取故事权限级别描述
   * @param permissionLevel 权限级别
   * @returns 权限描述
   */
  getPermissionDescription(permissionLevel: string): string {
    switch (permissionLevel) {
      case "private":
        return "私密 - 仅作者可见";
      case "friends":
        return "好友 - 仅好友可见";
      case "public":
        return "公开 - 所有人可见";
      case "characters_only":
        return "人物专享 - 仅点亮人物的用户可见";
      default:
        return "未知权限级别";
    }
  }

  /**
   * 检查用户是否可以修改故事权限级别
   * @param story 故事
   * @param newPermissionLevel 新的权限级别
   * @param currentUser 当前用户
   * @returns 是否可以修改
   */
  async canChangePermissionLevel(
    story: Story,
    newPermissionLevel: string,
    currentUser: User,
  ): Promise<boolean> {
    // 只有作者可以修改权限级别
    if (story.userId !== currentUser.id) {
      return false;
    }

    // 如果故事已经有人物点亮，不能修改为人物专享以外的权限
    if (
      story.permissionLevel === "characters_only" &&
      newPermissionLevel !== "characters_only"
    ) {
      const hasLightings = await this.characterLightingRepository
        .createQueryBuilder("lighting")
        .innerJoin("lighting.character", "character")
        .innerJoin("character.stories", "story")
        .where("story.id = :storyId", { storyId: story.id })
        .getCount();

      if (hasLightings > 0) {
        return false;
      }
    }

    return true;
  }

  /**
   * 获取故事的访问统计信息
   * @param story 故事
   * @returns 访问统计
   */
  async getAccessStats(story: Story): Promise<{
    totalViews: number;
    uniqueViewers: number;
    lightedCharacters: number;
    friendViews: number;
  }> {
    // 获取点亮的人物数量
    const lightedCharacters = await this.characterLightingRepository
      .createQueryBuilder("lighting")
      .innerJoin("lighting.character", "character")
      .innerJoin("character.stories", "story")
      .where("story.id = :storyId", { storyId: story.id })
      .getCount();

    // 获取好友访问量（如果是好友权限）
    let friendViews = 0;
    if (story.permissionLevel === "friends") {
      const friendsCount = await this.userRelationshipRepository.count({
        where: [
          { userId: story.userId, status: 1 as const }, // CONFIRMED
          { relatedUserId: story.userId, status: 1 as const }, // CONFIRMED
        ],
      });
      friendViews = friendsCount;
    }

    return {
      totalViews: story.viewCount,
      uniqueViewers: Math.floor(story.viewCount * 0.7), // 估算唯一访问者
      lightedCharacters,
      friendViews,
    };
  }
}
