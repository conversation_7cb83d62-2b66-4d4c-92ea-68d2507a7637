import { Injectable, NotFoundException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { Notification, NotificationType } from "./entities/notification.entity";
import type { CreateNotificationDto } from "./dto/notification.dto";

/**
 * 通知服务
 * 提供通知的创建、查询、更新等功能
 */
@Injectable()
export class NotificationsService {
  constructor(
    @InjectRepository(Notification)
    private notificationRepository: Repository<Notification>,
  ) {}

  /**
   * 创建通知
   */
  async createNotification(
    createData: CreateNotificationDto,
  ): Promise<Notification> {
    const notification = this.notificationRepository.create(createData);
    return await this.notificationRepository.save(notification);
  }

  /**
   * 获取用户的通知列表
   */
  async getUserNotifications(
    userId: string,
    page: number = 1,
    limit: number = 20,
    unreadOnly: boolean = false,
  ): Promise<{
    notifications: Notification[];
    total: number;
    unreadCount: number;
    hasNext: boolean;
  }> {
    const offset = (page - 1) * limit;
    const queryBuilder =
      this.notificationRepository.createQueryBuilder("notification");

    // 基础查询条件
    queryBuilder.where("notification.userId = :userId", { userId });

    // 如果只查询未读消息
    if (unreadOnly) {
      queryBuilder.andWhere("notification.isRead = false");
    }

    // 分页查询
    const [notifications, total] = await queryBuilder
      .orderBy("notification.createdAt", "DESC")
      .skip(offset)
      .take(limit)
      .getManyAndCount();

    // 获取未读消息数量
    const unreadCount = await this.notificationRepository.count({
      where: { userId, isRead: false },
    });

    return {
      notifications,
      total,
      unreadCount,
      hasNext: total > offset + limit,
    };
  }

  /**
   * 标记通知为已读
   */
  async markAsRead(notificationId: string, userId: string): Promise<void> {
    const notification = await this.notificationRepository.findOne({
      where: { id: notificationId, userId },
    });

    if (!notification) {
      throw new NotFoundException("通知不存在");
    }

    if (!notification.isRead) {
      await this.notificationRepository.update(notificationId, {
        isRead: true,
        readAt: new Date(),
      });
    }
  }

  /**
   * 标记所有通知为已读
   */
  async markAllAsRead(userId: string): Promise<void> {
    await this.notificationRepository.update(
      { userId, isRead: false },
      { isRead: true, readAt: new Date() },
    );
  }

  /**
   * 删除通知
   */
  async deleteNotification(
    notificationId: string,
    userId: string,
  ): Promise<void> {
    const result = await this.notificationRepository.delete({
      id: notificationId,
      userId,
    });

    if (result.affected === 0) {
      throw new NotFoundException("通知不存在");
    }
  }

  /**
   * 批量删除通知
   */
  async deleteNotifications(
    notificationIds: string[],
    userId: string,
  ): Promise<void> {
    await this.notificationRepository.delete({
      id: notificationIds as never,
      userId,
    });
  }

  /**
   * 获取未读通知数量
   */
  async getUnreadCount(userId: string): Promise<number> {
    return await this.notificationRepository.count({
      where: { userId, isRead: false },
    });
  }

  /**
   * 创建人物点亮通知
   */
  async createCharacterLightingNotification(
    userId: string,
    lighterName: string,
    characterName: string,
    lightingId: string,
  ): Promise<Notification> {
    return await this.createNotification({
      userId,
      type: NotificationType.CHARACTER_LIGHTING,
      title: "有人点亮了你的人物",
      content: `${lighterName} 点亮了你的人物"${characterName}"`,
      relatedId: lightingId,
      actionUrl: `/characters/lightings/${lightingId}`,
    });
  }

  /**
   * 创建关系确认通知
   */
  async createRelationshipConfirmedNotification(
    userId: string,
    confirmerName: string,
    relationshipId: string,
  ): Promise<Notification> {
    return await this.createNotification({
      userId,
      type: NotificationType.RELATIONSHIP_CONFIRMED,
      title: "关系确认",
      content: `${confirmerName} 确认了与你的关系`,
      relatedId: relationshipId,
      actionUrl: `/relationships/${relationshipId}`,
    });
  }

  /**
   * 创建故事分享通知
   */
  async createStorySharedNotification(
    userId: string,
    sharerName: string,
    storyTitle: string,
    storyId: string,
  ): Promise<Notification> {
    return await this.createNotification({
      userId,
      type: NotificationType.STORY_SHARED,
      title: "故事被分享",
      content: `${sharerName} 分享了故事"${storyTitle}"`,
      relatedId: storyId,
      actionUrl: `/stories/${storyId}`,
    });
  }

  /**
   * 创建故事点赞通知
   */
  async createStoryLikedNotification(
    userId: string,
    likerName: string,
    storyTitle: string,
    storyId: string,
  ): Promise<Notification> {
    return await this.createNotification({
      userId,
      type: NotificationType.STORY_LIKED,
      title: "故事被点赞",
      content: `${likerName} 点赞了你的故事"${storyTitle}"`,
      relatedId: storyId,
      actionUrl: `/stories/${storyId}`,
    });
  }

  /**
   * 创建故事评论通知
   */
  async createStoryCommentedNotification(
    userId: string,
    commenterName: string,
    storyTitle: string,
    commentId: string,
    storyId: string,
  ): Promise<Notification> {
    return await this.createNotification({
      userId,
      type: NotificationType.STORY_COMMENTED,
      title: "故事有新评论",
      content: `${commenterName} 评论了你的故事"${storyTitle}"`,
      relatedId: commentId,
      actionUrl: `/stories/${storyId}#comment-${commentId}`,
    });
  }

  /**
   * 清理过期通知（30天前的已读通知）
   */
  async cleanupOldNotifications(): Promise<void> {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    await this.notificationRepository.delete({
      isRead: true,
      readAt: thirtyDaysAgo as never,
    });
  }
}
