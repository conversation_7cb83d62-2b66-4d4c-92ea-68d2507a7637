import { Module, forwardRef } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { UploadService } from "./upload.service";
import { UploadController } from "./upload.controller";
import { ossConfig } from "../../config/oss.config";
import { AuthModule } from "../auth/auth.module";

@Module({
  imports: [
    ConfigModule.forFeature(ossConfig),
    forwardRef(() => AuthModule), // 修复JwtAuthGuard依赖问题
  ],
  providers: [UploadService],
  controllers: [UploadController],
  exports: [UploadService],
})
export class UploadModule {}
