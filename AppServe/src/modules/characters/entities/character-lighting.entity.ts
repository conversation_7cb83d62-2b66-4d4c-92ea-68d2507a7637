import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  Unique,
  Index,
} from "typeorm";
import { User } from "../../users/entities/user.entity";
import { Character } from "./character.entity";
import { Story } from "../../stories/entities/story.entity";
import { LightRequest } from "./light-request.entity";

export enum LightingStatus {
  PENDING = 1,
  CONFIRMED = 2,
  REJECTED = 3,
}

@Entity("character_lightings")
@Unique(["characterId", "storyId", "lighterUserId"]) // 一个人物在一个故事中只能被一个用户点亮
@Index(["characterId"]) // 人物查询索引
@Index(["lighterUserId"]) // 点亮用户查询索引
@Index(["storyId", "characterId"]) // 故事人物查询索引
export class CharacterLighting {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ name: "character_id" })
  characterId: string;

  @Column({ name: "story_id" })
  storyId: string;

  @Column({ name: "lighter_user_id" })
  lighterUserId: string;

  @Column({ name: "creator_user_id" })
  creatorUserId: string;

  @Column({ name: "request_id", nullable: true })
  requestId: string;

  @Column({
    length: 20,
    default: "active",
    comment: "状态：active/cancelled",
  })
  status: "active" | "cancelled";

  @Column({ name: "confirmed_at", nullable: true, comment: "确认时间" })
  confirmedAt: Date;

  @CreateDateColumn({ name: "created_at", comment: "创建时间" })
  createdAt: Date;

  // 关联关系
  @ManyToOne(() => Character, (character) => character.lightings, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "character_id" })
  character: Character;

  @ManyToOne(() => Story, { onDelete: "CASCADE" })
  @JoinColumn({ name: "story_id" })
  story: Story;

  @ManyToOne(() => User, (user) => user.lightings, { onDelete: "CASCADE" })
  @JoinColumn({ name: "lighter_user_id" })
  lighterUser: User;

  @ManyToOne(() => User, (user) => user.receivedLightings, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "creator_user_id" })
  creatorUser: User;

  @ManyToOne(() => LightRequest, { nullable: true })
  @JoinColumn({ name: "request_id" })
  request: LightRequest;
}
