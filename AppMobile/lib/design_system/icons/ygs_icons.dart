import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../tokens/ygs_colors.dart';
import '../tokens/ygs_spacing.dart';

/// YGS设计系统 - 图标组件
/// 基于Figma设计规范v1.0.0
class YgsIcons {
  YgsIcons._(); // 防止实例化

  // ========== Tab图标路径定义 ==========
  /// 推荐Tab图标
  static const String tabHomeActive = 'assets/icons/tab/tab-home-active.png';
  static const String tabHomeDefault = 'assets/icons/tab/tab-home-default.png';
  
  /// 故事Tab图标
  static const String tabStoryActive = 'assets/icons/tab/tab-story-active.png';
  static const String tabStoryDefault = 'assets/icons/tab/tab-story-default.png';
  
  /// 消息Tab图标
  static const String tabNewsActive = 'assets/icons/tab/tab-news-active.png';
  static const String tabNewsDefault = 'assets/icons/tab/tab-news-default.png';
  
  /// 我的Tab图标
  static const String tabMyActive = 'assets/icons/tab/tab-my-active.png';
  static const String tabMyDefault = 'assets/icons/tab/tab-my-default.png';

  // ========== 图标组件构建方法 ==========
  /// 构建Tab图标组件
  static Widget buildTabIcon({
    required String activePath,
    required String defaultPath,
    required bool isActive,
    double? size,
    double? width,
    double? height,
  }) {
    final iconSize = size ?? YgsSpacing.iconTab;
    
    return Image.asset(
      isActive ? activePath : defaultPath,
      width: width ?? iconSize,
      height: height ?? iconSize,
      fit: BoxFit.contain,
      errorBuilder: (context, error, stackTrace) {
        // 图标加载失败时的占位符
        return Container(
          width: width ?? iconSize,
          height: height ?? iconSize,
          decoration: BoxDecoration(
            color: YgsColors.textTertiary,
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Icon(
            Icons.image_not_supported,
            size: (iconSize * 0.6),
            color: YgsColors.backgroundCard,
          ),
        );
      },
    );
  }

  /// 推荐Tab图标
  static Widget tabHome({
    required bool isActive,
    double? size,
  }) {
    return buildTabIcon(
      activePath: tabHomeActive,
      defaultPath: tabHomeDefault,
      isActive: isActive,
      size: size,
    );
  }

  /// 故事Tab图标
  static Widget tabStory({
    required bool isActive,
    double? size,
  }) {
    return buildTabIcon(
      activePath: tabStoryActive,
      defaultPath: tabStoryDefault,
      isActive: isActive,
      size: size,
    );
  }

  /// 消息Tab图标
  static Widget tabNews({
    required bool isActive,
    double? size,
  }) {
    return buildTabIcon(
      activePath: tabNewsActive,
      defaultPath: tabNewsDefault,
      isActive: isActive,
      size: size,
    );
  }

  /// 我的Tab图标
  static Widget tabMy({
    required bool isActive,
    double? size,
  }) {
    return buildTabIcon(
      activePath: tabMyActive,
      defaultPath: tabMyDefault,
      isActive: isActive,
      size: size,
    );
  }

  // ========== 系统图标封装 ==========
  /// 构建系统图标组件
  static Widget buildSystemIcon({
    required IconData icon,
    double? size,
    Color? color,
    YgsIconSize iconSize = YgsIconSize.medium,
  }) {
    double iconSizeValue;
    
    switch (iconSize) {
      case YgsIconSize.small:
        iconSizeValue = YgsSpacing.iconSm;
        break;
      case YgsIconSize.medium:
        iconSizeValue = YgsSpacing.iconMd;
        break;
      case YgsIconSize.large:
        iconSizeValue = YgsSpacing.iconLg;
        break;
      case YgsIconSize.extraLarge:
        iconSizeValue = YgsSpacing.iconXl;
        break;
    }

    return Icon(
      icon,
      size: size ?? iconSizeValue,
      color: color ?? YgsColors.textPrimary,
    );
  }

  /// 检查图标
  static Widget check({
    YgsIconSize size = YgsIconSize.medium,
    Color? color,
  }) {
    return buildSystemIcon(
      icon: Icons.check,
      iconSize: size,
      color: color ?? YgsColors.successPrimary,
    );
  }

  /// 错误图标
  static Widget error({
    YgsIconSize size = YgsIconSize.medium,
    Color? color,
  }) {
    return buildSystemIcon(
      icon: Icons.error_outline,
      iconSize: size,
      color: color ?? YgsColors.errorPrimary,
    );
  }

  /// 警告图标
  static Widget warning({
    YgsIconSize size = YgsIconSize.medium,
    Color? color,
  }) {
    return buildSystemIcon(
      icon: Icons.warning_outlined,
      iconSize: size,
      color: color ?? YgsColors.warningPrimary,
    );
  }

  /// 信息图标
  static Widget info({
    YgsIconSize size = YgsIconSize.medium,
    Color? color,
  }) {
    return buildSystemIcon(
      icon: Icons.info_outline,
      iconSize: size,
      color: color ?? YgsColors.infoPrimary,
    );
  }

  /// 关闭图标
  static Widget close({
    YgsIconSize size = YgsIconSize.medium,
    Color? color,
  }) {
    return buildSystemIcon(
      icon: Icons.close,
      iconSize: size,
      color: color,
    );
  }

  /// 返回图标
  static Widget back({
    YgsIconSize size = YgsIconSize.medium,
    Color? color,
  }) {
    return buildSystemIcon(
      icon: Icons.arrow_back_ios,
      iconSize: size,
      color: color,
    );
  }

  /// 搜索图标
  static Widget search({
    YgsIconSize size = YgsIconSize.medium,
    Color? color,
  }) {
    return buildSystemIcon(
      icon: Icons.search,
      iconSize: size,
      color: color,
    );
  }

  /// 更多图标
  static Widget more({
    YgsIconSize size = YgsIconSize.medium,
    Color? color,
  }) {
    return buildSystemIcon(
      icon: Icons.more_horiz,
      iconSize: size,
      color: color,
    );
  }
}

/// 图标尺寸枚举
enum YgsIconSize {
  /// 小图标 - 20.w
  small,
  
  /// 中图标 - 24.w
  medium,
  
  /// 大图标 - 32.w
  large,
  
  /// 超大图标 - 40.w
  extraLarge,
}