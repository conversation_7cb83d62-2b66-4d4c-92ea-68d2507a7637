# YGS Flutter 架构设计文档

> **文档版本**: v1.0.0  
> **Flutter版本**: 3.32.0  
> **创建时间**: 2025-07-23  
> **架构模式**: Clean Architecture + MVVM  
> **设计原则**: SOLID原则、DRY原则、企业级标准

---

## 📋 架构设计概述

本文档详细定义了YGS Flutter应用的技术架构设计，包括分层架构、数据流设计、状态管理方案、模块化设计等关键技术决策。

## 🏗️ 整体架构设计

### Clean Architecture 分层模型

```
┌─────────────────────────────────────────────────────────────┐
│                        UI Layer                              │
│                   (Widgets & Pages)                          │
├─────────────────────────────────────────────────────────────┤
│                   Presentation Layer                         │
│              (ViewModels & Controllers)                      │
├─────────────────────────────────────────────────────────────┤
│                     Domain Layer                             │
│              (Use Cases & Entities)                          │
├─────────────────────────────────────────────────────────────┤
│                      Data Layer                              │
│         (Repositories & Data Sources)                        │
├─────────────────────────────────────────────────────────────┤
│                 Infrastructure Layer                         │
│            (Network, Storage, Platform)                      │
└─────────────────────────────────────────────────────────────┘
```

### 数据流架构图

```
┌─────────┐     ┌──────────────┐     ┌─────────────┐
│   UI    │────▶│  ViewModel   │────▶│   UseCase   │
│         │◀────│              │◀────│             │
└─────────┘     └──────────────┘     └─────────────┘
                                             │
                                             ▼
┌─────────┐     ┌──────────────┐     ┌─────────────┐
│  Model  │◀────│  Repository  │◀────│  DataSource │
│         │────▶│              │────▶│             │
└─────────┘     └──────────────┘     └─────────────┘
```

## 📁 项目目录结构

```
lib/
├── app/                        # 应用配置层
│   ├── app.dart               # 应用主入口
│   ├── config/                # 配置文件
│   │   ├── env_config.dart    # 环境配置
│   │   ├── api_config.dart    # API配置
│   │   └── app_config.dart    # 应用配置
│   ├── di/                    # 依赖注入
│   │   ├── injection.dart     # 注入配置
│   │   └── modules/           # 模块注入
│   └── routes/                # 路由配置
│       ├── app_router.dart    # 路由定义
│       └── route_guards.dart  # 路由守卫
│
├── core/                      # 核心基础设施
│   ├── constants/             # 常量定义
│   │   ├── api_constants.dart
│   │   ├── app_constants.dart
│   │   └── storage_keys.dart
│   ├── errors/                # 错误处理
│   │   ├── exceptions.dart
│   │   └── failures.dart
│   ├── network/               # 网络基础
│   │   ├── dio_client.dart
│   │   ├── interceptors/
│   │   └── api_result.dart
│   ├── themes/                # 主题系统
│   │   ├── app_theme.dart
│   │   ├── app_colors.dart
│   │   └── app_typography.dart
│   ├── utils/                 # 工具类
│   │   ├── validators.dart
│   │   ├── formatters.dart
│   │   └── extensions/
│   └── widgets/               # 通用组件
│       ├── buttons/
│       ├── dialogs/
│       └── loading/
│
├── features/                  # 功能模块（按领域划分）
│   ├── auth/                  # 认证模块
│   │   ├── data/
│   │   │   ├── datasources/
│   │   │   ├── models/
│   │   │   └── repositories/
│   │   ├── domain/
│   │   │   ├── entities/
│   │   │   ├── repositories/
│   │   │   └── usecases/
│   │   └── presentation/
│   │       ├── pages/
│   │       ├── widgets/
│   │       └── providers/
│   │
│   ├── stories/               # 故事模块
│   │   ├── data/
│   │   ├── domain/
│   │   └── presentation/
│   │
│   ├── characters/            # 人物模块
│   │   ├── data/
│   │   ├── domain/
│   │   └── presentation/
│   │
│   ├── lighting/              # 点亮系统
│   │   ├── data/
│   │   ├── domain/
│   │   └── presentation/
│   │
│   └── social/                # 社交模块
│       ├── data/
│       ├── domain/
│       └── presentation/
│
├── services/                  # 应用服务
│   ├── auth_service.dart      # 认证服务
│   ├── storage_service.dart   # 存储服务
│   ├── notification_service.dart
│   └── analytics_service.dart
│
└── main.dart                  # 应用入口
```

## 🔄 状态管理架构

### Riverpod 2.5 架构设计

#### Provider层次结构
```dart
// 1. 基础Provider - 提供单一数据源
final userProvider = StateProvider<User?>((ref) => null);

// 2. 派生Provider - 基于其他Provider计算
final isAuthenticatedProvider = Provider<bool>((ref) {
  final user = ref.watch(userProvider);
  return user != null;
});

// 3. 异步Provider - 处理异步数据
final storiesProvider = FutureProvider<List<Story>>((ref) async {
  final repository = ref.read(storyRepositoryProvider);
  return repository.getStories();
});

// 4. StateNotifierProvider - 复杂状态管理
final authStateProvider = StateNotifierProvider<AuthStateNotifier, AuthState>((ref) {
  return AuthStateNotifier(ref.read(authRepositoryProvider));
});
```

#### 状态管理最佳实践
```dart
// 状态类定义
@freezed
class AuthState with _$AuthState {
  const factory AuthState.initial() = _Initial;
  const factory AuthState.loading() = _Loading;
  const factory AuthState.authenticated(User user) = _Authenticated;
  const factory AuthState.unauthenticated() = _Unauthenticated;
  const factory AuthState.error(String message) = _Error;
}

// StateNotifier实现
class AuthStateNotifier extends StateNotifier<AuthState> {
  final AuthRepository _repository;
  
  AuthStateNotifier(this._repository) : super(const AuthState.initial());
  
  Future<void> login(String phone, String code) async {
    state = const AuthState.loading();
    try {
      final user = await _repository.login(phone, code);
      state = AuthState.authenticated(user);
    } catch (e) {
      state = AuthState.error(e.toString());
    }
  }
}
```

### 数据流管理

#### 单向数据流原则
```
UI Event → Action → StateNotifier → State Change → UI Update
```

#### 实现示例
```dart
// UI层
class LoginPage extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authStateProvider);
    
    return authState.when(
      initial: () => LoginForm(),
      loading: () => LoadingIndicator(),
      authenticated: (user) => HomeRedirect(),
      unauthenticated: () => LoginForm(),
      error: (message) => ErrorDialog(message),
    );
  }
}

// 事件处理
void _onLoginPressed() {
  ref.read(authStateProvider.notifier).login(phone, code);
}
```

## 🗄️ 数据层架构

### Repository模式实现

```dart
// 抽象接口定义
abstract class StoryRepository {
  Future<List<Story>> getStories({int page = 1, int limit = 20});
  Future<Story> getStoryById(String id);
  Future<Story> createStory(StoryCreateRequest request);
  Future<void> updateStory(String id, StoryUpdateRequest request);
  Future<void> deleteStory(String id);
}

// 具体实现
class StoryRepositoryImpl implements StoryRepository {
  final RemoteDataSource _remoteDataSource;
  final LocalDataSource _localDataSource;
  final NetworkInfo _networkInfo;
  
  StoryRepositoryImpl({
    required RemoteDataSource remoteDataSource,
    required LocalDataSource localDataSource,
    required NetworkInfo networkInfo,
  }) : _remoteDataSource = remoteDataSource,
       _localDataSource = localDataSource,
       _networkInfo = networkInfo;
       
  @override
  Future<List<Story>> getStories({int page = 1, int limit = 20}) async {
    if (await _networkInfo.isConnected) {
      try {
        final stories = await _remoteDataSource.getStories(page, limit);
        await _localDataSource.cacheStories(stories);
        return stories;
      } catch (e) {
        return _localDataSource.getCachedStories();
      }
    } else {
      return _localDataSource.getCachedStories();
    }
  }
}
```

### 数据源分离

```dart
// 远程数据源
class RemoteDataSource {
  final DioClient _dioClient;
  
  Future<List<StoryModel>> getStories(int page, int limit) async {
    final response = await _dioClient.get(
      '/stories',
      queryParameters: {'page': page, 'limit': limit},
    );
    
    return (response.data['data'] as List)
        .map((json) => StoryModel.fromJson(json))
        .toList();
  }
}

// 本地数据源
class LocalDataSource {
  final IsarDatabase _database;
  
  Future<void> cacheStories(List<StoryModel> stories) async {
    await _database.writeTxn(() async {
      await _database.stories.putAll(stories);
    });
  }
  
  Future<List<StoryModel>> getCachedStories() async {
    return _database.stories.where().findAll();
  }
}
```

## 🔐 安全架构设计

### 认证流程
```dart
class AuthInterceptor extends Interceptor {
  final TokenManager _tokenManager;
  
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    final token = await _tokenManager.getAccessToken();
    if (token != null) {
      options.headers['Authorization'] = 'Bearer $token';
    }
    handler.next(options);
  }
  
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    if (err.response?.statusCode == 401) {
      final refreshed = await _tokenManager.refreshToken();
      if (refreshed) {
        // 重试请求
        final cloneReq = await _retry(err.requestOptions);
        return handler.resolve(cloneReq);
      }
    }
    handler.next(err);
  }
}
```

### 数据加密
```dart
class SecureStorageService {
  static const _storage = FlutterSecureStorage();
  
  Future<void> saveSecure(String key, String value) async {
    final encrypted = await _encrypt(value);
    await _storage.write(key: key, value: encrypted);
  }
  
  Future<String?> getSecure(String key) async {
    final encrypted = await _storage.read(key: key);
    if (encrypted != null) {
      return _decrypt(encrypted);
    }
    return null;
  }
}
```

## 🎨 UI组件架构

### 设计系统实现
```dart
// 主题定义
class AppTheme {
  static ThemeData lightTheme = ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme.fromSeed(
      seedColor: AppColors.primary,
      brightness: Brightness.light,
    ),
    textTheme: AppTypography.textTheme,
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: AppButtonStyles.elevated,
    ),
  );
  
  static ThemeData darkTheme = ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme.fromSeed(
      seedColor: AppColors.primary,
      brightness: Brightness.dark,
    ),
    textTheme: AppTypography.textTheme,
  );
}
```

### 响应式布局
```dart
class ResponsiveBuilder extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;
  
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth >= 1200) {
          return desktop ?? tablet ?? mobile;
        } else if (constraints.maxWidth >= 768) {
          return tablet ?? mobile;
        }
        return mobile;
      },
    );
  }
}
```

## 🚀 性能优化架构

### 图片加载优化
```dart
class OptimizedImage extends StatelessWidget {
  final String imageUrl;
  
  @override
  Widget build(BuildContext context) {
    return CachedNetworkImage(
      imageUrl: imageUrl,
      memCacheWidth: 300,
      memCacheHeight: 300,
      placeholder: (context, url) => Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: Container(color: Colors.white),
      ),
      errorWidget: (context, url, error) => Icon(Icons.error),
      imageBuilder: (context, imageProvider) => Container(
        decoration: BoxDecoration(
          image: DecorationImage(
            image: imageProvider,
            fit: BoxFit.cover,
          ),
        ),
      ),
    );
  }
}
```

### 列表优化
```dart
class OptimizedList<T> extends StatelessWidget {
  final List<T> items;
  final Widget Function(BuildContext, T) itemBuilder;
  
  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: items.length,
      itemExtent: 80.0, // 固定高度优化
      addAutomaticKeepAlives: false,
      addRepaintBoundaries: false,
      itemBuilder: (context, index) {
        return RepaintBoundary(
          child: itemBuilder(context, items[index]),
        );
      },
    );
  }
}
```

## 📊 监控与分析架构

### 性能监控
```dart
class PerformanceMonitor {
  static void trackScreenView(String screenName) {
    FirebaseAnalytics.instance.setCurrentScreen(
      screenName: screenName,
      screenClassOverride: screenName,
    );
  }
  
  static void trackPerformance(String name, Future Function() operation) async {
    final trace = FirebasePerformance.instance.newTrace(name);
    await trace.start();
    try {
      await operation();
    } finally {
      await trace.stop();
    }
  }
}
```

### 错误追踪
```dart
class ErrorReporter {
  static void reportError(dynamic error, StackTrace? stack) {
    FirebaseCrashlytics.instance.recordError(
      error,
      stack,
      fatal: false,
    );
  }
  
  static void reportFlutterError(FlutterErrorDetails details) {
    FirebaseCrashlytics.instance.recordFlutterFatalError(details);
  }
}
```

## 🔧 开发工具架构

### 日志系统
```dart
class Logger {
  static const _logger = PrettyDioLogger(
    requestHeader: true,
    requestBody: true,
    responseBody: true,
    responseHeader: false,
    compact: false,
  );
  
  static void d(String message) => debugPrint('🟦 DEBUG: $message');
  static void i(String message) => debugPrint('🟩 INFO: $message');
  static void w(String message) => debugPrint('🟨 WARN: $message');
  static void e(String message) => debugPrint('🟥 ERROR: $message');
}
```

### 调试工具
```dart
class DebugUtils {
  static Widget debugBanner({required Widget child}) {
    return Stack(
      children: [
        child,
        if (kDebugMode)
          Positioned(
            top: 0,
            right: 0,
            child: Banner(
              message: 'DEBUG',
              location: BannerLocation.topEnd,
            ),
          ),
      ],
    );
  }
}
```

---

**架构设计**: YGS技术架构组  
**最後更新**: 2025-07-23