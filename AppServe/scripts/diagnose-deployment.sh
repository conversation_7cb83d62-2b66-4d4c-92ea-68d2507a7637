#!/bin/bash

# 部署问题诊断脚本

set -e

echo "🔍 开始诊断部署问题..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查架构兼容性
check_architecture() {
    echo -e "${BLUE}1. 检查架构兼容性${NC}"
    echo "================================"
    
    SYSTEM_ARCH=$(uname -m)
    echo "💻 系统架构: $SYSTEM_ARCH"
    
    if docker image inspect node:22.16.0-alpine &> /dev/null; then
        NODE_ARCH=$(docker image inspect node:22.16.0-alpine --format '{{.Architecture}}')
        echo "🐳 Node镜像架构: $NODE_ARCH"
        
        if [ "$SYSTEM_ARCH" = "x86_64" ] && [ "$NODE_ARCH" = "arm64" ]; then
            echo -e "${RED}❌ 架构不匹配！这会导致严重的性能问题${NC}"
            echo -e "${YELLOW}💡 建议：使用x86_64架构的镜像${NC}"
        elif [ "$SYSTEM_ARCH" = "aarch64" ] && [ "$NODE_ARCH" = "amd64" ]; then
            echo -e "${RED}❌ 架构不匹配！这会导致严重的性能问题${NC}"
            echo -e "${YELLOW}💡 建议：使用ARM架构的镜像${NC}"
        else
            echo -e "${GREEN}✅ 架构匹配${NC}"
        fi
    else
        echo -e "${RED}❌ Node镜像不存在${NC}"
    fi
    echo ""
}

# 检查容器状态
check_containers() {
    echo -e "${BLUE}2. 检查容器状态${NC}"
    echo "================================"
    
    containers=("story-app-backend" "story-app-postgres" "story-app-redis" "story-app-nginx")
    
    for container in "${containers[@]}"; do
        if docker ps -q -f name=$container | grep -q .; then
            status=$(docker ps --format "{{.Status}}" -f name=$container)
            echo -e "${GREEN}✅ $container: $status${NC}"
        else
            echo -e "${RED}❌ $container: 未运行${NC}"
        fi
    done
    echo ""
}

# 检查应用日志
check_app_logs() {
    echo -e "${BLUE}3. 检查应用日志${NC}"
    echo "================================"
    
    if docker ps -q -f name=story-app-backend | grep -q .; then
        echo "📋 应用容器日志 (最后20行):"
        echo "--------------------------------"
        docker logs story-app-backend --tail 20
        echo "--------------------------------"
        
        # 检查常见错误模式
        echo ""
        echo "🔍 错误分析:"
        
        logs=$(docker logs story-app-backend 2>&1)
        
        if echo "$logs" | grep -i "error" > /dev/null; then
            echo -e "${RED}❌ 发现错误信息${NC}"
            echo "$logs" | grep -i "error" | tail -3
        fi
        
        if echo "$logs" | grep -i "ECONNREFUSED" > /dev/null; then
            echo -e "${RED}❌ 数据库连接被拒绝${NC}"
        fi
        
        if echo "$logs" | grep -i "timeout" > /dev/null; then
            echo -e "${RED}❌ 连接超时${NC}"
        fi
        
        if echo "$logs" | grep -i "listening" > /dev/null; then
            echo -e "${GREEN}✅ 应用正在监听端口${NC}"
        fi
        
        if echo "$logs" | grep -i "ready" > /dev/null; then
            echo -e "${GREEN}✅ 应用已就绪${NC}"
        fi
    else
        echo -e "${RED}❌ 应用容器未运行${NC}"
    fi
    echo ""
}

# 检查网络连接
check_network() {
    echo -e "${BLUE}4. 检查网络连接${NC}"
    echo "================================"
    
    # 检查容器内部端口
    if docker ps -q -f name=story-app-backend | grep -q .; then
        echo "🔍 检查应用容器内部端口:"
        docker exec story-app-backend netstat -tuln 2>/dev/null || echo "netstat 不可用"
        
        echo ""
        echo "🔍 检查应用容器内进程:"
        docker exec story-app-backend ps aux 2>/dev/null || echo "ps 不可用"
        
        echo ""
        echo "🔍 测试容器内部健康检查端点:"
        if docker exec story-app-backend wget -q --spider http://localhost:3000/health 2>/dev/null; then
            echo -e "${GREEN}✅ 容器内部健康检查成功${NC}"
        else
            echo -e "${RED}❌ 容器内部健康检查失败${NC}"
            
            # 尝试检查3000端口是否在监听
            if docker exec story-app-backend netstat -tuln 2>/dev/null | grep ":3000"; then
                echo -e "${YELLOW}⚠️ 端口3000在监听，但健康检查端点不可用${NC}"
            else
                echo -e "${RED}❌ 端口3000未监听${NC}"
            fi
        fi
    fi
    echo ""
}

# 检查应用配置
check_app_config() {
    echo -e "${BLUE}5. 检查应用配置${NC}"
    echo "================================"
    
    # 检查环境变量文件
    if [ -f ".env.production" ]; then
        echo -e "${GREEN}✅ .env.production 文件存在${NC}"
        
        # 检查关键配置
        echo "🔍 关键配置项:"
        grep -E "^(PORT|DB_|REDIS_)" .env.production 2>/dev/null || echo "未找到关键配置"
    else
        echo -e "${RED}❌ .env.production 文件不存在${NC}"
    fi
    
    # 检查应用文件
    if [ -f "dist/main.js" ]; then
        echo -e "${GREEN}✅ 应用主文件存在${NC}"
        echo "📏 文件大小: $(du -h dist/main.js | cut -f1)"
    else
        echo -e "${RED}❌ dist/main.js 不存在${NC}"
    fi
    
    # 检查package.json
    if [ -f "package.json" ]; then
        echo -e "${GREEN}✅ package.json 存在${NC}"
        if grep -q "\"health\"" package.json; then
            echo -e "${GREEN}✅ 发现健康检查相关配置${NC}"
        fi
    fi
    echo ""
}

# 测试数据库连接
check_database() {
    echo -e "${BLUE}6. 检查数据库连接${NC}"
    echo "================================"
    
    # 加载环境变量
    if [ -f ".env.production" ]; then
        source .env.production
    fi
    
    # 测试PostgreSQL
    if docker ps -q -f name=story-app-postgres | grep -q .; then
        echo "🔍 测试PostgreSQL连接:"
        if docker exec story-app-postgres pg_isready -U ${DB_USERNAME:-postgres} > /dev/null 2>&1; then
            echo -e "${GREEN}✅ PostgreSQL 可连接${NC}"
        else
            echo -e "${RED}❌ PostgreSQL 连接失败${NC}"
        fi
    fi
    
    # 测试Redis
    if docker ps -q -f name=story-app-redis | grep -q .; then
        echo "🔍 测试Redis连接:"
        if docker exec story-app-redis redis-cli ping > /dev/null 2>&1; then
            echo -e "${GREEN}✅ Redis 可连接${NC}"
        else
            echo -e "${RED}❌ Redis 连接失败${NC}"
        fi
    fi
    echo ""
}

# 性能分析
performance_analysis() {
    echo -e "${BLUE}7. 性能分析${NC}"
    echo "================================"
    
    if docker ps -q -f name=story-app-backend | grep -q .; then
        echo "📊 应用容器资源使用:"
        docker stats story-app-backend --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}"
        
        echo ""
        echo "🕐 容器启动时间:"
        started_at=$(docker inspect story-app-backend --format '{{.State.StartedAt}}')
        echo "启动时间: $started_at"
    fi
    echo ""
}

# 提供解决建议
provide_solutions() {
    echo -e "${BLUE}8. 解决建议${NC}"
    echo "================================"
    
    echo "🚀 常见问题解决方案:"
    echo ""
    echo "1. 如果是架构不匹配导致的慢启动:"
    echo "   - 在Mac M4上重新下载x86_64镜像"
    echo "   - 使用: ./scripts/download-x86-images.sh"
    echo ""
    echo "2. 如果健康检查失败:"
    echo "   - 检查应用是否有 /health 端点"
    echo "   - 确认端口3000正确暴露"
    echo "   - 检查数据库连接配置"
    echo ""
    echo "3. 如果应用启动慢:"
    echo "   - 检查依赖安装是否正确"
    echo "   - 确认dist/main.js文件存在"
    echo "   - 查看应用日志排查错误"
    echo ""
    echo "4. 手动测试命令:"
    echo "   - 查看应用日志: docker logs story-app-backend -f"
    echo "   - 进入容器调试: docker exec -it story-app-backend sh"
    echo "   - 手动测试健康检查: curl http://localhost:3000/api/v1/healt"
    echo ""
}

# 主函数
main() {
    check_architecture
    check_containers
    check_app_logs
    check_network
    check_app_config
    check_database
    performance_analysis
    provide_solutions
    
    echo -e "${GREEN}🎉 诊断完成！${NC}"
    echo "请根据上述分析结果进行相应的修复。"
}

# 执行诊断
main 