import { Injectable, Logger, NotFoundException } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { EnhancedRedisService } from "../../common/services/enhanced-redis.service";
import * as OSS from "ali-oss";

export enum ImageType {
  AVATAR = "avatar",
  STORY_COVER = "story",
  CHARACTER = "character",
  STORY_CONTENT = "storyContent",
}

export interface ImageAccessRequest {
  imagePath: string;
  imageType: ImageType;
  userId: string;
  resourceOwnerId?: string; // 资源所有者ID（如故事作者、人物所有者等）
  storyId?: string;
  characterId?: string;
}

@Injectable()
export class ImageService {
  private readonly logger = new Logger(ImageService.name);
  private client: OSS;

  constructor(
    private configService: ConfigService,
    private redisService: EnhancedRedisService,
  ) {
    // OSS 配置初始化
    this.client = new OSS({
      region: this.configService.get<string>("OSS_REGION"),
      accessKeyId: this.configService.get<string>("OSS_ACCESS_KEY_ID")!,
      accessKeySecret: this.configService.get<string>("OSS_ACCESS_KEY_SECRET")!,
      bucket: this.configService.get<string>("OSS_BUCKET"),
    });
  }

  /**
   * 智能生成图片访问URL
   * 根据图片类型和权限级别，返回最优的访问方式：
   * - 公开图片：返回CDN公开URL，长期缓存
   * - 好友图片：返回CDN URL，中期缓存
   * - 私密图片：返回预签名URL，短期访问
   */
  async getOptimizedImageUrl(
    imagePath: string,
    imageType: ImageType,
    userId?: string,
    accessLevel: "PUBLIC" | "FRIENDS" | "PRIVATE" = "PUBLIC",
  ): Promise<string> {
    const cdnDomain =
      this.configService.get<string>("CDN_DOMAIN") ||
      this.configService.get<string>("OSS_BUCKET") +
        "." +
        this.configService.get<string>("OSS_REGION") +
        ".aliyuncs.com";

    switch (accessLevel) {
      case "PUBLIC":
        // 公开图片直接返回CDN URL，无需权限验证
        // 适用于：头像、公开故事封面等
        return `https://${cdnDomain}/${imagePath}`;

      case "FRIENDS":
        // 好友可见图片，使用中期缓存的CDN URL
        // 前端需要在请求头中携带token进行权限验证
        return `https://${cdnDomain}/${imagePath}`;

      case "PRIVATE":
        // 私密图片使用预签名URL，提供临时访问权限
        // 适用于：私密故事图片、敏感内容等
        return await this.generateTemporaryUrl(imagePath, 3600); // 1小时有效期

      default:
        return `https://${cdnDomain}/${imagePath}`;
    }
  }

  /**
   * 检查用户是否有权访问指定图片
   */
  async checkImageAccess(request: ImageAccessRequest): Promise<boolean> {
    const { imageType, userId, imagePath } = request;

    try {
      // 检查图片是否存在
      await this.client.head(imagePath);
    } catch (error) {
      if (
        error instanceof Error &&
        (error as Error & { code: string }).code === "NoSuchKey"
      ) {
        throw new NotFoundException("图片不存在");
      }
      throw error;
    }

    // 权限检查逻辑
    switch (imageType) {
      case ImageType.AVATAR:
        // 头像：所有登录用户都可以查看
        return true;

      case ImageType.STORY_COVER:
        // 故事封面：根据故事可见性级别判断
        return await this.checkStoryAccess(userId, request.storyId || "");

      case ImageType.CHARACTER:
        // 人物头像：根据人物和故事的可见性判断
        return await this.checkCharacterAccess(
          userId,
          request.characterId || "",
        );

      case ImageType.STORY_CONTENT:
        // 故事内容图片：需要有故事查看权限
        return await this.checkStoryContentAccess(
          userId,
          request.storyId || "",
        );

      default:
        return false;
    }
  }

  /**
   * 生成临时访问URL（预签名URL）
   */
  async generateTemporaryUrl(
    imagePath: string,
    expiresIn: number = 3600,
  ): Promise<string> {
    try {
      const signedUrl = this.client.signatureUrl(imagePath, {
        expires: expiresIn,
        method: "GET",
      });

      this.logger.log(`生成临时URL: ${imagePath}, 有效期: ${expiresIn}秒`);
      return signedUrl;
    } catch (error) {
      this.logger.error(
        `生成临时URL失败: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  /**
   * 直接代理返回图片流
   */
  async getImageStream(imagePath: string): Promise<Buffer> {
    try {
      const result = await this.client.get(imagePath);
      return result.content;
    } catch (error) {
      if (
        error instanceof Error &&
        (error as Error & { code: string }).code === "NoSuchKey"
      ) {
        throw new NotFoundException("图片不存在");
      }
      this.logger.error(
        `获取图片流失败: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  /**
   * 检查故事访问权限
   */
  private async checkStoryAccess(
    userId: string,
    storyId: string,
  ): Promise<boolean> {
    // TODO: 实现具体的故事权限检查逻辑
    // 这里需要查询数据库，根据故事的可见性级别和用户关系来判断

    // 示例逻辑：
    // 1. 查询故事信息和可见性级别
    // 2. 如果是私密故事，只有作者可以查看
    // 3. 如果是好友可见，检查用户关系
    // 4. 如果是公开故事，所有用户可以查看

    this.logger.log(`检查故事访问权限: userId=${userId}, storyId=${storyId}`);

    // 临时返回true，实际应用中需要实现具体逻辑
    return true;
  }

  /**
   * 检查人物访问权限
   */
  private async checkCharacterAccess(
    userId: string,
    characterId: string,
  ): Promise<boolean> {
    // TODO: 实现人物访问权限检查
    // 需要检查：
    // 1. 人物所属的故事是否可见
    // 2. 用户是否有查看该人物的权限

    this.logger.log(
      `检查人物访问权限: userId=${userId}, characterId=${characterId}`,
    );
    return true;
  }

  /**
   * 检查故事内容访问权限
   */
  private async checkStoryContentAccess(
    userId: string,
    storyId: string,
  ): Promise<boolean> {
    // TODO: 实现故事内容访问权限检查
    // 通常与故事访问权限相同，但可能有更严格的控制

    this.logger.log(
      `检查故事内容访问权限: userId=${userId}, storyId=${storyId}`,
    );
    return true;
  }

  /**
   * 解析图片路径获取相关信息
   */
  parseImagePath(imagePath: string): { type: ImageType; resourceId?: string } {
    // 根据路径规则解析图片类型和关联资源ID
    // 例如：ygs-app/avatars/user123/avatar.jpg
    //      ygs-app/stories/story456/cover.jpg

    if (imagePath.includes("/avatars/")) {
      return { type: ImageType.AVATAR };
    } else if (
      imagePath.includes("/stories/") &&
      imagePath.includes("/cover")
    ) {
      const storyId = this.extractStoryIdFromPath(imagePath);
      return { type: ImageType.STORY_COVER, resourceId: storyId };
    } else if (imagePath.includes("/characters/")) {
      const characterId = this.extractCharacterIdFromPath(imagePath);
      return { type: ImageType.CHARACTER, resourceId: characterId };
    } else if (imagePath.includes("/story-content/")) {
      const storyId = this.extractStoryIdFromPath(imagePath);
      return { type: ImageType.STORY_CONTENT, resourceId: storyId };
    }

    // 默认返回头像类型
    return { type: ImageType.AVATAR };
  }

  private extractStoryIdFromPath(path: string): string {
    // 从路径中提取故事ID
    const storiesMatch = path.match(/stories\/([^\/]+)/);
    if (storiesMatch) {
      return storiesMatch[1];
    }

    // 匹配story-content路径
    const contentMatch = path.match(/story-content\/([^\/]+)/);
    return contentMatch ? contentMatch[1] : "";
  }

  private extractCharacterIdFromPath(path: string): string {
    // 从路径中提取人物ID
    const match = path.match(/characters\/([^\/]+)/);
    return match ? match[1] : "";
  }

  async generateSignedUrl(
    objectName: string,
    method = "GET",
    expires = 3600,
  ): Promise<string> {
    try {
      // 生成签名URL
      const url = this.client.signatureUrl(objectName, {
        method: method as "GET" | "POST" | "PUT" | "DELETE",
        expires,
      });

      // 缓存URL (短时间缓存，避免重复生成)
      const cacheKey = `signed_url:${objectName}:${method}:${expires}`;
      await this.redisService.setCache(cacheKey, url, Math.min(expires, 300));

      return url;
    } catch (error) {
      throw new NotFoundException(`无法生成图片访问链接: ${error}`);
    }
  }
}
