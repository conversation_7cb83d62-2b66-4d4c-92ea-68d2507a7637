include: package:flutter_lints/flutter.yaml

analyzer:
  language:
    strict-casts: true
    strict-inference: true
    strict-raw-types: true
  
  errors:
    # 将部分警告提升为错误
    missing_required_param: error
    missing_return: error
    todo: ignore
    deprecated_member_use_from_same_package: ignore
    
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
    - "build/**"
    - "lib/generated/**"

linter:
  rules:
    # 基础规则
    prefer_const_constructors: true
    prefer_const_declarations: true
    prefer_const_literals_to_create_immutables: true
    prefer_final_fields: true
    prefer_final_locals: true
    prefer_is_empty: true
    prefer_is_not_empty: true
    
    # 命名规范
    camel_case_types: true
    library_names: true
    file_names: true
    non_constant_identifier_names: true
    constant_identifier_names: true
    
    # 导入规范
    avoid_relative_lib_imports: true
    always_use_package_imports: false
    
    # 文档规范
    public_member_api_docs: false
    
    # 代码风格
    always_declare_return_types: true
    always_put_control_body_on_new_line: true
    always_put_required_named_parameters_first: true
    always_require_non_null_named_parameters: true
    annotate_overrides: true
    avoid_empty_else: true
    avoid_init_to_null: true
    avoid_null_checks_in_equality_operators: true
    avoid_return_types_on_setters: true
    avoid_types_as_parameter_names: true
    avoid_unnecessary_containers: true
    curly_braces_in_flow_control_structures: true
    empty_catches: true
    empty_constructor_bodies: true
    library_prefixes: true
    no_duplicate_case_values: true
    null_closures: true
    prefer_adjacent_string_concatenation: true
    prefer_collection_literals: true
    prefer_conditional_assignment: true
    prefer_contains: true
    prefer_equal_for_default_values: true
    prefer_for_elements_to_map_fromIterable: true
    prefer_function_declarations_over_variables: true
    prefer_if_null_operators: true
    prefer_inlined_adds: true
    prefer_interpolation_to_compose_strings: true
    prefer_iterable_whereType: true
    prefer_null_aware_operators: true
    prefer_spread_collections: true
    prefer_typing_uninitialized_variables: true
    recursive_getters: true
    slash_for_doc_comments: true
    sort_constructors_first: false
    sort_unnamed_constructors_first: false
    type_init_formals: true
    unnecessary_brace_in_string_interps: true
    unnecessary_const: true
    unnecessary_getters_setters: true
    unnecessary_new: true
    unnecessary_null_in_if_null_operators: true
    unnecessary_string_escapes: true
    unnecessary_string_interpolations: true
    unnecessary_this: true
    use_full_hex_values_for_flutter_colors: true
    use_function_type_syntax_for_parameters: true
    use_rethrow_when_possible: true