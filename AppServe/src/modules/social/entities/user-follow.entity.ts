import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  <PERSON>inColumn,
  CreateDateColumn,
  Index,
  Unique,
} from "typeorm";
import { User } from "../../users/entities/user.entity";

export enum FollowStatus {
  ACTIVE = "active",
  BLOCKED = "blocked",
  MUTED = "muted",
}

/**
 * 用户关注关系实体
 * 记录用户之间的关注关系
 */
@Entity("user_follows")
@Unique(["followerId", "followingId"])
@Index(["followerId", "status"])
@Index(["followingId", "status"])
@Index(["createdAt"])
export class UserFollow {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({
    type: "uuid",
    comment: "关注者用户ID",
  })
  followerId: string;

  @Column({
    type: "uuid",
    comment: "被关注者用户ID",
  })
  followingId: string;

  @Column({
    type: "enum",
    enum: FollowStatus,
    default: FollowStatus.ACTIVE,
    comment: "关注状态",
  })
  status: FollowStatus;

  @Column({
    type: "boolean",
    default: false,
    comment: "是否为特别关注",
  })
  isSpecial: boolean;

  @Column({
    type: "jsonb",
    nullable: true,
    comment: "关注设置（通知偏好等）",
  })
  settings: {
    notifyOnPost?: boolean;
    notifyOnComment?: boolean;
    showInTimeline?: boolean;
  };

  @Column({
    type: "jsonb",
    nullable: true,
    comment: "扩展字段",
  })
  metadata: Record<string, unknown>;

  @CreateDateColumn({
    type: "timestamp with time zone",
    comment: "关注时间",
  })
  createdAt: Date;

  // 关联关系
  @ManyToOne(() => User, { eager: false })
  @JoinColumn({ name: "followerId" })
  follower: User;

  @ManyToOne(() => User, { eager: false })
  @JoinColumn({ name: "followingId" })
  following: User;
}
