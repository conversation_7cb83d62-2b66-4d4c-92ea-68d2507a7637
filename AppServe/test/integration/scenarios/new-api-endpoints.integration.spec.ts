/**
 * 新增API接口集成测试
 *
 * 测试新实现的企业级API接口：
 * 1. GET /characters/lighted - 获取点亮人物列表
 * 2. POST /users/verify - 用户验证
 *
 * 企业级测试标准：
 * - 完整HTTP请求响应验证
 * - 数据库状态验证
 * - 权限控制验证
 * - 性能基准验证
 * - 错误处理验证
 * - 边界条件测试
 */

import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import type { DataSource } from "typeorm";
import { getDataSourceToken } from "@nestjs/typeorm";
import request from "supertest";
import type { INestApplication } from "@nestjs/common";
import { AppModule } from "../../../src/app.module";
import { IntegrationTestDataFactory } from "../setup/database-setup";
import { User } from "../../../src/modules/users/entities/user.entity";
import type { Character } from "../../../src/modules/characters/entities/character.entity";
import { CharacterLighting } from "../../../src/modules/characters/entities/character-lighting.entity";
import type { Story } from "../../../src/modules/stories/entities/story.entity";
import { AuthService } from "../../../src/modules/auth/auth.service";
import { CharactersService } from "../../../src/modules/characters/characters.service";
import { UsersService } from "../../../src/modules/users/users.service";
import { JwtAuthGuard } from "../../../src/modules/auth/guards/jwt-auth.guard";
import { JwtService } from "@nestjs/jwt";

describe("New API Endpoints - Enterprise Integration Tests", () => {
  let app: INestApplication;
  let module: TestingModule;
  let dataSource: DataSource;
  let dataFactory: IntegrationTestDataFactory;
  let mockAuthService: AuthService;
  let mockCharactersService: CharactersService;
  let mockUsersService: UsersService;
  let jwtService: JwtService;

  // 测试数据
  let testUser: User;
  let otherUser: User;
  let lightedCharacter1: Character;
  let lightedCharacter2: Character;
  let testStory: Story;
  let userToken: string;
  let otherUserToken: string;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({
        canActivate: jest.fn((context) => {
          const request = context.switchToHttp().getRequest();
          const authHeader = request.headers.authorization;

          if (authHeader?.startsWith("Bearer ")) {
            const token = authHeader.substring(7);
            try {
              // 模拟解析JWT令牌并设置用户信息
              const payload = JSON.parse(
                Buffer.from(token.split(".")[1], "base64").toString(),
              );
              request.user = {
                id: payload.sub,
                phone: payload.phone,
              };
              return true;
            } catch (error) {
              return false;
            }
          }
          return false;
        }),
      })
      .compile();

    app = module.createNestApplication();
    await app.init();

    dataSource = module.get<DataSource>(getDataSourceToken());
    dataFactory = new IntegrationTestDataFactory(dataSource);
    mockAuthService = module.get<AuthService>(AuthService);
    mockCharactersService = module.get<CharactersService>(CharactersService);
    mockUsersService = module.get<UsersService>(UsersService);
    jwtService = module.get<JwtService>(JwtService);

    // 清理测试环境
    await dataFactory.clearDatabase();
  });

  afterAll(async () => {
    await dataFactory.clearDatabase();
    await app.close();
  });

  beforeEach(async () => {
    // 为每个测试创建干净的数据环境
    await dataFactory.clearTestData();
    await setupTestData();
  });

  /**
   * 设置测试数据
   */
  async function setupTestData() {
    // 创建测试用户
    testUser = await dataFactory.createUser({
      phone: "13800138001",
      nickname: "测试用户",
      email: "<EMAIL>",
      password: "hashedpassword",
    });

    otherUser = await dataFactory.createUser({
      phone: "13800138002",
      nickname: "其他用户",
      email: "<EMAIL>",
      password: "hashedpassword",
    });

    // 创建测试故事
    testStory = await dataFactory.createStory({
      title: "测试故事",
      content: "这是一个测试故事",
      authorId: otherUser.id,
      visibility: "public",
    });

    // 创建已点亮的人物
    lightedCharacter1 = await dataFactory.createCharacter({
      name: "已点亮人物1",
      creatorId: otherUser.id,
      relationship: "朋友",
    });

    lightedCharacter2 = await dataFactory.createCharacter({
      name: "已点亮人物2",
      creatorId: otherUser.id,
      relationship: "同事",
    });

    // 创建未点亮的人物
    const mockUnlightedCharacter = await dataFactory.createCharacter({
      name: "未点亮人物",
      creatorId: otherUser.id,
      relationship: "同学",
    });

    // 创建点亮关系
    await dataFactory.createCharacterLighting({
      characterId: lightedCharacter1.id,
      lighterUserId: testUser.id,
      confirmedAt: new Date(),
    });

    await dataFactory.createCharacterLighting({
      characterId: lightedCharacter2.id,
      lighterUserId: testUser.id,
      confirmedAt: new Date(),
    });

    // 生成企业级JWT令牌用于集成测试
    userToken = jwtService.sign({
      sub: testUser.id,
      phone: testUser.phone,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 3600, // 1小时后过期
    });

    otherUserToken = jwtService.sign({
      sub: otherUser.id,
      phone: otherUser.phone,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 3600, // 1小时后过期
    });
  }

  describe("🌟 GET /characters/lighted - 获取点亮人物列表", () => {
    it("should return paginated lighted characters for authenticated user", async () => {
      const startTime = Date.now();

      const response = await request(app.getHttpServer())
        .get("/characters/lighted")
        .set("Authorization", `Bearer ${userToken}`)
        .query({
          page: 1,
          pageSize: 10,
        })
        .expect(200);

      const endTime = Date.now();

      // 验证响应结构
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe("获取点亮人物列表成功");
      expect(response.body.data).toBeDefined();
      expect(response.body.data.items).toBeInstanceOf(Array);
      expect(response.body.data.pagination).toBeDefined();

      // 验证分页信息
      expect(response.body.data.pagination.page).toBe(1);
      expect(response.body.data.pagination.pageSize).toBe(10);
      expect(response.body.data.pagination.total).toBeGreaterThanOrEqual(2);

      // 验证返回的人物数据
      const lightedCharacters = response.body.data.items;
      expect(lightedCharacters.length).toBeGreaterThanOrEqual(2);

      // 验证人物数据结构
      const firstCharacter = lightedCharacters[0];
      expect(firstCharacter.id).toBeDefined();
      expect(firstCharacter.name).toBeDefined();
      expect(firstCharacter.relationship).toBeDefined();
      expect(firstCharacter.isLighted).toBe(true);
      expect(firstCharacter.creator).toBeDefined();
      expect(firstCharacter.creator.id).toBeDefined();
      expect(firstCharacter.creator.nickname).toBeDefined();

      // 验证只返回当前用户点亮的人物
      for (const character of lightedCharacters) {
        expect(character.isLighted).toBe(true);

        // 验证数据库中确实存在该点亮关系
        const lighting = await dataSource
          .getRepository(CharacterLighting)
          .findOne({
            where: {
              characterId: character.id,
              lighterUserId: testUser.id,
            },
          });
        expect(lighting).toBeDefined();
      }

      // 性能验证：响应时间应在500ms内
      expect(endTime - startTime).toBeLessThan(500);
    });

    it("should support filtering by character name", async () => {
      const response = await request(app.getHttpServer())
        .get("/characters/lighted")
        .set("Authorization", `Bearer ${userToken}`)
        .query({
          search: "已点亮人物1",
          page: 1,
          pageSize: 10,
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.items.length).toBe(1);
      expect(response.body.data.items[0].name).toBe("已点亮人物1");
    });

    it("should support sorting by lighting time", async () => {
      const response = await request(app.getHttpServer())
        .get("/characters/lighted")
        .set("Authorization", `Bearer ${userToken}`)
        .query({
          sortBy: "lightedAt",
          sortOrder: "DESC",
          page: 1,
          pageSize: 10,
        })
        .expect(200);

      expect(response.body.success).toBe(true);

      const items = response.body.data.items;
      if (items.length > 1) {
        // 验证排序结果
        const firstDate = new Date(items[0].lightedAt);
        const secondDate = new Date(items[1].lightedAt);
        expect(firstDate.getTime()).toBeGreaterThanOrEqual(
          secondDate.getTime(),
        );
      }
    });

    it("should return empty list for user with no lighted characters", async () => {
      const response = await request(app.getHttpServer())
        .get("/characters/lighted")
        .set("Authorization", `Bearer ${otherUserToken}`)
        .query({
          page: 1,
          pageSize: 10,
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.items).toEqual([]);
      expect(response.body.data.pagination.total).toBe(0);
    });

    it("should handle pagination correctly", async () => {
      // 测试第一页
      const page1Response = await request(app.getHttpServer())
        .get("/characters/lighted")
        .set("Authorization", `Bearer ${userToken}`)
        .query({
          page: 1,
          pageSize: 1,
        })
        .expect(200);

      expect(page1Response.body.data.items.length).toBe(1);
      expect(page1Response.body.data.pagination.page).toBe(1);

      // 测试第二页
      const page2Response = await request(app.getHttpServer())
        .get("/characters/lighted")
        .set("Authorization", `Bearer ${userToken}`)
        .query({
          page: 2,
          pageSize: 1,
        })
        .expect(200);

      expect(page2Response.body.data.items.length).toBe(1);
      expect(page2Response.body.data.pagination.page).toBe(2);

      // 验证两页返回的是不同的人物
      expect(page1Response.body.data.items[0].id).not.toBe(
        page2Response.body.data.items[0].id,
      );
    });

    it("should require authentication", async () => {
      await request(app.getHttpServer()).get("/characters/lighted").expect(401);
    });

    it("should handle invalid page parameters gracefully", async () => {
      // 测试无效页码
      const response1 = await request(app.getHttpServer())
        .get("/characters/lighted")
        .set("Authorization", `Bearer ${userToken}`)
        .query({
          page: 0,
          pageSize: 10,
        })
        .expect(400);

      expect(response1.body.success).toBe(false);

      // 测试无效页面大小
      const response2 = await request(app.getHttpServer())
        .get("/characters/lighted")
        .set("Authorization", `Bearer ${userToken}`)
        .query({
          page: 1,
          pageSize: 0,
        })
        .expect(400);

      expect(response2.body.success).toBe(false);
    });
  });

  describe("🔐 POST /users/verify - 用户验证", () => {
    it("should verify phone number successfully", async () => {
      const startTime = Date.now();

      const response = await request(app.getHttpServer())
        .post("/users/verify")
        .set("Authorization", `Bearer ${userToken}`)
        .send({
          verificationType: "phone",
          phoneNumber: "13800138001",
          verificationCode: "123456",
        })
        .expect(200);

      const endTime = Date.now();

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe("手机验证成功");
      expect(response.body.data.verified).toBe(true);
      expect(response.body.data.verificationType).toBe("phone");

      // 性能验证：响应时间应在1000ms内
      expect(endTime - startTime).toBeLessThan(1000);
    });

    it("should verify email successfully", async () => {
      const response = await request(app.getHttpServer())
        .post("/users/verify")
        .set("Authorization", `Bearer ${userToken}`)
        .send({
          verificationType: "email",
          email: "<EMAIL>",
          verificationCode: "654321",
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe("邮箱验证成功");
      expect(response.body.data.verified).toBe(true);
      expect(response.body.data.verificationType).toBe("email");
    });

    it("should verify identity successfully", async () => {
      const response = await request(app.getHttpServer())
        .post("/users/verify")
        .set("Authorization", `Bearer ${userToken}`)
        .send({
          verificationType: "identity",
          realName: "张三",
          idCardNumber: "110101199001011234",
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe("身份验证成功");
      expect(response.body.data.verified).toBe(true);
      expect(response.body.data.verificationType).toBe("identity");
    });

    it("should verify security questions successfully", async () => {
      const response = await request(app.getHttpServer())
        .post("/users/verify")
        .set("Authorization", `Bearer ${userToken}`)
        .send({
          verificationType: "security",
          securityQuestions: [
            {
              question: "您的出生地是？",
              answer: "北京",
            },
            {
              question: "您母亲的姓名是？",
              answer: "李四",
            },
          ],
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe("安全验证成功");
      expect(response.body.data.verified).toBe(true);
      expect(response.body.data.verificationType).toBe("security");
    });

    it("should reject invalid phone number format", async () => {
      const response = await request(app.getHttpServer())
        .post("/users/verify")
        .set("Authorization", `Bearer ${userToken}`)
        .send({
          verificationType: "phone",
          phoneNumber: "invalid-phone",
          verificationCode: "123456",
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain("手机号格式不正确");
    });

    it("should reject invalid email format", async () => {
      const response = await request(app.getHttpServer())
        .post("/users/verify")
        .set("Authorization", `Bearer ${userToken}`)
        .send({
          verificationType: "email",
          email: "invalid-email",
          verificationCode: "123456",
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain("邮箱格式不正确");
    });

    it("should reject invalid ID card number format", async () => {
      const response = await request(app.getHttpServer())
        .post("/users/verify")
        .set("Authorization", `Bearer ${userToken}`)
        .send({
          verificationType: "identity",
          realName: "张三",
          idCardNumber: "invalid-id",
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain("身份证号格式不正确");
    });

    it("should reject insufficient security questions", async () => {
      const response = await request(app.getHttpServer())
        .post("/users/verify")
        .set("Authorization", `Bearer ${userToken}`)
        .send({
          verificationType: "security",
          securityQuestions: [
            {
              question: "您的出生地是？",
              answer: "北京",
            },
            // 缺少第二个安全问题
          ],
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain("至少需要2个安全问题");
    });

    it("should handle unsupported verification type", async () => {
      const response = await request(app.getHttpServer())
        .post("/users/verify")
        .set("Authorization", `Bearer ${userToken}`)
        .send({
          verificationType: "unsupported",
          data: "test",
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain("不支持的验证类型");
    });

    it("should require authentication", async () => {
      await request(app.getHttpServer())
        .post("/users/verify")
        .send({
          verificationType: "phone",
          phoneNumber: "13800138001",
          verificationCode: "123456",
        })
        .expect(401);
    });

    it("should handle missing required fields", async () => {
      // 缺少手机号
      const response1 = await request(app.getHttpServer())
        .post("/users/verify")
        .set("Authorization", `Bearer ${userToken}`)
        .send({
          verificationType: "phone",
          verificationCode: "123456",
        })
        .expect(400);

      expect(response1.body.success).toBe(false);

      // 缺少验证码
      const response2 = await request(app.getHttpServer())
        .post("/users/verify")
        .set("Authorization", `Bearer ${userToken}`)
        .send({
          verificationType: "phone",
          phoneNumber: "13800138001",
        })
        .expect(400);

      expect(response2.body.success).toBe(false);
    });

    it("should update user verification status in database", async () => {
      await request(app.getHttpServer())
        .post("/users/verify")
        .set("Authorization", `Bearer ${userToken}`)
        .send({
          verificationType: "phone",
          phoneNumber: "13800138001",
          verificationCode: "123456",
        })
        .expect(200);

      // 验证数据库中用户状态已更新
      const updatedUser = await dataSource
        .getRepository(User)
        .findOne({ where: { id: testUser.id } });

      expect(updatedUser).toBeDefined();
      expect(updatedUser?.isPhoneVerified).toBe(true);
      expect(updatedUser?.updatedAt.getTime()).toBeGreaterThan(
        testUser.updatedAt.getTime(),
      );
    });
  });

  describe("🔒 权限和安全验证", () => {
    it("should prevent access with expired tokens", async () => {
      // 使用过期或无效的令牌
      const expiredToken =
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c";

      await request(app.getHttpServer())
        .get("/characters/lighted")
        .set("Authorization", `Bearer ${expiredToken}`)
        .expect(401);

      await request(app.getHttpServer())
        .post("/users/verify")
        .set("Authorization", `Bearer ${expiredToken}`)
        .send({
          verificationType: "phone",
          phoneNumber: "13800138001",
          verificationCode: "123456",
        })
        .expect(401);
    });

    it("should validate request rate limiting", async () => {
      // 快速连续发送多个验证请求
      const requests = [];
      for (let i = 0; i < 10; i++) {
        requests.push(
          request(app.getHttpServer())
            .post("/users/verify")
            .set("Authorization", `Bearer ${userToken}`)
            .send({
              verificationType: "phone",
              phoneNumber: "13800138001",
              verificationCode: "123456",
            }),
        );
      }

      const responses = await Promise.all(requests);

      // 验证部分请求被速率限制拒绝
      const rateLimitedCount = responses.filter((r) => r.status === 429).length;

      expect(rateLimitedCount).toBeGreaterThan(0);
    });
  });

  describe("📊 性能和负载测试", () => {
    it("should handle concurrent requests efficiently", async () => {
      const startTime = Date.now();

      // 并发发送多个请求
      const concurrentRequests = [];
      for (let i = 0; i < 5; i++) {
        concurrentRequests.push(
          request(app.getHttpServer())
            .get("/characters/lighted")
            .set("Authorization", `Bearer ${userToken}`)
            .query({
              page: 1,
              pageSize: 10,
            }),
        );
      }

      const responses = await Promise.all(concurrentRequests);
      const endTime = Date.now();

      // 验证所有请求都成功
      responses.forEach((response) => {
        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
      });

      // 验证并发处理性能
      expect(endTime - startTime).toBeLessThan(2000);
    });

    it("should handle large datasets efficiently", async () => {
      // 创建更多测试数据
      const additionalCharacters = [];
      for (let i = 0; i < 50; i++) {
        const character = await dataFactory.createCharacter({
          name: `批量人物${i}`,
          creatorId: otherUser.id,
          relationship: "测试关系",
        });

        await dataFactory.createCharacterLighting({
          characterId: character.id,
          lighterUserId: testUser.id,
          confirmedAt: new Date(),
        });

        additionalCharacters.push(character);
      }

      const startTime = Date.now();

      const response = await request(app.getHttpServer())
        .get("/characters/lighted")
        .set("Authorization", `Bearer ${userToken}`)
        .query({
          page: 1,
          pageSize: 20,
        })
        .expect(200);

      const endTime = Date.now();

      expect(response.body.data.items.length).toBe(20);
      expect(response.body.data.pagination.total).toBeGreaterThanOrEqual(50);

      // 大数据集查询应在合理时间内完成
      expect(endTime - startTime).toBeLessThan(1000);
    });
  });
});
