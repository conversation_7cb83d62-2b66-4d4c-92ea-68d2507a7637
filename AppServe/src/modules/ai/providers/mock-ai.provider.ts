import { Injectable, Logger } from "@nestjs/common";
import type {
  AIProvider,
  AIGenerateRequest,
  AIGenerateResponse,
} from "../interfaces/ai-provider.interface";

/**
 * 模拟AI服务提供商实现
 * 用于开发阶段的功能测试，后续可替换为真实的AI服务提供商
 *
 * 特点：
 * - 模拟真实AI服务的响应时间和数据结构
 * - 提供多样化的模拟数据
 * - 支持错误模拟和故障测试
 * - 完全离线运行，无需外部依赖
 */
@Injectable()
export class MockAIProvider implements AIProvider {
  private readonly logger = new Logger(MockAIProvider.name);

  readonly name = "Mock AI Provider";
  readonly version = "1.0.0";

  constructor() {
    this.logger.log("Mock AI Provider 已初始化");
  }

  /**
   * 检查服务可用性
   */
  async isAvailable(): Promise<boolean> {
    // 模拟网络检查延迟
    await new Promise((resolve) => setTimeout(resolve, 100));
    return true;
  }

  /**
   * 生成文本内容
   */
  async generateText(request: AIGenerateRequest): Promise<AIGenerateResponse> {
    this.logger.debug(`生成文本请求: ${request.prompt}`);

    // 模拟API调用延迟
    await new Promise((resolve) =>
      setTimeout(resolve, 1000 + Math.random() * 2000),
    );

    // 模拟生成内容
    const content = this.mockGenerateContent(request.prompt);

    // 模拟令牌计算
    const promptTokens = this.estimateTokens(request.prompt);
    const completionTokens = this.estimateTokens(content);

    return {
      content,
      promptTokens,
      completionTokens,
      totalTokens: promptTokens + completionTokens,
      model: request.model || "mock-model-v1",
      timestamp: new Date(),
    };
  }

  /**
   * 生成标题
   */
  async generateTitle(keywords: string[]): Promise<string> {
    this.logger.debug(`生成标题请求，关键词: ${keywords.join(", ")}`);

    // 模拟API调用延迟
    await new Promise((resolve) =>
      setTimeout(resolve, 800 + Math.random() * 1200),
    );

    return this.mockGenerateTitle(keywords);
  }

  /**
   * 生成故事内容
   */
  async generateStoryContent(prompt: string): Promise<string> {
    this.logger.debug(`生成故事内容请求: ${prompt}`);

    // 模拟API调用延迟
    await new Promise((resolve) =>
      setTimeout(resolve, 1500 + Math.random() * 2500),
    );

    return this.mockGenerateStoryContent(prompt);
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<{
    status: "healthy" | "degraded" | "unhealthy";
    latency: number;
    message?: string;
  }> {
    const startTime = Date.now();

    // 模拟健康检查延迟
    await new Promise((resolve) =>
      setTimeout(resolve, 50 + Math.random() * 150),
    );

    const latency = Date.now() - startTime;

    // 模拟偶发的服务降级
    const isHealthy = Math.random() > 0.05; // 5%概率服务降级

    return {
      status: isHealthy ? "healthy" : "degraded",
      latency,
      message: isHealthy ? "Mock AI服务运行正常" : "Mock AI服务性能降级",
    };
  }

  // 私有方法：模拟生成逻辑

  /**
   * 模拟内容生成
   */
  private mockGenerateContent(prompt: string): string {
    const templates = [
      `关于"${prompt}"的故事开始了。这是一个温暖而富有意义的故事，充满了成长与感悟。\\n\\n在那个特别的日子里，一切都显得那么美好。阳光透过窗户洒在房间里，带来了无限的希望和可能。故事的主人公正准备踏上一段新的旅程，心中既有兴奋，也有一丝紧张。\\n\\n这个故事想要告诉我们的是，每个人都有自己独特的价值和意义，只要我们勇敢地去追寻，就一定能找到属于自己的答案。`,

      `"${prompt}"让我想起了一个关于友谊和勇气的故事。\\n\\n在一个宁静的小镇上，生活着一群善良的人们。他们相互扶持，共同面对生活中的挑战。每当遇到困难时，他们总是能够团结一心，用智慧和勇气解决问题。\\n\\n这个故事告诉我们，真正的力量不在于个人的能力，而在于我们彼此之间的连接和支持。当我们携手并肩时，没有什么困难是无法克服的。`,

      `有一个关于"${prompt}"的传说，流传在人们的心中。\\n\\n传说中，每个人的心里都有一颗特殊的种子，只要用爱和耐心去浇灌，就能开出最美丽的花朵。这些花朵不仅美丽，还能散发出温暖的光芒，照亮周围的人们。\\n\\n故事的结尾告诉我们，最珍贵的不是外在的财富，而是内心的充实和与他人分享的快乐。当我们学会给予时，我们也同时收获了更多的幸福。`,
    ];

    return templates[Math.floor(Math.random() * templates.length)];
  }

  /**
   * 模拟标题生成
   */
  private mockGenerateTitle(keywords: string[]): string {
    const primaryKeyword = keywords[0] || "故事";
    const secondaryKeyword = keywords[1] || "回忆";

    const titlePatterns = [
      `${primaryKeyword}的故事`,
      `关于${primaryKeyword}的回忆`,
      `${primaryKeyword}与${secondaryKeyword}`,
      `那些年的${primaryKeyword}`,
      `${primaryKeyword}的奇遇记`,
      `寻找${primaryKeyword}`,
      `${primaryKeyword}的秘密`,
      `${primaryKeyword}之歌`,
      `我的${primaryKeyword}时光`,
      `${primaryKeyword}的温暖记忆`,
      `关于${primaryKeyword}的感悟`,
      `${primaryKeyword}的美好时光`,
    ];

    return titlePatterns[Math.floor(Math.random() * titlePatterns.length)];
  }

  /**
   * 模拟故事内容生成
   */
  private mockGenerateStoryContent(prompt: string): string {
    const storyTypes = [
      {
        opening: `在一个阳光明媚的下午，${prompt}的故事开始了。`,
        body: `这是一个关于成长、友谊和勇气的故事，充满了温暖的回忆和深刻的感悟。\\n\\n每当想起那些日子，心中总是涌起一阵暖流。那时的我们单纯而美好，对未来充满期待。我们一起经历了许多难忘的时刻，有欢笑也有泪水，有相遇也有离别。\\n\\n但正是这些经历，让我们变得更加坚强和成熟。`,
        ending: `现在回想起来，那段时光真的很珍贵。它教会了我们什么是真正的友谊，什么是勇敢面对困难的勇气。这些美好的回忆将永远伴随我们，成为我们前进路上的明灯。`,
      },
      {
        opening: `从前有个${prompt}，生活在一个充满奇迹的世界里。`,
        body: `这个世界有着独特的规则和美丽的风景，而我们的主人公正要开始一段不平凡的旅程。\\n\\n旅程中，遇到了各种各样的人和事。有善良的朋友给予帮助，有智慧的长者提供指导，也有需要克服的挑战和困难。每一次经历都是成长的机会，每一次选择都在塑造着未来。\\n\\n渐渐地，主人公明白了一个道理：真正的财富不在于拥有多少，而在于能够给予多少。`,
        ending: `故事的结尾，主人公找到了自己真正想要的东西。那不是金银财宝，而是内心的平静和与他人分享快乐的能力。这个故事告诉我们，最重要的不是终点，而是沿途的风景和陪伴我们的人。`,
      },
    ];

    const selectedStory =
      storyTypes[Math.floor(Math.random() * storyTypes.length)];

    return `${selectedStory.opening}\\n\\n${selectedStory.body}\\n\\n${selectedStory.ending}`;
  }

  /**
   * 估算令牌数量
   */
  private estimateTokens(text: string): number {
    // 简单的中文令牌估算：1个中文字符约等于1.5个令牌
    const chineseChars = (text.match(/[\u4e00-\u9fa5]/g) || []).length;
    const englishWords = text
      .split(/\s+/)
      .filter((word) => /[a-zA-Z]/.test(word)).length;

    return Math.ceil(chineseChars * 1.5 + englishWords);
  }
}
