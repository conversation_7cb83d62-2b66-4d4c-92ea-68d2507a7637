import { Module, Global } from "@nestjs/common";
// import { RedisService } from './services/redis.service'; // 临时注释等待迁移
import { EnhancedRedisService } from "./services/enhanced-redis.service";
import { DeviceFingerprintService } from "./services/device-fingerprint.service";
import { VerificationSecurityService } from "./services/verification-security.service";

/**
 * 公共模块
 * 提供企业级基础服务
 * - EnhancedRedisService: Redis缓存服务
 * - DeviceFingerprintService: 设备指纹验证服务
 * - VerificationSecurityService: 验证安全服务
 */
@Global()
@Module({
  providers: [
    // RedisService, // 临时注释等待迁移
    EnhancedRedisService, // 新的官方服务
    DeviceFingerprintService, // 设备指纹服务
    VerificationSecurityService, // 验证安全服务
  ],
  exports: [
    // RedisService, // 临时注释等待迁移
    EnhancedRedisService,
    DeviceFingerprintService,
    VerificationSecurityService,
  ],
})
export class CommonModule {}
