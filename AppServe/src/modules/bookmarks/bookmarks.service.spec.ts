/* eslint-disable @typescript-eslint/no-explicit-any, @typescript-eslint/no-unused-vars */
import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import {
  NotFoundException,
  BadRequestException,
  ConflictException,
} from "@nestjs/common";
import type { Repository } from "typeorm";

import { BookmarksService } from "./bookmarks.service";
import {
  Bookmark,
  BookmarkCategory,
  BookmarkStatus,
} from "./entities/bookmark.entity";
import { Story } from "../stories/entities/story.entity";
import { User } from "../users/entities/user.entity";
import type {
  CreateBookmarkDto,
  UpdateBookmarkCategoryDto,
  BookmarkQueryDto,
} from "./dto";
import { BookmarkSortBy } from "./dto";

describe("BookmarksService - 企业级单元测试", () => {
  let service: BookmarksService;
  let mockBookmarkRepository: jest.Mocked<Repository<Bookmark>>;
  let mockStoryRepository: jest.Mocked<Repository<Story>>;
  let mockUserRepository: jest.Mocked<Repository<User>>;

  // 测试数据工厂
  const mockUser = {
    id: "user-001",
    username: "testuser",
    nickname: "测试用户",
    isActive: true,
  };

  const mockStory = {
    id: "story-001",
    title: "测试故事",
    content: "这是一个测试故事的内容",
    coverImageUrl: "https://example.com/cover.jpg",
    userId: "author-001",
    user: { id: "author-001", nickname: "故事作者" },
    status: "published",
    permissionLevel: "public",
  };

  const mockBookmark = {
    id: "bookmark-001",
    userId: "user-001",
    storyId: "story-001",
    category: BookmarkCategory.DEFAULT,
    customCategoryName: null,
    status: BookmarkStatus.ACTIVE,
    note: "这是一个很棒的故事",
    storySnapshot: {
      title: "测试故事",
      summary: "这是一个测试故事的内容",
      coverImage: "https://example.com/cover.jpg",
      authorName: "故事作者",
    },
    metadata: { source: "web", device: "desktop" },
    createdAt: new Date(),
    updatedAt: new Date(),
    story: mockStory,
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BookmarksService,
        {
          provide: getRepositoryToken(Bookmark),
          useValue: {
            findOne: jest.fn(),
            save: jest.fn(),
            create: jest.fn(),
            remove: jest.fn(),
            createQueryBuilder: jest.fn().mockReturnValue({
              leftJoinAndSelect: jest.fn().mockReturnThis(),
              where: jest.fn().mockReturnThis(),
              andWhere: jest.fn().mockReturnThis(),
              orderBy: jest.fn().mockReturnThis(),
              skip: jest.fn().mockReturnThis(),
              take: jest.fn().mockReturnThis(),
              getCount: jest.fn(),
              getMany: jest.fn(),
              select: jest.fn().mockReturnThis(),
              setParameter: jest.fn().mockReturnThis(),
              getRawOne: jest.fn(),
              getRawMany: jest.fn(),
              groupBy: jest.fn().mockReturnThis(),
            }),
            update: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(Story),
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(User),
          useValue: {
            findOne: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<BookmarksService>(BookmarksService);
    mockBookmarkRepository = module.get(getRepositoryToken(Bookmark));
    mockStoryRepository = module.get(getRepositoryToken(Story));
    mockUserRepository = module.get(getRepositoryToken(User));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("createBookmark - 创建收藏", () => {
    const createBookmarkDto: CreateBookmarkDto = {
      storyId: "story-001",
      category: BookmarkCategory.FAVORITE,
      note: "很棒的故事",
      metadata: { source: "mobile", device: "ios" },
    };

    it("应该成功创建收藏", async () => {
      // Arrange
      mockStoryRepository.findOne.mockResolvedValue(mockStory as any);
      mockBookmarkRepository.findOne.mockResolvedValue(null);
      mockBookmarkRepository.create.mockReturnValue(mockBookmark as any);
      mockBookmarkRepository.save.mockResolvedValue(mockBookmark as any);

      // Act
      const result = await service.createBookmark(
        createBookmarkDto,
        "user-001",
      );

      // Assert
      expect(mockStoryRepository.findOne).toHaveBeenCalledWith({
        where: { id: "story-001" },
        relations: ["author"],
      });
      expect(mockBookmarkRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: "user-001",
          storyId: "story-001",
          category: BookmarkCategory.FAVORITE,
          note: "很棒的故事",
          storySnapshot: expect.any(Object),
        }),
      );
      expect(result).toEqual(
        expect.objectContaining({
          id: "bookmark-001",
          category: BookmarkCategory.FAVORITE,
        }),
      );
    });

    it("当故事不存在时应该抛出NotFoundException", async () => {
      // Arrange
      mockStoryRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.createBookmark(createBookmarkDto, "user-001"),
      ).rejects.toThrow(NotFoundException);
      expect(mockStoryRepository.findOne).toHaveBeenCalled();
    });

    it("当已经收藏过该故事时应该抛出ConflictException", async () => {
      // Arrange
      mockStoryRepository.findOne.mockResolvedValue(mockStory as any);
      mockBookmarkRepository.findOne.mockResolvedValue({
        ...mockBookmark,
        status: BookmarkStatus.ACTIVE,
      } as any);

      // Act & Assert
      await expect(
        service.createBookmark(createBookmarkDto, "user-001"),
      ).rejects.toThrow(ConflictException);
    });

    it("应该重新激活已删除的收藏", async () => {
      // Arrange
      const deletedBookmark = {
        ...mockBookmark,
        status: BookmarkStatus.ARCHIVED,
      };
      mockStoryRepository.findOne.mockResolvedValue(mockStory as any);
      mockBookmarkRepository.findOne.mockResolvedValue(deletedBookmark as any);
      mockBookmarkRepository.save.mockResolvedValue({
        ...deletedBookmark,
        status: BookmarkStatus.ACTIVE,
      } as any);

      // Act
      const result = await service.createBookmark(
        createBookmarkDto,
        "user-001",
      );

      // Assert
      expect(result.status).toBe(BookmarkStatus.ACTIVE);
      expect(mockBookmarkRepository.save).toHaveBeenCalled();
    });

    it("使用自定义分类时应该要求提供分类名称", async () => {
      // Arrange
      const customCategoryDto = {
        ...createBookmarkDto,
        category: BookmarkCategory.CUSTOM,
        customCategoryName: undefined,
      };
      mockStoryRepository.findOne.mockResolvedValue(mockStory as any);
      mockBookmarkRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.createBookmark(customCategoryDto, "user-001"),
      ).rejects.toThrow(BadRequestException);
    });

    it("应该正确创建故事快照", async () => {
      // Arrange
      mockStoryRepository.findOne.mockResolvedValue(mockStory as any);
      mockBookmarkRepository.findOne.mockResolvedValue(null);
      mockBookmarkRepository.create.mockReturnValue(mockBookmark as any);
      mockBookmarkRepository.save.mockResolvedValue(mockBookmark as any);

      // Act
      await service.createBookmark(createBookmarkDto, "user-001");

      // Assert
      expect(mockBookmarkRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          storySnapshot: {
            title: "测试故事",
            summary: "这是一个测试故事的内容",
            coverImage: "https://example.com/cover.jpg",
            authorName: "故事作者",
          },
        }),
      );
    });
  });

  describe("getUserBookmarks - 获取用户收藏列表", () => {
    const queryDto: BookmarkQueryDto = {
      page: 1,
      limit: 10,
      sortBy: BookmarkSortBy.CREATED_DESC,
    };

    it("应该成功获取用户收藏列表", async () => {
      // Arrange
      const mockQueryBuilder = mockBookmarkRepository.createQueryBuilder();
      (mockQueryBuilder.getCount as jest.Mock).mockResolvedValue(1);
      (mockQueryBuilder.getMany as jest.Mock).mockResolvedValue([mockBookmark]);

      // Act
      const result = await service.getUserBookmarks("user-001", queryDto);

      // Assert
      expect(mockBookmarkRepository.createQueryBuilder).toHaveBeenCalled();
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledTimes(2);
      expect(result).toEqual({
        data: expect.arrayContaining([
          expect.objectContaining({
            id: "bookmark-001",
          }),
        ]),
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      });
    });

    it("应该正确应用分类筛选", async () => {
      // Arrange
      const queryWithCategory = {
        ...queryDto,
        category: BookmarkCategory.FAVORITE,
      };
      const mockQueryBuilder = mockBookmarkRepository.createQueryBuilder();

      // Act
      await service.getUserBookmarks("user-001", queryWithCategory);

      // Assert
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "bookmark.category = :category",
        { category: BookmarkCategory.FAVORITE },
      );
    });

    it("应该正确应用关键词搜索", async () => {
      // Arrange
      const queryWithKeyword = { ...queryDto, keyword: "测试" };
      const mockQueryBuilder = mockBookmarkRepository.createQueryBuilder();

      // Act
      await service.getUserBookmarks("user-001", queryWithKeyword);

      // Assert
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "(bookmark.storySnapshot->>'title' ILIKE :keyword OR bookmark.note ILIKE :keyword)",
        { keyword: "%测试%" },
      );
    });

    it("应该正确应用排序", async () => {
      // Arrange
      const sortOptions = [
        BookmarkSortBy.CREATED_DESC,
        BookmarkSortBy.CREATED_ASC,
        BookmarkSortBy.UPDATED_DESC,
        BookmarkSortBy.STORY_TITLE_ASC,
      ];

      for (const sortBy of sortOptions) {
        const queryWithSort = { ...queryDto, sortBy };
        const mockQueryBuilder = mockBookmarkRepository.createQueryBuilder();

        // Act
        await service.getUserBookmarks("user-001", queryWithSort);

        // Assert
        expect(mockQueryBuilder.orderBy).toHaveBeenCalled();
      }
    });
  });

  describe("getBookmarkDetail - 获取收藏详情", () => {
    it("应该成功获取收藏详情", async () => {
      // Arrange
      mockBookmarkRepository.findOne.mockResolvedValue(mockBookmark as any);

      // Act
      const result = await service.getBookmarkDetail(
        "bookmark-001",
        "user-001",
      );

      // Assert
      expect(mockBookmarkRepository.findOne).toHaveBeenCalledWith({
        where: { id: "bookmark-001", userId: "user-001" },
        relations: ["story", "story.user"],
      });
      expect(result).toEqual(
        expect.objectContaining({
          id: "bookmark-001",
          note: "这是一个很棒的故事",
        }),
      );
    });

    it("当收藏不存在时应该抛出NotFoundException", async () => {
      // Arrange
      mockBookmarkRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.getBookmarkDetail("non-existent", "user-001"),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe("updateBookmarkCategory - 更新收藏分类", () => {
    const updateDto: UpdateBookmarkCategoryDto = {
      category: BookmarkCategory.TO_READ,
      note: "更新后的备注",
    };

    it("应该成功更新收藏分类", async () => {
      // Arrange
      mockBookmarkRepository.findOne.mockResolvedValue(mockBookmark as any);
      const updatedBookmark = {
        ...mockBookmark,
        category: BookmarkCategory.TO_READ,
      };
      mockBookmarkRepository.save.mockResolvedValue(updatedBookmark as any);

      // Act
      const result = await service.updateBookmarkCategory(
        "bookmark-001",
        updateDto,
        "user-001",
      );

      // Assert
      expect(result.category).toBe(BookmarkCategory.TO_READ);
      expect(mockBookmarkRepository.save).toHaveBeenCalled();
    });

    it("当收藏不存在时应该抛出NotFoundException", async () => {
      // Arrange
      mockBookmarkRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.updateBookmarkCategory("non-existent", updateDto, "user-001"),
      ).rejects.toThrow(NotFoundException);
    });

    it("使用自定义分类时应该验证分类名称", async () => {
      // Arrange
      const customUpdateDto = {
        category: BookmarkCategory.CUSTOM,
        customCategoryName: undefined,
      };
      mockBookmarkRepository.findOne.mockResolvedValue(mockBookmark as any);

      // Act & Assert
      await expect(
        service.updateBookmarkCategory(
          "bookmark-001",
          customUpdateDto,
          "user-001",
        ),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe("deleteBookmark - 删除收藏", () => {
    it("应该成功删除收藏", async () => {
      // Arrange
      mockBookmarkRepository.findOne.mockResolvedValue(mockBookmark as any);

      // Act
      await service.deleteBookmark("bookmark-001", "user-001");

      // Assert
      expect(mockBookmarkRepository.findOne).toHaveBeenCalledWith({
        where: { id: "bookmark-001", userId: "user-001" },
      });
      expect(mockBookmarkRepository.remove).toHaveBeenCalledWith(mockBookmark);
    });

    it("当收藏不存在时应该抛出NotFoundException", async () => {
      // Arrange
      mockBookmarkRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.deleteBookmark("non-existent", "user-001"),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe("getBookmarkStats - 获取收藏统计", () => {
    it("应该成功获取收藏统计数据", async () => {
      // Arrange
      const mockBaseStats = {
        totalBookmarks: "10",
        activeBookmarks: "8",
        archivedBookmarks: "1",
        invalidBookmarks: "1",
      };
      const mockCategoryStats = [
        { category: BookmarkCategory.FAVORITE, count: "3" },
        { category: BookmarkCategory.TO_READ, count: "2" },
        {
          category: BookmarkCategory.CUSTOM,
          customCategoryName: "学习",
          count: "1",
        },
      ];

      const mockQueryBuilder = mockBookmarkRepository.createQueryBuilder();
      (mockQueryBuilder.getRawOne as jest.Mock).mockResolvedValue(
        mockBaseStats,
      );
      (mockQueryBuilder.getRawMany as jest.Mock).mockResolvedValue(
        mockCategoryStats,
      );

      // Act
      const result = await service.getBookmarkStats("user-001");

      // Assert
      expect(result).toEqual({
        totalBookmarks: 10,
        activeBookmarks: 8,
        archivedBookmarks: 1,
        invalidBookmarks: 1,
        categoryStats: expect.arrayContaining([
          expect.objectContaining({
            category: BookmarkCategory.FAVORITE,
            count: 3,
          }),
        ]),
      });
    });

    it("应该正确处理空的统计结果", async () => {
      // Arrange
      const emptyStats = {
        totalBookmarks: null,
        activeBookmarks: null,
        archivedBookmarks: null,
        invalidBookmarks: null,
      };
      const mockQueryBuilder = mockBookmarkRepository.createQueryBuilder();
      (mockQueryBuilder.getRawOne as jest.Mock).mockResolvedValue(emptyStats);
      (mockQueryBuilder.getRawMany as jest.Mock).mockResolvedValue([]);

      // Act
      const result = await service.getBookmarkStats("user-001");

      // Assert
      expect(result).toEqual({
        totalBookmarks: 0,
        activeBookmarks: 0,
        archivedBookmarks: 0,
        invalidBookmarks: 0,
        categoryStats: [],
      });
    });
  });

  describe("isStoryBookmarked - 检查故事是否已收藏", () => {
    it("已收藏的故事应该返回true", async () => {
      // Arrange
      mockBookmarkRepository.findOne.mockResolvedValue(mockBookmark as any);

      // Act
      const result = await service.isStoryBookmarked("story-001", "user-001");

      // Assert
      expect(result).toBe(true);
      expect(mockBookmarkRepository.findOne).toHaveBeenCalledWith({
        where: {
          storyId: "story-001",
          userId: "user-001",
          status: BookmarkStatus.ACTIVE,
        },
      });
    });

    it("未收藏的故事应该返回false", async () => {
      // Arrange
      mockBookmarkRepository.findOne.mockResolvedValue(null);

      // Act
      const result = await service.isStoryBookmarked("story-002", "user-001");

      // Assert
      expect(result).toBe(false);
    });
  });

  describe("updateBookmarksOnStoryDelete - 故事删除时更新收藏", () => {
    it("应该将相关收藏标记为无效", async () => {
      // Arrange & Act
      await service.updateBookmarksOnStoryDelete("story-001");

      // Assert
      expect(mockBookmarkRepository.update).toHaveBeenCalledWith(
        { storyId: "story-001", status: BookmarkStatus.ACTIVE },
        { status: BookmarkStatus.INVALID },
      );
    });
  });

  describe("私有方法测试", () => {
    it("buildBookmarkResponse - 应该正确构建收藏响应", () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = (service as any).buildBookmarkResponse(mockBookmark);

      expect(result).toEqual(
        expect.objectContaining({
          id: "bookmark-001",
          userId: "user-001",
          storyId: "story-001",
          category: BookmarkCategory.DEFAULT,
          note: "这是一个很棒的故事",
          storySnapshot: expect.any(Object),
        }),
      );
    });

    it("buildBookmarkResponse - 应该正确处理关联的故事信息", () => {
      // Arrange
      const bookmarkWithStory = { ...mockBookmark, story: mockStory };

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = (service as any).buildBookmarkResponse(bookmarkWithStory);

      expect(result.story).toEqual(
        expect.objectContaining({
          id: "story-001",
          title: "测试故事",
          authorName: "故事作者",
        }),
      );
    });

    it("applySorting - 应该正确应用各种排序方式", () => {
      const mockQueryBuilder = {
        orderBy: jest.fn().mockReturnThis(),
      };

      const sortOptions = [
        BookmarkSortBy.CREATED_DESC,
        BookmarkSortBy.CREATED_ASC,
        BookmarkSortBy.UPDATED_DESC,
        BookmarkSortBy.STORY_TITLE_ASC,
      ];

      for (const sortBy of sortOptions) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (service as any).applySorting(mockQueryBuilder, sortBy);
        expect(mockQueryBuilder.orderBy).toHaveBeenCalled();
      }
    });
  });

  describe("错误边界测试", () => {
    it("应该处理数据库连接错误", async () => {
      // Arrange
      mockBookmarkRepository.createQueryBuilder.mockImplementation(() => {
        throw new Error("数据库连接失败");
      });

      // Act & Assert
      await expect(
        service.getUserBookmarks("user-001", { page: 1, limit: 10 }),
      ).rejects.toThrow("数据库连接失败");
    });

    it("应该处理无效的收藏数据", async () => {
      // Arrange
      const invalidBookmark = { ...mockBookmark, userId: null };
      mockBookmarkRepository.findOne.mockResolvedValue(invalidBookmark as any);

      // Act & Assert
      await expect(
        service.getBookmarkDetail("bookmark-001", "user-001"),
      ).rejects.toThrow(NotFoundException);
    });
  });
});
