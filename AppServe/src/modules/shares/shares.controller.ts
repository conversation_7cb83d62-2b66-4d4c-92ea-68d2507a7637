import {
  Controller,
  Get,
  Post,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Req,
  HttpStatus,
  Ip,
  Headers,
} from "@nestjs/common";
import type { Request } from "express";
import { SharesService } from "./shares.service";
import { CreateShareDto, ShareAccessDto } from "./dto";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import type { ApiResponseDto } from "../../common/dto/api-response.dto";
import type {
  ShareResponseDto,
  ShareContentResponseDto,
  ShareStatsResponseDto,
} from "./dto";

/**
 * 分享控制器
 * 提供故事分享相关的API接口
 */
@Controller()
export class SharesController {
  constructor(private readonly sharesService: SharesService) {}

  /**
   * 生成分享链接
   * POST /stories/:id/share
   */
  @Post("stories/:id/share")
  @UseGuards(JwtAuthGuard)
  async createShare(
    @Param("id") storyId: string,
    @Body() createShareDto: CreateShareDto,
    @Req() req: Request & { user: { id: string } },
  ): Promise<ApiResponseDto<ShareResponseDto>> {
    const share = await this.sharesService.createShare(
      storyId,
      createShareDto,
      req.user.id,
    );

    return {
      success: true,
      statusCode: HttpStatus.CREATED,
      message: "分享链接创建成功",
      data: share,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 访问分享内容
   * GET /share/:token
   */
  @Get("share/:token")
  async accessShare(
    @Param("token") token: string,
    @Query() accessDto: ShareAccessDto,
    @Ip() ipAddress: string,
    @Headers("user-agent") userAgent: string,
    @Headers("referer") referer: string,
    @Req() req: Request & { user: { id: string } },
  ): Promise<ApiResponseDto<ShareContentResponseDto>> {
    const currentUserId = req.user?.id;

    const content = await this.sharesService.accessShare(
      token,
      accessDto,
      ipAddress,
      userAgent,
      referer,
      currentUserId,
    );

    return {
      success: true,
      statusCode: HttpStatus.OK,
      message: "分享内容获取成功",
      data: content,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 获取故事分享统计
   * GET /stories/:id/share-stats
   */
  @Get("stories/:id/share-stats")
  @UseGuards(JwtAuthGuard)
  async getShareStats(
    @Param("id") storyId: string,
    @Req() req: Request & { user: { id: string } },
  ): Promise<ApiResponseDto<ShareStatsResponseDto>> {
    const stats = await this.sharesService.getShareStats(storyId, req.user.id);

    return {
      success: true,
      statusCode: HttpStatus.OK,
      message: "分享统计获取成功",
      data: stats,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 获取用户分享列表
   * GET /shares/my
   */
  @Get("shares/my")
  @UseGuards(JwtAuthGuard)
  async getUserShares(
    @Req() req: Request & { user: { id: string } },
  ): Promise<ApiResponseDto<ShareResponseDto[]>> {
    const shares = await this.sharesService.getUserShares(req.user.id);

    return {
      success: true,
      statusCode: HttpStatus.OK,
      message: "分享列表获取成功",
      data: shares,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 删除分享
   * DELETE /shares/:id
   */
  @Delete("shares/:id")
  @UseGuards(JwtAuthGuard)
  async deleteShare(
    @Param("id") shareId: string,
    @Req() req: Request & { user: { id: string } },
  ): Promise<ApiResponseDto<null>> {
    await this.sharesService.deleteShare(shareId, req.user.id);

    return {
      success: true,
      statusCode: HttpStatus.OK,
      message: "分享删除成功",
      data: null,
      timestamp: new Date().toISOString(),
    };
  }
}
