# YGS Flutter应用启动说明

## 🚀 快速启动

### 1. 环境准备
确保您已安装：
- Flutter SDK 3.32+
- Dart SDK 3.5+
- iOS开发：Xcode 14.0+ (仅macOS)
- Android开发：Android Studio

### 2. 启动步骤

#### 方式一：使用启动脚本（推荐）
```bash
cd AppMobile
./run_app.sh
```

#### 方式二：手动启动

##### 在iOS模拟器运行：
```bash
cd AppMobile
flutter pub get
flutter run -d iphone
```

##### 在Android模拟器运行：
```bash
cd AppMobile
flutter pub get
flutter run -d android
```

##### 在Chrome浏览器运行（最简单）：
```bash
cd AppMobile
flutter pub get
flutter run -d chrome
```

##### 在macOS桌面运行：
```bash
cd AppMobile
flutter pub get
flutter run -d macos
```

### 3. 常见问题解决

#### 问题1：Flutter镜像下载失败
如果遇到镜像下载问题，请设置国内镜像：
```bash
export FLUTTER_STORAGE_BASE_URL="https://storage.flutter-io.cn"
export PUB_HOSTED_URL="https://pub.flutter-io.cn"
```

或使用官方源：
```bash
unset FLUTTER_STORAGE_BASE_URL
unset PUB_HOSTED_URL
```

#### 问题2：依赖获取失败
```bash
flutter clean
flutter pub cache repair
flutter pub get
```

#### 问题3：iOS运行失败
```bash
cd ios
pod install
cd ..
flutter run -d iphone
```

### 4. 登录测试

应用启动后，您可以使用以下测试账号登录：

**手机号登录：**
- 手机号：13900000001
- 验证码：123456（测试环境固定）

**后端API地址：**
- 开发环境：http://localhost:3000/v1
- 生产环境：https://api.yougushi.com/v1

### 5. 功能预览

✅ **已实现功能：**
- 精美的启动页面
- 手机号验证码登录
- 首页展示（欢迎卡片、功能模块、故事列表）
- 底部导航（首页、故事、创作、人物、我的）
- 个人中心
- 退出登录

⏳ **待实现功能：**
- 故事创作
- 人物点亮系统
- 社交功能
- 通知系统
- 设置页面

### 6. 开发调试

**热重载：**
- 按 `r` 键：热重载
- 按 `R` 键：热重启
- 按 `q` 键：退出

**查看日志：**
```bash
flutter logs
```

**性能分析：**
- 按 `P` 键：显示性能图层
- 按 `w` 键：显示widget边界

### 7. 构建发布版

**Android APK：**
```bash
flutter build apk --release
```

**iOS：**
```bash
flutter build ios --release
```

**Web：**
```bash
flutter build web --release
```

---

如有任何问题，请查看项目文档或联系开发团队。