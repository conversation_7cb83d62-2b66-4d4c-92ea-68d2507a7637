#!/usr/bin/env node

/**
 * 增强SMS功能测试脚本
 * 直接测试SMS服务的核心功能
 */

const { NestFactory } = require('@nestjs/core');
const { AppModule } = require('../dist/app.module');
const { ConfigService } = require('@nestjs/config');

async function bootstrap() {
  console.log('🚀 启动增强SMS功能测试...\n');

  // 创建应用实例
  const app = await NestFactory.createApplicationContext(AppModule);
  const configService = app.get(ConfigService);

  // 获取SMS服务实例
  const { SmsService } = require('../dist/modules/auth/services/sms.service');
  const { AliyunSmsService } = require('../dist/modules/auth/services/aliyun-sms.service');
  const { DeviceFingerprintService } = require('../dist/common/services/device-fingerprint.service');
  const { VerificationSecurityService } = require('../dist/common/services/verification-security.service');
  
  const smsService = app.get(SmsService);
  const aliyunSmsService = app.get(AliyunSmsService);
  const deviceFingerprintService = app.get(DeviceFingerprintService);
  const verificationSecurityService = app.get(VerificationSecurityService);

  console.log('📊 当前SMS服务状态:');
  const smsStatus = await smsService.getSmsServiceStatus();
  console.log(`- 模式: ${smsStatus.mode}`);
  console.log(`- 环境: ${smsStatus.environment}`);
  console.log(`- VIP手机号: ${smsStatus.vipPhones.join(', ')}`);
  console.log(`- 客户端状态: ${smsStatus.clientStatus}\n`);

  // 测试案例
  const testCases = [
    {
      name: '普通手机号 - Mock验证码',
      phone: '13888888888',
      expected: 'Mock验证码'
    },
    {
      name: 'VIP手机号 - 真实验证码',
      phone: '16675158665',
      expected: '真实验证码'
    }
  ];

  // 模拟Request对象
  const mockRequest = {
    headers: {
      'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15',
      'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
      'accept-encoding': 'gzip, deflate, br',
      'x-forwarded-for': '*************'
    },
    ip: '*************',
    connection: { remoteAddress: '*************' },
    socket: { remoteAddress: '*************' }
  };

  // 执行测试
  for (const testCase of testCases) {
    console.log(`\n🔍 测试案例: ${testCase.name}`);
    console.log(`📞 手机号: ${testCase.phone}`);
    
    try {
      // 1. 发送验证码测试
      console.log('📤 发送验证码...');
      const sendResult = await smsService.sendVerificationCode(testCase.phone, mockRequest);
      
      if (sendResult.success) {
        console.log(`✅ 发送成功: ${sendResult.message}`);
      } else {
        console.log(`❌ 发送失败: ${sendResult.message}`);
        continue;
      }

      // 2. 验证码验证测试
      console.log('🔐 验证码验证...');
      
      // 对于VIP号码，我们需要从日志中获取真实验证码
      // 这里我们使用一个通用的测试验证码
      const testCode = '1234'; // 4位验证码
      
      const verifyResult = await smsService.verifyCode(testCase.phone, testCode, mockRequest);
      
      if (verifyResult) {
        console.log(`✅ 验证成功`);
      } else {
        console.log(`❌ 验证失败`);
      }
      
    } catch (error) {
      console.error(`❌ 测试失败: ${error.message}`);
    }
  }

  // 测试设备指纹功能
  console.log('\n🧪 设备指纹功能测试...');
  
  const deviceInfo = deviceFingerprintService.generateDeviceFingerprint(mockRequest);
  
  console.log(`📱 设备指纹: ${deviceInfo.fingerprint}`);
  console.log(`🖥️ 平台: ${deviceInfo.platform}`);
  console.log(`🌐 IP: ${deviceInfo.ip}`);
  console.log(`📝 User-Agent: ${deviceInfo.userAgent.substring(0, 50)}...`);

  // 测试验证安全功能
  console.log('\n🔒 验证安全功能测试...');
  
  const securityCheck = await verificationSecurityService.checkVerificationAttempt(
    '13888888888',
    deviceInfo.ip,
    deviceInfo.fingerprint
  );
  
  console.log(`🛡️ 安全检查: ${securityCheck.allowed ? '通过' : '拒绝'}`);
  if (!securityCheck.allowed) {
    console.log(`❌ 拒绝原因: ${securityCheck.reason}`);
  }

  // 获取SMS统计信息
  console.log('\n📊 SMS统计信息...');
  const smsStats = await smsService.getStatistics();
  console.log(`📈 今日发送总数: ${smsStats.totalSent}`);
  console.log(`❌ 今日失败总数: ${smsStats.totalFailed}`);
  console.log(`✅ 今日成功率: ${smsStats.dailySuccessRate}%`);
  console.log(`⏱️ 平均响应时间: ${smsStats.averageResponseTime}ms`);

  console.log('\n✅ 所有测试完成！');
  
  // 关闭应用
  await app.close();
}

bootstrap().catch(error => {
  console.error('❌ 测试启动失败:', error);
  process.exit(1);
});