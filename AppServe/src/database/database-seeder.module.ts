/**
 * 企业级数据播种器模块
 *
 * 功能: 为前后端联调提供数据生成服务
 * 依赖: 所有业务实体和相关服务
 */

import { Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";

// 实体导入
import { User } from "../modules/users/entities/user.entity";
import { Story } from "../modules/stories/entities/story.entity";
// StoryCharacter 是通过 ManyToMany 关系自动管理的，不需要单独导入
import { Character } from "../modules/characters/entities/character.entity";
import { CharacterLighting } from "../modules/characters/entities/character-lighting.entity";
import { LightRequest } from "../modules/characters/entities/light-request.entity";
import { UserFollow } from "../modules/social/entities/user-follow.entity";
import { FriendGroup } from "../modules/social/entities/friend-group.entity";
import { FriendGroupMember } from "../modules/social/entities/friend-group-member.entity";

// 服务导入
import { EnhancedDatabaseSeederService } from "./enhanced-database-seeder.service";

@Module({
  imports: [
    TypeOrmModule.forFeature([
      User,
      Story,
      Character,
      CharacterLighting,
      LightRequest,
      UserFollow,
      FriendGroup,
      FriendGroupMember,
    ]),
  ],
  providers: [EnhancedDatabaseSeederService],
  exports: [EnhancedDatabaseSeederService],
})
export class DatabaseSeederModule {}
