import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { HomepageController } from "./homepage.controller";
import { HomepageService } from "./homepage.service";
import { User } from "../users/entities/user.entity";
import { Story } from "../stories/entities/story.entity";
import { Character } from "../characters/entities/character.entity";
import { Bookmark } from "../bookmarks/entities/bookmark.entity";
import { TimelineEvent } from "../timeline/entities/timeline-event.entity";
import { ReferenceCollection } from "../story-references/entities/reference-collection.entity";
import { UserFollow } from "../social/entities/user-follow.entity";
import { FriendGroup } from "../social/entities/friend-group.entity";
import { AuthModule } from "../auth/auth.module";

@Module({
  imports: [
    TypeOrmModule.forFeature([
      User,
      Story,
      Character,
      Bookmark,
      TimelineEvent,
      ReferenceCollection,
      UserFollow,
      FriendGroup,
    ]),
    AuthModule,
  ],
  controllers: [HomepageController],
  providers: [HomepageService],
  exports: [HomepageService],
})
export class HomepageModule {}
