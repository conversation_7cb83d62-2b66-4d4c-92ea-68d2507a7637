/**
 * OSS配置 - 企业级单元测试
 */

import { ossConfig } from "./oss.config";

describe("OSSConfig - 企业级配置测试", () => {
  let originalEnv: NodeJS.ProcessEnv;

  beforeEach(() => {
    // 保存原始环境变量
    originalEnv = { ...process.env };
  });

  afterEach(() => {
    // 恢复原始环境变量
    process.env = originalEnv;
  });

  describe("配置函数", () => {
    it("should be defined", () => {
      expect(ossConfig).toBeDefined();
      expect(typeof ossConfig).toBe("function");
    });

    it("should return configuration object", () => {
      // Arrange
      process.env.OSS_ACCESS_KEY_ID = "test_key";
      process.env.OSS_ACCESS_KEY_SECRET = "test_secret";
      process.env.OSS_BUCKET = "test-bucket";
      process.env.OSS_REGION = "cn-hangzhou";

      // Act
      const config = ossConfig();

      // Assert
      expect(config).toBeDefined();
      expect(typeof config).toBe("object");
    });
  });

  describe("访问密钥配置", () => {
    it("should use environment variables for access keys", () => {
      // Arrange
      process.env.OSS_ACCESS_KEY_ID = "LTAI4GcGkR8EYYzUxxxxxxxx";
      process.env.OSS_ACCESS_KEY_SECRET = "ZjH8QXxxxxxxxxxxxxxxxxxx";

      // Act
      const config = ossConfig();

      // Assert
      expect(config.accessKeyId).toBe("LTAI4GcGkR8EYYzUxxxxxxxx");
      expect(config.accessKeySecret).toBe("ZjH8QXxxxxxxxxxxxxxxxxxx");
    });

    it("should handle missing access key ID", () => {
      // Arrange
      delete process.env.OSS_ACCESS_KEY_ID;
      process.env.OSS_ACCESS_KEY_SECRET = "test_secret";

      // Act
      const config = ossConfig();

      // Assert
      expect(config.accessKeyId).toBe("");
    });

    it("should handle missing access key secret", () => {
      // Arrange
      process.env.OSS_ACCESS_KEY_ID = "test_key";
      delete process.env.OSS_ACCESS_KEY_SECRET;

      // Act
      const config = ossConfig();

      // Assert
      expect(config.accessKeySecret).toBe("");
    });
  });

  describe("存储桶配置", () => {
    it("should configure bucket name", () => {
      // Arrange
      process.env.OSS_BUCKET = "ygs-prod-storage";

      // Act
      const config = ossConfig();

      // Assert
      expect(config.bucket).toBe("ygs-prod-storage");
    });

    it("should use default bucket when not specified", () => {
      // Arrange
      delete process.env.OSS_BUCKET;

      // Act
      const config = ossConfig();

      // Assert
      expect(config.bucket).toBe("ygs-app-storage");
    });
  });

  describe("区域配置", () => {
    it("should configure region", () => {
      // Arrange
      process.env.OSS_REGION = "cn-shanghai";

      // Act
      const config = ossConfig();

      // Assert
      expect(config.region).toBe("oss-cn-shanghai");
    });

    it("should use default region when not specified", () => {
      // Arrange
      delete process.env.OSS_REGION;

      // Act
      const config = ossConfig();

      // Assert
      expect(config.region).toBe("oss-cn-hangzhou");
    });

    it("should handle different region formats", () => {
      // Arrange
      process.env.OSS_REGION = "beijing";

      // Act
      const config = ossConfig();

      // Assert
      expect(config.region).toBe("oss-beijing");
    });
  });

  describe("内网访问配置", () => {
    it("should configure internal endpoint for production", () => {
      // Arrange
      process.env.NODE_ENV = "production";
      process.env.OSS_INTERNAL = "true";

      // Act
      const config = ossConfig();

      // Assert
      expect(config.internal).toBe(true);
    });

    it("should use external endpoint for development", () => {
      // Arrange
      process.env.NODE_ENV = "development";
      delete process.env.OSS_INTERNAL;

      // Act
      const config = ossConfig();

      // Assert
      expect(config.internal).toBe(false);
    });
  });

  describe("HTTPS配置", () => {
    it("should enable HTTPS by default", () => {
      // Act
      const config = ossConfig();

      // Assert
      expect(config.secure).toBe(true);
    });

    it("should allow HTTP for development", () => {
      // Arrange
      process.env.OSS_SECURE = "false";

      // Act
      const config = ossConfig();

      // Assert
      expect(config.secure).toBe(false);
    });
  });

  describe("超时配置", () => {
    it("should configure request timeout", () => {
      // Arrange
      process.env.OSS_TIMEOUT = "30000";

      // Act
      const config = ossConfig();

      // Assert
      expect(config.timeout).toBe(30000);
    });

    it("should use default timeout when not specified", () => {
      // Arrange
      delete process.env.OSS_TIMEOUT;

      // Act
      const config = ossConfig();

      // Assert
      expect(config.timeout).toBe(60000); // 默认60秒
    });
  });

  describe("配置验证", () => {
    it("should validate required configuration", () => {
      // Arrange
      delete process.env.OSS_ACCESS_KEY_ID;
      delete process.env.OSS_ACCESS_KEY_SECRET;
      delete process.env.OSS_BUCKET;

      // Act
      const config = ossConfig();

      // Assert
      expect(config).toBeDefined();
      // 应该有默认值或错误处理
    });

    it("should handle empty string values", () => {
      // Arrange
      process.env.OSS_ACCESS_KEY_ID = "";
      process.env.OSS_ACCESS_KEY_SECRET = "";
      process.env.OSS_BUCKET = "";

      // Act
      const config = ossConfig();

      // Assert
      expect(config.accessKeyId).toBe("");
      expect(config.accessKeySecret).toBe("");
      expect(config.bucket).toBeTruthy(); // 应该有默认值
    });
  });
});
