import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import '../../../../app/routes/app_router.dart';
import '../../../../design_system/tokens/ygs_colors.dart';
import '../../../../design_system/tokens/ygs_spacing.dart';
import '../../../../design_system/tokens/ygs_typography.dart';
import '../../../../design_system/components/ygs_error_toast.dart';

/// 登录页第一阶段：手机号输入页面
/// 基于Figma设计实现，遵循企业级开发标准
class LoginPage001 extends StatefulWidget {
  const LoginPage001({super.key});

  @override
  State<LoginPage001> createState() => _LoginPage001State();
}

class _LoginPage001State extends State<LoginPage001>
    with TickerProviderStateMixin {
  
  // ========================= 状态变量 =========================
  
  /// 当前页面阶段：false=手机号阶段，true=验证码阶段
  bool _isCodeStage = false;
  
  /// 是否正在加载
  bool _isLoading = false;
  
  /// 是否同意协议
  bool _isAgreed = false;
  
  /// 脱敏手机号（用于验证码阶段显示）
  String _maskedPhone = '';
  
  /// 按钮按下状态
  bool _isButtonPressed = false;
  
  /// 按钮高亮闪烁状态
  bool _isButtonHighlight = false;
  
  // ========================= 控制器 =========================
  
  /// 手机号输入控制器
  final TextEditingController _phoneController = TextEditingController();
  final FocusNode _phoneFocusNode = FocusNode();
  
  /// 验证码输入控制器（4个）
  final List<TextEditingController> _codeControllers =
      List.generate(4, (index) => TextEditingController());
  final List<FocusNode> _codeFocusNodes =
      List.generate(4, (index) => FocusNode());
  
  
  // ========================= 动画控制器 =========================
  
  late AnimationController _fadeAnimationController;
  late AnimationController _slideAnimationController;
  late AnimationController _cursorAnimationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _cursorAnimation;

  // ========================= 资源路径 =========================
  
  static const String _logoImagePath = 'assets/images/splash/logo.svg';
  static const String _backgroundImagePath = 'assets/images/auth/simple_background.svg';

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _initListeners();
    _startAnimations();
  }

  @override
  void dispose() {
    _phoneController.dispose();
    _phoneFocusNode.dispose();
    
    for (var controller in _codeControllers) {
      controller.dispose();
    }
    for (var focusNode in _codeFocusNodes) {
      focusNode.dispose();
    }
    
    _fadeAnimationController.dispose();
    _slideAnimationController.dispose();
    _cursorAnimationController.dispose();
    super.dispose();
  }

  // ========================= 初始化方法 =========================
  
  void _initAnimations() {
    _fadeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _slideAnimationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _cursorAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeAnimationController, curve: Curves.easeIn),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideAnimationController,
      curve: Curves.easeOutCubic,
    ));

    _cursorAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _cursorAnimationController, curve: Curves.easeInOut),
    );

    _cursorAnimationController.repeat(reverse: true);
  }

  void _initListeners() {
    _phoneController.addListener(_validatePhoneInput);
    
    for (int i = 0; i < _codeControllers.length; i++) {
      _codeControllers[i].addListener(() => _validateCodeInput());
      // 移除重复的_handleCodeInput调用，使用onChanged处理
      _codeControllers[i].addListener(() => setState(() {})); // 更新边框颜色
      _codeFocusNodes[i].addListener(() => setState(() {})); // 更新光标显示
    }
  }

  void _startAnimations() {
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) {
        _fadeAnimationController.forward();
        _slideAnimationController.forward();
      }
    });
  }

  // ========================= 输入验证逻辑 =========================
  
  void _validatePhoneInput() {
    if (_isCodeStage) return; // 验证码阶段不处理手机号验证
    
    final phone = _phoneController.text.trim();
    final isValidPhone = phone.isNotEmpty &&
        phone.length >= 11 &&
        RegExp(r'^1[3-9]\d{9}$').hasMatch(phone);
    
    // 手机号阶段：手机号有效 且 同意协议 才能提交
    // 不需要 _isButtonEnabled 变量，直接根据条件判断
  }
  
  void _validateCodeInput() {
    if (!_isCodeStage) return; // 手机号阶段不处理验证码验证
    
    final codeLength = _codeControllers
        .map((c) => c.text.trim())
        .where((text) => text.isNotEmpty)
        .length;
    
    // 验证码输入完成后自动登录
    if (codeLength == 4 && !_isLoading) {
      setState(() {
        _isLoading = true;
      });
      Future.delayed(const Duration(milliseconds: 300), () {
        _handleSubmit();
      });
    }
  }

  // ========================= 验证码输入处理 =========================
  
  void _handleCodeInput(int index) {
    final text = _codeControllers[index].text;
    
    // 确保输入只有一位数字
    if (text.length > 1) {
      _codeControllers[index].text = text.substring(0, 1);
      return;
    }

    if (text.isNotEmpty && index < 3) {
      // 输入后自动跳转到下一个输入框
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && index + 1 < _codeFocusNodes.length) {
          _codeFocusNodes[index + 1].requestFocus();
        }
      });
    }
    // 删除逻辑交给键盘处理，不在这里处理以避免冲突
  }

  Color _getCodeInputBorderColor(int index) {
    final text = _codeControllers[index].text;
    final hasFocus = _codeFocusNodes[index].hasFocus;

    if (text.isNotEmpty) {
      return const Color(0xFF11C625); // 已输入：绿色
    } else if (hasFocus) {
      return const Color(0xFF2A2A39); // 聚焦：深色
    } else {
      return const Color(0xFF2A2A39); // 默认：深色
    }
  }

  // ========================= 业务逻辑 =========================
  
  /// 处理协议同意状态变更
  void _handleAgreementChange(bool? value) {
    setState(() {
      _isAgreed = value ?? false;
    });
  }

  /// 处理提交按钮点击
  Future<void> _handleSubmit() async {
    // 验证码阶段允许执行，因为已经设置了 _isLoading
    if (_isLoading && !_isCodeStage) return;

    // 手机号阶段的验证
    if (!_isCodeStage) {
      final phone = _phoneController.text.trim();
      final isValidPhone = phone.isNotEmpty &&
          phone.length >= 11 &&
          RegExp(r'^1[3-9]\d{9}$').hasMatch(phone);

      if (!isValidPhone || !_isAgreed) {
        _showValidationErrors(phone, isValidPhone);
        return;
      }

      setState(() {
        _isLoading = true;
      });
    }

    try {
      // 模拟网络请求
      await Future<void>.delayed(const Duration(milliseconds: 1500));

      if (!mounted) return;

      if (_isCodeStage) {
        // 验证码阶段：跳转到首页
        context.go(AppPaths.home);
      } else {
        // 手机号阶段：切换到验证码阶段
        _switchToCodeStage();
      }
    } catch (e) {
      if (mounted) {
        YgsErrorToastManager.show(
          context,
          message: '网络连接失败，请检查网络后重试',
        );
      }
    } finally {
      // 只有手机号阶段才恢复加载状态，验证码阶段跳转后不恢复
      if (mounted && !_isCodeStage) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 切换到验证码输入阶段
  void _switchToCodeStage() {
    final phone = _phoneController.text.trim();
    if (phone.length >= 11) {
      _maskedPhone = '${phone.substring(0, 3)}****${phone.substring(7)}';
    }

    setState(() {
      _isCodeStage = true;
      _isLoading = false;
    });

    // 自动聚焦第一个验证码输入框
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        _codeFocusNodes[0].requestFocus();
      }
    });
  }

  /// 返回手机号输入阶段
  void _backToPhoneStage() {
    setState(() {
      _isCodeStage = false;
      _isLoading = false;
      _maskedPhone = '';
    });

    // 清空验证码
    for (var controller in _codeControllers) {
      controller.clear();
    }
  }

  // ========================= 按钮颜色计算 =========================
  
  /// 计算按钮颜色 - 行业标准高亮效果
  Color _getButtonColor(Color baseColor, bool isDisabled) {
    if (isDisabled) {
      return baseColor; // 禁用状态保持原色
    }
    
    if (_isButtonHighlight) {
      // 高亮状态：瞬间变亮（行业标准做法）
      return Color.lerp(baseColor, Colors.white, 0.2) ?? baseColor;
    } else if (_isButtonPressed) {
      // 按下状态：轻微加深
      return Color.lerp(baseColor, Colors.black, 0.15) ?? baseColor;
    } else {
      // 正常状态
      return baseColor;
    }
  }

  // ========================= 验证错误处理 =========================
  
  void _showValidationErrors(String phone, bool isValidPhone) {
    String? errorMessage;

    // 添加手机号为空的检查
    if (phone.isEmpty) {
      errorMessage = '请先输入手机号码';
    } else if (!isValidPhone) {
      if (phone.length < 11) {
        errorMessage = '手机号码不足11位';
      } else if (!RegExp(r'^1[3-9]\d{9}$').hasMatch(phone)) {
        errorMessage = '手机号格式不正确';
      }
    } else if (isValidPhone && !_isAgreed) {
      errorMessage = '请先阅读并同意用户协议和隐私政策';
    }

    if (errorMessage != null) {
      YgsErrorToastManager.show(
        context,
        message: errorMessage,
      );
    }
  }

  // ========================= UI构建方法 =========================

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      resizeToAvoidBottomInset: false, // 企业级标准：设为false，自定义处理
      body: Stack(
        children: [
          _buildBackground(),
          FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: _buildMainContent(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBackground() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: YgsColors.backgroundGradient,
      ),
      child: Stack(
        children: [
          Positioned(
            right: -YgsSpacing.maxContentWidth * 0.3125,
            top: -YgsSpacing.maxContentWidth * 0.25,
            child: SizedBox(
              width: YgsSpacing.maxContentWidth * 0.875,
              height: YgsSpacing.maxContentWidth * 0.875,
              child: SvgPicture.asset(
                _backgroundImagePath,
                fit: BoxFit.contain,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMainContent() {
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final isKeyboardVisible = keyboardHeight > 0;
    
    return GestureDetector(
      // 优化：只在真正的空白区域收起键盘，避免拦截TextField事件
      onTap: () {
        // 延迟执行，让TextField有机会获取焦点
        Future.microtask(() {
          FocusScope.of(context).unfocus();
        });
      },
      // 向下滑动手势：取消键盘
      onPanUpdate: (details) {
        // 检测向下滑动
        if (details.delta.dy > 0 && isKeyboardVisible) {
          // 向下滑动超过5像素且键盘可见时收起键盘
          if (details.delta.dy > 5) {
            FocusScope.of(context).unfocus();
          }
        }
      },
      // 滑动结束时也可以收起键盘
      onPanEnd: (details) {
        // 如果是快速向下滑动
        if (details.velocity.pixelsPerSecond.dy > 300 && isKeyboardVisible) {
          FocusScope.of(context).unfocus();
        }
      },
      behavior: HitTestBehavior.translucent, // 改为translucent，允许子组件优先响应
      child: SafeArea(
        child: Transform.translate(
          // 增加上移距离，确保底部文字不被键盘遮挡
          offset: Offset(0, isKeyboardVisible ? -keyboardHeight * 0.55 : 0),
          child: SingleChildScrollView(
            physics: keyboardHeight > 0 
                ? const ClampingScrollPhysics() // 键盘弹出时允许滚动
                : const NeverScrollableScrollPhysics(), // 正常状态禁止滚动
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight: MediaQuery.of(context).size.height - MediaQuery.of(context).padding.top,
              ),
              child: IntrinsicHeight(
                child: Column(
                  children: [
                    const SizedBox(height: 160), // 适度增加顶部间距
                    // logo跟随键盘高度实时变化，实现同步动画
                    Opacity(
                      opacity: keyboardHeight > 0 ? (1.0 - (keyboardHeight / 300)).clamp(0.0, 1.0) : 1.0,
                      child: Transform.scale(
                        scale: keyboardHeight > 0 ? (1.0 - (keyboardHeight / 300)).clamp(0.0, 1.0) : 1.0,
                        child: _buildLogoSection(),
                      ),
                    ),
                    SizedBox(
                      height: keyboardHeight > 0 ? (56 * (1.0 - keyboardHeight / 300)).clamp(10.0, 56.0) : 56.0,
                    ),
                    _buildTitleSection(),
                    
                    // 根据Figma设计稿精确间距，适度放松
                    if (_isCodeStage) ...[
                      const SizedBox(height: 50), // 适度增加间距
                      _buildCodeHintSection(),
                      const SizedBox(height: 20), // 适度增加间距
                      _buildCodeInputSection(),
                      const SizedBox(height: 80), // 适度增加间距
                    ] else ...[
                      const SizedBox(height: 70), // 适度增加间距
                      _buildPhoneInputSection(),
                      const SizedBox(height: 80), // 适度增加间距
                    ],

                    // 按钮区域
                    _buildButtonSection(),

                    const SizedBox(height: 29), // 恢复原始间距

                    // 底部内容
                    _isCodeStage ? _buildBackToPhoneSection() : _buildAgreementSection(),

                    // 减少底部预留空间，让内容更好地填充页面
                    const SizedBox(height: 120), // 减少底部空间
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLogoSection() {
    return Container(
      width: 120.w,
      height: 120.w,
      child: SvgPicture.asset(
        _logoImagePath,
        width: 120.w,
        height: 120.w,
        fit: BoxFit.contain,
        placeholderBuilder: (context) => Container(
          width: 120.w,
          height: 120.w,
          decoration: BoxDecoration(
            gradient: YgsColors.brandGradient,
            borderRadius: BorderRadius.circular(25.r),
            boxShadow: YgsColors.elevatedShadow,
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.auto_stories, size: 48.w, color: YgsColors.onBrandPrimary),
                SizedBox(height: 4.h),
                Text(
                  'YGS',
                  style: YgsTypography.bodyMedium.copyWith(
                    fontSize: 20.sp,
                    fontWeight: FontWeight.bold,
                    color: YgsColors.onBrandPrimary,
                    letterSpacing: 2,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTitleSection() {
    return Column(
      children: [
        Text(
          '有故事',
          style: YgsTypography.displayLarge.copyWith(fontSize: 48.sp),
        ),
        YgsSpacing.verticalSpaceXxl,
        Text(
          '未注册的手机号验证通过后将自动注册',
          style: YgsTypography.bodyLarge.copyWith(
            fontSize: 24.sp,
            color: YgsColors.textTertiary,
          ),
        ),
      ],
    );
  }

  Widget _buildCodeHintSection() {
    return Center(
      child: Text(
        '验证码已发送至$_maskedPhone',
        style: YgsTypography.bodyLarge.copyWith(fontSize: 24.sp),
      ),
    );
  }

  Widget _buildPhoneInputSection() {
    return Center(
      child: GestureDetector(
        // 确保输入框区域的点击事件优先响应
        onTap: () {
          _phoneFocusNode.requestFocus();
        },
        child: Container(
          width: 600.w,
          height: 100.h,
          decoration: BoxDecoration(
            color: const Color(0xFFFFFFFF),
            borderRadius: BorderRadius.circular(16.r),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFFEDF0F6).withValues(alpha: 0.6),
                blurRadius: 24.r,
                offset: Offset(0, 12.h),
                spreadRadius: 8.r,
              ),
            ],
          ),
          child: Row(
            children: [
              // +86 区域
              Container(
                width: 100.w,
                height: 100.h,
                child: Center(
                  child: Transform.translate(
                    offset: Offset(0, 1.h), // 向下微调1个单位
                    child: Text(
                      '+86',
                      style: TextStyle(
                        fontFamily: 'PingFang SC',
                        fontSize: 30.sp, // 严格按照Figma设计稿30px
                        fontWeight: FontWeight.w400,
                        color: const Color(0xFF151523),
                      ),
                    ),
                  ),
                ),
              ),
              
              // 分割线
              Container(
                width: 1.w,
                height: 50.h,
                color: const Color(0xFFDEE1E5),
              ),
              
              // 手机号输入区域
              Expanded(
                child: Container(
                  height: 100.h,
                  alignment: Alignment.center,
                  child: TextField(
                    controller: _phoneController,
                    focusNode: _phoneFocusNode,
                    keyboardType: TextInputType.phone,
                    textInputAction: TextInputAction.done,
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                      LengthLimitingTextInputFormatter(11),
                    ],
                    onSubmitted: (_) {
                      _phoneFocusNode.unfocus();
                    },
                    style: TextStyle(
                      fontFamily: 'PingFang SC',
                      fontSize: 30.sp, // 严格按照Figma设计稿30px
                      fontWeight: FontWeight.w400,
                      color: const Color(0xFF151523),
                    ),
                    decoration: InputDecoration(
                      hintText: '请输入手机号码',
                      hintStyle: TextStyle(
                        fontFamily: 'PingFang SC',
                        fontSize: 30.sp, // 严格按照Figma设计稿30px
                        fontWeight: FontWeight.w400,
                        color: const Color(0xFF868691),
                      ),
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(horizontal: 20.w),
                      isDense: true,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCodeInputSection() {
    return Center(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(4, (index) {
          return Container(
            width: 100.w,
            height: 100.w,
            margin: EdgeInsets.symmetric(horizontal: 12.w),
            decoration: BoxDecoration(
              color: Colors.transparent,
              borderRadius: BorderRadius.circular(12.r),
              border: Border.all(
                color: _getCodeInputBorderColor(index),
                width: 2.w,
              ),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFFEDF0F6).withValues(alpha: 0.4),
                  blurRadius: 12.r,
                  offset: Offset(0, 6.h),
                  spreadRadius: 2.r,
                ),
              ],
            ),
            child: Stack(
              children: [
                Container(
                  width: 100.w,
                  height: 100.w,
                  alignment: Alignment.center,
                  child: KeyboardListener(
                    focusNode: FocusNode(),
                    onKeyEvent: (KeyEvent event) {
                      // 处理删除键
                      if (event is KeyDownEvent && 
                          event.logicalKey == LogicalKeyboardKey.backspace &&
                          _codeControllers[index].text.isEmpty &&
                          index > 0) {
                        _codeFocusNodes[index - 1].requestFocus();
                      }
                    },
                    child: TextField(
                      controller: _codeControllers[index],
                      focusNode: _codeFocusNodes[index],
                      keyboardType: TextInputType.number,
                      textInputAction: index == 3 ? TextInputAction.done : TextInputAction.next,
                      textAlign: TextAlign.center,
                      maxLength: 1,
                      cursorWidth: 0,
                      cursorHeight: 0,
                      showCursor: false,
                      onChanged: (value) {
                        // 输入时立即触发跳转逻辑
                        _handleCodeInput(index);
                      },
                      onSubmitted: (_) {
                        if (index == 3) {
                          _codeFocusNodes[index].unfocus();
                        } else {
                          // 按回车也跳转到下一个
                          _codeFocusNodes[index + 1].requestFocus();
                        }
                      },
                    style: TextStyle(
                      fontFamily: 'Roboto',
                      fontSize: 56.sp, // 按照Figma设计稿56px
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFF151523),
                      height: 1.0,
                    ),
                    decoration: const InputDecoration(
                      border: InputBorder.none,
                      counterText: '',
                      contentPadding: EdgeInsets.zero,
                      isDense: true,
                      isCollapsed: true,
                    ),
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    ),
                  ),
                ),
                if (_codeFocusNodes[index].hasFocus && _codeControllers[index].text.isEmpty)
                  Positioned(
                    left: 0,
                    right: 0,
                    top: 0,
                    bottom: 0,
                    child: Center(
                      child: AnimatedBuilder(
                        animation: _cursorAnimation,
                        builder: (context, child) {
                          return Opacity(
                            opacity: _cursorAnimation.value,
                            child: Container(
                              width: 3.w,
                              height: 45.h,
                              color: const Color(0xFF2A2A39),
                            ),
                          );
                        },
                      ),
                    ),
                  ),
              ],
            ),
          );
        }),
      ),
    );
  }

  Widget _buildButtonSection() {
    // 清晰的状态判断
    String buttonText;
    Color buttonColor;
    bool isDisabled;

    if (_isCodeStage) {
      // 验证码阶段
      if (_isLoading) {
        buttonText = '登录中...';
        buttonColor = const Color(0xFFF67676); // 灰粉色
        isDisabled = true;
      } else {
        buttonText = '登录';
        buttonColor = const Color(0xFFE22626); // 红色
        isDisabled = false;
      }
    } else {
      // 手机号阶段
      if (_isLoading) {
        buttonText = '发送验证证...';
        buttonColor = const Color(0xFFE22626); // 红色
        isDisabled = false;
      } else {
        buttonText = '验证码登录';
        buttonColor = const Color(0xFFE22626); // 红色
        isDisabled = false;
      }
    }

    return GestureDetector(
      onTapDown: isDisabled ? null : (_) {
        // 按下时立即高亮闪烁
        setState(() {
          _isButtonPressed = true;
          _isButtonHighlight = true;
        });
      },
      onTapUp: isDisabled ? null : (_) {
        // 松开时恢复按下状态，但保持高亮150ms
        setState(() {
          _isButtonPressed = false;
        });
        
        // 行业标准：高亮效果持续150ms后消失
        Future.delayed(const Duration(milliseconds: 150), () {
          if (mounted) {
            setState(() {
              _isButtonHighlight = false;
            });
          }
        });
      },
      onTapCancel: isDisabled ? null : () {
        // 取消时立即恢复所有状态
        setState(() {
          _isButtonPressed = false;
          _isButtonHighlight = false;
        });
      },
      onTap: isDisabled ? null : () {
        FocusScope.of(context).unfocus(); // 点击按钮时收起键盘
        _handleSubmit();
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 150), // 简化动画时长
        width: 600.w,
        height: 90.h,
        decoration: BoxDecoration(
          color: _getButtonColor(buttonColor, isDisabled),
          borderRadius: BorderRadius.circular(16.r),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFFEDF0F6).withValues(alpha: 0.4),
              blurRadius: 16.r,
              offset: Offset(0, 8.h),
              spreadRadius: 4.r,
            ),
          ],
        ),
        child: SizedBox(
          width: 600.w,
          height: 90.h, // 强制内容区域也是90.h
          child: Center(
            child: _isLoading
                ? Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.center, // 确保垂直居中
                    children: [
                      SizedBox(
                        width: 24.w,
                        height: 24.w,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2.5.w,
                        ),
                      ),
                      SizedBox(width: 12.w),
                      Text(
                        buttonText,
                        style: TextStyle(
                          fontFamily: 'PingFang SC',
                          fontSize: 28.sp,
                          fontWeight: FontWeight.w500,
                          color: Colors.white,
                          height: 1.0, // 固定行高
                        ),
                      ),
                    ],
                  )
                : Text(
                    buttonText,
                    style: TextStyle(
                      fontFamily: 'PingFang SC',
                      fontSize: 28.sp,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                      height: 1.0, // 固定行高
                    ),
                  ),
          ),
        ),
      ),
    );
  }

  Widget _buildBackToPhoneSection() {
    return Center(
      child: GestureDetector(
        onTap: _backToPhoneStage,
        child: Text(
          '重新输入手机号',
          style: TextStyle(
            fontFamily: 'PingFang SC',
            fontSize: 28.sp,
            fontWeight: FontWeight.w400,
            color: const Color(0xFF2A2A39),
            height: 30.h / 28.sp,
          ),
        ),
      ),
    );
  }

  Widget _buildAgreementSection() {
    return Center(
      child: SizedBox(
        width: 555.w + 22.w + 12.w,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            GestureDetector(
              onTap: () => _handleAgreementChange(!_isAgreed),
              behavior: HitTestBehavior.opaque, // 扩大点击区域到整个容器
              child: Transform.translate(
                offset: Offset(0, -3.h), // 减少向上偏移，让圆圈与文字第一行对齐
                child: Container(
                  width: 36.w, // 适度扩大点击容器，避免影响布局
                  height: 36.w,
                  child: Center(
                    child: Container(
                      width: 22.w, // 实际圈圈大小不变
                      height: 22.w,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: _isAgreed ? const Color(0xFF507DAF) : const Color(0xFF2A2A39),
                          width: 2.w,
                        ),
                        color: _isAgreed ? const Color(0xFF507DAF) : Colors.transparent,
                      ),
                      child: _isAgreed
                          ? Icon(Icons.check, size: 13.w, color: Colors.white)
                          : null,
                    ),
                  ),
                ),
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: RichText(
                text: TextSpan(
                  style: TextStyle(
                    fontFamily: 'PingFang SC',
                    fontSize: 22.sp,
                    height: 1.36,
                  ),
                  children: const [
                    TextSpan(
                      text: '我已阅读并向意',
                      style: TextStyle(color: Color(0xFFB6B6BD)),
                    ),
                    TextSpan(
                      text: '用户协议',
                      style: TextStyle(color: Color(0xFF507DAF)),
                    ),
                    TextSpan(
                      text: '和',
                      style: TextStyle(color: Color(0xFFB6B6BD)),
                    ),
                    TextSpan(
                      text: '隐私政策',
                      style: TextStyle(color: Color(0xFF507DAF)),
                    ),
                    TextSpan(
                      text: '，同时登录并使用有故事及其关联产品相关服务，运营商将对你提供的手机号进行验证',
                      style: TextStyle(color: Color(0xFFB6B6BD)),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}