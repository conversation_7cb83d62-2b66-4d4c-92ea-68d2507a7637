/**
 * 有故事 APP - 主页面Tab导航
 * 
 * 功能特性：
 * - 底部Tab导航
 * - 四个主要模块
 * - 精美的图标和动画
 * - B&O 极简设计风格
 */

import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { SafeAreaView } from 'react-native-safe-area-context';

// 导入常量
import { THEME } from '../constants';

// ===== Tab导航参数类型 =====
export type MainTabParamList = {
  Home: undefined;
  Story: undefined;
  Message: undefined;
  Profile: undefined;
};

// 创建Tab导航器
const Tab = createBottomTabNavigator<MainTabParamList>();

// ===== 临时页面组件 =====
const HomeScreen: React.FC = () => (
  <SafeAreaView style={styles.screenContainer}>
    <View style={styles.centerContent}>
      <Text style={styles.screenTitle}>首页</Text>
      <Text style={styles.screenSubtitle}>故事发现与分享</Text>
    </View>
  </SafeAreaView>
);

const StoryScreen: React.FC = () => (
  <SafeAreaView style={styles.screenContainer}>
    <View style={styles.centerContent}>
      <Text style={styles.screenTitle}>故事</Text>
      <Text style={styles.screenSubtitle}>创作你的故事</Text>
    </View>
  </SafeAreaView>
);

const MessageScreen: React.FC = () => (
  <SafeAreaView style={styles.screenContainer}>
    <View style={styles.centerContent}>
      <Text style={styles.screenTitle}>消息</Text>
      <Text style={styles.screenSubtitle}>互动与通知</Text>
    </View>
  </SafeAreaView>
);

const ProfileScreen: React.FC = () => (
  <SafeAreaView style={styles.screenContainer}>
    <View style={styles.centerContent}>
      <Text style={styles.screenTitle}>我的</Text>
      <Text style={styles.screenSubtitle}>个人中心</Text>
    </View>
  </SafeAreaView>
);

// ===== 自定义Tab图标组件 =====
const TabIcon: React.FC<{
  focused: boolean;
  title: string;
}> = ({ focused, title }) => {
  const getIconSymbol = (title: string) => {
    switch (title) {
      case '首页': return '○';
      case '故事': return '●';
      case '消息': return '◇';
      case '我的': return '◐';
      default: return '○';
    }
  };

  return (
    <View style={styles.tabIconContainer}>
      <Text 
        style={[
          styles.tabIcon,
          { color: focused ? THEME.colors.primary : THEME.colors.textSecondary }
        ]}
      >
        {getIconSymbol(title)}
      </Text>
    </View>
  );
};

/**
 * 主页面Tab导航组件
 */
const MainTabNavigator: React.FC = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        headerShown: false,
        tabBarShowLabel: true,
        tabBarStyle: styles.tabBar,
        tabBarActiveTintColor: THEME.colors.primary,
        tabBarInactiveTintColor: THEME.colors.textSecondary,
        tabBarLabelStyle: styles.tabBarLabel,
        tabBarIcon: ({ focused }) => {
          let title = '';
          switch (route.name) {
            case 'Home': title = '首页'; break;
            case 'Story': title = '故事'; break;
            case 'Message': title = '消息'; break;
            case 'Profile': title = '我的'; break;
          }
          return <TabIcon focused={focused} title={title} />;
        },
      })}
    >
      <Tab.Screen
        name="Home"
        component={HomeScreen}
        options={{
          tabBarLabel: '首页',
        }}
      />
      <Tab.Screen
        name="Story"
        component={StoryScreen}
        options={{
          tabBarLabel: '故事',
        }}
      />
      <Tab.Screen
        name="Message"
        component={MessageScreen}
        options={{
          tabBarLabel: '消息',
        }}
      />
      <Tab.Screen
        name="Profile"
        component={ProfileScreen}
        options={{
          tabBarLabel: '我的',
        }}
      />
    </Tab.Navigator>
  );
};

// ===== 样式定义 =====
const styles = StyleSheet.create({
  screenContainer: {
    flex: 1,
    backgroundColor: THEME.colors.background,
  },
  centerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: THEME.spacing.xl,
  },
  screenTitle: {
    fontSize: 32,
    fontWeight: '300',
    color: THEME.colors.text,
    marginBottom: THEME.spacing.md,
    letterSpacing: 2,
  },
  screenSubtitle: {
    fontSize: 16,
    color: THEME.colors.textSecondary,
    textAlign: 'center',
    letterSpacing: 1,
  },
  tabBar: {
    backgroundColor: THEME.colors.background,
    borderTopWidth: 1,
    borderTopColor: THEME.colors.separator,
    paddingTop: THEME.spacing.xs,
    paddingBottom: THEME.spacing.sm,
    height: 60,
  },
  tabBarLabel: {
    fontSize: 11,
    fontWeight: '500',
    letterSpacing: 0.5,
    marginTop: 2,
  },
  tabIconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 24,
    height: 24,
  },
  tabIcon: {
    fontSize: 18,
    fontWeight: '300',
  },
});

export default MainTabNavigator; 