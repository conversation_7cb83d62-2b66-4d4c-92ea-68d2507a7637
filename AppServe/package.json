{"name": "story-app-backend", "version": "1.0.0", "description": "有故事APP后端服务 - 正式版本", "author": "YGS Team", "private": true, "license": "UNLICENSED", "engines": {"node": ">=22.16.0", "npm": ">=11.0.0"}, "scripts": {"build": "nest build", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "lint:check": "eslint \"{src,apps,libs,test}/**/*.ts\"", "lint:fast": "eslint \"src/**/*.ts\" --fix --cache --max-warnings 0", "lint:check:fast": "eslint \"src/**/*.ts\" --cache --max-warnings 0", "type-check": "tsc --noEmit", "quality-check": "node scripts/quality-check.js", "quality-check:smart": "node scripts/quality-check.js --smart", "pre-commit": "npm run quality-check", "test": "jest --config=test/configs/jest.unit.config.js", "test:unit": "jest --config=test/configs/jest.unit.config.js --coverage", "test:unit:watch": "jest --config=test/configs/jest.unit.config.js --watch", "test:integration": "jest --config=test/configs/jest.integration.config.js --coverage --verbose", "test:integration:watch": "jest --config=test/configs/jest.integration.config.js --watch", "test:e2e": "jest --config=test/configs/jest.e2e.config.js --verbose", "test:e2e:watch": "jest --config=test/configs/jest.e2e.config.js --watch", "test:all": "npm run test:unit && npm run test:integration && npm run test:e2e", "test:cleanup:integration": "NODE_ENV=integration-test node -e 'import(\"./dist/testing/test-data-cleanup.service.js\").then(m => new m.TestDataCleanupService().cleanupAllTestData())'", "test:cleanup:e2e": "NODE_ENV=e2e-test node -e 'import(\"./dist/testing/test-data-cleanup.service.js\").then(m => new m.TestDataCleanupService().cleanupAllTestData())'", "test:enterprise": "echo '🏢 企业级测试套件执行...' && npm run test:unit && npm run test:integration && npm run test:cleanup:integration && npm run test:e2e && npm run test:cleanup:e2e", "test:ci": "jest --ci --coverage --watchAll=false --config=jest.config.js", "test:coverage:monitor": "node test/scripts/test-coverage-monitor.js", "test:api:full": "node test/scripts/run-api-tests.js", "test:sms:enhanced": "node test/scripts/test-sms-enhanced.js", "migration:run": "typeorm migration:run -d ./ormconfig.js", "migration:revert": "typeorm migration:revert -d ./ormconfig.js", "migration:generate": "typeorm migration:generate -d ./ormconfig.js", "docker:start": "docker-compose up -d && echo '🚀 启动YGS混合环境：本地Redis + 阿里云RDS'", "docker:redis": "docker-compose up -d redis && echo '✅ Redis服务已启动'", "docker:stop": "docker-compose down && echo '⏹️ Docker服务已停止'", "docker:clean": "docker-compose down -v && echo '🧹 Docker服务和数据卷已清理'", "env:dev": "cp .env.development .env && echo '✅ 已切换到开发环境（混合模式：本地服务+云端资源）'", "env:prod": "cp .env.production .env && echo '✅ 已切换到生产环境配置'", "test:env:check": "echo '📊 测试环境配置检查:' && echo '集成测试数据库:' && grep DB_NAME .env.integration.test && echo 'E2E测试数据库:' && grep DB_NAME .env.e2e.test", "env:check": "echo '当前环境配置:' && head -5 .env", "check:deps": "grep -r 'JwtAuthGuard' src/ --include='*.ts' && echo '请确保相关模块导入了AuthModule'", "oss:configure": "node scripts/configure-oss.js", "oss:test": "node scripts/test-oss-connection.js", "oss:test-advanced": "node scripts/test-oss-advanced.js", "seed:all": "node scripts/seed-database.js all", "seed:users": "node scripts/seed-database.js users", "seed:clean": "node scripts/seed-database.js clean", "seed:stats": "node scripts/seed-database.js stats", "seed:validate": "node scripts/seed-database.js validate", "api:collection": "node scripts/generate-api-collection.js", "api:docs": "echo 'Swagger文档: http://localhost:3000/api/docs' && echo 'OpenAPI JSON: http://localhost:3000/api/docs-json'", "health:check": "curl -s http://localhost:3000/api/v1/health || echo '❌ 服务器未运行'", "health:detailed": "curl -s http://localhost:3000/api/v1/health/detailed || echo '❌ 服务器未运行'", "rds:test": "node scripts/test-rds-connection.js && echo '✅ 阿里云RDS连接正常'", "prepare": "husky"}, "dependencies": {"@alicloud/dysmsapi20170525": "^4.1.2", "@alicloud/openapi-client": "^0.4.15", "@alicloud/tea-util": "^1.4.10", "@fastify/multipart": "^9.0.3", "@fastify/static": "^8.2.0", "@keyv/redis": "^4.6.0", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^11.1.3", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.3", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.3", "@nestjs/platform-fastify": "^11.1.3", "@nestjs/swagger": "^11.2.0", "@nestjs/terminus": "^11.0.0", "@nestjs/throttler": "^6.4.0", "@nestjs/typeorm": "^11.0.0", "@types/ali-oss": "^6.16.11", "@types/bcrypt": "^6.0.0", "ali-oss": "^6.23.0", "bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "cache-manager": "^6.4.3", "cache-manager-redis-store": "^3.0.1", "cache-manager-redis-yet": "^5.1.5", "cacheable": "^1.10.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "compression": "^1.7.4", "dotenv": "^16.6.1", "helmet": "^7.0.0", "ioredis": "^5.3.0", "multer": "^1.4.5-lts.1", "nest-winston": "^1.9.7", "openai": "^4.0.0", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.11.0", "redis": "^4.6.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "typeorm": "^0.3.17", "uuid": "^9.0.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.28.0", "@babel/plugin-transform-class-properties": "^7.27.1", "@babel/plugin-transform-runtime": "^7.28.0", "@babel/preset-env": "^7.28.0", "@babel/preset-typescript": "^7.27.1", "@babel/runtime": "^7.27.6", "@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.1.3", "@testing-library/jest-dom": "^6.6.3", "@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.2", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/multer": "^1.4.7", "@types/node": "^20.3.1", "@types/passport-jwt": "^3.0.9", "@types/passport-local": "^1.0.35", "@types/pg": "^8.10.2", "@types/supertest": "^2.0.12", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "husky": "^9.1.7", "jest": "^29.5.0", "jest-extended": "^6.0.0", "jest-html-reporters": "^3.1.7", "jest-junit": "^16.0.0", "lint-staged": "^16.1.2", "nock": "^14.0.5", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "sqlite3": "^5.1.7", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "lint-staged": {"*.ts": ["eslint --fix --max-warnings 0", "git add"]}}