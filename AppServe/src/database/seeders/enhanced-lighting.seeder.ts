/**
 * 企业级点亮申请种子数据
 *
 * 基于 YGS v1.0.0 点亮系统双重排他性约束设计
 * 涵盖各种申请状态和业务场景
 */

import { USER_ROLES } from "./enhanced-user.seeder";
import { STORY_TITLES } from "./enhanced-story.seeder";
import { CHARACTER_NAMES } from "./enhanced-character.seeder";

export interface EnhancedLightRequestSeedData {
  storyTitle: string;
  characterName: string;
  requesterUsername: string;
  phoneVerification?: string; // 脱敏手机号
  hasPhoneVerification: boolean;
  message?: string;
  status:
    | "active"
    | "cancelled"
    | "pending"
    | "approved"
    | "rejected"
    | "expired";
  daysAgo: number; // 申请发起时间（几天前）
  processedDaysAgo?: number; // 处理时间（几天前）
}

export const ENHANCED_LIGHT_REQUEST_SEED_DATA: EnhancedLightRequestSeedData[] =
  [
    // ========== 成功的点亮申请（已完成） ==========
    {
      storyTitle: STORY_TITLES.UNIVERSITY_PROMISE,
      characterName: CHARACTER_NAMES.LIMING_UNIVERSITY,
      requesterUsername: USER_ROLES.LI_XIAOHONG,
      phoneVerification: "138****8002",
      hasPhoneVerification: true,
      message:
        "李明，我是小红！还记得我吗？大学时我们一起参加过编程比赛，你还帮我调试代码到凌晨。看到这个故事，满满都是回忆。",
      status: "approved",
      daysAgo: 30,
      processedDaysAgo: 28,
    },
    {
      storyTitle: STORY_TITLES.RAINY_NIGHT,
      characterName: CHARACTER_NAMES.LIMING_RAINY,
      requesterUsername: USER_ROLES.LI_XIAOHONG,
      phoneVerification: "138****8002",
      hasPhoneVerification: true,
      message:
        "明远，我就是那个雨夜陪你的李明。看到这个故事，我也哭了。那些年的友谊，是我最珍贵的回忆。",
      status: "approved",
      daysAgo: 25,
      processedDaysAgo: 24,
    },
    {
      storyTitle: STORY_TITLES.UNIVERSITY_PROMISE,
      characterName: CHARACTER_NAMES.ZHAOHUA_UNIVERSITY,
      requesterUsername: USER_ROLES.ZHOU_FAXIAN,
      phoneVerification: "138****8007",
      hasPhoneVerification: true,
      message:
        "我是赵华！看到十年之约的故事，真的很感动。虽然现在在老家当老师，但一直记得我们的约定。",
      status: "approved",
      daysAgo: 20,
      processedDaysAgo: 19,
    },
    {
      storyTitle: STORY_TITLES.STUDENT_XIAOYU,
      characterName: CHARACTER_NAMES.XIAOYU,
      requesterUsername: USER_ROLES.ZHOU_FAXIAN,
      phoneVerification: "138****8007",
      hasPhoneVerification: true,
      message:
        "王老师，我是小雨。看到您还记得我，真的很感动。您的鼓励改变了我的人生，现在我也在用医术帮助更多的人。",
      status: "approved",
      daysAgo: 15,
      processedDaysAgo: 14,
    },
    {
      storyTitle: STORY_TITLES.DRAMA_MEMORIES,
      characterName: CHARACTER_NAMES.XIAOXIAO,
      requesterUsername: USER_ROLES.LI_XIAOHONG,
      phoneVerification: "138****8002",
      hasPhoneVerification: true,
      message:
        "哈哈，我就是那个爱哭鬼晓晓！现在看到感人的故事还是会哭。那些追剧的日子真的好怀念啊！",
      status: "approved",
      daysAgo: 10,
      processedDaysAgo: 9,
    },

    // ========== 待处理的申请（active） ==========
    {
      storyTitle: STORY_TITLES.UNIVERSITY_PROMISE,
      characterName: CHARACTER_NAMES.WANGQIANG,
      requesterUsername: USER_ROLES.CHEN_VIP,
      phoneVerification: "166****8665",
      hasPhoneVerification: true,
      message:
        "明远，我是王强！看到你写的故事，热泪盈眶。创业这些年，一直记得大学时的梦想和兄弟情。期待今年6月的聚会！",
      status: "active",
      daysAgo: 2,
    },
    {
      storyTitle: STORY_TITLES.TEACHING_REGRET,
      characterName: CHARACTER_NAMES.XIAOJUN,
      requesterUsername: USER_ROLES.ZHAO_XIAOFANG,
      hasPhoneVerification: false,
      message:
        "王老师，我是小军。虽然您可能不记得我了，但您的故事让我很感动。当年的事早就过去了，您是个好老师。",
      status: "active",
      daysAgo: 1,
    },
    {
      storyTitle: STORY_TITLES.SORRY_MOM,
      characterName: CHARACTER_NAMES.MOTHER,
      requesterUsername: USER_ROLES.LIU_XIAOXIN,
      phoneVerification: "138****8005",
      hasPhoneVerification: true,
      message:
        "孩子，看到你的故事，妈妈也哭了。不管发生什么，记得家永远是你的港湾。妈妈永远爱你。",
      status: "active",
      daysAgo: 3,
    },

    // ========== 已拒绝的申请（rejected - 无通知） ==========
    {
      storyTitle: STORY_TITLES.UNIVERSITY_PROMISE,
      characterName: CHARACTER_NAMES.LIMING_UNIVERSITY,
      requesterUsername: USER_ROLES.SUN_WENTI,
      phoneVerification: "138****8006",
      hasPhoneVerification: true,
      message: "我好像也是李明，让我点亮试试。",
      status: "rejected",
      daysAgo: 8,
      processedDaysAgo: 7,
    },
    {
      storyTitle: STORY_TITLES.STUDENT_XIAOYU,
      characterName: CHARACTER_NAMES.XIAOYU,
      requesterUsername: USER_ROLES.ZHAO_XIAOFANG,
      hasPhoneVerification: false,
      message: "我觉得我可能是小雨，虽然我不是医生，但故事很感人。",
      status: "rejected",
      daysAgo: 12,
      processedDaysAgo: 11,
    },

    // ========== 已取消的申请（cancelled） ==========
    {
      storyTitle: STORY_TITLES.DRAMA_MEMORIES,
      characterName: CHARACTER_NAMES.XIAOMEI,
      requesterUsername: USER_ROLES.ZHAO_XIAOFANG,
      hasPhoneVerification: false,
      message: "我也爱追剧，可能我就是小美吧。",
      status: "cancelled",
      daysAgo: 5,
      processedDaysAgo: 4,
    },

    // ========== 已过期的申请（expired - 7天） ==========
    {
      storyTitle: STORY_TITLES.STARTUP_JOURNEY,
      characterName: CHARACTER_NAMES.WANGQIANG,
      requesterUsername: USER_ROLES.ZHOU_FAXIAN,
      phoneVerification: "138****8007",
      hasPhoneVerification: true,
      message: "看到创业故事很感动，我也有个朋友叫王强，可能是我。",
      status: "expired",
      daysAgo: 10, // 超过7天自动过期
    },
    {
      storyTitle: STORY_TITLES.DRAMA_MEMORIES,
      characterName: CHARACTER_NAMES.ALIN,
      requesterUsername: USER_ROLES.LI_XIAOHONG,
      hasPhoneVerification: false,
      message: "阿琳是不是我啊？我现在也是律师，大学时也爱分析剧情。",
      status: "expired",
      daysAgo: 9,
    },

    // ========== 排他性冲突示例 ==========
    // 注意：这些申请会因为违反排他性约束而被系统阻止
    // 1. 人物已被点亮（李明已被李小红点亮，其他人无法再申请）
    // 2. 用户已点亮该作者的其他人物（李小红已点亮张明远的李明，无法再点亮其他人物）
  ];

// 导出申请统计数据，方便测试验证
export const LIGHT_REQUEST_STATS = {
  total: ENHANCED_LIGHT_REQUEST_SEED_DATA.length,
  approved: ENHANCED_LIGHT_REQUEST_SEED_DATA.filter(
    (r) => r.status === "approved",
  ).length,
  active: ENHANCED_LIGHT_REQUEST_SEED_DATA.filter((r) => r.status === "active")
    .length,
  rejected: ENHANCED_LIGHT_REQUEST_SEED_DATA.filter(
    (r) => r.status === "rejected",
  ).length,
  cancelled: ENHANCED_LIGHT_REQUEST_SEED_DATA.filter(
    (r) => r.status === "cancelled",
  ).length,
  expired: ENHANCED_LIGHT_REQUEST_SEED_DATA.filter(
    (r) => r.status === "expired",
  ).length,
  withPhone: ENHANCED_LIGHT_REQUEST_SEED_DATA.filter(
    (r) => r.hasPhoneVerification,
  ).length,
};
