/**
 * NotificationsService 单元测试
 * 测试通知系统的核心功能，包括创建、查询、更新、删除等操作
 */

import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import type { Repository } from "typeorm";
import { NotFoundException } from "@nestjs/common";
import { NotificationsService } from "./notifications.service";
import { Notification, NotificationType } from "./entities/notification.entity";

describe("NotificationsService", () => {
  let service: NotificationsService;
  let mockRepository: jest.Mocked<Repository<Notification>>;

  // Mock数据
  const mockNotification = {
    id: "notification-id",
    userId: "user-id",
    type: NotificationType.CHARACTER_LIGHTING,
    title: "测试通知",
    content: "测试通知内容",
    relatedId: "related-id",
    actionUrl: "/test",
    isRead: false,
    readAt: undefined,
    createdAt: new Date(),
    user: {
      id: "user-id",
      username: "testuser",
      nickname: "测试用户",
      phone: "13800138000",
      email: "<EMAIL>",
    },
  } as unknown as Notification;

  const mockCreateNotificationDto = {
    userId: "user-id",
    type: NotificationType.CHARACTER_LIGHTING,
    title: "测试通知",
    content: "测试通知内容",
    relatedId: "related-id",
    actionUrl: "/test",
  };

  beforeEach(async () => {
    const mockRepositoryMethods = {
      create: jest.fn(),
      save: jest.fn(),
      findOne: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
      createQueryBuilder: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NotificationsService,
        {
          provide: getRepositoryToken(Notification),
          useValue: mockRepositoryMethods,
        },
      ],
    }).compile();

    service = module.get<NotificationsService>(NotificationsService);
    mockRepository = module.get(
      getRepositoryToken(Notification),
    ) as jest.Mocked<Repository<Notification>>;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  describe("createNotification", () => {
    it("should create a notification successfully", async () => {
      mockRepository.create.mockReturnValue(mockNotification);
      mockRepository.save.mockResolvedValue(mockNotification);

      const result = await service.createNotification(
        mockCreateNotificationDto,
      );

      expect(result).toEqual(mockNotification);
      expect(mockRepository.create).toHaveBeenCalledWith(
        mockCreateNotificationDto,
      );
      expect(mockRepository.save).toHaveBeenCalledWith(mockNotification);
    });
  });

  describe("getUserNotifications", () => {
    let mockQueryBuilder: {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      where: jest.MockedFunction<any>;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      andWhere: jest.MockedFunction<any>;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      orderBy: jest.MockedFunction<any>;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      skip: jest.MockedFunction<any>;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      take: jest.MockedFunction<any>;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      getManyAndCount: jest.MockedFunction<any>;
    };

    beforeEach(() => {
      // 重新创建QueryBuilder mock以确保所有方法返回this
      mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn(),
      };

      // 确保所有链式方法都正确返回this
      mockQueryBuilder.where.mockImplementation(() => mockQueryBuilder);
      mockQueryBuilder.andWhere.mockImplementation(() => mockQueryBuilder);
      mockQueryBuilder.orderBy.mockImplementation(() => mockQueryBuilder);
      mockQueryBuilder.skip.mockImplementation(() => mockQueryBuilder);
      mockQueryBuilder.take.mockImplementation(() => mockQueryBuilder);

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      mockRepository.createQueryBuilder.mockReturnValue(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        mockQueryBuilder as any,
      );
    });

    it("should get user notifications with pagination", async () => {
      const notifications = [mockNotification];
      const total = 1;
      const unreadCount = 1;

      mockQueryBuilder.getManyAndCount.mockResolvedValue([
        notifications,
        total,
      ]);
      mockRepository.count.mockResolvedValue(unreadCount);

      const result = await service.getUserNotifications(
        "user-id",
        1,
        20,
        false,
      );

      expect(result).toEqual({
        notifications,
        total,
        unreadCount,
        hasNext: false,
      });
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        "notification.userId = :userId",
        { userId: "user-id" },
      );
      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith(
        "notification.createdAt",
        "DESC",
      );
      expect(mockQueryBuilder.skip).toHaveBeenCalledWith(0);
      expect(mockQueryBuilder.take).toHaveBeenCalledWith(20);
    });

    it("should get only unread notifications when unreadOnly is true", async () => {
      const notifications = [mockNotification];
      const total = 1;
      const unreadCount = 1;

      mockQueryBuilder.getManyAndCount.mockResolvedValue([
        notifications,
        total,
      ]);
      mockRepository.count.mockResolvedValue(unreadCount);

      await service.getUserNotifications("user-id", 1, 20, true);

      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "notification.isRead = false",
      );
    });

    it("should calculate hasNext correctly", async () => {
      const notifications = Array(20).fill(mockNotification);
      const total = 30;
      const unreadCount = 5;

      mockQueryBuilder.getManyAndCount.mockResolvedValue([
        notifications,
        total,
      ]);
      mockRepository.count.mockResolvedValue(unreadCount);

      const result = await service.getUserNotifications(
        "user-id",
        1,
        20,
        false,
      );

      expect(result.hasNext).toBe(true);
    });
  });

  describe("markAsRead", () => {
    it("should mark notification as read successfully", async () => {
      const unreadNotification = {
        ...mockNotification,
        isRead: false,
      } as unknown as Notification;
      mockRepository.findOne.mockResolvedValue(unreadNotification);
      mockRepository.update.mockResolvedValue({ affected: 1 } as never);

      await service.markAsRead("notification-id", "user-id");

      expect(mockRepository.findOne).toHaveBeenCalledWith({
        where: { id: "notification-id", userId: "user-id" },
      });
      expect(mockRepository.update).toHaveBeenCalledWith("notification-id", {
        isRead: true,
        readAt: expect.any(Date),
      });
    });

    it("should throw NotFoundException when notification not found", async () => {
      mockRepository.findOne.mockResolvedValue(null);

      await expect(
        service.markAsRead("notification-id", "user-id"),
      ).rejects.toThrow(NotFoundException);
      await expect(
        service.markAsRead("notification-id", "user-id"),
      ).rejects.toThrow("通知不存在");
    });

    it("should not update already read notification", async () => {
      const readNotification = {
        ...mockNotification,
        isRead: true,
      } as unknown as Notification;
      mockRepository.findOne.mockResolvedValue(readNotification);

      await service.markAsRead("notification-id", "user-id");

      expect(mockRepository.update).not.toHaveBeenCalled();
    });
  });

  describe("markAllAsRead", () => {
    it("should mark all user notifications as read", async () => {
      mockRepository.update.mockResolvedValue({ affected: 5 } as never);

      await service.markAllAsRead("user-id");

      expect(mockRepository.update).toHaveBeenCalledWith(
        { userId: "user-id", isRead: false },
        { isRead: true, readAt: expect.any(Date) },
      );
    });
  });

  describe("deleteNotification", () => {
    it("should delete notification successfully", async () => {
      mockRepository.delete.mockResolvedValue({ affected: 1 } as never);

      await service.deleteNotification("notification-id", "user-id");

      expect(mockRepository.delete).toHaveBeenCalledWith({
        id: "notification-id",
        userId: "user-id",
      });
    });

    it("should throw NotFoundException when notification not found", async () => {
      mockRepository.delete.mockResolvedValue({ affected: 0 } as never);

      await expect(
        service.deleteNotification("notification-id", "user-id"),
      ).rejects.toThrow(NotFoundException);
      await expect(
        service.deleteNotification("notification-id", "user-id"),
      ).rejects.toThrow("通知不存在");
    });
  });

  describe("deleteNotifications", () => {
    it("should delete multiple notifications", async () => {
      const notificationIds = ["id1", "id2", "id3"];
      mockRepository.delete.mockResolvedValue({ affected: 3 } as never);

      await service.deleteNotifications(notificationIds, "user-id");

      expect(mockRepository.delete).toHaveBeenCalledWith({
        id: notificationIds,
        userId: "user-id",
      });
    });
  });

  describe("getUnreadCount", () => {
    it("should return unread notifications count", async () => {
      mockRepository.count.mockResolvedValue(5);

      const result = await service.getUnreadCount("user-id");

      expect(result).toBe(5);
      expect(mockRepository.count).toHaveBeenCalledWith({
        where: { userId: "user-id", isRead: false },
      });
    });
  });

  describe("createCharacterLightingNotification", () => {
    it("should create character lighting notification", async () => {
      const mockCreatedNotification = {
        ...mockNotification,
        type: NotificationType.CHARACTER_LIGHTING,
        title: "有人点亮了你的人物",
        content: '张三 点亮了你的人物"测试人物"',
        relatedId: "lighting-id",
        actionUrl: "/characters/lightings/lighting-id",
      } as unknown as Notification;

      mockRepository.create.mockReturnValue(mockCreatedNotification);
      mockRepository.save.mockResolvedValue(mockCreatedNotification);

      const result = await service.createCharacterLightingNotification(
        "user-id",
        "张三",
        "测试人物",
        "lighting-id",
      );

      expect(result).toEqual(mockCreatedNotification);
      expect(mockRepository.create).toHaveBeenCalledWith({
        userId: "user-id",
        type: NotificationType.CHARACTER_LIGHTING,
        title: "有人点亮了你的人物",
        content: '张三 点亮了你的人物"测试人物"',
        relatedId: "lighting-id",
        actionUrl: "/characters/lightings/lighting-id",
      });
    });
  });

  describe("createRelationshipConfirmedNotification", () => {
    it("should create relationship confirmed notification", async () => {
      const mockCreatedNotification = {
        ...mockNotification,
        type: NotificationType.RELATIONSHIP_CONFIRMED,
        title: "关系确认",
        content: "李四 确认了与你的关系",
        relatedId: "relationship-id",
        actionUrl: "/relationships/relationship-id",
      } as unknown as Notification;

      mockRepository.create.mockReturnValue(mockCreatedNotification);
      mockRepository.save.mockResolvedValue(mockCreatedNotification);

      const result = await service.createRelationshipConfirmedNotification(
        "user-id",
        "李四",
        "relationship-id",
      );

      expect(result).toEqual(mockCreatedNotification);
      expect(mockRepository.create).toHaveBeenCalledWith({
        userId: "user-id",
        type: NotificationType.RELATIONSHIP_CONFIRMED,
        title: "关系确认",
        content: "李四 确认了与你的关系",
        relatedId: "relationship-id",
        actionUrl: "/relationships/relationship-id",
      });
    });
  });

  describe("createStorySharedNotification", () => {
    it("should create story shared notification", async () => {
      const mockCreatedNotification = {
        ...mockNotification,
        type: NotificationType.STORY_SHARED,
        title: "故事被分享",
        content: '王五 分享了故事"测试故事"',
        relatedId: "story-id",
        actionUrl: "/stories/story-id",
      } as unknown as Notification;

      mockRepository.create.mockReturnValue(mockCreatedNotification);
      mockRepository.save.mockResolvedValue(mockCreatedNotification);

      const result = await service.createStorySharedNotification(
        "user-id",
        "王五",
        "测试故事",
        "story-id",
      );

      expect(result).toEqual(mockCreatedNotification);
      expect(mockRepository.create).toHaveBeenCalledWith({
        userId: "user-id",
        type: NotificationType.STORY_SHARED,
        title: "故事被分享",
        content: '王五 分享了故事"测试故事"',
        relatedId: "story-id",
        actionUrl: "/stories/story-id",
      });
    });
  });

  describe("createStoryLikedNotification", () => {
    it("should create story liked notification", async () => {
      const mockCreatedNotification = {
        ...mockNotification,
        type: NotificationType.STORY_LIKED,
        title: "故事被点赞",
        content: '赵六 点赞了你的故事"测试故事"',
        relatedId: "story-id",
        actionUrl: "/stories/story-id",
      } as unknown as Notification;

      mockRepository.create.mockReturnValue(mockCreatedNotification);
      mockRepository.save.mockResolvedValue(mockCreatedNotification);

      const result = await service.createStoryLikedNotification(
        "user-id",
        "赵六",
        "测试故事",
        "story-id",
      );

      expect(result).toEqual(mockCreatedNotification);
      expect(mockRepository.create).toHaveBeenCalledWith({
        userId: "user-id",
        type: NotificationType.STORY_LIKED,
        title: "故事被点赞",
        content: '赵六 点赞了你的故事"测试故事"',
        relatedId: "story-id",
        actionUrl: "/stories/story-id",
      });
    });
  });

  describe("createStoryCommentedNotification", () => {
    it("should create story commented notification", async () => {
      const mockCreatedNotification = {
        ...mockNotification,
        type: NotificationType.STORY_COMMENTED,
        title: "故事有新评论",
        content: '孙七 评论了你的故事"测试故事"',
        relatedId: "comment-id",
        actionUrl: "/stories/story-id#comment-comment-id",
      } as unknown as Notification;

      mockRepository.create.mockReturnValue(mockCreatedNotification);
      mockRepository.save.mockResolvedValue(mockCreatedNotification);

      const result = await service.createStoryCommentedNotification(
        "user-id",
        "孙七",
        "测试故事",
        "comment-id",
        "story-id",
      );

      expect(result).toEqual(mockCreatedNotification);
      expect(mockRepository.create).toHaveBeenCalledWith({
        userId: "user-id",
        type: NotificationType.STORY_COMMENTED,
        title: "故事有新评论",
        content: '孙七 评论了你的故事"测试故事"',
        relatedId: "comment-id",
        actionUrl: "/stories/story-id#comment-comment-id",
      });
    });
  });

  describe("cleanupOldNotifications", () => {
    it("should cleanup old read notifications", async () => {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      mockRepository.delete.mockResolvedValue({ affected: 10 } as never);

      await service.cleanupOldNotifications();

      expect(mockRepository.delete).toHaveBeenCalledWith({
        isRead: true,
        readAt: expect.any(Date),
      });
    });
  });
});
