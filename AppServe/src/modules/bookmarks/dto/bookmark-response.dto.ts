import type {
  BookmarkCategory,
  BookmarkStatus,
} from "../entities/bookmark.entity";

/**
 * 收藏响应DTO
 */
export class BookmarkResponseDto {
  id: string;
  userId: string;
  storyId: string;
  category: BookmarkCategory;
  customCategoryName?: string;
  status: BookmarkStatus;
  note?: string;
  storySnapshot: {
    title: string;
    summary?: string;
    coverImage?: string;
    authorName: string;
  };
  metadata?: Record<string, unknown>;
  createdAt: Date;
  updatedAt: Date;

  // 关联的故事信息（如果故事仍存在）
  story?: {
    id: string;
    title: string;
    summary?: string;
    coverImage?: string;
    authorId: string;
    authorName: string;
    status: string;
    isPublic: boolean;
  };
}

/**
 * 收藏分类统计响应DTO
 */
export class BookmarkCategoryStatsDto {
  category: BookmarkCategory;
  customCategoryName?: string;
  count: number;
}

/**
 * 收藏统计响应DTO
 */
export class BookmarkStatsResponseDto {
  totalBookmarks: number;
  activeBookmarks: number;
  archivedBookmarks: number;
  invalidBookmarks: number;
  categoryStats: BookmarkCategoryStatsDto[];
}
