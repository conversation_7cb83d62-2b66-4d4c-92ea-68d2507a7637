import type { MigrationInterface, QueryRunner } from "typeorm";

export class EnhanceUserAnomalyStatusManagement1752392409157
  implements MigrationInterface
{
  name = "EnhanceUserAnomalyStatusManagement1752392409157";

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 添加用户异常状态管理字段
    await queryRunner.query(
      `ALTER TABLE "users" ADD COLUMN "anomaly_status" varchar(20) NOT NULL DEFAULT 'normal'`,
    );
    await queryRunner.query(
      `COMMENT ON COLUMN "users"."anomaly_status" IS '用户异常状态：normal-正常，warning-警告，restricted-受限，suspended-暂停'`,
    );

    // 添加异常行为检测字段
    await queryRunner.query(
      `ALTER TABLE "users" ADD COLUMN "anomaly_warning_count" integer NOT NULL DEFAULT 0`,
    );
    await queryRunner.query(
      `COMMENT ON COLUMN "users"."anomaly_warning_count" IS '异常警告累计次数'`,
    );

    await queryRunner.query(
      `ALTER TABLE "users" ADD COLUMN "anomaly_restriction_count" integer NOT NULL DEFAULT 0`,
    );
    await queryRunner.query(
      `COMMENT ON COLUMN "users"."anomaly_restriction_count" IS '异常限制累计次数'`,
    );

    await queryRunner.query(
      `ALTER TABLE "users" ADD COLUMN "lighting_restricted_until" TIMESTAMP NULL`,
    );
    await queryRunner.query(
      `COMMENT ON COLUMN "users"."lighting_restricted_until" IS '点亮申请限制到期时间'`,
    );

    await queryRunner.query(
      `ALTER TABLE "users" ADD COLUMN "last_invalid_lighting_attempt" TIMESTAMP NULL`,
    );
    await queryRunner.query(
      `COMMENT ON COLUMN "users"."last_invalid_lighting_attempt" IS '最后一次无效点亮申请时间'`,
    );

    await queryRunner.query(
      `ALTER TABLE "users" ADD COLUMN "daily_lighting_attempts" integer NOT NULL DEFAULT 0`,
    );
    await queryRunner.query(
      `COMMENT ON COLUMN "users"."daily_lighting_attempts" IS '今日点亮申请次数'`,
    );

    await queryRunner.query(
      `ALTER TABLE "users" ADD COLUMN "lighting_attempt_reset_date" date NOT NULL DEFAULT CURRENT_DATE`,
    );
    await queryRunner.query(
      `COMMENT ON COLUMN "users"."lighting_attempt_reset_date" IS '点亮申请计数重置日期'`,
    );

    // 修改默认值
    await queryRunner.query(
      `ALTER TABLE "users" ALTER COLUMN "profile_display_settings" SET DEFAULT '{"showBirthday":false,"showBio":true,"showStatistics":true,"showCharacters":true,"showTimeline":true}'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 回滚字段添加
    await queryRunner.query(
      `ALTER TABLE "users" DROP COLUMN "lighting_attempt_reset_date"`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" DROP COLUMN "daily_lighting_attempts"`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" DROP COLUMN "last_invalid_lighting_attempt"`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" DROP COLUMN "lighting_restricted_until"`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" DROP COLUMN "anomaly_restriction_count"`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" DROP COLUMN "anomaly_warning_count"`,
    );
    await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "anomaly_status"`);

    // 回滚默认值
    await queryRunner.query(
      `ALTER TABLE "users" ALTER COLUMN "profile_display_settings" SET DEFAULT '{"showBio": true, "showBirthday": false, "showTimeline": true, "showCharacters": true, "showStatistics": true}'`,
    );
  }
}
