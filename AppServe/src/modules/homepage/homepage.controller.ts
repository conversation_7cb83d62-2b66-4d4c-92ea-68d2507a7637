import {
  Controller,
  Get,
  Put,
  Param,
  Body,
  UseGuards,
  Request,
  HttpStatus,
} from "@nestjs/common";
import { ApiTags, ApiOperation, ApiParam, ApiBody } from "@nestjs/swagger";
import { HomepageService } from "./homepage.service";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { ApiResponse } from "../../common/dto/api-response.dto";
import { UpdateDisplaySettingsDto, UpdateHomepageSettingsDto } from "./dto";
import type { HomepageDataResponseDto, PersonalStatsResponseDto } from "./dto";

/**
 * 个人主页管理控制器
 * 提供个人主页数据获取、展示设置管理等功能
 */
@ApiTags("个人主页管理")
@Controller("homepage")
export class HomepageController {
  constructor(private readonly homepageService: HomepageService) {}

  // ==================== 个人主页数据获取 ====================

  /**
   * 获取个人主页完整数据
   * API: GET /api/homepage/:userId
   */
  @Get(":userId")
  @ApiOperation({
    summary: "获取个人主页完整数据",
    description:
      "获取指定用户的个人主页完整数据，包括基本信息、统计数据、内容预览等",
  })
  @ApiParam({
    name: "userId",
    description: "目标用户ID",
    example: "123e4567-e89b-12d3-a456-426614174000",
  })
  async getHomepageData(
    @Param("userId") userId: string,
    @Request() req?: { user?: { id: string } },
  ): Promise<{
    success: boolean;
    data: HomepageDataResponseDto;
    message: string;
  }> {
    const currentUserId = req?.user?.id;
    const homepageData = await this.homepageService.getHomepageData(
      userId,
      currentUserId,
    );

    return ApiResponse.success(
      homepageData,
      "获取个人主页数据成功",
      HttpStatus.OK,
    );
  }

  // ==================== 个人主页展示设置管理 ====================

  /**
   * 更新个人主页展示设置
   * API: PUT /api/homepage/display-settings
   */
  @Put("display-settings")
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: "更新个人主页展示设置",
    description: "更新当前用户的个人主页展示设置，控制各项内容的显示状态",
  })
  @ApiBody({
    type: "object",
    description: "展示设置更新数据",
    schema: {
      type: "object",
      properties: {
        showBirthday: {
          type: "boolean",
          description: "是否展示生日",
          example: true,
        },
        showBio: {
          type: "boolean",
          description: "是否展示个人简介",
          example: true,
        },
        showStatistics: {
          type: "boolean",
          description: "是否展示统计信息",
          example: true,
        },
        showCharacters: {
          type: "boolean",
          description: "是否展示人物",
          example: true,
        },
        showTimeline: {
          type: "boolean",
          description: "是否展示时间线",
          example: true,
        },
        showStories: {
          type: "boolean",
          description: "是否展示故事",
          example: true,
        },
        showBookmarks: {
          type: "boolean",
          description: "是否展示收藏",
          example: false,
        },
        showFollowList: {
          type: "boolean",
          description: "是否展示关注列表",
          example: false,
        },
        showReferenceCollections: {
          type: "boolean",
          description: "是否展示引用集合",
          example: true,
        },
      },
    },
  })
  async updateDisplaySettings(
    @Request() req: { user: { id: string } },
    @Body() updateDto: UpdateDisplaySettingsDto,
  ): Promise<{
    success: boolean;
    data: { message: string };
    message: string;
  }> {
    const userId = req.user.id;
    const result = await this.homepageService.updateDisplaySettings(
      userId,
      updateDto,
    );

    return ApiResponse.success(result, "展示设置更新成功", HttpStatus.OK);
  }

  /**
   * 更新个人主页高级设置
   * API: PUT /api/homepage/settings
   */
  @Put("settings")
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: "更新个人主页高级设置",
    description: "更新当前用户的个人主页高级设置，包括主题、布局、个性化内容等",
  })
  @ApiBody({
    type: "object",
    description: "高级设置更新数据",
    schema: {
      type: "object",
      properties: {
        theme: {
          type: "string",
          enum: ["light", "dark", "auto"],
          description: "主页主题",
          example: "auto",
        },
        layout: {
          type: "string",
          enum: ["classic", "modern", "minimal", "timeline_focused"],
          description: "主页布局",
          example: "modern",
        },
        customBio: {
          type: "string",
          description: "个性化简介",
          example: "一个热爱创作的故事写手",
        },
        backgroundImage: {
          type: "string",
          description: "主页背景图片URL",
          example: "https://example.com/background.jpg",
        },
        pinnedItems: {
          type: "array",
          items: { type: "string" },
          description: "置顶内容ID列表",
          example: ["story-123", "character-456"],
        },
        socialLinks: {
          type: "array",
          description: "社交媒体链接",
          items: {
            type: "object",
            properties: {
              platform: { type: "string", description: "平台名称" },
              url: { type: "string", description: "链接地址" },
              display: { type: "boolean", description: "是否显示" },
            },
          },
        },
        showBirthday: {
          type: "boolean",
          description: "是否展示生日",
        },
        showBio: {
          type: "boolean",
          description: "是否展示个人简介",
        },
        showStatistics: {
          type: "boolean",
          description: "是否展示统计信息",
        },
        showCharacters: {
          type: "boolean",
          description: "是否展示人物",
        },
        showTimeline: {
          type: "boolean",
          description: "是否展示时间线",
        },
        showStories: {
          type: "boolean",
          description: "是否展示故事",
        },
        showBookmarks: {
          type: "boolean",
          description: "是否展示收藏",
        },
        showFollowList: {
          type: "boolean",
          description: "是否展示关注列表",
        },
        showReferenceCollections: {
          type: "boolean",
          description: "是否展示引用集合",
        },
      },
    },
  })
  async updateHomepageSettings(
    @Request() req: { user: { id: string } },
    @Body() updateDto: UpdateHomepageSettingsDto,
  ): Promise<{
    success: boolean;
    data: { message: string };
    message: string;
  }> {
    const userId = req.user.id;
    const result = await this.homepageService.updateHomepageSettings(
      userId,
      updateDto,
    );

    return ApiResponse.success(result, "主页设置更新成功", HttpStatus.OK);
  }

  // ==================== 个人统计数据 ====================

  /**
   * 获取个人统计数据
   * API: GET /api/homepage/stats/:userId
   */
  @Get("stats/:userId")
  @ApiOperation({
    summary: "获取个人统计数据",
    description:
      "获取指定用户的详细统计数据，包括创作、社交、互动等全方位数据分析",
  })
  @ApiParam({
    name: "userId",
    description: "目标用户ID",
    example: "123e4567-e89b-12d3-a456-426614174000",
  })
  async getPersonalStats(@Param("userId") userId: string): Promise<{
    success: boolean;
    data: PersonalStatsResponseDto;
    message: string;
  }> {
    const personalStats = await this.homepageService.getPersonalStats(userId);

    return ApiResponse.success(
      personalStats,
      "获取个人统计数据成功",
      HttpStatus.OK,
    );
  }
}
