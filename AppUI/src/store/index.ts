/**
 * 有故事 APP - Redux Store 配置
 * 
 * 功能特性：
 * - Redux Toolkit 集成
 * - 类型安全配置
 * - 开发工具支持
 * - 中间件配置
 */

import { configureStore } from '@reduxjs/toolkit';

// 导入所有 slice
// import authSlice from './slices/authSlice';
// import userSlice from './slices/userSlice';

/**
 * Redux Store 配置
 */
export const store = configureStore({
  reducer: {
    // auth: authSlice,
    // user: userSlice,
    // 临时空对象，避免 reducer 为空的错误
    _placeholder: (state = {}) => state,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }),
  devTools: __DEV__,
});

// ===== 类型定义 =====
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// ===== 类型化的 hooks =====
import { useDispatch, useSelector, TypedUseSelectorHook } from 'react-redux';

export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector; 