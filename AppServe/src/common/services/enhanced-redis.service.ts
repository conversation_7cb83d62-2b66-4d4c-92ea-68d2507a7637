import type { OnModuleInit, OnModuleDestroy } from "@nestjs/common";
import { Injectable, Inject } from "@nestjs/common";
import { CACHE_MANAGER, Cache } from "@nestjs/cache-manager";
import { Redis } from "ioredis";
import { ConfigService } from "@nestjs/config";

/**
 * 增强Redis服务
 * 结合官方CacheManager和直接ioredis，提供完整的Redis功能
 * - 缓存操作: 使用官方@nestjs/cache-manager
 * - 高级功能: 使用直接ioredis (分布式锁、计数器、管道等)
 *
 * 完全替代 @liaoliaots/nestjs-redis，兼容 NestJS 11
 */
@Injectable()
export class EnhancedRedisService implements OnModuleInit, OnModuleDestroy {
  private readonly redis: Redis;

  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    private configService: ConfigService,
  ) {
    // 创建直接ioredis实例用于高级功能
    const redisHost = this.configService.get("REDIS_HOST", "localhost");
    const redisPort = this.configService.get("REDIS_PORT", 6379);
    const redisPassword = this.configService.get("REDIS_PASSWORD");

    console.log("🔧 EnhancedRedisService 配置:", {
      host: redisHost,
      port: redisPort,
      password: redisPassword ? "已设置" : "未设置",
      db: this.configService.get("REDIS_DB", 0),
    });

    this.redis = new Redis({
      host: redisHost === "localhost" ? "127.0.0.1" : redisHost, // 强制使用IPv4
      port: redisPort,
      password: redisPassword,
      db: this.configService.get("REDIS_DB", 0),
      // 企业级连接池配置
      maxRetriesPerRequest: 3,
      lazyConnect: true,
      connectTimeout: 10000,
      commandTimeout: 5000,
      family: 4, // 强制IPv4
      keepAlive: 30000,
      // 重连策略
      retryStrategy: (times) => Math.min(times * 50, 2000),
    });
  }

  async onModuleInit() {
    try {
      await this.redis.ping();
      console.log("✅ Enhanced Redis Service 初始化成功");
    } catch (error) {
      console.error("❌ Enhanced Redis Service 初始化失败:", error);
    }
  }

  async onModuleDestroy() {
    await this.redis.quit();
    console.log("🔒 Enhanced Redis Service 已关闭");
  }

  // ===========================================
  // 缓存操作 (使用官方 @nestjs/cache-manager)
  // ===========================================

  /**
   * 设置缓存
   * @param key 缓存键
   * @param value 缓存值
   * @param ttl TTL(毫秒)
   */
  async setCache(key: string, value: unknown, ttl?: number): Promise<void> {
    try {
      if (ttl) {
        await this.cacheManager.set(key, value, ttl);
      } else {
        await this.cacheManager.set(key, value);
      }
    } catch (error) {
      console.error(`Failed to set cache ${key}:`, error);
      throw error;
    }
  }

  /**
   * 获取缓存
   * @param key 缓存键
   */
  async getCache<T = unknown>(key: string): Promise<T | undefined> {
    try {
      const result = await this.cacheManager.get<T>(key);
      return result === null ? undefined : result;
    } catch (error) {
      console.error(`Failed to get cache ${key}:`, error);
      return undefined;
    }
  }

  /**
   * 删除缓存
   * @param key 缓存键
   */
  async delCache(key: string): Promise<void> {
    try {
      await this.cacheManager.del(key);
    } catch (error) {
      console.error(`Failed to delete cache ${key}:`, error);
      throw error;
    }
  }

  /**
   * 清空所有缓存
   */
  async clearCache(): Promise<void> {
    try {
      // 使用Redis原生命令清空数据库
      await this.redis.flushdb();
    } catch (error) {
      console.error("Failed to clear cache:", error);
      throw error;
    }
  }

  // ===========================================
  // 分布式锁 (使用直接 ioredis)
  // ===========================================

  /**
   * 获取分布式锁
   * @param key 锁的键名
   * @param ttl 锁的过期时间（毫秒）
   * @param retryCount 重试次数
   * @param retryDelay 重试延迟（毫秒）
   */
  async acquireLock(
    key: string,
    ttl: number,
    retryCount = 3,
    retryDelay = 100,
  ): Promise<string | null> {
    const lockValue = `lock:${Date.now()}:${Math.random()}`;

    for (let i = 0; i < retryCount; i++) {
      try {
        // 使用SET命令的NX和PX选项实现原子操作
        const result = await this.redis.set(key, lockValue, "PX", ttl, "NX");

        if (result === "OK") {
          return lockValue;
        }

        // 重试前等待
        if (i < retryCount - 1) {
          await new Promise((resolve) => setTimeout(resolve, retryDelay));
        }
      } catch (error) {
        console.error(`Failed to acquire lock ${key}:`, error);
      }
    }

    return null;
  }

  /**
   * 释放分布式锁
   * @param key 锁的键名
   * @param lockValue 锁的值（用于验证所有权）
   */
  async releaseLock(key: string, lockValue: string): Promise<boolean> {
    // 使用Lua脚本确保原子性：只有锁的所有者才能释放锁
    const luaScript = `
      if redis.call("get", KEYS[1]) == ARGV[1] then
        return redis.call("del", KEYS[1])
      else
        return 0
      end
    `;

    try {
      const result = await this.redis.eval(luaScript, 1, key, lockValue);
      return result === 1;
    } catch (error) {
      console.error(`Failed to release lock ${key}:`, error);
      return false;
    }
  }

  // ===========================================
  // 计数器操作 (使用直接 ioredis)
  // ===========================================

  /**
   * 增加计数
   * @param key 计数器键
   * @param ttl TTL(毫秒)，仅在首次创建时设置
   */
  async incr(key: string, ttl?: number): Promise<number> {
    try {
      const result = await this.redis.incr(key);
      if (ttl && result === 1) {
        await this.redis.expire(key, Math.floor(ttl / 1000));
      }
      return result;
    } catch (error) {
      console.error(`Failed to increment ${key}:`, error);
      return 0;
    }
  }

  /**
   * 减少计数
   * @param key 计数器键
   */
  async decr(key: string): Promise<number> {
    try {
      return await this.redis.decr(key);
    } catch (error) {
      console.error(`Failed to decrement ${key}:`, error);
      return 0;
    }
  }

  // ===========================================
  // 基础Redis操作 (兼容原有接口)
  // ===========================================

  /**
   * 设置键值
   * @param key 键
   * @param value 值
   * @param ttl TTL(毫秒)
   */
  async set(key: string, value: string, ttl?: number): Promise<boolean> {
    try {
      if (ttl) {
        await this.redis.setex(key, Math.floor(ttl / 1000), value);
      } else {
        await this.redis.set(key, value);
      }
      return true;
    } catch (error) {
      console.error(`Failed to set ${key}:`, error);
      return false;
    }
  }

  /**
   * 获取键值
   * @param key 键
   */
  async get(key: string): Promise<string | null> {
    try {
      return await this.redis.get(key);
    } catch (error) {
      console.error(`Failed to get ${key}:`, error);
      return null;
    }
  }

  /**
   * 删除键
   * @param key 键
   */
  async del(key: string): Promise<boolean> {
    try {
      const result = await this.redis.del(key);
      return result > 0;
    } catch (error) {
      console.error(`Failed to delete ${key}:`, error);
      return false;
    }
  }

  /**
   * 设置过期时间
   * @param key 键
   * @param ttl TTL(毫秒)
   */
  async expire(key: string, ttl: number): Promise<boolean> {
    try {
      const result = await this.redis.expire(key, Math.floor(ttl / 1000));
      return result === 1;
    } catch (error) {
      console.error(`Failed to set expiry for ${key}:`, error);
      return false;
    }
  }

  /**
   * 检查键是否存在
   * @param key 键
   */
  async exists(key: string): Promise<boolean> {
    try {
      const result = await this.redis.exists(key);
      return result === 1;
    } catch (error) {
      console.error(`Failed to check existence of ${key}:`, error);
      return false;
    }
  }

  /**
   * 设置过期时间戳
   * @param key 键
   * @param timestamp 时间戳
   */
  async expireAt(key: string, timestamp: number): Promise<boolean> {
    try {
      const result = await this.redis.expireat(key, timestamp);
      return result === 1;
    } catch (error) {
      console.error(`Failed to set expiry at timestamp for ${key}:`, error);
      return false;
    }
  }

  // ===========================================
  // 高级操作
  // ===========================================

  /**
   * 创建管道操作
   */
  pipeline() {
    return this.redis.pipeline();
  }

  /**
   * 获取原生Redis客户端（用于高级操作）
   */
  getClient(): Redis {
    return this.redis;
  }

  /**
   * 获取缓存管理器（用于官方缓存操作）
   */
  getCacheManager(): Cache {
    return this.cacheManager;
  }
}
