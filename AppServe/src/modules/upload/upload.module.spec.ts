/**
 * 上传模块 - 企业级单元测试
 */

import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { ConfigService } from "@nestjs/config";
import { UploadModule } from "./upload.module";
import { UploadService } from "./upload.service";
import { UploadController } from "./upload.controller";
import { ossConfig } from "../../config/oss.config";

// Mock OSS配置
jest.mock("../../config/oss.config", () => ({
  ossConfig: jest.fn(() => ({
    accessKeyId: "mock-key",
    accessKeySecret: "mock-secret",
    bucket: "mock-bucket",
    region: "oss-cn-hangzhou",
  })),
}));

describe("UploadModule - 企业级模块测试", () => {
  let module: TestingModule;
  let mockConfigService: jest.Mocked<ConfigService>;

  beforeEach(async () => {
    mockConfigService = {
      get: jest.fn(),
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } as any;

    module = await Test.createTestingModule({
      imports: [UploadModule],
    })
      .overrideProvider(ConfigService)
      .useValue(mockConfigService)
      .compile();
  });

  afterEach(async () => {
    if (module) {
      await module.close();
    }
  });

  describe("模块初始化", () => {
    it("should compile the module", () => {
      expect(module).toBeDefined();
    });

    it("should provide UploadService", () => {
      const service = module.get<UploadService>(UploadService);
      expect(service).toBeDefined();
    });

    it("should provide UploadController", () => {
      const controller = module.get<UploadController>(UploadController);
      expect(controller).toBeDefined();
    });
  });

  describe("依赖注入", () => {
    it("should inject UploadService into UploadController", () => {
      const controller = module.get<UploadController>(UploadController);
      const service = module.get<UploadService>(UploadService);

      expect(controller).toBeDefined();
      expect(service).toBeDefined();
    });

    it("should inject ConfigService", () => {
      const configService = module.get<ConfigService>(ConfigService);
      expect(configService).toBeDefined();
    });
  });

  describe("模块配置", () => {
    it("should be a valid NestJS module", () => {
      expect(UploadModule).toBeDefined();
      expect(typeof UploadModule).toBe("function");
    });

    it("should export UploadService", () => {
      const service = module.get<UploadService>(UploadService);
      expect(service).toBeDefined();
    });
  });

  describe("ConfigModule集成", () => {
    it("should configure OSS settings", () => {
      const configService = module.get<ConfigService>(ConfigService);
      expect(configService).toBeDefined();

      // 验证OSS配置函数被正确调用
      expect(ossConfig).toBeDefined();
      expect(typeof ossConfig).toBe("function");
    });
  });

  describe("AuthModule集成", () => {
    it("should handle forwardRef with AuthModule", () => {
      // 验证forwardRef不会导致循环依赖问题
      const service = module.get<UploadService>(UploadService);
      expect(service).toBeDefined();
    });
  });

  describe("文件上传功能", () => {
    it("should handle file upload operations", () => {
      const service = module.get<UploadService>(UploadService);
      expect(service).toBeDefined();
      // 文件上传相关的业务逻辑验证
    });

    it("should handle OSS integration", () => {
      const service = module.get<UploadService>(UploadService);
      expect(service).toBeDefined();
      // OSS集成相关的业务逻辑验证
    });

    it("should handle authentication for uploads", () => {
      const controller = module.get<UploadController>(UploadController);
      expect(controller).toBeDefined();
      // 上传认证相关的业务逻辑验证
    });
  });
});
