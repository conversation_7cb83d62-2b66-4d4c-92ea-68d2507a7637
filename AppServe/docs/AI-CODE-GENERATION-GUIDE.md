# 🤖 AI代码生成规范指南

> **目标**: 确保AI生成的代码符合ESLint规则，减少代码质量问题

## 🚨 ESLint错误产生原因分析

### 主要错误类型
1. **`@typescript-eslint/no-explicit-any`**: 使用了 `any` 类型
2. **`@typescript-eslint/no-unused-vars`**: 声明了未使用的变量
3. **类型不一致**: Mock对象类型不匹配

### 错误产生流程
```
AI生成代码 → ESLint检查 → 发现违规 → 手动修复 → 重新检查
```

## 🛠️ 全局规则配置优化

### 1. ESLint配置增强
```javascript
// .eslintrc.js
overrides: [
  {
    // 测试文件专用规则
    files: ['**/*.spec.ts', '**/*.test.ts'],
    rules: {
      '@typescript-eslint/no-unused-vars': ['error', {
        'varsIgnorePattern': '^(mock|Mock|test|Test|spec|Spec|jest)'
      }],
    }
  }
]
```

### 2. TypeScript配置统一
```json
// tsconfig.json
{
  "strict": true,
  "noUnusedLocals": true,
  "noUnusedParameters": true,
  "strictNullChecks": true
}
```

## 📝 代码生成规范

### 1. 导入语句规范
```typescript
// ✅ 正确：明确导入，按类型分组
import { Test, TestingModule } from '@nestjs/testing';
import type { Repository } from 'typeorm';
import { YourService } from './your.service';

// ❌ 错误：未使用的导入
import { ConfigService } from '@nestjs/config'; // 如果未使用会报错
import OSS from 'ali-oss'; // 如果只在类型中使用应该用 import type
```

### 2. Mock变量命名规范
```typescript
// ✅ 正确：以mock开头，会被ESLint忽略
const mockRepository = { find: jest.fn() };
const mockConfigService = { get: jest.fn() };

// ❌ 错误：普通变量名，未使用会报错
const repository = { find: jest.fn() };
const configService = { get: jest.fn() };
```

### 3. 类型断言规范
```typescript
// ✅ 正确：使用具体类型
const mockResult: OSS.PutObjectResult = {
  name: 'test',
  url: 'https://example.com',
  res: { status: 200 }
};

// ❌ 错误：使用any类型
const mockResult = {
  name: 'test',
  url: 'https://example.com',
  res: { status: 200 }
} as any;
```

### 4. 私有方法测试规范
```typescript
// ✅ 正确：添加eslint-disable注释
it('should test private method', () => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const result = (service as any).privateMethod();
  expect(result).toBeDefined();
});

// ❌ 错误：直接使用any
it('should test private method', () => {
  const result = (service as any).privateMethod();
  expect(result).toBeDefined();
});
```

## 🎯 AI代码生成最佳实践

### 1. 预防性规则设计
```typescript
// 代码生成时遵循的模式
interface CodeGenerationRules {
  // 变量命名
  mockVariables: 'mock' + PascalCase;
  testVariables: 'test' + PascalCase;
  
  // 类型使用
  useSpecificTypes: true;
  avoidAnyType: true;
  
  // 导入管理
  removeUnusedImports: true;
  groupImportsByType: true;
}
```

### 2. 模板驱动生成
```typescript
// 使用标准模板避免重复错误
const testTemplate = {
  imports: standardImports,
  mockSetup: standardMockSetup,
  testStructure: standardTestStructure,
};
```

### 3. 错误检查流程
```bash
# 代码生成后立即检查
npm run lint -- --fix
npm run type-check
```

## 🔧 开发环境配置

### VSCode配置
```json
{
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "explicit",
    "source.removeUnusedImports": "explicit"
  }
}
```

### Git钩子配置
```bash
# pre-commit时自动修复
lint-staged:
  "*.ts": ["eslint --fix", "git add"]
```

## 📊 效果评估

### 错误减少指标
- **目标**: ESLint错误减少90%
- **测量**: 每次生成代码后的错误数量
- **优化**: 持续改进生成规则

### 代码质量指标
- **类型安全**: 0个any类型使用
- **导入清洁**: 0个未使用导入
- **测试规范**: 100%符合企业标准

## 🚀 实施建议

1. **立即应用**: 使用新的ESLint配置
2. **模板使用**: 基于提供的模板生成代码
3. **自动化检查**: 配置IDE自动修复
4. **持续改进**: 根据实际使用情况调整规则

---

通过这些规范，我们可以显著减少AI代码生成时的ESLint错误，提高代码质量和开发效率。