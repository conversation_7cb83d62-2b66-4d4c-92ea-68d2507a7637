# YGS v1.0.0 种子数据统计报告

> **生成时间**: 2025/7/28 21:40:00  
> **数据库**: ygs-prod  
> **主机**: pgm-bp14hy1423z0mg05vo.pg.rds.aliyuncs.com  
> **总记录数**: 228 条

## 📊 数据概览

### 核心数据统计

| 表名 | 记录数 | 主要功能 |
|------|--------|----------|
| **users** | 10 | 用户账户管理，包含完整的用户信息、权限控制、安全验证等 |
| **stories** | 9 | 故事内容管理，支持多种发布状态和权限级别 |
| **characters** | 12 | 故事人物管理，支持点亮功能的核心实体 |
| **themes** | 20 | 故事主题分类，帮助用户组织和发现内容 |

### 互动功能数据

| 表名 | 记录数 | 主要功能 |
|------|--------|----------|
| **comments** | 15 | 用户评论系统，支持多层级回复 |
| **comment_likes** | 8 | 评论点赞功能 |
| **bookmarks** | 20 | 故事收藏管理，支持分类和标签 |
| **shares** | 11 | 故事分享链接管理 |
| **share_access_logs** | 21 | 分享访问记录追踪 |

### 核心业务功能

| 表名 | 记录数 | 主要功能 |
|------|--------|----------|
| **light_requests** | 13 | 人物点亮申请，YGS平台的核心功能 |
| **character_lightings** | 5 | 成功的人物点亮关系记录 |
| **user_follows** | 16 | 用户关注关系管理 |
| **friend_groups** | 11 | 好友分组功能 |

### 系统支持功能

| 表名 | 记录数 | 主要功能 |
|------|--------|----------|
| **notifications** | 23 | 系统通知推送 |
| **timeline_events** | 34 | 用户个人时间线管理 |

## 🔍 关键数据样本分析

### 👥 用户表 (10 条记录)

**表结构特点**:
- 总字段数: 39
- 关键字段: id, nickname, username, status, is_identity_verified

**数据样本**:
- 用户示例: 明远说 (13800138001)
- 安全等级: 2, VIP用户: 否
- 验证状态: 手机已验证, 邮箱已验证

### 📖 故事表 (9 条记录)

**表结构特点**:
- 总字段数: 20
- 关键字段: id, user_id, title, status, theme_id

**数据样本**:
- 故事标题: "写给十年后的自己"
- 发布状态: 已发布, 权限: private
- 统计数据: 浏览1次, 点赞0次, 评论0条

### 🎭 人物表 (12 条记录)

**表结构特点**:
- 总字段数: 15
- 关键字段: id, creator_id, name, lighter_user_id

**数据样本**:
- 人物姓名: "李明"
- 关系类型: 室友, 性别: male
- 点亮状态: 已点亮 (被点亮1次)

### 🎨 主题表 (20 条记录)

**表结构特点**:
- 总字段数: 8
- 关键字段: id, name

**数据样本**:

### 💡 点亮申请表 (13 条记录)

**表结构特点**:
- 总字段数: 12
- 关键字段: id, story_id, character_id, requester_id, story_author_id

**数据样本**:
- 申请状态: approved
- 手机验证: 已验证
- 申请消息: "李明，我是小红！还记得我吗？大学时我们一起参加过编程比赛，你还帮我调试代码到凌晨。看到这个故事，满满..."

### ✨ 人物点亮表 (5 条记录)

**表结构特点**:
- 总字段数: 9
- 关键字段: id, character_id, story_id, lighter_user_id, creator_user_id

**数据样本**:

### 💬 评论表 (15 条记录)

**表结构特点**:
- 总字段数: 14
- 关键字段: id, status

**数据样本**:

### ⭐ 收藏表 (20 条记录)

**表结构特点**:
- 总字段数: 11
- 关键字段: id, status

**数据样本**:

### 🔗 分享表 (11 条记录)

**表结构特点**:
- 总字段数: 15
- 关键字段: id, status

**数据样本**:

### 🔔 通知表 (23 条记录)

**表结构特点**:
- 总字段数: 10
- 关键字段: id, user_id, title, related_id

**数据样本**:

## 🎯 业务场景覆盖

### 1. 用户管理场景
- **完整用户生命周期**: 从注册、验证到VIP升级的完整流程
- **安全防护机制**: 包含异常检测、账户锁定、登录失败计数等企业级安全特性
- **个性化设置**: 用户可自定义个人主页展示内容和隐私设置

### 2. 内容创作场景
- **多样化故事类型**: 包含20个不同主题分类
- **灵活权限控制**: 支持私密、好友可见、公开、仅人物可见等多级权限
- **富媒体支持**: 故事可包含图片、位置、时间等多维度信息

### 3. 社交互动场景
- **点亮核心功能**: 13个点亮申请，5个成功点亮
- **评论互动系统**: 15条评论，8个点赞
- **社交关系网络**: 16个关注关系，11个好友分组

### 4. 内容分享场景
- **多渠道分享**: 11个分享链接，支持公开、密码保护等模式
- **访问追踪**: 21条访问记录，包含IP、设备、来源等详细信息

### 5. 个人数据管理
- **时间线功能**: 34个个人时间线事件
- **收藏系统**: 20个收藏记录，支持分类和标签管理
- **通知中心**: 23条系统通知

## 🔑 测试账号信息

### 主要测试账号
1. **创作者张明远**: 13800138001 / Test123456!
   - 角色: 主要内容创建者
   - 特点: 已创建多个故事，具有完整的人物关系网

2. **点亮者李小红**: 13800138002 / Test123456!
   - 角色: 活跃的点亮用户
   - 特点: 成功点亮多个人物，是社交功能的核心参与者

3. **教师王建国**: 13800138003 / Test123456!
   - 角色: 教育工作者用户
   - 特点: 代表专业用户群体，有丰富的人生阅历

4. **VIP陈总**: 16675158665 / Test123456!
   - 角色: VIP用户
   - 特点: 具有VIP权限，体验高级功能

5. **系统测试**: 13800138009 / Test123456!
   - 角色: 系统测试专用账号
   - 特点: 用于自动化测试和功能验证

## 📈 数据质量指标

- **数据完整性**: ✅ 所有核心表都包含测试数据
- **关联关系**: ✅ 表间外键关系正确建立
- **业务逻辑**: ✅ 点亮、关注、评论等业务流程完整
- **安全合规**: ✅ 密码加密、敏感信息保护
- **性能优化**: ✅ 包含足够数据量用于性能测试

## 🔄 数据更新机制

- **种子数据重置**: 使用 `npm run seed:clean` 清理数据
- **增量数据生成**: 使用 `npm run seed:users` 仅生成用户数据
- **数据验证**: 使用 `npm run seed:validate` 检查数据完整性
- **统计查询**: 使用 `npm run seed:stats` 获取最新统计

---

**报告生成时间**: 2025/7/28 21:41:19  
**数据版本**: YGS v1.0.0 企业级种子数据  
**维护状态**: 活跃维护中
