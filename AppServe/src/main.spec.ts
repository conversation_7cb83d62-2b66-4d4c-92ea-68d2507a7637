/**
 * main.ts 启动文件单元测试
 * 测试应用启动配置、全局设置和错误处理
 */

import { ValidationPipe } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { NestFactory } from "@nestjs/core";
import type { NestFastifyApplication } from "@nestjs/platform-fastify";
import { FastifyAdapter } from "@nestjs/platform-fastify";
import { AppModule } from "./app.module";
import { HttpExceptionFilter } from "./common/filters/http-exception.filter";
import { TransformInterceptor } from "./common/interceptors/transform.interceptor";
import { LoggingInterceptor } from "./common/interceptors/logging.interceptor";

// Mock NestFactory
jest.mock("@nestjs/core", () => ({
  NestFactory: {
    create: jest.fn(),
  },
}));

// Mock SwaggerModule
jest.mock("@nestjs/swagger", () => ({
  DocumentBuilder: jest.fn().mockImplementation(() => ({
    setTitle: jest.fn().mockReturnThis(),
    setDescription: jest.fn().mockReturnThis(),
    setVersion: jest.fn().mockReturnThis(),
    addTag: jest.fn().mockReturnThis(),
    build: jest.fn().mockReturnValue({}),
  })),
  SwaggerModule: {
    createDocument: jest.fn().mockReturnValue({}),
    setup: jest.fn(),
  },
}));

describe("Main Bootstrap Function", () => {
  let mockApp: jest.Mocked<NestFastifyApplication>;
  let mockConfigService: jest.Mocked<ConfigService>;
  let consoleSpy: jest.SpyInstance;

  beforeEach(() => {
    // Mock ConfigService
    mockConfigService = {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      get: jest.fn((key: string, defaultValue?: any) => {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const config: Record<string, any> = {
          PORT: 3000,
          NODE_ENV: "development",
          FRONTEND_URL: "http://localhost:3000",
        };
        return config[key] ?? defaultValue;
      }),
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } as any;

    // Mock NestFastifyApplication
    mockApp = {
      get: jest.fn().mockReturnValue(mockConfigService),
      setGlobalPrefix: jest.fn(),
      register: jest.fn().mockResolvedValue(undefined),
      enableCors: jest.fn(),
      useGlobalPipes: jest.fn(),
      useGlobalFilters: jest.fn(),
      useGlobalInterceptors: jest.fn(),
      listen: jest.fn().mockResolvedValue(undefined),
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } as any;

    // Mock NestFactory.create
    (NestFactory.create as jest.Mock).mockResolvedValue(mockApp);

    // Mock console methods
    consoleSpy = jest.spyOn(console, "log").mockImplementation();
  });

  afterEach(() => {
    jest.clearAllMocks();
    consoleSpy.mockRestore();
  });

  describe("应用创建和配置", () => {
    beforeEach(async () => {
      // 动态导入和执行bootstrap逻辑
      const bootstrap = async () => {
        const app = await NestFactory.create<NestFastifyApplication>(
          AppModule,
          new FastifyAdapter({
            logger: false,
          }),
        );

        const configService = app.get(ConfigService);
        const nodeEnv = configService.get<string>("NODE_ENV", "development");

        // 设置全局前缀
        app.setGlobalPrefix("api");

        // CORS配置
        const frontendUrl = configService.get<string>(
          "FRONTEND_URL",
          "http://localhost:3000",
        );
        app.enableCors({
          origin: [
            frontendUrl,
            "http://localhost:3000",
            "http://127.0.0.1:3000",
          ],
          credentials: true,
        });

        // 全局管道
        const validationPipe = new ValidationPipe({
          whitelist: true,
          forbidNonWhitelisted: true,
          transform: true,
          disableErrorMessages: nodeEnv === "production",
        });
        app.useGlobalPipes(validationPipe);

        // 全局过滤器
        app.useGlobalFilters(new HttpExceptionFilter());

        // 全局拦截器
        app.useGlobalInterceptors(
          new TransformInterceptor(),
          new LoggingInterceptor(),
        );

        return { app, configService, validationPipe, nodeEnv };
      };

      await bootstrap();
    });

    it("should create NestJS application with FastifyAdapter", () => {
      expect(NestFactory.create).toHaveBeenCalledWith(
        AppModule,
        expect.any(FastifyAdapter),
      );
    });

    it("should set global prefix to 'api'", () => {
      expect(mockApp.setGlobalPrefix).toHaveBeenCalledWith("api");
    });

    it("should enable CORS with correct configuration", () => {
      expect(mockApp.enableCors).toHaveBeenCalledWith({
        origin: [
          "http://localhost:3000",
          "http://localhost:3000",
          "http://127.0.0.1:3000",
        ],
        credentials: true,
      });
    });

    it("should setup global pipes with ValidationPipe", () => {
      expect(mockApp.useGlobalPipes).toHaveBeenCalledWith(
        expect.any(ValidationPipe),
      );
    });

    it("should setup global filters", () => {
      expect(mockApp.useGlobalFilters).toHaveBeenCalledWith(
        expect.any(HttpExceptionFilter),
      );
    });

    it("should setup global interceptors", () => {
      expect(mockApp.useGlobalInterceptors).toHaveBeenCalledWith(
        expect.any(TransformInterceptor),
        expect.any(LoggingInterceptor),
      );
    });
  });

  describe("生产环境配置", () => {
    it("should disable validation error messages in production", async () => {
      mockConfigService.get.mockImplementation(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (key: string, defaultValue?: any) => {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const config: Record<string, any> = {
            PORT: 3000,
            NODE_ENV: "production",
            FRONTEND_URL: "https://story-app.com",
          };
          return config[key] ?? defaultValue;
        },
      );

      const bootstrap = async () => {
        const app = await NestFactory.create<NestFastifyApplication>(
          AppModule,
          new FastifyAdapter(),
        );
        const configService = app.get(ConfigService);
        const nodeEnv = configService.get<string>("NODE_ENV", "development");

        const validationPipe = new ValidationPipe({
          whitelist: true,
          forbidNonWhitelisted: true,
          transform: true,
          disableErrorMessages: nodeEnv === "production",
        });

        return { validationPipe, nodeEnv };
      };

      const { validationPipe: mockValidationPipe, nodeEnv } = await bootstrap();
      expect(nodeEnv).toBe("production");
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      expect((mockValidationPipe as any).options.disableErrorMessages).toBe(
        true,
      );
    });
  });

  describe("环境配置", () => {
    it("should use production CORS configuration in production", async () => {
      mockConfigService.get.mockImplementation(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (key: string, defaultValue?: any) => {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const config: Record<string, any> = {
            PORT: 3000,
            NODE_ENV: "production",
            FRONTEND_URL: "https://story-app.com",
          };
          return config[key] ?? defaultValue;
        },
      );

      const bootstrap = async () => {
        const app = await NestFactory.create<NestFastifyApplication>(
          AppModule,
          new FastifyAdapter(),
        );
        const configService = app.get(ConfigService);

        const frontendUrl = configService.get<string>(
          "FRONTEND_URL",
          "http://localhost:3000",
        );
        app.enableCors({
          origin: [
            frontendUrl,
            "http://localhost:3000",
            "http://127.0.0.1:3000",
          ],
          credentials: true,
        });

        return { frontendUrl };
      };

      const { frontendUrl } = await bootstrap();
      expect(frontendUrl).toBe("https://story-app.com");
    });
  });

  describe("Swagger文档配置", () => {
    it("should setup Swagger in development environment", () => {
      // eslint-disable-next-line @typescript-eslint/no-var-requires
      const { DocumentBuilder, SwaggerModule } = require("@nestjs/swagger");

      const mockDocument = {};
      const mockConfig = {};

      DocumentBuilder.mockImplementation(() => ({
        setTitle: jest.fn().mockReturnThis(),
        setDescription: jest.fn().mockReturnThis(),
        setVersion: jest.fn().mockReturnThis(),
        addTag: jest.fn().mockReturnThis(),
        build: jest.fn().mockReturnValue(mockConfig),
      }));

      SwaggerModule.createDocument.mockReturnValue(mockDocument);

      // 模拟开发环境下的Swagger设置
      const setupSwagger = (app: NestFastifyApplication) => {
        const config = new DocumentBuilder()
          .setTitle("Story App API")
          .setDescription("The Story App API description")
          .setVersion("1.0")
          .addTag("stories")
          .build();

        const document = SwaggerModule.createDocument(app, config);
        SwaggerModule.setup("api-docs", app, document);
      };

      setupSwagger(mockApp);

      expect(DocumentBuilder).toHaveBeenCalled();
      expect(SwaggerModule.createDocument).toHaveBeenCalledWith(
        mockApp,
        mockConfig,
      );
      expect(SwaggerModule.setup).toHaveBeenCalledWith(
        "api-docs",
        mockApp,
        mockDocument,
      );
    });
  });

  describe("配置值测试", () => {
    it("should use default port when PORT is not set", () => {
      mockConfigService.get.mockImplementation(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (key: string, defaultValue?: any) => {
          if (key === "PORT") return defaultValue;
          return "development";
        },
      );

      const port = mockConfigService.get<number>("PORT", 3000);
      expect(port).toBe(3000);
    });

    it("should use default NODE_ENV when not set", () => {
      mockConfigService.get.mockImplementation(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (key: string, defaultValue?: any) => {
          if (key === "NODE_ENV") return defaultValue;
          return 3000;
        },
      );

      const nodeEnv = mockConfigService.get<string>("NODE_ENV", "development");
      expect(nodeEnv).toBe("development");
    });
  });
});
