import 'package:freezed_annotation/freezed_annotation.dart';

part 'api_result.freezed.dart';

/// API响应结果包装类
@freezed
class ApiResult<T> with _$ApiResult<T> {
  const factory ApiResult({
    @Default(true) bool success,
    @Default(0) int code,
    @Default('') String message,
    T? data,
    DateTime? timestamp,
  }) = _ApiResult<T>;

  /// 从JSON创建ApiResult（用于网络响应解析）
  factory ApiResult.fromJson(
    Map<String, dynamic> json, {
    T Function(dynamic)? dataParser,
  }) {
    return ApiResult<T>(
      success: (json['success'] as bool?) ?? true,
      code: (json['code'] as int?) ?? 0,
      message: (json['message'] as String?) ?? '',
      data: json['data'] != null && dataParser != null 
          ? dataParser(json['data']) 
          : json['data'] as T?,
      timestamp: json['timestamp'] != null 
          ? DateTime.parse(json['timestamp'] as String) 
          : null,
    );
  }
      
  /// 创建带数据的结果
  factory ApiResult.withData(T data) => ApiResult(
    success: true,
    code: 0,
    message: 'success',
    data: data,
    timestamp: DateTime.now(),
  );
  
  /// 创建错误结果
  factory ApiResult.error(String message, {int code = -1}) => ApiResult(
    success: false,
    code: code,
    message: message,
    data: null,
    timestamp: DateTime.now(),
  );
}

/// 分页响应数据
@freezed
class PageData<T> with _$PageData<T> {
  const factory PageData({
    @Default([]) List<T> items,
    @Default(1) int page,
    @Default(20) int pageSize,
    @Default(0) int total,
    @Default(0) int totalPages,
    @Default(false) bool hasNext,
    @Default(false) bool hasPrevious,
  }) = _PageData<T>;
      
  /// 创建空的分页数据
  factory PageData.empty() => const PageData(
    items: [],
    page: 1,
    pageSize: 20,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrevious: false,
  );
}

/// API响应扩展方法
extension ApiResultX<T> on ApiResult<T> {
  /// 是否请求成功
  bool get isSuccess => success && code == 0;

  /// 获取数据，失败时抛出异常
  T getDataOrThrow() {
    if (!isSuccess) {
      throw Exception(message);
    }
    if (data == null) {
      throw Exception('响应数据为空');
    }
    return data!;
  }

  /// 转换数据类型
  ApiResult<R> map<R>(R Function(T data) mapper) {
    return ApiResult<R>(
      success: success,
      code: code,
      message: message,
      data: data != null ? mapper(data!) : null,
      timestamp: timestamp,
    );
  }
}