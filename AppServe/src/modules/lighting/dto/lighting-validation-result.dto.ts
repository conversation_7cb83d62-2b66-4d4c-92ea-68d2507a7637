import { ApiProperty } from "@nestjs/swagger";

export class LightingValidationResultDto {
  @ApiProperty({
    description: "验证是否通过",
    example: false,
  })
  isValid: boolean;

  @ApiProperty({
    description: "人物是否存在",
    example: true,
  })
  characterExists: boolean;

  @ApiProperty({
    description: "手机号是否匹配",
    example: true,
  })
  phoneMatches: boolean;

  @ApiProperty({
    description: "是否无重复申请",
    example: true,
  })
  noDuplicateApplication: boolean;

  @ApiProperty({
    description: "人物是否未被他人点亮",
    example: false,
  })
  characterNotLightedByOthers: boolean;

  @ApiProperty({
    description: "用户状态是否正常",
    example: true,
  })
  userStatusNormal: boolean;

  @ApiProperty({
    description: "频率限制是否通过",
    example: false,
  })
  rateLimitValid: boolean;

  @ApiProperty({
    description: "拒绝原因（如果有）",
    example: '该人物已被用户"张三"认证，无法重复申请',
    required: false,
  })
  rejectionReason?: string;

  @ApiProperty({
    description: "冷却时间（秒）",
    example: 3600,
    required: false,
  })
  cooldownSeconds?: number;

  @ApiProperty({
    description: "用户当前异常状态",
    example: "normal",
    enum: ["normal", "warning", "restricted", "suspended"],
  })
  userAnomalyStatus: string;

  @ApiProperty({
    description: "今日剩余申请次数",
    example: 15,
  })
  remainingDailyAttempts: number;

  constructor(data: Partial<LightingValidationResultDto>) {
    Object.assign(this, data);
  }
}
