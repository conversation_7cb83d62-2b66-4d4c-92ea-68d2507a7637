# 有故事APP生产环境配置
# 适配阿里云ECS: ************

# 上游后端服务
upstream story_app_backend {
    server story-app-backend:3000;
    keepalive 32;
}

# HTTP 服务配置 (暂时不重定向HTTPS，便于测试)
server {
    listen 80;
    server_name ************ localhost;
    
    # 健康检查端点
    location /api/v1/health {
        proxy_pass http://story_app_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_connect_timeout 5s;
        proxy_send_timeout 5s;
        proxy_read_timeout 5s;
        access_log off;
    }
    
    # API路由配置
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        
        proxy_pass http://story_app_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 超时设置
        proxy_connect_timeout 5s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 文件上传接口
    location /api/upload {
        limit_req zone=upload burst=5 nodelay;
        
        proxy_pass http://story_app_backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 文件上传超时设置
        proxy_connect_timeout 10s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
        client_max_body_size 100m;
    }
    
    # WebSocket支持 (未来可能需要)
    location /ws {
        proxy_pass http://story_app_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 86400;
    }
    
    # 静态文件 (如果有)
    location /static/ {
        expires 30d;
        add_header Cache-Control "public, no-transform";
    }
    
    # 默认路由 - API文档或状态页
    location / {
        proxy_pass http://story_app_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 安全配置
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # 错误页面
    error_page 502 503 504 /50x.html;
    location = /50x.html {
        return 200 '{"error": "Service temporarily unavailable", "code": 502}';
        add_header Content-Type application/json;
    }
}

# HTTPS 配置 (暂时注释，先确保HTTP正常工作)
# server {
#     listen 443 ssl http2;
#     server_name ************ localhost;
#     
#     # SSL配置 (自签名证书，后续可替换为正式证书)
#     ssl_certificate /etc/nginx/ssl/server.crt;
#     ssl_certificate_key /etc/nginx/ssl/server.key;
#     ssl_session_timeout 1d;
#     ssl_session_cache shared:SSL:50m;
#     ssl_session_tickets off;
#     ... 其他HTTPS配置
# }

# 监控和管理接口 (仅本地访问)
server {
    listen 127.0.0.1:8888;
    server_name localhost;
    
    location /nginx_status {
        stub_status on;
        access_log off;
        allow 127.0.0.1;
        deny all;
    }
} 