import type { CanActivate, ExecutionContext } from "@nestjs/common";
import {
  Injectable,
  ForbiddenException,
  NotFoundException,
} from "@nestjs/common";
import { Reflector } from "@nestjs/core";
import { StoriesService } from "../stories.service";
import { StoryPermissionService } from "../services/story-permission.service";

/**
 * 故事访问权限守卫
 * 用于验证用户是否有权限访问特定故事
 */
@Injectable()
export class StoryAccessGuard implements CanActivate {
  constructor(
    private readonly storiesService: StoriesService,
    private readonly storyPermissionService: StoryPermissionService,
    private readonly reflector: Reflector,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const storyId = request.params.id;
    const currentUser = request.user;

    if (!storyId) {
      throw new NotFoundException("故事ID不能为空");
    }

    if (!currentUser) {
      throw new ForbiddenException("用户未登录");
    }

    try {
      // 获取故事信息
      const story = await this.storiesService.findById(storyId);

      // 检查访问权限
      const hasAccess = await this.storyPermissionService.validateAccess(
        story,
        currentUser,
      );

      if (!hasAccess) {
        throw new ForbiddenException("无权访问此故事");
      }

      // 将故事信息添加到请求对象中，便于后续使用
      request.story = story;

      return true;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new ForbiddenException("权限验证失败");
    }
  }
}

/**
 * 故事编辑权限守卫
 * 用于验证用户是否有权限编辑特定故事
 */
@Injectable()
export class StoryEditGuard implements CanActivate {
  constructor(
    private readonly storiesService: StoriesService,
    private readonly storyPermissionService: StoryPermissionService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const storyId = request.params.id;
    const currentUser = request.user;

    if (!storyId) {
      throw new NotFoundException("故事ID不能为空");
    }

    if (!currentUser) {
      throw new ForbiddenException("用户未登录");
    }

    try {
      // 获取故事信息
      const story = await this.storiesService.findById(storyId);

      // 检查编辑权限
      const hasEditAccess =
        await this.storyPermissionService.validateEditAccess(
          story,
          currentUser,
        );

      if (!hasEditAccess) {
        throw new ForbiddenException("无权编辑此故事");
      }

      // 将故事信息添加到请求对象中，便于后续使用
      request.story = story;

      return true;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new ForbiddenException("权限验证失败");
    }
  }
}

/**
 * 故事操作权限守卫
 * 用于验证用户是否有权限对故事进行特定操作（点赞、评论、分享等）
 */
@Injectable()
export class StoryActionGuard implements CanActivate {
  constructor(
    private readonly storiesService: StoriesService,
    private readonly storyPermissionService: StoryPermissionService,
    private readonly reflector: Reflector,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const storyId = request.params.id;
    const currentUser = request.user;

    // 从装饰器获取需要验证的操作类型
    const action = this.reflector.get<string>("action", context.getHandler());

    if (!storyId) {
      throw new NotFoundException("故事ID不能为空");
    }

    if (!currentUser) {
      throw new ForbiddenException("用户未登录");
    }

    try {
      // 获取故事信息
      const story = await this.storiesService.findById(storyId);

      // 根据操作类型进行权限验证
      let hasPermission = false;

      switch (action) {
        case "like":
          hasPermission = await this.storyPermissionService.validateLikeAccess(
            story,
            currentUser,
          );
          break;
        case "comment":
          hasPermission =
            await this.storyPermissionService.validateCommentAccess(
              story,
              currentUser,
            );
          break;
        case "share":
          hasPermission = await this.storyPermissionService.validateShareAccess(
            story,
            currentUser,
          );
          break;
        default:
          hasPermission = await this.storyPermissionService.validateAccess(
            story,
            currentUser,
          );
      }

      if (!hasPermission) {
        throw new ForbiddenException(
          `无权对此故事进行${this.getActionName(action)}操作`,
        );
      }

      // 将故事信息添加到请求对象中，便于后续使用
      request.story = story;

      return true;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new ForbiddenException("权限验证失败");
    }
  }

  private getActionName(action: string): string {
    switch (action) {
      case "like":
        return "点赞";
      case "comment":
        return "评论";
      case "share":
        return "分享";
      default:
        return "访问";
    }
  }
}

/**
 * 故事操作类型装饰器
 * 用于标记需要验证的操作类型
 */
export const StoryAction = (action: string) => {
  return (
    target: unknown,
    propertyKey: string,
    descriptor: PropertyDescriptor,
  ) => {
    Reflect.defineMetadata("action", action, descriptor.value);
  };
};
