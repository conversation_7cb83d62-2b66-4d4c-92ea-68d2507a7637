import { IsString, Min<PERSON>eng<PERSON>, <PERSON><PERSON>eng<PERSON> } from "class-validator";
import { ApiProperty } from "@nestjs/swagger";

/**
 * 生成内容请求DTO
 * 用于AI内容生成功能的请求参数验证
 */
export class GenerateContentDto {
  @ApiProperty({
    description: "内容生成提示词，描述想要生成的内容类型和主题",
    example: "一个关于友情的温馨故事",
    minLength: 1,
    maxLength: 200,
  })
  @IsString({ message: "生成提示必须是字符串" })
  @MinLength(1, { message: "生成提示不能为空" })
  @MaxLength(200, { message: "生成提示不能超过200个字符" })
  prompt: string;
}
