import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { BadRequestException } from "@nestjs/common";
import { ImageController } from "./image.controller";
import { ImageService } from "./image.service";
import type { AuthRequest } from "../../common/types/request.types";

describe("ImageController", () => {
  let controller: ImageController;
  let mockImageService: jest.Mocked<ImageService>;

  // Mock数据
  const mockUser = { id: "user-1", username: "testuser", userNumber: "100001" };
  const mockAuthRequest = { user: mockUser } as AuthRequest;

  const mockImageInfo = {
    type: "avatar",
    resourceId: "user-1",
    fileName: "avatar.jpg",
  };

  const mockTemporaryUrl =
    "https://oss.example.com/temp/avatar.jpg?expires=123456";

  beforeEach(async () => {
    const mockServiceMethods = {
      parseImagePath: jest.fn(),
      checkImageAccess: jest.fn(),
      generateTemporaryUrl: jest.fn(),
      getImageStream: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [ImageController],
      providers: [
        {
          provide: ImageService,
          useValue: mockServiceMethods,
        },
      ],
    }).compile();

    controller = module.get<ImageController>(ImageController);
    mockImageService = module.get(ImageService) as jest.Mocked<ImageService>;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("getBatchImageUrls", () => {
    it("应该成功批量获取图片URL", async () => {
      const paths = "avatar/user-1.jpg,cover/story-1.jpg";
      const expires = 7200;

      mockImageService.parseImagePath.mockReturnValue(mockImageInfo);
      mockImageService.checkImageAccess.mockResolvedValue(true);
      mockImageService.generateTemporaryUrl.mockResolvedValue(mockTemporaryUrl);

      const result = await controller.getBatchImageUrls(
        paths,
        mockAuthRequest,
        expires,
      );

      expect(mockImageService.parseImagePath).toHaveBeenCalledTimes(2);
      expect(mockImageService.checkImageAccess).toHaveBeenCalledTimes(2);
      expect(mockImageService.generateTemporaryUrl).toHaveBeenCalledTimes(2);

      expect(result.success).toBe(true);
      expect(result.data.total).toBe(2);
      expect(result.data.successful).toBe(2);
      expect(result.data.expires).toBe(7200);
      expect(result.data.results).toHaveLength(2);
      expect(result.data.results[0].success).toBe(true);
      expect(result.data.results[0].url).toBe(mockTemporaryUrl);
      expect(result.message).toBe("批量URL生成完成");
    });

    it("应该处理部分成功的情况", async () => {
      const paths = "avatar/user-1.jpg,private/secret.jpg";

      mockImageService.parseImagePath.mockReturnValue(mockImageInfo);
      mockImageService.checkImageAccess
        .mockResolvedValueOnce(true) // 第一个图片有权限
        .mockResolvedValueOnce(false); // 第二个图片无权限
      mockImageService.generateTemporaryUrl.mockResolvedValue(mockTemporaryUrl);

      const result = await controller.getBatchImageUrls(paths, mockAuthRequest);

      expect(result.success).toBe(true);
      expect(result.data.total).toBe(2);
      expect(result.data.successful).toBe(1);
      expect(result.data.results[0].success).toBe(true);
      expect(result.data.results[1].success).toBe(false);
      expect(result.data.results[1].error).toBe("无权访问此图片");
    });

    it("应该处理解析错误", async () => {
      const paths = "invalid-path";

      mockImageService.parseImagePath.mockImplementation(() => {
        throw new Error("Invalid image path");
      });

      const result = await controller.getBatchImageUrls(paths, mockAuthRequest);

      expect(result.success).toBe(true);
      expect(result.data.total).toBe(1);
      expect(result.data.successful).toBe(0);
      expect(result.data.results[0].success).toBe(false);
      expect(result.data.results[0].error).toBe("Invalid image path");
    });

    it("应该使用默认过期时间", async () => {
      const paths = "avatar/user-1.jpg";

      mockImageService.parseImagePath.mockReturnValue(mockImageInfo);
      mockImageService.checkImageAccess.mockResolvedValue(true);
      mockImageService.generateTemporaryUrl.mockResolvedValue(mockTemporaryUrl);

      const result = await controller.getBatchImageUrls(paths, mockAuthRequest);

      expect(mockImageService.generateTemporaryUrl).toHaveBeenCalledWith(
        "avatar/user-1.jpg",
        3600, // 默认1小时
      );
      expect(result.data.expires).toBe(3600);
    });

    it("应该处理空路径参数", async () => {
      await expect(
        controller.getBatchImageUrls("", mockAuthRequest),
      ).rejects.toThrow(BadRequestException);

      await expect(
        controller.getBatchImageUrls("   ", mockAuthRequest),
      ).rejects.toThrow(BadRequestException);
    });

    it("应该拒绝过多的图片路径", async () => {
      const paths = Array.from({ length: 51 }, (_, i) => `image${i}.jpg`).join(
        ",",
      );

      await expect(
        controller.getBatchImageUrls(paths, mockAuthRequest),
      ).rejects.toThrow(BadRequestException);
    });

    it("应该过滤空的路径", async () => {
      const paths = "avatar/user-1.jpg,,cover/story-1.jpg,  ";

      mockImageService.parseImagePath.mockReturnValue(mockImageInfo);
      mockImageService.checkImageAccess.mockResolvedValue(true);
      mockImageService.generateTemporaryUrl.mockResolvedValue(mockTemporaryUrl);

      const result = await controller.getBatchImageUrls(paths, mockAuthRequest);

      expect(result.data.total).toBe(2); // 只处理2个有效路径
      expect(mockImageService.parseImagePath).toHaveBeenCalledTimes(2);
    });

    it("应该正确传递权限检查参数", async () => {
      const paths = "avatar/user-1.jpg";

      mockImageService.parseImagePath.mockReturnValue({
        type: "avatar",
        resourceId: "user-1",
        fileName: "avatar.jpg",
      });
      mockImageService.checkImageAccess.mockResolvedValue(true);
      mockImageService.generateTemporaryUrl.mockResolvedValue(mockTemporaryUrl);

      await controller.getBatchImageUrls(paths, mockAuthRequest);

      expect(mockImageService.checkImageAccess).toHaveBeenCalledWith({
        imagePath: "avatar/user-1.jpg",
        imageType: "avatar",
        userId: "user-1",
        storyId: "user-1",
        characterId: "user-1",
      });
    });
  });

  describe("getContentType", () => {
    it("应该正确返回JPEG类型", () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const contentType = (controller as any).getContentType("image.jpg");
      expect(contentType).toBe("image/jpeg");

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const contentType2 = (controller as any).getContentType("image.jpeg");
      expect(contentType2).toBe("image/jpeg");
    });

    it("应该正确返回PNG类型", () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const contentType = (controller as any).getContentType("image.png");
      expect(contentType).toBe("image/png");
    });

    it("应该正确返回GIF类型", () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const contentType = (controller as any).getContentType("image.gif");
      expect(contentType).toBe("image/gif");
    });

    it("应该正确返回WebP类型", () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const contentType = (controller as any).getContentType("image.webp");
      expect(contentType).toBe("image/webp");
    });

    it("应该正确返回BMP类型", () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const contentType = (controller as any).getContentType("image.bmp");
      expect(contentType).toBe("image/bmp");
    });

    it("应该正确返回SVG类型", () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const contentType = (controller as any).getContentType("image.svg");
      expect(contentType).toBe("image/svg+xml");
    });

    it("应该为未知类型返回默认JPEG", () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const contentType = (controller as any).getContentType("image.unknown");
      expect(contentType).toBe("image/jpeg");

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const contentType2 = (controller as any).getContentType("no-extension");
      expect(contentType2).toBe("image/jpeg");
    });

    it("应该处理大小写不敏感", () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const contentType = (controller as any).getContentType("IMAGE.JPG");
      expect(contentType).toBe("image/jpeg");

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const contentType2 = (controller as any).getContentType("Image.PNG");
      expect(contentType2).toBe("image/png");
    });
  });

  it("应该正确注入ImageService", () => {
    expect(controller).toBeDefined();
    expect(mockImageService).toBeDefined();
  });
});
