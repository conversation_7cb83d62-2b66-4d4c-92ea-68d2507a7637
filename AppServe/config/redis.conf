# 有故事APP Redis 7 生产环境配置
# 针对阿里云ECS优化

# ARM64 内核警告忽略 (解决WSL2开发环境问题)
ignore-warnings ARM64-COW-BUG

# 网络配置
bind 0.0.0.0
port 6379
timeout 300
tcp-keepalive 300

# 通用配置
daemonize no                              # Docker容器不需要后台运行
pidfile /var/run/redis.pid

# 日志配置
loglevel notice
logfile ""                                # 输出到stdout，便于Docker日志收集

# 数据库配置
databases 16

# 持久化配置
save 900 1                                # 900秒内有1个key变化就保存
save 300 10                               # 300秒内有10个key变化就保存
save 60 10000                             # 60秒内有10000个key变化就保存

stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# AOF配置 (更安全的持久化)
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# 内存管理
maxmemory 200mb                           # 预留内存
maxmemory-policy allkeys-lru              # LRU淘汰策略

# 安全配置
# requirepass 将通过环境变量设置
protected-mode yes

# 慢查询日志
slowlog-log-slower-than 10000             # 记录超过10ms的命令
slowlog-max-len 128

# 客户端连接
maxclients 100
tcp-backlog 511

# 高级配置
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
list-compress-depth 0
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64
hll-sparse-max-bytes 3000

# 活跃重新散列
activerehashing yes

# 客户端输出缓冲区
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# Hz 设置
hz 10

# AOF重写增量式同步
aof-rewrite-incremental-fsync yes

# RDB快照增量式同步
rdb-save-incremental-fsync yes

# 通知配置
notify-keyspace-events ""

# 延迟监控
latency-monitor-threshold 100 