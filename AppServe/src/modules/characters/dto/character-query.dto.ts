import {
  IsOptional,
  IsEnum,
  IsUUID,
  IsString,
  IsInt,
  Min,
  <PERSON>,
  IsBoolean,
  IsNotEmpty,
  Max<PERSON>ength,
} from "class-validator";
import { Transform } from "class-transformer";

/**
 * 人物查询DTO
 * 用于人物列表查询的参数验证
 */
export class CharacterQueryDto {
  @IsOptional()
  @IsInt()
  @Min(1)
  @Transform(({ value }) => parseInt(value))
  page?: number = 1;

  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(100)
  @Transform(({ value }) => parseInt(value))
  limit?: number = 10;

  @IsOptional()
  @IsUUID()
  creatorId?: string;

  @IsOptional()
  @IsUUID()
  storyId?: string;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === "true" || value === true)
  isLighted?: boolean;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === "true" || value === true)
  isActive?: boolean;

  @IsOptional()
  @IsEnum(["男", "女", "未知"])
  gender?: string;

  @IsOptional()
  @IsEnum(["家人", "朋友", "同事", "同学", "恋人", "配偶", "其他"])
  relationship?: string;

  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsEnum(["newest", "oldest", "name_asc", "name_desc", "most_lighted"])
  sortBy?: string = "newest";
}

/**
 * 人物名称验证DTO
 */
export class ValidateCharacterNameDto {
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  name: string;
}

/**
 * 人物统计DTO
 */
export class CharacterStatisticsDto {
  totalCharacters: number;
  lightedCharacters: number;
  unlightedCharacters: number;
  totalLightings: number;
  lightingRate: number;
  relationshipDistribution: Record<string, number>;
  genderDistribution: Record<string, number>;
}
