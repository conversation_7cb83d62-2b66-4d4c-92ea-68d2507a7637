import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  Index,
} from "typeorm";
import { User } from "../../users/entities/user.entity";
import { Story } from "../../stories/entities/story.entity";

export enum CommentTargetType {
  STORY = "story",
  COMMENT = "comment",
}

export enum CommentStatus {
  ACTIVE = "active",
  DELETED = "deleted",
  HIDDEN = "hidden",
  REPORTED = "reported",
}

/**
 * 评论实体
 * 支持对故事和评论的多级回复
 */
@Entity("comments")
@Index(["targetType", "targetId", "status"])
@Index(["authorId", "status"])
@Index(["createdAt"])
export class Comment {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({
    type: "enum",
    enum: CommentTargetType,
    comment: "评论目标类型：story-故事，comment-评论",
  })
  targetType: CommentTargetType;

  @Column({
    type: "uuid",
    comment: "目标ID（故事ID或父评论ID）",
  })
  targetId: string;

  @Column({
    type: "text",
    comment: "评论内容",
  })
  content: string;

  @Column({
    type: "uuid",
    comment: "评论作者ID",
  })
  authorId: string;

  @Column({
    type: "enum",
    enum: CommentStatus,
    default: CommentStatus.ACTIVE,
    comment: "评论状态",
  })
  status: CommentStatus;

  @Column({
    type: "int",
    default: 0,
    comment: "点赞数",
  })
  likeCount: number;

  @Column({
    type: "int",
    default: 0,
    comment: "回复数",
  })
  replyCount: number;

  @Column({
    type: "uuid",
    nullable: true,
    comment: "根评论ID（用于多级回复的扁平化存储）",
  })
  rootCommentId: string;

  @Column({
    type: "uuid",
    nullable: true,
    comment: "直接回复的评论ID",
  })
  parentCommentId: string;

  @Column({
    type: "int",
    default: 0,
    comment: "回复层级深度",
  })
  depth: number;

  @Column({
    type: "jsonb",
    nullable: true,
    comment: "扩展字段（提及用户、附件等）",
  })
  metadata: Record<string, unknown>;

  @CreateDateColumn({
    type: "timestamp with time zone",
    comment: "创建时间",
  })
  createdAt: Date;

  @UpdateDateColumn({
    type: "timestamp with time zone",
    comment: "更新时间",
  })
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => User, { eager: false })
  @JoinColumn({ name: "authorId" })
  author: User;

  @ManyToOne(() => Story, { eager: false })
  @JoinColumn({ name: "targetId" })
  targetStory?: Story;

  @ManyToOne(() => Comment, { eager: false })
  @JoinColumn({ name: "parentCommentId" })
  parentComment?: Comment;

  @OneToMany(() => Comment, (comment) => comment.parentComment)
  replies: Comment[];

  @ManyToOne(() => Comment, { eager: false })
  @JoinColumn({ name: "rootCommentId" })
  rootComment?: Comment;
}
