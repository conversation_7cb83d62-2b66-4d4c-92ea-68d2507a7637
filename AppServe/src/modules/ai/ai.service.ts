import {
  Injectable,
  BadRequestException,
  ForbiddenException,
  Logger,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, MoreThan } from "typeorm";
import { EnhancedRedisService } from "../../common/services/enhanced-redis.service";
import { AiUsageLog } from "./entities/ai-usage-log.entity";
import { User } from "../users/entities/user.entity";
import { ConfigService } from "@nestjs/config";
import { MockAIProvider } from "./providers/mock-ai.provider";
// 删除未使用的 AIProvider 导入

/**
 * AI服务类 - v1.0.0
 * 实现AI辅助创作功能，包括配额管理和成本控制
 *
 * 核心功能：
 * - AI生成标题和内容（使用模拟数据）
 * - 每日配额管理（3次/天）
 * - 使用记录和成本追踪
 * - 企业级错误处理和日志记录
 *
 * 设计模式：
 * - 策略模式：支持多种AI服务提供商
 * - 配额模式：Redis实现的配额控制
 * - 工厂模式：AI响应生成器
 */
@Injectable()
export class AiService {
  private readonly logger = new Logger(AiService.name);
  private readonly DAILY_QUOTA_LIMIT = 3; // 每日3次配额
  private readonly MOCK_AI_ENABLED = true; // v1.0.0阶段使用模拟数据

  constructor(
    @InjectRepository(AiUsageLog)
    private readonly aiUsageLogRepository: Repository<AiUsageLog>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly enhancedRedisService: EnhancedRedisService,
    private readonly configService: ConfigService,
    private readonly aiProvider: MockAIProvider,
  ) {
    this.logger.log("AI服务已初始化 - 使用Mock AI Provider");
  }

  /**
   * 生成故事标题
   * @param userId 用户ID
   * @param keywords 关键词数组
   * @returns 生成的标题
   */
  async generateStoryTitle(
    userId: string,
    keywords: string[],
  ): Promise<string> {
    this.logger.log(
      `用户 ${userId} 请求生成故事标题，关键词: ${keywords.join(", ")}`,
    );

    // 1. 检查和消耗配额
    const hasQuota = await this.checkAndConsumeQuota(userId);
    if (!hasQuota) {
      throw new ForbiddenException("今日AI生成配额已用完，请明日再试");
    }

    // 2. 验证关键词（在try-catch之外，验证错误直接抛出）
    this.validateKeywords(keywords);

    try {
      // 3. 生成标题（通过AI提供商）
      const title = await this.aiProvider.generateTitle(keywords);

      // 4. 记录使用日志
      await this.logAiUsage(userId, "title_generation", {
        promptTokens: this.estimateTokens(keywords.join(" ")),
        completionTokens: this.estimateTokens(title),
        success: true,
      });

      this.logger.log(`标题生成成功: "${title}"`);
      return title;
    } catch (error) {
      // 失败时退还配额
      await this.refundQuota(userId);

      // 记录失败日志
      await this.logAiUsage(userId, "title_generation", {
        promptTokens: this.estimateTokens(keywords.join(" ")),
        completionTokens: 0,
        success: false,
      });

      this.logger.error(
        `标题生成失败: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined,
      );
      throw new BadRequestException("标题生成失败，请稍后重试");
    }
  }

  /**
   * 生成故事内容
   * @param userId 用户ID
   * @param prompt 生成提示
   * @returns 生成的内容
   */
  async generateStoryContent(userId: string, prompt: string): Promise<string> {
    this.logger.log(`用户 ${userId} 请求生成故事内容`);

    // 1. 检查和消耗配额
    const hasQuota = await this.checkAndConsumeQuota(userId);
    if (!hasQuota) {
      throw new ForbiddenException("今日AI生成配额已用完，请明日再试");
    }

    // 2. 验证提示词（在try-catch之外，验证错误直接抛出）
    this.validatePrompt(prompt);

    try {
      // 3. 生成内容（通过AI提供商）
      const content = await this.aiProvider.generateStoryContent(prompt);

      // 4. 记录使用日志
      await this.logAiUsage(userId, "content_generation", {
        promptTokens: this.estimateTokens(prompt),
        completionTokens: this.estimateTokens(content),
        success: true,
      });

      this.logger.log(`内容生成成功，长度: ${content.length} 字符`);
      return content;
    } catch (error) {
      // 失败时退还配额
      await this.refundQuota(userId);

      // 记录失败日志
      await this.logAiUsage(userId, "content_generation", {
        promptTokens: this.estimateTokens(prompt),
        completionTokens: 0,
        success: false,
      });

      this.logger.error(
        `内容生成失败: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined,
      );
      throw new BadRequestException("内容生成失败，请稍后重试");
    }
  }

  /**
   * 检查并消耗配额
   * @param userId 用户ID
   * @returns 是否有可用配额
   */
  async checkAndConsumeQuota(userId: string): Promise<boolean> {
    const today = new Date().toISOString().split("T")[0];
    const quotaKey = `ai_quota:${userId}:${today}`;

    try {
      // 原子性地递增计数
      const currentUsage = await this.enhancedRedisService.incr(quotaKey);

      // 首次使用时设置过期时间
      if (currentUsage === 1) {
        const endOfDay = new Date();
        endOfDay.setHours(23, 59, 59, 999);
        await this.enhancedRedisService.expireAt(
          quotaKey,
          Math.floor(endOfDay.getTime() / 1000),
        );
      }

      // 检查是否超出限制
      if (currentUsage > this.DAILY_QUOTA_LIMIT) {
        // 超出限制时递减计数
        await this.enhancedRedisService.decr(quotaKey);
        this.logger.warn(
          `用户 ${userId} 配额已用完，当前使用次数: ${currentUsage - 1}`,
        );
        return false;
      }

      this.logger.log(
        `用户 ${userId} 配额检查通过，当前使用次数: ${currentUsage}`,
      );
      return true;
    } catch (error) {
      this.logger.error(
        `配额检查失败: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined,
      );
      // Redis故障时降级处理，允许使用但记录错误
      return true;
    }
  }

  /**
   * 获取剩余配额
   * @param userId 用户ID
   * @returns 剩余配额数量
   */
  async getRemainingQuota(userId: string): Promise<number> {
    const today = new Date().toISOString().split("T")[0];
    const quotaKey = `ai_quota:${userId}:${today}`;

    try {
      const currentUsage = await this.enhancedRedisService.get(quotaKey);
      const parsedUsage = currentUsage ? parseInt(currentUsage, 10) : 0;
      const usedCount = isNaN(parsedUsage) ? 0 : parsedUsage;
      const remaining = Math.max(0, this.DAILY_QUOTA_LIMIT - usedCount);

      this.logger.debug(`用户 ${userId} 剩余配额: ${remaining}`);
      return remaining;
    } catch (error) {
      this.logger.error(
        `获取剩余配额失败: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined,
      );
      return this.DAILY_QUOTA_LIMIT; // 降级处理
    }
  }

  /**
   * 重置每日配额（手动触发）
   * @param userId 用户ID
   */
  async resetDailyQuota(userId: string): Promise<void> {
    const today = new Date().toISOString().split("T")[0];
    const quotaKey = `ai_quota:${userId}:${today}`;

    try {
      await this.enhancedRedisService.del(quotaKey);
      this.logger.log(`用户 ${userId} 每日配额已重置`);
    } catch (error) {
      this.logger.error(
        `配额重置失败: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined,
      );
      throw new BadRequestException("配额重置失败");
    }
  }

  /**
   * 退还配额（失败时调用）
   * @param userId 用户ID
   */
  private async refundQuota(userId: string): Promise<void> {
    const today = new Date().toISOString().split("T")[0];
    const quotaKey = `ai_quota:${userId}:${today}`;

    try {
      await this.enhancedRedisService.decr(quotaKey);
      this.logger.log(`用户 ${userId} 配额已退还`);
    } catch (error) {
      this.logger.error(
        `配额退还失败: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined,
      );
    }
  }

  /**
   * 记录AI使用日志
   * @param userId 用户ID
   * @param featureType 功能类型
   * @param usage 使用情况
   */
  async logAiUsage(
    userId: string,
    featureType:
      | "title_generation"
      | "content_generation"
      | "content_optimization",
    usage: {
      promptTokens: number;
      completionTokens: number;
      success: boolean;
    },
  ): Promise<void> {
    try {
      const totalTokens = usage.promptTokens + usage.completionTokens;
      const costEstimate = this.calculateCostEstimate(totalTokens);

      const usageLog = this.aiUsageLogRepository.create({
        userId,
        featureType,
        promptTokens: usage.promptTokens,
        completionTokens: usage.completionTokens,
        totalTokens,
        costEstimate,
        success: usage.success,
      });

      await this.aiUsageLogRepository.save(usageLog);
      this.logger.debug(
        `AI使用日志已记录: ${featureType}, 成功: ${usage.success}`,
      );
    } catch (error) {
      this.logger.error(
        `记录AI使用日志失败: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined,
      );
      // 日志记录失败不应该影响主流程
    }
  }

  /**
   * 获取用户AI使用统计
   * @param userId 用户ID
   * @param days 统计天数
   * @returns 使用统计
   */
  async getUserUsageStatistics(
    userId: string,
    days: number = 30,
  ): Promise<{
    totalUsage: number;
    successfulUsage: number;
    totalCost: number;
    averageCostPerRequest: number;
    usageByFeature: Record<string, number>;
  }> {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const logs = await this.aiUsageLogRepository.find({
        where: {
          userId,
          createdAt: MoreThan(startDate),
        },
        order: { createdAt: "DESC" },
      });

      const totalUsage = logs.length;
      const successfulUsage = logs.filter((log) => log.success).length;
      const totalCost = logs.reduce(
        (sum, log) => sum + (log.costEstimate || 0),
        0,
      );
      const averageCostPerRequest = totalUsage > 0 ? totalCost / totalUsage : 0;

      const usageByFeature = logs.reduce(
        (acc, log) => {
          acc[log.featureType] = (acc[log.featureType] || 0) + 1;
          return acc;
        },
        {} as Record<string, number>,
      );

      return {
        totalUsage,
        successfulUsage,
        totalCost,
        averageCostPerRequest,
        usageByFeature,
      };
    } catch (error) {
      this.logger.error(
        `获取用户使用统计失败: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined,
      );
      throw new BadRequestException("获取使用统计失败");
    }
  }

  /**
   * 获取AI服务提供商信息
   */
  async getProviderInfo(): Promise<{
    name: string;
    version: string;
    isAvailable: boolean;
    healthStatus: unknown;
  }> {
    const healthStatus = await this.aiProvider.healthCheck();
    const isAvailable = await this.aiProvider.isAvailable();

    return {
      name: this.aiProvider.name,
      version: this.aiProvider.version,
      isAvailable,
      healthStatus,
    };
  }

  /**
   * 切换AI服务提供商（管理功能）
   * 预留接口，后续可支持动态切换不同的AI服务提供商
   */
  async switchProvider(providerName: string): Promise<void> {
    // TODO: 实现动态切换逻辑
    this.logger.log(`请求切换到AI提供商: ${providerName}`);
    throw new BadRequestException("AI提供商切换功能暂未实现");
  }

  /**
   * 验证关键词
   */
  private validateKeywords(keywords: string[]): void {
    if (!keywords || keywords.length === 0) {
      throw new BadRequestException("关键词不能为空");
    }

    if (keywords.length > 5) {
      throw new BadRequestException("关键词不能超过5个");
    }

    for (const keyword of keywords) {
      if (!keyword || keyword.trim().length === 0) {
        throw new BadRequestException("关键词不能为空");
      }

      if (keyword.length > 10) {
        throw new BadRequestException("单个关键词不能超过10个字符");
      }
    }
  }

  /**
   * 验证提示词
   */
  private validatePrompt(prompt: string): void {
    if (!prompt || prompt.trim().length === 0) {
      throw new BadRequestException("生成提示不能为空");
    }

    if (prompt.length > 200) {
      throw new BadRequestException("生成提示不能超过200个字符");
    }

    // 基础内容安全检测
    const sensitiveWords = ["政治", "暴力", "血腥", "恐怖"];
    for (const word of sensitiveWords) {
      if (prompt.includes(word)) {
        throw new BadRequestException("提示内容包含敏感词，请修改后重试");
      }
    }
  }

  /**
   * 估算令牌数量
   */
  private estimateTokens(text: string): number {
    // 简单的中文令牌估算：1个中文字符约等于1.5个令牌
    const chineseChars = (text.match(/[\u4e00-\u9fa5]/g) || []).length;
    const englishWords = text
      .split(/\s+/)
      .filter((word) => /[a-zA-Z]/.test(word)).length;

    return Math.ceil(chineseChars * 1.5 + englishWords);
  }

  /**
   * 计算成本估算
   */
  private calculateCostEstimate(totalTokens: number): number {
    // 模拟成本计算：每1000个令牌约0.01元
    return (totalTokens / 1000) * 0.01;
  }
}
