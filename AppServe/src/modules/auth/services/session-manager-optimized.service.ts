import { Injectable, Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, MoreThan, <PERSON>Than } from "typeorm";
import { RefreshToken } from "../../users/entities/refresh-token.entity";
import { User } from "../../users/entities/user.entity";
import { createHash } from "crypto";
import type { DeviceInfo } from "../../../common/types/request.types";

/**
 * 优化版会话管理服务
 *
 * 优化策略：
 * - 移除定时任务，降低资源消耗
 * - 数据库层面过滤过期token，提升查询效率
 * - 提供手动清理接口，保持数据整洁
 * - 保留完整的监控和扩展能力
 */
@Injectable()
export class OptimizedSessionManagerService {
  private readonly logger = new Logger(OptimizedSessionManagerService.name);
  private readonly maxConcurrentSessions: number;
  private readonly enableMonitoring: boolean;

  constructor(
    @InjectRepository(RefreshToken)
    private readonly refreshTokenRepository: Repository<RefreshToken>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly configService: ConfigService,
  ) {
    this.maxConcurrentSessions = this.configService.get<number>(
      "auth.maxConcurrentSessions",
      1,
    );
    this.enableMonitoring = this.configService.get<boolean>(
      "auth.enableSessionMonitoring",
      true,
    );

    this.logger.log(
      `优化版会话管理初始化 - 最大并发会话: ${this.maxConcurrentSessions}`,
    );
  }

  /**
   * 验证并发会话限制（优化版）
   */
  async validateConcurrentSessions(
    userId: string,
    deviceInfo?: DeviceInfo,
  ): Promise<void> {
    // 只统计未过期的活跃会话
    const activeSessions = await this.refreshTokenRepository.count({
      where: {
        userId,
        isActive: true,
        expiresAt: MoreThan(new Date()), // 数据库层面过滤过期token
      },
    });

    if (this.enableMonitoring) {
      this.logger.debug(`用户 ${userId} 当前有效会话数: ${activeSessions}`);
    }

    if (activeSessions >= this.maxConcurrentSessions) {
      await this.handleSessionLimit(userId, deviceInfo);
    }
  }

  /**
   * 创建新会话令牌
   */
  async createSession(
    user: User,
    tokenHash: string,
    deviceInfo?: DeviceInfo,
  ): Promise<RefreshToken> {
    const deviceFingerprint = this.generateDeviceFingerprint(deviceInfo);

    const refreshToken = this.refreshTokenRepository.create({
      userId: user.id,
      tokenHash,
      deviceFingerprint,
      deviceInfo: deviceInfo ? this.sanitizeDeviceInfo(deviceInfo) : undefined,
      expiresAt: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14天
      isActive: true,
    });

    const savedToken = await this.refreshTokenRepository.save(refreshToken);

    if (this.enableMonitoring) {
      this.logger.debug(`会话创建: 用户=${user.id}, 设备=${deviceFingerprint}`);
    }

    return savedToken;
  }

  /**
   * 验证会话（优化版 - 自动处理过期token）
   */
  async validateSession(tokenHash: string): Promise<RefreshToken | null> {
    // 数据库查询时直接过滤过期token
    const session = await this.refreshTokenRepository.findOne({
      where: {
        tokenHash,
        isActive: true,
        expiresAt: MoreThan(new Date()), // 关键优化：数据库层面过滤
      },
      relations: ["user"],
    });

    if (session) {
      // 更新最后使用时间
      session.lastUsedAt = new Date();
      await this.refreshTokenRepository.save(session);
    }

    return session;
  }

  /**
   * 注销用户所有会话
   */
  async invalidateAllUserSessions(userId: string): Promise<void> {
    await this.refreshTokenRepository.update(
      { userId, isActive: true },
      { isActive: false },
    );

    if (this.enableMonitoring) {
      this.logger.log(`已注销用户 ${userId} 的所有会话`);
    }
  }

  /**
   * 注销特定会话
   */
  async invalidateSession(tokenHash: string): Promise<void> {
    await this.refreshTokenRepository.update(
      { tokenHash },
      { isActive: false },
    );
  }

  /**
   * 手动清理过期会话（替代定时任务）
   * 用于管理后台或定期维护
   */
  async manualCleanupExpiredSessions(): Promise<{
    success: boolean;
    cleanedCount: number;
    duration: number;
    message: string;
  }> {
    try {
      this.logger.log("开始手动清理过期会话...");

      const startTime = Date.now();

      // 批量更新过期但仍标记为active的token
      const result = await this.refreshTokenRepository.update(
        {
          isActive: true,
          expiresAt: LessThan(new Date()), // 清理已过期的token
        },
        { isActive: false },
      );

      const cleanedCount = result.affected || 0;
      const duration = Date.now() - startTime;

      this.logger.log(
        `手动清理完成 - 清理数量: ${cleanedCount}, 耗时: ${duration}ms`,
      );

      return {
        success: true,
        cleanedCount,
        duration,
        message: `成功清理 ${cleanedCount} 个过期会话`,
      };
    } catch (error) {
      this.logger.error("手动会话清理失败", error);
      return {
        success: false,
        cleanedCount: 0,
        duration: 0,
        message: `清理失败: ${(error as Error).message}`,
      };
    }
  }

  /**
   * 获取会话统计信息（包含清理建议）
   */
  async getUserSessionStats(userId: string): Promise<{
    activeCount: number;
    totalCount: number;
    expiredCount: number;
    devices: string[];
    lastLoginTime: Date | null;
    cleanupRecommended: boolean;
  }> {
    const allSessions = await this.refreshTokenRepository.find({
      where: { userId },
      order: { createdAt: "DESC" },
    });

    const now = new Date();
    const activeSessions = allSessions.filter(
      (s) => s.isActive && s.expiresAt > now,
    );
    const expiredSessions = allSessions.filter(
      (s) => s.isActive && s.expiresAt <= now,
    );
    const devices = [
      ...new Set(allSessions.map((s) => s.deviceFingerprint).filter(Boolean)),
    ];
    const lastLogin = allSessions.length > 0 ? allSessions[0].lastUsedAt : null;

    return {
      activeCount: activeSessions.length,
      totalCount: allSessions.length,
      expiredCount: expiredSessions.length,
      devices,
      lastLoginTime: lastLogin,
      cleanupRecommended: expiredSessions.length > 10, // 超过10个过期会话建议清理
    };
  }

  /**
   * 获取系统级会话统计（用于监控）
   */
  async getSystemSessionStats(): Promise<{
    totalActiveSessions: number;
    totalExpiredSessions: number;
    cleanupRecommended: boolean;
    oldestExpiredSession: Date | null;
  }> {
    const now = new Date();

    const [activeCount, expiredCount, oldestExpired] = await Promise.all([
      // 活跃会话数
      this.refreshTokenRepository.count({
        where: { isActive: true, expiresAt: MoreThan(now) },
      }),

      // 过期但未清理的会话数
      this.refreshTokenRepository.count({
        where: { isActive: true, expiresAt: LessThan(now) },
      }),

      // 最旧的过期会话
      this.refreshTokenRepository.findOne({
        where: { isActive: true, expiresAt: LessThan(now) },
        order: { expiresAt: "ASC" },
        select: ["expiresAt"],
      }),
    ]);

    return {
      totalActiveSessions: activeCount,
      totalExpiredSessions: expiredCount,
      cleanupRecommended: expiredCount > 100, // 超过100个过期会话建议清理
      oldestExpiredSession: oldestExpired?.expiresAt || null,
    };
  }

  // ==================== 私有方法 ====================

  private async handleSessionLimit(
    userId: string,
    deviceInfo?: DeviceInfo,
  ): Promise<void> {
    const deviceFingerprint = this.generateDeviceFingerprint(deviceInfo);

    // 检查是否是同一设备的重新登录
    const sameDeviceSession = await this.refreshTokenRepository.findOne({
      where: {
        userId,
        deviceFingerprint,
        isActive: true,
        expiresAt: MoreThan(new Date()), // 确保未过期
      },
    });

    if (sameDeviceSession) {
      await this.invalidateSession(sameDeviceSession.tokenHash);
      if (this.enableMonitoring) {
        this.logger.log(`同设备重新登录，替换会话: ${userId}`);
      }
      return;
    }

    // 不同设备登录，根据策略处理
    if (this.maxConcurrentSessions === 1) {
      await this.invalidateAllUserSessions(userId);
      if (this.enableMonitoring) {
        this.logger.log(`单会话限制，注销用户所有会话: ${userId}`);
      }
    } else {
      await this.invalidateOldestValidSession(userId);
      if (this.enableMonitoring) {
        this.logger.log(`多会话限制，注销最旧有效会话: ${userId}`);
      }
    }
  }

  private async invalidateOldestValidSession(userId: string): Promise<void> {
    const oldestSession = await this.refreshTokenRepository.findOne({
      where: {
        userId,
        isActive: true,
        expiresAt: MoreThan(new Date()), // 只考虑未过期的会话
      },
      order: { lastUsedAt: "ASC" },
    });

    if (oldestSession) {
      await this.invalidateSession(oldestSession.tokenHash);
    }
  }

  private generateDeviceFingerprint(deviceInfo?: DeviceInfo): string {
    if (!deviceInfo) {
      return `unknown_${Date.now()}`;
    }

    const { userAgent, ip, platform } = deviceInfo;
    const fingerprint = `${userAgent || ""}|${ip || ""}|${platform || ""}`;

    return createHash("md5").update(fingerprint).digest("hex").substring(0, 16);
  }

  private sanitizeDeviceInfo(deviceInfo?: DeviceInfo): DeviceInfo | undefined {
    if (!deviceInfo) return undefined;

    return {
      userAgent: deviceInfo.userAgent?.substring(0, 500) || undefined,
      platform: deviceInfo.platform || undefined,
      version: deviceInfo.version || undefined,
      ip: deviceInfo.ip || undefined,
    };
  }
}
