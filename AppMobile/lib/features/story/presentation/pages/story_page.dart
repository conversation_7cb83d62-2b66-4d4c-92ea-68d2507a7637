import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../design_system/tokens/ygs_colors.dart';
import '../../../../design_system/tokens/ygs_typography.dart';
import '../widgets/hybrid_glass_theme_card.dart';
import '../widgets/create_new_theme_card.dart';

/// 故事模块页面 - 时间轴模式
class StoryPage extends ConsumerStatefulWidget {
  const StoryPage({super.key});

  @override
  ConsumerState<StoryPage> createState() => _StoryPageState();
}

class _StoryPageState extends ConsumerState<StoryPage> {
  int _currentTabIndex = 0; // 0: 时间, 1: 主题, 2: 人物, 3: 引用
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: YgsColors.backgroundPrimary, // #F4F7FA
      body: SafeArea(
        child: Column(
          children: [
            // 顶部tab导航栏
            _buildTopTabBar(),

            // 主要内容区域 - PageView切换
            Expanded(
              child: PageView(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentTabIndex = index;
                  });
                },
                children: [
                  _buildTimelineTab(), // 时间tab页面
                  _buildThemeTab(), // 主题tab页面
                  _buildCharacterTab(), // 人物tab页面
                  _buildQuoteTab(), // 引用tab页面
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建顶部tab导航栏 - 彻底优化的自定义方案
  Widget _buildTopTabBar() {
    return Container(
      color: YgsColors.backgroundPrimary,
      child: Column(
        children: [
          // Tab行容器 - 使用固定高度确保一致性
          SizedBox(
            height: 80.h,
            child: Row(
              children: [
                // 时间tab - 固定宽度，精确对齐时间线
                _buildTimeTab(),

                // 其他三个tab - 使用绝对定位对齐下划线
                Expanded(
                  child: Stack(
                    children: [
                      _buildPositionedTab('主题', 1),
                      _buildPositionedTab('人物', 2),
                      _buildPositionedTab('引用', 3),
                    ],
                  ),
                ),

                // 添加按钮
                _buildAddButton(),
              ],
            ),
          ),

          // 底部分割线 + 指示器容器
          SizedBox(
            height: 8.h,
            child: Stack(
              children: [
                // 底部分割线
                Positioned(
                  left: 0,
                  right: 0,
                  bottom: 0,
                  child: Container(
                    height: 1.h,
                    color: YgsColors.divider,
                  ),
                ),

                // 下划线指示器 - 直接跟随当前tab
                AnimatedPositioned(
                  duration: const Duration(milliseconds: 150), // 快速响应
                  curve: Curves.easeOut,
                  left: _getIndicatorLeft(),
                  bottom: 0.h, // 向下移动1.h（最小单位）
                  child: Container(
                    width: _getIndicatorWidth(),
                    height: 4.h,
                    decoration: BoxDecoration(
                      color: const Color(0xFF0F0F23),
                      borderRadius: BorderRadius.circular(2.r),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建时间tab - 精确对齐时间线
  Widget _buildTimeTab() {
    return SizedBox(
      width: 160.w, // 固定宽度，给时间线留出空间
      child: GestureDetector(
        onTap: () => _pageController.animateToPage(0,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut),
        behavior: HitTestBehavior.opaque,
        child: Stack(
          children: [
            // 时间文字，精确定位到90.w时间线位置
            Positioned(
              left: 58.w, // 90.w - 32.w = 58.w，让"时间"二字中心对齐时间线
              top: 0,
              bottom: 0,
              child: Align(
                alignment: Alignment.center,
                child: Text(
                  '时间',
                  style: _currentTabIndex == 0
                      ? YgsTypography.headlineSmall.copyWith(
                          fontSize: 32.sp,
                          fontWeight: FontWeight.w600,
                          color: YgsColors.textPrimary,
                        )
                      : YgsTypography.headlineSmall.copyWith(
                          fontSize: 28.sp,
                          fontWeight: FontWeight.w500,
                          color: YgsColors.textSecondary,
                        ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建绝对定位tab - 精确对齐下划线
  Widget _buildPositionedTab(String text, int index) {
    // 计算tab文字的精确位置，与下划线算法保持一致
    final screenWidth = MediaQuery.sizeOf(context).width;
    final timePosition = 90.w; // 调整为新的时间轴位置
    final buttonAreaWidth = 100.w;
    final quotePosition = screenWidth - buttonAreaWidth - 80.w;
    final totalDistance = quotePosition - timePosition;
    final singleGap = totalDistance / 3;

    // 计算当前tab的中心位置
    double tabCenter;
    switch (index) {
      case 1: // 主题
        tabCenter = timePosition + singleGap;
        break;
      case 2: // 人物
        tabCenter = timePosition + (singleGap * 2);
        break;
      case 3: // 引用
        tabCenter = timePosition + (singleGap * 3);
        break;
      default:
        tabCenter = 0;
    }

    // 文字左边位置 = 中心位置 - 文字宽度一半
    final textLeft = tabCenter - 32.w; // 32.w是两个中文字符宽度的一半

    return Positioned(
      left: textLeft - 160.w, // 减去时间tab宽度，因为这是在Expanded容器内
      top: 0,
      bottom: 0,
      child: GestureDetector(
        onTap: () => _pageController.animateToPage(index,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut),
        behavior: HitTestBehavior.opaque,
        child: Container(
          width: 64.w, // 给点击区域足够宽度
          alignment: Alignment.center,
          child: Text(
            text,
            style: _currentTabIndex == index
                ? YgsTypography.headlineSmall.copyWith(
                    fontSize: 32.sp,
                    fontWeight: FontWeight.w600,
                    color: YgsColors.textPrimary,
                  )
                : YgsTypography.headlineSmall.copyWith(
                    fontSize: 28.sp,
                    fontWeight: FontWeight.w500,
                    color: YgsColors.textSecondary,
                  ),
          ),
        ),
      ),
    );
  }

  /// 构建添加按钮
  Widget _buildAddButton() {
    return Padding(
      padding: EdgeInsets.only(right: 36.w),
      child: GestureDetector(
        onTap: () {
          // TODO: 添加创作功能
        },
        child: Container(
          width: 64.w,
          height: 64.w,
          decoration: const BoxDecoration(
            color: Color(0xFFFF4B4B),
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.add,
            color: Colors.white,
            size: 32.w,
          ),
        ),
      ),
    );
  }

  /// 获取指示器左边位置 - 四个tab等间距分布
  double _getIndicatorLeft() {
    final screenWidth = MediaQuery.sizeOf(context).width;
    final indicatorWidth = _getIndicatorWidth();

    // 关键位置定义
    final timePosition = 90.w; // 时间tab位置(对齐时间线) - 调整为新位置
    final buttonAreaWidth = 100.w; // 右侧按钮区域
    final quotePosition =
        screenWidth - buttonAreaWidth - 80.w; // 引用tab位置(向左移动30.w给+号更多空间)

    // 计算四个tab的等间距分布
    final totalDistance = quotePosition - timePosition; // 时间到引用的总距离
    final singleGap = totalDistance / 3; // 三个间隔的单位距离

    switch (_currentTabIndex) {
      case 0: // 时间tab - 固定位置对齐时间线
        return timePosition - (indicatorWidth / 2);

      case 1: // 主题tab - 时间+1个间隔
        final tabCenter = timePosition + singleGap;
        return tabCenter - (indicatorWidth / 2);

      case 2: // 人物tab - 时间+2个间隔
        final tabCenter = timePosition + (singleGap * 2);
        return tabCenter - (indicatorWidth / 2);

      case 3: // 引用tab - 时间+3个间隔
        final tabCenter = timePosition + (singleGap * 3);
        return tabCenter - (indicatorWidth / 2);

      default:
        return 0;
    }
  }

  /// 获取指示器宽度 - 基于文字宽度，稍微加长
  double _getIndicatorWidth() {
    switch (_currentTabIndex) {
      case 0:
        return 84.w; // 时间 - 加长一点
      case 1:
        return 84.w; // 主题 - 加长一点
      case 2:
        return 84.w; // 人物 - 加长一点
      case 3:
        return 84.w; // 引用 - 加长一点
      default:
        return 84.w;
    }
  }

  /// 构建时间tab页面 - 完整的时间轴内容
  Widget _buildTimelineTab() {
    return Stack(
      children: [
        // 时间轴背景线 - 直接从tab栏开始连接
        Positioned(
          left: 90.w, // 调整为新的底部导航首页图标位置 (40.w + 50.w)
          top: -20.h, // 向上延伸，连接到tab栏内
          bottom: 0, // 延伸到底部
          child: Container(
            width: 1.w,
            color: const Color(0xFF151523),
          ),
        ),
        // 时间轴内容
        _buildTimelineContent(),
      ],
    );
  }

  /// 构建主题tab页面 - 严格按照Figma设计稿实现
  Widget _buildThemeTab() {
    return Container(
      color: YgsColors.backgroundPrimary,
      child: ListView(
        padding: EdgeInsets.only(
          left: 26.w,
          right: 26.w,
          top: 60.h,
          bottom: 160.h,
        ),
        children: [
          // 第一行：创建新主题 + 孩子们
          Row(
            children: [
              Expanded(
                child: CreateNewThemeCard(
                  onTap: () {
                    print('点击了创建新主题卡片');
                    // TODO: 跳转到创建主题页面
                  },
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: HybridGlassThemeCard(
                  title: '#孩子们',
                  subtitle: '已有56个故事',
                  iconPath: 'assets/icons/themes/icon-children-02.png',
                  backgroundImagePath: 'assets/images/story_covers/孩子们.png',
                  onTap: () {
                    print('点击了孩子们主题卡片');
                  },
                ),
              ),
            ],
          ),

          SizedBox(height: 20.h),

          // 第二行：爱情故事 + 学生时代
          Row(
            children: [
              Expanded(
                child: HybridGlassThemeCard(
                  title: '#爱情故事',
                  subtitle: '已有56个故事',
                  iconPath: 'assets/icons/themes/icon-romance-02.png',
                  backgroundImagePath: 'assets/images/story_covers/爱情故事.png',
                  onTap: () {
                    print('点击了爱情故事主题卡片');
                  },
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: HybridGlassThemeCard(
                  title: '#学生时代',
                  subtitle: '已有56个故事',
                  iconPath: 'assets/icons/themes/icon-school days-02.png',
                  backgroundImagePath: 'assets/images/story_covers/学生时代.png',
                  onTap: () {
                    print('点击了学生时代主题卡片');
                  },
                ),
              ),
            ],
          ),

          SizedBox(height: 20.h),

          // 第二行：学生时代 + 那些成长
          Row(
            children: [
              Expanded(
                child: HybridGlassThemeCard(
                  title: '#学生时代',
                  subtitle: '已有56个故事',
                  iconPath: 'assets/icons/themes/icon-school days-02.png',
                  backgroundImagePath: 'assets/images/story_covers/学生时代.png',
                  onTap: () {
                    print('点击了学生时代主题卡片');
                  },
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: HybridGlassThemeCard(
                  title: '#那些成长',
                  subtitle: '已有56个故事',
                  iconPath: 'assets/icons/themes/icon-those who grow up-02.png',
                  backgroundImagePath: 'assets/images/story_covers/那些成长.png',
                  onTap: () {
                    print('点击了那些成长主题卡片');
                  },
                ),
              ),
            ],
          ),

          SizedBox(height: 20.h),

          // 第三行：爸爸妈妈 + 旅行游记
          Row(
            children: [
              Expanded(
                child: HybridGlassThemeCard(
                  title: '#爸爸妈妈',
                  subtitle: '已有56个故事',
                  iconPath: 'assets/icons/themes/icon-mom and dad-02.png',
                  backgroundImagePath: 'assets/images/story_covers/爸爸妈妈.png',
                  onTap: () {
                    print('点击了爸爸妈妈主题卡片');
                  },
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: HybridGlassThemeCard(
                  title: '#旅行游记',
                  subtitle: '已有56个故事',
                  iconPath: 'assets/icons/themes/icon-travelogue-02.png',
                  backgroundImagePath: 'assets/images/story_covers/旅行游记.png',
                  onTap: () {
                    print('点击了旅行游记主题卡片');
                  },
                ),
              ),
            ],
          ),

          SizedBox(height: 20.h),

          // 第四行：关于梦想 + 对世界说
          Row(
            children: [
              Expanded(
                child: HybridGlassThemeCard(
                  title: '#关于梦想',
                  subtitle: '已有42个故事',
                  iconPath: 'assets/icons/themes/icon-about dream-02.png',
                  backgroundImagePath: 'assets/images/story_covers/关于梦想.png',
                  onTap: () {
                    print('点击了关于梦想主题卡片');
                  },
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: HybridGlassThemeCard(
                  title: '#对世界说',
                  subtitle: '已有38个故事',
                  iconPath: 'assets/icons/themes/icon-tell the world-02.png',
                  backgroundImagePath: 'assets/images/story_covers/对世界说.png',
                  onTap: () {
                    print('点击了对世界说主题卡片');
                  },
                ),
              ),
            ],
          ),

          SizedBox(height: 20.h),

          // 第五行：创业路上 + 闪光时刻
          Row(
            children: [
              Expanded(
                child: HybridGlassThemeCard(
                  title: '#创业路上',
                  subtitle: '已有29个故事',
                  iconPath: 'assets/icons/themes/icon-the road to entrepreneurship-02.png',
                  backgroundImagePath: 'assets/images/story_covers/创业路上.png',
                  onTap: () {
                    print('点击了创业路上主题卡片');
                  },
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: HybridGlassThemeCard(
                  title: '#闪光时刻',
                  subtitle: '已有35个故事',
                  iconPath: 'assets/icons/themes/icon-flash-02.png',
                  backgroundImagePath: 'assets/images/story_covers/闪光时刻.png',
                  onTap: () {
                    print('点击了闪光时刻主题卡片');
                  },
                ),
              ),
            ],
          ),

          // SizedBox(height: 30.h),

          // // 第二行：学生时代 + 那些成长 - 使用自适应主题卡片
          // Row(
          //   children: [
          //     Expanded(
          //       child: AdaptiveThemeCard(
          //         title: '#学生时代',
          //         subtitle: '已有56个故事',
          //         iconPath: 'assets/icons/themes/icon-school days.png',
          //         backgroundImagePath: 'assets/images/story_covers/学生时代.png',
          //         onTap: () {
          //           // TODO: 跳转到对应主题的故事列表页面
          //           print('点击了主题卡片: #学生时代');
          //         },
          //       ),
          //     ),
          //     SizedBox(width: 30.w),
          //     Expanded(
          //       child: AdaptiveThemeCard(
          //         title: '#那些成长',
          //         subtitle: '已有56个故事',
          //         iconPath: 'assets/icons/themes/icon-those who grow up.png',
          //         backgroundImagePath: 'assets/images/story_covers/那些成长.png',
          //         onTap: () {
          //           // TODO: 跳转到对应主题的故事列表页面
          //           print('点击了主题卡片: #那些成长');
          //         },
          //       ),
          //     ),
          //   ],
          // ),

          // SizedBox(height: 30.h),

          // // 第三行：爸爸妈妈 + 旅行游记 - 使用自适应主题卡片
          // Row(
          //   children: [
          //     Expanded(
          //       child: AdaptiveThemeCard(
          //         title: '#爸爸妈妈',
          //         subtitle: '已有56个故事',
          //         iconPath: 'assets/icons/themes/icon-mom and dad.png',
          //         backgroundImagePath: 'assets/images/story_covers/爸爸妈妈.png',
          //         onTap: () {
          //           // TODO: 跳转到对应主题的故事列表页面
          //           print('点击了主题卡片: #爸爸妈妈');
          //         },
          //       ),
          //     ),
          //     SizedBox(width: 30.w),
          //     Expanded(
          //       child: AdaptiveThemeCard(
          //         title: '#旅行游记',
          //         subtitle: '已有56个故事',
          //         iconPath: 'assets/icons/themes/icon-travelogue.png',
          //         backgroundImagePath: 'assets/images/story_covers/旅行游记.png',
          //         onTap: () {
          //           // TODO: 跳转到对应主题的故事列表页面
          //           print('点击了主题卡片: #旅行游记');
          //         },
          //       ),
          //     ),
          //   ],
          // ),
        ],
      ),
    );
  }

  /// 构建人物tab页面
  Widget _buildCharacterTab() {
    return Container(
      color: YgsColors.backgroundPrimary,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.people,
              size: 100.w,
              color: YgsColors.textSecondary,
            ),
            SizedBox(height: 20.h),
            Text(
              '人物页面',
              style: YgsTypography.headlineLarge.copyWith(
                fontSize: 40.sp,
                color: YgsColors.textPrimary,
              ),
            ),
            SizedBox(height: 10.h),
            Text(
              '左右滑动测试切换功能',
              style: YgsTypography.bodyMedium.copyWith(
                fontSize: 24.sp,
                color: YgsColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建引用tab页面
  Widget _buildQuoteTab() {
    return Container(
      color: YgsColors.backgroundPrimary,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.format_quote,
              size: 100.w,
              color: YgsColors.textSecondary,
            ),
            SizedBox(height: 20.h),
            Text(
              '引用页面',
              style: YgsTypography.headlineLarge.copyWith(
                fontSize: 40.sp,
                color: YgsColors.textPrimary,
              ),
            ),
            SizedBox(height: 10.h),
            Text(
              '左右滑动测试切换功能',
              style: YgsTypography.bodyMedium.copyWith(
                fontSize: 24.sp,
                color: YgsColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建时间轴内容 - 提取出来的原内容
  Widget _buildTimelineContent() {
    return ListView(
      padding: EdgeInsets.only(top: 60.h, bottom: 100.h), // 从160.h减少到100.h，缩小底部距离
      children: [
        // 第一个故事卡片 - 结婚五周年纪念
        _buildTimelineItem(
          year: '2024',
          date: '06.05',
          title: '结婚五周年纪念',
          category: '#爱情故事',
          categoryColor: const Color(0xFF507DAF),
          content: '时隔三年，老铁们再次相聚，那些青春岁月的美好记忆涌上心头',
          location: '上海浦东新区二十三号',
          likeCount: 888,
          isLiked: false,
          images: [
            'assets/images/test/story_image_1.png',
            'assets/images/test/story_image_2.png',
            'assets/images/test/story_image_3.png',
          ],
          interactionAvatars: [
            'assets/images/avatars/avatar-01.png',
            'assets/images/avatars/avatar-02.png',
            'assets/images/avatars/avatar-03.png',
          ],
          interactionNames: ['三一', '宁知心', '张乐'],
        ),

        // 第二个故事卡片 - 全家西藏之旅
        _buildTimelineItem(
          year: '2024',
          date: '05.12',
          title: '全家西藏之旅',
          category: '#关于梦想',
          categoryColor: const Color(0xFF507DAF),
          content: '带着家人踏上了心中向往已久的西藏，感受那份纯净与壮美',
          location: '上海浦东新区二十三号',
          likeCount: 888,
          isLiked: false,
          images: [
            'assets/images/test/story_image_2.png',
            'assets/images/test/story_image_3.png',
            'assets/images/test/story_image_4.png',
          ],
          interactionAvatars: [
            'assets/images/avatars/avatar-01.png',
            'assets/images/avatars/avatar-02.png',
            'assets/images/avatars/avatar-03.png',
          ],
          interactionNames: ['三一', '宁知心', '张乐'],
        ),

        // 第三个特殊卡片 - 你的故事从这里开始
        _buildSpecialTimelineItem(
          year: '1990',
          date: '02.01',
          title: '你的故事从这里开始',
          content: '35年来，你一定有很多有意义的故事要记录吧！从这一天开始，记录属于你的人生时间线。',
          location: '人生起点',
        ),
      ],
    );
  }

  /// 构建单个时间轴项目
  Widget _buildTimelineItem({
    required String year,
    required String date,
    required String title,
    required String category,
    required Color categoryColor,
    required String content,
    required String location,
    required int likeCount,
    required bool isLiked,
    required List<String> images,
    required List<String> interactionAvatars,
    required List<String> interactionNames,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: 40.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 时间标签 - 对齐底部导航首页图标
          Container(
            margin: EdgeInsets.only(left: 40.w), // 调整为新的对齐位置 (90.w - 50.w)
            child: Container(
              width: 100.w,
              height: 56.h,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(999.r),
                border: Border.all(color: const Color(0xFF151523), width: 1.w),
                boxShadow: [
                  BoxShadow(
                    color: Colors.white.withValues(alpha: 0.25),
                    offset: const Offset(0, 4),
                    blurRadius: 4,
                  ),
                  BoxShadow(
                    color: const Color(0xFF0B1129).withValues(alpha: 0.06),
                    offset: const Offset(0, 12),
                    blurRadius: 24,
                    spreadRadius: 8,
                  ),
                ],
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Flexible(
                    child: Text(
                      year,
                      style: YgsTypography.bodyMedium.copyWith(
                        fontSize: 20.sp,
                        fontWeight: FontWeight.w500,
                        color: const Color(0xFF2A2A39),
                      ),
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Flexible(
                    child: Text(
                      date,
                      style: YgsTypography.bodyMedium.copyWith(
                        fontSize: 20.sp,
                        fontWeight: FontWeight.w400,
                        color: const Color(0xFF2A2A39),
                      ),
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            ),
          ),

          SizedBox(width: 21.w),

          // 故事卡片
          Expanded(
            child: Container(
              margin: EdgeInsets.only(right: 45.w), // 从75.w减少到45.w，增大卡片宽度
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16.r),
              ),
              clipBehavior: Clip.none,
              child: Stack(
                clipBehavior: Clip.none,
                children: [
                  // 卡片内容
                  Padding(
                    padding: EdgeInsets.all(25.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 标题
                        Text(
                          title,
                          style: YgsTypography.headlineSmall.copyWith(
                            fontSize: 30.sp,
                            fontWeight: FontWeight.w500,
                            color: Colors.black,
                          ),
                        ),

                        SizedBox(height: 8.h),

                        // 分类标签
                        Text(
                          category,
                          style: YgsTypography.bodyMedium.copyWith(
                            fontSize: 20.sp,
                            fontWeight: FontWeight.w500,
                            color: categoryColor,
                          ),
                        ),

                        SizedBox(height: 16.h),

                        // 内容
                        Text(
                          content,
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                          style: YgsTypography.bodyMedium.copyWith(
                            fontSize: 24.sp,
                            fontWeight: FontWeight.w400,
                            color: const Color(0xFF868691),
                            height: 1.4,
                          ),
                        ),

                        SizedBox(height: 20.h),

                        // 图片展示
                        SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Row(
                            children: [
                              for (int i = 0; i < images.length && i < 3; i++)
                                Padding(
                                  padding:
                                      EdgeInsets.only(right: i < 2 ? 16.w : 0),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(8.r),
                                    child: Image.asset(
                                      images[i],
                                      width: 120.w,
                                      height: 120.w,
                                      fit: BoxFit.cover,
                                      errorBuilder:
                                          (context, error, stackTrace) {
                                        return Container(
                                          width: 120.w,
                                          height: 120.w,
                                          decoration: BoxDecoration(
                                            color: const Color(0xFFF0F0F0),
                                            borderRadius:
                                                BorderRadius.circular(8.r),
                                          ),
                                          child: Icon(
                                            Icons.image,
                                            size: 30.w,
                                            color: YgsColors.textSecondary,
                                          ),
                                        );
                                      },
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),

                        SizedBox(height: 20.h),

                        // 互动头像
                        Row(
                          children: [
                            for (int i = 0;
                                i < interactionAvatars.length && i < 3;
                                i++)
                              Padding(
                                padding:
                                    EdgeInsets.only(right: i < 2 ? 80.w : 0),
                                child: Column(
                                  children: [
                                    ClipOval(
                                      child: Image.asset(
                                        interactionAvatars[i],
                                        width: 48.w,
                                        height: 48.w,
                                        fit: BoxFit.cover,
                                        errorBuilder:
                                            (context, error, stackTrace) {
                                          return Container(
                                            width: 48.w,
                                            height: 48.w,
                                            decoration: const BoxDecoration(
                                              color: Color(0xFFF0F0F0),
                                              shape: BoxShape.circle,
                                            ),
                                            child: Icon(
                                              Icons.person,
                                              size: 24.w,
                                              color: YgsColors.textSecondary,
                                            ),
                                          );
                                        },
                                      ),
                                    ),
                                    SizedBox(height: 8.h),
                                    Text(
                                      interactionNames[i],
                                      style: YgsTypography.bodySmall.copyWith(
                                        fontSize: 20.sp,
                                        color: const Color(0xFF868691),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                          ],
                        ),

                        SizedBox(height: 16.h),

                        // 底部信息
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            // 地址
                            Expanded(
                              child: Row(
                                children: [
                                  Image.asset(
                                    'assets/icons/basic/icon-address.png',
                                    width: 38.w,
                                    height: 38.w,
                                    errorBuilder: (context, error, stackTrace) {
                                      return Icon(
                                        Icons.location_on_outlined,
                                        size: 38.w,
                                        color: const Color(0xFFB6B6BD),
                                      );
                                    },
                                  ),
                                  SizedBox(width: 8.w),
                                  Flexible(
                                    child: Text(
                                      location,
                                      style: YgsTypography.bodySmall.copyWith(
                                        fontSize: 18.sp,
                                        color: const Color(0xFFB6B6BD),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),

                            // 点赞
                            Row(
                              children: [
                                Image.asset(
                                  isLiked
                                      ? 'assets/icons/basic/icon-love-active.png'
                                      : 'assets/icons/basic/icon-like-default.png',
                                  width: 40.w,
                                  height: 40.w,
                                  errorBuilder: (context, error, stackTrace) {
                                    return Icon(
                                      isLiked
                                          ? Icons.favorite
                                          : Icons.favorite_border,
                                      size: 40.w,
                                      color: isLiked
                                          ? Colors.red
                                          : const Color(0xFFB6B6BD),
                                    );
                                  },
                                ),
                                SizedBox(width: 8.w),
                                Text(
                                  likeCount.toString(),
                                  style: YgsTypography.bodySmall.copyWith(
                                    fontSize: 18.sp,
                                    color: const Color(0xFFB6B6BD),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // 右上角装饰图标
                  Positioned(
                    top: 20.h,
                    right: 20.w,
                    child: Transform.rotate(
                      angle: 15 * 3.14159 / 180,
                      child: Image.asset(
                        _getThemeIcon(category),
                        width: 40.w,
                        height: 40.w,
                        fit: BoxFit.contain,
                        color: categoryColor,
                        errorBuilder: (context, error, stackTrace) {
                          return Icon(
                            Icons.auto_stories,
                            color: categoryColor,
                            size: 40.w,
                          );
                        },
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建特殊时间轴项目
  Widget _buildSpecialTimelineItem({
    required String year,
    required String date,
    required String title,
    required String content,
    required String location,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: 40.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 时间标签
          Container(
            margin: EdgeInsets.only(left: 40.w), // 调整为新的对齐位置 (90.w - 50.w)
            child: Container(
              width: 100.w,
              height: 56.h,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(999.r),
                border: Border.all(color: const Color(0xFF151523), width: 1.w),
                boxShadow: [
                  BoxShadow(
                    color: Colors.white.withValues(alpha: 0.25),
                    offset: const Offset(0, 4),
                    blurRadius: 4,
                  ),
                  BoxShadow(
                    color: const Color(0xFF0B1129).withValues(alpha: 0.06),
                    offset: const Offset(0, 12),
                    blurRadius: 24,
                    spreadRadius: 8,
                  ),
                ],
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Flexible(
                    child: Text(
                      year,
                      style: YgsTypography.bodyMedium.copyWith(
                        fontSize: 20.sp,
                        fontWeight: FontWeight.w500,
                        color: const Color(0xFF2A2A39),
                      ),
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Flexible(
                    child: Text(
                      date,
                      style: YgsTypography.bodyMedium.copyWith(
                        fontSize: 20.sp,
                        fontWeight: FontWeight.w400,
                        color: const Color(0xFF2A2A39),
                      ),
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            ),
          ),

          SizedBox(width: 21.w),

          // 特殊故事卡片
          Expanded(
            child: Container(
              margin: EdgeInsets.only(right: 45.w), // 从75.w减少到45.w，增大卡片宽度
              height: 280.h,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Color(0xFFFFE6E6),
                    Color(0xFFFFFFFF),
                  ],
                ),
                borderRadius: BorderRadius.circular(16.r),
              ),
              child: Stack(
                children: [
                  Padding(
                    padding: EdgeInsets.all(25.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Image.asset(
                              'assets/icons/basic/icon-timeline.png',
                              width: 56.w,
                              height: 56.w,
                              color: YgsColors.textPrimary,
                              errorBuilder: (context, error, stackTrace) {
                                return Icon(
                                  Icons.timeline,
                                  color: YgsColors.textPrimary,
                                  size: 56.w,
                                );
                              },
                            ),
                            SizedBox(width: 8.w),
                            Expanded(
                              child: Text(
                                title,
                                style: YgsTypography.headlineSmall.copyWith(
                                  fontSize: 30.sp,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.black,
                                ),
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 32.h),
                        Text(
                          content,
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                          style: YgsTypography.bodyMedium.copyWith(
                            fontSize: 24.sp,
                            fontWeight: FontWeight.w400,
                            color: const Color(0xFF868691),
                            height: 1.4,
                          ),
                        ),
                        Spacer(),
                        Row(
                          children: [
                            Image.asset(
                              'assets/icons/basic/icon-address.png',
                              width: 38.w,
                              height: 38.w,
                              errorBuilder: (context, error, stackTrace) {
                                return Icon(
                                  Icons.location_on_outlined,
                                  size: 38.w,
                                  color: const Color(0xFFB6B6BD),
                                );
                              },
                            ),
                            SizedBox(width: 8.w),
                            Text(
                              location,
                              style: YgsTypography.bodySmall.copyWith(
                                fontSize: 18.sp,
                                color: const Color(0xFFB6B6BD),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 根据分类获取主题图标
  String _getThemeIcon(String category) {
    switch (category) {
      case '#爱情故事':
        return 'assets/icons/themes/icon-romance-02.png';
      case '#关于梦想':
        return 'assets/icons/themes/icon-about dream-02.png';
      case '#孩子们':
        return 'assets/icons/themes/icon-children-02.png';
      case '#创业路上':
        return 'assets/icons/themes/icon-the road to entrepreneurship-02.png';
      case '#家庭时光':
        return 'assets/icons/themes/icon-mom and dad-02.png';
      case '#工作生活':
        return 'assets/icons/themes/icon-flash-02.png';
      case '#学生时代':
        return 'assets/icons/themes/icon-school days-02.png';
      case '#成长足迹':
        return 'assets/icons/themes/icon-those who grow up-02.png';
      case '#游记':
        return 'assets/icons/themes/icon-travelogue-02.png';
      case '#告诉世界':
        return 'assets/icons/themes/icon-tell the world-02.png';
      default:
        return 'assets/icons/themes/icon-about dream-02.png';
    }
  }
}
