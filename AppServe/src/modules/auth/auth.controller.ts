import {
  Body,
  Controller,
  Post,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
  Get,
} from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from "@nestjs/swagger";
import { AuthService } from "./auth.service";
import { OptimizedSessionManagerService } from "./services/session-manager-optimized.service";
import { LoginDto, PhoneLoginDto } from "./dto/login.dto";
import { JwtAuthGuard } from "./guards/jwt-auth.guard";
import { AuthRequest } from "../../common/types/request.types";
import {
  RegisterWithEmailDto,
  SetEmailPasswordDto,
  RefreshTokenDto,
} from "./dto/auth.dto";

@ApiTags("认证")
@Controller("auth")
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly sessionManager: OptimizedSessionManagerService,
  ) {}

  @Post("send-sms")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: "发送短信验证码",
    description: `
      发送手机短信验证码用于登录/注册
      
      限制条件：
      - 同一手机号每分钟最多发送1次
      - 同一IP每小时最多发送10次
      - 验证码有效期5分钟
      - 仅支持中国大陆手机号
    `,
  })
  @ApiResponse({ status: 200, description: "验证码发送成功" })
  async sendVerificationCode(@Body() { phone }: { phone: string }) {
    return this.authService.sendPhoneVerificationCode(phone);
  }

  @Post("login/phone")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: "手机号+验证码登录/注册",
    description: `
      使用手机号和短信验证码进行登录，如果用户不存在则自动注册
      
      业务流程：
      1. 用户输入手机号和验证码
      2. 系统验证验证码是否正确且未过期
      3. 如果手机号已注册，直接登录
      4. 如果手机号未注册，自动创建用户并登录
      5. 返回用户信息和JWT令牌对
      
      注意事项：
      - 验证码验证后立即失效
      - 自动注册用户的用户名为手机号
      - 可通过isFirstLogin字段判断是否为新用户
    `,
  })
  @ApiResponse({
    status: 200,
    description: "登录成功",
    schema: {
      type: "object",
      properties: {
        success: { type: "boolean", example: true },
        message: { type: "string", example: "登录成功" },
        data: {
          type: "object",
          properties: {
            user: {
              type: "object",
              properties: {
                id: { type: "string", example: "uuid-string" },
                phone: { type: "string", example: "138****8000" },
                email: { type: "string", example: null, nullable: true },
                username: { type: "string", example: "13800138000" },
                avatarUrl: { type: "string", example: null, nullable: true },
                isFirstLogin: { type: "boolean", example: true },
              },
            },
            tokens: {
              type: "object",
              properties: {
                accessToken: {
                  type: "string",
                  example: "eyJhbGciOiJIUzI1NiIs...",
                },
                refreshToken: {
                  type: "string",
                  example: "eyJhbGciOiJIUzI1NiIs...",
                },
              },
            },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: "验证码错误或已过期",
    schema: {
      type: "object",
      properties: {
        success: { type: "boolean", example: false },
        message: { type: "string", example: "验证码错误或已过期" },
        error: { type: "string", example: "INVALID_VERIFICATION_CODE" },
      },
    },
  })
  async loginWithPhone(
    @Body() loginDto: PhoneLoginDto,
    @Request() req: AuthRequest,
  ) {
    const result = await this.authService.loginWithPhone(
      loginDto.phone,
      loginDto.code,
      req,
    );

    // 企业级标准响应格式 - 与其他接口保持一致
    return {
      success: true,
      message: result.isFirstLogin ? "注册并登录成功" : "登录成功",
      data: {
        user: {
          id: result.user.id,
          phone: result.user.phone,
          email: result.user.email,
          username: result.user.username,
          avatarUrl: result.user.avatarUrl,
          isFirstLogin: result.isFirstLogin,
        },
        tokens: {
          accessToken: result.accessToken,
          refreshToken: result.refreshToken,
        },
      },
    };
  }

  @Post("register/email")
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: "邮箱+密码注册",
    description: `
      使用邮箱和密码创建新用户账户
      
      注册规则：
      - 邮箱必须唯一，不能重复注册
      - 密码长度至少8位，包含数字和字母
      - 用户名必须唯一，2-20个字符
      - 注册成功后自动登录，返回令牌对
      
      安全特性：
      - 密码使用bcrypt加密存储
      - 邮箱格式验证
      - 防止邮箱枚举攻击
    `,
  })
  @ApiResponse({
    status: 201,
    description: "注册成功",
    schema: {
      type: "object",
      properties: {
        success: { type: "boolean", example: true },
        message: { type: "string", example: "注册成功" },
        data: {
          type: "object",
          properties: {
            user: {
              type: "object",
              properties: {
                id: { type: "string", example: "uuid-string" },
                phone: { type: "string", example: null, nullable: true },
                email: { type: "string", example: "<EMAIL>" },
                username: { type: "string", example: "用户名" },
                avatarUrl: { type: "string", example: null, nullable: true },
              },
            },
            tokens: {
              type: "object",
              properties: {
                accessToken: {
                  type: "string",
                  example: "eyJhbGciOiJIUzI1NiIs...",
                },
                refreshToken: {
                  type: "string",
                  example: "eyJhbGciOiJIUzI1NiIs...",
                },
              },
            },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 409,
    description: "邮箱或用户名已被注册",
    schema: {
      type: "object",
      properties: {
        success: { type: "boolean", example: false },
        message: { type: "string", example: "邮箱已被注册" },
        error: { type: "string", example: "EMAIL_ALREADY_EXISTS" },
      },
    },
  })
  async registerWithEmail(@Body() registerDto: RegisterWithEmailDto) {
    const result = await this.authService.registerWithEmail(
      registerDto.email,
      registerDto.password,
      registerDto.username,
    );

    return {
      success: true,
      message: "注册成功",
      data: {
        user: {
          id: result.user.id,
          phone: result.user.phone,
          email: result.user.email,
          username: result.user.username,
          avatarUrl: result.user.avatarUrl,
        },
        tokens: {
          accessToken: result.accessToken,
          refreshToken: result.refreshToken,
        },
      },
    };
  }

  @Post("login/email")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: "邮箱+密码登录",
    description: `
      使用邮箱和密码登录系统
      
      登录流程：
      1. 用户输入邮箱和密码
      2. 系统验证邮箱格式和密码强度
      3. 查找用户并验证密码
      4. 生成并返回令牌对
      
      安全特性：
      - 密码错误3次后账户临时锁定15分钟
      - 登录失败记录和监控
      - 设备特征识别
    `,
  })
  @ApiResponse({
    status: 200,
    description: "登录成功",
    schema: {
      type: "object",
      properties: {
        success: { type: "boolean", example: true },
        message: { type: "string", example: "登录成功" },
        data: {
          type: "object",
          properties: {
            user: {
              type: "object",
              properties: {
                id: { type: "string", example: "uuid-string" },
                phone: { type: "string", example: null, nullable: true },
                email: { type: "string", example: "<EMAIL>" },
                username: { type: "string", example: "用户名" },
                avatarUrl: { type: "string", example: null, nullable: true },
              },
            },
            tokens: {
              type: "object",
              properties: {
                accessToken: {
                  type: "string",
                  example: "eyJhbGciOiJIUzI1NiIs...",
                },
                refreshToken: {
                  type: "string",
                  example: "eyJhbGciOiJIUzI1NiIs...",
                },
              },
            },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: "邮箱或密码错误",
    schema: {
      type: "object",
      properties: {
        success: { type: "boolean", example: false },
        message: { type: "string", example: "邮箱或密码错误" },
        error: { type: "string", example: "INVALID_CREDENTIALS" },
      },
    },
  })
  async loginWithEmail(@Body() loginDto: LoginDto) {
    const result = await this.authService.loginWithEmail(
      loginDto.email,
      loginDto.password,
    );

    return {
      success: true,
      message: "登录成功",
      data: {
        user: {
          id: result.user.id,
          phone: result.user.phone,
          email: result.user.email,
          username: result.user.username,
          avatarUrl: result.user.avatarUrl,
        },
        tokens: {
          accessToken: result.accessToken,
          refreshToken: result.refreshToken,
        },
      },
    };
  }

  @Post("set-email-password")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "为已有用户设置邮箱和密码" })
  @ApiResponse({ status: 200, description: "设置成功" })
  @ApiResponse({ status: 409, description: "邮箱已被其他用户使用" })
  async setEmailAndPassword(
    @Request() req: AuthRequest,
    @Body() setEmailPasswordDto: SetEmailPasswordDto,
  ) {
    const updatedUser = await this.authService.setEmailAndPassword(
      req.user.id,
      setEmailPasswordDto.email,
      setEmailPasswordDto.password,
    );

    return {
      success: true,
      message: "邮箱和密码设置成功",
      data: {
        user: {
          id: updatedUser.id,
          phone: updatedUser.phone,
          email: updatedUser.email,
          username: updatedUser.username,
          avatarUrl: updatedUser.avatarUrl,
        },
      },
    };
  }

  @Post("refresh")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: "刷新访问令牌",
    description: `
      使用刷新令牌获取新的访问令牌和刷新令牌
      
      令牌机制：
      - 访问令牌(accessToken)：有效期45分钟，用于API访问
      - 刷新令牌(refreshToken)：有效期14天，用于获取新的访问令牌
      - 每次刷新都会返回新的令牌对，旧令牌立即失效
      
      安全特性：
      - 刷新令牌限制每用户最多3个并发会话
      - 检测令牌重放攻击
      - 设备绑定验证
    `,
  })
  @ApiResponse({
    status: 200,
    description: "令牌刷新成功",
    schema: {
      type: "object",
      properties: {
        success: { type: "boolean", example: true },
        message: { type: "string", example: "Token刷新成功" },
        data: {
          type: "object",
          properties: {
            tokens: {
              type: "object",
              properties: {
                accessToken: {
                  type: "string",
                  example: "eyJhbGciOiJIUzI1NiIs...",
                },
                refreshToken: {
                  type: "string",
                  example: "eyJhbGciOiJIUzI1NiIs...",
                },
              },
            },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: "无效的刷新令牌",
    schema: {
      type: "object",
      properties: {
        success: { type: "boolean", example: false },
        message: { type: "string", example: "无效的刷新令牌" },
        error: { type: "string", example: "INVALID_REFRESH_TOKEN" },
      },
    },
  })
  async refreshToken(@Body() refreshTokenDto: RefreshTokenDto) {
    const result = await this.authService.refreshToken(
      refreshTokenDto.refreshToken,
    );
    return {
      success: true,
      data: { tokens: result },
      message: "令牌刷新成功",
    };
  }

  @Get("profile")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "获取当前用户信息" })
  @ApiResponse({ status: 200, description: "获取成功" })
  @ApiResponse({ status: 401, description: "未授权" })
  async getProfile(@Request() req: AuthRequest) {
    return {
      success: true,
      message: "获取用户信息成功",
      data: {
        user: {
          id: req.user.id,
          phone: req.user.phone,
          email: req.user.email,
          username: req.user.username,
          avatarUrl: req.user.avatarUrl,
          birthDate: req.user.birthDate,
          isActive: req.user.isActive,
          createdAt: req.user.createdAt,
          updatedAt: req.user.updatedAt,
        },
      },
    };
  }

  @Post("logout")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "用户登出（注销所有会话）" })
  @ApiResponse({ status: 200, description: "登出成功" })
  async logout(@Request() req: AuthRequest) {
    const result = await this.authService.logout(req.user.id);
    return {
      success: true,
      message: result.message,
    };
  }

  @Post("sessions/cleanup")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: "手动清理过期会话",
    description: `
      手动触发过期会话清理，用于系统维护
      
      功能：
      - 清理所有过期但仍标记为active的RefreshToken
      - 返回清理统计信息
      - 管理员可定期调用此接口进行数据库维护
    `,
  })
  @ApiResponse({
    status: 200,
    description: "清理成功",
    schema: {
      type: "object",
      properties: {
        success: { type: "boolean", example: true },
        message: { type: "string", example: "成功清理 15 个过期会话" },
        data: {
          type: "object",
          properties: {
            cleanedCount: { type: "number", example: 15 },
            duration: { type: "number", example: 45 },
            timestamp: { type: "string", example: "2025-07-24T10:30:00.000Z" },
          },
        },
      },
    },
  })
  async cleanupExpiredSessions() {
    const result = await this.sessionManager.manualCleanupExpiredSessions();

    return {
      success: result.success,
      message: result.message,
      data: {
        cleanedCount: result.cleanedCount,
        duration: result.duration,
        timestamp: new Date().toISOString(),
      },
    };
  }

  @Get("sessions/stats")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: "获取用户会话统计",
    description: "获取当前用户的会话统计信息，包括活跃会话数、设备信息等",
  })
  @ApiResponse({
    status: 200,
    description: "获取成功",
    schema: {
      type: "object",
      properties: {
        success: { type: "boolean", example: true },
        data: {
          type: "object",
          properties: {
            activeCount: { type: "number", example: 2 },
            totalCount: { type: "number", example: 5 },
            expiredCount: { type: "number", example: 1 },
            devices: {
              type: "array",
              items: { type: "string" },
              example: ["device1", "device2"],
            },
            cleanupRecommended: { type: "boolean", example: false },
          },
        },
      },
    },
  })
  async getUserSessionStats(@Request() req: AuthRequest) {
    const stats = await this.sessionManager.getUserSessionStats(req.user.id);

    return {
      success: true,
      data: stats,
    };
  }

  @Get("sessions/system-stats")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: "获取系统会话统计（管理功能）",
    description: "获取系统级别的会话统计信息，用于系统监控和维护",
  })
  @ApiResponse({ status: 200, description: "获取成功" })
  async getSystemSessionStats() {
    const stats = await this.sessionManager.getSystemSessionStats();

    return {
      success: true,
      data: stats,
    };
  }
}
