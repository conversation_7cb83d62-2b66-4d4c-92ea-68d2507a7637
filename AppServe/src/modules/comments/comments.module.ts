import { Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { CommentsController } from "./comments.controller";
import { CommentsService } from "./comments.service";
import { Comment } from "./entities/comment.entity";
import { CommentLike } from "./entities/comment-like.entity";
import { User } from "../users/entities/user.entity";
import { Story } from "../stories/entities/story.entity";
import { AuthModule } from "../auth/auth.module";

/**
 * 评论模块
 * 提供评论系统的完整功能
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([Comment, CommentLike, User, Story]),
    AuthModule,
  ],
  controllers: [CommentsController],
  providers: [CommentsService],
  exports: [CommentsService],
})
export class CommentsModule {}
