import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ot<PERSON>mpty,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "class-validator";
import { ApiProperty } from "@nestjs/swagger";

export class SubmitLightRequestDto {
  @ApiProperty({
    description: "申请理由（必填）",
    example: "这就是我，我想点亮这个人物",
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(200)
  reason: string;

  @ApiProperty({
    description: "是否发送手机号用于身份确认",
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  includePhone?: boolean = false; // 可选，默认不发送
}
