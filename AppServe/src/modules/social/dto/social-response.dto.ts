import type { FollowStatus } from "../entities/user-follow.entity";
import type {
  GroupType,
  GroupVisibility,
} from "../entities/friend-group.entity";
import type {
  MemberRole,
  MemberStatus,
} from "../entities/friend-group-member.entity";

/**
 * 关注关系响应DTO
 */
export class FollowResponseDto {
  id: string;
  followerId: string;
  followingId: string;
  followerName: string;
  followerAvatar?: string;
  followingName: string;
  followingAvatar?: string;
  status: FollowStatus;
  isSpecial: boolean;
  settings?: {
    notifyOnPost?: boolean;
    notifyOnComment?: boolean;
    showInTimeline?: boolean;
  };
  metadata?: Record<string, unknown>;
  createdAt: Date;
}

/**
 * 用户简要信息DTO（用于关注列表）
 */
export class UserBriefDto {
  id: string;
  username: string;
  name: string;
  avatar?: string;
  bio?: string;
  followersCount: number;
  followingCount: number;
  storiesCount: number;
  isFollowing: boolean;
  isFollowingMe: boolean;
  isMutualFollow: boolean;
  followedAt?: Date;
}

/**
 * 好友分组响应DTO
 */
export class FriendGroupResponseDto {
  id: string;
  name: string;
  description?: string;
  ownerId: string;
  ownerName: string;
  groupType: GroupType;
  visibility: GroupVisibility;
  colorTag?: string;
  memberCount: number;
  sortOrder: number;
  isActive: boolean;
  permissions?: {
    canViewProfile?: boolean;
    canViewStories?: boolean;
    canComment?: boolean;
    canShare?: boolean;
    canMessage?: boolean;
  };
  metadata?: Record<string, unknown>;
  createdAt: Date;
  updatedAt: Date;

  // 当前用户在该分组中的状态（如果是成员）
  myMemberStatus?: MemberStatus;
  myRole?: MemberRole;
}

/**
 * 分组成员响应DTO
 */
export class GroupMemberResponseDto {
  id: string;
  groupId: string;
  groupName: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  role: MemberRole;
  status: MemberStatus;
  addedBy: string;
  addedByName: string;
  note?: string;
  permissionOverrides?: {
    canViewProfile?: boolean;
    canViewStories?: boolean;
    canComment?: boolean;
    canShare?: boolean;
    canMessage?: boolean;
  };
  metadata?: Record<string, unknown>;
  addedAt: Date;
}

/**
 * 社交统计响应DTO
 */
export class SocialStatsResponseDto {
  userId: string;
  followingCount: number;
  followersCount: number;
  mutualFollowsCount: number;
  specialFollowsCount: number;
  friendGroupsCount: number;
  groupMembershipsCount: number;

  // 今日新增统计
  newFollowersToday: number;
  newFollowingToday: number;

  // 热门分组
  topGroups: Array<{
    groupId: string;
    groupName: string;
    memberCount: number;
    groupType: GroupType;
  }>;

  // 互动最多的关注者
  topInteractors: Array<{
    userId: string;
    userName: string;
    userAvatar?: string;
    interactionScore: number;
    lastInteractionAt: Date;
  }>;
}
