/**
 * 企业级集成测试配置
 * 
 * 设计原则:
 * 1. 真实数据库环境，验证模块间数据交互
 * 2. 完整业务流程测试，覆盖端到端场景
 * 3. 企业级质量标准，80%+覆盖率目标
 * 4. 严格ESLint规范，0错误0警告
 */

const path = require('path');

module.exports = {
  displayName: 'Integration Tests',
  rootDir: path.resolve(__dirname, '../..'),
  testEnvironment: 'node',
  preset: 'ts-jest',
  transform: {
    '^.+\\.ts$': 'ts-jest',
  },
  testMatch: [
    '<rootDir>/test/integration/**/*.integration.spec.ts'
  ],
  setupFilesAfterEnv: [
    '<rootDir>/test/integration/setup/global-setup.ts',
    '<rootDir>/test/integration/setup/database-setup.ts'
  ],
  globalSetup: '<rootDir>/test/integration/setup/integration-global-setup.ts',
  globalTeardown: '<rootDir>/test/integration/setup/integration-global-teardown.ts',
  maxWorkers: 1, // 串行执行防止数据库竞态
  testTimeout: 60000, // 60秒超时，适应真实数据库操作
  
  // 企业级覆盖率要求 - 集成测试目标80%+
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 85,
      lines: 80,
      statements: 80,
    },
  },
  
  // 集成测试专用配置
  collectCoverageFrom: [
    'src/modules/**/*.service.ts',
    'src/modules/**/*.controller.ts',
    'src/modules/**/*.module.ts',
    'src/common/**/*.service.ts',
    '!src/**/*.spec.ts',
    '!src/**/*.interface.ts',
    '!src/**/*.dto.ts',
  ],
  
  // 环境变量覆盖
  globals: {
    'ts-jest': {
      tsconfig: '<rootDir>/tsconfig.json',
    },
  },
  
  // 集成测试专用模块映射
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@test/(.*)$': '<rootDir>/test/$1',
    '^@integration/(.*)$': '<rootDir>/test/integration/$1',
  },
  
  // TypeScript配置
  globals: {
    'ts-jest': {
      tsconfig: '<rootDir>/tsconfig.test.json',
      diagnostics: {
        ignoreCodes: [151001]
      }
    }
  },
  
  // 详细报告配置
  verbose: true,
  collectCoverage: true,
  coverageDirectory: '<rootDir>/coverage/integration',
  coverageReporters: ['text', 'lcov', 'html'],
  
  // 清理配置
  clearMocks: true,
  restoreMocks: true,
  
  // 强制退出配置
  forceExit: true,
  detectOpenHandles: true,
};