import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import type { Repository } from "typeorm";
import { NotFoundException, ForbiddenException } from "@nestjs/common";
import { StoriesService } from "./stories.service";
import { Story, StoryStatus } from "./entities/story.entity";
import { Character } from "../characters/entities/character.entity";
import { User } from "../users/entities/user.entity";
import { Theme } from "./entities/theme.entity";
import type { CreateStoryDto, UpdateStoryDto } from "./dto";

describe("StoriesService", () => {
  let service: StoriesService;
  let storyRepository: Repository<Story>;
  let characterRepository: Repository<Character>;
  let userRepository: Repository<User>;
  let themeRepository: Repository<Theme>;

  // Mock数据
  const mockUser = {
    id: "user-id",
    userNumber: "123456",
    nickname: "测试用户",
    username: "testuser",
    phone: "13800138000",
    email: "<EMAIL>",
    avatarUrl: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    isActive: true,
    status: "active",
  };

  const mockTheme = {
    id: "theme-id",
    name: "测试主题",
    description: "这是一个测试主题",
    createdAt: new Date(),
    updatedAt: new Date(),
    stories: [],
  };

  const mockStory = {
    id: "story-id",
    title: "测试故事",
    content: "这是一个测试故事的内容",
    description: "故事描述",
    userId: "user-id",
    themeId: "theme-id",
    status: StoryStatus.PUBLISHED,
    viewCount: 0,
    likeCount: 0,
    commentCount: 0,
    shareCount: 0,
    isPublic: true,
    tags: ["测试", "故事"],
    createdAt: new Date(),
    updatedAt: new Date(),
    publishedAt: new Date(),
    // 关联关系
    author: mockUser,
    theme: mockTheme,
    characters: [],
    lightingRequests: [],
    storyShares: [],
    comments: [],
    likes: [],
  };

  const mockCharacter = {
    id: "character-id",
    name: "测试角色",
    description: "这是一个测试角色",
    storyId: "story-id",
    userId: "user-id",
    avatarUrl: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    story: mockStory,
    user: mockUser,
  };

  beforeEach(async () => {
    // Mock Repository - 企业级命名规范
    const mockStoryRepositoryMethods = {
      create: jest.fn(),
      save: jest.fn(),
      findOne: jest.fn(),
      find: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      remove: jest.fn(),
      increment: jest.fn(),
      createQueryBuilder: jest.fn(),
      count: jest.fn(),
    };

    const mockCharacterRepositoryMethods = {
      find: jest.fn(),
      count: jest.fn(),
      createQueryBuilder: jest.fn(),
    };

    const mockUserRepositoryMethods = {
      findOne: jest.fn(),
    };

    const mockThemeRepositoryMethods = {
      findOne: jest.fn(),
    };

    // Mock QueryBuilder
    const mockQueryBuilder = {
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      orWhere: jest.fn().mockReturnThis(),
      leftJoinAndSelect: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      addSelect: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      take: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      offset: jest.fn().mockReturnThis(),
      getMany: jest.fn(),
      getOne: jest.fn(),
      getManyAndCount: jest.fn(),
      getCount: jest.fn(),
    };

    mockStoryRepositoryMethods.createQueryBuilder.mockReturnValue(
      mockQueryBuilder,
    );
    mockCharacterRepositoryMethods.createQueryBuilder.mockReturnValue(
      mockQueryBuilder,
    );

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        StoriesService,
        {
          provide: getRepositoryToken(Story),
          useValue: mockStoryRepositoryMethods,
        },
        {
          provide: getRepositoryToken(Character),
          useValue: mockCharacterRepositoryMethods,
        },
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepositoryMethods,
        },
        {
          provide: getRepositoryToken(Theme),
          useValue: mockThemeRepositoryMethods,
        },
      ],
    }).compile();

    service = module.get<StoriesService>(StoriesService);
    storyRepository = module.get<Repository<Story>>(getRepositoryToken(Story));
    characterRepository = module.get<Repository<Character>>(
      getRepositoryToken(Character),
    );
    userRepository = module.get<Repository<User>>(getRepositoryToken(User));
    themeRepository = module.get<Repository<Theme>>(getRepositoryToken(Theme));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  describe("createStory", () => {
    const createStoryDto: CreateStoryDto = {
      title: "新故事",
      content: "这是新故事的内容",
      themeId: "theme-id",
      permissionLevel: "public",
      allowComments: true,
      allowLikes: true,
      allowSharing: true,
    };

    it("should create a story successfully", async () => {
      (userRepository.findOne as jest.Mock).mockResolvedValue(mockUser);
      (themeRepository.findOne as jest.Mock).mockResolvedValue(mockTheme);
      (storyRepository.create as jest.Mock).mockReturnValue(mockStory);
      (storyRepository.save as jest.Mock).mockResolvedValue(mockStory);
      // Mock findById调用，因为createStory最后会调用findById
      (storyRepository.findOne as jest.Mock).mockResolvedValue(mockStory);

      const result = await service.createStory("user-id", createStoryDto);

      expect(result).toEqual(mockStory);
      expect(userRepository.findOne).toHaveBeenCalledWith({
        where: { id: "user-id" },
      });
      expect(themeRepository.findOne).toHaveBeenCalledWith({
        where: { id: "theme-id", isActive: true },
      });
      expect(storyRepository.create).toHaveBeenCalledWith({
        ...createStoryDto,
        userId: "user-id",
        status: StoryStatus.DRAFT,
        aiSafetyScore: 0.95,
      });
      expect(storyRepository.save).toHaveBeenCalled();
    });

    it("should throw NotFoundException when author not found", async () => {
      (userRepository.findOne as jest.Mock).mockResolvedValue(null);

      await expect(
        service.createStory("nonexistent-user", createStoryDto),
      ).rejects.toThrow(NotFoundException);
      await expect(
        service.createStory("nonexistent-user", createStoryDto),
      ).rejects.toThrow("作者不存在");
    });

    it("should throw NotFoundException when theme not found", async () => {
      (userRepository.findOne as jest.Mock).mockResolvedValue(mockUser);
      (themeRepository.findOne as jest.Mock).mockResolvedValue(null);

      await expect(
        service.createStory("user-id", createStoryDto),
      ).rejects.toThrow(NotFoundException);
      await expect(
        service.createStory("user-id", createStoryDto),
      ).rejects.toThrow("主题不存在或已停用");
    });

    it("should create story without theme", async () => {
      const createStoryWithoutTheme: CreateStoryDto = {
        ...createStoryDto,
        themeId: undefined,
      };

      (userRepository.findOne as jest.Mock).mockResolvedValue(mockUser);
      (storyRepository.create as jest.Mock).mockReturnValue(mockStory);
      (storyRepository.save as jest.Mock).mockResolvedValue(mockStory);
      // Mock findById调用
      (storyRepository.findOne as jest.Mock).mockResolvedValue(mockStory);

      const result = await service.createStory(
        "user-id",
        createStoryWithoutTheme,
      );

      expect(result).toEqual(mockStory);
      expect(themeRepository.findOne).not.toHaveBeenCalled();
    });
  });

  describe("updateStory", () => {
    const updateStoryDto: UpdateStoryDto = {
      title: "更新的故事",
      content: "更新的内容",
      permissionLevel: "private",
      allowComments: false,
    };

    it("should update story successfully", async () => {
      const updatedStory = {
        ...mockStory,
        ...updateStoryDto,
      };

      (storyRepository.findOne as jest.Mock)
        .mockResolvedValueOnce(mockStory) // 第一次调用用于更新验证
        .mockResolvedValueOnce(updatedStory); // 第二次调用用于findById返回
      (storyRepository.update as jest.Mock).mockResolvedValue({ affected: 1 });

      const result = await service.updateStory(
        "story-id",
        updateStoryDto,
        "user-id",
      );

      expect(result.title).toBe("更新的故事");
      expect(storyRepository.update).toHaveBeenCalled();
    });

    it("should throw NotFoundException when story not found", async () => {
      (storyRepository.findOne as jest.Mock).mockResolvedValue(null);

      await expect(
        service.updateStory("nonexistent-story", updateStoryDto, "user-id"),
      ).rejects.toThrow(NotFoundException);
      await expect(
        service.updateStory("nonexistent-story", updateStoryDto, "user-id"),
      ).rejects.toThrow("故事不存在");
    });

    it("should throw ForbiddenException when user is not the author", async () => {
      const otherUserStory = { ...mockStory, userId: "other-user-id" };
      (storyRepository.findOne as jest.Mock).mockResolvedValue(otherUserStory);

      await expect(
        service.updateStory("story-id", updateStoryDto, "user-id"),
      ).rejects.toThrow(ForbiddenException);
      await expect(
        service.updateStory("story-id", updateStoryDto, "user-id"),
      ).rejects.toThrow("无权编辑此故事");
    });

    it("should update theme when themeId is provided", async () => {
      const updateWithTheme = {
        ...updateStoryDto,
        themeId: "new-theme-id",
      };

      const updatedStory = {
        ...mockStory,
        ...updateWithTheme,
      };

      (storyRepository.findOne as jest.Mock)
        .mockResolvedValueOnce(mockStory) // 第一次调用用于更新验证
        .mockResolvedValueOnce(updatedStory); // 第二次调用用于findById返回
      (themeRepository.findOne as jest.Mock).mockResolvedValue(mockTheme);
      (storyRepository.update as jest.Mock).mockResolvedValue({ affected: 1 });

      const result = await service.updateStory(
        "story-id",
        updateWithTheme,
        "user-id",
      );

      expect(themeRepository.findOne).toHaveBeenCalledWith({
        where: { id: "new-theme-id", isActive: true },
      });
      expect(result.themeId).toBe("new-theme-id");
    });
  });

  describe("deleteStory", () => {
    it("should delete story successfully", async () => {
      (storyRepository.findOne as jest.Mock).mockResolvedValue(mockStory);
      (storyRepository.remove as jest.Mock).mockResolvedValue(undefined);

      await service.deleteStory("story-id", "user-id");

      expect(storyRepository.remove).toHaveBeenCalledWith(mockStory);
    });

    it("should throw NotFoundException when story not found", async () => {
      (storyRepository.findOne as jest.Mock).mockResolvedValue(null);

      await expect(
        service.deleteStory("nonexistent-story", "user-id"),
      ).rejects.toThrow(NotFoundException);
    });

    it("should throw ForbiddenException when user is not the author", async () => {
      const otherUserStory = { ...mockStory, userId: "other-user-id" };
      (storyRepository.findOne as jest.Mock).mockResolvedValue(otherUserStory);

      await expect(service.deleteStory("story-id", "user-id")).rejects.toThrow(
        ForbiddenException,
      );
    });
  });

  describe("findById", () => {
    it("should return story when found", async () => {
      (storyRepository.findOne as jest.Mock).mockResolvedValue(mockStory);

      const result = await service.findById("story-id");

      expect(result).toEqual(mockStory);
      expect(storyRepository.findOne).toHaveBeenCalledWith({
        where: { id: "story-id" },
        relations: ["user", "theme", "characters"],
      });
    });

    it("should throw NotFoundException when story not found", async () => {
      (storyRepository.findOne as jest.Mock).mockResolvedValue(null);

      await expect(service.findById("nonexistent-story")).rejects.toThrow(
        NotFoundException,
      );
      await expect(service.findById("nonexistent-story")).rejects.toThrow(
        "故事不存在",
      );
    });
  });

  describe("validateStoryAccess", () => {
    it("should return true for public story", async () => {
      const publicStory = { ...mockStory, permissionLevel: "public" };
      (storyRepository.findOne as jest.Mock).mockResolvedValue(publicStory);

      const result = await service.validateStoryAccess(
        "story-id",
        "other-user-id",
      );

      expect(result).toBe(true);
    });

    it("should return true for story author", async () => {
      const privateStory = { ...mockStory, permissionLevel: "private" };
      (storyRepository.findOne as jest.Mock).mockResolvedValue(privateStory);

      const result = await service.validateStoryAccess("story-id", "user-id");

      expect(result).toBe(true);
    });

    it("should return false for private story accessed by non-author", async () => {
      const privateStory = { ...mockStory, permissionLevel: "private" };
      (storyRepository.findOne as jest.Mock).mockResolvedValue(privateStory);

      const result = await service.validateStoryAccess(
        "story-id",
        "other-user-id",
      );

      expect(result).toBe(false);
    });

    it("should return false when story not found", async () => {
      (storyRepository.findOne as jest.Mock).mockResolvedValue(null);

      await expect(
        service.validateStoryAccess("nonexistent-story", "user-id"),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe("getUserStories", () => {
    it("should return user stories with pagination", async () => {
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[mockStory], 1]),
      };

      (storyRepository.createQueryBuilder as jest.Mock).mockReturnValue(
        mockQueryBuilder,
      );

      const result = await service.getUserStories("user-id", {
        page: 1,
        limit: 10,
      });

      expect(result).toEqual({
        data: expect.any(Array),
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      });
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        "story.userId = :userId",
        { userId: "user-id" },
      );
    });
  });

  describe("getPublicStories", () => {
    it("should return public stories with pagination", async () => {
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[mockStory], 1]),
      };

      (storyRepository.createQueryBuilder as jest.Mock).mockReturnValue(
        mockQueryBuilder,
      );

      const result = await service.getPublicStories({ page: 1, limit: 10 });

      expect(result).toEqual({
        data: expect.any(Array),
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      });
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        "story.permissionLevel = :permissionLevel",
        { permissionLevel: "public" },
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "story.status = :status",
        { status: StoryStatus.PUBLISHED },
      );
    });
  });

  describe("validateStoryContent", () => {
    it("should return valid result for safe content", async () => {
      const safeContent = "这是一个安全的故事内容";

      const result = await service.validateStoryContent(safeContent);

      expect(result).toEqual({
        isValid: true,
        aiSafetyScore: expect.any(Number),
        violations: [],
        suggestions: [],
      });
    });

    it("should detect sensitive content", async () => {
      const sensitiveContent = "这是包含敏感词1的内容";

      const result = await service.validateStoryContent(sensitiveContent);

      expect(result.isValid).toBe(false);
      expect(result.violations.length).toBeGreaterThan(0);
      expect(result.aiSafetyScore).toBeLessThan(0.95);
    });

    it("should handle empty content", async () => {
      const result = await service.validateStoryContent("");

      expect(result).toEqual({
        isValid: true,
        aiSafetyScore: 0.95,
        violations: [],
        suggestions: [],
      });
    });

    it("should handle very long content", async () => {
      const longContent = "很长的内容".repeat(10000);

      const result = await service.validateStoryContent(longContent);

      expect(result.isValid).toBe(false);
      expect(result.violations).toContain("内容过长");
      expect(result.aiSafetyScore).toBeLessThan(1.0);
    });
  });

  describe("getAccessibleStories", () => {
    it("should return accessible stories for user", async () => {
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[mockStory], 1]),
      };

      (storyRepository.createQueryBuilder as jest.Mock).mockReturnValue(
        mockQueryBuilder,
      );

      const result = await service.getAccessibleStories(
        { page: 1, limit: 10 },
        "user-id",
      );

      expect(result).toEqual({
        data: expect.any(Array),
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      });
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        "story.status = :status",
        { status: StoryStatus.PUBLISHED },
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "(story.permissionLevel = :public OR story.userId = :currentUserId)",
        { public: "public", currentUserId: "user-id" },
      );
    });

    it("should handle search query in accessible stories", async () => {
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[mockStory], 1]),
      };

      (storyRepository.createQueryBuilder as jest.Mock).mockReturnValue(
        mockQueryBuilder,
      );

      await service.getAccessibleStories(
        { page: 1, limit: 10, search: "测试" },
        "user-id",
      );

      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "(story.title ILIKE :search OR story.content ILIKE :search)",
        { search: "%测试%" },
      );
    });
  });

  describe("incrementViewCount", () => {
    it("should increment story view count", async () => {
      (storyRepository.increment as jest.Mock).mockResolvedValue(undefined);

      await service.incrementViewCount("story-id");

      expect(storyRepository.increment).toHaveBeenCalledWith(
        { id: "story-id" },
        "viewCount",
        1,
      );
    });
  });

  describe("incrementLikeCount", () => {
    it("should increment story like count", async () => {
      (storyRepository.increment as jest.Mock).mockResolvedValue(undefined);

      await service.incrementLikeCount("story-id");

      expect(storyRepository.increment).toHaveBeenCalledWith(
        { id: "story-id" },
        "likeCount",
        1,
      );
    });
  });

  describe("createStory - additional edge cases", () => {
    const createStoryDto: CreateStoryDto = {
      title: "新故事",
      content: "这是新故事的内容",
      themeId: "theme-id",
      permissionLevel: "public" as const,
      allowComments: true,
      allowLikes: true,
      allowSharing: true,
      characterIds: ["character-id"],
      storyDate: "2023-01-01",
    };

    it("should handle character association in createStory", async () => {
      (userRepository.findOne as jest.Mock).mockResolvedValue(mockUser);
      (themeRepository.findOne as jest.Mock).mockResolvedValue(mockTheme);
      (storyRepository.create as jest.Mock).mockReturnValue(mockStory);
      (storyRepository.save as jest.Mock).mockResolvedValue(mockStory);
      (storyRepository.findOne as jest.Mock).mockResolvedValue(mockStory);

      // Mock the associateCharactersWithStory method
      const mockAssociateCharactersWithStory = jest
        .fn()
        .mockResolvedValue(undefined);
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (service as any).associateCharactersWithStory =
        mockAssociateCharactersWithStory;

      const result = await service.createStory("user-id", createStoryDto);

      expect(result).toEqual(mockStory);
      expect(mockAssociateCharactersWithStory).toHaveBeenCalledWith(
        "story-id",
        ["character-id"],
        "user-id",
      );
    });

    it("should handle content validation failure in createStory", async () => {
      const createStoryWithBadContent: CreateStoryDto = {
        ...createStoryDto,
        content: "这是包含敏感词1的内容",
      };

      (userRepository.findOne as jest.Mock).mockResolvedValue(mockUser);
      (themeRepository.findOne as jest.Mock).mockResolvedValue(mockTheme);

      await expect(
        service.createStory("user-id", createStoryWithBadContent),
      ).rejects.toThrow("内容安全检测失败");
    });

    it("should handle storyDate conversion in createStory", async () => {
      (userRepository.findOne as jest.Mock).mockResolvedValue(mockUser);
      (themeRepository.findOne as jest.Mock).mockResolvedValue(mockTheme);
      (storyRepository.create as jest.Mock).mockReturnValue(mockStory);
      (storyRepository.save as jest.Mock).mockResolvedValue(mockStory);
      (storyRepository.findOne as jest.Mock).mockResolvedValue(mockStory);

      // Mock the character-related methods
      const mockAssociateCharactersWithStory = jest
        .fn()
        .mockResolvedValue(undefined);
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (service as any).associateCharactersWithStory =
        mockAssociateCharactersWithStory;

      await service.createStory("user-id", createStoryDto);

      expect(storyRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          storyDate: expect.any(Date),
        }),
      );
    });
  });

  describe("updateStory - additional edge cases", () => {
    const updateStoryDto: UpdateStoryDto = {
      title: "更新的故事",
      content: "更新的内容",
      permissionLevel: "private" as const,
      allowComments: false,
      characterIds: ["character-id-1", "character-id-2"],
      storyDate: "2023-01-01",
    };

    it("should handle character association update", async () => {
      (storyRepository.findOne as jest.Mock)
        .mockResolvedValueOnce(mockStory)
        .mockResolvedValueOnce({ ...mockStory, ...updateStoryDto });
      (storyRepository.update as jest.Mock).mockResolvedValue({ affected: 1 });

      // Mock the service method directly
      const mockUpdateStoryCharacters = jest.fn().mockResolvedValue(undefined);
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (service as any).updateStoryCharacters = mockUpdateStoryCharacters;

      const result = await service.updateStory(
        "story-id",
        updateStoryDto,
        "user-id",
      );

      expect(result.title).toBe("更新的故事");
      expect(mockUpdateStoryCharacters).toHaveBeenCalledWith(
        "story-id",
        ["character-id-1", "character-id-2"],
        "user-id",
      );
    });

    it("should handle content validation failure in updateStory", async () => {
      const updateStoryWithBadContent: UpdateStoryDto = {
        ...updateStoryDto,
        content: "这是包含敏感词1的内容",
      };

      (storyRepository.findOne as jest.Mock).mockResolvedValue(mockStory);

      await expect(
        service.updateStory("story-id", updateStoryWithBadContent, "user-id"),
      ).rejects.toThrow("内容安全检测失败");
    });

    it("should handle storyDate conversion in updateStory", async () => {
      (storyRepository.findOne as jest.Mock)
        .mockResolvedValueOnce(mockStory)
        .mockResolvedValueOnce({ ...mockStory, ...updateStoryDto });
      (storyRepository.update as jest.Mock).mockResolvedValue({ affected: 1 });

      // Mock the character-related methods
      const mockUpdateStoryCharacters = jest.fn().mockResolvedValue(undefined);
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (service as any).updateStoryCharacters = mockUpdateStoryCharacters;

      await service.updateStory("story-id", updateStoryDto, "user-id");

      expect(storyRepository.update).toHaveBeenCalledWith(
        "story-id",
        expect.objectContaining({
          storyDate: expect.any(Date),
        }),
      );
    });
  });

  describe("validateStoryAccess - additional cases", () => {
    it("should handle friends permission level", async () => {
      const friendsStory = { ...mockStory, permissionLevel: "friends" };
      (storyRepository.findOne as jest.Mock).mockResolvedValue(friendsStory);

      // Mock isFriend method
      const mockIsFriend = jest.fn().mockResolvedValue(true);
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (service as any).isFriend = mockIsFriend;

      const result = await service.validateStoryAccess(
        "story-id",
        "friend-user-id",
      );

      expect(result).toBe(true);
      expect(mockIsFriend).toHaveBeenCalledWith("user-id", "friend-user-id");
    });

    it("should handle characters_only permission level", async () => {
      const charactersOnlyStory = {
        ...mockStory,
        permissionLevel: "characters_only",
      };
      (storyRepository.findOne as jest.Mock).mockResolvedValue(
        charactersOnlyStory,
      );

      // Mock hasLightedCharacterInStory method
      const mockHasLightedCharacterInStory = jest.fn().mockResolvedValue(true);
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (service as any).hasLightedCharacterInStory =
        mockHasLightedCharacterInStory;

      const result = await service.validateStoryAccess(
        "story-id",
        "lighted-user-id",
      );

      expect(result).toBe(true);
      expect(mockHasLightedCharacterInStory).toHaveBeenCalledWith(
        "story-id",
        "lighted-user-id",
      );
    });

    it("should handle unknown permission level", async () => {
      const unknownPermissionStory = {
        ...mockStory,
        permissionLevel: "unknown",
      };
      (storyRepository.findOne as jest.Mock).mockResolvedValue(
        unknownPermissionStory,
      );

      const result = await service.validateStoryAccess(
        "story-id",
        "other-user-id",
      );

      expect(result).toBe(false);
    });
  });

  describe("getUserStories - additional filters", () => {
    it("should handle theme filter", async () => {
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[mockStory], 1]),
      };

      (storyRepository.createQueryBuilder as jest.Mock).mockReturnValue(
        mockQueryBuilder,
      );

      await service.getUserStories("user-id", {
        page: 1,
        limit: 10,
        themeId: "theme-id",
      });

      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "story.themeId = :themeId",
        { themeId: "theme-id" },
      );
    });

    it("should handle different sort options", async () => {
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[mockStory], 1]),
      };

      (storyRepository.createQueryBuilder as jest.Mock).mockReturnValue(
        mockQueryBuilder,
      );

      await service.getUserStories("user-id", {
        page: 1,
        limit: 10,
        sortBy: "most_liked",
      });

      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith(
        "story.likeCount",
        "DESC",
      );
    });

    it("should handle most_viewed sort option", async () => {
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[mockStory], 1]),
      };

      (storyRepository.createQueryBuilder as jest.Mock).mockReturnValue(
        mockQueryBuilder,
      );

      await service.getUserStories("user-id", {
        page: 1,
        limit: 10,
        sortBy: "most_viewed",
      });

      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith(
        "story.viewCount",
        "DESC",
      );
    });

    it("should handle oldest sort option", async () => {
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[mockStory], 1]),
      };

      (storyRepository.createQueryBuilder as jest.Mock).mockReturnValue(
        mockQueryBuilder,
      );

      await service.getUserStories("user-id", {
        page: 1,
        limit: 10,
        sortBy: "oldest",
      });

      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith(
        "story.createdAt",
        "ASC",
      );
    });

    it("should handle status filter", async () => {
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[mockStory], 1]),
      };

      (storyRepository.createQueryBuilder as jest.Mock).mockReturnValue(
        mockQueryBuilder,
      );

      await service.getUserStories("user-id", {
        page: 1,
        limit: 10,
        status: StoryStatus.PUBLISHED,
      });

      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "story.status = :status",
        { status: StoryStatus.PUBLISHED },
      );
    });
  });

  describe("Private Methods and Edge Cases", () => {
    it("should handle associateCharactersWithStory method", async () => {
      const mockCharacters = [
        { id: "char1", creatorId: "user-id" },
        { id: "char2", creatorId: "user-id" },
      ];

      (characterRepository.find as jest.Mock).mockResolvedValue(mockCharacters);

      const mockQueryBuilder = {
        relation: jest.fn().mockReturnThis(),
        of: jest.fn().mockReturnThis(),
        add: jest.fn().mockResolvedValue(undefined),
      };

      (storyRepository.createQueryBuilder as jest.Mock).mockReturnValue(
        mockQueryBuilder,
      );

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      await (service as any).associateCharactersWithStory(
        "story-id",
        ["char1", "char2"],
        "user-id",
      );

      expect(characterRepository.find).toHaveBeenCalledWith({
        where: {
          id: expect.anything(), // In(['char1', 'char2'])
          creatorId: "user-id",
        },
      });
      expect(mockQueryBuilder.add).toHaveBeenCalledWith(["char1", "char2"]);
    });

    it("should throw error when character validation fails", async () => {
      // 只返回一个字符，但期望两个
      const mockCharacters = [{ id: "char1", creatorId: "user-id" }];

      (characterRepository.find as jest.Mock).mockResolvedValue(mockCharacters);

      await expect(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (service as any).associateCharactersWithStory(
          "story-id",
          ["char1", "char2"],
          "user-id",
        ),
      ).rejects.toThrow("部分人物不存在或无权访问");
    });

    it("should handle updateStoryCharacters method", async () => {
      const mockQueryBuilder = {
        relation: jest.fn().mockReturnThis(),
        of: jest.fn().mockReturnThis(),
        remove: jest.fn().mockResolvedValue(undefined),
        add: jest.fn().mockResolvedValue(undefined),
      };

      (storyRepository.createQueryBuilder as jest.Mock).mockReturnValue(
        mockQueryBuilder,
      );

      // Mock getStoryCharacterIds
      jest
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        .spyOn(service as any, "getStoryCharacterIds")
        .mockResolvedValue(["old-char1"]);

      // Mock associateCharactersWithStory
      jest
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        .spyOn(service as any, "associateCharactersWithStory")
        .mockResolvedValue(undefined);

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      await (service as any).updateStoryCharacters(
        "story-id",
        ["new-char1", "new-char2"],
        "user-id",
      );

      expect(mockQueryBuilder.remove).toHaveBeenCalled();
      expect(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (service as any).associateCharactersWithStory,
      ).toHaveBeenCalledWith("story-id", ["new-char1", "new-char2"], "user-id");
    });

    it("should handle getStoryCharacterIds method", async () => {
      const mockRawResults = [
        { character_id: "char1" },
        { character_id: "char2" },
      ];

      const mockQueryBuilder = {
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue(mockRawResults),
      };

      (storyRepository.createQueryBuilder as jest.Mock).mockReturnValue(
        mockQueryBuilder,
      );

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = await (service as any).getStoryCharacterIds("story-id");

      expect(result).toEqual(["char1", "char2"]);
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        "story.id = :storyId",
        { storyId: "story-id" },
      );
    });

    it("should handle isFriend method (placeholder implementation)", async () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = await (service as any).isFriend("user1", "user2");
      expect(result).toBe(false);
    });

    it("should handle hasLightedCharacterInStory method (placeholder implementation)", async () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = await (service as any).hasLightedCharacterInStory(
        "story-id",
        "user-id",
      );
      expect(result).toBe(false);
    });
  });
});
