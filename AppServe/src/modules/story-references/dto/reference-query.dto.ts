import { IsOptional, IsEnum, IsString, IsA<PERSON>y, IsInt } from "class-validator";
import { Transform, Type } from "class-transformer";
import {
  ReferenceStatus,
  ReferenceVisibility,
} from "../entities/story-reference.entity";
import {
  CollectionType,
  CollectionStatus,
  CollectionVisibility,
} from "../entities/reference-collection.entity";
import { PaginationQueryDto } from "../../../common/dto/pagination-query.dto";

/**
 * 故事引用查询DTO
 */
export class ReferenceQueryDto extends PaginationQueryDto {
  @IsOptional()
  @IsString({
    message: "集合ID必须是字符串",
  })
  collectionId?: string;

  @IsOptional()
  @IsEnum(ReferenceStatus, {
    message: "引用状态必须是有效的选项",
  })
  status?: ReferenceStatus = ReferenceStatus.ACTIVE;

  @IsOptional()
  @IsEnum(ReferenceVisibility, {
    message: "可见性设置必须是有效的选项",
  })
  visibility?: ReferenceVisibility;

  @IsOptional()
  @IsArray({
    message: "标签筛选必须是数组",
  })
  @IsString({
    each: true,
    message: "每个标签必须是字符串",
  })
  tags?: string[];

  @IsOptional()
  @IsInt({
    message: "最小重要性评分必须是整数",
  })
  @Type(() => Number)
  minImportance?: number;

  @IsOptional()
  @IsString({
    message: "搜索关键词必须是字符串",
  })
  search?: string;

  @IsOptional()
  @Transform(({ value }: { value: string }) => value?.toLowerCase())
  @IsString({
    message: "排序字段必须是字符串",
  })
  sortBy?: string = "createdAt";

  @IsOptional()
  @Transform(({ value }: { value: string }) => value?.toUpperCase())
  @Type(() => String)
  sortOrder?: "ASC" | "DESC" = "DESC";
}

/**
 * 引用集合查询DTO
 */
export class CollectionQueryDto extends PaginationQueryDto {
  @IsOptional()
  @IsEnum(CollectionType, {
    message: "集合类型必须是有效的选项",
  })
  collectionType?: CollectionType;

  @IsOptional()
  @IsEnum(CollectionStatus, {
    message: "集合状态必须是有效的选项",
  })
  status?: CollectionStatus = CollectionStatus.ACTIVE;

  @IsOptional()
  @IsEnum(CollectionVisibility, {
    message: "可见性设置必须是有效的选项",
  })
  visibility?: CollectionVisibility;

  @IsOptional()
  @IsArray({
    message: "标签筛选必须是数组",
  })
  @IsString({
    each: true,
    message: "每个标签必须是字符串",
  })
  tags?: string[];

  @IsOptional()
  @Type(() => Boolean)
  isFeatured?: boolean;

  @IsOptional()
  @IsString({
    message: "搜索关键词必须是字符串",
  })
  search?: string;

  @IsOptional()
  @Transform(({ value }: { value: string }) => value?.toLowerCase())
  @IsString({
    message: "排序字段必须是字符串",
  })
  sortBy?: string = "lastContentUpdate";

  @IsOptional()
  @Transform(({ value }: { value: string }) => value?.toUpperCase())
  @Type(() => String)
  sortOrder?: "ASC" | "DESC" = "DESC";
}
