<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="jest tests" tests="19" failures="4" errors="0" time="11.475">
  <testsuite name="src/modules/lighting/lighting.service.spec.ts" errors="0" failures="4" skipped="0" timestamp="2025-07-19T13:07:29" time="11.316" tests="19">
    <testcase classname="LightingService - Enterprise Unit Tests › submitLightRequest - 企业级业务逻辑测试" name="should submit light request successfully" time="0.01">
      <failure>ConflictException: 您已提交过该人物的点亮申请，请等待处理
    at LightingService.submitLightRequest (/Users/<USER>/Desktop/YGS/ygs-v1.0.0/AppServe/src/modules/lighting/lighting.service.ts:108:13)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at Object.&lt;anonymous&gt; (/Users/<USER>/Desktop/YGS/ygs-v1.0.0/AppServe/src/modules/lighting/lighting.service.spec.ts:240:22)</failure>
    </testcase>
    <testcase classname="LightingService - Enterprise Unit Tests › submitLightRequest - 企业级业务逻辑测试" name="should throw NotFoundException when story not found" time="0.003">
    </testcase>
    <testcase classname="LightingService - Enterprise Unit Tests › submitLightRequest - 企业级业务逻辑测试" name="should throw NotFoundException when character not found" time="0.002">
    </testcase>
    <testcase classname="LightingService - Enterprise Unit Tests › submitLightRequest - 企业级业务逻辑测试" name="should throw BadRequestException when character does not belong to story" time="0.002">
    </testcase>
    <testcase classname="LightingService - Enterprise Unit Tests › submitLightRequest - 企业级业务逻辑测试" name="should throw BadRequestException when requester is story author" time="0.002">
    </testcase>
    <testcase classname="LightingService - Enterprise Unit Tests › submitLightRequest - 企业级业务逻辑测试" name="should throw BadRequestException when requester is character creator" time="0.001">
    </testcase>
    <testcase classname="LightingService - Enterprise Unit Tests › submitLightRequest - 企业级业务逻辑测试" name="should throw ConflictException when duplicate request exists" time="0.022">
      <failure>Error: expect(received).rejects.toThrow(expected)

Expected constructor: ConflictException
Received constructor: ForbiddenException

Received message: &quot;您当前无法提交点亮申请，请检查账户状态&quot;

      221 |     // 检查用户是否可以申请点亮
      222 |     if (!user.canApplyLighting()) {
    &gt; 223 |       throw new ForbiddenException(&quot;您当前无法提交点亮申请，请检查账户状态&quot;);
          |             ^
      224 |     }
      225 |   }
      226 |

      at LightingService.validateUserAnomalyStatus (src/modules/lighting/lighting.service.ts:223:13)
      at LightingService.validateLightingRequest (src/modules/lighting/lighting.service.ts:165:16)
      at LightingService.submitLightRequest (src/modules/lighting/lighting.service.ts:93:5)
      at Object.&lt;anonymous&gt; (src/modules/lighting/lighting.service.spec.ts:369:7)
    at Object.toThrow (/Users/<USER>/Desktop/YGS/ygs-v1.0.0/AppServe/node_modules/expect/build/index.js:218:22)
    at Object.&lt;anonymous&gt; (/Users/<USER>/Desktop/YGS/ygs-v1.0.0/AppServe/src/modules/lighting/lighting.service.spec.ts:371:17)
    at Promise.then.completed (/Users/<USER>/Desktop/YGS/ygs-v1.0.0/AppServe/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Desktop/YGS/ygs-v1.0.0/AppServe/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Desktop/YGS/ygs-v1.0.0/AppServe/node_modules/jest-circus/build/run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (/Users/<USER>/Desktop/YGS/ygs-v1.0.0/AppServe/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Desktop/YGS/ygs-v1.0.0/AppServe/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Desktop/YGS/ygs-v1.0.0/AppServe/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Desktop/YGS/ygs-v1.0.0/AppServe/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Desktop/YGS/ygs-v1.0.0/AppServe/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Desktop/YGS/ygs-v1.0.0/AppServe/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Desktop/YGS/ygs-v1.0.0/AppServe/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Desktop/YGS/ygs-v1.0.0/AppServe/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Desktop/YGS/ygs-v1.0.0/AppServe/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LightingService - Enterprise Unit Tests › confirmLightRequest - 企业级分布式锁测试" name="should confirm light request successfully with distributed lock" time="0.002">
    </testcase>
    <testcase classname="LightingService - Enterprise Unit Tests › confirmLightRequest - 企业级分布式锁测试" name="should throw ConflictException when cannot acquire lock" time="0.002">
    </testcase>
    <testcase classname="LightingService - Enterprise Unit Tests › confirmLightRequest - 企业级分布式锁测试" name="should throw NotFoundException when request not found" time="0.001">
    </testcase>
    <testcase classname="LightingService - Enterprise Unit Tests › confirmLightRequest - 企业级分布式锁测试" name="should throw ForbiddenException when confirmer is not story author" time="0.004">
    </testcase>
    <testcase classname="LightingService - Enterprise Unit Tests › checkUserLightingLimit - 企业级业务规则测试" name="should check user lighting limits correctly" time="0.002">
    </testcase>
    <testcase classname="LightingService - Enterprise Unit Tests › validatePhoneVerification - 企业级验证测试" name="should validate phone verification successfully" time="0.001">
    </testcase>
    <testcase classname="LightingService - Enterprise Unit Tests › validatePhoneVerification - 企业级验证测试" name="should throw NotFoundException when user not found" time="0.002">
    </testcase>
    <testcase classname="LightingService - Enterprise Unit Tests › validatePhoneVerification - 企业级验证测试" name="should throw BadRequestException when phone does not match" time="0.001">
    </testcase>
    <testcase classname="LightingService - Enterprise Unit Tests › Performance - 企业级性能标准" name="should complete submitLightRequest within performance threshold" time="0.002">
      <failure>ForbiddenException: 您当前无法提交点亮申请，请检查账户状态
    at LightingService.validateUserAnomalyStatus (/Users/<USER>/Desktop/YGS/ygs-v1.0.0/AppServe/src/modules/lighting/lighting.service.ts:223:13)
    at LightingService.validateLightingRequest (/Users/<USER>/Desktop/YGS/ygs-v1.0.0/AppServe/src/modules/lighting/lighting.service.ts:165:16)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at LightingService.submitLightRequest (/Users/<USER>/Desktop/YGS/ygs-v1.0.0/AppServe/src/modules/lighting/lighting.service.ts:93:5)
    at Object.&lt;anonymous&gt; (/Users/<USER>/Desktop/YGS/ygs-v1.0.0/AppServe/src/modules/lighting/lighting.service.spec.ts:614:7)</failure>
    </testcase>
    <testcase classname="LightingService - Enterprise Unit Tests › Performance - 企业级性能标准" name="should handle concurrent requests efficiently" time="0.002">
      <failure>ForbiddenException: 您当前无法提交点亮申请，请检查账户状态
    at LightingService.validateUserAnomalyStatus (/Users/<USER>/Desktop/YGS/ygs-v1.0.0/AppServe/src/modules/lighting/lighting.service.ts:223:13)
    at LightingService.validateLightingRequest (/Users/<USER>/Desktop/YGS/ygs-v1.0.0/AppServe/src/modules/lighting/lighting.service.ts:165:16)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at LightingService.submitLightRequest (/Users/<USER>/Desktop/YGS/ygs-v1.0.0/AppServe/src/modules/lighting/lighting.service.ts:93:5)
    at async Promise.all (index 0)
    at Object.&lt;anonymous&gt; (/Users/<USER>/Desktop/YGS/ygs-v1.0.0/AppServe/src/modules/lighting/lighting.service.spec.ts:673:7)</failure>
    </testcase>
    <testcase classname="LightingService - Enterprise Unit Tests › Edge Cases - 企业级边界测试" name="should handle null and undefined inputs gracefully" time="0.001">
    </testcase>
    <testcase classname="LightingService - Enterprise Unit Tests › Edge Cases - 企业级边界测试" name="should handle empty string inputs gracefully" time="0.002">
    </testcase>
  </testsuite>
</testsuites>