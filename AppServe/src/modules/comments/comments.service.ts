import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import type { SelectQueryBuilder } from "typeorm";
import { Repository } from "typeorm";
import {
  Comment,
  CommentTargetType,
  CommentStatus,
} from "./entities/comment.entity";
import { CommentLike } from "./entities/comment-like.entity";
import type {
  CreateCommentDto,
  UpdateCommentDto,
  CommentQueryDto,
} from "./dto";
import { CommentSortBy } from "./dto";
import type {
  CommentResponseDto,
  CommentDetailResponseDto,
  CommentLikeResponseDto,
  CommentStatsResponseDto,
} from "./dto";
import { User } from "../users/entities/user.entity";
import { Story } from "../stories/entities/story.entity";
import type { PaginatedResponseDto } from "../../common/dto/paginated-response.dto";

/**
 * 评论服务
 * 提供评论的CRUD操作、点赞功能和统计功能
 */
@Injectable()
export class CommentsService {
  constructor(
    @InjectRepository(Comment)
    private readonly commentRepository: Repository<Comment>,
    @InjectRepository(CommentLike)
    private readonly commentLikeRepository: Repository<CommentLike>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Story)
    private readonly storyRepository: Repository<Story>,
  ) {}

  /**
   * 创建评论
   */
  async createComment(
    createCommentDto: CreateCommentDto,
    authorId: string,
  ): Promise<CommentResponseDto> {
    const {
      targetType,
      targetId,
      content,
      parentCommentId,
      rootCommentId,
      metadata,
    } = createCommentDto;

    // 验证目标是否存在
    await this.validateTarget(targetType, targetId);

    // 验证父评论（如果是回复评论）
    let parentComment: Comment | undefined;
    let depth = 0;
    let finalRootCommentId = rootCommentId;

    if (parentCommentId) {
      parentComment =
        (await this.commentRepository.findOne({
          where: { id: parentCommentId, status: CommentStatus.ACTIVE },
        })) || undefined;

      if (!parentComment) {
        throw new NotFoundException("父评论不存在或已被删除");
      }

      depth = parentComment.depth + 1;

      // 如果没有指定根评论ID，使用父评论的根评论ID或父评论本身的ID
      if (!finalRootCommentId) {
        finalRootCommentId = parentComment.rootCommentId || parentComment.id;
      }

      // 限制回复深度（防止无限嵌套）
      if (depth > 5) {
        throw new BadRequestException("回复层级不能超过5级");
      }
    }

    // 创建评论
    const comment = this.commentRepository.create({
      targetType,
      targetId,
      content,
      authorId,
      parentCommentId,
      rootCommentId: finalRootCommentId,
      depth,
      metadata,
    });

    const savedComment = await this.commentRepository.save(comment);

    // 如果是回复，更新父评论的回复数
    if (parentComment) {
      await this.commentRepository.increment(
        { id: parentCommentId },
        "replyCount",
        1,
      );
    }

    // 返回完整的评论信息
    return this.buildCommentResponse(savedComment, authorId);
  }

  /**
   * 获取评论列表
   */
  async getComments(
    query: CommentQueryDto,
    currentUserId?: string,
  ): Promise<PaginatedResponseDto<CommentResponseDto>> {
    const { page = 1, limit = 20, sortBy = CommentSortBy.CREATED_DESC } = query;
    const skip = (page - 1) * limit;

    const queryBuilder = this.buildCommentQuery(query);

    // 应用排序
    this.applySorting(queryBuilder, sortBy);

    // 获取总数
    const total = await queryBuilder.getCount();

    // 获取数据
    const comments = await queryBuilder.skip(skip).take(limit).getMany();

    // 构建响应数据
    const items = await Promise.all(
      comments.map((comment) =>
        this.buildCommentResponse(comment, currentUserId),
      ),
    );

    const totalPages = Math.ceil(total / limit);
    return {
      data: items,
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  /**
   * 获取评论详情
   */
  async getCommentDetail(
    commentId: string,
    currentUserId?: string,
  ): Promise<CommentDetailResponseDto> {
    const comment = await this.commentRepository.findOne({
      where: { id: commentId },
      relations: ["author", "parentComment", "parentComment.author"],
    });

    if (!comment) {
      throw new NotFoundException("评论不存在");
    }

    // 获取回复列表
    const replies = await this.commentRepository.find({
      where: { parentCommentId: commentId, status: CommentStatus.ACTIVE },
      relations: ["author"],
      order: { createdAt: "ASC" },
      take: 20, // 限制回复数量
    });

    const baseResponse = await this.buildCommentResponse(
      comment,
      currentUserId,
    );
    const repliesResponse = await Promise.all(
      replies.map((reply) => this.buildCommentResponse(reply, currentUserId)),
    );

    return {
      ...baseResponse,
      replies: repliesResponse,
      parentComment: comment.parentComment
        ? {
            id: comment.parentComment.id,
            authorName: comment.parentComment.author?.nickname || "未知用户",
            content: comment.parentComment.content,
          }
        : undefined,
    };
  }

  /**
   * 更新评论
   */
  async updateComment(
    commentId: string,
    updateCommentDto: UpdateCommentDto,
    authorId: string,
  ): Promise<CommentResponseDto> {
    const comment = await this.commentRepository.findOne({
      where: { id: commentId },
    });

    if (!comment) {
      throw new NotFoundException("评论不存在");
    }

    if (comment.authorId !== authorId) {
      throw new ForbiddenException("只能编辑自己的评论");
    }

    if (comment.status !== CommentStatus.ACTIVE) {
      throw new BadRequestException("该评论无法编辑");
    }

    // 更新评论
    await this.commentRepository.update(commentId, {
      content: updateCommentDto.content,
      // 只更新允许的字段
    });

    const updatedComment = await this.commentRepository.findOne({
      where: { id: commentId },
      relations: ["author"],
    });

    if (!updatedComment) {
      throw new NotFoundException("更新后的评论不存在");
    }

    return this.buildCommentResponse(updatedComment, authorId);
  }

  /**
   * 删除评论
   */
  async deleteComment(commentId: string, currentUserId: string): Promise<void> {
    const comment = await this.commentRepository.findOne({
      where: { id: commentId },
      relations: ["targetStory"],
    });

    if (!comment) {
      throw new NotFoundException("评论不存在");
    }

    // 检查删除权限
    const canDelete = await this.canDeleteComment(comment, currentUserId);
    if (!canDelete) {
      throw new ForbiddenException("没有权限删除该评论");
    }

    // 软删除评论（保留子评论）
    await this.commentRepository.update(commentId, {
      status: CommentStatus.DELETED,
      content: "[评论已删除]",
    });

    // 如果有父评论，减少父评论的回复数
    if (comment.parentCommentId) {
      await this.commentRepository.decrement(
        { id: comment.parentCommentId },
        "replyCount",
        1,
      );
    }
  }

  /**
   * 点赞/取消点赞评论
   */
  async toggleCommentLike(
    commentId: string,
    userId: string,
  ): Promise<CommentLikeResponseDto> {
    const comment = await this.commentRepository.findOne({
      where: { id: commentId, status: CommentStatus.ACTIVE },
    });

    if (!comment) {
      throw new NotFoundException("评论不存在");
    }

    // 检查是否已点赞
    const existingLike = await this.commentLikeRepository.findOne({
      where: { commentId, userId },
    });

    let isLiked: boolean;
    let likeCount: number;

    if (existingLike) {
      // 取消点赞
      await this.commentLikeRepository.remove(existingLike);
      await this.commentRepository.decrement({ id: commentId }, "likeCount", 1);
      isLiked = false;
      likeCount = comment.likeCount - 1;
    } else {
      // 添加点赞
      const like = this.commentLikeRepository.create({
        commentId,
        userId,
      });
      await this.commentLikeRepository.save(like);
      await this.commentRepository.increment({ id: commentId }, "likeCount", 1);
      isLiked = true;
      likeCount = comment.likeCount + 1;
    }

    return {
      commentId,
      isLiked,
      likeCount,
    };
  }

  /**
   * 获取评论统计
   */
  async getCommentStats(
    targetType: CommentTargetType,
    targetId: string,
  ): Promise<CommentStatsResponseDto> {
    const stats = await this.commentRepository
      .createQueryBuilder("comment")
      .select([
        "COUNT(*) as totalComments",
        "SUM(comment.likeCount) as totalLikes",
        "SUM(comment.replyCount) as totalReplies",
        "COUNT(CASE WHEN comment.status = :activeStatus THEN 1 END) as activeComments",
      ])
      .where("comment.targetType = :targetType", { targetType })
      .andWhere("comment.targetId = :targetId", { targetId })
      .setParameter("activeStatus", CommentStatus.ACTIVE)
      .getRawOne();

    return {
      totalComments: parseInt(stats.totalComments) || 0,
      totalLikes: parseInt(stats.totalLikes) || 0,
      totalReplies: parseInt(stats.totalReplies) || 0,
      activeComments: parseInt(stats.activeComments) || 0,
    };
  }

  /**
   * 构建评论查询
   */
  private buildCommentQuery(
    query: CommentQueryDto,
  ): SelectQueryBuilder<Comment> {
    const queryBuilder = this.commentRepository
      .createQueryBuilder("comment")
      .leftJoinAndSelect("comment.author", "author")
      .where("1 = 1");

    if (query.targetType) {
      queryBuilder.andWhere("comment.targetType = :targetType", {
        targetType: query.targetType,
      });
    }

    if (query.targetId) {
      queryBuilder.andWhere("comment.targetId = :targetId", {
        targetId: query.targetId,
      });
    }

    if (query.status) {
      queryBuilder.andWhere("comment.status = :status", {
        status: query.status,
      });
    } else {
      queryBuilder.andWhere("comment.status = :status", {
        status: CommentStatus.ACTIVE,
      });
    }

    if (query.authorId) {
      queryBuilder.andWhere("comment.authorId = :authorId", {
        authorId: query.authorId,
      });
    }

    if (query.rootCommentId) {
      queryBuilder.andWhere("comment.rootCommentId = :rootCommentId", {
        rootCommentId: query.rootCommentId,
      });
    }

    if (query.keyword) {
      queryBuilder.andWhere("comment.content ILIKE :keyword", {
        keyword: `%${query.keyword}%`,
      });
    }

    return queryBuilder;
  }

  /**
   * 应用排序
   */
  private applySorting(
    queryBuilder: SelectQueryBuilder<Comment>,
    sortBy: CommentSortBy,
  ): void {
    switch (sortBy) {
      case CommentSortBy.CREATED_DESC:
        queryBuilder.orderBy("comment.createdAt", "DESC");
        break;
      case CommentSortBy.CREATED_ASC:
        queryBuilder.orderBy("comment.createdAt", "ASC");
        break;
      case CommentSortBy.LIKE_COUNT_DESC:
        queryBuilder.orderBy("comment.likeCount", "DESC");
        break;
      case CommentSortBy.REPLY_COUNT_DESC:
        queryBuilder.orderBy("comment.replyCount", "DESC");
        break;
      default:
        queryBuilder.orderBy("comment.createdAt", "DESC");
    }
  }

  /**
   * 构建评论响应数据
   */
  private async buildCommentResponse(
    comment: Comment,
    currentUserId?: string,
  ): Promise<CommentResponseDto> {
    // 检查当前用户是否已点赞
    let isLiked = false;
    if (currentUserId) {
      const like = await this.commentLikeRepository.findOne({
        where: { commentId: comment.id, userId: currentUserId },
      });
      isLiked = !!like;
    }

    return {
      id: comment.id,
      targetType: comment.targetType,
      targetId: comment.targetId,
      content: comment.content,
      authorId: comment.authorId,
      authorName: comment.author?.nickname || "未知用户",
      authorAvatar: comment.author?.avatarUrl,
      status: comment.status,
      likeCount: comment.likeCount,
      replyCount: comment.replyCount,
      rootCommentId: comment.rootCommentId,
      parentCommentId: comment.parentCommentId,
      depth: comment.depth,
      isLiked,
      metadata: comment.metadata,
      createdAt: comment.createdAt,
      updatedAt: comment.updatedAt,
    };
  }

  /**
   * 验证评论目标是否存在
   */
  private async validateTarget(
    targetType: CommentTargetType,
    targetId: string,
  ): Promise<void> {
    if (targetType === CommentTargetType.STORY) {
      const story = await this.storyRepository.findOne({
        where: { id: targetId },
      });
      if (!story) {
        throw new NotFoundException("目标故事不存在");
      }
    } else if (targetType === CommentTargetType.COMMENT) {
      const comment = await this.commentRepository.findOne({
        where: { id: targetId, status: CommentStatus.ACTIVE },
      });
      if (!comment) {
        throw new NotFoundException("目标评论不存在");
      }
    }
  }

  /**
   * 检查是否可以删除评论
   */
  private async canDeleteComment(
    comment: Comment,
    currentUserId: string,
  ): Promise<boolean> {
    // 评论作者可以删除自己的评论
    if (comment.authorId === currentUserId) {
      return true;
    }

    // 如果是对故事的评论，故事作者也可以删除
    if (comment.targetType === CommentTargetType.STORY) {
      const story = await this.storyRepository.findOne({
        where: { id: comment.targetId },
      });
      if (story && story.userId === currentUserId) {
        return true;
      }
    }

    return false;
  }
}
