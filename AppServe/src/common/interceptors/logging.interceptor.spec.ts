import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import type { ExecutionContext, CallHandler } from "@nestjs/common";
import { Logger } from "@nestjs/common";
import { LoggingInterceptor } from "./logging.interceptor";
import { of } from "rxjs";
import type { FastifyRequest } from "fastify";

describe("LoggingInterceptor", () => {
  let interceptor: LoggingInterceptor;
  let mockLogger: jest.SpyInstance;

  const mockRequest: Partial<FastifyRequest> = {
    method: "GET",
    url: "/api/test",
  };

  let mockExecutionContext: ExecutionContext;

  const mockCallHandler: CallHandler = {
    handle: jest.fn().mockReturnValue(of({ message: "test response" })),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [LoggingInterceptor],
    }).compile();

    interceptor = module.get<LoggingInterceptor>(LoggingInterceptor);

    // 在每个测试前重新创建Mock
    mockExecutionContext = {
      switchToHttp: jest.fn().mockReturnValue({
        getRequest: jest.fn().mockReturnValue(mockRequest),
      }),
      getHandler: jest.fn(),
      getClass: jest.fn(),
      getArgs: jest.fn(),
      getArgByIndex: jest.fn(),
      switchToRpc: jest.fn(),
      switchToWs: jest.fn(),
      getType: jest.fn(),
    } as unknown as ExecutionContext;

    // Mock Logger
    mockLogger = jest
      .spyOn(Logger.prototype, "log")
      .mockImplementation(() => {});
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe("intercept", () => {
    it("应该记录请求方法和URL", (done) => {
      const result$ = interceptor.intercept(
        mockExecutionContext,
        mockCallHandler,
      );

      result$.subscribe({
        next: (data) => {
          expect(data).toEqual({ message: "test response" });
          expect(mockCallHandler.handle).toHaveBeenCalled();
          expect(mockExecutionContext.switchToHttp).toHaveBeenCalled();
        },
        complete: () => {
          // 使用setTimeout确保异步操作完成
          setTimeout(() => {
            expect(mockLogger).toHaveBeenCalledWith(
              expect.stringMatching(/GET \/api\/test - \d+ms/),
              "LoggingInterceptor",
            );
            done();
          }, 0);
        },
      });
    });

    it("应该记录响应时间", (done) => {
      const startTime = Date.now();
      jest
        .spyOn(Date, "now")
        .mockReturnValueOnce(startTime)
        .mockReturnValueOnce(startTime + 100);

      const result$ = interceptor.intercept(
        mockExecutionContext,
        mockCallHandler,
      );

      result$.subscribe({
        complete: () => {
          setTimeout(() => {
            expect(mockLogger).toHaveBeenCalledWith(
              "GET /api/test - 100ms",
              "LoggingInterceptor",
            );
            done();
          }, 0);
        },
      });
    });

    it("应该处理POST请求", (done) => {
      const postRequest = { method: "POST", url: "/api/users" };
      const postContext = {
        switchToHttp: jest.fn().mockReturnValue({
          getRequest: jest.fn().mockReturnValue(postRequest),
        }),
      } as unknown as ExecutionContext;

      const result$ = interceptor.intercept(postContext, mockCallHandler);

      result$.subscribe({
        complete: () => {
          setTimeout(() => {
            expect(mockLogger).toHaveBeenCalledWith(
              expect.stringMatching(/POST \/api\/users - \d+ms/),
              "LoggingInterceptor",
            );
            done();
          }, 0);
        },
      });
    });

    it("应该处理PUT请求", (done) => {
      const putRequest = { method: "PUT", url: "/api/users/123" };
      const putContext = {
        switchToHttp: jest.fn().mockReturnValue({
          getRequest: jest.fn().mockReturnValue(putRequest),
        }),
      } as unknown as ExecutionContext;

      const result$ = interceptor.intercept(putContext, mockCallHandler);

      result$.subscribe({
        complete: () => {
          setTimeout(() => {
            expect(mockLogger).toHaveBeenCalledWith(
              expect.stringMatching(/PUT \/api\/users\/123 - \d+ms/),
              "LoggingInterceptor",
            );
            done();
          }, 0);
        },
      });
    });

    it("应该处理DELETE请求", (done) => {
      const deleteRequest = { method: "DELETE", url: "/api/users/123" };
      const deleteContext = {
        switchToHttp: jest.fn().mockReturnValue({
          getRequest: jest.fn().mockReturnValue(deleteRequest),
        }),
      } as unknown as ExecutionContext;

      const result$ = interceptor.intercept(deleteContext, mockCallHandler);

      result$.subscribe({
        complete: () => {
          setTimeout(() => {
            expect(mockLogger).toHaveBeenCalledWith(
              expect.stringMatching(/DELETE \/api\/users\/123 - \d+ms/),
              "LoggingInterceptor",
            );
            done();
          }, 0);
        },
      });
    });

    it("应该处理包含查询参数的URL", (done) => {
      const requestWithQuery = {
        method: "GET",
        url: "/api/test?page=1&limit=10",
      };
      const contextWithQuery = {
        switchToHttp: jest.fn().mockReturnValue({
          getRequest: jest.fn().mockReturnValue(requestWithQuery),
        }),
      } as unknown as ExecutionContext;

      const result$ = interceptor.intercept(contextWithQuery, mockCallHandler);

      result$.subscribe({
        complete: () => {
          setTimeout(() => {
            expect(mockLogger).toHaveBeenCalledWith(
              expect.stringMatching(/GET \/api\/test\?page=1&limit=10 - \d+ms/),
              "LoggingInterceptor",
            );
            done();
          }, 0);
        },
      });
    });

    it("应该传递原始响应数据", (done) => {
      const responseData = { id: 1, name: "test", items: [1, 2, 3] };
      const customCallHandler: CallHandler = {
        handle: jest.fn().mockReturnValue(of(responseData)),
      };

      const result$ = interceptor.intercept(
        mockExecutionContext,
        customCallHandler,
      );

      result$.subscribe({
        next: (data) => {
          expect(data).toEqual(responseData);
          done();
        },
      });
    });

    it("应该在出错时仍然记录日志", (done) => {
      const errorResponse = new Error("Internal Server Error");
      const errorCallHandler: CallHandler = {
        handle: jest.fn().mockReturnValue(of(errorResponse)),
      };

      const result$ = interceptor.intercept(
        mockExecutionContext,
        errorCallHandler,
      );

      result$.subscribe({
        next: (data) => {
          expect(data).toBe(errorResponse);
        },
        complete: () => {
          setTimeout(() => {
            expect(mockLogger).toHaveBeenCalledWith(
              expect.stringMatching(/GET \/api\/test - \d+ms/),
              "LoggingInterceptor",
            );
            done();
          }, 0);
        },
      });
    });

    it("应该处理空响应", (done) => {
      const emptyCallHandler: CallHandler = {
        handle: jest.fn().mockReturnValue(of(null)),
      };

      const result$ = interceptor.intercept(
        mockExecutionContext,
        emptyCallHandler,
      );

      result$.subscribe({
        next: (data) => {
          expect(data).toBeNull();
        },
        complete: () => {
          setTimeout(() => {
            expect(mockLogger).toHaveBeenCalledWith(
              expect.stringMatching(/GET \/api\/test - \d+ms/),
              "LoggingInterceptor",
            );
            done();
          }, 0);
        },
      });
    });

    it("应该处理不同的响应时间", (done) => {
      const startTime = Date.now();
      jest
        .spyOn(Date, "now")
        .mockReturnValueOnce(startTime)
        .mockReturnValueOnce(startTime + 500);

      const result$ = interceptor.intercept(
        mockExecutionContext,
        mockCallHandler,
      );

      result$.subscribe({
        complete: () => {
          setTimeout(() => {
            expect(mockLogger).toHaveBeenCalledWith(
              "GET /api/test - 500ms",
              "LoggingInterceptor",
            );
            done();
          }, 0);
        },
      });
    });
  });

  it("应该正确实例化", () => {
    expect(interceptor).toBeDefined();
    expect(interceptor).toBeInstanceOf(LoggingInterceptor);
  });
});
