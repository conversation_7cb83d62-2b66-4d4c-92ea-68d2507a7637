# OSX
#
.DS_Store

# Xcode
#
build/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata
*.xccheckout
*.moved-aside
DerivedData
*.hmap
*.ipa
*.xcuserstate
**/.xcode.env.local
*.dSYM/
*.xcarchive

# Android/IntelliJ
#
build/
.idea
.gradle
local.properties
*.iml
*.hprof
.cxx/
*.keystore
!debug.keystore
.kotlin/
lint-results*
*.apk
*.aab

# node.js
#
node_modules/
npm-debug.log
yarn-error.log
*.log
package-lock.json.tmp
yarn.lock.tmp

# fastlane
#
# It is recommended to not store the screenshots in the git repo. Instead, use fastlane to re-generate the
# screenshots whenever they are needed.
# For more information about the recommended setup visit:
# https://docs.fastlane.tools/best-practices/source-control/

**/fastlane/report.xml
**/fastlane/Preview.html
**/fastlane/screenshots
**/fastlane/test_output

# Bundle artifact
*.jsbundle

# Ruby / CocoaPods
**/Pods/
/vendor/bundle/

# Temporary files created by Metro to check the health of the file watcher
.metro-health-check*

# testing
/coverage

# Yarn
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

# React Native
*.orig
.expo/
.expo-shared/

# React Native 构建产物
**/android/app/build/
**/android/build/
**/ios/build/
**/ios/DerivedData/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# Cache directories
.cache/
.tmp/
.temp/

# Gradle cache and wrapper
.gradle/
gradle-app.setting
!gradle/wrapper/gradle-wrapper.jar
!**/src/main/**/build/
!**/src/test/**/build/

# CMake 构建文件
CMakeFiles/
CMakeCache.txt
cmake_install.cmake
Makefile
CMakeScripts/
CMakeOutput.log
CMakeError.log
*.cmake
cmake-build-*/

# Android NDK/CMake 构建产物
**/.cxx/
**/CMakeFiles/
**/CMakeCache.txt
**/cmake_install.cmake
android/app/.cxx/
android/app/build/outputs/
android/.gradle/

# Flipper
**/Flipper/

# Build scripts (custom)
android/fast-build.sh

# Backup files
*.bak
*.backup

# Archive files (except those in assets)
*.zip
*.tar.gz
*.rar
!**/assets/**/*.zip
!**/assets/**/*.tar.gz

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# React Native 特定日志
**/CMakeOutput.log
**/CMakeError.log
metro.log

# Reports
reports/
coverage/

# Temporary files
*.tmp
*.temp
.tmp/
.temp/

# Lock files (keep package-lock.json but ignore temporary ones)
*.lock.tmp
*.lockb

# TypeScript incremental compilation info
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# React Native 自动链接文件夹
**/autolinked_build/
**/*_autolinked_build/
