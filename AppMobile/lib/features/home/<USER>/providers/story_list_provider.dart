import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/story_entity.dart';
import '../../data/datasources/story_mock_datasource.dart';

part 'story_list_provider.freezed.dart';

/// 故事列表状态
@freezed
class StoryListState with _$StoryListState {
  const factory StoryListState({
    @Default([]) List<StoryEntity> stories,
    @Default(false) bool isLoading,
    @Default(false) bool isLoadingMore,
    @Default(true) bool hasNextPage,
    @Default(1) int currentPage,
    @Default(10) int pageSize,
    String? error,
    String? selectedCategory,
  }) = _StoryListState;
}

/// 故事列表Provider - 实现高性能分页和缓存
class StoryListNotifier extends StateNotifier<StoryListState> {
  StoryListNotifier() : super(const StoryListState());

  /// 初始化加载故事列表
  Future<void> loadStories({String? category}) async {
    if (state.isLoading) return;

    state = state.copyWith(
      isLoading: true,
      error: null,
      selectedCategory: category,
      stories: [], // 清空现有数据
      currentPage: 1,
      hasNextPage: true,
    );

    try {
      final response = await StoryMockDataSource.getMockStoryList(
        page: 1,
        limit: state.pageSize,
        category: category,
      );

      state = state.copyWith(
        stories: response.stories,
        hasNextPage: response.hasNextPage,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        error: '加载故事失败: ${e.toString()}',
        isLoading: false,
      );
    }
  }

  /// 加载更多故事 - 分页加载
  Future<void> loadMoreStories() async {
    if (state.isLoadingMore || !state.hasNextPage) return;

    state = state.copyWith(isLoadingMore: true, error: null);

    try {
      final nextPage = state.currentPage + 1;
      final response = await StoryMockDataSource.getMockStoryList(
        page: nextPage,
        limit: state.pageSize,
        category: state.selectedCategory,
      );

      // 防重复数据
      final existingIds = state.stories.map((s) => s.id).toSet();
      final newStories = response.stories
          .where((story) => !existingIds.contains(story.id))
          .toList();

      state = state.copyWith(
        stories: [...state.stories, ...newStories],
        currentPage: nextPage,
        hasNextPage: response.hasNextPage,
        isLoadingMore: false,
      );
    } catch (e) {
      state = state.copyWith(
        error: '加载更多失败: ${e.toString()}',
        isLoadingMore: false,
      );
    }
  }

  /// 刷新故事列表 - 下拉刷新专用，不清空现有数据
  Future<void> refreshStories() async {
    if (state.isLoading) return;

    // 不清空现有数据，只设置loading状态
    state = state.copyWith(
      isLoading: true,
      error: null,
      currentPage: 1,
      hasNextPage: true,
    );

    try {
      final response = await StoryMockDataSource.getMockStoryList(
        page: 1,
        limit: state.pageSize,
        category: state.selectedCategory,
      );

      // 直接替换数据，不显示骨架屏
      state = state.copyWith(
        stories: response.stories,
        hasNextPage: response.hasNextPage,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        error: '刷新失败: ${e.toString()}',
        isLoading: false,
      );
    }
  }

  /// 点赞故事
  Future<void> likeStory(String storyId) async {
    try {
      final updatedStories = state.stories.map((story) {
        if (story.id == storyId) {
          return story.copyWith(
            isLiked: !story.isLiked,
            likeCount: story.isLiked 
                ? story.likeCount - 1 
                : story.likeCount + 1,
          );
        }
        return story;
      }).toList();

      state = state.copyWith(stories: updatedStories);
      
      // TODO: 调用实际API
      // await _repository.likeStory(storyId);
    } catch (e) {
      state = state.copyWith(error: '点赞失败: ${e.toString()}');
    }
  }

  /// 切换分类
  Future<void> changeCategory(String? category) async {
    if (state.selectedCategory == category) return;
    await loadStories(category: category);
  }
}

/// Provider定义
final storyListProvider = StateNotifierProvider<StoryListNotifier, StoryListState>((ref) {
  return StoryListNotifier();
});

/// 关注Tab的故事列表Provider
final followingStoriesProvider = Provider<AsyncValue<List<StoryEntity>>>((ref) {
  final state = ref.watch(storyListProvider);
  
  if (state.isLoading && state.stories.isEmpty) {
    return const AsyncValue.loading();
  }
  
  if (state.error != null && state.stories.isEmpty) {
    return AsyncValue.error(state.error!, StackTrace.current);
  }
  
  return AsyncValue.data(state.stories);
});

/// 世界Tab的故事列表Provider
final worldStoriesProvider = Provider<AsyncValue<List<StoryEntity>>>((ref) {
  final state = ref.watch(storyListProvider);
  
  if (state.isLoading && state.stories.isEmpty) {
    return const AsyncValue.loading();
  }
  
  if (state.error != null && state.stories.isEmpty) {
    return AsyncValue.error(state.error!, StackTrace.current);
  }
  
  return AsyncValue.data(state.stories);
});