/**
 * 企业级分享系统种子数据
 *
 * 基于 YGS v1.0.0 故事分享功能设计
 * 涵盖分享链接、访问控制、访问日志等完整场景
 */

import { USER_ROLES } from "./enhanced-user.seeder";
import { STORY_TITLES } from "./enhanced-story.seeder";

export interface EnhancedShareSeedData {
  sharerUsername: string;
  storyTitle: string;
  shareToken: string;
  shareType: "public" | "password" | "limited";
  password?: string;
  maxAccessCount?: number;
  currentAccessCount: number;
  expiresAt?: string;
  isActive: boolean;
  createdDaysAgo: number;
  tags: string[];
  note?: string;
}

export interface EnhancedShareAccessLogSeedData {
  shareToken: string;
  accessorIp: string;
  accessorUserAgent: string;
  accessorUsername?: string; // 如果是登录用户访问
  accessResult:
    | "success"
    | "password_required"
    | "expired"
    | "limit_exceeded"
    | "blocked";
  accessDaysAgo: number;
  accessLocation?: string;
  referrer?: string;
}

export const ENHANCED_SHARE_SEED_DATA: EnhancedShareSeedData[] = [
  // ========== 张明远的分享 ==========
  {
    sharerUsername: USER_ROLES.ZHANG_MINGYUAN,
    storyTitle: STORY_TITLES.UNIVERSITY_PROMISE,
    shareToken: "share_university_promise_001",
    shareType: "public",
    currentAccessCount: 45,
    isActive: true,
    createdDaysAgo: 20,
    tags: ["友情", "大学", "热门"],
    note: "分享给朋友圈，希望更多人能看到我们的友情故事",
  },
  {
    sharerUsername: USER_ROLES.ZHANG_MINGYUAN,
    storyTitle: STORY_TITLES.STARTUP_JOURNEY,
    shareToken: "share_startup_journey_001",
    shareType: "password",
    password: "startup2020",
    currentAccessCount: 12,
    isActive: true,
    createdDaysAgo: 15,
    tags: ["创业", "朋友", "私密"],
    note: "只分享给创业圈的朋友，设置了密码保护",
  },
  {
    sharerUsername: USER_ROLES.ZHANG_MINGYUAN,
    storyTitle: STORY_TITLES.RAINY_NIGHT,
    shareToken: "share_rainy_night_001",
    shareType: "limited",
    maxAccessCount: 10,
    currentAccessCount: 8,
    expiresAt: "2025-08-15",
    isActive: true,
    createdDaysAgo: 10,
    tags: ["私密", "友情", "限制访问"],
    note: "仅限李明和几个特别好的朋友查看",
  },

  // ========== 王建国的分享 ==========
  {
    sharerUsername: USER_ROLES.WANG_JIANGUO,
    storyTitle: STORY_TITLES.STUDENT_XIAOYU,
    shareToken: "share_student_xiaoyu_001",
    shareType: "public",
    currentAccessCount: 89,
    isActive: true,
    createdDaysAgo: 25,
    tags: ["师生情", "励志", "教育"],
    note: "分享给教师同行，希望他们也能感受到教育的意义",
  },
  {
    sharerUsername: USER_ROLES.WANG_JIANGUO,
    storyTitle: STORY_TITLES.TEACHING_REGRET,
    shareToken: "share_teaching_regret_001",
    shareType: "password",
    password: "teacher30years",
    currentAccessCount: 23,
    isActive: true,
    createdDaysAgo: 18,
    tags: ["教育反思", "私密", "教师"],
    note: "分享给同事，需要密码保护的深度反思内容",
  },

  // ========== 李小红的分享 ==========
  {
    sharerUsername: USER_ROLES.LI_XIAOHONG,
    storyTitle: STORY_TITLES.DRAMA_MEMORIES,
    shareToken: "share_drama_memories_001",
    shareType: "public",
    currentAccessCount: 34,
    isActive: true,
    createdDaysAgo: 12,
    tags: ["青春", "追剧", "回忆"],
    note: "分享给喜欢看剧的朋友们，回忆那些美好时光",
  },
  {
    sharerUsername: USER_ROLES.LI_XIAOHONG,
    storyTitle: STORY_TITLES.UNIVERSITY_PROMISE,
    shareToken: "share_lighted_story_001",
    shareType: "limited",
    maxAccessCount: 5,
    currentAccessCount: 3,
    expiresAt: "2025-09-01",
    isActive: true,
    createdDaysAgo: 8,
    tags: ["点亮成功", "特殊分享", "纪念"],
    note: "分享我成功点亮的故事，限制访问次数作为纪念",
  },

  // ========== 陈总的分享 ==========
  {
    sharerUsername: USER_ROLES.CHEN_VIP,
    storyTitle: STORY_TITLES.UNIVERSITY_PROMISE,
    shareToken: "share_vip_recommend_001",
    shareType: "public",
    currentAccessCount: 156,
    isActive: true,
    createdDaysAgo: 30,
    tags: ["VIP推荐", "优质内容", "商业分享"],
    note: "VIP用户推荐的优质故事，分享给商业伙伴们",
  },
  {
    sharerUsername: USER_ROLES.CHEN_VIP,
    storyTitle: STORY_TITLES.STARTUP_JOURNEY,
    shareToken: "share_business_case_001",
    shareType: "password",
    password: "business2025",
    currentAccessCount: 67,
    isActive: true,
    createdDaysAgo: 22,
    tags: ["商业案例", "创业", "VIP分享"],
    note: "作为商业案例分享给投资人和创业者，需要密码",
  },

  // ========== 过期和失效的分享 ==========
  {
    sharerUsername: USER_ROLES.ZHAO_XIAOFANG,
    storyTitle: STORY_TITLES.STUDENT_XIAOYU,
    shareToken: "share_expired_001",
    shareType: "limited",
    maxAccessCount: 20,
    currentAccessCount: 20,
    expiresAt: "2025-07-01",
    isActive: false,
    createdDaysAgo: 35,
    tags: ["已过期", "达到上限"],
    note: "分享给朋友的励志故事，已达到访问上限",
  },
  {
    sharerUsername: USER_ROLES.WU_SIMI,
    storyTitle: STORY_TITLES.LETTER_TO_FUTURE,
    shareToken: "share_private_expired_001",
    shareType: "password",
    password: "private123",
    currentAccessCount: 2,
    expiresAt: "2025-07-20",
    isActive: false,
    createdDaysAgo: 40,
    tags: ["私密", "已过期", "个人"],
    note: "曾经短暂分享给家人的私密内容，现已过期",
  },
];

export const ENHANCED_SHARE_ACCESS_LOG_SEED_DATA: EnhancedShareAccessLogSeedData[] =
  [
    // ========== 《那年夏天，我们约定十年后再聚》的访问记录 ==========
    {
      shareToken: "share_university_promise_001",
      accessorIp: "*************",
      accessorUserAgent:
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
      accessorUsername: USER_ROLES.LI_XIAOHONG,
      accessResult: "success",
      accessDaysAgo: 19,
      accessLocation: "上海",
      referrer: "https://weixin.qq.com",
    },
    {
      shareToken: "share_university_promise_001",
      accessorIp: "*************",
      accessorUserAgent:
        "Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15",
      accessorUsername: USER_ROLES.WANG_JIANGUO,
      accessResult: "success",
      accessDaysAgo: 18,
      accessLocation: "北京",
      referrer: "https://weixin.qq.com",
    },
    {
      shareToken: "share_university_promise_001",
      accessorIp: "*************",
      accessorUserAgent:
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
      accessResult: "success",
      accessDaysAgo: 17,
      accessLocation: "深圳",
      referrer: "https://t.me",
    },
    {
      shareToken: "share_university_promise_001",
      accessorIp: "*************",
      accessorUserAgent:
        "Mozilla/5.0 (Android 12; Mobile; rv:109.0) Gecko/109.0 Firefox/115.0",
      accessorUsername: USER_ROLES.CHEN_VIP,
      accessResult: "success",
      accessDaysAgo: 15,
      accessLocation: "杭州",
      referrer: "direct",
    },
    {
      shareToken: "share_university_promise_001",
      accessorIp: "*************",
      accessorUserAgent:
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
      accessResult: "success",
      accessDaysAgo: 10,
      accessLocation: "广州",
      referrer: "https://weibo.com",
    },

    // ========== 《创业路上，感谢有你们》的访问记录（需要密码） ==========
    {
      shareToken: "share_startup_journey_001",
      accessorIp: "*************",
      accessorUserAgent:
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
      accessorUsername: USER_ROLES.CHEN_VIP,
      accessResult: "success",
      accessDaysAgo: 14,
      accessLocation: "上海",
      referrer: "direct",
    },
    {
      shareToken: "share_startup_journey_001",
      accessorIp: "*************",
      accessorUserAgent:
        "Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15",
      accessResult: "password_required",
      accessDaysAgo: 12,
      accessLocation: "北京",
      referrer: "https://linkedin.com",
    },
    {
      shareToken: "share_startup_journey_001",
      accessorIp: "*************",
      accessorUserAgent:
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
      accessorUsername: USER_ROLES.LI_XIAOHONG,
      accessResult: "success",
      accessDaysAgo: 11,
      accessLocation: "苏州",
      referrer: "direct",
    },
    {
      shareToken: "share_startup_journey_001",
      accessorIp: "*************",
      accessorUserAgent:
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
      accessResult: "password_required",
      accessDaysAgo: 8,
      accessLocation: "深圳",
      referrer: "https://zhihu.com",
    },

    // ========== 《小雨，你现在过得还好吗》的访问记录 ==========
    {
      shareToken: "share_student_xiaoyu_001",
      accessorIp: "192.168.1.300",
      accessorUserAgent:
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
      accessorUsername: USER_ROLES.ZHOU_FAXIAN,
      accessResult: "success",
      accessDaysAgo: 24,
      accessLocation: "某县城",
      referrer: "https://weixin.qq.com",
    },
    {
      shareToken: "share_student_xiaoyu_001",
      accessorIp: "192.168.1.301",
      accessorUserAgent:
        "Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15",
      accessorUsername: USER_ROLES.LI_XIAOHONG,
      accessResult: "success",
      accessDaysAgo: 23,
      accessLocation: "苏州",
      referrer: "https://weixin.qq.com",
    },
    {
      shareToken: "share_student_xiaoyu_001",
      accessorIp: "192.168.1.302",
      accessorUserAgent:
        "Mozilla/5.0 (Android 12; Mobile; rv:109.0) Gecko/109.0 Firefox/115.0",
      accessorUsername: USER_ROLES.ZHAO_XIAOFANG,
      accessResult: "success",
      accessDaysAgo: 20,
      accessLocation: "成都",
      referrer: "https://douban.com",
    },

    // ========== 限制访问的分享记录 ==========
    {
      shareToken: "share_rainy_night_001",
      accessorIp: "192.168.1.400",
      accessorUserAgent:
        "Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15",
      accessorUsername: USER_ROLES.LI_XIAOHONG,
      accessResult: "success",
      accessDaysAgo: 9,
      accessLocation: "苏州",
      referrer: "direct",
    },
    {
      shareToken: "share_rainy_night_001",
      accessorIp: "192.168.1.401",
      accessorUserAgent:
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
      accessorUsername: USER_ROLES.WANG_JIANGUO,
      accessResult: "success",
      accessDaysAgo: 7,
      accessLocation: "北京",
      referrer: "direct",
    },
    {
      shareToken: "share_rainy_night_001",
      accessorIp: "192.168.1.402",
      accessorUserAgent:
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
      accessResult: "limit_exceeded",
      accessDaysAgo: 3,
      accessLocation: "上海",
      referrer: "direct",
    },

    // ========== VIP分享的访问记录 ==========
    {
      shareToken: "share_vip_recommend_001",
      accessorIp: "192.168.1.500",
      accessorUserAgent:
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
      accessResult: "success",
      accessDaysAgo: 29,
      accessLocation: "上海",
      referrer: "https://linkedin.com",
    },
    {
      shareToken: "share_vip_recommend_001",
      accessorIp: "192.168.1.501",
      accessorUserAgent:
        "Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15",
      accessResult: "success",
      accessDaysAgo: 28,
      accessLocation: "北京",
      referrer: "https://linkedin.com",
    },
    {
      shareToken: "share_vip_recommend_001",
      accessorIp: "192.168.1.502",
      accessorUserAgent:
        "Mozilla/5.0 (Android 12; Mobile; rv:109.0) Gecko/109.0 Firefox/115.0",
      accessResult: "success",
      accessDaysAgo: 25,
      accessLocation: "深圳",
      referrer: "https://36kr.com",
    },

    // ========== 失败的访问记录 ==========
    {
      shareToken: "share_expired_001",
      accessorIp: "192.168.1.600",
      accessorUserAgent:
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
      accessResult: "expired",
      accessDaysAgo: 5,
      accessLocation: "广州",
      referrer: "https://weixin.qq.com",
    },
    {
      shareToken: "share_private_expired_001",
      accessorIp: "192.168.1.601",
      accessorUserAgent:
        "Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15",
      accessResult: "expired",
      accessDaysAgo: 10,
      accessLocation: "杭州",
      referrer: "direct",
    },
    {
      shareToken: "share_business_case_001",
      accessorIp: "192.168.1.602",
      accessorUserAgent:
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
      accessResult: "password_required",
      accessDaysAgo: 15,
      accessLocation: "南京",
      referrer: "https://zhihu.com",
    },
  ];

// 导出分享统计数据
export const SHARING_STATS = {
  totalShares: ENHANCED_SHARE_SEED_DATA.length,
  activeShares: ENHANCED_SHARE_SEED_DATA.filter((s) => s.isActive).length,
  expiredShares: ENHANCED_SHARE_SEED_DATA.filter((s) => !s.isActive).length,

  // 按分享类型统计
  byShareType: {
    public: ENHANCED_SHARE_SEED_DATA.filter((s) => s.shareType === "public")
      .length,
    password: ENHANCED_SHARE_SEED_DATA.filter((s) => s.shareType === "password")
      .length,
    limited: ENHANCED_SHARE_SEED_DATA.filter((s) => s.shareType === "limited")
      .length,
  },

  // 访问统计
  totalAccesses: ENHANCED_SHARE_ACCESS_LOG_SEED_DATA.length,
  successfulAccesses: ENHANCED_SHARE_ACCESS_LOG_SEED_DATA.filter(
    (a) => a.accessResult === "success",
  ).length,
  blockedAccesses: ENHANCED_SHARE_ACCESS_LOG_SEED_DATA.filter((a) =>
    ["password_required", "expired", "limit_exceeded", "blocked"].includes(
      a.accessResult,
    ),
  ).length,

  // 按访问结果统计
  byAccessResult: {
    success: ENHANCED_SHARE_ACCESS_LOG_SEED_DATA.filter(
      (a) => a.accessResult === "success",
    ).length,
    password_required: ENHANCED_SHARE_ACCESS_LOG_SEED_DATA.filter(
      (a) => a.accessResult === "password_required",
    ).length,
    expired: ENHANCED_SHARE_ACCESS_LOG_SEED_DATA.filter(
      (a) => a.accessResult === "expired",
    ).length,
    limit_exceeded: ENHANCED_SHARE_ACCESS_LOG_SEED_DATA.filter(
      (a) => a.accessResult === "limit_exceeded",
    ).length,
    blocked: ENHANCED_SHARE_ACCESS_LOG_SEED_DATA.filter(
      (a) => a.accessResult === "blocked",
    ).length,
  },

  // 热门分享（按访问次数排序）
  topShares: ENHANCED_SHARE_SEED_DATA.sort(
    (a, b) => b.currentAccessCount - a.currentAccessCount,
  )
    .slice(0, 5)
    .map((s) => ({
      storyTitle: s.storyTitle,
      accessCount: s.currentAccessCount,
      shareType: s.shareType,
    })),

  // 流量来源分析
  trafficSources: (() => {
    const sources: { [key: string]: number } = {};
    ENHANCED_SHARE_ACCESS_LOG_SEED_DATA.forEach((log) => {
      const referrer = log.referrer || "direct";
      const domain =
        referrer === "direct"
          ? "direct"
          : referrer.replace("https://", "").split("/")[0];
      sources[domain] = (sources[domain] || 0) + 1;
    });
    return Object.entries(sources)
      .sort((a, b) => (b[1] as number) - (a[1] as number))
      .slice(0, 5)
      .map(([source, count]) => ({ source, count }));
  })(),

  // 地理位置分布
  locationDistribution: (() => {
    const locations: { [key: string]: number } = {};
    ENHANCED_SHARE_ACCESS_LOG_SEED_DATA.forEach((log) => {
      if (log.accessLocation) {
        locations[log.accessLocation] =
          (locations[log.accessLocation] || 0) + 1;
      }
    });
    return Object.entries(locations)
      .sort((a, b) => (b[1] as number) - (a[1] as number))
      .slice(0, 5)
      .map(([location, count]) => ({ location, count }));
  })(),
};
