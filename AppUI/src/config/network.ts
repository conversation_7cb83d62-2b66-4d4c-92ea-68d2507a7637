/**
 * 有故事 APP - 网络配置管理
 * 
 * 功能特性：
 * - 跨平台网络配置
 * - 开发/生产环境适配
 * - Android 模拟器网络处理
 */

import { Platform } from 'react-native';

// ===== 环境配置 =====
export const ENV = {
  DEVELOPMENT: 'development',
  PRODUCTION: 'production',
} as const;

// ===== 网络配置接口 =====
export interface NetworkConfig {
  baseURL: string;
  timeout: number;
  enableLogging: boolean;
}

/**
 * 获取当前环境
 */
export const getCurrentEnvironment = (): string => {
  return __DEV__ ? ENV.DEVELOPMENT : ENV.PRODUCTION;
};

/**
 * 获取API基础URL
 * 
 * Android 模拟器网络说明：
 * - localhost/127.0.0.1 -> 指向模拟器自身，无法访问宿主机
 * - ******** -> 模拟器访问宿主机的特殊IP
 * - 需要使用 adb reverse 进行端口转发
 */
export const getApiBaseURL = (): string => {
  const isDev = getCurrentEnvironment() === ENV.DEVELOPMENT;
  
  if (!isDev) {
    return 'https://your-production-api.com/api/v1';
  }

  // 开发环境配置
  switch (Platform.OS) {
    case 'android':
      // Android 模拟器使用特殊IP访问宿主机
      return 'http://********:3000/api/v1';
    
    case 'ios':
      // iOS 模拟器可以直接使用 localhost
      return 'http://localhost:3000/api/v1';
    
    default:
      return 'http://localhost:3000/api/v1';
  }
};

/**
 * 获取完整网络配置
 */
export const getNetworkConfig = (): NetworkConfig => {
  const isDev = getCurrentEnvironment() === ENV.DEVELOPMENT;
  
  return {
    baseURL: getApiBaseURL(),
    timeout: isDev ? 15000 : 10000, // 开发环境超时时间更长
    enableLogging: isDev, // 仅开发环境启用日志
  };
};

/**
 * 网络调试工具
 */
export const networkDebugger = {
  /**
   * 打印当前网络配置
   */
  printConfig(): void {
    if (__DEV__) {
      const config = getNetworkConfig();
      console.log('🌐 Network Configuration:');
      console.log(`  Platform: ${Platform.OS}`);
      console.log(`  Environment: ${getCurrentEnvironment()}`);
      console.log(`  Base URL: ${config.baseURL}`);
      console.log(`  Timeout: ${config.timeout}ms`);
      console.log(`  Logging: ${config.enableLogging ? 'Enabled' : 'Disabled'}`);
    }
  },

  /**
   * 测试网络连接
   */
  async testConnection(): Promise<boolean> {
    try {
      const config = getNetworkConfig();
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);
      
      const response = await fetch(`${config.baseURL.replace('/api/v1', '')}/health`, {
        method: 'GET',
        signal: controller.signal,
      });
      
      clearTimeout(timeoutId);
      
      const isConnected = response.ok;
      
      if (__DEV__) {
        console.log(`🔗 Network Test: ${isConnected ? '✅ Connected' : '❌ Failed'}`);
        if (!isConnected) {
          console.log(`   Status: ${response.status} ${response.statusText}`);
        }
      }
      
      return isConnected;
    } catch (error) {
      if (__DEV__) {
        console.log('🔗 Network Test: ❌ Connection Error');
        console.log('   Error:', error);
        console.log('💡 Troubleshooting Tips:');
        console.log('   1. 确保后端服务正在运行 (docker-compose up)');
        console.log('   2. 检查端口转发: adb reverse tcp:3000 tcp:3000');
        console.log('   3. 确认防火墙允许端口访问');
        console.log('   4. 尝试重启Metro: npx react-native start --reset-cache');
      }
      return false;
    }
  },
};

// ===== 默认导出 =====
export default {
  getApiBaseURL,
  getNetworkConfig,
  networkDebugger,
}; 