import {
  Injectable,
  NotFoundException,
  ForbiddenException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, Between } from "typeorm";
import { EntityUpdateHelper } from "../../common/helpers/entity-update.helper";
import type { EventType } from "./entities/timeline-event.entity";
import {
  TimelineEvent,
  EventStatus,
  EventVisibility,
  EventImportance,
} from "./entities/timeline-event.entity";
import { User } from "../users/entities/user.entity";
import { Story } from "../stories/entities/story.entity";
import type {
  CreateTimelineEventDto,
  TimelineQueryDto,
  UpdateTimelineEventDto,
  TimelineEventResponseDto,
  TimelineStatsResponseDto,
} from "./dto";
import type { PaginatedResponseDto } from "../../common/dto/paginated-response.dto";

@Injectable()
export class TimelineService {
  constructor(
    @InjectRepository(TimelineEvent)
    private readonly timelineEventRepository: Repository<TimelineEvent>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Story)
    private readonly storyRepository: Repository<Story>,
  ) {}

  // ==================== 时间线事件管理 ====================

  /**
   * 创建时间线事件
   */
  async createTimelineEvent(
    createEventDto: CreateTimelineEventDto,
    userId: string,
  ): Promise<TimelineEventResponseDto> {
    const {
      title,
      description,
      eventType,
      visibility = EventVisibility.PRIVATE,
      importance = EventImportance.MEDIUM,
      eventDate,
      endDate,
      location,
      relatedPeople,
      tags,
      relatedStoryId,
      images,
      attachments,
      color,
      icon,
      isMilestone = false,
      isPinned = false,
      customData,
      metadata,
    } = createEventDto;

    // 验证关联故事存在
    if (relatedStoryId) {
      const story = await this.storyRepository.findOne({
        where: { id: relatedStoryId, userId },
      });
      if (!story) {
        throw new NotFoundException("关联的故事不存在或无权限访问");
      }
    }

    // 创建时间线事件
    const timelineEvent = this.timelineEventRepository.create({
      userId,
      title,
      description,
      eventType,
      visibility,
      importance,
      eventDate: new Date(eventDate),
      endDate: endDate ? new Date(endDate) : undefined,
      location,
      relatedPeople: relatedPeople || [],
      tags: tags || [],
      relatedStoryId,
      images: images || [],
      attachments: attachments || [],
      color,
      icon,
      isMilestone,
      isPinned,
      customData: customData || {},
      metadata: metadata || {},
    });

    const savedEvent = await this.timelineEventRepository.save(timelineEvent);

    return this.formatEventResponse(savedEvent, userId);
  }

  /**
   * 获取用户的时间线事件列表
   */
  async getUserTimeline(
    targetUserId: string,
    queryDto: TimelineQueryDto,
    currentUserId?: string,
  ): Promise<PaginatedResponseDto<TimelineEventResponseDto>> {
    const {
      page = 1,
      limit = 20,
      eventType,
      status = EventStatus.ACTIVE,
      visibility,
      minImportance,
      tags,
      isMilestone,
      isPinned,
      startDate,
      endDate,
      search,
      sortBy = "eventDate",
      sortOrder = "DESC",
    } = queryDto;

    const queryBuilder = this.timelineEventRepository
      .createQueryBuilder("event")
      .leftJoinAndSelect("event.user", "user")
      .leftJoinAndSelect("event.relatedStory", "story")
      .where("event.userId = :userId", { userId: targetUserId })
      .andWhere("event.status = :status", { status });

    // 权限检查：只能看到公开的或自己的事件
    if (currentUserId !== targetUserId) {
      queryBuilder.andWhere("event.visibility = :publicVisibility", {
        publicVisibility: EventVisibility.PUBLIC,
      });
    }

    // 应用筛选条件
    if (eventType) {
      queryBuilder.andWhere("event.eventType = :eventType", { eventType });
    }

    if (visibility && currentUserId === targetUserId) {
      queryBuilder.andWhere("event.visibility = :visibility", { visibility });
    }

    if (minImportance) {
      queryBuilder.andWhere("event.importance >= :minImportance", {
        minImportance,
      });
    }

    if (tags && tags.length > 0) {
      queryBuilder.andWhere("event.tags && :tags", { tags });
    }

    if (isMilestone !== undefined) {
      queryBuilder.andWhere("event.isMilestone = :isMilestone", {
        isMilestone,
      });
    }

    if (isPinned !== undefined) {
      queryBuilder.andWhere("event.isPinned = :isPinned", { isPinned });
    }

    if (startDate) {
      queryBuilder.andWhere("event.eventDate >= :startDate", {
        startDate: new Date(startDate),
      });
    }

    if (endDate) {
      queryBuilder.andWhere("event.eventDate <= :endDate", {
        endDate: new Date(endDate),
      });
    }

    if (search) {
      queryBuilder.andWhere(
        "(event.title ILIKE :search OR event.description ILIKE :search)",
        { search: `%${search}%` },
      );
    }

    // 排序
    const validSortFields = [
      "eventDate",
      "createdAt",
      "updatedAt",
      "importance",
      "likesCount",
      "viewCount",
    ];
    const sortField = validSortFields.includes(sortBy) ? sortBy : "eventDate";
    queryBuilder.orderBy(`event.${sortField}`, sortOrder);

    // 置顶事件优先
    queryBuilder.addOrderBy("event.isPinned", "DESC");

    // 分页
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    const [events, total] = await queryBuilder.getManyAndCount();

    const formattedEvents = await Promise.all(
      events.map((event) => this.formatEventResponse(event, currentUserId)),
    );

    return {
      data: formattedEvents,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
      hasNext: page < Math.ceil(total / limit),
      hasPrev: page > 1,
    };
  }

  /**
   * 获取时间线事件详情
   */
  async getTimelineEventById(
    eventId: string,
    currentUserId?: string,
  ): Promise<TimelineEventResponseDto> {
    const event = await this.timelineEventRepository
      .createQueryBuilder("event")
      .leftJoinAndSelect("event.user", "user")
      .leftJoinAndSelect("event.relatedStory", "story")
      .where("event.id = :eventId", { eventId })
      .getOne();

    if (!event) {
      throw new NotFoundException("时间线事件不存在");
    }

    // 权限检查
    if (
      event.visibility === EventVisibility.PRIVATE &&
      event.userId !== currentUserId
    ) {
      throw new ForbiddenException("无权限查看该时间线事件");
    }

    // 更新浏览统计
    if (currentUserId && currentUserId !== event.userId) {
      await this.updateViewStats(eventId);
    }

    return this.formatEventResponse(event, currentUserId);
  }

  /**
   * 更新时间线事件
   */
  async updateTimelineEvent(
    eventId: string,
    updateEventDto: UpdateTimelineEventDto,
    userId: string,
  ): Promise<TimelineEventResponseDto> {
    const event = await this.timelineEventRepository.findOne({
      where: { id: eventId },
    });

    if (!event) {
      throw new NotFoundException("时间线事件不存在");
    }

    if (event.userId !== userId) {
      throw new ForbiddenException("无权限修改该时间线事件");
    }

    // 验证关联故事存在
    if (updateEventDto.relatedStoryId) {
      const story = await this.storyRepository.findOne({
        where: { id: updateEventDto.relatedStoryId, userId },
      });
      if (!story) {
        throw new NotFoundException("关联的故事不存在或无权限访问");
      }
    }

    // 企业级更新数据构建 - 使用链式构建器模式
    const updateData = EntityUpdateHelper.buildUpdateData<TimelineEvent>()
      .setIf(updateEventDto.title !== undefined, "title", updateEventDto.title!)
      .setIf(
        updateEventDto.description !== undefined,
        "description",
        updateEventDto.description!,
      )
      .setIf(
        updateEventDto.eventType !== undefined,
        "eventType",
        updateEventDto.eventType!,
      )
      .setIf(
        updateEventDto.visibility !== undefined,
        "visibility",
        updateEventDto.visibility!,
      )
      .setIf(
        updateEventDto.importance !== undefined,
        "importance",
        updateEventDto.importance!,
      )
      .setIf(
        updateEventDto.eventDate !== undefined,
        "eventDate",
        new Date(updateEventDto.eventDate!),
      )
      .setIf(
        updateEventDto.endDate !== undefined && updateEventDto.endDate !== null,
        "endDate",
        new Date(updateEventDto.endDate!),
      )
      .setIf(
        updateEventDto.endDate === null,
        "endDate",
        null as unknown as Date,
      )
      .setIf(
        updateEventDto.location !== undefined,
        "location",
        updateEventDto.location!,
      )
      .setIf(
        updateEventDto.relatedPeople !== undefined,
        "relatedPeople",
        updateEventDto.relatedPeople!,
      )
      .setIf(updateEventDto.tags !== undefined, "tags", updateEventDto.tags!)
      .setIf(
        updateEventDto.relatedStoryId !== undefined,
        "relatedStoryId",
        updateEventDto.relatedStoryId!,
      )
      .setIf(
        updateEventDto.images !== undefined,
        "images",
        updateEventDto.images!,
      )
      .setIf(
        updateEventDto.attachments !== undefined,
        "attachments",
        updateEventDto.attachments!,
      )
      .setIf(updateEventDto.color !== undefined, "color", updateEventDto.color!)
      .setIf(updateEventDto.icon !== undefined, "icon", updateEventDto.icon!)
      .setIf(
        updateEventDto.isMilestone !== undefined,
        "isMilestone",
        updateEventDto.isMilestone!,
      )
      .setIf(
        updateEventDto.isPinned !== undefined,
        "isPinned",
        updateEventDto.isPinned!,
      )
      .setIf(
        updateEventDto.customData !== undefined,
        "customData",
        updateEventDto.customData!,
      )
      .setIf(
        updateEventDto.metadata !== undefined,
        "metadata",
        updateEventDto.metadata!,
      )
      .build();

    // 企业级安全更新操作
    await EntityUpdateHelper.safeUpdate(
      this.timelineEventRepository,
      eventId,
      updateData,
    );

    const updatedEvent = await this.timelineEventRepository
      .createQueryBuilder("event")
      .leftJoinAndSelect("event.user", "user")
      .leftJoinAndSelect("event.relatedStory", "story")
      .where("event.id = :eventId", { eventId })
      .getOne();

    return this.formatEventResponse(updatedEvent!, userId);
  }

  /**
   * 删除时间线事件
   */
  async deleteTimelineEvent(eventId: string, userId: string): Promise<void> {
    const event = await this.timelineEventRepository.findOne({
      where: { id: eventId },
    });

    if (!event) {
      throw new NotFoundException("时间线事件不存在");
    }

    if (event.userId !== userId) {
      throw new ForbiddenException("无权限删除该时间线事件");
    }

    // 软删除
    await this.timelineEventRepository.update(eventId, {
      status: EventStatus.DELETED,
    });
  }

  /**
   * 获取时间线统计数据
   */
  async getTimelineStats(userId: string): Promise<TimelineStatsResponseDto> {
    const [
      totalEvents,
      milestoneEvents,
      publicEvents,
      privateEvents,
      eventsThisYear,
      eventsThisMonth,
    ] = await Promise.all([
      this.timelineEventRepository.count({
        where: { userId, status: EventStatus.ACTIVE },
      }),
      this.timelineEventRepository.count({
        where: { userId, status: EventStatus.ACTIVE, isMilestone: true },
      }),
      this.timelineEventRepository.count({
        where: {
          userId,
          status: EventStatus.ACTIVE,
          visibility: EventVisibility.PUBLIC,
        },
      }),
      this.timelineEventRepository.count({
        where: {
          userId,
          status: EventStatus.ACTIVE,
          visibility: EventVisibility.PRIVATE,
        },
      }),
      this.timelineEventRepository.count({
        where: {
          userId,
          status: EventStatus.ACTIVE,
          eventDate: Between(...this.getCurrentYearRange()),
        },
      }),
      this.timelineEventRepository.count({
        where: {
          userId,
          status: EventStatus.ACTIVE,
          eventDate: Between(...this.getCurrentMonthRange()),
        },
      }),
    ]);

    // 按类型统计
    const typeStats = await this.getEventTypeStats(userId);

    // 按重要性统计
    const importanceStats = await this.getImportanceStats(userId);

    // 热门标签
    const topTags = await this.getTopTags(userId);

    // 年度分布
    const yearlyDistribution = await this.getYearlyDistribution(userId);

    // 最近事件
    const recentEvents = await this.getRecentEvents(userId);

    // 即将到来的事件
    const upcomingEvents = await this.getUpcomingEvents(userId);

    // 计算总年数
    const oldestEvent = await this.timelineEventRepository
      .createQueryBuilder("event")
      .where("event.userId = :userId", { userId })
      .andWhere("event.status = :status", { status: EventStatus.ACTIVE })
      .orderBy("event.eventDate", "ASC")
      .getOne();

    const totalYears = oldestEvent
      ? new Date().getFullYear() -
        new Date(oldestEvent.eventDate).getFullYear() +
        1
      : 0;

    return {
      userId,
      totalEvents,
      milestoneEvents,
      publicEvents,
      privateEvents,
      totalYears,
      eventsThisYear,
      eventsThisMonth,
      typeStats,
      importanceStats,
      topTags,
      yearlyDistribution,
      recentEvents,
      upcomingEvents,
    };
  }

  // ==================== 私有辅助方法 ====================

  /**
   * 更新浏览统计
   */
  private async updateViewStats(eventId: string): Promise<void> {
    await this.timelineEventRepository.increment(
      { id: eventId },
      "viewCount",
      1,
    );
  }

  /**
   * 格式化事件响应
   */
  private async formatEventResponse(
    event: TimelineEvent,
    currentUserId?: string,
  ): Promise<TimelineEventResponseDto> {
    return {
      id: event.id,
      userId: event.userId,
      userName: event.user?.nickname || "未知用户",
      title: event.title,
      description: event.description,
      eventType: event.eventType,
      status: event.status,
      visibility: event.visibility,
      importance: event.importance,
      eventDate: event.eventDate,
      endDate: event.endDate,
      location: event.location,
      relatedPeople: event.relatedPeople,
      tags: event.tags,
      relatedStoryId: event.relatedStoryId,
      relatedStoryTitle: event.relatedStory?.title,
      images: event.images,
      attachments: event.attachments,
      color: event.color,
      icon: event.icon,
      isMilestone: event.isMilestone,
      isPinned: event.isPinned,
      likesCount: event.likesCount,
      commentsCount: event.commentsCount,
      viewCount: event.viewCount,
      customData: event.customData,
      metadata: event.metadata,
      createdAt: event.createdAt,
      updatedAt: event.updatedAt,
      canEdit: currentUserId === event.userId,
      canDelete: currentUserId === event.userId,
      canComment:
        event.visibility === EventVisibility.PUBLIC ||
        currentUserId === event.userId,
    };
  }

  // 统计相关的私有方法
  private getCurrentYearRange(): [Date, Date] {
    const now = new Date();
    const startOfYear = new Date(now.getFullYear(), 0, 1);
    const endOfYear = new Date(now.getFullYear(), 11, 31, 23, 59, 59);
    return [startOfYear, endOfYear];
  }

  private getCurrentMonthRange(): [Date, Date] {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(
      now.getFullYear(),
      now.getMonth() + 1,
      0,
      23,
      59,
      59,
    );
    return [startOfMonth, endOfMonth];
  }

  private async getEventTypeStats(userId: string): Promise<
    Array<{
      eventType: EventType;
      count: number;
      percentage: number;
    }>
  > {
    const stats = await this.timelineEventRepository
      .createQueryBuilder("event")
      .select("event.eventType", "eventType")
      .addSelect("COUNT(*)", "count")
      .where("event.userId = :userId", { userId })
      .andWhere("event.status = :status", { status: EventStatus.ACTIVE })
      .groupBy("event.eventType")
      .getRawMany();

    const totalEvents = stats.reduce(
      (sum, stat) => sum + parseInt(stat.count, 10),
      0,
    );

    return stats.map((stat) => ({
      eventType: stat.eventType,
      count: parseInt(stat.count, 10),
      percentage:
        totalEvents > 0 ? (parseInt(stat.count, 10) / totalEvents) * 100 : 0,
    }));
  }

  private async getImportanceStats(userId: string): Promise<
    Array<{
      importance: EventImportance;
      count: number;
      percentage: number;
    }>
  > {
    const stats = await this.timelineEventRepository
      .createQueryBuilder("event")
      .select("event.importance", "importance")
      .addSelect("COUNT(*)", "count")
      .where("event.userId = :userId", { userId })
      .andWhere("event.status = :status", { status: EventStatus.ACTIVE })
      .groupBy("event.importance")
      .getRawMany();

    const totalEvents = stats.reduce(
      (sum, stat) => sum + parseInt(stat.count, 10),
      0,
    );

    return stats.map((stat) => ({
      importance: stat.importance,
      count: parseInt(stat.count, 10),
      percentage:
        totalEvents > 0 ? (parseInt(stat.count, 10) / totalEvents) * 100 : 0,
    }));
  }

  private async getTopTags(userId: string): Promise<
    Array<{
      tag: string;
      count: number;
      recentUse: Date;
    }>
  > {
    const tags = await this.timelineEventRepository
      .createQueryBuilder("event")
      .select("UNNEST(event.tags)", "tag")
      .addSelect("COUNT(*)", "count")
      .addSelect("MAX(event.createdAt)", "recentUse")
      .where("event.userId = :userId", { userId })
      .andWhere("event.status = :status", { status: EventStatus.ACTIVE })
      .groupBy("tag")
      .orderBy("count", "DESC")
      .limit(10)
      .getRawMany();

    return tags.map((tag) => ({
      tag: tag.tag,
      count: parseInt(tag.count, 10),
      recentUse: tag.recentUse,
    }));
  }

  private async getYearlyDistribution(userId: string): Promise<
    Array<{
      year: number;
      count: number;
      milestones: number;
    }>
  > {
    const distribution = await this.timelineEventRepository
      .createQueryBuilder("event")
      .select("EXTRACT(YEAR FROM event.eventDate)", "year")
      .addSelect("COUNT(*)", "count")
      .addSelect(
        "SUM(CASE WHEN event.isMilestone THEN 1 ELSE 0 END)",
        "milestones",
      )
      .where("event.userId = :userId", { userId })
      .andWhere("event.status = :status", { status: EventStatus.ACTIVE })
      .groupBy("year")
      .orderBy("year", "DESC")
      .limit(10)
      .getRawMany();

    return distribution.map((item) => ({
      year: parseInt(item.year, 10),
      count: parseInt(item.count, 10),
      milestones: parseInt(item.milestones, 10),
    }));
  }

  private async getRecentEvents(userId: string): Promise<
    Array<{
      eventId: string;
      title: string;
      eventType: EventType;
      eventDate: Date;
      isMilestone: boolean;
    }>
  > {
    const events = await this.timelineEventRepository
      .createQueryBuilder("event")
      .where("event.userId = :userId", { userId })
      .andWhere("event.status = :status", { status: EventStatus.ACTIVE })
      .orderBy("event.createdAt", "DESC")
      .limit(5)
      .getMany();

    return events.map((event) => ({
      eventId: event.id,
      title: event.title,
      eventType: event.eventType,
      eventDate: event.eventDate,
      isMilestone: event.isMilestone,
    }));
  }

  private async getUpcomingEvents(userId: string): Promise<
    Array<{
      eventId: string;
      title: string;
      eventType: EventType;
      eventDate: Date;
      endDate?: Date;
    }>
  > {
    const now = new Date();
    const events = await this.timelineEventRepository
      .createQueryBuilder("event")
      .where("event.userId = :userId", { userId })
      .andWhere("event.status = :status", { status: EventStatus.ACTIVE })
      .andWhere("event.endDate IS NOT NULL")
      .andWhere("event.endDate > :now", { now })
      .orderBy("event.endDate", "ASC")
      .limit(5)
      .getMany();

    return events.map((event) => ({
      eventId: event.id,
      title: event.title,
      eventType: event.eventType,
      eventDate: event.eventDate,
      endDate: event.endDate,
    }));
  }
}
