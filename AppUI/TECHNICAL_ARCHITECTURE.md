# 有故事 APP - 前端技术架构文档

## 目录
- [项目概述](#项目概述)
- [技术栈](#技术栈)
- [项目结构](#项目结构)
- [核心模块](#核心模块)
- [网络配置管理](#网络配置管理)
- [状态管理](#状态管理)
- [导航系统](#导航系统)
- [UI组件体系](#ui组件体系)
- [开发工具](#开发工具)
- [安全措施](#安全措施)
- [性能优化](#性能优化)
- [测试策略](#测试策略)
- [构建部署](#构建部署)

---

## 项目概述

### 1.1 应用定位
有故事 APP 是一个企业级故事创作平台的移动端应用，为用户提供便捷的故事创作、分享和互动体验。

### 1.2 技术特点
- **企业级架构**: 基于React Native CLI，确保原生性能和可扩展性
- **类型安全**: 全面采用TypeScript，提供编译时类型检查
- **状态管理**: 使用Redux Toolkit，实现可预测的状态管理
- **网络优化**: 智能网络配置，支持跨平台自动适配

---

## 技术栈

### 2.1 核心框架
- **React Native**: `0.75.4` - 跨平台移动应用框架
- **TypeScript**: `5.0.4` - 类型安全的JavaScript超集
- **React**: `18.3.1` - 用户界面构建库

### 2.2 状态管理
- **Redux Toolkit**: `2.2.7` - 现代Redux状态管理
- **React Redux**: `9.1.2` - React与Redux连接库

### 2.3 导航路由
- **React Navigation**: `6.x` - 声明式导航库
  - `@react-navigation/native`
  - `@react-navigation/native-stack`
  - `@react-navigation/bottom-tabs`

### 2.4 网络通信
- **Axios**: `1.7.2` - HTTP客户端库
- **Flipper Network Plugin** - 网络请求调试

### 2.5 数据存储
- **AsyncStorage**: `1.24.0` - 异步本地存储
- **Keychain Services** - 敏感数据加密存储

### 2.6 开发工具
- **Metro**: React Native打包工具
- **Flipper**: 调试和性能分析
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化

---

## 项目结构

```
AppUI/
├── src/                          # 源码目录
│   ├── components/               # 共享组件
│   │   ├── common/              # 通用组件
│   │   ├── forms/               # 表单组件
│   │   └── ui/                  # UI基础组件
│   ├── screens/                 # 页面组件
│   │   ├── auth/                # 认证相关页面
│   │   ├── home/                # 首页相关页面
│   │   ├── story/               # 故事相关页面
│   │   ├── character/           # 角色相关页面
│   │   └── profile/             # 个人资料页面
│   ├── navigation/              # 导航配置
│   │   ├── RootNavigator.tsx    # 根导航器
│   │   └── types.ts             # 导航类型定义
│   ├── services/                # API服务层
│   │   ├── api.ts               # Axios实例配置
│   │   ├── authService.ts       # 认证服务
│   │   └── storyService.ts      # 故事服务
│   ├── store/                   # Redux状态管理
│   │   ├── index.ts             # Store配置
│   │   ├── slices/              # 状态切片
│   │   └── types.ts             # 状态类型定义
│   ├── config/                  # 配置文件
│   │   ├── network.ts           # 网络配置管理
│   │   └── constants.ts         # 应用常量
│   ├── utils/                   # 工具函数
│   ├── hooks/                   # 自定义Hooks
│   └── constants/               # 常量定义
├── scripts/                     # 开发脚本
│   ├── setup-android-network.sh # Android网络配置
│   └── check-network.sh        # 网络状态检查
├── android/                     # Android原生代码
├── ios/                         # iOS原生代码
└── TECHNICAL_ARCHITECTURE.md   # 技术架构文档
```

---

## 核心模块

### 4.1 认证模块 (`src/screens/auth/`)

#### AuthScreen.tsx
- 手机号登录/注册流程
- 短信验证码验证
- 用户状态管理
- 优雅的动画效果

```typescript
const AuthScreen: React.FC = () => {
  const [currentStep, setCurrentStep] = useState<AuthStep>('phone');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  
  // 发送验证码
  const sendVerificationCode = async () => {
    await authService.sendSms({ phone: phoneNumber });
  };
  
  // 验证并登录
  const verifyAndLogin = async () => {
    const response = await authService.loginWithPhone({
      phone: phoneNumber,
      code: verificationCode
    });
  };
};
```

#### SplashScreen.tsx
- 应用启动页面
- 认证状态检查
- 自动登录处理

### 4.2 主页模块 (`src/screens/home/<USER>

#### HomeScreen.tsx
- 故事列表展示
- 推荐算法集成
- 下拉刷新和上拉加载

### 4.3 故事模块 (`src/screens/story/`)

#### StoryScreen.tsx
- 故事详情展示
- 交互功能（点赞、评论）
- 分享功能

### 4.4 个人资料模块 (`src/screens/profile/`)

#### ProfileScreen.tsx
- 用户信息展示和编辑
- 设置功能
- 退出登录

---

## 网络配置管理

### 5.1 跨平台网络适配

**文件**: `src/config/network.ts`

提供企业级网络配置管理，支持：
- 开发/生产环境自动切换
- iOS/Android平台差异化配置
- 网络连接状态监控和诊断

```typescript
/**
 * 获取API基础URL
 * 
 * Android 模拟器网络说明：
 * - localhost/127.0.0.1 -> 指向模拟器自身，无法访问宿主机
 * - ******** -> 模拟器访问宿主机的特殊IP
 */
export const getApiBaseURL = (): string => {
  const isDev = getCurrentEnvironment() === ENV.DEVELOPMENT;
  
  if (!isDev) {
    return 'https://your-production-api.com/api/v1';
  }

  // 开发环境配置
  switch (Platform.OS) {
    case 'android':
      // Android 模拟器使用特殊IP访问宿主机
      return 'http://********:3000/api/v1';
    
    case 'ios':
      // iOS 模拟器可以直接使用 localhost
      return 'http://localhost:3000/api/v1';
    
    default:
      return 'http://localhost:3000/api/v1';
  }
};
```

### 5.2 自动化工具

#### Android网络配置脚本
**文件**: `scripts/setup-android-network.sh`

功能特性：
- 自动检测ADB和设备连接
- 设置端口转发 (8081, 3000)
- 测试网络连接状态
- 提供问题排查指引

```bash
# 设置端口转发
adb reverse tcp:8081 tcp:8081  # Metro bundler
adb reverse tcp:3000 tcp:3000  # API服务
```

#### 网络状态检查脚本
**文件**: `scripts/check-network.sh`

快速诊断功能：
- 后端服务状态检查
- Metro bundler状态检查
- Android端口转发验证
- API连接测试

### 5.3 网络调试工具

**调试器**: `networkDebugger`

开发环境功能：
- 实时网络配置信息打印
- 自动连接状态测试
- 故障排除建议
- 性能监控集成

```typescript
// 应用启动时的网络诊断
useEffect(() => {
  if (__DEV__) {
    networkDebugger.testConnection().catch(error => {
      console.log('🔗 Initial network test failed:', error);
    });
  }
}, []);
```

---

## 状态管理

### 6.1 Redux Toolkit架构

**Store配置**: `src/store/index.ts`

```typescript
export const store = configureStore({
  reducer: {
    auth: authSlice.reducer,
    user: userSlice.reducer,
    story: storySlice.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
      },
    }),
});
```

### 6.2 状态切片设计

#### Auth状态切片
```typescript
interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  tokens: {
    accessToken: string | null;
    refreshToken: string | null;
  };
  isLoading: boolean;
}
```

#### User状态切片
```typescript
interface UserState {
  profile: UserProfile | null;
  preferences: UserPreferences;
  isLoading: boolean;
}
```

#### Character状态切片
```typescript
interface CharacterState {
  characters: Character[];
  selectedCharacter: Character | null;
  lightingRequests: LightingRequest[];
  isLoading: boolean;
  validationErrors: {
    duplicateName: boolean;
    lightingConstraint: boolean;
  };
}

interface Character {
  id: string;
  name: string;
  description?: string;
  isLighted: boolean;
  ownerId: string;
  storyId: string;
  createdAt: string;
}

interface LightingRequest {
  id: string;
  characterId: string;
  storyAuthorId: string;
  status: 'pending' | 'confirmed' | 'rejected' | 'expired';
  phoneNumber: string;
  expiresAt: string;
  createdAt: string;
}
```

### 6.3 异步操作处理

使用`createAsyncThunk`处理异步操作：

#### 基础认证操作
```typescript
export const loginWithPhone = createAsyncThunk(
  'auth/loginWithPhone',
  async (request: LoginWithPhoneRequest) => {
    const response = await authService.loginWithPhone(request);
    return response;
  }
);
```

#### 人物管理操作
```typescript
// 创建人物（包含同名校验）
export const createCharacter = createAsyncThunk(
  'character/create',
  async (request: CreateCharacterRequest, { rejectWithValue }) => {
    try {
      const response = await characterService.createCharacter(request);
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 409) {
        // 同名冲突错误
        return rejectWithValue({
          type: 'DUPLICATE_NAME',
          message: '您已创建过同名人物，请使用不同的名称'
        });
      }
      return rejectWithValue({
        type: 'UNKNOWN_ERROR',
        message: error.message
      });
    }
  }
);

// 申请点亮人物（包含一用户一点亮限制校验）
export const requestLighting = createAsyncThunk(
  'character/requestLighting',
  async (request: LightingRequest, { rejectWithValue }) => {
    try {
      const response = await characterService.requestLighting(request);
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 409) {
        return rejectWithValue({
          type: 'LIGHTING_CONSTRAINT',
          message: '您已点亮过该用户的其他人物，每个用户只能点亮一个人物'
        });
      }
      return rejectWithValue({
        type: 'UNKNOWN_ERROR',
        message: error.message
      });
    }
  }
);

// 实时验证人物名称
export const validateCharacterName = createAsyncThunk(
  'character/validateName',
  async (request: { name: string; ownerId: string }) => {
    const response = await characterService.validateName(request);
    return response.data;
  }
);
```

#### 业务规则处理Reducers
```typescript
const characterSlice = createSlice({
  name: 'character',
  initialState,
  reducers: {
    clearValidationErrors: (state) => {
      state.validationErrors.duplicateName = false;
      state.validationErrors.lightingConstraint = false;
    },
    setDuplicateNameError: (state, action) => {
      state.validationErrors.duplicateName = action.payload;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(createCharacter.rejected, (state, action) => {
        if (action.payload?.type === 'DUPLICATE_NAME') {
          state.validationErrors.duplicateName = true;
        }
      })
      .addCase(requestLighting.rejected, (state, action) => {
        if (action.payload?.type === 'LIGHTING_CONSTRAINT') {
          state.validationErrors.lightingConstraint = true;
        }
      })
      .addCase(createCharacter.fulfilled, (state, action) => {
        state.characters.push(action.payload);
        state.validationErrors.duplicateName = false;
      });
  }
});
```

---

## 导航系统

### 7.1 导航架构

**根导航器**: `src/navigation/RootNavigator.tsx`

```typescript
const RootNavigator: React.FC = () => {
  const { isAuthenticated } = useSelector((state: RootState) => state.auth);
  
  return (
    <NavigationContainer>
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        {isAuthenticated ? (
          <Stack.Screen name="Main" component={MainTabNavigator} />
        ) : (
          <>
            <Stack.Screen name="Splash" component={SplashScreen} />
            <Stack.Screen name="Auth" component={AuthScreen} />
          </>
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
};
```

### 7.2 底部标签导航

**主标签导航**: `src/screens/MainTabNavigator.tsx`

```typescript
const MainTabNavigator: React.FC = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          // 图标配置
        },
      })}
    >
      <Tab.Screen name="Home" component={HomeScreen} />
      <Tab.Screen name="Story" component={StoryScreen} />
      <Tab.Screen name="Message" component={MessageScreen} />
      <Tab.Screen name="Profile" component={ProfileScreen} />
    </Tab.Navigator>
  );
};
```

### 7.3 类型安全导航

**导航类型定义**: `src/navigation/types.ts`

```typescript
export type RootStackParamList = {
  Splash: undefined;
  Auth: undefined;
  Main: undefined;
};

export type MainTabParamList = {
  Home: undefined;
  Story: { storyId?: string };
  Message: undefined;
  Profile: undefined;
};
```

---

## UI组件体系

### 8.1 设计系统

**主题配置**: `src/constants/theme.ts`

```typescript
export const THEME = {
  colors: {
    primary: '#2C3E50',
    secondary: '#E8F4FD',
    background: '#FFFFFF',
    text: {
      primary: '#2C3E50',
      secondary: '#7F8C8D',
      placeholder: '#BDC3C7',
    },
  },
  typography: {
    h1: { fontSize: 28, fontWeight: '600' },
    h2: { fontSize: 24, fontWeight: '600' },
    body: { fontSize: 16, fontWeight: '400' },
  },
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
  },
};
```

### 8.2 通用组件

#### LoadingButton组件
```typescript
interface LoadingButtonProps {
  title: string;
  onPress: () => void;
  loading?: boolean;
  disabled?: boolean;
  variant?: 'primary' | 'secondary';
}

const LoadingButton: React.FC<LoadingButtonProps> = ({
  title,
  onPress,
  loading = false,
  disabled = false,
  variant = 'primary',
}) => {
  // 组件实现
};
```

### 8.3 表单组件

#### CustomTextInput组件
```typescript
interface CustomTextInputProps {
  label: string;
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  secureTextEntry?: boolean;
  keyboardType?: KeyboardTypeOptions;
}
```

---

## 开发工具

### 9.1 自定义Hooks

#### useAuth Hook
```typescript
export const useAuth = () => {
  const dispatch = useDispatch();
  const { isAuthenticated, user, isLoading } = useSelector(
    (state: RootState) => state.auth
  );
  
  const login = useCallback(async (request: LoginWithPhoneRequest) => {
    await dispatch(loginWithPhone(request));
  }, [dispatch]);
  
  const logout = useCallback(() => {
    dispatch(authSlice.actions.logout());
  }, [dispatch]);
  
  return {
    isAuthenticated,
    user,
    isLoading,
    login,
    logout,
  };
};
```

#### useNetwork Hook
```typescript
export const useNetwork = () => {
  const [isConnected, setIsConnected] = useState(true);
  
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      setIsConnected(state.isConnected ?? false);
    });
    
    return unsubscribe;
  }, []);
  
  return { isConnected };
};
```

### 9.2 工具函数

**工具函数集合**: `src/utils/`

```typescript
// 表单验证
export const validatePhoneNumber = (phone: string): boolean => {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
};

// 日期格式化
export const formatDate = (date: Date, format: string): string => {
  // 实现日期格式化
};

// 防抖函数
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  // 实现防抖逻辑
};
```

---

## 安全措施

### 10.1 数据安全

#### Token管理
```typescript
export const tokenManager = {
  async getToken(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(TOKEN_KEY);
    } catch (error) {
      console.error('获取 Token 失败:', error);
      return null;
    }
  },

  async setTokens(accessToken: string, refreshToken: string): Promise<void> {
    try {
      await AsyncStorage.multiSet([
        [TOKEN_KEY, accessToken],
        [REFRESH_TOKEN_KEY, refreshToken],
      ]);
    } catch (error) {
      console.error('保存 Token 失败:', error);
    }
  },
};
```

#### 敏感数据加密
- 使用Keychain Services存储敏感信息
- Token自动刷新机制
- 安全的网络传输(HTTPS)

### 10.2 API安全

#### 请求拦截器
```typescript
// 自动添加认证头
instance.interceptors.request.use(
  async (config: any) => {
    const token = await AsyncStorage.getItem(TOKEN_KEY);
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  }
);
```

#### 响应拦截器
```typescript
// 自动处理Token过期
instance.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      // 自动刷新Token或跳转登录
    }
    return Promise.reject(error);
  }
);
```

---

## 性能优化

### 11.1 代码分割

#### 懒加载组件
```typescript
const LazyStoryScreen = React.lazy(() => import('../screens/story/StoryScreen'));

const StoryScreenWrapper: React.FC = () => (
  <Suspense fallback={<LoadingSpinner />}>
    <LazyStoryScreen />
  </Suspense>
);
```

### 11.2 内存管理

#### 图片优化
- 使用Fast Image库
- 图片懒加载
- 内存缓存管理

#### 列表优化
```typescript
const StoryList: React.FC = () => {
  const renderItem = useCallback(({ item }: { item: Story }) => (
    <StoryCard story={item} />
  ), []);
  
  return (
    <FlatList
      data={stories}
      renderItem={renderItem}
      getItemLayout={(data, index) => ({
        length: ITEM_HEIGHT,
        offset: ITEM_HEIGHT * index,
        index,
      })}
      removeClippedSubviews={true}
      maxToRenderPerBatch={10}
    />
  );
};
```

### 11.3 网络优化

#### 请求缓存
```typescript
const apiCache = new Map<string, { data: any; timestamp: number }>();

const getCachedData = (key: string, maxAge: number = 300000) => {
  const cached = apiCache.get(key);
  if (cached && Date.now() - cached.timestamp < maxAge) {
    return cached.data;
  }
  return null;
};
```

---

## 测试策略

### 12.1 单元测试

#### Jest配置
```javascript
module.exports = {
  preset: 'react-native',
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.ts'],
  testMatch: [
    '**/__tests__/**/*.test.{js,jsx,ts,tsx}',
  ],
};
```

#### 组件测试示例
```typescript
describe('AuthScreen', () => {
  it('应该正确渲染手机号输入界面', () => {
    const { getByPlaceholderText } = render(<AuthScreen />);
    expect(getByPlaceholderText('请输入手机号')).toBeTruthy();
  });
  
  it('应该验证手机号格式', () => {
    const isValid = validatePhoneNumber('13800138000');
    expect(isValid).toBe(true);
  });
});
```

### 12.2 集成测试

#### API测试
```typescript
describe('AuthService', () => {
  it('应该成功发送短信验证码', async () => {
    const response = await authService.sendSms({ phone: '13800138000' });
    expect(response.code).toBe(200);
  });
});
```

### 12.3 E2E测试

使用Detox进行端到端测试：

```typescript
describe('登录流程', () => {
  it('应该完成完整的登录流程', async () => {
    await element(by.id('phoneInput')).typeText('13800138000');
    await element(by.id('sendCodeButton')).tap();
    await element(by.id('codeInput')).typeText('123456');
    await element(by.id('loginButton')).tap();
    await expect(element(by.id('homeScreen'))).toBeVisible();
  });
});
```

---

## 构建部署

### 13.1 开发环境

#### Metro配置
```javascript
module.exports = {
  transformer: {
    getTransformOptions: async () => ({
      transform: {
        experimentalImportSupport: false,
        inlineRequires: true,
      },
    }),
  },
};
```

#### 调试配置
- Flipper集成
- 网络请求监控
- Redux DevTools

### 13.2 生产构建

#### Android构建
```bash
# 生成签名APK
cd android
./gradlew assembleRelease

# 生成AAB包
./gradlew bundleRelease
```

#### iOS构建
```bash
# 归档构建
xcodebuild -workspace ios/YGSApp.xcworkspace \
  -scheme YGSApp \
  -configuration Release \
  -destination generic/platform=iOS \
  archive -archivePath YGSApp.xcarchive
```

### 13.3 持续集成

#### GitHub Actions配置
```yaml
name: CI/CD Pipeline
on:
  push:
    branches: [main, develop]
    
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18
      - name: Install dependencies
        run: npm ci
      - name: Run tests
        run: npm test
```

---

## 脚本管理

### 14.1 脚本工具链

#### 核心开发脚本
项目在 `scripts/` 目录下统一管理所有开发脚本，遵循企业级开发规范：

```bash
scripts/
├── README.md                    # 脚本使用说明文档
├── dev-start.sh                 # 智能开发启动脚本
├── check-network.sh             # 快速网络状态检查
├── setup-android-network.sh     # Android 网络配置
├── clean-build.sh               # 构建清理脚本
├── fast-android.sh              # Android 快速构建
└── aliases.sh                   # 开发命令别名配置
```

#### 脚本功能特性
- ✅ **智能诊断**: 自动检测环境和网络状态
- ✅ **一键配置**: 自动配置端口转发和网络连接
- ✅ **错误处理**: 完善的错误提示和解决方案
- ✅ **颜色输出**: 标准化的颜色编码提高可读性
- ✅ **别名支持**: 快捷命令提高开发效率

#### 推荐的命令别名
```bash
alias ygs-start='cd /path/to/AppUI && ./scripts/dev-start.sh'
alias ygs-check='cd /path/to/AppUI && ./scripts/check-network.sh'
alias ygs-clean='cd /path/to/AppUI && ./scripts/clean-build.sh'
alias ygs-android-fast='cd /path/to/AppUI && ./scripts/fast-android.sh'
```

### 14.2 开发工作流程

#### 日常开发启动
```bash
# 1. 启动开发环境（推荐）
ygs-start

# 2. 检查网络状态
ygs-check

# 3. 清理构建缓存（如需要）
ygs-clean

# 4. 快速Android构建
ygs-android-fast
```

#### 故障排查流程
1. **网络问题**: 运行 `ygs-check` 进行快速诊断
2. **构建问题**: 运行 `ygs-clean` 清理缓存后重试
3. **端口冲突**: 运行 `ygs-start --force` 重新配置
4. **ADB问题**: 运行 `./scripts/setup-android-network.sh` 重新配置

### 14.3 脚本开发规范

#### 标准化格式
```bash
#!/bin/bash

# ===========================================
# 有故事 APP - [脚本名称和用途]
# ===========================================

# 颜色定义
RED='\033[0;31m'      # 错误信息
GREEN='\033[0;32m'    # 成功信息
YELLOW='\033[1;33m'   # 警告信息
BLUE='\033[0;34m'     # 提示信息
NC='\033[0m'          # 无颜色

# 脚本逻辑...
```

#### 输出标准
- ✅ 成功操作
- ❌ 失败操作
- ⚠️  警告信息
- 💡 提示信息
- 🔧 配置操作

---

## 总结

本技术架构文档详细描述了有故事APP前端的技术实现方案，涵盖了从项目结构到部署流程的各个方面。主要特点：

### 企业级特性
- ✅ **类型安全**: 全面TypeScript支持
- ✅ **模块化设计**: 清晰的代码组织结构
- ✅ **性能优化**: 多层次性能优化策略
- ✅ **安全保障**: 完善的安全措施
- ✅ **网络管理**: 智能网络配置和调试工具
- ✅ **脚本工具链**: 标准化的开发脚本管理

### 开发效率
- ✅ **自动化工具**: 完整的开发脚本工具链
- ✅ **调试友好**: 丰富的调试和诊断功能
- ✅ **代码质量**: 标准化的代码规范和测试策略
- ✅ **文档完善**: 详细的技术文档和注释
- ✅ **快捷命令**: 便利的命令别名系统

### 可维护性
- ✅ **架构清晰**: 模块化和组件化设计
- ✅ **扩展性强**: 支持功能扩展和技术栈升级
- ✅ **标准化**: 统一的开发规范和最佳实践
- ✅ **脚本规范**: 标准化的脚本开发和维护流程

这套架构为有故事APP的长期发展提供了坚实的技术基础，确保应用能够在企业级要求下稳定运行并持续发展。
