import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../errors/exceptions.dart';
import '../utils/logger.dart';
import 'api_result.dart';
import 'dio_client.dart';

/// API服务基类
abstract class BaseApiService {
  late final Dio _dio;

  BaseApiService({Dio? dio}) {
    _dio = dio ?? DioClient.instance;
  }

  /// GET请求
  Future<ApiResult<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    T Function(dynamic)? parser,
  }) async {
    try {
      AppLogger.d('GET请求: $path', error: queryParameters);
      
      final response = await _dio.get(
        path,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );

      return _handleResponse<T>(response, parser);
    } on DioException catch (e) {
      AppLogger.e('GET请求失败: $path', error: e);
      throw DioClient.handleDioError(e);
    } catch (e) {
      AppLogger.e('GET请求异常: $path', error: e);
      throw UnknownException(
        message: e.toString(),
        code: 'get_request_error',
      );
    }
  }

  /// POST请求
  Future<ApiResult<T>> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    T Function(dynamic)? parser,
  }) async {
    try {
      AppLogger.d('POST请求: $path', data);
      
      final response = await _dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );

      return _handleResponse<T>(response, parser);
    } on DioException catch (e) {
      AppLogger.e('POST请求失败: $path', e);
      throw DioClient.handleDioError(e);
    } catch (e) {
      AppLogger.e('POST请求异常: $path', e);
      throw UnknownException(
        message: e.toString(),
        code: 'post_request_error',
      );
    }
  }

  /// PUT请求
  Future<ApiResult<T>> put<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    T Function(dynamic)? parser,
  }) async {
    try {
      AppLogger.d('PUT请求: $path', data);
      
      final response = await _dio.put(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );

      return _handleResponse<T>(response, parser);
    } on DioException catch (e) {
      AppLogger.e('PUT请求失败: $path', e);
      throw DioClient.handleDioError(e);
    } catch (e) {
      AppLogger.e('PUT请求异常: $path', e);
      throw UnknownException(
        message: e.toString(),
        code: 'put_request_error',
      );
    }
  }

  /// DELETE请求
  Future<ApiResult<T>> delete<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    T Function(dynamic)? parser,
  }) async {
    try {
      AppLogger.d('DELETE请求: $path', data);
      
      final response = await _dio.delete(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );

      return _handleResponse<T>(response, parser);
    } on DioException catch (e) {
      AppLogger.e('DELETE请求失败: $path', e);
      throw DioClient.handleDioError(e);
    } catch (e) {
      AppLogger.e('DELETE请求异常: $path', e);
      throw UnknownException(
        message: e.toString(),
        code: 'delete_request_error',
      );
    }
  }

  /// PATCH请求
  Future<ApiResult<T>> patch<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    T Function(dynamic)? parser,
  }) async {
    try {
      AppLogger.d('PATCH请求: $path', data);
      
      final response = await _dio.patch(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );

      return _handleResponse<T>(response, parser);
    } on DioException catch (e) {
      AppLogger.e('PATCH请求失败: $path', e);
      throw DioClient.handleDioError(e);
    } catch (e) {
      AppLogger.e('PATCH请求异常: $path', e);
      throw UnknownException(
        message: e.toString(),
        code: 'patch_request_error',
      );
    }
  }

  /// 上传文件
  Future<ApiResult<T>> upload<T>(
    String path, {
    required File file,
    String? fieldName = 'file',
    Map<String, dynamic>? fields,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    T Function(dynamic)? parser,
  }) async {
    try {
      AppLogger.d('上传文件: $path', {'fileName': file.path});
      
      final fileName = file.path.split('/').last;
      final formData = FormData.fromMap({
        if (fields != null) ...fields,
        fieldName!: await MultipartFile.fromFile(
          file.path,
          filename: fileName,
        ),
      });

      final response = await _dio.post(
        path,
        data: formData,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
      );

      return _handleResponse<T>(response, parser);
    } on DioException catch (e) {
      AppLogger.e('文件上传失败: $path', e);
      throw DioClient.handleDioError(e);
    } catch (e) {
      AppLogger.e('文件上传异常: $path', e);
      throw UnknownException(
        message: e.toString(),
        code: 'upload_error',
      );
    }
  }

  /// 处理响应数据
  ApiResult<T> _handleResponse<T>(
    Response response,
    T Function(dynamic)? parser,
  ) {
    final data = response.data;
    
    // 直接返回数据的情况
    if (data == null) {
      return ApiResult<T>(
        success: true,
        code: 0,
        message: 'success',
        data: null,
        timestamp: DateTime.now(),
      );
    }

    // 标准API响应格式
    if (data is Map<String, dynamic>) {
      final success = data['success'] ?? true;
      final code = data['statusCode'] ?? data['code'] ?? 0;
      final message = data['message'] ?? '';
      final responseData = data['data'];
      final timestamp = data['timestamp'];

      // 业务错误处理
      if (!success || code != 0) {
        throw BusinessException(
          message: message,
          code: code.toString(),
          data: responseData,
        );
      }

      return ApiResult<T>(
        success: success,
        code: code,
        message: message,
        data: parser != null && responseData != null
            ? parser(responseData)
            : responseData,
        timestamp: timestamp != null
            ? DateTime.parse(timestamp)
            : DateTime.now(),
      );
    }

    // 非标准格式，直接解析
    return ApiResult<T>(
      success: true,
      code: 0,
      message: 'success',
      data: parser != null ? parser(data) : data,
      timestamp: DateTime.now(),
    );
  }
}

/// BaseApiService的Provider
final baseApiServiceProvider = Provider<BaseApiService>((ref) {
  throw UnimplementedError('请使用具体的API服务Provider');
});