/**
 * 故事引用模块 - 企业级单元测试
 */

import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import { StoryReferencesModule } from "./story-references.module";
import { StoryReferencesService } from "./story-references.service";
import { StoryReferencesController } from "./story-references.controller";
import { StoryReference } from "./entities/story-reference.entity";
import { ReferenceCollection } from "./entities/reference-collection.entity";
import { User } from "../users/entities/user.entity";
import { Story } from "../stories/entities/story.entity";

describe("StoryReferencesModule - 企业级模块测试", () => {
  let module: TestingModule;

  // Mock Repository工厂
  const createMockRepository = () => ({
    find: jest.fn(),
    findOne: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    createQueryBuilder: jest.fn(() => ({
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      getMany: jest.fn(),
      getOne: jest.fn(),
    })),
  });

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [StoryReferencesModule],
    })
      .overrideProvider(getRepositoryToken(StoryReference))
      .useValue(createMockRepository())
      .overrideProvider(getRepositoryToken(ReferenceCollection))
      .useValue(createMockRepository())
      .overrideProvider(getRepositoryToken(User))
      .useValue(createMockRepository())
      .overrideProvider(getRepositoryToken(Story))
      .useValue(createMockRepository())
      .compile();
  });

  afterEach(async () => {
    if (module) {
      await module.close();
    }
  });

  describe("模块初始化", () => {
    it("should compile the module", () => {
      expect(module).toBeDefined();
    });

    it("should provide StoryReferencesService", () => {
      const service = module.get<StoryReferencesService>(
        StoryReferencesService,
      );
      expect(service).toBeDefined();
    });

    it("should provide StoryReferencesController", () => {
      const controller = module.get<StoryReferencesController>(
        StoryReferencesController,
      );
      expect(controller).toBeDefined();
    });
  });

  describe("依赖注入", () => {
    it("should inject StoryReferencesService into StoryReferencesController", () => {
      const controller = module.get<StoryReferencesController>(
        StoryReferencesController,
      );
      const service = module.get<StoryReferencesService>(
        StoryReferencesService,
      );

      expect(controller).toBeDefined();
      expect(service).toBeDefined();
    });

    it("should inject all required repositories", () => {
      const service = module.get<StoryReferencesService>(
        StoryReferencesService,
      );
      expect(service).toBeDefined();

      // 验证所有实体的Repository都可以获取
      const repositories = [
        getRepositoryToken(StoryReference),
        getRepositoryToken(ReferenceCollection),
        getRepositoryToken(User),
        getRepositoryToken(Story),
      ];

      repositories.forEach((token) => {
        const repository = module.get(token);
        expect(repository).toBeDefined();
      });
    });
  });

  describe("模块配置", () => {
    it("should be a valid NestJS module", () => {
      expect(StoryReferencesModule).toBeDefined();
      expect(typeof StoryReferencesModule).toBe("function");
    });

    it("should export StoryReferencesService", () => {
      const service = module.get<StoryReferencesService>(
        StoryReferencesService,
      );
      expect(service).toBeDefined();
    });
  });

  describe("TypeORM集成", () => {
    it("should configure TypeORM with all required entities", () => {
      // 验证所有实体都已注册
      const entities = [StoryReference, ReferenceCollection, User, Story];

      entities.forEach((entity) => {
        expect(entity).toBeDefined();
        expect(typeof entity).toBe("function");
      });
    });
  });

  describe("业务逻辑验证", () => {
    it("should handle story reference operations", () => {
      const service = module.get<StoryReferencesService>(
        StoryReferencesService,
      );
      expect(service).toBeDefined();
      // 故事引用相关的业务逻辑验证
    });

    it("should handle reference collection operations", () => {
      const service = module.get<StoryReferencesService>(
        StoryReferencesService,
      );
      expect(service).toBeDefined();
      // 引用集合相关的业务逻辑验证
    });
  });
});
