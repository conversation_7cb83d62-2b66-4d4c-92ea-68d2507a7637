/* eslint-disable @typescript-eslint/no-explicit-any, @typescript-eslint/no-unused-vars */
import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { HttpStatus } from "@nestjs/common";
import type { Request } from "express";

import { SocialController } from "./social.controller";
import { SocialService } from "./social.service";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { FollowStatus } from "./entities/user-follow.entity";
import { GroupType, GroupVisibility } from "./entities/friend-group.entity";
import {
  MemberRole,
  MemberStatus,
} from "./entities/friend-group-member.entity";
import type {
  FollowUserDto,
  CreateFriendGroupDto,
  FollowListQueryDto,
  FriendGroupQueryDto,
  GroupMemberQueryDto,
  FollowResponseDto,
  UserBriefDto,
  FriendGroupResponseDto,
  GroupMemberResponseDto,
  SocialStatsResponseDto,
} from "./dto";

describe("SocialController - 企业级单元测试", () => {
  let controller: SocialController;
  let mockSocialService: jest.Mocked<SocialService>;

  // 测试数据工厂
  const mockUser = { id: "user-001", nickname: "测试用户" };
  const mockRequest = {
    user: mockUser,
  } as unknown as Request & { user: { id: string } };

  const mockFollowResponse: FollowResponseDto = {
    id: "follow-001",
    followerId: "user-001",
    followingId: "user-002",
    followerName: "测试用户",
    followerAvatar: "https://example.com/avatar1.jpg",
    followingName: "被关注用户",
    followingAvatar: "https://example.com/avatar2.jpg",
    status: FollowStatus.ACTIVE,
    isSpecial: false,
    settings: {
      notifyOnPost: true,
      notifyOnComment: false,
      showInTimeline: true,
    },
    metadata: { source: "web" },
    createdAt: new Date(),
  };

  const mockUserBrief: UserBriefDto = {
    id: "user-002",
    username: "followeduser",
    name: "被关注用户",
    avatar: "https://example.com/avatar2.jpg",
    bio: "这是一个测试用户",
    followersCount: 100,
    followingCount: 50,
    storiesCount: 25,
    isFollowing: true,
    isFollowingMe: false,
    isMutualFollow: false,
    followedAt: new Date(),
  };

  const mockPaginatedUserResponse = {
    data: [mockUserBrief],
    total: 1,
    page: 1,
    limit: 10,
    totalPages: 1,
    hasNext: false,
    hasPrev: false,
  };

  const mockFriendGroupResponse: FriendGroupResponseDto = {
    id: "group-001",
    name: "好友分组",
    description: "我的好朋友们",
    ownerId: "user-001",
    ownerName: "测试用户",
    groupType: GroupType.FRIENDS,
    visibility: GroupVisibility.PRIVATE,
    colorTag: "#FF5733",
    memberCount: 5,
    sortOrder: 1,
    isActive: true,
    permissions: {
      canViewProfile: true,
      canViewStories: true,
      canComment: true,
      canShare: false,
      canMessage: true,
    },
    metadata: { created_from: "mobile" },
    createdAt: new Date(),
    updatedAt: new Date(),
    myMemberStatus: MemberStatus.ACTIVE,
    myRole: MemberRole.MEMBER,
  };

  const mockPaginatedGroupResponse = {
    data: [mockFriendGroupResponse],
    total: 1,
    page: 1,
    limit: 10,
    totalPages: 1,
    hasNext: false,
    hasPrev: false,
  };

  const mockGroupMemberResponse: GroupMemberResponseDto = {
    id: "member-001",
    groupId: "group-001",
    groupName: "好友分组",
    userId: "user-002",
    userName: "被关注用户",
    userAvatar: "https://example.com/avatar2.jpg",
    role: MemberRole.MEMBER,
    status: MemberStatus.ACTIVE,
    addedBy: "user-001",
    addedByName: "测试用户",
    note: "通过共同好友添加",
    permissionOverrides: {
      canViewProfile: true,
    },
    metadata: { added_method: "manual" },
    addedAt: new Date(),
  };

  const mockPaginatedMemberResponse = {
    data: [mockGroupMemberResponse],
    total: 1,
    page: 1,
    limit: 10,
    totalPages: 1,
    hasNext: false,
    hasPrev: false,
  };

  const mockSocialStatsResponse: SocialStatsResponseDto = {
    userId: "user-001",
    followingCount: 150,
    followersCount: 200,
    mutualFollowsCount: 50,
    specialFollowsCount: 20,
    friendGroupsCount: 5,
    groupMembershipsCount: 8,
    newFollowersToday: 3,
    newFollowingToday: 1,
    topGroups: [
      {
        groupId: "group-001",
        groupName: "最佳朋友",
        memberCount: 25,
        groupType: GroupType.FRIENDS,
      },
      {
        groupId: "group-002",
        groupName: "同事",
        memberCount: 15,
        groupType: GroupType.COLLEAGUES,
      },
    ],
    topInteractors: [
      {
        userId: "user-003",
        userName: "活跃用户",
        userAvatar: "https://example.com/avatar3.jpg",
        interactionScore: 95,
        lastInteractionAt: new Date(),
      },
    ],
  };

  beforeEach(async () => {
    const mockService = {
      followUser: jest.fn(),
      unfollowUser: jest.fn(),
      getFollowingList: jest.fn(),
      getFollowersList: jest.fn(),
      createFriendGroup: jest.fn(),
      getFriendGroups: jest.fn(),
      getGroupMembers: jest.fn(),
      addMembersToGroup: jest.fn(),
      getSocialStats: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [SocialController],
      providers: [
        {
          provide: SocialService,
          useValue: mockService,
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({
        canActivate: jest.fn().mockReturnValue(true),
      })
      .compile();

    controller = module.get<SocialController>(SocialController);
    mockSocialService = module.get(SocialService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("followUser - POST /social/follow", () => {
    const followUserDto: FollowUserDto = {
      userId: "user-002",
      isSpecial: false,
      settings: {
        notifyOnPost: true,
        notifyOnComment: false,
        showInTimeline: true,
      },
    };

    it("应该成功关注用户", async () => {
      // Arrange
      mockSocialService.followUser.mockResolvedValue(mockFollowResponse);

      // Act
      const result = await controller.followUser(followUserDto, mockRequest);

      // Assert
      expect(mockSocialService.followUser).toHaveBeenCalledWith(
        followUserDto,
        "user-001",
      );
      expect(result).toEqual({
        success: true,
        message: "关注成功",
        data: mockFollowResponse,
        statusCode: HttpStatus.CREATED,
        timestamp: expect.any(String),
      });
    });

    it("应该正确传递关注设置", async () => {
      // Arrange
      const specialFollowDto: FollowUserDto = {
        userId: "user-003",
        isSpecial: true,
        settings: {
          notifyOnPost: true,
          notifyOnComment: true,
          showInTimeline: true,
        },
        metadata: { platform: "mobile" },
      };
      const specialFollowResponse = {
        ...mockFollowResponse,
        followingId: "user-003",
        isSpecial: true,
      };
      mockSocialService.followUser.mockResolvedValue(specialFollowResponse);

      // Act
      const result = await controller.followUser(specialFollowDto, mockRequest);

      // Assert
      expect(mockSocialService.followUser).toHaveBeenCalledWith(
        specialFollowDto,
        "user-001",
      );
      expect(result.data.isSpecial).toBe(true);
      expect(result.statusCode).toBe(HttpStatus.CREATED);
    });

    it("应该正确处理服务层错误", async () => {
      // Arrange
      mockSocialService.followUser.mockRejectedValue(new Error("不能关注自己"));

      // Act & Assert
      await expect(
        controller.followUser(followUserDto, mockRequest),
      ).rejects.toThrow("不能关注自己");
    });

    it("应该正确处理重复关注错误", async () => {
      // Arrange
      mockSocialService.followUser.mockRejectedValue(
        new Error("已经关注该用户"),
      );

      // Act & Assert
      await expect(
        controller.followUser(followUserDto, mockRequest),
      ).rejects.toThrow("已经关注该用户");
    });

    it("应该正确处理用户不存在错误", async () => {
      // Arrange
      mockSocialService.followUser.mockRejectedValue(
        new Error("要关注的用户不存在"),
      );

      // Act & Assert
      await expect(
        controller.followUser(followUserDto, mockRequest),
      ).rejects.toThrow("要关注的用户不存在");
    });
  });

  describe("unfollowUser - DELETE /social/follow/:userId", () => {
    it("应该成功取消关注", async () => {
      // Arrange
      const unfollowResult = { success: true, message: "已取消关注" };
      mockSocialService.unfollowUser.mockResolvedValue(unfollowResult);

      // Act
      const result = await controller.unfollowUser("user-002", mockRequest);

      // Assert
      expect(mockSocialService.unfollowUser).toHaveBeenCalledWith(
        "user-002",
        "user-001",
      );
      expect(result).toEqual({
        success: true,
        message: "操作成功",
        data: unfollowResult,
        statusCode: HttpStatus.OK,
        timestamp: expect.any(String),
      });
    });

    it("应该正确处理关注关系不存在的错误", async () => {
      // Arrange
      mockSocialService.unfollowUser.mockRejectedValue(
        new Error("关注关系不存在"),
      );

      // Act & Assert
      await expect(
        controller.unfollowUser("user-002", mockRequest),
      ).rejects.toThrow("关注关系不存在");
    });
  });

  describe("getFollowingList - GET /social/following/:userId", () => {
    const queryDto: FollowListQueryDto = {
      page: 1,
      limit: 10,
      status: FollowStatus.ACTIVE,
      sortBy: "createdAt",
      sortOrder: "DESC",
    };

    it("应该成功获取关注列表", async () => {
      // Arrange
      mockSocialService.getFollowingList.mockResolvedValue(
        mockPaginatedUserResponse,
      );

      // Act
      const result = await controller.getFollowingList(
        "user-001",
        queryDto,
        mockRequest,
      );

      // Assert
      expect(mockSocialService.getFollowingList).toHaveBeenCalledWith(
        "user-001",
        queryDto,
        "user-001",
      );
      expect(result).toEqual({
        success: true,
        message: "获取关注列表成功",
        data: mockPaginatedUserResponse,
        statusCode: HttpStatus.OK,
        timestamp: expect.any(String),
      });
    });

    it("应该正确传递查询参数", async () => {
      // Arrange
      const customQuery: FollowListQueryDto = {
        page: 2,
        limit: 5,
        status: FollowStatus.ACTIVE,
        isSpecial: true,
        search: "测试",
        sortBy: "updatedAt",
        sortOrder: "ASC",
      };
      mockSocialService.getFollowingList.mockResolvedValue(
        mockPaginatedUserResponse,
      );

      // Act
      await controller.getFollowingList("user-002", customQuery, mockRequest);

      // Assert
      expect(mockSocialService.getFollowingList).toHaveBeenCalledWith(
        "user-002",
        customQuery,
        "user-001",
      );
    });

    it("应该处理空结果", async () => {
      // Arrange
      const emptyResponse = {
        ...mockPaginatedUserResponse,
        data: [],
        total: 0,
      };
      mockSocialService.getFollowingList.mockResolvedValue(emptyResponse);

      // Act
      const result = await controller.getFollowingList(
        "user-001",
        queryDto,
        mockRequest,
      );

      // Assert
      expect(result.data.total).toBe(0);
      expect(result.data.data).toEqual([]);
    });

    it("应该正确处理分页参数", async () => {
      // Arrange
      const paginationQuery = { ...queryDto, page: 3, limit: 20 };
      const paginatedResponse = {
        ...mockPaginatedUserResponse,
        page: 3,
        limit: 20,
        totalPages: 5,
        hasNext: true,
        hasPrev: true,
      };
      mockSocialService.getFollowingList.mockResolvedValue(paginatedResponse);

      // Act
      const result = await controller.getFollowingList(
        "user-001",
        paginationQuery,
        mockRequest,
      );

      // Assert
      expect(result.data.page).toBe(3);
      expect(result.data.limit).toBe(20);
      expect(result.data.hasNext).toBe(true);
      expect(result.data.hasPrev).toBe(true);
    });
  });

  describe("getFollowersList - GET /social/followers/:userId", () => {
    const queryDto: FollowListQueryDto = {
      page: 1,
      limit: 10,
      status: FollowStatus.ACTIVE,
    };

    it("应该成功获取粉丝列表", async () => {
      // Arrange
      mockSocialService.getFollowersList.mockResolvedValue(
        mockPaginatedUserResponse,
      );

      // Act
      const result = await controller.getFollowersList(
        "user-001",
        queryDto,
        mockRequest,
      );

      // Assert
      expect(mockSocialService.getFollowersList).toHaveBeenCalledWith(
        "user-001",
        queryDto,
        "user-001",
      );
      expect(result).toEqual({
        success: true,
        message: "获取粉丝列表成功",
        data: mockPaginatedUserResponse,
        statusCode: HttpStatus.OK,
        timestamp: expect.any(String),
      });
    });

    it("应该支持搜索功能", async () => {
      // Arrange
      const searchQuery = { ...queryDto, search: "活跃" };
      const filteredResponse = {
        ...mockPaginatedUserResponse,
        data: [
          {
            ...mockUserBrief,
            name: "活跃用户",
            username: "activeuser",
          },
        ],
      };
      mockSocialService.getFollowersList.mockResolvedValue(filteredResponse);

      // Act
      const result = await controller.getFollowersList(
        "user-001",
        searchQuery,
        mockRequest,
      );

      // Assert
      expect(mockSocialService.getFollowersList).toHaveBeenCalledWith(
        "user-001",
        searchQuery,
        "user-001",
      );
      expect(result.data.data[0].name).toBe("活跃用户");
    });
  });

  describe("createFriendGroup - POST /social/groups", () => {
    const createGroupDto: CreateFriendGroupDto = {
      name: "测试分组",
      description: "这是一个测试分组",
      groupType: GroupType.FRIENDS,
      visibility: GroupVisibility.PRIVATE,
      colorTag: "#FF5733",
      sortOrder: 1,
      permissions: {
        canViewProfile: true,
        canViewStories: true,
        canComment: true,
        canShare: false,
        canMessage: true,
      },
      initialMembers: ["user-002", "user-003"],
    };

    it("应该成功创建好友分组", async () => {
      // Arrange
      mockSocialService.createFriendGroup.mockResolvedValue(
        mockFriendGroupResponse,
      );

      // Act
      const result = await controller.createFriendGroup(
        createGroupDto,
        mockRequest,
      );

      // Assert
      expect(mockSocialService.createFriendGroup).toHaveBeenCalledWith(
        createGroupDto,
        "user-001",
      );
      expect(result).toEqual({
        success: true,
        message: "创建分组成功",
        data: mockFriendGroupResponse,
        statusCode: HttpStatus.CREATED,
        timestamp: expect.any(String),
      });
    });

    it("应该正确处理最小参数创建", async () => {
      // Arrange
      const minimalDto = { name: "简单分组" };
      const minimalResponse = {
        ...mockFriendGroupResponse,
        name: "简单分组",
        description: undefined,
        groupType: GroupType.CUSTOM,
        memberCount: 0,
      };
      mockSocialService.createFriendGroup.mockResolvedValue(minimalResponse);

      // Act
      const result = await controller.createFriendGroup(
        minimalDto,
        mockRequest,
      );

      // Assert
      expect(result.data.name).toBe("简单分组");
      expect(result.data.groupType).toBe(GroupType.CUSTOM);
      expect(result.statusCode).toBe(HttpStatus.CREATED);
    });

    it("应该正确处理不同分组类型", async () => {
      // Arrange
      const familyGroupDto = {
        ...createGroupDto,
        name: "家庭分组",
        groupType: GroupType.FAMILY,
        visibility: GroupVisibility.FRIENDS_ONLY,
      };
      const familyGroupResponse = {
        ...mockFriendGroupResponse,
        name: "家庭分组",
        groupType: GroupType.FAMILY,
        visibility: GroupVisibility.FRIENDS_ONLY,
      };
      mockSocialService.createFriendGroup.mockResolvedValue(
        familyGroupResponse,
      );

      // Act
      const result = await controller.createFriendGroup(
        familyGroupDto,
        mockRequest,
      );

      // Assert
      expect(result.data.groupType).toBe(GroupType.FAMILY);
      expect(result.data.visibility).toBe(GroupVisibility.FRIENDS_ONLY);
    });

    it("应该正确处理分组名重复错误", async () => {
      // Arrange
      mockSocialService.createFriendGroup.mockRejectedValue(
        new Error("已存在同名的分组"),
      );

      // Act & Assert
      await expect(
        controller.createFriendGroup(createGroupDto, mockRequest),
      ).rejects.toThrow("已存在同名的分组");
    });
  });

  describe("getFriendGroups - GET /social/groups", () => {
    const queryDto: FriendGroupQueryDto = {
      page: 1,
      limit: 10,
      isActive: true,
    };

    it("应该成功获取分组列表", async () => {
      // Arrange
      mockSocialService.getFriendGroups.mockResolvedValue(
        mockPaginatedGroupResponse,
      );

      // Act
      const result = await controller.getFriendGroups(queryDto, mockRequest);

      // Assert
      expect(mockSocialService.getFriendGroups).toHaveBeenCalledWith(
        "user-001",
        queryDto,
      );
      expect(result).toEqual({
        success: true,
        message: "获取分组列表成功",
        data: mockPaginatedGroupResponse,
        statusCode: HttpStatus.OK,
        timestamp: expect.any(String),
      });
    });

    it("应该正确传递筛选条件", async () => {
      // Arrange
      const filterQuery: FriendGroupQueryDto = {
        page: 1,
        limit: 5,
        groupType: GroupType.FAMILY,
        visibility: GroupVisibility.PUBLIC,
        isActive: true,
        search: "家庭",
        sortBy: "memberCount",
        sortOrder: "DESC",
      };
      mockSocialService.getFriendGroups.mockResolvedValue(
        mockPaginatedGroupResponse,
      );

      // Act
      await controller.getFriendGroups(filterQuery, mockRequest);

      // Assert
      expect(mockSocialService.getFriendGroups).toHaveBeenCalledWith(
        "user-001",
        filterQuery,
      );
    });

    it("应该处理空分组列表", async () => {
      // Arrange
      const emptyResponse = {
        ...mockPaginatedGroupResponse,
        data: [],
        total: 0,
      };
      mockSocialService.getFriendGroups.mockResolvedValue(emptyResponse);

      // Act
      const result = await controller.getFriendGroups(queryDto, mockRequest);

      // Assert
      expect(result.data.total).toBe(0);
      expect(result.data.data).toEqual([]);
    });

    it("应该正确处理排序参数", async () => {
      // Arrange
      const sortQuery = {
        ...queryDto,
        sortBy: "createdAt",
        sortOrder: "ASC" as const,
      };
      mockSocialService.getFriendGroups.mockResolvedValue(
        mockPaginatedGroupResponse,
      );

      // Act
      await controller.getFriendGroups(sortQuery, mockRequest);

      // Assert
      expect(mockSocialService.getFriendGroups).toHaveBeenCalledWith(
        "user-001",
        expect.objectContaining({
          sortBy: "createdAt",
          sortOrder: "ASC",
        }),
      );
    });
  });

  describe("getGroupMembers - GET /social/groups/:groupId/members", () => {
    const queryDto: GroupMemberQueryDto = {
      page: 1,
      limit: 10,
      status: MemberStatus.ACTIVE,
    };

    it("应该成功获取分组成员", async () => {
      // Arrange
      mockSocialService.getGroupMembers.mockResolvedValue(
        mockPaginatedMemberResponse,
      );

      // Act
      const result = await controller.getGroupMembers(
        "group-001",
        queryDto,
        mockRequest,
      );

      // Assert
      expect(mockSocialService.getGroupMembers).toHaveBeenCalledWith(
        "group-001",
        queryDto,
        "user-001",
      );
      expect(result).toEqual({
        success: true,
        message: "获取分组成员成功",
        data: mockPaginatedMemberResponse,
        statusCode: HttpStatus.OK,
        timestamp: expect.any(String),
      });
    });

    it("应该正确传递查询参数", async () => {
      // Arrange
      const customQuery: GroupMemberQueryDto = {
        page: 2,
        limit: 5,
        status: MemberStatus.ACTIVE,
        role: MemberRole.ADMIN,
        search: "管理",
        sortBy: "addedAt",
        sortOrder: "ASC",
      };
      mockSocialService.getGroupMembers.mockResolvedValue(
        mockPaginatedMemberResponse,
      );

      // Act
      await controller.getGroupMembers("group-001", customQuery, mockRequest);

      // Assert
      expect(mockSocialService.getGroupMembers).toHaveBeenCalledWith(
        "group-001",
        customQuery,
        "user-001",
      );
    });

    it("应该正确处理角色筛选", async () => {
      // Arrange
      const adminQuery = { ...queryDto, role: MemberRole.ADMIN };
      const adminResponse = {
        ...mockPaginatedMemberResponse,
        data: [
          {
            ...mockGroupMemberResponse,
            role: MemberRole.ADMIN,
            userName: "管理员",
          },
        ],
      };
      mockSocialService.getGroupMembers.mockResolvedValue(adminResponse);

      // Act
      const result = await controller.getGroupMembers(
        "group-001",
        adminQuery,
        mockRequest,
      );

      // Assert
      expect(result.data.data[0].role).toBe(MemberRole.ADMIN);
      expect(result.data.data[0].userName).toBe("管理员");
    });

    it("应该正确处理分组不存在错误", async () => {
      // Arrange
      mockSocialService.getGroupMembers.mockRejectedValue(
        new Error("分组不存在"),
      );

      // Act & Assert
      await expect(
        controller.getGroupMembers("non-existent", queryDto, mockRequest),
      ).rejects.toThrow("分组不存在");
    });

    it("应该正确处理权限不足错误", async () => {
      // Arrange
      mockSocialService.getGroupMembers.mockRejectedValue(
        new Error("无权限查看该分组成员"),
      );

      // Act & Assert
      await expect(
        controller.getGroupMembers("group-001", queryDto, mockRequest),
      ).rejects.toThrow("无权限查看该分组成员");
    });
  });

  describe("addMembersToGroup - POST /social/groups/:groupId/members", () => {
    const userIds = ["user-002", "user-003", "user-004"];

    it("应该成功添加成员到分组", async () => {
      // Arrange
      mockSocialService.addMembersToGroup.mockResolvedValue(3);

      // Act
      const result = await controller.addMembersToGroup(
        "group-001",
        userIds,
        mockRequest,
      );

      // Assert
      expect(mockSocialService.addMembersToGroup).toHaveBeenCalledWith(
        "group-001",
        userIds,
        "user-001",
      );
      expect(result).toEqual({
        success: true,
        message: "成功添加 3 个成员",
        data: { addedCount: 3 },
        statusCode: HttpStatus.CREATED,
        timestamp: expect.any(String),
      });
    });

    it("应该正确处理部分添加成功的情况", async () => {
      // Arrange
      mockSocialService.addMembersToGroup.mockResolvedValue(2);

      // Act
      const result = await controller.addMembersToGroup(
        "group-001",
        userIds,
        mockRequest,
      );

      // Assert
      expect(result.data.addedCount).toBe(2);
      expect(result.message).toBe("成功添加 2 个成员");
    });

    it("应该正确处理没有新增成员的情况", async () => {
      // Arrange
      mockSocialService.addMembersToGroup.mockResolvedValue(0);

      // Act
      const result = await controller.addMembersToGroup(
        "group-001",
        userIds,
        mockRequest,
      );

      // Assert
      expect(result.data.addedCount).toBe(0);
      expect(result.message).toBe("成功添加 0 个成员");
    });

    it("应该正确处理分组不存在错误", async () => {
      // Arrange
      mockSocialService.addMembersToGroup.mockRejectedValue(
        new Error("分组不存在"),
      );

      // Act & Assert
      await expect(
        controller.addMembersToGroup("non-existent", userIds, mockRequest),
      ).rejects.toThrow("分组不存在");
    });

    it("应该正确处理权限不足错误", async () => {
      // Arrange
      mockSocialService.addMembersToGroup.mockRejectedValue(
        new Error("只有分组所有者可以添加成员"),
      );

      // Act & Assert
      await expect(
        controller.addMembersToGroup("group-001", userIds, mockRequest),
      ).rejects.toThrow("只有分组所有者可以添加成员");
    });

    it("应该正确处理用户不存在错误", async () => {
      // Arrange
      mockSocialService.addMembersToGroup.mockRejectedValue(
        new Error("部分用户不存在"),
      );

      // Act & Assert
      await expect(
        controller.addMembersToGroup("group-001", userIds, mockRequest),
      ).rejects.toThrow("部分用户不存在");
    });
  });

  describe("getSocialStats - GET /social/stats/:userId", () => {
    it("应该成功获取社交统计数据", async () => {
      // Arrange
      mockSocialService.getSocialStats.mockResolvedValue(
        mockSocialStatsResponse,
      );

      // Act
      const result = await controller.getSocialStats("user-001");

      // Assert
      expect(mockSocialService.getSocialStats).toHaveBeenCalledWith("user-001");
      expect(result).toEqual({
        success: true,
        message: "获取社交统计成功",
        data: mockSocialStatsResponse,
        statusCode: HttpStatus.OK,
        timestamp: expect.any(String),
      });
    });

    it("应该正确返回完整的统计数据", async () => {
      // Arrange
      const detailedStats: SocialStatsResponseDto = {
        ...mockSocialStatsResponse,
        followingCount: 300,
        followersCount: 500,
        mutualFollowsCount: 150,
        specialFollowsCount: 50,
        friendGroupsCount: 10,
        groupMembershipsCount: 15,
        newFollowersToday: 8,
        newFollowingToday: 3,
      };
      mockSocialService.getSocialStats.mockResolvedValue(detailedStats);

      // Act
      const result = await controller.getSocialStats("user-002");

      // Assert
      expect(result.data.followingCount).toBe(300);
      expect(result.data.followersCount).toBe(500);
      expect(result.data.mutualFollowsCount).toBe(150);
      expect(result.data.topGroups).toBeDefined();
      expect(result.data.topInteractors).toBeDefined();
    });

    it("应该正确处理空统计数据", async () => {
      // Arrange
      const emptyStats: SocialStatsResponseDto = {
        userId: "user-new",
        followingCount: 0,
        followersCount: 0,
        mutualFollowsCount: 0,
        specialFollowsCount: 0,
        friendGroupsCount: 0,
        groupMembershipsCount: 0,
        newFollowersToday: 0,
        newFollowingToday: 0,
        topGroups: [],
        topInteractors: [],
      };
      mockSocialService.getSocialStats.mockResolvedValue(emptyStats);

      // Act
      const result = await controller.getSocialStats("user-new");

      // Assert
      expect(result.data.followingCount).toBe(0);
      expect(result.data.followersCount).toBe(0);
      expect(result.data.topGroups).toEqual([]);
      expect(result.data.topInteractors).toEqual([]);
    });

    it("应该正确返回热门分组信息", async () => {
      // Arrange
      const statsWithGroups = {
        ...mockSocialStatsResponse,
        topGroups: [
          {
            groupId: "group-popular",
            groupName: "热门分组",
            memberCount: 100,
            groupType: GroupType.FRIENDS,
          },
          {
            groupId: "group-work",
            groupName: "工作分组",
            memberCount: 50,
            groupType: GroupType.COLLEAGUES,
          },
        ],
      };
      mockSocialService.getSocialStats.mockResolvedValue(statsWithGroups);

      // Act
      const result = await controller.getSocialStats("user-001");

      // Assert
      expect(result.data.topGroups).toHaveLength(2);
      expect(result.data.topGroups[0].memberCount).toBe(100);
      expect(result.data.topGroups[1].groupType).toBe(GroupType.COLLEAGUES);
    });

    it("应该正确返回互动用户信息", async () => {
      // Arrange
      const statsWithInteractors = {
        ...mockSocialStatsResponse,
        topInteractors: [
          {
            userId: "user-active",
            userName: "超级活跃用户",
            userAvatar: "https://example.com/avatar-active.jpg",
            interactionScore: 98,
            lastInteractionAt: new Date(),
          },
        ],
      };
      mockSocialService.getSocialStats.mockResolvedValue(statsWithInteractors);

      // Act
      const result = await controller.getSocialStats("user-001");

      // Assert
      expect(result.data.topInteractors).toHaveLength(1);
      expect(result.data.topInteractors[0].interactionScore).toBe(98);
      expect(result.data.topInteractors[0].userName).toBe("超级活跃用户");
    });
  });

  describe("错误处理和边界测试", () => {
    it("应该正确处理服务层抛出的各种异常", async () => {
      // Arrange
      const errors = [
        "不能关注自己",
        "要关注的用户不存在",
        "已经关注该用户",
        "关注关系不存在",
        "已存在同名的分组",
        "分组不存在",
        "无权限查看该分组成员",
        "只有分组所有者可以添加成员",
        "部分用户不存在",
      ];

      for (const error of errors) {
        mockSocialService.followUser.mockRejectedValue(new Error(error));

        // Act & Assert
        await expect(
          controller.followUser({ userId: "user-002" }, mockRequest),
        ).rejects.toThrow(error);
      }
    });

    it("应该处理无效的UUID格式", async () => {
      // Arrange
      mockSocialService.followUser.mockRejectedValue(new Error("无效的ID格式"));

      // Act & Assert
      await expect(
        controller.followUser({ userId: "invalid-uuid" }, mockRequest),
      ).rejects.toThrow("无效的ID格式");
    });

    it("应该处理请求中缺少用户信息的情况", async () => {
      // Arrange
      const invalidRequest = {} as Request & { user: { id: string } };

      // Act & Assert
      await expect(
        controller.followUser({ userId: "user-002" }, invalidRequest),
      ).rejects.toThrow();
    });

    it("应该处理空的初始成员列表", async () => {
      // Arrange
      const groupWithoutMembers = {
        name: "空分组",
        initialMembers: [],
      };
      const emptyGroupResponse = {
        ...mockFriendGroupResponse,
        name: "空分组",
        memberCount: 0,
      };
      mockSocialService.createFriendGroup.mockResolvedValue(emptyGroupResponse);

      // Act
      const result = await controller.createFriendGroup(
        groupWithoutMembers,
        mockRequest,
      );

      // Assert
      expect(result.data.memberCount).toBe(0);
    });
  });

  describe("ApiResponse 格式验证", () => {
    it("所有成功响应应该包含正确的 ApiResponse 格式", async () => {
      // Arrange
      mockSocialService.followUser.mockResolvedValue(mockFollowResponse);

      // Act
      const result = await controller.followUser(
        { userId: "user-002" },
        mockRequest,
      );

      // Assert
      expect(result).toEqual({
        success: true,
        message: expect.any(String),
        data: expect.any(Object),
        statusCode: expect.any(Number),
        timestamp: expect.any(String),
      });
    });

    it("应该包含正确的HTTP状态码", async () => {
      // Arrange
      mockSocialService.followUser.mockResolvedValue(mockFollowResponse);
      mockSocialService.getFollowingList.mockResolvedValue(
        mockPaginatedUserResponse,
      );
      mockSocialService.createFriendGroup.mockResolvedValue(
        mockFriendGroupResponse,
      );
      mockSocialService.getSocialStats.mockResolvedValue(
        mockSocialStatsResponse,
      );

      // Act
      const followResult = await controller.followUser(
        { userId: "user-002" },
        mockRequest,
      );
      const listResult = await controller.getFollowingList(
        "user-001",
        { page: 1, limit: 10 },
        mockRequest,
      );
      const createResult = await controller.createFriendGroup(
        { name: "测试分组" },
        mockRequest,
      );
      const statsResult = await controller.getSocialStats("user-001");

      // Assert
      expect(followResult.statusCode).toBe(HttpStatus.CREATED);
      expect(listResult.statusCode).toBe(HttpStatus.OK);
      expect(createResult.statusCode).toBe(HttpStatus.CREATED);
      expect(statsResult.statusCode).toBe(HttpStatus.OK);
    });
  });

  describe("关注设置处理", () => {
    it("应该正确处理不同的关注设置", async () => {
      // Arrange
      const settingsVariations = [
        {
          notifyOnPost: true,
          notifyOnComment: true,
          showInTimeline: true,
        },
        {
          notifyOnPost: false,
          notifyOnComment: false,
          showInTimeline: false,
        },
        {
          notifyOnPost: true,
          notifyOnComment: false,
          showInTimeline: true,
        },
      ];

      for (const settings of settingsVariations) {
        const followDto = {
          userId: "user-002",
          isSpecial: true,
          settings,
        };
        const response = {
          ...mockFollowResponse,
          settings,
        };
        mockSocialService.followUser.mockResolvedValue(response);

        // Act
        const result = await controller.followUser(followDto, mockRequest);

        // Assert
        expect(mockSocialService.followUser).toHaveBeenCalledWith(
          followDto,
          "user-001",
        );
        expect(result.data.settings).toEqual(settings);
      }
    });
  });

  describe("分组权限处理", () => {
    it("应该正确处理不同的分组权限设置", async () => {
      // Arrange
      const permissionVariations = [
        {
          canViewProfile: true,
          canViewStories: true,
          canComment: true,
          canShare: true,
          canMessage: true,
        },
        {
          canViewProfile: true,
          canViewStories: false,
          canComment: false,
          canShare: false,
          canMessage: true,
        },
        {
          canViewProfile: false,
          canViewStories: false,
          canComment: false,
          canShare: false,
          canMessage: false,
        },
      ];

      for (const permissions of permissionVariations) {
        const groupDto = {
          name: "权限测试分组",
          permissions,
        };
        const response = {
          ...mockFriendGroupResponse,
          permissions,
        };
        mockSocialService.createFriendGroup.mockResolvedValue(response);

        // Act
        const result = await controller.createFriendGroup(
          groupDto,
          mockRequest,
        );

        // Assert
        expect(result.data.permissions).toEqual(permissions);
      }
    });
  });

  describe("分页参数处理", () => {
    it("应该正确处理默认分页参数", async () => {
      // Arrange
      mockSocialService.getFollowingList.mockResolvedValue(
        mockPaginatedUserResponse,
      );

      // Act
      await controller.getFollowingList("user-001", {}, mockRequest);

      // Assert
      expect(mockSocialService.getFollowingList).toHaveBeenCalledWith(
        "user-001",
        expect.objectContaining({
          page: undefined, // 由服务层设置默认值
          limit: undefined,
        }),
        "user-001",
      );
    });

    it("应该正确验证分页参数范围", async () => {
      // Arrange
      const invalidQuery = {
        page: -1,
        limit: 0,
      };

      // 模拟服务层验证
      mockSocialService.getFollowingList.mockRejectedValue(
        new Error("无效的分页参数"),
      );

      // Act & Assert
      await expect(
        controller.getFollowingList("user-001", invalidQuery, mockRequest),
      ).rejects.toThrow("无效的分页参数");
    });

    it("应该正确处理大页码", async () => {
      // Arrange
      const largePageQuery = {
        page: 100,
        limit: 50,
      };
      const emptyResponse = {
        ...mockPaginatedUserResponse,
        data: [],
        total: 0,
        page: 100,
        totalPages: 0,
        hasNext: false,
        hasPrev: true,
      };
      mockSocialService.getFollowingList.mockResolvedValue(emptyResponse);

      // Act
      const result = await controller.getFollowingList(
        "user-001",
        largePageQuery,
        mockRequest,
      );

      // Assert
      expect(result.data.page).toBe(100);
      expect(result.data.data).toEqual([]);
      expect(result.data.hasNext).toBe(false);
      expect(result.data.hasPrev).toBe(true);
    });
  });
});
