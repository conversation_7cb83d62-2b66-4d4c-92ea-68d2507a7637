name: Test Coverage Monitoring

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # 每天上午9点运行
    - cron: '0 9 * * *'

jobs:
  coverage:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:6
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Setup test environment
      run: |
        cp .env.example .env
        echo "NODE_ENV=test" >> .env
        echo "DB_HOST=localhost" >> .env
        echo "DB_PORT=5432" >> .env
        echo "DB_USERNAME=postgres" >> .env
        echo "DB_PASSWORD=postgres" >> .env
        echo "DB_NAME=test_db" >> .env
        echo "REDIS_HOST=localhost" >> .env
        echo "REDIS_PORT=6379" >> .env
        echo "JWT_SECRET=test-secret" >> .env
        echo "JWT_REFRESH_SECRET=test-refresh-secret" >> .env
    
    - name: Run coverage monitoring
      run: node scripts/test-coverage-monitor.js
      env:
        CI: true
    
    - name: Upload coverage reports
      uses: actions/upload-artifact@v3
      with:
        name: coverage-reports
        path: |
          coverage/
          !coverage/tmp/
    
    - name: Comment PR with coverage
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          const path = './coverage/coverage-report.md';
          
          if (fs.existsSync(path)) {
            const report = fs.readFileSync(path, 'utf8');
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `## 📊 测试覆盖率报告\n\n${report}`
            });
          }
    
    - name: Check coverage thresholds
      run: |
        if [ -f coverage/coverage-summary.json ]; then
          node -e "
            const coverage = require('./coverage/coverage-summary.json');
            const thresholds = {
              statements: 85,
              branches: 85,
              functions: 85,
              lines: 85
            };
            
            let failed = false;
            Object.entries(thresholds).forEach(([key, threshold]) => {
              const actual = coverage.total[key].pct;
              if (actual < threshold) {
                console.log(\`❌ \${key}: \${actual}% < \${threshold}%\`);
                failed = true;
              } else {
                console.log(\`✅ \${key}: \${actual}% >= \${threshold}%\`);
              }
            });
            
            if (failed) {
              console.log('\\n❌ 测试覆盖率不达标，请改进测试');
              process.exit(1);
            } else {
              console.log('\\n✅ 测试覆盖率达标');
            }
          "
        else
          echo "❌ 覆盖率报告文件不存在"
          exit 1
        fi