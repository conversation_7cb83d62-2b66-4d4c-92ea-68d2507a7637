import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import { JwtService } from "@nestjs/jwt";
import { ConfigService } from "@nestjs/config";
import { StoriesService } from "./stories.service";
import { StoriesController } from "./stories.controller";
import { StoryCharactersController } from "./controllers/story-characters.controller";
import { StoryPermissionService } from "./services/story-permission.service";
import {
  StoryAccessGuard,
  StoryEditGuard,
  StoryActionGuard,
} from "./guards/story-access.guard";
import { Story } from "./entities/story.entity";
import { StoryChapter } from "./entities/story-chapter.entity";
import { StoryShare } from "./entities/story-share.entity";
import { Theme } from "./entities/theme.entity";
import { Character } from "../characters/entities/character.entity";
import { CharacterLighting } from "../characters/entities/character-lighting.entity";
import { User } from "../users/entities/user.entity";
import { UserRelationship } from "../users/entities/user-relationship.entity";
import { CharactersService } from "../characters/characters.service";
import { SmsService } from "../auth/services/sms.service";
import { AuthService } from "../auth/auth.service";

/**
 * 故事模块测试 - v1.0.0
 * 测试故事模块的基本实例化和依赖注入
 */
describe("StoriesModule", () => {
  let module: TestingModule;
  let storiesService: StoriesService;
  let storiesController: StoriesController;
  let storyCharactersController: StoryCharactersController;
  let storyPermissionService: StoryPermissionService;
  let storyAccessGuard: StoryAccessGuard;
  let storyEditGuard: StoryEditGuard;
  let storyActionGuard: StoryActionGuard;

  // Mock Repository基础方法
  const mockRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
    findOneBy: jest.fn(),
    save: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    createQueryBuilder: jest.fn(() => ({
      select: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      getMany: jest.fn(),
      getOne: jest.fn(),
      leftJoinAndSelect: jest.fn().mockReturnThis(),
      innerJoinAndSelect: jest.fn().mockReturnThis(),
      leftJoin: jest.fn().mockReturnThis(),
      groupBy: jest.fn().mockReturnThis(),
      having: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      take: jest.fn().mockReturnThis(),
    })),
  };

  // Mock CharactersService
  const mockCharactersService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
    findByStoryId: jest.fn(),
    getCharactersByStory: jest.fn(),
  };

  // Mock JWT服务
  const mockJwtService = {
    signAsync: jest.fn(),
    sign: jest.fn(),
    verify: jest.fn(),
  };

  // Mock配置服务
  const mockConfigService = {
    get: jest.fn((key: string) => {
      const config: Record<string, string> = {
        JWT_SECRET: "test-secret",
        JWT_EXPIRES_IN: "2700",
        JWT_REFRESH_EXPIRES_IN: "1209600",
        JWT_REFRESH_SECRET: "test-refresh-secret",
        BCRYPT_ROUNDS: "12",
      };
      return config[key];
    }),
  };

  // Mock SMS服务
  const mockSmsService = {
    sendVerificationCode: jest.fn(),
    verifyCode: jest.fn(),
  };

  // Mock AuthService
  const mockAuthService = {
    login: jest.fn(),
    logout: jest.fn(),
    refreshToken: jest.fn(),
    validateUser: jest.fn(),
    createUser: jest.fn(),
    updateUser: jest.fn(),
    register: jest.fn(),
    sendVerificationCode: jest.fn(),
    verifyCode: jest.fn(),
  };

  beforeEach(async () => {
    module = await Test.createTestingModule({
      providers: [
        StoriesService,
        StoriesController,
        StoryCharactersController,
        StoryPermissionService,
        StoryAccessGuard,
        StoryEditGuard,
        StoryActionGuard,
        {
          provide: getRepositoryToken(Story),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(StoryChapter),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(StoryShare),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(Theme),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(Character),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(CharacterLighting),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(User),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(UserRelationship),
          useValue: mockRepository,
        },
        {
          provide: CharactersService,
          useValue: mockCharactersService,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: SmsService,
          useValue: mockSmsService,
        },
        {
          provide: AuthService,
          useValue: mockAuthService,
        },
      ],
    }).compile();

    storiesService = module.get<StoriesService>(StoriesService);
    storiesController = module.get<StoriesController>(StoriesController);
    storyCharactersController = module.get<StoryCharactersController>(
      StoryCharactersController,
    );
    storyPermissionService = module.get<StoryPermissionService>(
      StoryPermissionService,
    );
    storyAccessGuard = module.get<StoryAccessGuard>(StoryAccessGuard);
    storyEditGuard = module.get<StoryEditGuard>(StoryEditGuard);
    storyActionGuard = module.get<StoryActionGuard>(StoryActionGuard);
  });

  afterEach(async () => {
    if (module) {
      await module.close();
    }
    jest.clearAllMocks();
  });

  describe("模块实例化", () => {
    it("应该能够正确实例化模块", () => {
      expect(module).toBeDefined();
    });

    it("应该正确提供 StoriesService", () => {
      expect(storiesService).toBeDefined();
      expect(storiesService).toBeInstanceOf(StoriesService);
    });

    it("应该正确提供所有控制器", () => {
      expect(storiesController).toBeDefined();
      expect(storiesController).toBeInstanceOf(StoriesController);
      expect(storyCharactersController).toBeDefined();
      expect(storyCharactersController).toBeInstanceOf(
        StoryCharactersController,
      );
    });

    it("应该正确提供 StoryPermissionService", () => {
      expect(storyPermissionService).toBeDefined();
      expect(storyPermissionService).toBeInstanceOf(StoryPermissionService);
    });

    it("应该正确提供所有守卫", () => {
      expect(storyAccessGuard).toBeDefined();
      expect(storyAccessGuard).toBeInstanceOf(StoryAccessGuard);
      expect(storyEditGuard).toBeDefined();
      expect(storyEditGuard).toBeInstanceOf(StoryEditGuard);
      expect(storyActionGuard).toBeDefined();
      expect(storyActionGuard).toBeInstanceOf(StoryActionGuard);
    });
  });

  describe("模块依赖注入", () => {
    it("应该正确注入所有 Repository 依赖", () => {
      const storyRepo = module.get(getRepositoryToken(Story));
      const storyChapterRepo = module.get(getRepositoryToken(StoryChapter));
      const storyShareRepo = module.get(getRepositoryToken(StoryShare));
      const themeRepo = module.get(getRepositoryToken(Theme));
      const characterRepo = module.get(getRepositoryToken(Character));
      const characterLightingRepo = module.get(
        getRepositoryToken(CharacterLighting),
      );
      const userRepo = module.get(getRepositoryToken(User));
      const userRelationshipRepo = module.get(
        getRepositoryToken(UserRelationship),
      );

      expect(storyRepo).toBeDefined();
      expect(storyChapterRepo).toBeDefined();
      expect(storyShareRepo).toBeDefined();
      expect(themeRepo).toBeDefined();
      expect(characterRepo).toBeDefined();
      expect(characterLightingRepo).toBeDefined();
      expect(userRepo).toBeDefined();
      expect(userRelationshipRepo).toBeDefined();
    });

    it("应该正确注入外部模块服务", () => {
      const charactersService =
        module.get<CharactersService>(CharactersService);
      const jwtService = module.get<JwtService>(JwtService);
      const configService = module.get<ConfigService>(ConfigService);

      expect(charactersService).toBeDefined();
      expect(jwtService).toBeDefined();
      expect(configService).toBeDefined();
    });
  });

  describe("模块导出", () => {
    it("应该正确导出 StoriesService", () => {
      // 验证服务可以被外部模块使用
      expect(storiesService).toBeDefined();
      expect(storiesService).toBeInstanceOf(StoriesService);
    });

    it("应该正确导出 StoryPermissionService", () => {
      // 验证权限服务可以被外部模块使用
      expect(storyPermissionService).toBeDefined();
      expect(storyPermissionService).toBeInstanceOf(StoryPermissionService);
    });
  });

  describe("模块配置", () => {
    it("应该正确配置 TypeORM 实体", () => {
      // 验证模块能够正确配置所有必要的数据库实体
      expect(module.get(getRepositoryToken(Story))).toBeDefined();
      expect(module.get(getRepositoryToken(StoryChapter))).toBeDefined();
      expect(module.get(getRepositoryToken(StoryShare))).toBeDefined();
      expect(module.get(getRepositoryToken(Theme))).toBeDefined();
      expect(module.get(getRepositoryToken(Character))).toBeDefined();
      expect(module.get(getRepositoryToken(CharacterLighting))).toBeDefined();
      expect(module.get(getRepositoryToken(User))).toBeDefined();
      expect(module.get(getRepositoryToken(UserRelationship))).toBeDefined();
    });

    it("应该正确处理 forwardRef 依赖", () => {
      // 验证循环依赖处理正确
      const charactersService =
        module.get<CharactersService>(CharactersService);
      const jwtService = module.get<JwtService>(JwtService);
      expect(charactersService).toBeDefined();
      expect(jwtService).toBeDefined();
    });
  });

  describe("业务功能验证", () => {
    it("应该支持故事创建和管理功能", () => {
      expect(storiesService).toBeDefined();
      expect(storiesController).toBeDefined();

      // 验证控制器和服务都已正确实例化，支持故事管理功能
      expect(typeof storiesService.createStory).toBe("function");
      expect(typeof storiesService.getUserStories).toBe("function");
      expect(typeof storiesService.findById).toBe("function");
      expect(typeof storiesService.updateStory).toBe("function");
      expect(typeof storiesService.deleteStory).toBe("function");
    });

    it("应该支持故事权限控制功能", () => {
      // 验证权限服务和守卫已正确实例化
      expect(storyPermissionService).toBeDefined();
      expect(storyAccessGuard).toBeDefined();
      expect(storyEditGuard).toBeDefined();
      expect(storyActionGuard).toBeDefined();
    });

    it("应该支持故事人物管理功能", () => {
      // 验证故事人物控制器已正确实例化
      expect(storyCharactersController).toBeDefined();
      expect(storyCharactersController).toBeInstanceOf(
        StoryCharactersController,
      );

      // 验证人物相关的Repository已正确注入
      const characterRepo = module.get(getRepositoryToken(Character));
      const characterLightingRepo = module.get(
        getRepositoryToken(CharacterLighting),
      );

      expect(characterRepo).toBeDefined();
      expect(characterLightingRepo).toBeDefined();
    });

    it("应该支持内容安全检测功能", () => {
      // 验证故事内容管理相关的实体已正确注入
      const storyRepo = module.get(getRepositoryToken(Story));
      const storyChapterRepo = module.get(getRepositoryToken(StoryChapter));

      expect(storyRepo).toBeDefined();
      expect(storyChapterRepo).toBeDefined();
    });
  });
});
