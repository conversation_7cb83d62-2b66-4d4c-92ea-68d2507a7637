import { Module, forwardRef } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { LightingService } from "./lighting.service";
import { LightingController } from "./lighting.controller";
import { LightRequest } from "../characters/entities/light-request.entity";
import { CharacterLighting } from "../characters/entities/character-lighting.entity";
import { Character } from "../characters/entities/character.entity";
import { Story } from "../stories/entities/story.entity";
import { User } from "../users/entities/user.entity";
import { UsersModule } from "../users/users.module";
import { AuthModule } from "../auth/auth.module";

@Module({
  imports: [
    TypeOrmModule.forFeature([
      LightRequest,
      CharacterLighting,
      Character,
      Story,
      User,
    ]),
    forwardRef(() => UsersModule),
    AuthModule, // 修复JwtAuthGuard依赖问题
  ],
  controllers: [LightingController],
  providers: [LightingService],
  exports: [LightingService],
})
export class LightingModule {}
