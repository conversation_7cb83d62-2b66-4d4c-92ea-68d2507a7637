/* eslint-disable @typescript-eslint/no-explicit-any, @typescript-eslint/no-unused-vars */
import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { HttpStatus } from "@nestjs/common";
import type { Request } from "express";

import { SharesController } from "./shares.controller";
import { SharesService } from "./shares.service";
import { ShareType, ShareStatus } from "./entities/share.entity";
import type {
  CreateShareDto,
  ShareAccessDto,
  ShareResponseDto,
  ShareContentResponseDto,
  ShareStatsResponseDto,
} from "./dto";

describe("SharesController - 企业级单元测试", () => {
  let controller: SharesController;
  let mockSharesService: jest.Mocked<SharesService>;

  // 测试数据工厂
  const mockUser = { id: "user-001", nickname: "测试用户" };
  const mockRequest = {
    user: mockUser,
  } as unknown as Request & { user: { id: string } };

  const mockShareResponse: ShareResponseDto = {
    id: "share-001",
    token: "ABC123",
    shareUrl: "https://example.com/share/ABC123",
    storyId: "story-001",
    userId: "user-001",
    shareType: ShareType.PUBLIC,
    status: ShareStatus.ACTIVE,
    hasPassword: false,
    accessLimit: -1,
    accessCount: 10,
    description: "这是一个测试故事的分享",
    storySnapshot: {
      title: "测试故事",
      summary: "这是一个测试故事的内容",
      coverImage: "https://example.com/cover.jpg",
      authorName: "故事作者",
    },
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockShareContentResponse: ShareContentResponseDto = {
    share: {
      id: "share-001",
      token: "ABC123",
      shareType: ShareType.PUBLIC,
      description: "这是一个测试故事的分享",
      accessCount: 10,
      sharerName: "测试用户",
      createdAt: new Date(),
    },
    story: {
      id: "story-001",
      title: "测试故事",
      summary: "这是一个测试故事的内容",
      content: "这是一个测试故事的内容",
      coverImage: "https://example.com/cover.jpg",
      authorId: "author-001",
      authorName: "故事作者",
      createdAt: new Date(),
      isAccessible: true,
    },
    isOwner: false,
  };

  const mockShareStatsResponse: ShareStatsResponseDto = {
    totalShares: 5,
    activeShares: 4,
    totalAccessCount: 100,
    uniqueVisitors: 30,
    accessCountToday: 10,
    accessCountThisWeek: 45,
    accessCountThisMonth: 100,
    topShares: [
      {
        shareId: "share-001",
        storyTitle: "测试故事",
        accessCount: 50,
        createdAt: new Date(),
      },
      {
        shareId: "share-002",
        storyTitle: "另一个故事",
        accessCount: 25,
        createdAt: new Date(),
      },
    ],
  };

  beforeEach(async () => {
    const mockService = {
      createShare: jest.fn(),
      accessShare: jest.fn(),
      getShareStats: jest.fn(),
      getUserShares: jest.fn(),
      deleteShare: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [SharesController],
      providers: [
        {
          provide: SharesService,
          useValue: mockService,
        },
      ],
    }).compile();

    controller = module.get<SharesController>(SharesController);
    mockSharesService = module.get(SharesService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("createShare - POST /stories/:id/share", () => {
    const createShareDto: CreateShareDto = {
      shareType: ShareType.PUBLIC,
      description: "这是一个很棒的故事",
      metadata: { source: "web", device: "desktop" },
    };

    it("应该成功创建分享链接", async () => {
      // Arrange
      mockSharesService.createShare.mockResolvedValue(mockShareResponse);

      // Act
      const result = await controller.createShare(
        "story-001",
        createShareDto,
        mockRequest,
      );

      // Assert
      expect(mockSharesService.createShare).toHaveBeenCalledWith(
        "story-001",
        createShareDto,
        "user-001",
      );
      expect(result).toEqual({
        success: true,
        statusCode: HttpStatus.CREATED,
        message: "分享链接创建成功",
        data: mockShareResponse,
        timestamp: expect.any(String),
      });
    });

    it("应该正确创建私密分享", async () => {
      // Arrange
      const privateShareDto = {
        ...createShareDto,
        shareType: ShareType.LIMITED,
        password: "testpass",
        accessLimit: 10,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      };
      const privateShare = {
        ...mockShareResponse,
        shareType: ShareType.LIMITED,
        hasPassword: true,
        accessLimit: 10,
      };
      mockSharesService.createShare.mockResolvedValue(privateShare);

      // Act
      const result = await controller.createShare(
        "story-001",
        privateShareDto,
        mockRequest,
      );

      // Assert
      expect(result.data.shareType).toBe(ShareType.LIMITED);
      expect(result.data.accessLimit).toBe(10);
    });

    it("应该正确处理服务层错误", async () => {
      // Arrange
      mockSharesService.createShare.mockRejectedValue(new Error("故事不存在"));

      // Act & Assert
      await expect(
        controller.createShare("story-001", createShareDto, mockRequest),
      ).rejects.toThrow("故事不存在");
    });

    it("应该正确处理权限错误", async () => {
      // Arrange
      mockSharesService.createShare.mockRejectedValue(
        new Error("无权限分享此故事"),
      );

      // Act & Assert
      await expect(
        controller.createShare("story-001", createShareDto, mockRequest),
      ).rejects.toThrow("无权限分享此故事");
    });
  });

  describe("accessShare - GET /share/:token", () => {
    const accessDto: ShareAccessDto = {
      password: "testpass",
    };

    const mockHeaders = {
      "user-agent": "Mozilla/5.0",
      referer: "https://example.com",
    };

    it("应该成功访问分享内容", async () => {
      // Arrange
      mockSharesService.accessShare.mockResolvedValue(mockShareContentResponse);

      // Act
      const result = await controller.accessShare(
        "ABC123",
        accessDto,
        "192.168.1.1",
        mockHeaders["user-agent"],
        mockHeaders.referer,
        mockRequest,
      );

      // Assert
      expect(mockSharesService.accessShare).toHaveBeenCalledWith(
        "ABC123",
        accessDto,
        "192.168.1.1",
        "Mozilla/5.0",
        "https://example.com",
        "user-001",
      );
      expect(result).toEqual({
        success: true,
        statusCode: HttpStatus.OK,
        message: "分享内容获取成功",
        data: mockShareContentResponse,
        timestamp: expect.any(String),
      });
    });

    it("应该正确处理匿名访问", async () => {
      // Arrange
      const anonymousRequest = {} as Request & { user: { id: string } };
      mockSharesService.accessShare.mockResolvedValue(mockShareContentResponse);

      // Act
      const result = await controller.accessShare(
        "ABC123",
        accessDto,
        "192.168.1.1",
        mockHeaders["user-agent"],
        mockHeaders.referer,
        anonymousRequest,
      );

      // Assert
      expect(mockSharesService.accessShare).toHaveBeenCalledWith(
        "ABC123",
        accessDto,
        "192.168.1.1",
        "Mozilla/5.0",
        "https://example.com",
        undefined,
      );
      expect(result.success).toBe(true);
    });

    it("应该正确处理分享不存在的情况", async () => {
      // Arrange
      mockSharesService.accessShare.mockRejectedValue(new Error("分享不存在"));

      // Act & Assert
      await expect(
        controller.accessShare(
          "INVALID",
          accessDto,
          "192.168.1.1",
          mockHeaders["user-agent"],
          mockHeaders.referer,
          mockRequest,
        ),
      ).rejects.toThrow("分享不存在");
    });

    it("应该正确处理分享已过期的情况", async () => {
      // Arrange
      mockSharesService.accessShare.mockRejectedValue(new Error("分享已过期"));

      // Act & Assert
      await expect(
        controller.accessShare(
          "EXPIRED",
          accessDto,
          "192.168.1.1",
          mockHeaders["user-agent"],
          mockHeaders.referer,
          mockRequest,
        ),
      ).rejects.toThrow("分享已过期");
    });

    it("应该正确处理访问限制", async () => {
      // Arrange
      mockSharesService.accessShare.mockRejectedValue(
        new Error("分享访问次数已达上限"),
      );

      // Act & Assert
      await expect(
        controller.accessShare(
          "FULL",
          accessDto,
          "192.168.1.1",
          mockHeaders["user-agent"],
          mockHeaders.referer,
          mockRequest,
        ),
      ).rejects.toThrow("分享访问次数已达上限");
    });

    it("应该正确处理缺少header信息的情况", async () => {
      // Arrange
      mockSharesService.accessShare.mockResolvedValue(mockShareContentResponse);

      // Act
      const result = await controller.accessShare(
        "ABC123",
        accessDto,
        "192.168.1.1",
        undefined as any,
        undefined as any,
        mockRequest,
      );

      // Assert
      expect(mockSharesService.accessShare).toHaveBeenCalledWith(
        "ABC123",
        accessDto,
        "192.168.1.1",
        undefined,
        undefined,
        "user-001",
      );
      expect(result.success).toBe(true);
    });
  });

  describe("getShareStats - GET /stories/:id/share-stats", () => {
    it("应该成功获取分享统计", async () => {
      // Arrange
      mockSharesService.getShareStats.mockResolvedValue(mockShareStatsResponse);

      // Act
      const result = await controller.getShareStats("story-001", mockRequest);

      // Assert
      expect(mockSharesService.getShareStats).toHaveBeenCalledWith(
        "story-001",
        "user-001",
      );
      expect(result).toEqual({
        success: true,
        statusCode: HttpStatus.OK,
        message: "分享统计获取成功",
        data: mockShareStatsResponse,
        timestamp: expect.any(String),
      });
    });

    it("应该正确返回详细统计数据", async () => {
      // Arrange
      const detailedStats = {
        ...mockShareStatsResponse,
        topShares: [
          {
            shareId: "share-001",
            storyTitle: "故事1",
            accessCount: 30,
            createdAt: new Date(),
          },
          {
            shareId: "share-002",
            storyTitle: "故事2",
            accessCount: 20,
            createdAt: new Date(),
          },
          {
            shareId: "share-003",
            storyTitle: "故事3",
            accessCount: 10,
            createdAt: new Date(),
          },
        ],
      };
      mockSharesService.getShareStats.mockResolvedValue(detailedStats);

      // Act
      const result = await controller.getShareStats("story-001", mockRequest);

      // Assert
      expect(result.data.topShares).toHaveLength(3);
      expect(result.data.totalAccessCount).toBe(100);
    });

    it("应该正确处理无统计数据的情况", async () => {
      // Arrange
      const emptyStats = {
        totalShares: 0,
        activeShares: 0,
        totalAccessCount: 0,
        uniqueVisitors: 0,
        accessCountToday: 0,
        accessCountThisWeek: 0,
        accessCountThisMonth: 0,
        topShares: [],
      };
      mockSharesService.getShareStats.mockResolvedValue(emptyStats);

      // Act
      const result = await controller.getShareStats("story-001", mockRequest);

      // Assert
      expect(result.data.totalShares).toBe(0);
      expect(result.data.topShares).toEqual([]);
    });

    it("应该正确处理权限错误", async () => {
      // Arrange
      mockSharesService.getShareStats.mockRejectedValue(
        new Error("无权限查看此故事的分享统计"),
      );

      // Act & Assert
      await expect(
        controller.getShareStats("story-001", mockRequest),
      ).rejects.toThrow("无权限查看此故事的分享统计");
    });
  });

  describe("getUserShares - GET /shares/my", () => {
    it("应该成功获取用户分享列表", async () => {
      // Arrange
      const mockUserShares = [mockShareResponse];
      mockSharesService.getUserShares.mockResolvedValue(mockUserShares);

      // Act
      const result = await controller.getUserShares(mockRequest);

      // Assert
      expect(mockSharesService.getUserShares).toHaveBeenCalledWith("user-001");
      expect(result).toEqual({
        success: true,
        statusCode: HttpStatus.OK,
        message: "分享列表获取成功",
        data: mockUserShares,
        timestamp: expect.any(String),
      });
    });

    it("应该正确处理空分享列表", async () => {
      // Arrange
      mockSharesService.getUserShares.mockResolvedValue([]);

      // Act
      const result = await controller.getUserShares(mockRequest);

      // Assert
      expect(result.data).toEqual([]);
      expect(result.success).toBe(true);
    });

    it("应该正确返回多个分享记录", async () => {
      // Arrange
      const multipleShares = [
        mockShareResponse,
        {
          ...mockShareResponse,
          id: "share-002",
          shareType: ShareType.LIMITED,
          token: "XYZ789",
        },
      ];
      mockSharesService.getUserShares.mockResolvedValue(multipleShares);

      // Act
      const result = await controller.getUserShares(mockRequest);

      // Assert
      expect(result.data).toHaveLength(2);
      expect(result.data[0].shareType).toBe(ShareType.PUBLIC);
      expect(result.data[1].shareType).toBe(ShareType.LIMITED);
    });
  });

  describe("deleteShare - DELETE /shares/:id", () => {
    it("应该成功删除分享", async () => {
      // Arrange
      mockSharesService.deleteShare.mockResolvedValue();

      // Act
      const result = await controller.deleteShare("share-001", mockRequest);

      // Assert
      expect(mockSharesService.deleteShare).toHaveBeenCalledWith(
        "share-001",
        "user-001",
      );
      expect(result).toEqual({
        success: true,
        statusCode: HttpStatus.OK,
        message: "分享删除成功",
        data: null,
        timestamp: expect.any(String),
      });
    });

    it("应该正确处理分享不存在的情况", async () => {
      // Arrange
      mockSharesService.deleteShare.mockRejectedValue(
        new Error("分享记录不存在"),
      );

      // Act & Assert
      await expect(
        controller.deleteShare("non-existent", mockRequest),
      ).rejects.toThrow("分享记录不存在");
    });

    it("应该正确处理权限错误", async () => {
      // Arrange
      mockSharesService.deleteShare.mockRejectedValue(
        new Error("无权限删除此分享"),
      );

      // Act & Assert
      await expect(
        controller.deleteShare("share-001", mockRequest),
      ).rejects.toThrow("无权限删除此分享");
    });

    it("应该正确处理已删除的分享", async () => {
      // Arrange
      mockSharesService.deleteShare.mockRejectedValue(
        new Error("分享已被删除"),
      );

      // Act & Assert
      await expect(
        controller.deleteShare("deleted-share", mockRequest),
      ).rejects.toThrow("分享已被删除");
    });
  });

  describe("错误处理和边界测试", () => {
    it("应该正确处理服务层抛出的各种异常", async () => {
      // Arrange
      const errors = [
        "故事不存在",
        "分享不存在",
        "分享已过期",
        "无权限访问",
        "分享访问次数已达上限",
        "无权限分享此故事",
      ];

      for (const error of errors) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        mockSharesService.createShare.mockRejectedValue(new Error(error));

        // Act & Assert
        await expect(
          controller.createShare(
            "story-001",
            {
              shareType: ShareType.PUBLIC,
            },
            mockRequest,
          ),
        ).rejects.toThrow(error);
      }
    });

    it("应该处理无效的UUID格式", async () => {
      // Arrange
      mockSharesService.deleteShare.mockRejectedValue(
        new Error("无效的ID格式"),
      );

      // Act & Assert
      await expect(
        controller.deleteShare("invalid-uuid", mockRequest),
      ).rejects.toThrow("无效的ID格式");
    });

    it("应该处理请求中缺少用户信息的情况", async () => {
      // Arrange
      const invalidRequest = {} as Request & { user: { id: string } };

      // Act & Assert
      await expect(
        controller.createShare(
          "story-001",
          {
            shareType: ShareType.PUBLIC,
          },
          invalidRequest,
        ),
      ).rejects.toThrow();
    });
  });

  describe("ApiResponse 格式验证", () => {
    it("所有成功响应应该包含正确的 ApiResponse 格式", async () => {
      // Arrange
      mockSharesService.createShare.mockResolvedValue(mockShareResponse);

      // Act
      const result = await controller.createShare(
        "story-001",
        {
          shareType: ShareType.PUBLIC,
        },
        mockRequest,
      );

      // Assert
      expect(result).toEqual({
        success: true,
        message: expect.any(String),
        data: expect.any(Object),
        statusCode: expect.any(Number),
        timestamp: expect.any(String),
      });
    });

    it("应该包含正确的HTTP状态码", async () => {
      // Arrange
      mockSharesService.createShare.mockResolvedValue(mockShareResponse);
      mockSharesService.accessShare.mockResolvedValue(mockShareContentResponse);

      // Act
      const createResult = await controller.createShare(
        "story-001",
        {
          shareType: ShareType.PUBLIC,
        },
        mockRequest,
      );
      const accessResult = await controller.accessShare(
        "ABC123",
        {},
        "192.168.1.1",
        "Mozilla/5.0",
        "https://example.com",
        mockRequest,
      );

      // Assert
      expect(createResult.statusCode).toBe(HttpStatus.CREATED);
      expect(accessResult.statusCode).toBe(HttpStatus.OK);
    });

    it("删除操作应该返回正确的响应格式", async () => {
      // Arrange
      mockSharesService.deleteShare.mockResolvedValue();

      // Act
      const result = await controller.deleteShare("share-001", mockRequest);

      // Assert
      expect(result).toEqual({
        success: true,
        message: "分享删除成功",
        statusCode: HttpStatus.OK,
        data: null,
        timestamp: expect.any(String),
      });
      expect(result.data).toBeNull();
    });
  });

  describe("分享类型处理", () => {
    it("应该正确处理公开分享", async () => {
      // Arrange
      const publicShare = {
        ...mockShareResponse,
        shareType: ShareType.PUBLIC,
        accessLimit: -1,
      };
      mockSharesService.createShare.mockResolvedValue(publicShare);

      // Act
      const result = await controller.createShare(
        "story-001",
        {
          shareType: ShareType.PUBLIC,
        },
        mockRequest,
      );

      // Assert
      expect(result.data.shareType).toBe(ShareType.PUBLIC);
      expect(result.data.accessLimit).toBe(-1);
    });

    it("应该正确处理私密分享", async () => {
      // Arrange
      const privateShare = {
        ...mockShareResponse,
        shareType: ShareType.LIMITED,
        accessLimit: 10,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      };
      mockSharesService.createShare.mockResolvedValue(privateShare);

      // Act
      const result = await controller.createShare(
        "story-001",
        {
          shareType: ShareType.LIMITED,
          accessLimit: 10,
          expiresAt: new Date(
            Date.now() + 7 * 24 * 60 * 60 * 1000,
          ).toISOString(),
        },
        mockRequest,
      );

      // Assert
      expect(result.data.shareType).toBe(ShareType.LIMITED);
      expect(result.data.accessLimit).toBe(10);
      expect(result.data.expiresAt).toBeDefined();
    });
  });

  describe("分享属性处理", () => {
    it("应该正确处理分享属性", async () => {
      // Arrange
      const shareWithMetadata = {
        ...mockShareResponse,
        metadata: {
          allowDownload: true,
          requireLogin: true,
          watermark: false,
        },
      };
      mockSharesService.createShare.mockResolvedValue(shareWithMetadata);

      // Act
      const result = await controller.createShare(
        "story-001",
        {
          shareType: ShareType.PUBLIC,
          metadata: {
            allowDownload: true,
            requireLogin: true,
            watermark: false,
          },
        },
        mockRequest,
      );

      // Assert
      expect(result.data.metadata).toBeDefined();
      expect(result.data.metadata?.allowDownload).toBe(true);
      expect(result.data.metadata?.requireLogin).toBe(true);
      expect(result.data.metadata?.watermark).toBe(false);
    });

    it("应该正确处理分享限制", async () => {
      // Arrange
      const limitedShare = {
        ...mockShareResponse,
        shareType: ShareType.LIMITED,
        hasPassword: true,
        accessLimit: 100,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      };
      mockSharesService.createShare.mockResolvedValue(limitedShare);

      // Act
      const result = await controller.createShare(
        "story-001",
        {
          shareType: ShareType.LIMITED,
          password: "test123",
          accessLimit: 100,
          expiresAt: new Date(
            Date.now() + 7 * 24 * 60 * 60 * 1000,
          ).toISOString(),
        },
        mockRequest,
      );

      // Assert
      expect(result.data.shareType).toBe(ShareType.LIMITED);
      expect(result.data.hasPassword).toBe(true);
      expect(result.data.accessLimit).toBe(100);
      expect(result.data.expiresAt).toBeDefined();
    });
  });
});
