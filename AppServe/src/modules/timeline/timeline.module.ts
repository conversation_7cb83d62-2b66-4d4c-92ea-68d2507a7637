import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { TimelineController } from "./timeline.controller";
import { TimelineService } from "./timeline.service";
import { TimelineEvent } from "./entities/timeline-event.entity";
import { User } from "../users/entities/user.entity";
import { Story } from "../stories/entities/story.entity";
import { AuthModule } from "../auth/auth.module";

@Module({
  imports: [TypeOrmModule.forFeature([TimelineEvent, User, Story]), AuthModule],
  controllers: [TimelineController],
  providers: [TimelineService],
  exports: [TimelineService],
})
export class TimelineModule {}
