import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  <PERSON><PERSON><PERSON><PERSON><PERSON>n,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from "typeorm";
import { User } from "../../users/entities/user.entity";
import { Story } from "../../stories/entities/story.entity";

export enum EventType {
  BIRTH = "birth",
  EDUCATION = "education",
  WORK = "work",
  RELATIONSHIP = "relationship",
  TRAVEL = "travel",
  ACHIEVEMENT = "achievement",
  STORY_PUBLISHED = "story_published",
  STORY_FEATURED = "story_featured",
  MILESTONE = "milestone",
  CUSTOM = "custom",
}

export enum EventStatus {
  ACTIVE = "active",
  ARCHIVED = "archived",
  DELETED = "deleted",
}

export enum EventVisibility {
  PUBLIC = "public",
  FRIENDS_ONLY = "friends_only",
  PRIVATE = "private",
}

export enum EventImportance {
  LOW = 1,
  MEDIUM = 2,
  HIGH = 3,
  CRITICAL = 4,
}

/**
 * 时间线事件实体
 * 记录用户人生历程中的重要事件
 */
@Entity("timeline_events")
@Index(["userId", "status", "visibility"])
@Index(["userId", "eventDate"])
@Index(["eventType", "eventDate"])
@Index(["importance", "eventDate"])
export class TimelineEvent {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({
    type: "uuid",
    comment: "用户ID",
  })
  userId: string;

  @Column({
    type: "varchar",
    length: 200,
    comment: "事件标题",
  })
  title: string;

  @Column({
    type: "text",
    nullable: true,
    comment: "事件描述",
  })
  description: string;

  @Column({
    type: "enum",
    enum: EventType,
    comment: "事件类型",
  })
  eventType: EventType;

  @Column({
    type: "enum",
    enum: EventStatus,
    default: EventStatus.ACTIVE,
    comment: "事件状态",
  })
  status: EventStatus;

  @Column({
    type: "enum",
    enum: EventVisibility,
    default: EventVisibility.PRIVATE,
    comment: "可见性设置",
  })
  visibility: EventVisibility;

  @Column({
    type: "enum",
    enum: EventImportance,
    default: EventImportance.MEDIUM,
    comment: "重要程度",
  })
  importance: EventImportance;

  @Column({
    type: "date",
    comment: "事件发生日期",
  })
  eventDate: Date;

  @Column({
    type: "date",
    nullable: true,
    comment: "事件结束日期（可选，用于跨时间段的事件）",
  })
  endDate: Date;

  @Column({
    type: "varchar",
    length: 200,
    nullable: true,
    comment: "地点",
  })
  location: string;

  @Column({
    type: "simple-array",
    nullable: true,
    comment: "相关人员",
  })
  relatedPeople: string[];

  @Column({
    type: "simple-array",
    nullable: true,
    comment: "事件标签",
  })
  tags: string[];

  @Column({
    type: "uuid",
    nullable: true,
    comment: "关联的故事ID",
  })
  relatedStoryId: string;

  @Column({
    type: "simple-array",
    nullable: true,
    comment: "图片URL列表",
  })
  images: string[];

  @Column({
    type: "simple-array",
    nullable: true,
    comment: "附件URL列表",
  })
  attachments: string[];

  @Column({
    type: "varchar",
    length: 50,
    nullable: true,
    comment: "事件颜色标记",
  })
  color: string;

  @Column({
    type: "varchar",
    length: 50,
    nullable: true,
    comment: "事件图标",
  })
  icon: string;

  @Column({
    type: "boolean",
    default: false,
    comment: "是否为里程碑事件",
  })
  isMilestone: boolean;

  @Column({
    type: "boolean",
    default: false,
    comment: "是否置顶显示",
  })
  isPinned: boolean;

  @Column({
    type: "int",
    default: 0,
    comment: "点赞数",
  })
  likesCount: number;

  @Column({
    type: "int",
    default: 0,
    comment: "评论数",
  })
  commentsCount: number;

  @Column({
    type: "int",
    default: 0,
    comment: "浏览次数",
  })
  viewCount: number;

  @Column({
    type: "jsonb",
    nullable: true,
    comment: "扩展数据",
  })
  customData: {
    category?: string;
    subCategory?: string;
    duration?: string;
    outcome?: string;
    impact?: string;
    lessons?: string[];
    linkedEvents?: string[];
  };

  @Column({
    type: "jsonb",
    nullable: true,
    comment: "扩展字段",
  })
  metadata: Record<string, unknown>;

  @CreateDateColumn({
    type: "timestamp with time zone",
    comment: "创建时间",
  })
  createdAt: Date;

  @UpdateDateColumn({
    type: "timestamp with time zone",
    comment: "更新时间",
  })
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => User, { eager: false })
  @JoinColumn({ name: "userId" })
  user: User;

  @ManyToOne(() => Story, { eager: false })
  @JoinColumn({ name: "relatedStoryId" })
  relatedStory?: Story;
}
