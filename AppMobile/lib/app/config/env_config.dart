/// 环境配置类
class EnvConfig {
  EnvConfig._();
  
  // 环境类型
  static const String environment = String.fromEnvironment(
    'ENVIRONMENT',
    defaultValue: 'development',
  );
  
  // 是否为开发环境
  static bool get isDevelopment => environment == 'development';
  
  // 是否为测试环境
  static bool get isStaging => environment == 'staging';
  
  // 是否为生产环境
  static bool get isProduction => environment == 'production';
  
  // 是否启用日志
  static bool get enableLogging => !isProduction;
  
  // 是否启用性能监控
  static bool get enablePerformanceMonitoring => isProduction;
  
  // 是否启用崩溃报告
  static bool get enableCrashlytics => !isDevelopment;
  
  // 应用配置
  static const String appName = 'YGS';
  static const String appVersion = '1.0.0';
  static const String buildNumber = '1';
  
  // 加密密钥（实际项目中应该从安全存储获取）
  static const String encryptionKey = 'YGS_ENCRYPTION_KEY_2025';
}