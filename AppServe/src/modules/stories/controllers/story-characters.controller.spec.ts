import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import {
  BadRequestException,
  ForbiddenException,
  HttpStatus,
} from "@nestjs/common";
import type { AddCharactersToStoryDto } from "./story-characters.controller";
import { StoryCharactersController } from "./story-characters.controller";
import { StoriesService } from "../stories.service";
import { CharactersService } from "../../characters/characters.service";
import type { AuthRequest } from "../../../common/types/request.types";
import type {
  CharacterResponseDto,
  CharacterQueryDto,
} from "../../characters/dto";
import { PaginatedResponse } from "../../../common/dto/paginated-response.dto";
import { JwtAuthGuard } from "../../auth/guards/jwt-auth.guard";

describe("StoryCharactersController", () => {
  let controller: StoryCharactersController;
  let mockStoriesService: jest.Mocked<StoriesService>;
  let mockCharactersService: jest.Mocked<CharactersService>;

  // Mock数据 - 完整的User实体mock
  const mockUser = {
    id: "user-1",
    phone: "13800138000",
    email: "<EMAIL>",
    passwordHash: "hashedpassword",
    userNumber: "100001",
    nickname: "测试用户",
    username: "testuser",
    avatarUrl: "",
    birthDate: new Date(),
    bio: "测试用户简介",
    aiQuotaRemaining: 3,
    aiQuotaResetDate: new Date(),
    profileDisplaySettings: {
      showBirthday: false,
      showBio: true,
      showStatistics: true,
      showCharacters: true,
      showTimeline: true,
    },
    anomalyStatus: "normal" as const,
    status: "active",
    isActive: true,
    lastLoginAt: new Date(),
    lastLoginIp: "127.0.0.1",
    failedLoginAttempts: 0,
    lockedUntil: null,
    anomalyWarningCount: 0,
    anomalyRestrictionCount: 0,
    lightingRestrictedUntil: null,
    lastInvalidLightingAttempt: null,
    dailyLightingAttempts: 0,
    lightingAttemptResetDate: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
    // 关联关系
    stories: [],
    characters: [],
    lightings: [],
    receivedLightings: [],
    relationships: [],
    relatedRelationships: [],
    shares: [],
    notifications: [],
    refreshTokens: [],
    // 方法
    isLocked: () => false,
    shouldLockAccount: () => false,
    isLightingRestricted: () => false,
    isSuspended: () => false,
    canApplyLighting: () => true,
    shouldResetDailyAttempts: () => false,
    shouldTriggerWarning: () => false,
    shouldTriggerRestriction: () => false,
    shouldTriggerSuspension: () => false,
  };
  const mockAuthRequest = { user: mockUser } as AuthRequest;

  // Mock Theme对象
  const mockTheme = {
    id: "theme-1",
    name: "测试主题",
    description: "这是一个测试主题",
    icon: "📚",
    color: "#FF5733",
    sortOrder: 0,
    isActive: true,
    createdAt: new Date(),
    stories: [],
    getDisplayInfo: () => ({
      id: "theme-1",
      name: "测试主题",
      icon: "📚",
      color: "#FF5733",
      description: "这是一个测试主题",
    }),
  };

  const mockStory = {
    id: "story-1",
    userId: "user-1",
    title: "测试故事",
    content: "这是一个测试故事的内容",
    coverImageUrl: "",
    status: 2,
    viewCount: 0,
    likeCount: 0,
    themeId: "theme-1",
    storyDate: new Date(),
    location: "测试地点",
    images: ["image1.jpg"],
    permissionLevel: "public" as const,
    allowComments: true,
    allowLikes: true,
    allowSharing: true,
    aiSafetyScore: 95.5,
    commentCount: 0,
    createdAt: new Date(),
    updatedAt: new Date(),
    // 关联关系
    user: mockUser,
    theme: mockTheme,
    chapters: [],
    characters: [],
    shares: [],
  };

  const mockCharacters: CharacterResponseDto[] = [
    {
      id: "character-1",
      name: "张三",
      description: "主角",
      gender: "男",
      relationship: "朋友",
      customRelationship: "",
      avatarUrl: "https://example.com/avatar1.jpg",
      isLighted: false,
      lightingCount: 0,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      creator: {
        id: "user-1",
        nickname: "测试用户",
        avatar: "",
        userNumber: "100001",
      },
      storiesCount: 1,
    },
    {
      id: "character-2",
      name: "李四",
      description: "配角",
      gender: "女",
      relationship: "同事",
      customRelationship: "",
      avatarUrl: "https://example.com/avatar2.jpg",
      isLighted: false,
      lightingCount: 0,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      creator: {
        id: "user-1",
        nickname: "测试用户",
        avatar: "",
        userNumber: "100001",
      },
      storiesCount: 1,
    },
  ];

  const mockPaginatedCharacters = new PaginatedResponse(
    mockCharacters,
    2,
    1,
    10,
  );

  beforeEach(async () => {
    const mockStoriesServiceMethods = {
      findById: jest.fn(),
      validateStoryAccess: jest.fn(),
    };

    const mockCharactersServiceMethods = {
      addCharacterToStory: jest.fn(),
      removeCharacterFromStory: jest.fn(),
      getStoryCharacters: jest.fn(),
      getUserCharacters: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [StoryCharactersController],
      providers: [
        {
          provide: StoriesService,
          useValue: mockStoriesServiceMethods,
        },
        {
          provide: CharactersService,
          useValue: mockCharactersServiceMethods,
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({ canActivate: jest.fn(() => true) })
      .compile();

    controller = module.get<StoryCharactersController>(
      StoryCharactersController,
    );
    mockStoriesService = module.get(
      StoriesService,
    ) as jest.Mocked<StoriesService>;
    mockCharactersService = module.get(
      CharactersService,
    ) as jest.Mocked<CharactersService>;

    // Mock console to avoid output during tests
    jest.spyOn(console, "warn").mockImplementation(() => {});
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("addCharactersToStory", () => {
    it("应该成功添加人物到故事", async () => {
      const storyId = "story-1";
      const addCharactersDto: AddCharactersToStoryDto = {
        characterIds: ["character-1", "character-2"],
      };

      mockStoriesService.findById.mockResolvedValue(mockStory);
      mockCharactersService.addCharacterToStory.mockResolvedValue(undefined);

      const result = await controller.addCharactersToStory(
        mockAuthRequest,
        storyId,
        addCharactersDto,
      );

      expect(mockStoriesService.findById).toHaveBeenCalledWith(storyId);
      expect(mockCharactersService.addCharacterToStory).toHaveBeenCalledTimes(
        2,
      );
      expect(mockCharactersService.addCharacterToStory).toHaveBeenCalledWith(
        storyId,
        "character-1",
        "user-1",
      );
      expect(result.success).toBe(true);
      expect(result.message).toBe("成功添加 2 个人物到故事");
      expect(result.data.addedCount).toBe(2);
      expect(result.statusCode).toBe(HttpStatus.CREATED);
    });

    it("应该处理部分添加成功的情况", async () => {
      const storyId = "story-1";
      const addCharactersDto: AddCharactersToStoryDto = {
        characterIds: ["character-1", "character-2"],
      };

      mockStoriesService.findById.mockResolvedValue(mockStory);
      mockCharactersService.addCharacterToStory
        .mockResolvedValueOnce(undefined) // 第一个成功
        .mockRejectedValueOnce(new Error("人物已存在")); // 第二个失败

      const result = await controller.addCharactersToStory(
        mockAuthRequest,
        storyId,
        addCharactersDto,
      );

      expect(result.success).toBe(true);
      expect(result.message).toBe("成功添加 1 个人物到故事");
      expect(result.data.addedCount).toBe(1);
      expect(console.warn).toHaveBeenCalledWith(
        "Failed to add character character-2 to story story-1:",
        "人物已存在",
      );
    });

    it("应该拒绝无权限的用户添加人物", async () => {
      const storyId = "story-1";
      const addCharactersDto: AddCharactersToStoryDto = {
        characterIds: ["character-1"],
      };

      const unauthorizedStory = { ...mockStory, userId: "other-user" };
      mockStoriesService.findById.mockResolvedValue(unauthorizedStory);

      await expect(
        controller.addCharactersToStory(
          mockAuthRequest,
          storyId,
          addCharactersDto,
        ),
      ).rejects.toThrow(ForbiddenException);
    });

    it("应该处理故事不存在的情况", async () => {
      const storyId = "story-1";
      const addCharactersDto: AddCharactersToStoryDto = {
        characterIds: ["character-1"],
      };

      mockStoriesService.findById.mockRejectedValue(new Error("故事不存在"));

      await expect(
        controller.addCharactersToStory(
          mockAuthRequest,
          storyId,
          addCharactersDto,
        ),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe("removeCharacterFromStory", () => {
    it("应该成功从故事中移除人物", async () => {
      const storyId = "story-1";
      const characterId = "character-1";

      mockStoriesService.findById.mockResolvedValue(mockStory);
      mockCharactersService.removeCharacterFromStory.mockResolvedValue(
        undefined,
      );

      await controller.removeCharacterFromStory(
        mockAuthRequest,
        storyId,
        characterId,
      );

      expect(mockStoriesService.findById).toHaveBeenCalledWith(storyId);
      expect(
        mockCharactersService.removeCharacterFromStory,
      ).toHaveBeenCalledWith(storyId, characterId, "user-1");
    });

    it("应该拒绝无权限的用户移除人物", async () => {
      const storyId = "story-1";
      const characterId = "character-1";

      const unauthorizedStory = { ...mockStory, userId: "other-user" };
      mockStoriesService.findById.mockResolvedValue(unauthorizedStory);

      await expect(
        controller.removeCharacterFromStory(
          mockAuthRequest,
          storyId,
          characterId,
        ),
      ).rejects.toThrow(ForbiddenException);
    });

    it("应该处理移除失败的情况", async () => {
      const storyId = "story-1";
      const characterId = "character-1";

      mockStoriesService.findById.mockResolvedValue(mockStory);
      mockCharactersService.removeCharacterFromStory.mockRejectedValue(
        new Error("人物不在故事中"),
      );

      await expect(
        controller.removeCharacterFromStory(
          mockAuthRequest,
          storyId,
          characterId,
        ),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe("getStoryCharacters", () => {
    it("应该成功获取故事人物列表", async () => {
      const storyId = "story-1";
      const query: CharacterQueryDto = { page: 1, limit: 10 };

      mockStoriesService.validateStoryAccess.mockResolvedValue(true);
      mockCharactersService.getStoryCharacters.mockResolvedValue(
        mockPaginatedCharacters,
      );

      const result = await controller.getStoryCharacters(
        mockAuthRequest,
        storyId,
        query,
      );

      expect(mockStoriesService.validateStoryAccess).toHaveBeenCalledWith(
        storyId,
        "user-1",
      );
      expect(mockCharactersService.getStoryCharacters).toHaveBeenCalledWith(
        storyId,
        query,
      );
      expect(result.success).toBe(true);
      expect(result.message).toBe("获取故事人物列表成功");
      expect(result.data).toEqual(mockPaginatedCharacters);
      expect(result.statusCode).toBe(HttpStatus.OK);
    });

    it("应该拒绝无权限的用户获取故事人物", async () => {
      const storyId = "story-1";
      const query: CharacterQueryDto = { page: 1, limit: 10 };

      mockStoriesService.validateStoryAccess.mockResolvedValue(false);

      await expect(
        controller.getStoryCharacters(mockAuthRequest, storyId, query),
      ).rejects.toThrow(ForbiddenException);
    });

    it("应该处理获取失败的情况", async () => {
      const storyId = "story-1";
      const query: CharacterQueryDto = { page: 1, limit: 10 };

      mockStoriesService.validateStoryAccess.mockResolvedValue(true);
      mockCharactersService.getStoryCharacters.mockRejectedValue(
        new Error("数据库错误"),
      );

      await expect(
        controller.getStoryCharacters(mockAuthRequest, storyId, query),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe("updateStoryCharacters", () => {
    it("应该成功批量更新故事人物关联", async () => {
      const storyId = "story-1";
      const updateCharactersDto: AddCharactersToStoryDto = {
        characterIds: ["character-1", "character-3"],
      };

      const currentCharacters = new PaginatedResponse(
        [mockCharacters[0], mockCharacters[1]], // character-1, character-2
        2,
        1,
        1000,
      );

      mockStoriesService.findById.mockResolvedValue(mockStory);
      mockCharactersService.getStoryCharacters.mockResolvedValue(
        currentCharacters,
      );
      mockCharactersService.removeCharacterFromStory.mockResolvedValue(
        undefined,
      );
      mockCharactersService.addCharacterToStory.mockResolvedValue(undefined);

      const result = await controller.updateStoryCharacters(
        mockAuthRequest,
        storyId,
        updateCharactersDto,
      );

      expect(
        mockCharactersService.removeCharacterFromStory,
      ).toHaveBeenCalledWith(storyId, "character-2", "user-1");
      expect(mockCharactersService.addCharacterToStory).toHaveBeenCalledWith(
        storyId,
        "character-3",
        "user-1",
      );
      expect(result.success).toBe(true);
      expect(result.message).toBe("成功更新 2 个人物关联");
      expect(result.data.updatedCount).toBe(2);
    });

    it("应该处理部分更新失败的情况", async () => {
      const storyId = "story-1";
      const updateCharactersDto: AddCharactersToStoryDto = {
        characterIds: ["character-3"],
      };

      const currentCharacters = new PaginatedResponse(
        [mockCharacters[0]],
        1,
        1,
        1000,
      );

      mockStoriesService.findById.mockResolvedValue(mockStory);
      mockCharactersService.getStoryCharacters.mockResolvedValue(
        currentCharacters,
      );
      mockCharactersService.removeCharacterFromStory.mockRejectedValue(
        new Error("移除失败"),
      );
      mockCharactersService.addCharacterToStory.mockRejectedValue(
        new Error("添加失败"),
      );

      const result = await controller.updateStoryCharacters(
        mockAuthRequest,
        storyId,
        updateCharactersDto,
      );

      expect(result.success).toBe(true);
      expect(result.data.updatedCount).toBe(1); // 只算移除的数量
      expect(console.warn).toHaveBeenCalledTimes(2);
    });

    it("应该拒绝无权限的用户更新故事人物", async () => {
      const storyId = "story-1";
      const updateCharactersDto: AddCharactersToStoryDto = {
        characterIds: ["character-1"],
      };

      const unauthorizedStory = { ...mockStory, userId: "other-user" };
      mockStoriesService.findById.mockResolvedValue(unauthorizedStory);

      await expect(
        controller.updateStoryCharacters(
          mockAuthRequest,
          storyId,
          updateCharactersDto,
        ),
      ).rejects.toThrow(ForbiddenException);
    });
  });

  describe("getAvailableCharacters", () => {
    it("应该成功获取可添加的人物列表", async () => {
      const storyId = "story-1";
      const query: CharacterQueryDto = { page: 1, limit: 10 };

      const allUserCharacters = new PaginatedResponse(
        [
          ...mockCharacters,
          { ...mockCharacters[0], id: "character-3", name: "王五" },
        ],
        3,
        1,
        10,
      );

      const storyCharacters = new PaginatedResponse(
        [mockCharacters[0]], // 只有character-1在故事中
        1,
        1,
        1000,
      );

      mockStoriesService.findById.mockResolvedValue(mockStory);
      mockCharactersService.getUserCharacters.mockResolvedValue(
        allUserCharacters,
      );
      mockCharactersService.getStoryCharacters.mockResolvedValue(
        storyCharacters,
      );

      const result = await controller.getAvailableCharacters(
        mockAuthRequest,
        storyId,
        query,
      );

      expect(result.success).toBe(true);
      expect(result.message).toBe("获取可添加人物列表成功");
      expect(result.data.data).toHaveLength(2); // character-2 和 character-3
      expect(result.data.data.map((c) => c.id)).toEqual([
        "character-2",
        "character-3",
      ]);
    });

    it("应该拒绝无权限的用户获取可添加人物", async () => {
      const storyId = "story-1";
      const query: CharacterQueryDto = { page: 1, limit: 10 };

      const unauthorizedStory = { ...mockStory, userId: "other-user" };
      mockStoriesService.findById.mockResolvedValue(unauthorizedStory);

      await expect(
        controller.getAvailableCharacters(mockAuthRequest, storyId, query),
      ).rejects.toThrow(ForbiddenException);
    });

    it("应该处理没有可添加人物的情况", async () => {
      const storyId = "story-1";
      const query: CharacterQueryDto = { page: 1, limit: 10 };

      const allUserCharacters = new PaginatedResponse(
        [mockCharacters[0]],
        1,
        1,
        10,
      );

      const storyCharacters = new PaginatedResponse(
        [mockCharacters[0]], // 所有人物都在故事中
        1,
        1,
        1000,
      );

      mockStoriesService.findById.mockResolvedValue(mockStory);
      mockCharactersService.getUserCharacters.mockResolvedValue(
        allUserCharacters,
      );
      mockCharactersService.getStoryCharacters.mockResolvedValue(
        storyCharacters,
      );

      const result = await controller.getAvailableCharacters(
        mockAuthRequest,
        storyId,
        query,
      );

      expect(result.success).toBe(true);
      expect(result.data.data).toHaveLength(0);
      expect(result.data.total).toBe(0);
    });

    it("应该处理获取失败的情况", async () => {
      const storyId = "story-1";
      const query: CharacterQueryDto = { page: 1, limit: 10 };

      mockStoriesService.findById.mockResolvedValue(mockStory);
      mockCharactersService.getUserCharacters.mockRejectedValue(
        new Error("获取用户人物失败"),
      );

      await expect(
        controller.getAvailableCharacters(mockAuthRequest, storyId, query),
      ).rejects.toThrow(BadRequestException);
    });
  });

  it("应该正确注入依赖的服务", () => {
    expect(controller).toBeDefined();
    expect(mockStoriesService).toBeDefined();
    expect(mockCharactersService).toBeDefined();
  });
});
