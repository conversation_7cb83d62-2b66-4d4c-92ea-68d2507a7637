import {
  IsOptional,
  IsBoolean,
  IsString,
  IsArray,
  IsE<PERSON>,
  MaxLength,
} from "class-validator";

/**
 * 个人主页展示设置更新DTO
 */
export class UpdateDisplaySettingsDto {
  @IsOptional()
  @IsBoolean({
    message: "生日展示设置必须是布尔值",
  })
  showBirthday?: boolean;

  @IsOptional()
  @IsBoolean({
    message: "个人简介展示设置必须是布尔值",
  })
  showBio?: boolean;

  @IsOptional()
  @IsBoolean({
    message: "统计信息展示设置必须是布尔值",
  })
  showStatistics?: boolean;

  @IsOptional()
  @IsBoolean({
    message: "人物展示设置必须是布尔值",
  })
  showCharacters?: boolean;

  @IsOptional()
  @IsBoolean({
    message: "时间线展示设置必须是布尔值",
  })
  showTimeline?: boolean;

  @IsOptional()
  @IsBoolean({
    message: "故事展示设置必须是布尔值",
  })
  showStories?: boolean;

  @IsOptional()
  @IsBoolean({
    message: "收藏展示设置必须是布尔值",
  })
  showBookmarks?: boolean;

  @IsOptional()
  @IsBoolean({
    message: "关注列表展示设置必须是布尔值",
  })
  showFollowList?: boolean;

  @IsOptional()
  @IsBoolean({
    message: "引用集合展示设置必须是布尔值",
  })
  showReferenceCollections?: boolean;
}

export enum HomepageTheme {
  LIGHT = "light",
  DARK = "dark",
  AUTO = "auto",
}

export enum HomepageLayout {
  CLASSIC = "classic",
  MODERN = "modern",
  MINIMAL = "minimal",
  TIMELINE_FOCUSED = "timeline_focused",
}

/**
 * 个人主页高级设置DTO
 */
export class UpdateHomepageSettingsDto extends UpdateDisplaySettingsDto {
  @IsOptional()
  @IsEnum(HomepageTheme, {
    message: "主页主题必须是有效的选项",
  })
  theme?: HomepageTheme;

  @IsOptional()
  @IsEnum(HomepageLayout, {
    message: "主页布局必须是有效的选项",
  })
  layout?: HomepageLayout;

  @IsOptional()
  @IsString({
    message: "个性化简介必须是字符串",
  })
  @MaxLength(500, {
    message: "个性化简介不能超过500个字符",
  })
  customBio?: string;

  @IsOptional()
  @IsString({
    message: "主页背景图片URL必须是字符串",
  })
  backgroundImage?: string;

  @IsOptional()
  @IsArray({
    message: "置顶内容必须是数组",
  })
  @IsString({
    each: true,
    message: "每个置顶内容ID必须是字符串",
  })
  pinnedItems?: string[];

  @IsOptional()
  @IsArray({
    message: "社交链接必须是数组",
  })
  socialLinks?: Array<{
    platform: string;
    url: string;
    display: boolean;
  }>;
}

/**
 * 个人主页数据响应DTO
 */
export class HomepageDataResponseDto {
  // 用户基本信息
  userId: string;
  nickname: string;
  username?: string;
  avatarUrl?: string;
  birthDate?: Date;
  bio?: string;
  userNumber?: string;

  // 展示设置
  displaySettings: {
    showBirthday: boolean;
    showBio: boolean;
    showStatistics: boolean;
    showCharacters: boolean;
    showTimeline: boolean;
    showStories: boolean;
    showBookmarks: boolean;
    showFollowList: boolean;
    showReferenceCollections: boolean;
  };

  // 高级设置
  advancedSettings: {
    theme: HomepageTheme;
    layout: HomepageLayout;
    customBio?: string;
    backgroundImage?: string;
    pinnedItems?: string[];
    socialLinks?: Array<{
      platform: string;
      url: string;
      display: boolean;
    }>;
  };

  // 统计信息
  statistics: {
    storiesCount: number;
    charactersCount: number;
    lightedCharactersCount: number;
    bookmarksCount: number;
    followersCount: number;
    followingCount: number;
    timelineEventsCount: number;
    referenceCollectionsCount: number;
    totalLikesReceived: number;
    totalViewsReceived: number;
    memberSince: Date | null;
    lastActiveAt: Date | null;
  };

  // 内容预览（根据展示设置返回）
  content: {
    // 最新故事
    recentStories?: Array<{
      id: string;
      title: string;
      summary?: string;
      coverImageUrl?: string;
      createdAt: Date;
      likesCount: number;
      viewCount: number;
    }>;

    // 最新人物
    recentCharacters?: Array<{
      id: string;
      name: string;
      description?: string;
      avatarUrl?: string;
      isLighted: boolean;
      createdAt: Date;
    }>;

    // 时间线亮点
    timelineHighlights?: Array<{
      id: string;
      title: string;
      eventType: string;
      eventDate: Date;
      isMilestone: boolean;
    }>;

    // 收藏亮点
    bookmarkHighlights?: Array<{
      id: string;
      title: string;
      storyTitle: string;
      category: string;
      createdAt: Date;
    }>;

    // 引用集合亮点
    referenceCollectionHighlights?: Array<{
      id: string;
      name: string;
      description?: string;
      referenceCount: number;
      coverImage?: string;
      createdAt: Date;
    }>;
  };

  // 权限相关
  canEdit: boolean;
  isFollowing?: boolean;
  isFriend?: boolean;
}

/**
 * 个人统计数据响应DTO
 */
export class PersonalStatsResponseDto {
  userId: string;

  // 基础统计
  basicStats: {
    storiesCount: number;
    charactersCount: number;
    lightedCharactersCount: number;
    bookmarksCount: number;
    timelineEventsCount: number;
    referenceCollectionsCount: number;
  };

  // 社交统计
  socialStats: {
    followersCount: number;
    followingCount: number;
    friendsCount: number;
    mutualFollowsCount: number;
  };

  // 互动统计
  interactionStats: {
    totalLikesReceived: number;
    totalCommentsReceived: number;
    totalViewsReceived: number;
    totalSharesReceived: number;
    averageLikesPerStory: number;
    averageViewsPerStory: number;
  };

  // 创作统计
  creationStats: {
    storiesThisMonth: number;
    charactersThisMonth: number;
    timelineEventsThisMonth: number;
    mostActiveMonth: {
      month: string;
      storiesCount: number;
    };
    longestStoryStreak: number;
    currentStoryStreak: number;
  };

  // 趋势数据
  trendData: {
    // 过去12个月的创作趋势
    monthlyCreation: Array<{
      month: string;
      storiesCount: number;
      charactersCount: number;
      timelineEventsCount: number;
    }>;

    // 过去30天的互动趋势
    dailyInteraction: Array<{
      date: string;
      likesReceived: number;
      viewsReceived: number;
      commentsReceived: number;
    }>;
  };

  // 成就数据
  achievements: Array<{
    id: string;
    title: string;
    description: string;
    icon: string;
    achievedAt: Date;
    category: string;
  }>;

  // 账户信息
  accountInfo: {
    memberSince: Date;
    lastActiveAt?: Date;
    totalDaysActive: number;
    averageDailyActivity: number;
  };
}
