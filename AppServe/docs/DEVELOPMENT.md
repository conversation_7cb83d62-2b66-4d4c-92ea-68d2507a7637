# YGS 开发部署完全指南

> 全面的开发环境搭建、部署流程、问题解决指南

## 📋 快速导航

- [🚀 开发环境搭建](#-开发环境搭建)
- [🔧 环境配置详解](#-环境配置详解)
- [📦 部署流程](#-部署流程)
- [🐛 常见问题解决](#-常见问题解决)
- [🛠️ 运维管理](#️-运维管理)

## 🚀 开发环境搭建

### 前置要求

| 工具 | 版本要求 | 用途 |
|------|----------|------|
| Node.js | 22.16.0+ | 运行时环境 |
| npm | 11.0.0+ | 包管理器 |
| Docker | 20.10+ | 容器化 |
| Docker Compose | 2.0+ | 容器编排 |
| Git | 2.30+ | 版本控制 |

### 快速开始

```bash
# 1. 克隆项目
git clone <repository-url>
cd ygs-v1.0.0/AppServe

# 2. 安装依赖
npm install

# 3. 启动基础服务（PostgreSQL + Redis）
npm run docker:dev

# 4. 执行数据库迁移
npm run migration:run

# 5. 启动开发服务器
npm run start:dev

# 6. 验证服务
curl http://localhost:3000/api/v1/health
```

### 开发环境架构

```
开发环境架构图:
┌─────────────────────────────────────────────────────────────┐
│                    开发环境 (localhost)                      │
├─────────────────────────────────────────────────────────────┤
│  NestJS App (Port 3000)                                     │
│  ├── 开发模式: npm run start:dev                             │
│  ├── 调试模式: npm run start:debug                           │
│  └── 云端模式: npm run cloud:start                          │
├─────────────────────────────────────────────────────────────┤
│  PostgreSQL Container (Port 5432)                          │
│  ├── 本地: localhost:5432                                   │
│  └── 云端: RDS实例                                          │
├─────────────────────────────────────────────────────────────┤
│  Redis Container (Port 6379)                               │
│  ├── 密码: redis_password                                   │
│  └── 数据库: 0                                               │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 环境配置详解

### 环境变量配置

#### 开发环境 (.env)

```bash
# YGS 开发环境配置
NODE_ENV=development
PORT=3000

# 数据库配置 - 本地Docker
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=story_user
DB_PASSWORD=story_password
DB_DATABASE=story_app

# 数据库配置 - 云端RDS（阿里云）
# DB_HOST=rm-bp1234567890.mysql.rds.aliyuncs.com
# DB_PORT=3306
# DB_USERNAME=ygs_user
# DB_PASSWORD=YourCloudPassword

# Redis 配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=redis_password

# JWT 配置 (开发环境)
JWT_SECRET=dev-secret-key-change-in-production
JWT_EXPIRATION=2700                    # 45分钟（秒）
JWT_REFRESH_SECRET=dev-refresh-secret-key
JWT_REFRESH_EXPIRATION=1209600         # 14天（秒）

# OSS 配置（阿里云对象存储）
OSS_REGION=oss-cn-hangzhou
OSS_ACCESS_KEY_ID=your-access-key-id
OSS_ACCESS_KEY_SECRET=your-access-key-secret
OSS_BUCKET=ygs-oss-dev
OSS_ENDPOINT=https://oss-cn-hangzhou.aliyuncs.com
OSS_CDN_DOMAIN=https://image.yougushi.com

# AI 配置
OPENAI_API_KEY=                        # 可选，测试时留空
AI_DAILY_QUOTA=5

# 短信配置 (开发模式)
SMS_MOCK_MODE=true                     # 开发模式使用模拟短信
SMS_MOCK_CODE=123456                   # 固定验证码

# 文件上传配置
UPLOAD_DEST=uploads
MAX_FILE_SIZE=10485760                 # 10MB

# 应用配置
APP_VERSION=0.5.0
APP_NAME=有故事
```

### 数据库配置

#### 本地Docker数据库

```bash
# 启动本地数据库
npm run docker:dev

# 连接测试
docker exec -it story_postgres psql -U story_user -d story_app

# 查看表结构
\dt

# 退出
\q
```

#### 云端RDS数据库

```bash
# 启动云端环境（仅Redis本地，数据库连接RDS）
npm run cloud:start

# 测试RDS连接
node scripts/test-rds-connection.js

# 环境切换
npm run env:cloud    # 切换到云端环境
npm run env:local    # 切换回本地环境
```

### 数据库迁移管理

```bash
# 查看迁移状态
npm run migration:show

# 创建新迁移
npm run migration:generate -- --name=描述性名称

# 运行迁移
npm run migration:run

# 回滚迁移
npm run migration:revert

# 重置数据库（谨慎使用）
docker-compose down -v
docker-compose up -d postgres
npm run migration:run
```

## 📦 部署流程

### 部署架构

```
生产环境架构图:
┌─────────────────────────────────────────────────────────────┐
│                生产服务器 (************)                    │
├─────────────────────────────────────────────────────────────┤
│  Nginx Proxy (Port 80/443)                                 │
│  ├── SSL终止                                                │
│  ├── 反向代理                                               │
│  └── 负载均衡                                               │
├─────────────────────────────────────────────────────────────┤
│  NestJS App Container (Port 3000)                          │
│  ├── 生产模式运行                                           │
│  ├── PM2进程管理                                             │
│  └── 应用监控                                               │
├─────────────────────────────────────────────────────────────┤
│  PostgreSQL Container (Port 5432)                          │
│  ├── 数据持久化                                             │
│  ├── 定期备份                                               │
│  └── 性能优化                                               │
├─────────────────────────────────────────────────────────────┤
│  Redis Container (Port 6379)                               │
│  ├── 缓存管理                                               │
│  ├── 会话存储                                               │
│  └── 分布式锁                                               │
└─────────────────────────────────────────────────────────────┘
```

### 部署方案

#### 完整部署 (deploy-direct.sh)

**适用场景**：
- 首次部署
- 基础设施变更
- 依赖大幅更新
- 数据库迁移

**执行时间**：7-8分钟

```bash
cd /ygs-serve/ygs-v1.0.0/AppServe
git pull origin main
./scripts/deploy-direct.sh
```

#### 快速部署 (deploy-quick.sh)

**适用场景**：
- 日常代码更新
- 配置文件调整
- Bug修复

**执行时间**：1-2分钟

```bash
cd /ygs-serve/ygs-v1.0.0/AppServe
git pull origin main
./scripts/deploy-quick.sh
```

### 部署检查清单

#### 部署前检查
- [ ] 代码已提交并推送到主分支
- [ ] 所有测试通过
- [ ] 环境变量配置正确
- [ ] 数据库迁移文件已准备

#### 部署后验证
- [ ] 服务健康检查通过
- [ ] 数据库连接正常
- [ ] Redis连接正常
- [ ] API接口响应正常
- [ ] 日志无严重错误

```bash
# 健康检查
curl -f http://************/api/v1/health

# API测试
curl -X POST http://************/api/v1/auth/send-sms \
  -H "Content-Type: application/json" \
  -d '{"phone":"13800138000"}'

# 容器状态检查
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
```

## 🐛 常见问题解决

### 1. 容器启动问题

#### PostgreSQL容器无法启动

```bash
# 错误信息
ERROR: database "story_app" does not exist

# 解决方案
docker-compose down -v    # 清理数据卷
docker-compose up -d      # 重新启动

# 验证
docker logs story_postgres
```

#### Redis容器反复重启

```bash
# 错误信息 (ARM64架构)
WARNING Your kernel has a bug that could lead to data corruption

# 解决方案
# 在docker-compose.yml中添加ARM64兼容性配置
command: redis-server --requirepass redis_password --appendonly yes --ignore-warnings ARM64-COW-BUG

# 重启容器
docker-compose restart redis
```

### 2. 数据库迁移问题

#### 迁移文件执行顺序错误

```bash
# 错误信息
TypeORMError: Table "users" does not exist

# 检查迁移文件时间戳
ls -la src/migrations/

# 重命名迁移文件确保正确顺序
mv 1733554800000-CreateCoreTables.ts 1600000000000-CreateCoreTables.ts

# 更新类名和导出名称
export class CreateCoreTables1600000000000 implements MigrationInterface {
  name = 'CreateCoreTables1600000000000';

# 重新运行迁移
npm run migration:run
```

### 3. 应用启动问题

#### JWT配置错误

```bash
# 错误信息
Error: "expiresIn" should be a number of seconds or string representing a timespan

# 解决方案 - 在.env文件中使用数字格式
JWT_EXPIRATION=2700        # 而不是 "7d"
JWT_REFRESH_EXPIRATION=1209600  # 而不是 "14d"

# 重启服务
npm run start:dev
```

#### OSS配置问题

```bash
# 错误信息
OSS客户端初始化失败

# 检查OSS配置
echo $OSS_ACCESS_KEY_ID
echo $OSS_ACCESS_KEY_SECRET
echo $OSS_BUCKET

# 测试OSS连接
node scripts/test-oss-connection.js

# 验证bucket权限
node scripts/test-oss-bucket-policy.js
```

### 4. 网络连接问题

#### 无法连接Redis

```bash
# 检查Redis状态
docker exec story_redis redis-cli --no-auth-warning -a redis_password ping

# 重启Redis容器
docker restart story_redis

# 查看Redis日志
docker logs story_redis -f
```

#### 数据库连接失败

```bash
# 检查数据库状态
docker exec story_postgres pg_isready -U story_user -d story_app

# 重启数据库容器
docker restart story_postgres

# 查看数据库日志
docker logs story_postgres -f
```

### 5. 性能问题

#### 内存不足

```bash
# 检查容器资源使用
docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"

# 释放内存
docker system prune
npm run docker:clean

# 重启服务
npm run docker:dev
npm run start:dev
```

## 🛠️ 运维管理

### 容器管理

```bash
# 查看容器状态
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# 查看资源使用
docker stats story-app-backend story-app-postgres story-app-redis

# 重启服务
docker restart story-app-backend
docker restart story-app-nginx

# 查看日志
docker logs story-app-backend -f --tail 100
docker logs story-app-nginx -f --tail 50

# 清理系统
docker system prune -f
docker volume prune -f
```

### 健康监控

```bash
# 服务健康检查
curl -f http://localhost:3000/api/v1/health

# 详细健康检查
curl -f http://localhost:3000/api/v1/health/detailed

# 数据库连接测试
docker exec story_postgres pg_isready -U story_user -d story_app

# Redis连接测试
docker exec story_redis redis-cli --no-auth-warning -a redis_password ping

# OSS连接测试
node scripts/test-oss-connection.js
```

### 日志管理

```bash
# 查看应用日志
npm run logs:view

# 查看错误日志
npm run logs:error

# 查看容器日志
docker-compose logs -f postgres
docker-compose logs -f redis

# 清理日志
docker-compose logs --tail=0 -f
```

### 数据备份

```bash
# 备份数据库
docker exec story_postgres pg_dump -U story_user -d story_app > backup.sql

# 恢复数据库
docker exec -i story_postgres psql -U story_user -d story_app < backup.sql

# 备份Redis数据
docker exec story_redis redis-cli --no-auth-warning -a redis_password save

# 备份整个数据卷
docker run --rm -v story_postgres_data:/data -v $(pwd):/backup alpine tar czf /backup/postgres_backup.tar.gz -C /data .
```

### 应急处理

#### 快速回滚

```bash
# Git代码回滚
git checkout HEAD~1
./scripts/deploy-quick.sh

# 配置回滚
git checkout HEAD~1 -- nginx/
docker restart story-app-nginx
```

#### 服务重建

```bash
# 停止所有服务
docker stop story-app-nginx story-app-backend story-app-redis story-app-postgres

# 清理容器
docker rm story-app-nginx story-app-backend story-app-redis story-app-postgres

# 重新部署
./scripts/deploy-direct.sh
```

### 性能优化

#### 开发环境优化

```bash
# 启用TypeScript增量编译
npm run build:watch

# 使用Node.js调试器
npm run start:debug

# 内存使用分析
node --inspect dist/main.js
```

#### 生产环境优化

```bash
# 容器资源监控
docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"

# 数据库性能监控
docker exec story_postgres psql -U postgres -d story_app -c "SELECT count(*) FROM pg_stat_activity;"

# Nginx性能监控
curl http://127.0.0.1:8888/nginx_status
```

## 📋 常用命令参考

### 开发环境

```bash
# 环境管理
npm run env:check        # 检查当前环境
npm run env:docker       # 切换到Docker环境
npm run env:cloud        # 切换到云端环境

# 服务启动
npm run start:dev        # 开发模式
npm run start:debug      # 调试模式
npm run start:prod       # 生产模式

# Docker操作
npm run docker:dev       # 启动开发容器
npm run docker:stop      # 停止所有容器
npm run docker:clean     # 清理容器和数据卷

# 云端操作
npm run cloud:start      # 启动云端环境
npm run cloud:redis      # 仅启动Redis容器
npm run cloud:stop       # 停止云端环境
```

### 数据库操作

```bash
# 迁移管理
npm run migration:run            # 运行迁移
npm run migration:show           # 查看迁移状态
npm run migration:revert         # 回滚迁移
npm run migration:generate -- --name=迁移名称

# 数据库连接
node scripts/test-rds-connection.js  # 测试RDS连接
```

### 质量检查

```bash
# 代码检查
npm run lint             # ESLint检查
npm run lint:fix         # 自动修复

# 测试
npm run test             # 单元测试
npm run test:watch       # 监听模式测试
npm run test:e2e         # 端到端测试
npm run test:cov         # 测试覆盖率

# 构建
npm run build            # 构建项目
npm run build:prod       # 生产构建
```

### 生产环境

```bash
# 部署
./scripts/deploy-direct.sh       # 完整部署
./scripts/deploy-quick.sh        # 快速部署
./scripts/diagnose-deployment.sh # 部署诊断

# 监控
docker logs story-app-backend -f # 查看应用日志
curl http://************/api/v1/health  # 健康检查
```

---

---

## ⚠️ 开发助手使用规范

### 🚨 Claude行为约束
- **文档管理**: 严格围绕5个核心文档维护，禁止随意创建说明文档
- **服务启动**: NestJS启动时间5-15秒，看到编译完成即可继续，不要等待过长时间
- **问题修复**: 遇到启动问题立即修复，不要忽略错误继续等待

### 📚 核心文档列表
1. `README.md` - 项目入口导航
2. `docs/DEVELOPMENT.md` - 开发部署指南
3. `docs/ARCHITECTURE.md` - 架构技术文档
4. `docs/CODE_STRUCTURE.md` - 代码结构说明
5. `../对接文档/API接口文档.md` - API接口规范（122个接口完整标准）
6. `docs/TESTING_GUIDE.md` - 测试指南

**原则**: 所有补充内容必须围绕这些核心文档，避免项目臃肿

---

**最后更新**: 2025-07-12  
**文档版本**: v1.1 (添加助手规范)  
**维护团队**: YGS开发团队