import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter/foundation.dart';
import '../../../../design_system/tokens/ygs_colors.dart';
import '../../../../design_system/tokens/ygs_typography.dart';
import '../../../../design_system/components/ygs_skeleton.dart';
import '../../../../design_system/components/ygs_custom_refresh_indicator.dart';
import '../../../../core/responsive/adaptive_spacing.dart';
import '../providers/story_list_provider.dart';
import '../widgets/story_card_widget.dart';

/// 首页 - 专门用于"我关注的"页面展示
class HomePage extends ConsumerWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: YgsColors.backgroundPrimary, // #F4F7FA
      body: const _MyFollowingContent(),
    );
  }
}

/// "我关注的"页面内容 - 严格按照Figma设计稿实现
class _MyFollowingContent extends StatefulWidget {
  const _MyFollowingContent();

  @override
  State<_MyFollowingContent> createState() => _MyFollowingContentState();
}

class _MyFollowingContentState extends State<_MyFollowingContent> 
    with AutomaticKeepAliveClientMixin {
  int _currentTabIndex = 0; // 0: 世界, 1: 关注
  
  // 滚动控制器 - 用于无限滚动
  final ScrollController _scrollController = ScrollController();
  
  @override
  bool get wantKeepAlive => true; // 保持状态活跃
  
  @override
  void initState() {
    super.initState();
    _setupScrollListener();
    // 初始化加载数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _loadInitialData();
      }
    });
  }
  
  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }
  
  /// 设置滚动监听器 - 实现无限滚动
  void _setupScrollListener() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent - 200.h) {
        // 距离底部200像素时开始加载更多
        _loadMoreData();
      }
    });
  }
  
  /// 初始化加载数据
  void _loadInitialData() {
    final container = ProviderScope.containerOf(context);
    container.read(storyListProvider.notifier).loadStories();
  }
  
  /// 加载更多数据
  void _loadMoreData() {
    final container = ProviderScope.containerOf(context);
    container.read(storyListProvider.notifier).loadMoreStories();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // AutomaticKeepAliveClientMixin需要
    return Stack(
      children: [
        // 主要内容
        Column(
          children: [
            // 顶部状态栏和搜索区域
            _buildTopSection(),
            
            // Tab导航栏
            _buildTabNavigation(),
            
            // 内容区域
            Expanded(
              child: _currentTabIndex == 0 
                ? _buildWorldList()
                : _buildMyFollowingList(),
            ),
          ],
        ),
      ],
    );
  }

  /// 顶部状态栏和搜索区域
  Widget _buildTopSection() {
    return Container(
      color: YgsColors.backgroundPrimary,
      child: SafeArea(
        bottom: false, // 只处理顶部安全区域
        child: Padding(
          // 真机专用：大幅减小底部间距以缩小与Tab的距离
          padding: EdgeInsets.fromLTRB(
            36.w, 
            20.h, 
            36.w, 
            AdaptiveSpacing.getSearchToTabSpacing(context), // 企业级自适应间距
          ),
          child: Row(
            children: [
              // 搜索框
              Expanded(
                child: Container(
                  height: 80.h,
                  decoration: BoxDecoration(
                    color: YgsColors.backgroundCard,
                    borderRadius: BorderRadius.circular(23.r),
                    border: Border.all(
                      color: const Color(0xFF030A19),
                      width: 2.w,
                    ),
                  ),
                  child: Row(
                    children: [
                      // 搜索图标
                      Padding(
                        padding: EdgeInsets.only(left: 18.w, right: 11.w),
                        child: Image.asset(
                          'assets/icons/basic/icon-search.png',
                          width: 34.w,
                          height: 34.w,
                          fit: BoxFit.contain, // 确保图标比例正确
                          errorBuilder: (context, error, stackTrace) {
                            return Icon(
                              Icons.search,
                              size: 34.w,
                              color: YgsColors.textSecondary,
                            );
                          },
                        ),
                      ),
                      
                      // 分隔线
                      Container(
                        width: 2.w,
                        height: 34.h,
                        color: const Color(0xFFF0F0F1),
                      ),
                      
                      // 搜索提示文字 - 使用设计稿字体和尺寸
                      Expanded(
                        child: Padding(
                          padding: EdgeInsets.only(left: 15.w),
                          child: Text(
                            '搜索故事链接或有故事号',
                            style: const TextStyle(
                              fontFamily: 'PingFang SC',
                              fontSize: 28,
                              fontWeight: FontWeight.w400,
                              color: Color(0xFF868691),
                              height: 1.5,
                            ).copyWith(fontSize: 28.sp),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              
              // 添加按钮 - 严格按照Figma设计稿实现
              SizedBox(width: 15.w),
              GestureDetector(
                onTap: () {
                  // TODO: 添加创作功能
                },
                child: Container(
                  width: 64.w,
                  height: 64.w,
                  decoration: const BoxDecoration(
                    color: Color(0xFFFF4B4B), // 红色背景
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.add,
                    color: Colors.white,
                    size: 32.w,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Tab导航栏 - 严格按照Figma设计稿实现
  Widget _buildTabNavigation() {
    return Container(
      color: YgsColors.backgroundPrimary,
      child: Column(
        children: [
          // Tab标题 - 世界和关注，扩大上下点击范围
          Row(
            children: [
              // "世界" - 扩大点击区域优化交互体验
              Expanded(
                child: GestureDetector(
                  onTap: () => setState(() => _currentTabIndex = 0),
                  behavior: HitTestBehavior.opaque, // 扩大点击区域到整个Expanded区域
                  child: Container(
                    height: 80.h, // 进一步增加点击区域高度
                    padding: EdgeInsets.symmetric(vertical: 20.h), // 上下内边距扩大点击范围
                    alignment: Alignment.center,
                    child: Text(
                      '世界',
                      style: _currentTabIndex == 0 
                        ? YgsTypography.headlineSmall.copyWith(
                            fontSize: 32.sp,
                            fontWeight: FontWeight.w600,
                            color: YgsColors.textPrimary, // #151523
                          )
                        : YgsTypography.headlineSmall.copyWith(
                            fontSize: 32.sp,
                            fontWeight: FontWeight.w500,
                            color: YgsColors.textSecondary, // #868691
                          ),
                    ),
                  ),
                ),
              ),
              
              // "关注" - 扩大点击区域优化交互体验
              Expanded(
                child: GestureDetector(
                  onTap: () => setState(() => _currentTabIndex = 1),
                  behavior: HitTestBehavior.opaque, // 扩大点击区域到整个Expanded区域
                  child: Container(
                    height: 80.h, // 进一步增加点击区域高度
                    padding: EdgeInsets.symmetric(vertical: 20.h), // 上下内边距扩大点击范围
                    alignment: Alignment.center,
                    child: Text(
                      '关注',
                      style: _currentTabIndex == 1 
                        ? YgsTypography.headlineSmall.copyWith(
                            fontSize: 32.sp,
                            fontWeight: FontWeight.w600,
                            color: YgsColors.textPrimary, // #151523
                          )
                        : YgsTypography.headlineSmall.copyWith(
                            fontSize: 32.sp,
                            fontWeight: FontWeight.w500,
                            color: YgsColors.textSecondary, // #868691
                          ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          
          SizedBox(height: AdaptiveSpacing.getTabInternalSpacing(context)), // 企业级Tab间距
          
          // 分割线和指示器 - 严格按照Figma设计稿实现
          SizedBox(
            height: 4.h, // 按照设计稿调整高度
            child: Stack(
              children: [
                // 分割线 - 位于底部
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    height: 1.h,
                    color: const Color(0xFFDEE0E4),
                  ),
                ),
                
                // 指示器 - 精确对齐文字中心，使用设计稿样式
                if (_currentTabIndex == 0)
                  // 关注tab指示器 - 居中对齐
                  Positioned(
                    bottom: 0, // 与分割线在同一水平线
                    left: (375.w - 128.w) / 2, // 左侧tab区域中心对齐
                    child: Container(
                      width: 128.w, // w-32 = 128px
                      height: 4.h, // h-1 = 4px
                      decoration: const BoxDecoration(
                        color: Color(0xFF0F0F23), // 严格匹配设计稿颜色
                        borderRadius: BorderRadius.all(Radius.circular(2)), // 微圆角
                      ),
                    ),
                  ),
                if (_currentTabIndex == 1)
                  // 世界tab指示器 - 居中对齐
                  Positioned(
                    bottom: 0, // 与分割线在同一水平线
                    left: 375.w + (375.w - 128.w) / 2, // 右侧tab区域中心对齐
                    child: Container(
                      width: 128.w, // w-32 = 128px
                      height: 4.h, // h-1 = 4px
                      decoration: const BoxDecoration(
                        color: Color(0xFF0F0F23), // 严格匹配设计稿颜色
                        borderRadius: BorderRadius.all(Radius.circular(2)), // 微圆角
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// "我关注的"内容列表 - 使用高性能架构
  Widget _buildMyFollowingList() {
    return Consumer(
      builder: (context, ref, child) {
        final storyListState = ref.watch(storyListProvider);
        
        return Container(
          color: YgsColors.backgroundPrimary,
          child: _buildStoryListView(storyListState),
        );
      },
    );
  }

  /// "世界"内容列表 - 使用高性能架构
  Widget _buildWorldList() {
    return Consumer(
      builder: (context, ref, child) {
        final storyListState = ref.watch(storyListProvider);
        
        return Container(
          color: YgsColors.backgroundPrimary,
          child: _buildStoryListView(storyListState),
        );
      },
    );
  }
  
  /// 构建高性能故事列表视图
  Widget _buildStoryListView(StoryListState state) {
    if (state.isLoading && state.stories.isEmpty) {
      // 首次加载状态：显示2张故事卡片骨架图
      return Container(
        color: YgsColors.backgroundPrimary,
        child: const YgsSkeletonList(itemCount: 2),
      );
    }
    
    if (state.error != null && state.stories.isEmpty) {
      // 错误状态
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64.w,
              color: YgsColors.textSecondary,
            ),
            SizedBox(height: 16.h),
            Text(
              state.error!,
              style: YgsTypography.bodyMedium.copyWith(
                color: YgsColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16.h),
            ElevatedButton(
              onPressed: _loadInitialData,
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }
    
    // 使用YGS自定义RefreshIndicator - 三点动画替代系统默认蓝色指示器
    return YgsCustomRefreshIndicator(
      onRefresh: () async {
        final container = ProviderScope.containerOf(context);
        await container.read(storyListProvider.notifier).refreshStories();
      },
      child: ListView.builder(
        controller: _scrollController,
        padding: EdgeInsets.fromLTRB(30.w, 24.h, 30.w, 168.h + 40.h),
        physics: const AlwaysScrollableScrollPhysics(),
        itemCount: state.stories.length + (state.hasNextPage ? 1 : 0),
        cacheExtent: 1000.h, // 预缓存高度
        itemBuilder: (context, index) {
          if (index >= state.stories.length) {
            // 加载更多指示器：使用扁平化三点动画
            return _buildLoadingMoreIndicator(state.isLoadingMore);
          }
          
          final story = state.stories[index];
          return StoryCardWidget(
            key: ValueKey(story.id), // 优化性能的key
            story: story,
          );
        },
      ),
    );
  }
  
  /// 构建加载更多指示器
  Widget _buildLoadingMoreIndicator(bool isLoading) {
    return Container(
      height: 100.h,
      alignment: Alignment.center,
      child: isLoading
          ? const YgsThreeDotsLoading(size: 10.0)
          : const SizedBox.shrink(),
    );
  }

}

