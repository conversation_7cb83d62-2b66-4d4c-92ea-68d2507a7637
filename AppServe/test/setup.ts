import { Test } from "@nestjs/testing";
import { TypeOrmModule } from "@nestjs/typeorm";
import "jest-extended";

// 全局测试环境配置
global.beforeAll(async () => {
  // 设置测试环境变量
  process.env.NODE_ENV = "test";
  process.env.JWT_SECRET = "test-jwt-secret";
  process.env.JWT_EXPIRES_IN = "2700";
  process.env.JWT_REFRESH_EXPIRES_IN = "1209600";
  // Redis配置 - 修复认证问题
  process.env.REDIS_HOST = "localhost";
  process.env.REDIS_PORT = "6379";
  process.env.REDIS_PASSWORD = "redis_password";

  // 数据库连接配置 - 使用阿里云RDS (从环境变量获取)
  process.env.DB_HOST =
    process.env.DB_HOST || "pgm-bp14hy1423z0mg05vo.pg.rds.aliyuncs.com";
  process.env.DB_PORT = process.env.DB_PORT || "5432";
  process.env.DB_USERNAME = process.env.DB_USERNAME || "dev001";
  process.env.DB_PASSWORD = process.env.DB_PASSWORD || "Xy668899!";
  process.env.DB_NAME = process.env.DB_NAME || "ygs-dev";

  // 禁用真实短信服务
  process.env.SMS_MODE = "mock";
  process.env.ALIYUN_ACCESS_KEY_ID = "test";
  process.env.ALIYUN_ACCESS_KEY_SECRET = "test";

  // 禁用真实OSS服务
  process.env.OSS_REGION = "test";
  process.env.OSS_ACCESS_KEY_ID = "test";
  process.env.OSS_ACCESS_KEY_SECRET = "test";
  process.env.OSS_BUCKET = "test-bucket";
});

// 全局测试清理
global.afterAll(async () => {
  // 清理测试数据
  // 关闭数据库连接
  // 清理Redis缓存
});

// Jest扩展配置
expect.extend({
  toBeValidUUID(received) {
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    const pass = uuidRegex.test(received);

    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid UUID`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid UUID`,
        pass: false,
      };
    }
  },

  toBeValidJWT(received) {
    const jwtRegex = /^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/;
    const pass = jwtRegex.test(received);

    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid JWT`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid JWT`,
        pass: false,
      };
    }
  },
});

// 声明自定义匹配器类型
declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace jest {
    interface Matchers<R> {
      toBeValidUUID(): R;
      toBeValidJWT(): R;
    }
  }
}

// 测试工具函数
export class TestUtils {
  /**
   * 生成测试用户数据
   */
  static generateTestUser(overrides: Partial<Record<string, unknown>> = {}) {
    return {
      id: "test-user-id",
      phone: "13800138000",
      email: "<EMAIL>",
      username: "testuser",
      ygsNumber: "YGS001",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      ...overrides,
    };
  }

  /**
   * 生成测试故事数据
   */
  static generateTestStory(overrides: Partial<Record<string, unknown>> = {}) {
    return {
      id: "test-story-id",
      title: "Test Story",
      content: "This is a test story content",
      permissionLevel: "public",
      authorId: "test-user-id",
      createdAt: new Date(),
      updatedAt: new Date(),
      ...overrides,
    };
  }

  /**
   * 生成测试人物数据
   */
  static generateTestCharacter(
    overrides: Partial<Record<string, unknown>> = {},
  ) {
    return {
      id: "test-character-id",
      name: "Test Character",
      description: "This is a test character",
      storyId: "test-story-id",
      realUserId: null,
      isLighted: false,
      lightingCount: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
      ...overrides,
    };
  }

  /**
   * 生成测试点亮申请数据
   */
  static generateTestLightRequest(
    overrides: Partial<Record<string, unknown>> = {},
  ) {
    return {
      id: "test-light-request-id",
      userId: "test-user-id",
      characterId: "test-character-id",
      phone: "13800138000",
      status: "pending",
      createdAt: new Date(),
      updatedAt: new Date(),
      ...overrides,
    };
  }

  /**
   * 创建测试模块
   */
  static async createTestModule(moduleMetadata: Record<string, unknown>) {
    const module = await Test.createTestingModule(moduleMetadata).compile();
    return module;
  }

  /**
   * 创建带有数据库的测试模块
   */
  static async createTestModuleWithDatabase(
    moduleMetadata: Record<string, unknown>,
  ) {
    const module = await Test.createTestingModule({
      ...moduleMetadata,
      imports: [
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        ...((moduleMetadata.imports as any[]) || []),
        TypeOrmModule.forRoot({
          type: "postgres",
          host: process.env.DB_HOST,
          port: parseInt(process.env.DB_PORT || "5432"),
          username: process.env.DB_USERNAME,
          password: process.env.DB_PASSWORD,
          database: process.env.DB_NAME,
          entities: ["src/**/*.entity.ts"],
          synchronize: true,
          dropSchema: true,
          logging: false,
        }),
        // RedisModule.forRoot({
        //   type: "single",
        //   url: process.env.REDIS_URL,
        // }),
      ],
    }).compile();

    return module;
  }

  /**
   * 等待指定时间
   */
  static async sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * 生成随机字符串
   */
  static generateRandomString(length: number = 10): string {
    const chars =
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    let result = "";
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * 生成测试邮箱
   */
  static generateTestEmail(): string {
    return `test-${this.generateRandomString(8)}@example.com`;
  }

  /**
   * 生成测试手机号
   */
  static generateTestPhone(): string {
    return `1380013${Math.floor(Math.random() * 10000)
      .toString()
      .padStart(4, "0")}`;
  }
}

// 导出测试类型
export interface TestUser {
  id: string;
  phone: string;
  email: string;
  username: string;
  ygsNumber: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface TestStory {
  id: string;
  title: string;
  content: string;
  permissionLevel: string;
  authorId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface TestCharacter {
  id: string;
  name: string;
  description: string;
  storyId: string;
  realUserId: string | null;
  isLighted: boolean;
  lightingCount: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface TestLightRequest {
  id: string;
  userId: string;
  characterId: string;
  phone: string;
  status: string;
  createdAt: Date;
  updatedAt: Date;
}
