#!/usr/bin/env node

/**
 * 种子数据统计查询脚本
 * 用于查询生产数据库中各表的记录数量和示例数据
 * 创建时间: 2025-07-28
 */

const { Client } = require('pg');
require('dotenv').config({ path: '.env.production' });

/**
 * 数据库配置
 */
const dbConfig = {
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT),
  database: process.env.DB_NAME,
  user: process.env.DB_USERNAME,
  password: process.env.DB_PASSWORD,
  ssl: false, // 先尝试无SSL连接
  connectionTimeoutMillis: 30000,
  idleTimeoutMillis: 10000,
};

/**
 * 需要统计的表列表
 */
const tables = [
  'users',
  'stories', 
  'characters',
  'themes',
  'comments',
  'comment_likes',
  'bookmarks',
  'notifications',
  'timeline_events',
  'shares',
  'share_access_logs',
  'light_requests',
  'character_lightings',
  'user_follows',
  'friend_groups'
];

/**
 * 查询表记录数量
 */
async function getTableCount(client, tableName) {
  try {
    const result = await client.query(`SELECT COUNT(*) as count FROM "${tableName}"`);
    return parseInt(result.rows[0].count);
  } catch (error) {
    console.log(`⚠️ 表 ${tableName} 不存在或查询失败: ${error.message}`);
    return null;
  }
}

/**
 * 获取表结构信息
 */
async function getTableSchema(client, tableName) {
  try {
    const result = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = $1 
      ORDER BY ordinal_position
    `, [tableName]);
    return result.rows;
  } catch (error) {
    console.log(`⚠️ 获取表 ${tableName} 结构失败: ${error.message}`);
    return [];
  }
}

/**
 * 获取表示例数据
 */
async function getSampleData(client, tableName, limit = 3) {
  try {
    const result = await client.query(`SELECT * FROM "${tableName}" LIMIT $1`, [limit]);
    return result.rows;
  } catch (error) {
    console.log(`⚠️ 获取表 ${tableName} 示例数据失败: ${error.message}`);
    return [];
  }
}

/**
 * 主查询函数
 */
async function queryDatabaseStats() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔗 正在连接到生产数据库...');
    await client.connect();
    console.log('✅ 数据库连接成功');
    console.log(`📊 数据库: ${process.env.DB_NAME} (${process.env.DB_HOST})`);
    console.log('=' * 80);

    const stats = {
      timestamp: new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' }),
      database: process.env.DB_NAME,
      host: process.env.DB_HOST,
      tables: {}
    };

    let totalRecords = 0;
    
    for (const tableName of tables) {
      console.log(`\n📋 正在查询表: ${tableName}`);
      
      // 获取记录数量
      const count = await getTableCount(client, tableName);
      
      if (count !== null) {
        totalRecords += count;
        
        // 获取表结构
        const schema = await getTableSchema(client, tableName);
        
        // 获取示例数据
        const sampleData = await getSampleData(client, tableName, 2);
        
        stats.tables[tableName] = {
          count,
          schema: schema.map(col => ({
            name: col.column_name,
            type: col.data_type,
            nullable: col.is_nullable === 'YES',
            default: col.column_default
          })),
          samples: sampleData
        };
        
        console.log(`   ✅ 记录数: ${count.toLocaleString()}`);
        console.log(`   📝 字段数: ${schema.length}`);
        
        // 显示关键字段
        const keyFields = schema.filter(col => 
          col.column_name.includes('id') || 
          col.column_name.includes('name') || 
          col.column_name.includes('title') ||
          col.column_name.includes('status')
        ).slice(0, 5);
        
        if (keyFields.length > 0) {
          console.log(`   🔑 关键字段: ${keyFields.map(f => f.column_name).join(', ')}`);
        }
        
      } else {
        stats.tables[tableName] = {
          count: 0,
          exists: false,
          message: '表不存在'
        };
      }
    }

    console.log('\n' + '=' * 80);
    console.log('📊 数据库统计汇总');
    console.log('=' * 80);
    console.log(`🕐 查询时间: ${stats.timestamp}`);
    console.log(`💾 数据库: ${stats.database}`);
    console.log(`🌐 主机: ${stats.host}`);
    console.log(`📊 总记录数: ${totalRecords.toLocaleString()}`);
    
    // 按记录数排序显示
    const sortedTables = Object.entries(stats.tables)
      .filter(([name, data]) => data.exists !== false)
      .sort(([,a], [,b]) => b.count - a.count);
    
    console.log('\n📋 各表记录数量 (按数量降序):');
    sortedTables.forEach(([tableName, data]) => {
      console.log(`   ${tableName.padEnd(25)} | ${data.count.toLocaleString().padStart(8)} 条记录`);
    });
    
    // 显示不存在的表
    const missingTables = Object.entries(stats.tables)
      .filter(([name, data]) => data.exists === false)
      .map(([name]) => name);
    
    if (missingTables.length > 0) {
      console.log('\n⚠️ 不存在的表:');
      missingTables.forEach(tableName => {
        console.log(`   ❌ ${tableName}`);
      });
    }

    // 保存统计结果到文件
    const fs = require('fs');
    const statsFile = './database-stats.json';
    fs.writeFileSync(statsFile, JSON.stringify(stats, null, 2), 'utf8');
    console.log(`\n💾 统计结果已保存到: ${statsFile}`);
    
    return stats;
    
  } catch (error) {
    console.error('❌ 数据库查询失败:', error.message);
    throw error;
  } finally {
    await client.end();
    console.log('\n🔐 数据库连接已关闭');
  }
}

// 执行查询
if (require.main === module) {
  queryDatabaseStats()
    .then(() => {
      console.log('\n✅ 种子数据统计查询完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ 查询失败:', error.message);
      process.exit(1);
    });
}

module.exports = { queryDatabaseStats };