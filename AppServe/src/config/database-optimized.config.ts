import { registerAs } from "@nestjs/config";
import type { DataSourceOptions } from "typeorm";
import { DataSource } from "typeorm";
import { Logger } from "@nestjs/common";

const logger = new Logger("DatabaseConfig");

// 环境变量解析工具函数
function getIntegerEnv(
  value: string | undefined,
  defaultValue: number,
): number {
  if (!value) return defaultValue;
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? defaultValue : parsed;
}

export interface DatabaseConfig {
  type: "postgres";
  host: string;
  port: number;
  username: string;
  password: string;
  database: string;
  synchronize: boolean;
  logging: boolean | string[];
  ssl: boolean | object;
  entities: string[];
  migrations: string[];
  autoLoadEntities: boolean;
  // 企业级连接池配置
  extra: {
    // 连接池配置
    max: number;
    min: number;
    acquire: number;
    idle: number;
    evict: number;
    // PostgreSQL特定配置
    statement_timeout: number;
    query_timeout: number;
    lock_timeout: number;
    idle_in_transaction_session_timeout: number;
    // 性能优化
    application_name: string;
    // 连接重试
    connectionTimeoutMillis: number;
    idleTimeoutMillis: number;
    // 安全配置
    ssl?: {
      rejectUnauthorized: boolean;
      ca?: string;
      cert?: string;
      key?: string;
    };
  };
  // 监控配置
  maxQueryExecutionTime: number;
  // 缓存配置
  cache: {
    type: "redis";
    options: {
      host: string;
      port: number;
      password?: string;
      db: number;
    };
    duration: number;
    ignoreErrors: boolean;
  };
}

export const databaseConfig = registerAs("database", (): DatabaseConfig => {
  const isProduction = process.env.NODE_ENV === "production";
  const isDevelopment = process.env.NODE_ENV === "development";

  // 根据环境调整连接池大小
  const getPoolSize = () => {
    if (isProduction) {
      return {
        max: getIntegerEnv(process.env.DB_POOL_MAX, 20),
        min: getIntegerEnv(process.env.DB_POOL_MIN, 5),
      };
    } else {
      return {
        max: getIntegerEnv(process.env.DB_POOL_MAX, 10),
        min: getIntegerEnv(process.env.DB_POOL_MIN, 2),
      };
    }
  };

  const poolConfig = getPoolSize();

  const config: DatabaseConfig = {
    type: "postgres",
    host: process.env.DB_HOST || "localhost",
    port: getIntegerEnv(process.env.DB_PORT, 5432),
    username: process.env.DB_USERNAME || "postgres",
    password: process.env.DB_PASSWORD || "password",
    database: process.env.DB_NAME || "story_app_dev",
    synchronize: isDevelopment && process.env.AUTO_SYNC === "true",
    logging: isDevelopment ? ["query", "error", "warn"] : ["error"],
    ssl:
      process.env.DB_SSL === "true"
        ? {
            rejectUnauthorized: false,
            // 生产环境应配置适当的证书
            ...(isProduction && {
              ca: process.env.DB_SSL_CA,
              cert: process.env.DB_SSL_CERT,
              key: process.env.DB_SSL_KEY,
            }),
          }
        : false,
    entities: [__dirname + "/../**/*.entity{.ts,.js}"],
    migrations: [__dirname + "/../migrations/*{.ts,.js}"],
    autoLoadEntities: true,

    // 企业级连接池和性能配置
    extra: {
      // 连接池配置
      max: poolConfig.max,
      min: poolConfig.min,
      acquire: getIntegerEnv(process.env.DB_ACQUIRE_TIMEOUT, 60000), // 60秒
      idle: getIntegerEnv(process.env.DB_IDLE_TIMEOUT, 300000), // 5分钟
      evict: getIntegerEnv(process.env.DB_EVICT_INTERVAL, 60000), // 1分钟

      // PostgreSQL特定超时配置
      statement_timeout: getIntegerEnv(process.env.DB_STATEMENT_TIMEOUT, 30000), // 30秒
      query_timeout: getIntegerEnv(process.env.DB_QUERY_TIMEOUT, 30000), // 30秒
      lock_timeout: getIntegerEnv(process.env.DB_LOCK_TIMEOUT, 10000), // 10秒
      idle_in_transaction_session_timeout: getIntegerEnv(
        process.env.DB_IDLE_TRANSACTION_TIMEOUT,
        300000,
      ), // 5分钟

      // 应用标识
      application_name: `story-app-${process.env.NODE_ENV}-${process.env.INSTANCE_ID || "unknown"}`,

      // 连接超时
      connectionTimeoutMillis: getIntegerEnv(
        process.env.DB_CONNECTION_TIMEOUT,
        10000,
      ), // 10秒
      idleTimeoutMillis: getIntegerEnv(process.env.DB_IDLE_TIMEOUT, 300000), // 5分钟

      // 性能优化参数
      ...(isProduction && {
        // 生产环境性能调优
        shared_preload_libraries: "pg_stat_statements",
        max_connections: 200,
        shared_buffers: "256MB",
        effective_cache_size: "1GB",
        maintenance_work_mem: "64MB",
        checkpoint_completion_target: 0.9,
        wal_buffers: "16MB",
        default_statistics_target: 100,
        random_page_cost: 1.1,
        effective_io_concurrency: 200,
      }),
    },

    // 慢查询监控
    maxQueryExecutionTime: getIntegerEnv(
      process.env.DB_SLOW_QUERY_THRESHOLD,
      1000,
    ), // 1秒

    // 查询结果缓存配置
    cache: {
      type: "redis",
      options: {
        host: process.env.REDIS_HOST || "localhost",
        port: getIntegerEnv(process.env.REDIS_PORT, 6379),
        password: process.env.REDIS_PASSWORD,
        db: getIntegerEnv(process.env.REDIS_CACHE_DB, 1), // 使用独立的数据库
      },
      duration: getIntegerEnv(process.env.DB_CACHE_DURATION, 300000), // 5分钟
      ignoreErrors: true, // 缓存错误不影响查询
    },
  };

  // 配置验证
  validateDatabaseConfig(config);

  // 记录安全配置信息
  logDatabaseConfig(config);

  return config;
});

// TypeORM CLI配置（用于迁移）
export default new DataSource({
  type: "postgres",
  host: process.env.DB_HOST || "localhost",
  port: getIntegerEnv(process.env.DB_PORT, 5432),
  username: process.env.DB_USERNAME || "postgres",
  password: process.env.DB_PASSWORD || "password",
  database: process.env.DB_NAME || "story_app_dev",
  entities: [__dirname + "/../**/*.entity{.ts,.js}"],
  migrations: [__dirname + "/../migrations/*{.ts,.js}"],
  synchronize: false, // CLI中始终禁用自动同步
  logging: ["error"],
  extra: {
    max: 5, // CLI操作使用较小的连接池
    min: 1,
    connectionTimeoutMillis: 30000,
  },
} as DataSourceOptions);

function validateDatabaseConfig(config: DatabaseConfig): void {
  const errors: string[] = [];

  // 基础配置验证
  if (!config.host) {
    errors.push("Database host is required");
  }

  if (!config.username) {
    errors.push("Database username is required");
  }

  if (!config.password) {
    errors.push("Database password is required");
  }

  if (!config.database) {
    errors.push("Database name is required");
  }

  // 连接池配置验证
  if (config.extra.max < config.extra.min) {
    errors.push("Database pool max size must be greater than min size");
  }

  if (config.extra.max > 50) {
    errors.push("Database pool max size should not exceed 50 connections");
  }

  // 生产环境特殊验证
  if (process.env.NODE_ENV === "production") {
    if (config.synchronize) {
      errors.push("Synchronize must be disabled in production");
    }

    if (config.password.length < 8) {
      errors.push(
        "Database password must be at least 8 characters in production",
      );
    }

    if (!config.ssl) {
      logger.warn("⚠️  SSL is disabled in production environment");
    }
  }

  if (errors.length > 0) {
    throw new Error(
      `Database configuration validation failed:\n${errors.join("\n")}`,
    );
  }
}

function logDatabaseConfig(config: DatabaseConfig): void {
  const safeConfig = {
    type: config.type,
    host: config.host,
    port: config.port,
    database: config.database,
    username: config.username,
    password: maskPassword(config.password),
    ssl: !!config.ssl,
    synchronize: config.synchronize,
    poolSize: {
      max: config.extra.max,
      min: config.extra.min,
    },
    timeouts: {
      acquire: config.extra.acquire,
      idle: config.extra.idle,
      statement: config.extra.statement_timeout,
      query: config.extra.query_timeout,
    },
    cacheEnabled: !!config.cache,
    slowQueryThreshold: config.maxQueryExecutionTime,
  };

  logger.log("Database configuration loaded:", safeConfig);

  // 性能建议
  if (config.extra.max > 20) {
    logger.warn(
      `⚠️  Large connection pool size (${config.extra.max}). Consider optimizing queries first.`,
    );
  }

  if (config.maxQueryExecutionTime > 5000) {
    logger.warn(
      `⚠️  High slow query threshold (${config.maxQueryExecutionTime}ms). Consider lowering for better monitoring.`,
    );
  }
}

function maskPassword(password: string): string {
  if (!password || password.length <= 4) {
    return "***";
  }
  return (
    password.substring(0, 2) +
    "*".repeat(password.length - 4) +
    password.substring(password.length - 2)
  );
}

// 数据库监控和性能分析工具
interface SlowQuery {
  query: string;
  executionTime: number;
  parameters?: unknown[];
  timestamp: Date;
}

export class DatabaseMonitor {
  private static queryTimes: number[] = [];
  private static slowQueries: SlowQuery[] = [];

  static recordQuery(
    query: string,
    executionTime: number,
    parameters?: unknown[],
  ): void {
    this.queryTimes.push(executionTime);

    // 保持数组大小不超过1000
    if (this.queryTimes.length > 1000) {
      this.queryTimes = this.queryTimes.slice(-500);
    }

    // 记录慢查询
    const slowQueryThreshold = getIntegerEnv(
      process.env.DB_SLOW_QUERY_THRESHOLD,
      1000,
    );
    if (executionTime > slowQueryThreshold) {
      this.slowQueries.push({
        query: this.sanitizeQuery(query),
        executionTime,
        parameters: this.sanitizeParameters(parameters),
        timestamp: new Date(),
      });

      // 保持慢查询记录不超过100条
      if (this.slowQueries.length > 100) {
        this.slowQueries = this.slowQueries.slice(-50);
      }

      logger.warn(`🐌 Slow query detected (${executionTime}ms):`, {
        query: this.sanitizeQuery(query),
        executionTime,
      });
    }
  }

  static getStatistics(): {
    totalQueries: number;
    averageTime: number;
    maxTime: number;
    minTime: number;
    slowQueries: SlowQuery[];
    slowQueryCount: number;
    performance: string;
  } {
    const totalQueries = this.queryTimes.length;
    if (totalQueries === 0) {
      return {
        totalQueries: 0,
        averageTime: 0,
        maxTime: 0,
        minTime: 0,
        slowQueries: [],
        slowQueryCount: 0,
        performance: "good",
      };
    }

    const avgTime =
      this.queryTimes.reduce((sum, time) => sum + time, 0) / totalQueries;
    const maxTime = Math.max(...this.queryTimes);
    const minTime = Math.min(...this.queryTimes);

    // 评估性能状态
    const performance =
      avgTime < 100
        ? "excellent"
        : avgTime < 500
          ? "good"
          : avgTime < 1000
            ? "fair"
            : "poor";

    return {
      totalQueries,
      averageTime: Math.round(avgTime),
      maxTime,
      minTime,
      slowQueries: this.slowQueries.slice(-5), // 返回最近5条慢查询
      slowQueryCount: this.slowQueries.length,
      performance,
    };
  }

  private static sanitizeQuery(query: string): string {
    // 移除敏感信息并截断长查询
    return query
      .replace(/password\s*=\s*'[^']*'/gi, "password = '***'")
      .replace(/token\s*=\s*'[^']*'/gi, "token = '***'")
      .substring(0, 200);
  }

  private static sanitizeParameters(parameters?: unknown[]): unknown[] {
    if (!parameters) return [];

    return parameters.map((param) => {
      if (typeof param === "string" && param.length > 50) {
        return param.substring(0, 50) + "...";
      }
      return param;
    });
  }
}
