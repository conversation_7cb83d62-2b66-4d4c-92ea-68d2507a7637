import {
  Controller,
  Post,
  Get,
  Body,
  UseGuards,
  Req,
  HttpStatus,
  HttpCode,
  Logger,
  Query,
  ParseIntPipe,
  DefaultValuePipe,
} from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiBody,
  ApiQuery,
} from "@nestjs/swagger";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { AiService } from "./ai.service";
import {
  GenerateTitleDto,
  GenerateContentDto,
  QuotaStatusResponseDto,
  UsageStatisticsResponseDto,
} from "./dto";
import type { ApiResponse as ApiResponseDto } from "../../common/dto/api-response.dto";
import type { Request } from "express";

// 扩展Request类型以包含user属性
interface AuthenticatedRequest extends Request {
  user: {
    id: string;
    username: string;
    email?: string;
  };
}

/**
 * AI功能控制器 - v1.0.0
 * 提供AI辅助创作功能的RESTful API
 *
 * 核心功能：
 * - 故事标题生成
 * - 故事内容生成
 * - 用户配额管理
 * - 使用统计查询
 *
 * 安全特性：
 * - JWT认证保护
 * - 每日配额限制
 * - 请求频率限制
 * - 输入内容验证
 */
@ApiTags("AI功能")
@Controller("ai")
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class AiController {
  private readonly logger = new Logger(AiController.name);

  constructor(private readonly aiService: AiService) {}

  /**
   * 生成故事标题
   */
  @Post("generate-title")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: "生成故事标题",
    description: "基于关键词生成创意的故事标题，每日有配额限制",
  })
  @ApiBody({
    type: GenerateTitleDto,
    description: "标题生成请求参数，包含关键词数组",
  })
  @ApiResponse({
    status: 200,
    description: "标题生成成功",
    schema: {
      type: "object",
      properties: {
        success: { type: "boolean", example: true },
        data: { type: "string", example: "那些年的夏天" },
        message: { type: "string", example: "标题生成成功" },
      },
    },
  })
  @ApiResponse({
    status: 403,
    description: "配额不足或权限不够",
    schema: {
      type: "object",
      properties: {
        success: { type: "boolean", example: false },
        message: {
          type: "string",
          example: "今日AI生成配额已用完，请明日再试",
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: "请求参数错误",
    schema: {
      type: "object",
      properties: {
        success: { type: "boolean", example: false },
        message: { type: "string", example: "关键词不能为空" },
      },
    },
  })
  async generateTitle(
    @Body() generateTitleDto: GenerateTitleDto,
    @Req() req: AuthenticatedRequest,
  ): Promise<ApiResponseDto<string>> {
    const userId = req.user.id;

    this.logger.log(`用户 ${userId} 请求生成标题`);

    try {
      const title = await this.aiService.generateStoryTitle(
        userId,
        generateTitleDto.keywords,
      );

      return {
        success: true,
        data: title,
        message: "标题生成成功",
        statusCode: 200,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error(
        `标题生成失败: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined,
      );
      throw error;
    }
  }

  /**
   * 生成故事内容
   */
  @Post("generate-content")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: "生成故事内容",
    description: "基于提示词生成故事内容，每日有配额限制",
  })
  @ApiBody({
    type: GenerateContentDto,
    description: "内容生成请求参数，包含生成提示",
  })
  @ApiResponse({
    status: 200,
    description: "内容生成成功",
    schema: {
      type: "object",
      properties: {
        success: { type: "boolean", example: true },
        data: { type: "string", example: "在一个阳光明媚的下午..." },
        message: { type: "string", example: "内容生成成功" },
      },
    },
  })
  @ApiResponse({
    status: 403,
    description: "配额不足或权限不够",
  })
  @ApiResponse({
    status: 400,
    description: "请求参数错误",
  })
  async generateContent(
    @Body() generateContentDto: GenerateContentDto,
    @Req() req: AuthenticatedRequest,
  ): Promise<ApiResponseDto<string>> {
    const userId = req.user.id;

    this.logger.log(`用户 ${userId} 请求生成内容`);

    try {
      const content = await this.aiService.generateStoryContent(
        userId,
        generateContentDto.prompt,
      );

      return {
        success: true,
        data: content,
        message: "内容生成成功",
        statusCode: 200,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error(
        `内容生成失败: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined,
      );
      throw error;
    }
  }

  /**
   * 查询配额状态
   */
  @Get("quota-status")
  @ApiOperation({
    summary: "查询配额状态",
    description: "查询当前用户的AI功能使用配额情况",
  })
  @ApiResponse({
    status: 200,
    description: "配额状态查询成功",
    type: QuotaStatusResponseDto,
  })
  async getQuotaStatus(
    @Req() req: AuthenticatedRequest,
  ): Promise<ApiResponseDto<QuotaStatusResponseDto>> {
    const userId = req.user.id;

    this.logger.debug(`用户 ${userId} 查询配额状态`);

    try {
      const remainingQuota = await this.aiService.getRemainingQuota(userId);

      const quotaStatus: QuotaStatusResponseDto = {
        remainingQuota,
        totalDailyQuota: 3,
        usedQuota: 3 - remainingQuota,
        resetTime: this.getNextResetTime(),
      };

      return {
        success: true,
        data: quotaStatus,
        message: "配额状态查询成功",
        statusCode: 200,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error(
        `配额状态查询失败: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined,
      );
      throw error;
    }
  }

  /**
   * 查询使用统计
   */
  @Get("usage-statistics")
  @ApiOperation({
    summary: "查询使用统计",
    description: "查询用户AI功能使用统计数据",
  })
  @ApiQuery({
    name: "days",
    required: false,
    type: Number,
    description: "统计天数，默认为30天",
    example: 30,
  })
  @ApiResponse({
    status: 200,
    description: "使用统计查询成功",
    type: UsageStatisticsResponseDto,
  })
  async getUsageStatistics(
    @Query("days", new DefaultValuePipe(30), ParseIntPipe) days: number,
    @Req() req: AuthenticatedRequest,
  ): Promise<ApiResponseDto<UsageStatisticsResponseDto>> {
    const userId = req.user.id;

    this.logger.debug(`用户 ${userId} 查询使用统计，天数: ${days}`);

    try {
      // 限制查询范围
      const validDays = Math.min(Math.max(days, 1), 365);

      const statistics = await this.aiService.getUserUsageStatistics(
        userId,
        validDays,
      );

      const usageStatistics: UsageStatisticsResponseDto = {
        period: validDays,
        totalUsage: statistics.totalUsage,
        successfulUsage: statistics.successfulUsage,
        failureRate:
          statistics.totalUsage > 0
            ? ((statistics.totalUsage - statistics.successfulUsage) /
                statistics.totalUsage) *
              100
            : 0,
        totalCost: statistics.totalCost,
        averageCostPerRequest: statistics.averageCostPerRequest,
        usageByFeature: statistics.usageByFeature,
      };

      return {
        success: true,
        data: usageStatistics,
        message: "使用统计查询成功",
        statusCode: 200,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error(
        `使用统计查询失败: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined,
      );
      throw error;
    }
  }

  /**
   * 重置每日配额（管理员功能）
   */
  @Post("reset-quota")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: "重置每日配额",
    description: "手动重置用户的每日AI配额（管理员功能）",
  })
  @ApiResponse({
    status: 200,
    description: "配额重置成功",
    schema: {
      type: "object",
      properties: {
        success: { type: "boolean", example: true },
        message: { type: "string", example: "配额重置成功" },
      },
    },
  })
  async resetQuota(
    @Req() req: AuthenticatedRequest,
  ): Promise<ApiResponseDto<null>> {
    const userId = req.user.id;

    this.logger.log(`用户 ${userId} 请求重置配额`);

    try {
      await this.aiService.resetDailyQuota(userId);

      return {
        success: true,
        data: null,
        message: "配额重置成功",
        statusCode: 200,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error(
        `配额重置失败: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined,
      );
      throw error;
    }
  }

  /**
   * 获取下一次配额重置时间
   */
  private getNextResetTime(): Date {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);
    return tomorrow;
  }
}
