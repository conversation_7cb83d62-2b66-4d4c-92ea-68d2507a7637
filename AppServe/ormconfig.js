const { DataSource } = require('typeorm');
require('dotenv').config();
require('ts-node/register');

module.exports = new DataSource({
  type: 'postgres',
  host: process.env.DB_HOST || '127.0.0.1',
  port: parseInt(process.env.DB_PORT || '5432', 10),
  username: process.env.DB_USERNAME || 'story_user',
  password: process.env.DB_PASSWORD || 'story_password',
  database: process.env.DB_NAME || process.env.DB_DATABASE || 'story_app',
  entities: ['src/**/*.entity.ts'],
  migrations: ['src/migrations/*.ts'],
  synchronize: false,
  logging: process.env.DB_LOGGING === 'true' || process.env.NODE_ENV === 'development',
  // 阿里云RDS SSL连接支持
  ssl: process.env.DB_SSL === 'true' ? { 
    rejectUnauthorized: false,
    sslmode: 'require'
  } : false,
  // 云端数据库连接配置优化
  extra: {
    connectionTimeoutMillis: parseInt(process.env.DB_CONNECTION_TIMEOUT, 10) || 30000,
    idleTimeoutMillis: parseInt(process.env.DB_IDLE_TIMEOUT, 10) || 10000,
    max: parseInt(process.env.DB_MAX_CONNECTIONS, 10) || 10,
    min: parseInt(process.env.DB_MIN_CONNECTIONS, 10) || 2,
  },
}); 