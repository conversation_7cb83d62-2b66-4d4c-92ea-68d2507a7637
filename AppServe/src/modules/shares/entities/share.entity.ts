import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from "typeorm";
import { User } from "../../users/entities/user.entity";
import { Story } from "../../stories/entities/story.entity";

export enum ShareType {
  PUBLIC = "public",
  LIMITED = "limited",
}

export enum ShareStatus {
  ACTIVE = "active",
  EXPIRED = "expired",
  DISABLED = "disabled",
}

/**
 * 分享实体
 * 存储故事分享链接信息
 */
@Entity("shares")
@Index(["token"], { unique: true })
@Index(["storyId", "status"])
@Index(["userId", "createdAt"])
@Index(["expiresAt"])
export class Share {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({
    type: "varchar",
    length: 32,
    unique: true,
    comment: "分享令牌（用于生成分享链接）",
  })
  token: string;

  @Column({
    type: "uuid",
    comment: "故事ID",
  })
  storyId: string;

  @Column({
    type: "uuid",
    comment: "分享者用户ID",
  })
  userId: string;

  @Column({
    type: "enum",
    enum: ShareType,
    comment: "分享类型：public-公开，limited-有限制",
  })
  shareType: ShareType;

  @Column({
    type: "enum",
    enum: ShareStatus,
    default: ShareStatus.ACTIVE,
    comment: "分享状态",
  })
  status: ShareStatus;

  @Column({
    type: "varchar",
    length: 20,
    nullable: true,
    comment: "访问密码（当shareType为limited时使用）",
  })
  password: string;

  @Column({
    type: "int",
    default: -1,
    comment: "访问次数限制（-1表示无限制）",
  })
  accessLimit: number;

  @Column({
    type: "int",
    default: 0,
    comment: "已访问次数",
  })
  accessCount: number;

  @Column({
    type: "timestamp with time zone",
    nullable: true,
    comment: "过期时间（null表示永不过期）",
  })
  expiresAt: Date;

  @Column({
    type: "text",
    nullable: true,
    comment: "分享描述",
  })
  description: string;

  @Column({
    type: "jsonb",
    nullable: true,
    comment: "分享时的故事快照",
  })
  storySnapshot: {
    title: string;
    summary?: string;
    coverImage?: string;
    authorName: string;
  };

  @Column({
    type: "jsonb",
    nullable: true,
    comment: "扩展字段",
  })
  metadata: Record<string, unknown>;

  @CreateDateColumn({
    type: "timestamp with time zone",
    comment: "创建时间",
  })
  createdAt: Date;

  @UpdateDateColumn({
    type: "timestamp with time zone",
    comment: "更新时间",
  })
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => Story, { eager: false })
  @JoinColumn({ name: "storyId" })
  story: Story;

  @ManyToOne(() => User, { eager: false })
  @JoinColumn({ name: "userId" })
  user: User;
}
