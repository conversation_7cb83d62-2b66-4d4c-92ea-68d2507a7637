/**
 * 缓存配置测试 - 企业级配置验证
 * 测试Redis缓存配置和TTL设置
 */

import { CacheConfigService } from "./cache.config";
import type { ConfigService } from "@nestjs/config";

describe("CacheConfigService - 企业级配置测试", () => {
  let cacheConfigService: CacheConfigService;
  let mockConfigService: jest.Mocked<ConfigService>;

  beforeEach(() => {
    // Mock ConfigService
    mockConfigService = {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      get: jest.fn((key: string, defaultValue?: any) => {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const config: Record<string, any> = {
          NODE_ENV: "test",
          REDIS_DISABLED: "false",
          CACHE_TYPE: "redis",
          REDIS_HOST: "localhost",
          REDIS_PORT: 6379,
          REDIS_PASSWORD: undefined,
          REDIS_DB: 0,
          CACHE_TTL: 300,
        };
        return config[key] !== undefined ? config[key] : defaultValue;
      }),
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } as any;

    cacheConfigService = new CacheConfigService(mockConfigService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should be defined", () => {
    expect(cacheConfigService).toBeDefined();
  });

  describe("createCacheOptions方法", () => {
    it("should be defined", () => {
      expect(cacheConfigService.createCacheOptions).toBeDefined();
      expect(typeof cacheConfigService.createCacheOptions).toBe("function");
    });

    it("should return memory cache for test environment", async () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      mockConfigService.get.mockImplementation(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (key: string, defaultValue?: any) => {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const config: Record<string, any> = {
            NODE_ENV: "test",
            CACHE_TTL: 300,
          };
          return config[key] !== undefined ? config[key] : defaultValue;
        },
      );

      const options = await cacheConfigService.createCacheOptions();

      expect(options).toBeDefined();
      expect(options.isGlobal).toBe(true);
      expect(options.ttl).toBe(300 * 1000); // 转换为毫秒
      expect(options.store).toBeUndefined(); // 内存缓存无store配置
    });

    it("should return memory cache when Redis is disabled", async () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      mockConfigService.get.mockImplementation(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (key: string, defaultValue?: any) => {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const config: Record<string, any> = {
            NODE_ENV: "production",
            REDIS_DISABLED: "true",
            CACHE_TTL: 600,
          };
          return config[key] !== undefined ? config[key] : defaultValue;
        },
      );

      const options = await cacheConfigService.createCacheOptions();

      expect(options).toBeDefined();
      expect(options.isGlobal).toBe(true);
      expect(options.ttl).toBe(600 * 1000);
    });

    it("should return memory cache when cache type is memory", async () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      mockConfigService.get.mockImplementation(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (key: string, defaultValue?: any) => {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const config: Record<string, any> = {
            NODE_ENV: "production",
            CACHE_TYPE: "memory",
            CACHE_TTL: 300,
          };
          return config[key] !== undefined ? config[key] : defaultValue;
        },
      );

      const options = await cacheConfigService.createCacheOptions();

      expect(options).toBeDefined();
      expect(options.isGlobal).toBe(true);
      expect(options.ttl).toBe(300 * 1000);
    });

    it("should use default TTL when not specified", async () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      mockConfigService.get.mockImplementation(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (key: string, defaultValue?: any) => {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const config: Record<string, any> = {
            NODE_ENV: "test",
          };
          return config[key] !== undefined ? config[key] : defaultValue;
        },
      );

      const options = await cacheConfigService.createCacheOptions();

      expect(options.ttl).toBe(300 * 1000); // 默认5分钟转换为毫秒
    });
  });

  describe("ConfigService集成", () => {
    it("should call ConfigService.get with correct parameters", async () => {
      await cacheConfigService.createCacheOptions();

      expect(mockConfigService.get).toHaveBeenCalledWith("NODE_ENV");
      expect(mockConfigService.get).toHaveBeenCalledWith("REDIS_DISABLED");
      expect(mockConfigService.get).toHaveBeenCalledWith("CACHE_TYPE", "redis");
    });

    it("should handle missing configuration gracefully", async () => {
      mockConfigService.get.mockReturnValue(undefined);

      const options = await cacheConfigService.createCacheOptions();

      expect(options).toBeDefined();
      expect(options.isGlobal).toBe(true);
    });
  });

  describe("错误处理", () => {
    it("should not throw when creating cache options", async () => {
      await expect(
        cacheConfigService.createCacheOptions(),
      ).resolves.not.toThrow();
    });

    it("should return valid cache options structure", async () => {
      const options = await cacheConfigService.createCacheOptions();

      expect(options).toHaveProperty("isGlobal");
      expect(options).toHaveProperty("ttl");
      expect(options.isGlobal).toBe(true);
      expect(typeof options.ttl).toBe("number");
      expect(options.ttl).toBeGreaterThan(0);
    });
  });
});
