import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  Index,
  JoinColumn,
} from "typeorm";
import { User } from "../../users/entities/user.entity";

/**
 * AI使用记录实体
 * 记录用户AI功能使用情况，用于配额管理和成本控制
 */
@Entity("ai_usage_logs")
@Index(["userId", "createdAt"]) // 用户使用记录查询
@Index(["featureType"]) // 功能类型统计
@Index(["createdAt"]) // 时间范围查询
export class AiUsageLog {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ name: "user_id", comment: "用户ID" })
  userId: string;

  @Column({ name: "feature_type", length: 30, comment: "AI功能类型" })
  featureType:
    | "title_generation"
    | "content_generation"
    | "content_optimization";

  @Column({ name: "prompt_tokens", nullable: true, comment: "输入令牌数" })
  promptTokens: number;

  @Column({ name: "completion_tokens", nullable: true, comment: "输出令牌数" })
  completionTokens: number;

  @Column({ name: "total_tokens", nullable: true, comment: "总令牌数" })
  totalTokens: number;

  @Column({
    name: "cost_estimate",
    type: "decimal",
    precision: 10,
    scale: 4,
    nullable: true,
    comment: "预估成本",
  })
  costEstimate: number;

  @Column({ default: true, comment: "是否执行成功" })
  success: boolean;

  @CreateDateColumn({ name: "created_at", comment: "创建时间" })
  createdAt: Date;

  // 关联关系
  @ManyToOne(() => User, { onDelete: "CASCADE" })
  @JoinColumn({ name: "user_id" })
  user: User;

  // 业务方法
  /**
   * 获取功能类型描述
   */
  getFeatureTypeDescription(): string {
    const typeMap = {
      title_generation: "标题生成",
      content_generation: "内容生成",
      content_optimization: "内容优化",
    };
    return typeMap[this.featureType] || "未知功能";
  }

  /**
   * 计算成本效率
   */
  getCostEfficiency(): number {
    if (!this.totalTokens || !this.costEstimate) return 0;
    return this.totalTokens / this.costEstimate;
  }
}
