# 多阶段构建 - 开发环境
FROM node:18-alpine AS development

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apk add --no-cache curl

# 创建非root用户
RUN adduser -S nestjs -u 1001

# 复制 package 文件
COPY package*.json ./

# 安装依赖
RUN npm ci

# 复制源代码
COPY . .

# 更改所有者
RUN chown -R nestjs:root /app

# 切换到非root用户
USER nestjs

# 暴露端口
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/api/v1/healt || exit 1

# 开发环境命令
CMD ["npm", "run", "start:dev"]

# 生产环境构建阶段
FROM node:18-alpine AS builder

WORKDIR /app

# 复制 package 文件
COPY package*.json ./

# 安装所有依赖（包括开发依赖）
RUN npm ci

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 生产环境运行阶段
FROM node:18-alpine AS production

# 安装 tini 用于信号处理
RUN apk add --no-cache tini curl

WORKDIR /app

# 创建非root用户
RUN adduser -S nestjs -u 1001

# 复制 package 文件
COPY package*.json ./

# 只安装生产依赖
RUN npm ci --only=production && npm cache clean --force

# 从构建阶段复制编译后的代码
COPY --from=builder /app/dist ./dist

# 复制必要的配置文件
COPY --chown=nestjs:root ormconfig.js ./

# 更改所有者
RUN chown -R nestjs:root /app

# 切换到非root用户
USER nestjs

# 暴露端口
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# 使用 tini 作为入口点
ENTRYPOINT ["/sbin/tini", "--"]

# 生产环境命令
CMD ["node", "dist/main"] 