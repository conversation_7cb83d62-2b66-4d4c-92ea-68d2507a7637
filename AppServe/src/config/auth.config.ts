import { registerAs } from "@nestjs/config";
import { getStringEnv, parseIntEnv } from "../common/utils/env.utils";

export const authConfig = registerAs("auth", () => ({
  jwtSecret: getStringEnv(
    process.env.JWT_SECRET,
    "story-app-secret-key-change-in-production",
  ),
  jwtExpiresIn: getStringEnv(process.env.JWT_EXPIRES_IN, "7d"),
  jwtRefreshSecret: getStringEnv(
    process.env.JWT_REFRESH_SECRET,
    "story-app-refresh-secret-key",
  ),
  jwtRefreshExpiresIn: getStringEnv(process.env.JWT_REFRESH_EXPIRES_IN, "30d"),
  bcryptRounds: parseIntEnv(process.env.BCRYPT_ROUNDS, 12),
  smsProvider: getStringEnv(process.env.SMS_PROVIDER, "aliyun"),
  smsAccessKeyId: getStringEnv(process.env.SMS_ACCESS_KEY_ID, ""),
  smsAccessKeySecret: getStringEnv(process.env.SMS_ACCESS_KEY_SECRET, ""),
  smsSignName: getStringEnv(process.env.SMS_SIGN_NAME, "有故事APP"),
  smsTemplateCode: getStringEnv(process.env.SMS_TEMPLATE_CODE, "SMS_123456789"),
  verificationCodeExpiration: parseIntEnv(
    process.env.VERIFICATION_CODE_EXPIRATION,
    300,
  ), // 5分钟

  // 会话管理配置 - 支持动态调整和后期扩展
  maxConcurrentSessions: parseIntEnv(process.env.MAX_CONCURRENT_SESSIONS, 1), // 初期设为1
  sessionGracePeriod: parseIntEnv(process.env.SESSION_GRACE_PERIOD, 30), // 30秒宽限期
  sessionCleanupInterval: parseIntEnv(
    process.env.SESSION_CLEANUP_INTERVAL,
    3600,
  ), // 1小时清理一次
  enableSessionMonitoring:
    getStringEnv(process.env.ENABLE_SESSION_MONITORING, "true") === "true",
}));
