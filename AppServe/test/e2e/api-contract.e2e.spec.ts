/**
 * API契约和接口规范E2E测试
 *
 * 验证API接口是否严格遵守企业级API设计规范
 * 包括响应格式、状态码、错误处理、数据验证等
 */

import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import type { INestApplication } from "@nestjs/common";
import * as request from "supertest";
import { AppModule } from "../../src/app.module";

describe("API Contract and Interface Standards (E2E)", () => {
  let app: INestApplication;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let server: any;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();

    const port = Math.floor(Math.random() * 1000) + 4000;
    await app.listen(port);
    server = app.getHttpServer();
  }, 30000);

  afterAll(async () => {
    if (app) {
      await app.close();
    }
  });

  describe("📋 标准响应格式验证", () => {
    it("should return standardized success response format", async () => {
      const response = await request(server).get("/health").expect(200);

      // 验证标准成功响应格式
      expect(response.body).toMatchObject({
        status: expect.any(String),
        timestamp: expect.any(String),
        uptime: expect.any(Number),
      });
    });

    it("should return standardized error response format for 404", async () => {
      const response = await request(server)
        .get("/non-existent-endpoint")
        .expect(404);

      // 验证标准错误响应格式
      expect(response.body).toMatchObject({
        statusCode: 404,
        message: expect.any(String),
        timestamp: expect.any(String),
        path: "/non-existent-endpoint",
      });
    });

    it("should return standardized validation error format", async () => {
      const response = await request(server)
        .post("/auth/login")
        .send({
          // 缺少必需字段
        })
        .expect(400);

      expect(response.body).toMatchObject({
        statusCode: 400,
        message: expect.any(String),
        timestamp: expect.any(String),
      });
    });
  });

  describe("🔒 HTTP状态码规范验证", () => {
    it("should return correct status codes for different scenarios", async () => {
      // 200 - 成功获取资源
      await request(server).get("/health").expect(200);

      // 400 - 请求参数错误
      await request(server)
        .post("/auth/login")
        .send({ invalid: "data" })
        .expect(400);

      // 401 - 未授权访问
      await request(server).get("/users/profile").expect(401);

      // 404 - 资源不存在
      await request(server).get("/stories/non-existent-id").expect(404);

      // 405 - 方法不允许
      await request(server).patch("/health").expect(405);
    });
  });

  describe("📊 数据验证和类型检查", () => {
    it("should validate phone number format", async () => {
      const response = await request(server)
        .post("/auth/register")
        .send({
          phone: "invalid-phone",
          password: "Test123456!",
          confirmPassword: "Test123456!",
        })
        .expect(400);

      expect(response.body.message).toMatch(/手机号格式不正确|phone/i);
    });

    it("should validate password strength", async () => {
      const response = await request(server)
        .post("/auth/register")
        .send({
          phone: "13800138000",
          password: "123", // 弱密码
          confirmPassword: "123",
        })
        .expect(400);

      expect(response.body.message).toMatch(/密码强度不够|password/i);
    });

    it("should validate required fields", async () => {
      const response = await request(server)
        .post("/stories")
        .set("Authorization", "Bearer invalid-token")
        .send({
          // 缺少title和content
        })
        .expect(401); // 先返回认证错误

      // 如果有有效token，应该返回400验证错误
      expect([400, 401]).toContain(response.status);
    });
  });

  describe("🌐 国际化和本地化支持", () => {
    it("should support Chinese error messages", async () => {
      const response = await request(server)
        .post("/auth/register")
        .send({
          phone: "invalid",
        })
        .expect(400);

      // 检查错误消息是否包含中文
      const message = response.body.message;
      const hasChinese = /[\u4e00-\u9fa5]/.test(message);
      expect(hasChinese).toBe(true);
    });

    it("should handle Accept-Language header", async () => {
      const response = await request(server)
        .get("/health")
        .set("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
        .expect(200);

      expect(response.body).toBeDefined();
    });
  });

  describe("⏱️ 性能和超时处理", () => {
    it("should respond within acceptable time limits", async () => {
      const startTime = Date.now();

      await request(server).get("/stories").query({ limit: 10 }).expect(200);

      const responseTime = Date.now() - startTime;
      expect(responseTime).toBeLessThan(2000); // 2秒内响应
    });

    it("should handle timeout scenarios gracefully", async () => {
      // 模拟可能较慢的操作
      const response = await request(server)
        .get("/stories")
        .query({
          limit: 100,
          includeMeta: true,
          includeStats: true,
        })
        .timeout(5000);

      expect([200, 408, 504]).toContain(response.status);
    });
  });

  describe("🔐 安全头和CORS配置", () => {
    it("should set proper security headers", async () => {
      const response = await request(server).get("/health").expect(200);

      // 验证安全头
      expect(response.headers["x-content-type-options"]).toBe("nosniff");
      expect(response.headers["x-frame-options"]).toBeDefined();
      expect(response.headers["x-xss-protection"]).toBeDefined();
    });

    it("should handle CORS requests properly", async () => {
      const response = await request(server)
        .options("/health")
        .set("Origin", "https://example.com")
        .set("Access-Control-Request-Method", "GET")
        .expect(200);

      expect(response.headers["access-control-allow-origin"]).toBeDefined();
      expect(response.headers["access-control-allow-methods"]).toBeDefined();
    });

    it("should prevent sensitive information leakage", async () => {
      const response = await request(server).get("/health").expect(200);

      // 确保响应中不包含敏感信息
      const responseString = JSON.stringify(response.body);
      expect(responseString).not.toMatch(/password|secret|key|token/i);
    });
  });

  describe("📈 分页和查询参数标准", () => {
    it("should support standard pagination parameters", async () => {
      const response = await request(server)
        .get("/stories")
        .query({
          page: 1,
          limit: 5,
          sortBy: "createdAt",
          sortOrder: "desc",
        })
        .expect(200);

      expect(response.body.data).toHaveProperty("stories");
      expect(response.body.data).toHaveProperty("pagination");

      if (response.body.data.pagination) {
        expect(response.body.data.pagination).toMatchObject({
          page: expect.any(Number),
          limit: expect.any(Number),
          total: expect.any(Number),
          totalPages: expect.any(Number),
        });
      }
    });

    it("should validate pagination parameters", async () => {
      const response = await request(server)
        .get("/stories")
        .query({
          page: -1, // 无效页码
          limit: 1000, // 过大的限制
        })
        .expect(400);

      expect(response.body.message).toMatch(/page|limit|参数|无效/i);
    });
  });

  describe("🏷️ 内容类型和编码支持", () => {
    it("should handle JSON content type correctly", async () => {
      const response = await request(server)
        .post("/auth/login")
        .set("Content-Type", "application/json")
        .send(
          JSON.stringify({
            phone: "13800138000",
            password: "Test123456!",
          }),
        )
        .expect(400); // 预期验证失败或其他响应

      expect(response.headers["content-type"]).toMatch(/application\/json/);
    });

    it("should handle UTF-8 encoding properly", async () => {
      const chineseText = "这是中文测试内容";

      const response = await request(server)
        .post("/auth/register")
        .send({
          phone: "13800138000",
          password: "Test123456!",
          confirmPassword: "Test123456!",
          nickname: chineseText,
        })
        .expect(400); // 预期验证失败

      // 确保响应正确处理中文
      expect(response.body.message).toBeDefined();
    });
  });

  describe("🔄 幂等性和状态管理", () => {
    it("should handle idempotent operations correctly", async () => {
      // GET请求应该是幂等的
      const response1 = await request(server).get("/health").expect(200);

      const response2 = await request(server).get("/health").expect(200);

      // 健康检查响应格式应该一致
      expect(response1.body.status).toBe(response2.body.status);
    });

    it("should maintain proper state between requests", async () => {
      // 连续的健康检查应该显示uptime增长
      const response1 = await request(server).get("/health").expect(200);

      await new Promise((resolve) => setTimeout(resolve, 100));

      const response2 = await request(server).get("/health").expect(200);

      expect(response2.body.uptime).toBeGreaterThan(response1.body.uptime);
    });
  });

  describe("📊 监控和可观测性", () => {
    it("should provide metrics endpoint", async () => {
      const response = await request(server).get("/health/metrics").expect(200);

      expect(response.body).toHaveProperty("requestCount");
      expect(response.body).toHaveProperty("averageResponseTime");
      expect(response.body).toHaveProperty("errorRate");
    });

    it("should include request ID in responses", async () => {
      const response = await request(server).get("/health").expect(200);

      // 检查是否有请求追踪ID
      expect(
        response.headers["x-request-id"] || response.body.requestId,
      ).toBeDefined();
    });
  });
});
