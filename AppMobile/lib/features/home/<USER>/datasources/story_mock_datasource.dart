import '../../domain/entities/story_entity.dart';

/// 模拟数据源 - 用于开发和测试
class StoryMockDataSource {
  static const List<Map<String, dynamic>> _mockStoryData = [
    {
      'id': '1',
      'title': '从车库到上市的十年梦想',
      'content': '今天公司终于成功上市，回想十年前在车库里的第一行代码，那些日日夜夜的拼搏终于开花结果。记得当时只有我和两个合伙人，每天工作到深夜，吃着泡面讨论产品方向。现在看着股价开盘就涨停，眼泪忍不住流下来，这一路走来真的太不容易了，感谢所有支持我们的人。',
      'authorId': 'user_001',
      'authorName': '李明辉',
      'authorAvatar': 'assets/images/avatars/青年男.png',
      'categoryTag': '#创业路上',
      'categoryColor': 0xFF2E8B57,
      'categoryIcon': 'assets/icons/themes/icon-the road to entrepreneurship-02.png',
      'createdAt': '2025-08-01T10:30:00Z',
      'updatedAt': '2025-08-01T10:30:00Z',
      'location': '深圳',
      'likeCount': 1234,
      'isLiked': false,
      'images': [
        'assets/images/test/story_image_2.png',
        'assets/images/test/story_image_3.png',
        'assets/images/test/story_image_4.png',
      ],
      'interactionAvatars': [
        {
          'id': 'ia_001',
          'userId': 'user_101',
          'avatarUrl': 'assets/images/avatars/avatar-04.png',
          'isLightedUp': true,
          'interactionTime': '2025-08-01T11:00:00Z',
        },
        {
          'id': 'ia_002',
          'userId': 'user_102',
          'avatarUrl': 'assets/images/avatars/avatar-05.png',
          'isLightedUp': false,
          'interactionTime': '2025-08-01T11:15:00Z',
        },
        {
          'id': 'ia_003',
          'userId': 'user_103',
          'avatarUrl': 'assets/images/avatars/avatar-06.png',
          'isLightedUp': true,
          'interactionTime': '2025-08-01T11:30:00Z',
        },
      ],
      'isPublished': true,
      'isArchived': false,
    },
    {
      'id': '2',
      'title': '聪明的第一次骑自行车',
      'content': '今天聪明终于学会了骑自行车，看着他摇摇晃晃但坚持不懈的样子，我想起了自己小时候学车的经历。从最开始的恐惧到后来的兴奋，孩子们总是能给我们带来惊喜。他骑了一圈又一圈，每次都更加自信，脸上洋溢着成功的喜悦，这种纯真的快乐真的很感染人。',
      'authorId': 'user_002',
      'authorName': 'Floyd Miles',
      'authorAvatar': 'assets/images/avatars/少年-女.png',
      'categoryTag': '#孩子们',
      'categoryColor': 0xFF507DAF,
      'categoryIcon': 'assets/icons/themes/icon-children-02.png',
      'createdAt': '2025-07-28T15:20:00Z',
      'updatedAt': '2025-07-28T15:20:00Z',
      'location': '上海',
      'likeCount': 888,
      'isLiked': true,
      'images': [
        'assets/images/test/story_image_1.png',
        'assets/images/test/story_image_2.png',
        'assets/images/test/story_image_3.png',
        'assets/images/test/story_image_4.png',
      ],
      'interactionAvatars': [
        {
          'id': 'ia_004',
          'userId': 'user_104',
          'avatarUrl': 'assets/images/avatars/avatar-01.png',
          'isLightedUp': true,
          'interactionTime': '2025-07-28T16:00:00Z',
        },
        {
          'id': 'ia_005',
          'userId': 'user_105',
          'avatarUrl': 'assets/images/avatars/avatar-02.png',
          'isLightedUp': true,
          'interactionTime': '2025-07-28T16:15:00Z',
        },
        {
          'id': 'ia_006',
          'userId': 'user_106',
          'avatarUrl': 'assets/images/avatars/avatar-03.png',
          'isLightedUp': false,
          'interactionTime': '2025-07-28T16:30:00Z',
        },
      ],
      'isPublished': true,
      'isArchived': false,
    },
    {
      'id': '3',
      'title': '至回初恋地',
      'content': '和志明一起回到我们初次约会的咖啡厅，那份美好的回忆如潮水般涌来，时光荏苒，爱念如初。还记得那天他紧张得连咖啡都洒了，我们聊了整整三个小时，从电影聊到人生理想。现在再坐在这里，他依然会为我点同样的拿铁，加两颗方糖，这些小细节让我觉得很幸福。',
      'authorId': 'user_003',
      'authorName': '王春娇',
      'authorAvatar': 'assets/images/avatars/青年女.png',
      'categoryTag': '#爱情故事',
      'categoryColor': 0xFFE91E63,
      'categoryIcon': 'assets/icons/themes/icon-romance-02.png',
      'createdAt': '2025-07-27T09:45:00Z',
      'updatedAt': '2025-07-27T09:45:00Z',
      'location': '北京',
      'likeCount': 567,
      'isLiked': false,
      'images': [
        'assets/images/test/story_image_1.png',
        'assets/images/test/story_image_2.png',
      ],
      'interactionAvatars': [
        {
          'id': 'ia_007',
          'userId': 'user_107',
          'avatarUrl': 'assets/images/avatars/avatar-04.png',
          'isLightedUp': false,
          'interactionTime': '2025-07-27T10:00:00Z',
        },
        {
          'id': 'ia_008',
          'userId': 'user_108',
          'avatarUrl': 'assets/images/avatars/avatar-05.png',
          'isLightedUp': true,
          'interactionTime': '2025-07-27T10:15:00Z',
        },
        {
          'id': 'ia_009',
          'userId': 'user_109',
          'avatarUrl': 'assets/images/avatars/avatar-06.png',
          'isLightedUp': false,
          'interactionTime': '2025-07-27T10:30:00Z',
        },
      ],
      'isPublished': true,
      'isArchived': false,
    },
    {
      'id': '4',
      'title': '短故事测试',
      'content': '简短的内容测试。',
      'authorId': 'user_004',
      'authorName': '测试用户',
      'authorAvatar': 'assets/images/avatars/青年男.png',
      'categoryTag': '#闪光瞬间',
      'categoryColor': 0xFFFFB800,
      'categoryIcon': 'assets/icons/themes/icon-flash-02.png',
      'createdAt': '2025-08-02T14:20:00Z',
      'updatedAt': '2025-08-02T14:20:00Z',
      'location': '广州',
      'likeCount': 234,
      'isLiked': false,
      'images': [
        'assets/images/test/story_image_1.png',
      ],
      'interactionAvatars': [
        {
          'id': 'ia_010',
          'userId': 'user_110',
          'avatarUrl': 'assets/images/avatars/avatar-01.png',
          'isLightedUp': true,
          'interactionTime': '2025-08-02T15:00:00Z',
        },
      ],
      'isPublished': true,
      'isArchived': false,
    },
    {
      'id': '5',
      'title': '超长文本省略号测试',
      'content': '这是一个非常长的故事，用来测试省略号的效果。这个故事会包含很多很多的文字，目的是为了验证当文字超过三行时，系统能够正确地显示省略号。我们需要确保用户体验是良好的，不会因为文字过长而影响整体的页面布局。长长的文字会被自动截断，并在末尾显示三个点来表示还有更多内容。这样的设计既保持了页面的整洁，又给用户提供了完整内容的暗示。测试测试测试，更多文字内容，继续添加更多字符来确保一定会超过三行的显示限制。',
      'authorId': 'user_005',
      'authorName': '长文测试',
      'authorAvatar': 'assets/images/avatars/青年女.png',
      'categoryTag': '#学生时代',
      'categoryColor': 0xFF9C27B0,
      'categoryIcon': 'assets/icons/themes/icon-school days-02.png',
      'createdAt': '2025-08-02T16:45:00Z',
      'updatedAt': '2025-08-02T16:45:00Z',
      'location': '杭州',
      'likeCount': 1567,
      'isLiked': true,
      'images': [
        'assets/images/test/story_image_2.png',
        'assets/images/test/story_image_3.png',
      ],
      'interactionAvatars': [
        {
          'id': 'ia_011',
          'userId': 'user_111',
          'avatarUrl': 'assets/images/avatars/avatar-02.png',
          'isLightedUp': true,
          'interactionTime': '2025-08-02T17:00:00Z',
        },
        {
          'id': 'ia_012',
          'userId': 'user_112',
          'avatarUrl': 'assets/images/avatars/avatar-03.png',
          'isLightedUp': false,
          'interactionTime': '2025-08-02T17:15:00Z',
        },
      ],
      'isPublished': true,
      'isArchived': false,
    },
  ];

  /// 生成大量模拟数据用于性能测试
  static List<StoryEntity> generateMockStories({
    int count = 50,
    String? category,
  }) {
    final stories = <StoryEntity>[];
    
    for (int i = 0; i < count; i++) {
      final baseIndex = i % _mockStoryData.length;
      final baseData = Map<String, dynamic>.from(_mockStoryData[baseIndex]);
      
      // 修改ID和时间确保唯一性
      baseData['id'] = 'story_${i + 1}';
      baseData['createdAt'] = DateTime.now()
          .subtract(Duration(hours: i))
          .toIso8601String();
      baseData['updatedAt'] = baseData['createdAt'];
      
      // 随机化点赞数
      baseData['likeCount'] = 100 + (i * 23) % 2000;
      
      // 过滤分类
      if (category != null && !baseData['categoryTag'].toString().contains(category)) {
        continue;
      }
      
      stories.add(StoryEntity.fromJson(baseData));
    }
    
    return stories;
  }

  /// 模拟API响应延迟
  static Future<StoryListResponse> getMockStoryList({
    int page = 1,
    int limit = 10,
    String? category,
  }) async {
    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 500));
    
    final allStories = generateMockStories(count: 100, category: category);
    final startIndex = (page - 1) * limit;
    final endIndex = (startIndex + limit).clamp(0, allStories.length);
    
    final pageStories = allStories.sublist(
      startIndex.clamp(0, allStories.length),
      endIndex,
    );
    
    return StoryListResponse(
      stories: pageStories,
      totalCount: allStories.length,
      page: page,
      limit: limit,
      hasNextPage: endIndex < allStories.length,
      hasPreviousPage: page > 1,
    );
  }
}