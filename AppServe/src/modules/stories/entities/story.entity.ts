import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  ManyToMany,
  JoinTable,
} from "typeorm";
import { User } from "../../users/entities/user.entity";
import { StoryChapter } from "./story-chapter.entity";
import { Character } from "../../characters/entities/character.entity";
import { StoryShare } from "./story-share.entity";
import { Theme } from "./theme.entity";

export enum StoryStatus {
  DRAFT = 1,
  PUBLISHED = 2,
  ARCHIVED = 3,
}

@Entity("stories")
export class Story {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ name: "user_id" })
  userId: string;

  @Column({ length: 255, comment: "故事标题" })
  title: string;

  @Column({ type: "text", nullable: true, comment: "故事内容" })
  content: string;

  @Column({ name: "cover_image_url", nullable: true, comment: "封面图片URL" })
  coverImageUrl: string;

  @Column({
    type: "smallint",
    default: StoryStatus.DRAFT,
    comment: "状态：1草稿 2发布 3归档",
  })
  status: StoryStatus;

  @Column({ name: "view_count", default: 0, comment: "浏览次数" })
  viewCount: number;

  @Column({ name: "like_count", default: 0, comment: "点赞次数" })
  likeCount: number;

  @Column({ name: "theme_id", nullable: true, comment: "主题ID" })
  themeId: string;

  @Column({
    name: "story_date",
    type: "date",
    nullable: true,
    comment: "故事发生日期",
  })
  storyDate: Date;

  @Column({ length: 255, nullable: true, comment: "故事发生地点" })
  location: string;

  @Column({ type: "jsonb", nullable: true, comment: "故事图片数组" })
  images: string[];

  @Column({
    name: "permission_level",
    length: 20,
    default: "public",
    comment: "权限级别：private/friends/public/characters_only",
  })
  permissionLevel: "private" | "friends" | "public" | "characters_only";

  @Column({ name: "allow_comments", default: true, comment: "是否允许评论" })
  allowComments: boolean;

  @Column({ name: "allow_likes", default: true, comment: "是否允许点赞" })
  allowLikes: boolean;

  @Column({ name: "allow_sharing", default: true, comment: "是否允许分享" })
  allowSharing: boolean;

  @Column({
    name: "ai_safety_score",
    type: "decimal",
    precision: 3,
    scale: 2,
    nullable: true,
    comment: "AI安全检测评分",
  })
  aiSafetyScore: number;

  @Column({ name: "comment_count", default: 0, comment: "评论数量" })
  commentCount: number;

  @CreateDateColumn({ name: "created_at", comment: "创建时间" })
  createdAt: Date;

  @UpdateDateColumn({ name: "updated_at", comment: "更新时间" })
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => User, (user) => user.stories, { onDelete: "CASCADE" })
  @JoinColumn({ name: "user_id" })
  user: User;

  @ManyToOne(() => Theme, (theme) => theme.stories, { nullable: true })
  @JoinColumn({ name: "theme_id" })
  theme: Theme;

  @OneToMany(() => StoryChapter, (chapter) => chapter.story)
  chapters: StoryChapter[];

  @ManyToMany(() => Character, (character) => character.stories)
  @JoinTable({
    name: "story_characters",
    joinColumn: { name: "story_id", referencedColumnName: "id" },
    inverseJoinColumn: { name: "character_id", referencedColumnName: "id" },
  })
  characters: Character[];

  @OneToMany(() => StoryShare, (share) => share.story)
  shares: StoryShare[];
}
