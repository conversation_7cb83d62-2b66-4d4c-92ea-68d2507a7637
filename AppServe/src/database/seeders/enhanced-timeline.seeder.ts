/**
 * 企业级时间线种子数据
 *
 * 基于 YGS v1.0.0 个人时间线系统设计
 * 涵盖人生各个重要时刻和里程碑
 */

import { USER_ROLES } from "./enhanced-user.seeder";

export interface EnhancedTimelineEventSeedData {
  userUsername: string;
  title: string;
  description: string;
  eventDate: string;
  eventType:
    | "birth"
    | "education"
    | "work"
    | "relationship"
    | "achievement"
    | "travel"
    | "milestone"
    | "loss"
    | "celebration"
    | "other";
  importance: "critical" | "major" | "normal" | "minor";
  isPublic: boolean;
  location?: string;
  tags: string[];
  attachments?: string[];
  relatedStoryTitle?: string;
}

export const ENHANCED_TIMELINE_EVENT_SEED_DATA: EnhancedTimelineEventSeedData[] =
  [
    // ========== 张明远的人生时间线 ==========
    {
      userUsername: USER_ROLES.ZHANG_MINGYUAN,
      title: "出生在美丽的杭州",
      description:
        "1990年5月15日，我来到了这个世界。父母说那是一个阳光明媚的春日，正如他们对我人生的期待。",
      eventDate: "1990-05-15",
      eventType: "birth",
      importance: "critical",
      isPublic: true,
      location: "杭州市第一人民医院",
      tags: ["出生", "家庭", "起点"],
    },
    {
      userUsername: USER_ROLES.ZHANG_MINGYUAN,
      title: "考入理想大学",
      description:
        "经过高三一年的努力，终于考入了心仪的大学。那一刻，觉得所有的辛苦都值得了。",
      eventDate: "2008-09-01",
      eventType: "education",
      importance: "major",
      isPublic: true,
      location: "某大学",
      tags: ["大学", "教育", "成长"],
    },
    {
      userUsername: USER_ROLES.ZHANG_MINGYUAN,
      title: "遇见室友兄弟",
      description:
        "大学第一天，遇到了李明、王强、赵华三个室友。从那时起，我们开始了四年的兄弟情谊。",
      eventDate: "2008-09-02",
      eventType: "relationship",
      importance: "major",
      isPublic: true,
      location: "大学宿舍",
      tags: ["室友", "友情", "兄弟"],
      relatedStoryTitle: "那年夏天，我们约定十年后再聚",
    },
    {
      userUsername: USER_ROLES.ZHANG_MINGYUAN,
      title: "大学毕业典礼",
      description:
        "四年大学时光结束，与室友们立下十年之约。那天我们都哭了，但都相信友谊会一直延续。",
      eventDate: "2012-06-15",
      eventType: "milestone",
      importance: "major",
      isPublic: true,
      location: "大学礼堂",
      tags: ["毕业", "友情", "约定"],
      attachments: ["graduation_photo.jpg"],
      relatedStoryTitle: "那年夏天，我们约定十年后再聚",
    },
    {
      userUsername: USER_ROLES.ZHANG_MINGYUAN,
      title: "第一份工作入职",
      description:
        "毕业后进入一家金融公司，开始了我的职业生涯。虽然忙碌，但充满了对未来的期待。",
      eventDate: "2012-07-20",
      eventType: "work",
      importance: "major",
      isPublic: false,
      location: "上海",
      tags: ["工作", "金融", "职场"],
    },
    {
      userUsername: USER_ROLES.ZHANG_MINGYUAN,
      title: "决定创业",
      description:
        "工作八年后，决定离开稳定的工作去创业。这是人生中最重要的决定之一，既兴奋又忐忑。",
      eventDate: "2020-01-01",
      eventType: "work",
      importance: "major",
      isPublic: true,
      location: "上海",
      tags: ["创业", "梦想", "冒险"],
      relatedStoryTitle: "创业路上，感谢有你们",
    },
    {
      userUsername: USER_ROLES.ZHANG_MINGYUAN,
      title: "公司渡过难关",
      description:
        "在朋友们的帮助下，公司终于渡过了最困难的时期。这让我更加珍惜友情的可贵。",
      eventDate: "2021-06-30",
      eventType: "achievement",
      importance: "major",
      isPublic: true,
      location: "上海",
      tags: ["成功", "友情", "坚持"],
      relatedStoryTitle: "创业路上，感谢有你们",
    },

    // ========== 李小红的人生时间线 ==========
    {
      userUsername: USER_ROLES.LI_XIAOHONG,
      title: "生在美丽的苏州",
      description:
        "1992年8月20日出生，父母给我取名小红，希望我像红花一样美丽绽放。",
      eventDate: "1992-08-20",
      eventType: "birth",
      importance: "critical",
      isPublic: true,
      location: "苏州市妇幼保健院",
      tags: ["出生", "家庭", "美好愿望"],
    },
    {
      userUsername: USER_ROLES.LI_XIAOHONG,
      title: "大学时光开始",
      description:
        "考入大学，开始了人生中最美好的四年时光。在这里遇到了很多好朋友，包括后来点亮的故事人物。",
      eventDate: "2010-09-01",
      eventType: "education",
      importance: "major",
      isPublic: true,
      location: "某大学",
      tags: ["大学", "友情", "成长"],
    },
    {
      userUsername: USER_ROLES.LI_XIAOHONG,
      title: "与李明参加编程比赛",
      description:
        "和室友李明一起参加了学校的编程比赛，虽然没有获奖，但那段一起熬夜调试代码的经历很珍贵。",
      eventDate: "2011-11-15",
      eventType: "achievement",
      importance: "normal",
      isPublic: false,
      location: "大学计算机实验室",
      tags: ["编程", "友情", "挑战"],
    },
    {
      userUsername: USER_ROLES.LI_XIAOHONG,
      title: "毕业后各奔东西",
      description:
        "大学毕业后，和朋友们都去了不同的城市工作。虽然分别，但友情一直在心中。",
      eventDate: "2014-06-20",
      eventType: "milestone",
      importance: "major",
      isPublic: true,
      location: "大学",
      tags: ["毕业", "分别", "友情"],
    },
    {
      userUsername: USER_ROLES.LI_XIAOHONG,
      title: "加入有故事平台",
      description:
        "偶然发现了有故事这个平台，被人物点亮系统深深吸引。注册后开始了我的点亮之旅。",
      eventDate: "2024-12-01",
      eventType: "milestone",
      importance: "major",
      isPublic: true,
      location: "线上",
      tags: ["平台", "点亮", "重逢"],
    },
    {
      userUsername: USER_ROLES.LI_XIAOHONG,
      title: "第一次成功点亮",
      description:
        "成功点亮了张明远故事中的李明这个人物，重新联系上了大学室友。那一刻真的太激动了！",
      eventDate: "2024-12-30",
      eventType: "achievement",
      importance: "major",
      isPublic: true,
      location: "线上",
      tags: ["点亮", "重逢", "友情"],
    },

    // ========== 王建国的人生时间线 ==========
    {
      userUsername: USER_ROLES.WANG_JIANGUO,
      title: "出生在教师家庭",
      description:
        "1985年3月10日出生于一个教师家庭，从小就受到良好的教育熏陶，立志要成为一名好老师。",
      eventDate: "1985-03-10",
      eventType: "birth",
      importance: "critical",
      isPublic: true,
      location: "某县城",
      tags: ["出生", "教师家庭", "教育"],
    },
    {
      userUsername: USER_ROLES.WANG_JIANGUO,
      title: "师范大学毕业",
      description:
        "从师范大学毕业，正式踏上教师这条神圣的道路。那时年轻气盛，满腔热血。",
      eventDate: "2007-06-30",
      eventType: "education",
      importance: "major",
      isPublic: true,
      location: "师范大学",
      tags: ["毕业", "教师", "理想"],
    },
    {
      userUsername: USER_ROLES.WANG_JIANGUO,
      title: "初登讲台",
      description:
        "第一次站在讲台上，面对台下几十双求知的眼睛，既紧张又兴奋。从那一刻起，我就是王老师了。",
      eventDate: "2007-09-01",
      eventType: "work",
      importance: "major",
      isPublic: true,
      location: "某中学",
      tags: ["教师", "初次", "讲台"],
    },
    {
      userUsername: USER_ROLES.WANG_JIANGUO,
      title: "小军转学事件",
      description:
        "2005年，因为我的严厉批评，学生小军转学了。这件事成为我教师生涯中最大的遗憾和反思。",
      eventDate: "2005-12-30",
      eventType: "loss",
      importance: "major",
      isPublic: false,
      location: "某中学",
      tags: ["遗憾", "反思", "教育"],
      relatedStoryTitle: "三十年教书，我最后悔的一件事",
    },
    {
      userUsername: USER_ROLES.WANG_JIANGUO,
      title: "小雨高考成功",
      description:
        "得知学生小雨高考成功，考入理想大学，内心无比欣慰。她的坚强一直是我前进的动力。",
      eventDate: "2010-07-15",
      eventType: "achievement",
      importance: "major",
      isPublic: true,
      location: "某中学",
      tags: ["学生成功", "欣慰", "教育成果"],
      relatedStoryTitle: "小雨，你现在过得还好吗",
    },
    {
      userUsername: USER_ROLES.WANG_JIANGUO,
      title: "小军电话和解",
      description:
        "前年教师节，收到了小军的电话，他已经成为成功的汽修厂老板，并且原谅了我。那一刻，我哭了。",
      eventDate: "2023-09-10",
      eventType: "milestone",
      importance: "major",
      isPublic: true,
      location: "家中",
      tags: ["和解", "原谅", "释怀"],
      relatedStoryTitle: "三十年教书，我最后悔的一件事",
    },
    {
      userUsername: USER_ROLES.WANG_JIANGUO,
      title: "在平台与小雨重逢",
      description:
        "小雨在有故事平台上找到了我，通过点亮系统重新联系上了。师生重逢，这是教育最美好的回报。",
      eventDate: "2025-07-14",
      eventType: "celebration",
      importance: "major",
      isPublic: true,
      location: "线上",
      tags: ["师生重逢", "点亮", "欣慰"],
    },

    // ========== 周发现的人生时间线 ==========
    {
      userUsername: USER_ROLES.ZHOU_FAXIAN,
      title: "生在教师之家",
      description:
        "1993年9月15日出生，父亲是中学老师，从小在教育环境中成长，培养了我对知识的渴求。",
      eventDate: "1993-09-15",
      eventType: "birth",
      importance: "critical",
      isPublic: true,
      location: "某县城",
      tags: ["出生", "教师家庭"],
    },
    {
      userUsername: USER_ROLES.ZHOU_FAXIAN,
      title: "考入医学院",
      description:
        "高考后考入医学院，立志成为一名医生，救死扶伤。医学院的学习生活充实而有意义。",
      eventDate: "2011-09-01",
      eventType: "education",
      importance: "major",
      isPublic: true,
      location: "医学院",
      tags: ["医学", "理想", "学习"],
    },
    {
      userUsername: USER_ROLES.ZHOU_FAXIAN,
      title: "王建国老师的课",
      description:
        "高中时期，王建国老师给我上课。虽然我成绩不错，但老师的人格魅力更让我印象深刻。",
      eventDate: "2008-09-01",
      eventType: "education",
      importance: "normal",
      isPublic: false,
      location: "某中学",
      tags: ["老师", "高中", "影响"],
    },
    {
      userUsername: USER_ROLES.ZHOU_FAXIAN,
      title: "成为执业医师",
      description:
        "通过医师资格考试，正式成为一名医生。穿上白大褂的那一刻，感受到了责任的重量。",
      eventDate: "2016-08-15",
      eventType: "achievement",
      importance: "major",
      isPublic: true,
      location: "某医院",
      tags: ["医师", "成就", "责任"],
    },
    {
      userUsername: USER_ROLES.ZHOU_FAXIAN,
      title: "发现点亮系统",
      description:
        "在有故事平台上发现了人物点亮系统，被这种创新的社交方式深深吸引，开始了我的点亮之旅。",
      eventDate: "2024-11-15",
      eventType: "milestone",
      importance: "major",
      isPublic: true,
      location: "线上",
      tags: ["点亮", "发现", "创新"],
    },
    {
      userUsername: USER_ROLES.ZHOU_FAXIAN,
      title: "成功点亮恩师的学生角色",
      description:
        '在王老师的故事中找到了自己，成功点亮了"小雨"这个角色。师生通过平台重逢，非常感动。',
      eventDate: "2025-07-14",
      eventType: "achievement",
      importance: "major",
      isPublic: true,
      location: "线上",
      tags: ["点亮成功", "师生情", "重逢"],
    },

    // ========== 赵小芳的人生时间线 ==========
    {
      userUsername: USER_ROLES.ZHAO_XIAOFANG,
      title: "在书香世家出生",
      description:
        "1995年11月28日出生在一个爱读书的家庭，从小就被书籍包围，养成了爱阅读的习惯。",
      eventDate: "1995-11-28",
      eventType: "birth",
      importance: "critical",
      isPublic: true,
      location: "某城市",
      tags: ["出生", "书香门第", "阅读"],
    },
    {
      userUsername: USER_ROLES.ZHAO_XIAOFANG,
      title: "大学文学专业",
      description:
        "考入大学文学专业，在文学的海洋中尽情遨游。大学四年读了无数好书，拓宽了视野。",
      eventDate: "2013-09-01",
      eventType: "education",
      importance: "major",
      isPublic: true,
      location: "某大学",
      tags: ["文学", "大学", "读书"],
    },
    {
      userUsername: USER_ROLES.ZHAO_XIAOFANG,
      title: "第一份图书管理员工作",
      description:
        "毕业后成为了一名图书管理员，每天与书为伴，虽然工资不高，但内心很满足。",
      eventDate: "2017-07-01",
      eventType: "work",
      importance: "normal",
      isPublic: false,
      location: "某图书馆",
      tags: ["工作", "图书管理员", "书籍"],
    },
    {
      userUsername: USER_ROLES.ZHAO_XIAOFANG,
      title: "加入有故事平台",
      description:
        "偶然发现有故事平台，被这里真实的故事深深吸引。虽然自己不常写作，但很喜欢阅读别人的故事。",
      eventDate: "2025-01-15",
      eventType: "milestone",
      importance: "normal",
      isPublic: true,
      location: "线上",
      tags: ["阅读", "故事", "平台"],
    },

    // ========== 吴私密的人生时间线（部分私密） ==========
    {
      userUsername: USER_ROLES.WU_SIMI,
      title: "出生",
      description:
        "1988年12月24日出生，圣诞节前夜，父母说我是他们最好的圣诞礼物。",
      eventDate: "1988-12-24",
      eventType: "birth",
      importance: "critical",
      isPublic: false,
      location: "某城市",
      tags: ["出生", "圣诞", "礼物"],
    },
    {
      userUsername: USER_ROLES.WU_SIMI,
      title: "大学毕业",
      description:
        "从大学毕业，开始了我的职业生涯。选择了一份相对稳定的工作，追求内心的平静。",
      eventDate: "2010-06-30",
      eventType: "education",
      importance: "major",
      isPublic: false,
      location: "某大学",
      tags: ["毕业", "工作", "稳定"],
    },
    {
      userUsername: USER_ROLES.WU_SIMI,
      title: "开始写日记",
      description:
        "从25岁开始养成写日记的习惯，记录生活中的点点滴滴。这个习惯一直保持到现在。",
      eventDate: "2013-12-24",
      eventType: "milestone",
      importance: "normal",
      isPublic: false,
      location: "家中",
      tags: ["日记", "记录", "习惯"],
    },
    {
      userUsername: USER_ROLES.WU_SIMI,
      title: "健康检查虚惊",
      description:
        "体检发现指标异常，经过复查发现是虚惊一场。这次经历让我更加珍惜健康和家人。",
      eventDate: "2025-06-15",
      eventType: "other",
      importance: "major",
      isPublic: false,
      location: "医院",
      tags: ["健康", "虚惊", "珍惜"],
      relatedStoryTitle: "妈妈，对不起，我撒谎了",
    },
  ];

// 导出时间线统计数据
export const TIMELINE_STATS = {
  total: ENHANCED_TIMELINE_EVENT_SEED_DATA.length,
  public: ENHANCED_TIMELINE_EVENT_SEED_DATA.filter((e) => e.isPublic).length,
  private: ENHANCED_TIMELINE_EVENT_SEED_DATA.filter((e) => !e.isPublic).length,

  // 按事件类型统计
  byEventType: {
    birth: ENHANCED_TIMELINE_EVENT_SEED_DATA.filter(
      (e) => e.eventType === "birth",
    ).length,
    education: ENHANCED_TIMELINE_EVENT_SEED_DATA.filter(
      (e) => e.eventType === "education",
    ).length,
    work: ENHANCED_TIMELINE_EVENT_SEED_DATA.filter(
      (e) => e.eventType === "work",
    ).length,
    relationship: ENHANCED_TIMELINE_EVENT_SEED_DATA.filter(
      (e) => e.eventType === "relationship",
    ).length,
    achievement: ENHANCED_TIMELINE_EVENT_SEED_DATA.filter(
      (e) => e.eventType === "achievement",
    ).length,
    milestone: ENHANCED_TIMELINE_EVENT_SEED_DATA.filter(
      (e) => e.eventType === "milestone",
    ).length,
    celebration: ENHANCED_TIMELINE_EVENT_SEED_DATA.filter(
      (e) => e.eventType === "celebration",
    ).length,
    loss: ENHANCED_TIMELINE_EVENT_SEED_DATA.filter(
      (e) => e.eventType === "loss",
    ).length,
    other: ENHANCED_TIMELINE_EVENT_SEED_DATA.filter(
      (e) => e.eventType === "other",
    ).length,
  },

  // 按重要程度统计
  byImportance: {
    critical: ENHANCED_TIMELINE_EVENT_SEED_DATA.filter(
      (e) => e.importance === "critical",
    ).length,
    major: ENHANCED_TIMELINE_EVENT_SEED_DATA.filter(
      (e) => e.importance === "major",
    ).length,
    normal: ENHANCED_TIMELINE_EVENT_SEED_DATA.filter(
      (e) => e.importance === "normal",
    ).length,
    minor: ENHANCED_TIMELINE_EVENT_SEED_DATA.filter(
      (e) => e.importance === "minor",
    ).length,
  },

  // 关联故事的事件
  withRelatedStory: ENHANCED_TIMELINE_EVENT_SEED_DATA.filter(
    (e) => e.relatedStoryTitle,
  ).length,

  // 按用户统计事件数量
  byUser: (() => {
    const userCounts: { [key: string]: number } = {};
    ENHANCED_TIMELINE_EVENT_SEED_DATA.forEach((event) => {
      const username = event.userUsername;
      userCounts[username] = (userCounts[username] || 0) + 1;
    });
    return userCounts;
  })(),
};
