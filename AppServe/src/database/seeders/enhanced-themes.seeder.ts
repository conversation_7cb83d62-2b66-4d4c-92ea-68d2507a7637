/**
 * 企业级故事主题种子数据
 *
 * 基于 YGS v1.0.0 故事分类系统设计
 * 涵盖人生各个方面的主题分类
 */

export interface EnhancedThemeSeedData {
  name: string;
  description: string;
  colorCode: string;
  iconName: string;
  isActive: boolean;
  sortOrder: number;
  category: "emotion" | "relationship" | "growth" | "daily" | "special";
  tags: string[];
  exampleStoryTypes: string[];
}

export const ENHANCED_THEME_SEED_DATA: EnhancedThemeSeedData[] = [
  // ========== 情感类主题 ==========
  {
    name: "友情",
    description: "关于朋友之间珍贵情谊的故事，记录友谊的美好时光",
    colorCode: "#4CAF50",
    iconName: "people",
    isActive: true,
    sortOrder: 1,
    category: "emotion",
    tags: ["朋友", "陪伴", "信任", "共同回忆"],
    exampleStoryTypes: ["大学室友", "童年玩伴", "工作伙伴", "老友重逢"],
  },
  {
    name: "亲情",
    description: "家人之间的温暖故事，血浓于水的情感纽带",
    colorCode: "#FF9800",
    iconName: "home",
    isActive: true,
    sortOrder: 2,
    category: "emotion",
    tags: ["家人", "血缘", "关爱", "传承"],
    exampleStoryTypes: ["父母之爱", "兄弟姐妹", "祖孙情深", "家庭聚会"],
  },
  {
    name: "爱情",
    description: "浪漫爱情故事，记录爱情路上的甜蜜与成长",
    colorCode: "#E91E63",
    iconName: "favorite",
    isActive: true,
    sortOrder: 3,
    category: "emotion",
    tags: ["恋爱", "浪漫", "爱人", "情侣"],
    exampleStoryTypes: ["初恋记忆", "求婚故事", "情侣日常", "爱情长跑"],
  },
  {
    name: "感动",
    description: "那些触动心灵的瞬间，让人流泪的温暖故事",
    colorCode: "#9C27B0",
    iconName: "sentiment_very_satisfied",
    isActive: true,
    sortOrder: 4,
    category: "emotion",
    tags: ["感动", "温暖", "眼泪", "心灵"],
    exampleStoryTypes: ["善意相助", "意外温暖", "感人瞬间", "人间真情"],
  },

  // ========== 关系类主题 ==========
  {
    name: "师生",
    description: "师生之间的珍贵情谊，教育路上的感人故事",
    colorCode: "#2196F3",
    iconName: "school",
    isActive: true,
    sortOrder: 5,
    category: "relationship",
    tags: ["老师", "学生", "教育", "成长"],
    exampleStoryTypes: ["恩师难忘", "学生成长", "教学相长", "毕业感言"],
  },
  {
    name: "同事",
    description: "职场中的人际关系，工作伙伴的故事",
    colorCode: "#607D8B",
    iconName: "work",
    isActive: true,
    sortOrder: 6,
    category: "relationship",
    tags: ["同事", "职场", "合作", "团队"],
    exampleStoryTypes: ["职场友谊", "团队协作", "工作伙伴", "职场趣事"],
  },
  {
    name: "邻里",
    description: "邻居之间的温馨故事，社区生活的点点滴滴",
    colorCode: "#8BC34A",
    iconName: "location_city",
    isActive: true,
    sortOrder: 7,
    category: "relationship",
    tags: ["邻居", "社区", "互助", "生活"],
    exampleStoryTypes: ["邻里互助", "社区活动", "楼道故事", "邻家朋友"],
  },

  // ========== 成长类主题 ==========
  {
    name: "成长",
    description: "人生路上的成长故事，记录蜕变的珍贵时刻",
    colorCode: "#FF5722",
    iconName: "trending_up",
    isActive: true,
    sortOrder: 8,
    category: "growth",
    tags: ["成长", "蜕变", "进步", "人生"],
    exampleStoryTypes: ["青春叛逆", "成年礼", "人生转折", "自我发现"],
  },
  {
    name: "奋斗",
    description: "努力拼搏的故事，追求梦想的坚持与汗水",
    colorCode: "#795548",
    iconName: "fitness_center",
    isActive: true,
    sortOrder: 9,
    category: "growth",
    tags: ["奋斗", "努力", "梦想", "坚持"],
    exampleStoryTypes: ["创业历程", "考试备战", "技能学习", "目标达成"],
  },
  {
    name: "挫折",
    description: "面对困难的故事，从挫折中学习和成长",
    colorCode: "#9E9E9E",
    iconName: "psychology",
    isActive: true,
    sortOrder: 10,
    category: "growth",
    tags: ["挫折", "困难", "坚强", "恢复"],
    exampleStoryTypes: ["失败经历", "低谷时期", "重新站起", "教训总结"],
  },

  // ========== 日常类主题 ==========
  {
    name: "日常",
    description: "平凡生活中的小确幸，记录日常生活的美好",
    colorCode: "#FFC107",
    iconName: "today",
    isActive: true,
    sortOrder: 11,
    category: "daily",
    tags: ["日常", "生活", "平凡", "美好"],
    exampleStoryTypes: ["早餐故事", "上班路上", "周末时光", "生活琐事"],
  },
  {
    name: "旅行",
    description: "旅行路上的见闻和感悟，记录行万里路的收获",
    colorCode: "#00BCD4",
    iconName: "flight",
    isActive: true,
    sortOrder: 12,
    category: "daily",
    tags: ["旅行", "见闻", "风景", "文化"],
    exampleStoryTypes: ["旅游经历", "出差见闻", "自驾游", "异国体验"],
  },
  {
    name: "美食",
    description: "关于美食的故事，味蕾记忆和饮食文化",
    colorCode: "#FF9800",
    iconName: "restaurant",
    isActive: true,
    sortOrder: 13,
    category: "daily",
    tags: ["美食", "味道", "回忆", "文化"],
    exampleStoryTypes: ["家常菜", "特色小吃", "聚餐时光", "厨艺学习"],
  },
  {
    name: "爱好",
    description: "个人兴趣爱好的故事，热爱之事的分享",
    colorCode: "#3F51B5",
    iconName: "sports_esports",
    isActive: true,
    sortOrder: 14,
    category: "daily",
    tags: ["爱好", "兴趣", "热爱", "专业"],
    exampleStoryTypes: ["运动健身", "音乐艺术", "读书写作", "手工制作"],
  },

  // ========== 特殊类主题 ==========
  {
    name: "节日",
    description: "节日庆祝的温馨故事，特殊日子的珍贵回忆",
    colorCode: "#F44336",
    iconName: "celebration",
    isActive: true,
    sortOrder: 15,
    category: "special",
    tags: ["节日", "庆祝", "团聚", "传统"],
    exampleStoryTypes: ["春节聚会", "生日派对", "圣诞节", "中秋团圆"],
  },
  {
    name: "毕业",
    description: "毕业季的离别与成长，青春岁月的珍贵记忆",
    colorCode: "#1976D2",
    iconName: "school",
    isActive: true,
    sortOrder: 16,
    category: "special",
    tags: ["毕业", "离别", "青春", "成长"],
    exampleStoryTypes: ["毕业典礼", "离别时刻", "同学聚会", "青春回忆"],
  },
  {
    name: "工作",
    description: "职场生活的故事，工作中的成长与挑战",
    colorCode: "#424242",
    iconName: "business_center",
    isActive: true,
    sortOrder: 17,
    category: "special",
    tags: ["工作", "职场", "事业", "专业"],
    exampleStoryTypes: ["入职第一天", "项目经历", "晋升故事", "职场学习"],
  },
  {
    name: "宠物",
    description: "与萌宠相伴的温暖故事，动物朋友的陪伴",
    colorCode: "#8BC34A",
    iconName: "pets",
    isActive: true,
    sortOrder: 18,
    category: "special",
    tags: ["宠物", "动物", "陪伴", "可爱"],
    exampleStoryTypes: ["养宠经历", "萌宠趣事", "动物朋友", "宠物成长"],
  },
  {
    name: "回忆",
    description: "珍贵的回忆故事，时光留下的美好印记",
    colorCode: "#9C27B0",
    iconName: "history",
    isActive: true,
    sortOrder: 19,
    category: "special",
    tags: ["回忆", "往事", "怀念", "时光"],
    exampleStoryTypes: ["童年回忆", "青春岁月", "过往经历", "时光倒流"],
  },
  {
    name: "感恩",
    description: "感恩的故事，对他人和生活的感谢与珍惜",
    colorCode: "#FF6F00",
    iconName: "volunteer_activism",
    isActive: true,
    sortOrder: 20,
    category: "special",
    tags: ["感恩", "感谢", "珍惜", "回报"],
    exampleStoryTypes: ["感恩父母", "感谢朋友", "珍惜拥有", "回馈社会"],
  },
];

// 导出主题统计数据
export const THEME_STATS = {
  total: ENHANCED_THEME_SEED_DATA.length,
  active: ENHANCED_THEME_SEED_DATA.filter((t) => t.isActive).length,

  // 按分类统计
  byCategory: {
    emotion: ENHANCED_THEME_SEED_DATA.filter((t) => t.category === "emotion")
      .length,
    relationship: ENHANCED_THEME_SEED_DATA.filter(
      (t) => t.category === "relationship",
    ).length,
    growth: ENHANCED_THEME_SEED_DATA.filter((t) => t.category === "growth")
      .length,
    daily: ENHANCED_THEME_SEED_DATA.filter((t) => t.category === "daily")
      .length,
    special: ENHANCED_THEME_SEED_DATA.filter((t) => t.category === "special")
      .length,
  },

  // 主题色彩分析
  colorDistribution: {
    warm: ENHANCED_THEME_SEED_DATA.filter((t) =>
      ["#FF9800", "#E91E63", "#FF5722", "#FFC107", "#FF6F00"].includes(
        t.colorCode,
      ),
    ).length,
    cool: ENHANCED_THEME_SEED_DATA.filter((t) =>
      ["#2196F3", "#00BCD4", "#4CAF50", "#8BC34A", "#3F51B5"].includes(
        t.colorCode,
      ),
    ).length,
    neutral: ENHANCED_THEME_SEED_DATA.filter((t) =>
      ["#9E9E9E", "#607D8B", "#424242"].includes(t.colorCode),
    ).length,
  },

  // 最受欢迎的主题（按sortOrder排序前5）
  topThemes: ENHANCED_THEME_SEED_DATA.sort((a, b) => a.sortOrder - b.sortOrder)
    .slice(0, 5)
    .map((t) => ({ name: t.name, category: t.category })),
};
