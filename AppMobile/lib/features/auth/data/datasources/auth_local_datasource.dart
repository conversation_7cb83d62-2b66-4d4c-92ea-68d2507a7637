import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/constants/storage_keys.dart';
import '../models/auth_response_models.dart';

/// 认证本地数据源Provider
final authLocalDataSourceProvider = Provider<AuthLocalDataSource>((ref) {
  throw UnimplementedError('需要在应用初始化时覆盖此Provider');
});

/// 认证本地数据源接口
abstract class AuthLocalDataSource {
  Future<void> saveUser(User user);
  Future<User?> getUser();
  Future<void> saveTokens(String accessToken, String refreshToken);
  Future<String?> getAccessToken();
  Future<String?> getRefreshToken();
  Future<void> clearAuthData();
  Future<bool> isLoggedIn();
  Future<void> saveAuthResponse(AuthResponse response);
}

/// 认证本地数据源实现
class AuthLocalDataSourceImpl implements AuthLocalDataSource {
  final FlutterSecureStorage secureStorage;
  final SharedPreferences sharedPreferences;

  const AuthLocalDataSourceImpl({
    required this.secureStorage,
    required this.sharedPreferences,
  });

  @override
  Future<void> saveUser(User user) async {
    // 保存用户基本信息到安全存储
    await secureStorage.write(
      key: StorageKeys.userId,
      value: user.id,
    );
    if (user.phone != null) {
      await secureStorage.write(
        key: StorageKeys.userPhone,
        value: user.phone,
      );
    }
    
    // 保存用户详细信息到普通存储
    final userJson = json.encode(user.toJson());
    await sharedPreferences.setString(
      '${StorageKeys.userCachePrefix}${user.id}',
      userJson,
    );
  }

  @override
  Future<User?> getUser() async {
    final userId = await secureStorage.read(key: StorageKeys.userId);
    if (userId == null) return null;

    final userJson = sharedPreferences.getString(
      '${StorageKeys.userCachePrefix}$userId',
    );
    if (userJson == null) return null;

    try {
      return User.fromJson(json.decode(userJson));
    } catch (e) {
      return null;
    }
  }

  @override
  Future<void> saveTokens(String accessToken, String refreshToken) async {
    await secureStorage.write(
      key: StorageKeys.accessToken,
      value: accessToken,
    );
    await secureStorage.write(
      key: StorageKeys.refreshToken,
      value: refreshToken,
    );
    
    // 保存token保存时间，用于计算过期
    await sharedPreferences.setInt(
      'token_saved_at',
      DateTime.now().millisecondsSinceEpoch,
    );
  }

  @override
  Future<String?> getAccessToken() async {
    return secureStorage.read(key: StorageKeys.accessToken);
  }

  @override
  Future<String?> getRefreshToken() async {
    return secureStorage.read(key: StorageKeys.refreshToken);
  }

  @override
  Future<void> clearAuthData() async {
    // 清除安全存储
    await secureStorage.delete(key: StorageKeys.accessToken);
    await secureStorage.delete(key: StorageKeys.refreshToken);
    await secureStorage.delete(key: StorageKeys.userId);
    await secureStorage.delete(key: StorageKeys.userPhone);
    
    // 获取用户ID后再清除
    final userId = await secureStorage.read(key: StorageKeys.userId);
    
    // 清除用户缓存
    if (userId != null) {
      await sharedPreferences.remove('${StorageKeys.userCachePrefix}$userId');
    }
    
    // 清除token保存时间
    await sharedPreferences.remove('token_saved_at');
  }

  @override
  Future<bool> isLoggedIn() async {
    final accessToken = await getAccessToken();
    if (accessToken == null) return false;
    
    // 检查token是否过期（假设45分钟过期）
    final savedAt = sharedPreferences.getInt('token_saved_at');
    if (savedAt == null) return false;
    
    final savedTime = DateTime.fromMillisecondsSinceEpoch(savedAt);
    final now = DateTime.now();
    final difference = now.difference(savedTime);
    
    // Access token有效期45分钟
    return difference.inMinutes < 45;
  }
  
  @override
  Future<void> saveAuthResponse(AuthResponse response) async {
    // 保存token
    await saveTokens(response.accessToken, response.refreshToken);
    
    // 保存过期时间（秒）
    await sharedPreferences.setInt('token_expires_in', response.expiresIn);
    
    // 保存用户信息
    await saveUser(response.user);
  }
}