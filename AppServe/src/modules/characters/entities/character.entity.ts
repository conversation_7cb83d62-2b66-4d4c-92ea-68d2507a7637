import {
  En<PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  ManyToMany,
  JoinColumn,
  Index,
} from "typeorm";
import { User } from "../../users/entities/user.entity";
import { Story } from "../../stories/entities/story.entity";
import { CharacterLighting } from "./character-lighting.entity";

@Entity("characters")
@Index(["creatorId", "name"], { unique: true }) // 同一创建者下人物名称唯一
@Index(["creatorId"]) // 创建者查询索引
@Index(["isLighted"]) // 点亮状态索引
@Index(["lighterUserId"]) // 点亮用户索引 - 企业级性能优化
@Index(["creatorId", "lighterUserId"]) // 复合索引 - 支持用户点亮限制查询
export class Character {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ name: "creator_id" })
  creatorId: string;

  @Column({ length: 100, comment: "人物姓名" })
  name: string;

  @Column({ type: "text", nullable: true, comment: "人物描述" })
  description: string;

  @Column({ name: "avatar_url", nullable: true, comment: "人物头像URL" })
  avatarUrl: string;

  @Column({ length: 10, nullable: true, comment: "性别" })
  gender: string;

  @Column({ length: 50, nullable: true, comment: "关系类型" })
  relationship: string;

  @Column({
    name: "custom_relationship",
    length: 100,
    nullable: true,
    comment: "自定义关系",
  })
  customRelationship: string;

  @Column({ name: "is_lighted", default: false, comment: "是否已点亮" })
  isLighted: boolean;

  @Column({ name: "lighting_count", default: 0, comment: "被点亮次数" })
  lightingCount: number;

  // 🔥 企业级核心字段 - 人物排他性约束
  @Column({
    name: "lighter_user_id",
    nullable: true,
    comment: "点亮用户ID - 实现人物级排他性约束",
  })
  lighterUserId: string;

  @Column({
    name: "first_lighted_at",
    type: "timestamp",
    nullable: true,
    comment: "首次点亮时间",
  })
  firstLightedAt: Date;

  @Column({ name: "is_active", default: true, comment: "是否激活" })
  isActive: boolean;

  @CreateDateColumn({ name: "created_at", comment: "创建时间" })
  createdAt: Date;

  @UpdateDateColumn({ name: "updated_at", comment: "更新时间" })
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => User, (user) => user.characters, { onDelete: "CASCADE" })
  @JoinColumn({ name: "creator_id" })
  creator: User;

  @ManyToMany(() => Story, (story) => story.characters)
  stories: Story[];

  @OneToMany(() => CharacterLighting, (lighting) => lighting.character)
  lightings: CharacterLighting[];

  // 点亮用户关联 - 企业级排他性约束
  @ManyToOne(() => User, { nullable: true, onDelete: "SET NULL" })
  @JoinColumn({ name: "lighter_user_id" })
  lighterUser: User;

  // 🎯 企业级业务方法
  /**
   * 检查人物是否已被点亮
   */
  isLightedByUser(): boolean {
    return this.lighterUserId !== null;
  }

  /**
   * 检查是否可以被指定用户点亮
   */
  canBeLightedBy(userId: string): boolean {
    return !this.lighterUserId || this.lighterUserId === userId;
  }

  /**
   * 获取点亮状态描述
   */
  getLightingStatusDescription(): string {
    if (!this.lighterUserId) {
      return "未点亮";
    }
    return `已被用户点亮`;
  }
}
