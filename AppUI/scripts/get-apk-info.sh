#!/bin/bash

# 有故事 APP - APK信息获取脚本
# 用于APP备案，获取包名、公钥、MD5等信息

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔍 有故事 APP - APK信息获取脚本${NC}"
echo "================================================"

# 检查必要工具
check_tools() {
    echo -e "${YELLOW}📋 检查必要工具...${NC}"
    
    if ! command -v keytool &> /dev/null; then
        echo -e "${RED}❌ keytool 未找到，请确保已安装JDK${NC}"
        exit 1
    fi
    
    if ! command -v openssl &> /dev/null; then
        echo -e "${RED}❌ openssl 未找到，请安装openssl${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 工具检查完成${NC}"
}

# 获取APK信息
get_apk_info() {
    local apk_path="$1"
    
    if [ ! -f "$apk_path" ]; then
        echo -e "${RED}❌ APK文件不存在: $apk_path${NC}"
        exit 1
    fi
    
    echo -e "${BLUE}📱 分析APK: $apk_path${NC}"
    echo "================================================"
    
    # 获取签名信息
    echo -e "${YELLOW}🔐 签名证书信息:${NC}"
    keytool -printcert -jarfile "$apk_path" 2>/dev/null || {
        echo -e "${RED}❌ 无法获取签名信息，请检查APK是否已签名${NC}"
        exit 1
    }
    echo ""
}

# 获取keystore信息
get_keystore_info() {
    local keystore_path="app/yougushi-release-key.keystore"
    local alias="yougushi-app"
    local store_pass="YGS2024@Release"
    local key_pass="YGS2024@Release"
    
    echo -e "${GREEN}🎯 APP备案关键信息:${NC}"
    echo "================================================"
    
    # 包名
    echo -e "${BLUE}包名 (Package Name):${NC} com.yougushi.app"
    
    # MD5指纹
    if [ -f "$keystore_path" ]; then
        echo -e "${YELLOW}🔑 从Keystore获取MD5指纹:${NC}"
        md5_fingerprint=$(keytool -exportcert -alias "$alias" -keystore "$keystore_path" -storepass "$store_pass" 2>/dev/null | openssl x509 -fingerprint -md5 -noout | sed 's/md5 Fingerprint=//' | tr -d ':')
        echo -e "${BLUE}MD5指纹 (去除冒号):${NC} $md5_fingerprint"
        
        echo -e "${YELLOW}🔑 SHA1指纹:${NC}"
        sha1_fingerprint=$(keytool -list -v -keystore "$keystore_path" -alias "$alias" -storepass "$store_pass" -keypass "$key_pass" 2>/dev/null | grep "SHA1:" | sed 's/.*SHA1: *//' | tr -d ' :')
        echo -e "${BLUE}SHA1指纹 (去除冒号):${NC} $sha1_fingerprint"
        
        echo -e "${YELLOW}🔑 公钥信息 (备案格式):${NC}"
        
        # 获取十六进制Modulus
        echo -e "${BLUE}公钥 Modulus (十六进制字符串):${NC}"
        hex_modulus=$(keytool -list -rfc -keystore "$keystore_path" -alias "$alias" -storepass "$store_pass" 2>/dev/null | openssl x509 -inform PEM -pubkey -noout | openssl rsa -pubin -text -noout | grep -A 20 "Modulus:" | grep -v "Modulus:" | grep -v "Exponent:" | tr -d ' \n:' | sed 's/^00//')
        echo "$hex_modulus"
        
        # 获取十进制Modulus (如果python可用)
        if command -v python3 &> /dev/null; then
            echo -e "${BLUE}公钥 Modulus (十进制数字):${NC}"
            python3 -c "print(int('$hex_modulus', 16))" 2>/dev/null || echo "无法转换为十进制格式"
        fi
        
        echo ""
        echo -e "${YELLOW}🔑 传统PEM格式公钥 (供参考):${NC}"
        keytool -list -rfc -keystore "$keystore_path" -alias "$alias" -storepass "$store_pass" 2>/dev/null | openssl x509 -inform PEM -pubkey -noout
        
        echo ""
        echo -e "${GREEN}✅ Keystore信息获取完成${NC}"
    else
        echo -e "${RED}❌ Keystore文件不存在: $keystore_path${NC}"
    fi
}

# 显示备案信息汇总
show_summary() {
    echo ""
    echo -e "${GREEN}📋 APP备案信息汇总:${NC}"
    echo "================================================"
    echo -e "${BLUE}应用包名:${NC} com.yougushi.app"
    echo -e "${BLUE}应用版本:${NC} 1.0 (versionCode: 1)"
    echo -e "${BLUE}签名算法:${NC} SHA256withRSA"
    echo -e "${BLUE}密钥长度:${NC} 2048位"
    echo -e "${BLUE}证书有效期:${NC} 2025-06-20 至 2052-11-05 (约27年)"
    echo ""
    echo -e "${YELLOW}💡 重要说明:${NC}"
    echo "  1. 此签名证书为生产环境专用"
    echo "  2. 使用相同keystore重新打包，包名、公钥、MD5都不会变化"
    echo "  3. 建议妥善保管keystore文件和密码"
    echo "  4. APK文件位置: android/app/build/outputs/apk/release/app-release.apk"
    echo ""
    echo -e "${GREEN}📋 备案时使用说明:${NC}"
    echo "  • 包名: 直接使用 com.yougushi.app"
    echo "  • MD5指纹: 直接使用上述去除冒号的32位字符串"
    echo "  • 公钥: 根据平台要求使用十六进制或十进制Modulus"
    echo ""
    echo -e "${GREEN}✅ 备案信息获取完成，可用于正式备案${NC}"
}

# 主函数
main() {
    check_tools
    
    # 默认APK路径
    release_apk="./app/build/outputs/apk/release/app-release.apk"
    
    # 检查APK文件
    if [ -f "$release_apk" ]; then
        get_apk_info "$release_apk"
        get_keystore_info
        show_summary
    else
        echo -e "${RED}❌ 未找到Release APK文件${NC}"
        echo -e "${YELLOW}请先生成APK：${NC}"
        echo "   cd android && ./gradlew assembleRelease"
        exit 1
    fi
}

# 运行脚本
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "用法: $0"
    echo "功能: 获取APK的包名、签名MD5、公钥等APP备案所需信息"
    echo ""
    echo "此脚本会自动检查 android/app/build/outputs/apk/release/app-release.apk"
    echo "并获取对应的keystore信息用于APP备案"
    echo ""
    echo "输出格式:"
    echo "  - 包名: com.yougushi.app"
    echo "  - MD5指纹: 32位十六进制字符串 (无冒号)"
    echo "  - 公钥: 十六进制和十进制Modulus格式"
    exit 0
fi

main 