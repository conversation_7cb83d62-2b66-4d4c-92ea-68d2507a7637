/**
 * 企业级SMS服务集成测试
 * 测试SMS服务和AliyunSmsService的完整业务流程
 * 包括复杂业务场景、边界条件、安全机制等
 */

import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { ConfigService } from "@nestjs/config";
import { SmsService } from "./sms.service";
import { AliyunSmsService } from "./aliyun-sms.service";
import { EnhancedRedisService } from "../../../common/services/enhanced-redis.service";
import { DeviceFingerprintService } from "../../../common/services/device-fingerprint.service";
import { VerificationSecurityService } from "../../../common/services/verification-security.service";
import type { Request } from "express";

// 企业级阿里云SDK Mock配置
jest.mock("@alicloud/dysmsapi20170525", () => ({
  __esModule: true,
  default: jest.fn().mockImplementation(() => ({
    sendSmsWithOptions: jest.fn(),
  })),
  SendSmsRequest: jest.fn().mockImplementation((params: unknown) => params),
}));

jest.mock("@alicloud/openapi-client", () => ({
  Config: jest.fn().mockImplementation((params: unknown) => params),
}));

jest.mock("@alicloud/tea-util", () => ({
  RuntimeOptions: jest.fn().mockImplementation((params: unknown) => params),
}));

describe("企业级SMS服务集成测试", () => {
  let smsService: SmsService;

  const testData = {
    vipPhone: "16675158665",
    normalPhone: "13800138000",
    mockCode: "1234",
    mockRequest: {
      ip: "***********",
      headers: { "user-agent": "Mozilla/5.0..." },
    } as unknown as Request, // 更明确的类型转换
  };

  beforeEach(async () => {
    jest.clearAllMocks();

    const mockConfigService = {
      get: jest.fn((key: string) => {
        const config: { [key: string]: string } = {
          NODE_ENV: "test",
          SMS_MODE: "auto",
          SMS_MOCK_CODE: "123456",
        };
        return config[key];
      }),
    };

    const mockRedisService = {
      getClient: jest.fn().mockReturnValue({
        get: jest.fn().mockResolvedValue(null),
        set: jest.fn().mockResolvedValue("OK"),
        sismember: jest.fn().mockResolvedValue(false), // 用于黑名单检查
        sadd: jest.fn().mockResolvedValue(1),
        expire: jest.fn().mockResolvedValue(1),
        pipeline: jest.fn().mockReturnValue({
          get: jest.fn().mockReturnThis(),
          incr: jest.fn().mockReturnThis(),
          expire: jest.fn().mockReturnThis(),
          lpush: jest.fn().mockReturnThis(),
          ltrim: jest.fn().mockReturnThis(),
          exec: jest.fn().mockResolvedValue([]),
        }),
      }),
    };

    const mockDeviceFingerprintService = {
      generateDeviceFingerprint: jest.fn().mockReturnValue({
        ip: "***********",
        fingerprint: "mock-fingerprint-hash",
        userAgent: "Mozilla/5.0...",
        deviceId: "mock-device-id",
      }),
      validateSmsDevice: jest.fn().mockResolvedValue(true),
      recordSmsDevice: jest.fn().mockResolvedValue(undefined),
    };

    const mockVerificationSecurityService = {
      checkVerificationAttempt: jest.fn().mockResolvedValue({
        allowed: true,
        reason: null,
        remainingAttempts: 5,
      }),
      recordVerificationAttempt: jest.fn().mockResolvedValue(undefined),
      checkPhoneLockout: jest.fn().mockResolvedValue(false),
    };

    const mockAliyunSmsService = {
      sendVerificationCode: jest.fn().mockResolvedValue({
        success: true,
        requestId: "mock-request-id",
        bizId: "mock-biz-id",
      }),
      checkSmsBalance: jest.fn().mockResolvedValue(1000),
      getVipTestPhones: jest.fn().mockReturnValue(["16675158665"]),
      shouldSendRealSms: jest.fn().mockReturnValue(false),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SmsService,
        {
          provide: AliyunSmsService,
          useValue: mockAliyunSmsService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: EnhancedRedisService,
          useValue: mockRedisService,
        },
        {
          provide: DeviceFingerprintService,
          useValue: mockDeviceFingerprintService,
        },
        {
          provide: VerificationSecurityService,
          useValue: mockVerificationSecurityService,
        },
      ],
    }).compile();

    smsService = module.get<SmsService>(SmsService);
  });

  describe("🏢 企业级SMS服务完整流程测试", () => {
    it("should handle VIP phone SMS workflow", async () => {
      const result = await smsService.sendVerificationCode(
        testData.vipPhone,
        testData.mockRequest,
      );

      expect(result).toEqual({
        success: true,
        message: "验证码发送成功",
      });
    });

    it("should handle normal phone Mock SMS workflow", async () => {
      const result = await smsService.sendVerificationCode(
        testData.normalPhone,
        testData.mockRequest,
      );

      expect(result).toEqual({
        success: true,
        message: "验证码发送成功",
      });
    });
  });

  describe("🔧 企业级验证码验证测试", () => {
    it("should verify code successfully", async () => {
      const result = await smsService.verifyCode(
        testData.normalPhone,
        testData.mockCode,
        testData.mockRequest,
      );

      expect(typeof result).toBe("boolean");
    });
  });

  it("should be defined", () => {
    expect(smsService).toBeDefined();
  });
});
