import type { ExceptionFilter, ArgumentsHost } from "@nestjs/common";
import { Catch, HttpException, HttpStatus, Logger } from "@nestjs/common";
import type { FastifyRequest, FastifyReply } from "fastify";

@Catch()
export class HttpExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(HttpExceptionFilter.name);

  catch(exception: unknown, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const request = ctx.getRequest<FastifyRequest>();
    const response = ctx.getResponse<FastifyReply>();

    const status =
      exception instanceof HttpException
        ? exception.getStatus()
        : HttpStatus.INTERNAL_SERVER_ERROR;

    const message =
      exception instanceof HttpException
        ? exception.getResponse()
        : "Internal server error";

    // 记录错误日志
    this.logger.error({
      statusCode: status,
      timestamp: new Date().toISOString(),
      path: request.url,
      method: request.method,
      message: typeof message === "string" ? message : JSON.stringify(message),
      stack: exception instanceof Error ? exception.stack : undefined,
    });

    // 构造响应数据
    const errorResponse: Record<string, unknown> = {
      success: false,
      code: status,
      message:
        typeof message === "object" && message !== null && "message" in message
          ? (message as Record<string, unknown>).message
          : typeof message === "string"
            ? message
            : "Internal server error",
      error:
        typeof message === "object" && message !== null && "error" in message
          ? (message as Record<string, unknown>).error
          : undefined,
      timestamp: new Date().toISOString(),
      path: request.url,
    };

    // 开发环境下返回详细错误信息
    if (process.env.NODE_ENV === "development" && exception instanceof Error) {
      errorResponse.stack = exception.stack;
    }

    response.status(status).send(errorResponse);
  }
}
