import 'package:logger/logger.dart';
import '../../app/config/env_config.dart';

/// 日志工具类
class AppLogger {
  static final _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 2,
      errorMethodCount: 8,
      lineLength: 120,
      colors: true,
      printEmojis: true,
      printTime: true,
    ),
    level: EnvConfig.enableLogging ? Level.debug : Level.nothing,
  );

  AppLogger._();

  /// 获取Logger实例
  static Logger get instance => _logger;

  /// 调试日志
  static void d(String message, {dynamic error, StackTrace? stackTrace}) {
    _logger.d(message, time: DateTime.now(), error: error, stackTrace: stackTrace);
  }

  /// 信息日志
  static void i(String message, {dynamic error, StackTrace? stackTrace}) {
    _logger.i(message, time: DateTime.now(), error: error, stackTrace: stackTrace);
  }

  /// 警告日志
  static void w(String message, {dynamic error, StackTrace? stackTrace}) {
    _logger.w(message, time: DateTime.now(), error: error, stackTrace: stackTrace);
  }

  /// 错误日志
  static void e(String message, {dynamic error, StackTrace? stackTrace}) {
    _logger.e(message, time: DateTime.now(), error: error, stackTrace: stackTrace);
  }

  /// 致命错误日志
  static void wtf(String message, {dynamic error, StackTrace? stackTrace}) {
    _logger.f(message, time: DateTime.now(), error: error, stackTrace: stackTrace);
  }

  /// 详细日志
  static void v(String message, {dynamic error, StackTrace? stackTrace}) {
    _logger.t(message, time: DateTime.now(), error: error, stackTrace: stackTrace);
  }
}