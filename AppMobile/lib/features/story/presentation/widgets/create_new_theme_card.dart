import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../design_system/tokens/ygs_colors.dart';
import '../../../../design_system/tokens/ygs_typography.dart';

/// 创建新主题卡片 - 与主题卡片相同尺寸的创建入口
/// 设计风格：简洁现代，突出创建功能，符合YGS设计系统
class CreateNewThemeCard extends StatefulWidget {
  final VoidCallback? onTap;

  const CreateNewThemeCard({
    super.key,
    this.onTap,
  });

  @override
  State<CreateNewThemeCard> createState() => _CreateNewThemeCardState();
}

class _CreateNewThemeCardState extends State<CreateNewThemeCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    setState(() {
      _isPressed = true;
    });
    _animationController.forward();
  }

  void _handleTapUp(TapUpDetails details) {
    setState(() {
      _isPressed = false;
    });
    _animationController.reverse();
  }

  void _handleTapCancel() {
    setState(() {
      _isPressed = false;
    });
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      onTapDown: _handleTapDown,
      onTapUp: _handleTapUp,
      onTapCancel: _handleTapCancel,
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              width: 345.w,
              height: 240.h, // 保持与主题卡片相同高度
              decoration: BoxDecoration(
                // 按照Figma Mask Group的渐变背景
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    const Color(0xFFFFE8E8), // 浅粉色
                    const Color(0xFFFFD6D6), // 稍深一点的粉色
                    const Color(0xFFFFC4C4), // 更深的粉色
                  ],
                  stops: const [0.0, 0.5, 1.0],
                ),
                borderRadius: BorderRadius.circular(16.r),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // 加号图标 - 与主题卡片图标大小一致
                  Container(
                    width: 64.w, // 与主题卡片玻璃图标大小一致
                    height: 64.w,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.9),
                      borderRadius: BorderRadius.circular(16.r),
                      border: Border.all(
                        color: const Color(0xFF000000).withValues(alpha: 0.1),
                        width: 1.w,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 8.r,
                          offset: Offset(0, 2.h),
                        ),
                      ],
                    ),
                    child: Icon(
                      Icons.add,
                      size: 32.w, // 增大图标尺寸
                      color: const Color(0xFF151523),
                      weight: 2.0,
                    ),
                  ),
                  
                  SizedBox(height: 24.h),
                  
                  // 主标题 - 与主题卡片字体大小一致
                  Text(
                    '创建新主题',
                    style: TextStyle(
                      fontFamily: 'PingFang SC',
                      fontSize: 30.sp, // 与主题卡片标题大小一致
                      fontWeight: FontWeight.w700, // 与主题卡片字重一致
                      color: const Color(0xFF151523),
                      height: 1.0,
                      letterSpacing: -0.3.w,
                      shadows: [
                        Shadow(
                          offset: Offset(0, 1.h),
                          blurRadius: 2.r,
                          color: Colors.white.withValues(alpha: 0.8),
                        ),
                      ],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

