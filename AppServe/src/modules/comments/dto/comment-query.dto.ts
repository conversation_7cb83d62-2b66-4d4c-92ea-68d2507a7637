import {
  IsOptional,
  IsString,
  IsEnum,
  IsNumberString,
  <PERSON>,
  <PERSON>,
} from "class-validator";
import { Type } from "class-transformer";
import { CommentTargetType, CommentStatus } from "../entities/comment.entity";

export enum CommentSortBy {
  CREATED_DESC = "created_desc",
  CREATED_ASC = "created_asc",
  LIKE_COUNT_DESC = "like_count_desc",
  REPLY_COUNT_DESC = "reply_count_desc",
}

/**
 * 评论查询DTO
 */
export class CommentQueryDto {
  @IsOptional()
  @IsEnum(CommentTargetType, {
    message: "目标类型必须是 story 或 comment",
  })
  targetType?: CommentTargetType;

  @IsOptional()
  @IsString({
    message: "目标ID必须是字符串",
  })
  targetId?: string;

  @IsOptional()
  @IsEnum(CommentStatus, {
    message: "状态必须是有效的评论状态",
  })
  status?: CommentStatus;

  @IsOptional()
  @IsString({
    message: "作者ID必须是字符串",
  })
  authorId?: string;

  @IsOptional()
  @IsString({
    message: "根评论ID必须是字符串",
  })
  rootCommentId?: string;

  @IsOptional()
  @IsNumberString(
    {},
    {
      message: "页码必须是数字字符串",
    },
  )
  @Type(() => Number)
  @Min(1, {
    message: "页码最小值为1",
  })
  page?: number = 1;

  @IsOptional()
  @IsNumberString(
    {},
    {
      message: "每页数量必须是数字字符串",
    },
  )
  @Type(() => Number)
  @Min(1, {
    message: "每页数量最小值为1",
  })
  @Max(100, {
    message: "每页数量最大值为100",
  })
  limit?: number = 20;

  @IsOptional()
  @IsEnum(CommentSortBy, {
    message: "排序方式必须是有效的排序选项",
  })
  sortBy?: CommentSortBy = CommentSortBy.CREATED_DESC;

  @IsOptional()
  @IsString({
    message: "搜索关键词必须是字符串",
  })
  keyword?: string;
}
