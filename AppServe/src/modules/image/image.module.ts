import { Module, forwardRef } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { ImageService } from "./image.service";
import { ImageController } from "./image.controller";
import { SecureImageService } from "./secure-image.service";
import { ossConfig } from "../../config/oss.config";
import { AuthModule } from "../auth/auth.module";

@Module({
  imports: [
    ConfigModule.forFeature(ossConfig),
    forwardRef(() => AuthModule), // 修复JwtAuthGuard依赖问题
  ],
  providers: [ImageService, SecureImageService],
  controllers: [ImageController],
  exports: [ImageService, SecureImageService],
})
export class ImageModule {}
