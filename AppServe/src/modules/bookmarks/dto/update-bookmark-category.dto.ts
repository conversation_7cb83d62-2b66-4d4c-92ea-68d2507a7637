import { IsString, IsEnum, IsOptional, <PERSON><PERSON>ength } from "class-validator";
import { BookmarkCategory } from "../entities/bookmark.entity";

/**
 * 更新收藏分类DTO
 */
export class UpdateBookmarkCategoryDto {
  @IsEnum(BookmarkCategory, {
    message: "收藏分类必须是有效的分类选项",
  })
  category: BookmarkCategory;

  @IsOptional()
  @IsString({
    message: "自定义分类名称必须是字符串",
  })
  @MaxLength(100, {
    message: "自定义分类名称不能超过100个字符",
  })
  customCategoryName?: string;

  @IsOptional()
  @IsString({
    message: "收藏备注必须是字符串",
  })
  @MaxLength(500, {
    message: "收藏备注不能超过500个字符",
  })
  note?: string;
}
