import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
  UnauthorizedException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { Share, ShareType, ShareStatus } from "./entities/share.entity";
import { ShareAccessLog } from "./entities/share-access-log.entity";
import type { CreateShareDto, ShareAccessDto } from "./dto";
import type {
  ShareResponseDto,
  ShareContentResponseDto,
  ShareStatsResponseDto,
} from "./dto";
import { Story } from "../stories/entities/story.entity";
import { User } from "../users/entities/user.entity";
import * as crypto from "crypto";

/**
 * 分享服务
 * 提供故事分享链接的完整功能
 */
@Injectable()
export class SharesService {
  constructor(
    @InjectRepository(Share)
    private readonly shareRepository: Repository<Share>,
    @InjectRepository(ShareAccessLog)
    private readonly shareAccessLogRepository: Repository<ShareAccessLog>,
    @InjectRepository(Story)
    private readonly storyRepository: Repository<Story>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  /**
   * 创建分享链接
   */
  async createShare(
    storyId: string,
    createShareDto: CreateShareDto,
    userId: string,
  ): Promise<ShareResponseDto> {
    const {
      shareType,
      password,
      accessLimit = -1,
      expiresAt,
      description,
      metadata,
    } = createShareDto;

    // 验证故事是否存在且用户有权限分享
    const story = await this.storyRepository.findOne({
      where: { id: storyId },
      relations: ["author"],
    });

    if (!story) {
      throw new NotFoundException("故事不存在");
    }

    if (story.userId !== userId) {
      throw new ForbiddenException("只能分享自己的故事");
    }

    // 验证参数
    if (shareType === ShareType.LIMITED && !password) {
      throw new BadRequestException("有限制分享必须设置访问密码");
    }

    // 生成唯一的分享令牌
    const token = this.generateShareToken();

    // 创建故事快照
    const storySnapshot = {
      title: story.title,
      summary: story.content,
      coverImage: story.coverImageUrl,
      authorName: story.user?.nickname || "未知作者",
    };

    // 创建分享记录
    const share = this.shareRepository.create({
      token,
      storyId,
      userId,
      shareType,
      password: password ? await this.hashPassword(password) : undefined,
      accessLimit,
      expiresAt: expiresAt ? new Date(expiresAt) : undefined,
      description,
      storySnapshot,
      metadata,
    });

    const savedShare = await this.shareRepository.save(share);
    return this.buildShareResponse(savedShare);
  }

  /**
   * 访问分享内容
   */
  async accessShare(
    token: string,
    accessDto: ShareAccessDto,
    ipAddress: string,
    userAgent?: string,
    referer?: string,
    currentUserId?: string,
  ): Promise<ShareContentResponseDto> {
    // 查找分享记录
    const share = await this.shareRepository.findOne({
      where: { token },
      relations: ["story", "story.user", "user"],
    });

    if (!share) {
      await this.logAccess(
        null,
        ipAddress,
        userAgent,
        referer,
        currentUserId,
        false,
        "分享链接不存在",
      );
      throw new NotFoundException("分享链接不存在");
    }

    // 检查分享状态
    if (share.status !== ShareStatus.ACTIVE) {
      await this.logAccess(
        share.id,
        ipAddress,
        userAgent,
        referer,
        currentUserId,
        false,
        "分享已禁用或过期",
      );
      throw new BadRequestException("分享已禁用或过期");
    }

    // 检查过期时间
    if (share.expiresAt && share.expiresAt < new Date()) {
      // 自动更新状态为过期
      await this.shareRepository.update(share.id, {
        status: ShareStatus.EXPIRED,
      });
      await this.logAccess(
        share.id,
        ipAddress,
        userAgent,
        referer,
        currentUserId,
        false,
        "分享已过期",
      );
      throw new BadRequestException("分享已过期");
    }

    // 检查访问次数限制
    if (share.accessLimit > 0 && share.accessCount >= share.accessLimit) {
      await this.logAccess(
        share.id,
        ipAddress,
        userAgent,
        referer,
        currentUserId,
        false,
        "访问次数已达上限",
      );
      throw new BadRequestException("访问次数已达上限");
    }

    // 验证密码
    if (share.shareType === ShareType.LIMITED) {
      if (!accessDto.password) {
        await this.logAccess(
          share.id,
          ipAddress,
          userAgent,
          referer,
          currentUserId,
          false,
          "需要访问密码",
        );
        throw new UnauthorizedException("需要访问密码");
      }

      const isPasswordValid = await this.verifyPassword(
        accessDto.password,
        share.password,
      );
      if (!isPasswordValid) {
        await this.logAccess(
          share.id,
          ipAddress,
          userAgent,
          referer,
          currentUserId,
          false,
          "访问密码错误",
        );
        throw new UnauthorizedException("访问密码错误");
      }
    }

    // 检查故事是否仍然存在
    if (!share.story) {
      await this.logAccess(
        share.id,
        ipAddress,
        userAgent,
        referer,
        currentUserId,
        false,
        "目标故事不存在",
      );
      throw new NotFoundException("目标故事不存在");
    }

    // 记录访问日志
    await this.logAccess(
      share.id,
      ipAddress,
      userAgent,
      referer,
      currentUserId,
      true,
    );

    // 增加访问次数
    await this.shareRepository.increment({ id: share.id }, "accessCount", 1);

    // 构建响应数据
    const isOwner = currentUserId === share.userId;

    return {
      share: {
        id: share.id,
        token: share.token,
        shareType: share.shareType,
        description: share.description,
        accessCount: share.accessCount + 1,
        sharerName: share.user?.nickname || "未知用户",
        createdAt: share.createdAt,
      },
      story: {
        id: share.story.id,
        title: share.story.title,
        summary: share.story.content,
        content:
          isOwner || share.story.allowComments
            ? share.story.content
            : undefined,
        coverImage: share.story.coverImageUrl,
        authorId: share.story.userId,
        authorName: share.story.user?.nickname || "未知作者",
        createdAt: share.story.createdAt,
        isAccessible: isOwner || share.story.allowComments,
      },
      isOwner,
    };
  }

  /**
   * 获取故事分享统计
   */
  async getShareStats(
    storyId: string,
    userId: string,
  ): Promise<ShareStatsResponseDto> {
    // 验证权限
    const story = await this.storyRepository.findOne({
      where: { id: storyId },
    });
    if (!story || story.userId !== userId) {
      throw new ForbiddenException("无权限查看此故事的分享统计");
    }

    // 获取基础统计
    const shares = await this.shareRepository.find({
      where: { storyId },
      relations: ["story"],
    });

    const totalShares = shares.length;
    const activeShares = shares.filter(
      (share) => share.status === ShareStatus.ACTIVE,
    ).length;
    const totalAccessCount = shares.reduce(
      (sum, share) => sum + share.accessCount,
      0,
    );

    // 获取访问日志统计
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const thisWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    const shareIds = shares.map((share) => share.id);

    if (shareIds.length === 0) {
      return {
        totalShares: 0,
        activeShares: 0,
        totalAccessCount: 0,
        uniqueVisitors: 0,
        accessCountToday: 0,
        accessCountThisWeek: 0,
        accessCountThisMonth: 0,
        topShares: [],
      };
    }

    // 独立访客数
    const uniqueVisitorsResult = await this.shareAccessLogRepository
      .createQueryBuilder("log")
      .select("COUNT(DISTINCT log.ipAddress)", "count")
      .where("log.shareId IN (:...shareIds)", { shareIds })
      .andWhere("log.isSuccessful = true")
      .getRawOne();

    // 今日访问数
    const accessCountTodayResult = await this.shareAccessLogRepository
      .createQueryBuilder("log")
      .select("COUNT(*)", "count")
      .where("log.shareId IN (:...shareIds)", { shareIds })
      .andWhere("log.isSuccessful = true")
      .andWhere("log.createdAt >= :today", { today })
      .getRawOne();

    // 本周访问数
    const accessCountThisWeekResult = await this.shareAccessLogRepository
      .createQueryBuilder("log")
      .select("COUNT(*)", "count")
      .where("log.shareId IN (:...shareIds)", { shareIds })
      .andWhere("log.isSuccessful = true")
      .andWhere("log.createdAt >= :thisWeek", { thisWeek })
      .getRawOne();

    // 本月访问数
    const accessCountThisMonthResult = await this.shareAccessLogRepository
      .createQueryBuilder("log")
      .select("COUNT(*)", "count")
      .where("log.shareId IN (:...shareIds)", { shareIds })
      .andWhere("log.isSuccessful = true")
      .andWhere("log.createdAt >= :thisMonth", { thisMonth })
      .getRawOne();

    // 热门分享
    const topShares = shares
      .filter((share) => share.accessCount > 0)
      .sort((a, b) => b.accessCount - a.accessCount)
      .slice(0, 5)
      .map((share) => ({
        shareId: share.id,
        storyTitle: share.storySnapshot.title,
        accessCount: share.accessCount,
        createdAt: share.createdAt,
      }));

    return {
      totalShares,
      activeShares,
      totalAccessCount,
      uniqueVisitors: parseInt(uniqueVisitorsResult?.count) || 0,
      accessCountToday: parseInt(accessCountTodayResult?.count) || 0,
      accessCountThisWeek: parseInt(accessCountThisWeekResult?.count) || 0,
      accessCountThisMonth: parseInt(accessCountThisMonthResult?.count) || 0,
      topShares,
    };
  }

  /**
   * 获取用户的分享列表
   */
  async getUserShares(userId: string): Promise<ShareResponseDto[]> {
    const shares = await this.shareRepository.find({
      where: { userId },
      relations: ["story"],
      order: { createdAt: "DESC" },
    });

    return shares.map((share) => this.buildShareResponse(share));
  }

  /**
   * 删除分享
   */
  async deleteShare(shareId: string, userId: string): Promise<void> {
    const share = await this.shareRepository.findOne({
      where: { id: shareId },
    });

    if (!share) {
      throw new NotFoundException("分享记录不存在");
    }

    if (share.userId !== userId) {
      throw new ForbiddenException("只能删除自己的分享");
    }

    await this.shareRepository.remove(share);
  }

  /**
   * 生成分享令牌
   */
  private generateShareToken(): string {
    return crypto.randomBytes(16).toString("hex");
  }

  /**
   * 加密密码
   */
  private async hashPassword(password: string): Promise<string> {
    return crypto.createHash("sha256").update(password).digest("hex");
  }

  /**
   * 验证密码
   */
  private async verifyPassword(
    password: string,
    hashedPassword: string,
  ): Promise<boolean> {
    const hash = await this.hashPassword(password);
    return hash === hashedPassword;
  }

  /**
   * 记录访问日志
   */
  private async logAccess(
    shareId: string | null,
    ipAddress: string,
    userAgent?: string,
    referer?: string,
    userId?: string,
    isSuccessful: boolean = true,
    failureReason?: string,
  ): Promise<void> {
    const log = this.shareAccessLogRepository.create({
      shareId,
      ipAddress,
      userAgent,
      referer,
      userId,
      isSuccessful,
      failureReason,
    });

    await this.shareAccessLogRepository.save(log);
  }

  /**
   * 构建分享响应数据
   */
  private buildShareResponse(share: Share): ShareResponseDto {
    const baseUrl = process.env.FRONTEND_URL || "https://yougushi.com";

    return {
      id: share.id,
      token: share.token,
      shareUrl: `${baseUrl}/share/${share.token}`,
      storyId: share.storyId,
      userId: share.userId,
      shareType: share.shareType,
      status: share.status,
      hasPassword: !!share.password,
      accessLimit: share.accessLimit,
      accessCount: share.accessCount,
      expiresAt: share.expiresAt,
      description: share.description,
      storySnapshot: share.storySnapshot,
      metadata: share.metadata,
      createdAt: share.createdAt,
      updatedAt: share.updatedAt,
    };
  }
}
