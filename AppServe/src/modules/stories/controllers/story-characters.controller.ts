import {
  Controller,
  Get,
  Post,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
  BadRequestException,
  ForbiddenException,
} from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse as SwaggerApiResponse,
  ApiBearerAuth,
  ApiParam,
} from "@nestjs/swagger";
import { JwtAuthGuard } from "../../auth/guards/jwt-auth.guard";
import { StoriesService } from "../stories.service";
import { CharactersService } from "../../characters/characters.service";
import type { CharacterResponseDto } from "../../characters/dto";
import { CharacterQueryDto } from "../../characters/dto";
import { PaginatedResponse } from "../../../common/dto/paginated-response.dto";
import { ApiResponse } from "../../../common/dto/api-response.dto";
import { IsArray, IsUUID } from "class-validator";
import { AuthRequest } from "../../../common/types/request.types";

/**
 * 故事人物关联管理DTO
 */
export class AddCharactersToStoryDto {
  @IsArray()
  @IsUUID(undefined, { each: true })
  characterIds: string[];
}

/**
 * 故事人物关联控制器
 * 处理故事和人物之间的关联管理
 */
@ApiTags("故事人物关联")
@Controller("stories/:storyId/characters")
@UseGuards(JwtAuthGuard)
export class StoryCharactersController {
  constructor(
    private readonly storiesService: StoriesService,
    private readonly charactersService: CharactersService,
  ) {}

  /**
   * 添加人物到故事
   * POST /stories/:storyId/characters
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: "添加人物到故事" })
  @ApiParam({ name: "storyId", description: "故事ID", type: String })
  @SwaggerApiResponse({
    status: 201,
    description: "添加成功",
    type: ApiResponse,
  })
  @ApiBearerAuth()
  async addCharactersToStory(
    @Request() req: AuthRequest,
    @Param("storyId", ParseUUIDPipe) storyId: string,
    @Body() addCharactersDto: AddCharactersToStoryDto,
  ): Promise<ApiResponse<{ addedCount: number }>> {
    try {
      // 验证故事存在且用户有权限
      const story = await this.storiesService.findById(storyId);
      if (story.userId !== req.user.id) {
        throw new ForbiddenException("无权修改此故事");
      }

      // 批量添加人物
      let addedCount = 0;
      for (const characterId of addCharactersDto.characterIds) {
        try {
          await this.charactersService.addCharacterToStory(
            storyId,
            characterId,
            req.user.id,
          );
          addedCount++;
        } catch (error) {
          // 忽略单个人物添加失败的情况，继续处理其他人物
          console.warn(
            `Failed to add character ${characterId} to story ${storyId}:`,
            error instanceof Error ? error.message : String(error),
          );
        }
      }

      return new ApiResponse(
        true,
        `成功添加 ${addedCount} 个人物到故事`,
        { addedCount },
        HttpStatus.CREATED,
      );
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }
      throw new BadRequestException(
        error instanceof Error ? error.message : String(error),
      );
    }
  }

  /**
   * 从故事中移除人物
   * DELETE /stories/:storyId/characters/:characterId
   */
  @Delete(":characterId")
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: "从故事中移除人物" })
  @ApiParam({ name: "storyId", description: "故事ID", type: String })
  @ApiParam({ name: "characterId", description: "人物ID", type: String })
  @SwaggerApiResponse({ status: 204, description: "移除成功" })
  @ApiBearerAuth()
  async removeCharacterFromStory(
    @Request() req: AuthRequest,
    @Param("storyId", ParseUUIDPipe) storyId: string,
    @Param("characterId", ParseUUIDPipe) characterId: string,
  ): Promise<void> {
    try {
      // 验证故事存在且用户有权限
      const story = await this.storiesService.findById(storyId);
      if (story.userId !== req.user.id) {
        throw new ForbiddenException("无权修改此故事");
      }

      await this.charactersService.removeCharacterFromStory(
        storyId,
        characterId,
        req.user.id,
      );
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }
      throw new BadRequestException(
        error instanceof Error ? error.message : String(error),
      );
    }
  }

  /**
   * 获取故事人物列表
   * GET /stories/:storyId/characters
   */
  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "获取故事人物列表" })
  @ApiParam({ name: "storyId", description: "故事ID", type: String })
  @SwaggerApiResponse({
    status: 200,
    description: "获取成功",
    type: ApiResponse,
  })
  @ApiBearerAuth()
  async getStoryCharacters(
    @Request() req: AuthRequest,
    @Param("storyId", ParseUUIDPipe) storyId: string,
    @Query() query: CharacterQueryDto,
  ): Promise<ApiResponse<PaginatedResponse<CharacterResponseDto>>> {
    try {
      // 验证故事访问权限
      const hasAccess = await this.storiesService.validateStoryAccess(
        storyId,
        req.user.id,
      );
      if (!hasAccess) {
        throw new ForbiddenException("无权访问此故事");
      }

      const result = await this.charactersService.getStoryCharacters(
        storyId,
        query,
      );

      return new ApiResponse(
        true,
        "获取故事人物列表成功",
        result,
        HttpStatus.OK,
      );
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }
      throw new BadRequestException(
        error instanceof Error ? error.message : String(error),
      );
    }
  }

  /**
   * 批量更新故事人物关联
   * PUT /stories/:storyId/characters
   */
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "批量更新故事人物关联" })
  @ApiParam({ name: "storyId", description: "故事ID", type: String })
  @SwaggerApiResponse({
    status: 200,
    description: "更新成功",
    type: ApiResponse,
  })
  @ApiBearerAuth()
  async updateStoryCharacters(
    @Request() req: AuthRequest,
    @Param("storyId", ParseUUIDPipe) storyId: string,
    @Body() updateCharactersDto: AddCharactersToStoryDto,
  ): Promise<ApiResponse<{ updatedCount: number }>> {
    try {
      // 验证故事存在且用户有权限
      const story = await this.storiesService.findById(storyId);
      if (story.userId !== req.user.id) {
        throw new ForbiddenException("无权修改此故事");
      }

      // 获取当前故事的人物列表
      const currentCharacters = await this.charactersService.getStoryCharacters(
        storyId,
        { limit: 1000 },
      );
      const currentCharacterIds = currentCharacters.data.map((c) => c.id);

      // 计算需要添加和移除的人物
      const newCharacterIds = updateCharactersDto.characterIds;
      const toAdd = newCharacterIds.filter(
        (id) => !currentCharacterIds.includes(id),
      );
      const toRemove = currentCharacterIds.filter(
        (id) => !newCharacterIds.includes(id),
      );

      // 移除不需要的人物
      for (const characterId of toRemove) {
        try {
          await this.charactersService.removeCharacterFromStory(
            storyId,
            characterId,
            req.user.id,
          );
        } catch (error) {
          console.warn(
            `Failed to remove character ${characterId} from story ${storyId}:`,
            error instanceof Error ? error.message : String(error),
          );
        }
      }

      // 添加新的人物
      let addedCount = 0;
      for (const characterId of toAdd) {
        try {
          await this.charactersService.addCharacterToStory(
            storyId,
            characterId,
            req.user.id,
          );
          addedCount++;
        } catch (error) {
          console.warn(
            `Failed to add character ${characterId} to story ${storyId}:`,
            error instanceof Error ? error.message : String(error),
          );
        }
      }

      const updatedCount = toRemove.length + addedCount;

      return new ApiResponse(
        true,
        `成功更新 ${updatedCount} 个人物关联`,
        { updatedCount },
        HttpStatus.OK,
      );
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }
      throw new BadRequestException(
        error instanceof Error ? error.message : String(error),
      );
    }
  }

  /**
   * 获取可添加到故事的人物列表
   * GET /stories/:storyId/characters/available
   */
  @Get("available")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "获取可添加到故事的人物列表" })
  @ApiParam({ name: "storyId", description: "故事ID", type: String })
  @SwaggerApiResponse({
    status: 200,
    description: "获取成功",
    type: ApiResponse,
  })
  @ApiBearerAuth()
  async getAvailableCharacters(
    @Request() req: AuthRequest,
    @Param("storyId", ParseUUIDPipe) storyId: string,
    @Query() query: CharacterQueryDto,
  ): Promise<ApiResponse<PaginatedResponse<CharacterResponseDto>>> {
    try {
      // 验证故事存在且用户有权限
      const story = await this.storiesService.findById(storyId);
      if (story.userId !== req.user.id) {
        throw new ForbiddenException("无权修改此故事");
      }

      // 获取用户的所有人物
      const userCharacters = await this.charactersService.getUserCharacters(
        req.user.id,
        query,
      );

      // 获取已添加到故事的人物
      const storyCharacters = await this.charactersService.getStoryCharacters(
        storyId,
        { limit: 1000 },
      );
      const storyCharacterIds = storyCharacters.data.map((c) => c.id);

      // 过滤出可添加的人物
      const availableCharacters = userCharacters.data.filter(
        (character) => !storyCharacterIds.includes(character.id),
      );

      const result = new PaginatedResponse(
        availableCharacters,
        availableCharacters.length,
        query.page || 1,
        query.limit || 10,
      );

      return new ApiResponse(
        true,
        "获取可添加人物列表成功",
        result,
        HttpStatus.OK,
      );
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }
      throw new BadRequestException(
        error instanceof Error ? error.message : String(error),
      );
    }
  }
}
