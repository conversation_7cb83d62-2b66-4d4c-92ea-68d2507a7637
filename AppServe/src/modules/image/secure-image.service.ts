import { Injectable, Logger, NotFoundException } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";

import * as OSS from "ali-oss";

export enum ImageAccessLevel {
  PUBLIC = "public", // 公开内容，长期CDN缓存
  FRIENDS = "friends", // 好友可见，中期CDN缓存
  PRIVATE = "private", // 私密内容，签名URL访问
}

export interface SecureImageRequest {
  imagePath: string;
  accessLevel: ImageAccessLevel;
  userId: string;
  expiresIn?: number; // 签名URL有效期(秒)
}

export interface SecureImageResponse {
  url: string;
  accessLevel: ImageAccessLevel;
  expiresAt?: Date;
  cacheable: boolean;
}

@Injectable()
export class SecureImageService {
  private readonly logger = new Logger(SecureImageService.name);
  private readonly ossClient: OSS;

  constructor(private readonly configService: ConfigService) {
    const ossConfig = this.configService.get("oss");

    this.ossClient = new OSS({
      region: ossConfig.region,
      accessKeyId: ossConfig.accessKeyId,
      accessKeySecret: ossConfig.accessKeySecret,
      bucket: ossConfig.bucket,
      endpoint: ossConfig.endpoint,
    });
  }

  /**
   * 获取安全图片访问URL
   */
  async getSecureImageUrl(
    request: SecureImageRequest,
  ): Promise<SecureImageResponse> {
    const { imagePath, accessLevel, expiresIn } = request;

    // 检查图片是否存在
    await this.checkImageExists(imagePath);

    switch (accessLevel) {
      case ImageAccessLevel.PUBLIC:
        return this.getCDNUrl(imagePath, accessLevel);

      case ImageAccessLevel.FRIENDS:
        return this.getCDNUrl(imagePath, accessLevel);

      case ImageAccessLevel.PRIVATE:
        return await this.getSignedUrl(imagePath, accessLevel, expiresIn);

      default:
        throw new Error("不支持的访问级别");
    }
  }

  /**
   * 获取CDN访问URL (公开和半公开内容)
   */
  private getCDNUrl(
    imagePath: string,
    accessLevel: ImageAccessLevel,
  ): SecureImageResponse {
    const ossConfig = this.configService.get("oss");
    const cdnDomain =
      ossConfig.cdnDomain ||
      `https://${ossConfig.bucket}.oss-cn-hangzhou.aliyuncs.com`;

    // 确保路径包含基础目录
    const fullPath = imagePath.startsWith(ossConfig.baseDir)
      ? imagePath
      : `${ossConfig.baseDir}${imagePath}`;

    return {
      url: `${cdnDomain}/${fullPath}`,
      accessLevel,
      cacheable: true,
    };
  }

  /**
   * 生成签名URL (私密内容)
   */
  private async getSignedUrl(
    imagePath: string,
    accessLevel: ImageAccessLevel,
    expiresIn?: number,
  ): Promise<SecureImageResponse> {
    const defaultExpires =
      parseInt(process.env.OSS_SIGNED_URL_EXPIRES || "1800") || 1800; // 30分钟
    const expires = expiresIn || defaultExpires;

    try {
      const signedUrl = this.ossClient.signatureUrl(imagePath, {
        expires,
        method: "GET",
      });

      const expiresAt = new Date(Date.now() + expires * 1000);

      this.logger.log(`生成签名URL: ${imagePath}, 有效期: ${expires}秒`);

      return {
        url: signedUrl,
        accessLevel,
        expiresAt,
        cacheable: false,
      };
    } catch (error) {
      this.logger.error(
        `生成签名URL失败: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  /**
   * 批量获取安全图片URL
   */
  async getBatchSecureUrls(
    requests: SecureImageRequest[],
  ): Promise<SecureImageResponse[]> {
    const results = await Promise.allSettled(
      requests.map((request) => this.getSecureImageUrl(request)),
    );

    return results
      .map((result, index) => {
        if (result.status === "fulfilled") {
          return result.value;
        } else {
          this.logger.error(
            `批量处理失败: ${requests[index].imagePath}`,
            result.reason,
          );
          return null;
        }
      })
      .filter((result): result is SecureImageResponse => result !== null);
  }

  /**
   * 获取不同尺寸的图片URL
   */
  getImageSizes(baseUrl: string, accessLevel: ImageAccessLevel) {
    // 只有CDN访问的图片支持实时处理
    if (accessLevel === ImageAccessLevel.PRIVATE) {
      return { original: baseUrl };
    }

    return {
      original: baseUrl,
      large: `${baseUrl}?x-oss-process=image/resize,w_800`,
      medium: `${baseUrl}?x-oss-process=image/resize,w_400`,
      small: `${baseUrl}?x-oss-process=image/resize,w_200`,
      thumbnail: `${baseUrl}?x-oss-process=image/resize,w_100,h_100`,
    };
  }

  /**
   * 检查图片是否存在
   */
  private async checkImageExists(imagePath: string): Promise<void> {
    try {
      await this.ossClient.head(imagePath);
    } catch (error) {
      if (
        error instanceof Error &&
        (error as Error & { code: string }).code === "NoSuchKey"
      ) {
        throw new NotFoundException("图片不存在");
      }
      throw error;
    }
  }

  /**
   * 根据业务规则判断访问级别
   */
  determineAccessLevel(
    imageType: string,
    storyVisibility: string,
  ): ImageAccessLevel {
    switch (storyVisibility) {
      case "public":
        return ImageAccessLevel.PUBLIC;

      case "friends":
      case "followers":
      case "group":
        return ImageAccessLevel.FRIENDS;

      case "private":
      case "character_only":
        return ImageAccessLevel.PRIVATE;

      default:
        return ImageAccessLevel.PRIVATE; // 默认最安全级别
    }
  }

  /**
   * 清理过期的签名URL缓存 (如果使用Redis缓存)
   */
  async cleanupExpiredUrls(): Promise<void> {
    // TODO: 实现Redis缓存清理逻辑
    this.logger.log("清理过期签名URL缓存");
  }
}
