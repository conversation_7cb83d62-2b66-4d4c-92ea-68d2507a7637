/**
 * 有故事 APP - 认证页面
 * 
 * 功能特性：
 * - 手机号登录/注册
 * - 短信验证码
 * - 表单验证
 * - 优雅的交互体验
 * - B&O 极简设计风格
 */

import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  ScrollView,
  Platform,
  Animated,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import AsyncStorage from '@react-native-async-storage/async-storage';

// 导入类型和常量
import { RootStackParamList } from '../../navigation/RootNavigator';
import { THEME } from '../../constants';
import { authService } from '../../services/authService';
import { networkDebugger } from '../../config/network';

// ===== 类型定义 =====
type AuthScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'Auth'
>;

type AuthStep = 'phone' | 'verify';

/**
 * 认证页面组件
 */
const AuthScreen: React.FC = () => {
  const navigation = useNavigation<AuthScreenNavigationProp>();
  
  // 状态管理
  const [currentStep, setCurrentStep] = useState<AuthStep>('phone');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [countdown, setCountdown] = useState(0);
  
  // 动画引用
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  
  // 倒计时定时器
  const countdownRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // 页面加载动画
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();

    // 在开发环境测试网络连接
    if (__DEV__) {
      networkDebugger.testConnection().catch(error => {
        console.log('🔗 Initial network test failed:', error);
      });
    }

    return () => {
      if (countdownRef.current) {
        clearInterval(countdownRef.current);
      }
    };
  }, []);

  /**
   * 手机号验证
   */
  const validatePhoneNumber = (phone: string): boolean => {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  };

  /**
   * 验证码验证
   */
  const validateVerificationCode = (code: string): boolean => {
    return code.length === 6 && /^\d{6}$/.test(code);
  };

  /**
   * 发送验证码
   */
  const sendVerificationCode = async () => {
    if (!validatePhoneNumber(phoneNumber)) {
      Alert.alert('提示', '请输入正确的手机号码');
      return;
    }

    setIsLoading(true);
    
    try {
             await authService.sendSms({ phone: phoneNumber });
      
      setCurrentStep('verify');
      startCountdown();
      
      Alert.alert('验证码已发送', `验证码已发送至 ${phoneNumber}`);
    } catch (error: any) {
      Alert.alert('发送失败', error.message || '验证码发送失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 验证并登录
   */
  const verifyAndLogin = async () => {
    if (!validateVerificationCode(verificationCode)) {
      Alert.alert('提示', '请输入6位数字验证码');
      return;
    }

    setIsLoading(true);
    
    try {
             const response = await authService.loginWithPhone({
         phone: phoneNumber,
         code: verificationCode
       });
      
             // 保存认证信息
       await AsyncStorage.setItem('authToken', response.tokens.accessToken);
       await AsyncStorage.setItem('userId', response.user.id.toString());
       await AsyncStorage.setItem('userInfo', JSON.stringify(response.user));
      
      // 跳转到主页
      navigation.replace('Main');
      
    } catch (error: any) {
      Alert.alert('验证失败', error.message || '验证码错误，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 开始倒计时
   */
  const startCountdown = () => {
    setCountdown(60);
    countdownRef.current = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          if (countdownRef.current) {
            clearInterval(countdownRef.current);
          }
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  /**
   * 重新发送验证码
   */
  const resendCode = () => {
    if (countdown > 0) return;
    sendVerificationCode();
  };

  /**
   * 返回手机号输入
   */
  const backToPhoneInput = () => {
    setCurrentStep('phone');
    setVerificationCode('');
    if (countdownRef.current) {
      clearInterval(countdownRef.current);
      setCountdown(0);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          keyboardShouldPersistTaps="handled"
        >
          <Animated.View
            style={[
              styles.contentContainer,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }],
              },
            ]}
          >
            {/* 标题区域 */}
            <View style={styles.headerContainer}>
              <Text style={styles.titleText}>
                {currentStep === 'phone' ? '手机号登录' : '验证码'}
              </Text>
              <Text style={styles.subtitleText}>
                {currentStep === 'phone'
                  ? '请输入您的手机号码'
                  : `验证码已发送至 ${phoneNumber}`}
              </Text>
            </View>

            {/* 输入区域 */}
            <View style={styles.inputContainer}>
              {currentStep === 'phone' ? (
                <View style={styles.phoneInputContainer}>
                  <Text style={styles.prefixText}>+86</Text>
                  <TextInput
                    style={styles.phoneInput}
                    placeholder="请输入手机号码"
                    placeholderTextColor={THEME.colors.textSecondary}
                    value={phoneNumber}
                    onChangeText={setPhoneNumber}
                    keyboardType="numeric"
                    maxLength={11}
                    autoFocus
                  />
                </View>
              ) : (
                <View>
                  <TextInput
                    style={styles.codeInput}
                    placeholder="请输入6位验证码"
                    placeholderTextColor={THEME.colors.textSecondary}
                    value={verificationCode}
                    onChangeText={setVerificationCode}
                    keyboardType="numeric"
                    maxLength={6}
                    autoFocus
                  />
                  
                  {/* 重新发送按钮 */}
                  <TouchableOpacity
                    style={styles.resendButton}
                    onPress={resendCode}
                    disabled={countdown > 0}
                  >
                    <Text
                      style={[
                        styles.resendText,
                        countdown > 0 && styles.resendTextDisabled,
                      ]}
                    >
                      {countdown > 0 ? `重新发送 (${countdown}s)` : '重新发送'}
                    </Text>
                  </TouchableOpacity>
                </View>
              )}
            </View>

            {/* 按钮区域 */}
            <View style={styles.buttonContainer}>
              <TouchableOpacity
                style={[
                  styles.primaryButton,
                  isLoading && styles.buttonDisabled,
                ]}
                onPress={currentStep === 'phone' ? sendVerificationCode : verifyAndLogin}
                disabled={isLoading}
              >
                <Text style={styles.primaryButtonText}>
                  {isLoading
                    ? '处理中...'
                    : currentStep === 'phone'
                    ? '获取验证码'
                    : '确认登录'}
                </Text>
              </TouchableOpacity>

              {currentStep === 'verify' && (
                <TouchableOpacity
                  style={styles.secondaryButton}
                  onPress={backToPhoneInput}
                >
                  <Text style={styles.secondaryButtonText}>返回修改手机号</Text>
                </TouchableOpacity>
              )}
            </View>

            {/* 协议区域 */}
            <View style={styles.agreementContainer}>
              <Text style={styles.agreementText}>
                登录即表示同意{' '}
                <Text style={styles.linkText}>用户协议</Text>
                {' '}和{' '}
                <Text style={styles.linkText}>隐私政策</Text>
              </Text>
            </View>
          </Animated.View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

// ===== 样式定义 =====
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: THEME.colors.background,
  },
  keyboardContainer: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingHorizontal: THEME.spacing.xl,
  },
  contentContainer: {
    alignItems: 'center',
  },
  headerContainer: {
    alignItems: 'center',
    marginBottom: THEME.spacing.xl * 2,
  },
  titleText: {
    fontSize: 28,
    fontWeight: '300',
    color: THEME.colors.text,
    marginBottom: THEME.spacing.md,
    letterSpacing: 1,
  },
  subtitleText: {
    fontSize: 16,
    color: THEME.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  inputContainer: {
    width: '100%',
    marginBottom: THEME.spacing.xl * 2,
  },
  phoneInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: THEME.colors.border,
    paddingVertical: THEME.spacing.md,
  },
  prefixText: {
    fontSize: 18,
    color: THEME.colors.text,
    marginRight: THEME.spacing.md,
    fontWeight: '400',
  },
  phoneInput: {
    flex: 1,
    fontSize: 18,
    color: THEME.colors.text,
    paddingVertical: 0,
  },
  codeInput: {
    width: '100%',
    fontSize: 18,
    color: THEME.colors.text,
    borderBottomWidth: 1,
    borderBottomColor: THEME.colors.border,
    paddingVertical: THEME.spacing.md,
    textAlign: 'center',
    letterSpacing: 8,
  },
  resendButton: {
    alignSelf: 'center',
    marginTop: THEME.spacing.lg,
  },
  resendText: {
    fontSize: 14,
    color: THEME.colors.primary,
    fontWeight: '400',
  },
  resendTextDisabled: {
    color: THEME.colors.textSecondary,
  },
  buttonContainer: {
    width: '100%',
    alignItems: 'center',
  },
  primaryButton: {
    width: '100%',
    backgroundColor: THEME.colors.primary,
    paddingVertical: THEME.spacing.lg,
    borderRadius: THEME.radius.md,
    alignItems: 'center',
    marginBottom: THEME.spacing.md,
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  primaryButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: THEME.colors.surface,
    letterSpacing: 1,
  },
  secondaryButton: {
    paddingVertical: THEME.spacing.md,
  },
  secondaryButtonText: {
    fontSize: 14,
    color: THEME.colors.textSecondary,
    fontWeight: '400',
  },
  agreementContainer: {
    marginTop: THEME.spacing.xl,
    paddingHorizontal: THEME.spacing.md,
  },
  agreementText: {
    fontSize: 12,
    color: THEME.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 18,
  },
  linkText: {
    color: THEME.colors.primary,
    fontWeight: '500',
  },
});

export default AuthScreen; 