import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Req,
  HttpStatus,
} from "@nestjs/common";
import type { Request } from "express";
import { CommentsService } from "./comments.service";
import { CreateCommentDto, UpdateCommentDto, CommentQueryDto } from "./dto";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import type { ApiResponseDto } from "../../common/dto/api-response.dto";
import type { PaginatedResponseDto } from "../../common/dto/paginated-response.dto";
import type {
  CommentResponseDto,
  CommentDetailResponseDto,
  CommentLikeResponseDto,
  CommentStatsResponseDto,
} from "./dto";
import { CommentTargetType } from "./entities/comment.entity";

/**
 * 评论控制器
 * 提供评论相关的API接口
 */
@Controller("comments")
export class CommentsController {
  constructor(private readonly commentsService: CommentsService) {}

  /**
   * 创建评论
   * POST /comments
   */
  @Post()
  @UseGuards(JwtAuthGuard)
  async createComment(
    @Body() createCommentDto: CreateCommentDto,
    @Req() req: Request & { user: { id: string } },
  ): Promise<ApiResponseDto<CommentResponseDto>> {
    const comment = await this.commentsService.createComment(
      createCommentDto,
      req.user.id,
    );

    return {
      success: true,
      statusCode: HttpStatus.CREATED,
      message: "评论创建成功",
      data: comment,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 获取评论列表
   * GET /comments
   */
  @Get()
  async getComments(
    @Query() query: CommentQueryDto,
    @Req() req: Request & { user: { id: string } },
  ): Promise<ApiResponseDto<PaginatedResponseDto<CommentResponseDto>>> {
    const currentUserId = req.user?.id;
    const comments = await this.commentsService.getComments(
      query,
      currentUserId,
    );

    return {
      success: true,
      statusCode: HttpStatus.OK,
      message: "评论列表获取成功",
      data: comments,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 获取故事评论
   * GET /stories/:id/comments
   */
  @Get("/stories/:id/comments")
  async getStoryComments(
    @Param("id") storyId: string,
    @Query() query: Omit<CommentQueryDto, "targetType" | "targetId">,
    @Req() req: Request & { user: { id: string } },
  ): Promise<ApiResponseDto<PaginatedResponseDto<CommentResponseDto>>> {
    const currentUserId = req.user?.id;
    const fullQuery: CommentQueryDto = {
      ...query,
      targetType: CommentTargetType.STORY,
      targetId: storyId,
    };

    const comments = await this.commentsService.getComments(
      fullQuery,
      currentUserId,
    );

    return {
      success: true,
      statusCode: HttpStatus.OK,
      message: "故事评论获取成功",
      data: comments,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 获取评论详情
   * GET /comments/:id
   */
  @Get(":id")
  async getCommentDetail(
    @Param("id") commentId: string,
    @Req() req: Request & { user: { id: string } },
  ): Promise<ApiResponseDto<CommentDetailResponseDto>> {
    const currentUserId = req.user?.id;
    const comment = await this.commentsService.getCommentDetail(
      commentId,
      currentUserId,
    );

    return {
      success: true,
      statusCode: HttpStatus.OK,
      message: "评论详情获取成功",
      data: comment,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 更新评论
   * PUT /comments/:id
   */
  @Put(":id")
  @UseGuards(JwtAuthGuard)
  async updateComment(
    @Param("id") commentId: string,
    @Body() updateCommentDto: UpdateCommentDto,
    @Req() req: Request & { user: { id: string } },
  ): Promise<ApiResponseDto<CommentResponseDto>> {
    const comment = await this.commentsService.updateComment(
      commentId,
      updateCommentDto,
      req.user.id,
    );

    return {
      success: true,
      statusCode: HttpStatus.OK,
      message: "评论更新成功",
      data: comment,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 删除评论
   * DELETE /comments/:id
   */
  @Delete(":id")
  @UseGuards(JwtAuthGuard)
  async deleteComment(
    @Param("id") commentId: string,
    @Req() req: Request & { user: { id: string } },
  ): Promise<ApiResponseDto<null>> {
    await this.commentsService.deleteComment(commentId, req.user.id);

    return {
      success: true,
      statusCode: HttpStatus.OK,
      message: "评论删除成功",
      data: null,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 点赞/取消点赞评论
   * PUT /comments/:id/like
   */
  @Put(":id/like")
  @UseGuards(JwtAuthGuard)
  async toggleCommentLike(
    @Param("id") commentId: string,
    @Req() req: Request & { user: { id: string } },
  ): Promise<ApiResponseDto<CommentLikeResponseDto>> {
    const result = await this.commentsService.toggleCommentLike(
      commentId,
      req.user.id,
    );

    return {
      success: true,
      statusCode: HttpStatus.OK,
      message: result.isLiked ? "点赞成功" : "取消点赞成功",
      data: result,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 获取评论统计
   * GET /stories/:id/comment-stats
   */
  @Get("/stories/:id/comment-stats")
  async getCommentStats(
    @Param("id") storyId: string,
    @Req() _req: Request & { user: { id: string } },
  ): Promise<ApiResponseDto<CommentStatsResponseDto>> {
    const stats = await this.commentsService.getCommentStats(
      CommentTargetType.STORY,
      storyId,
    );

    return {
      success: true,
      statusCode: HttpStatus.OK,
      message: "评论统计获取成功",
      data: stats,
      timestamp: new Date().toISOString(),
    };
  }
}
