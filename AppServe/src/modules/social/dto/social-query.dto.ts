import { IsOptional, IsEnum, IsString } from "class-validator";
import { Transform, Type } from "class-transformer";
import { FollowStatus } from "../entities/user-follow.entity";
import { GroupType, GroupVisibility } from "../entities/friend-group.entity";
import {
  MemberStatus,
  MemberRole,
} from "../entities/friend-group-member.entity";
import { PaginationQueryDto } from "../../../common/dto/pagination-query.dto";

/**
 * 关注列表查询DTO
 */
export class FollowListQueryDto extends PaginationQueryDto {
  @IsOptional()
  @IsEnum(FollowStatus, {
    message: "关注状态必须是有效的选项",
  })
  status?: FollowStatus = FollowStatus.ACTIVE;

  @IsOptional()
  @Type(() => Boolean)
  isSpecial?: boolean;

  @IsOptional()
  @IsString({
    message: "搜索关键词必须是字符串",
  })
  search?: string;

  @IsOptional()
  @Transform(({ value }: { value: string }) => value?.toLowerCase())
  @IsString({
    message: "排序字段必须是字符串",
  })
  sortBy?: string = "createdAt";

  @IsOptional()
  @Transform(({ value }: { value: string }) => value?.toUpperCase())
  @Type(() => String)
  sortOrder?: "ASC" | "DESC" = "DESC";
}

/**
 * 好友分组查询DTO
 */
export class FriendGroupQueryDto extends PaginationQueryDto {
  @IsOptional()
  @IsEnum(GroupType, {
    message: "分组类型必须是有效的选项",
  })
  groupType?: GroupType;

  @IsOptional()
  @IsEnum(GroupVisibility, {
    message: "分组可见性必须是有效的选项",
  })
  visibility?: GroupVisibility;

  @IsOptional()
  @Type(() => Boolean)
  isActive?: boolean = true;

  @IsOptional()
  @IsString({
    message: "搜索关键词必须是字符串",
  })
  search?: string;

  @IsOptional()
  @Transform(({ value }: { value: string }) => value?.toLowerCase())
  @IsString({
    message: "排序字段必须是字符串",
  })
  sortBy?: string = "sortOrder";

  @IsOptional()
  @Transform(({ value }: { value: string }) => value?.toUpperCase())
  @Type(() => String)
  sortOrder?: "ASC" | "DESC" = "ASC";
}

/**
 * 分组成员查询DTO
 */
export class GroupMemberQueryDto extends PaginationQueryDto {
  @IsOptional()
  @IsEnum(MemberStatus, {
    message: "成员状态必须是有效的选项",
  })
  status?: MemberStatus = MemberStatus.ACTIVE;

  @IsOptional()
  @IsEnum(MemberRole, {
    message: "成员角色必须是有效的选项",
  })
  role?: MemberRole;

  @IsOptional()
  @IsString({
    message: "搜索关键词必须是字符串",
  })
  search?: string;

  @IsOptional()
  @Transform(({ value }: { value: string }) => value?.toLowerCase())
  @IsString({
    message: "排序字段必须是字符串",
  })
  sortBy?: string = "addedAt";

  @IsOptional()
  @Transform(({ value }: { value: string }) => value?.toUpperCase())
  @Type(() => String)
  sortOrder?: "ASC" | "DESC" = "DESC";
}
