/**
 * 首页模块 - 企业级单元测试
 */

import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import { HomepageModule } from "./homepage.module";
import { HomepageService } from "./homepage.service";
import { HomepageController } from "./homepage.controller";
import { User } from "../users/entities/user.entity";
import { Story } from "../stories/entities/story.entity";
import { Character } from "../characters/entities/character.entity";
import { Bookmark } from "../bookmarks/entities/bookmark.entity";
import { TimelineEvent } from "../timeline/entities/timeline-event.entity";
import { ReferenceCollection } from "../story-references/entities/reference-collection.entity";
import { UserFollow } from "../social/entities/user-follow.entity";
import { FriendGroup } from "../social/entities/friend-group.entity";

describe("HomepageModule - 企业级模块测试", () => {
  let module: TestingModule;

  // Mock Repository工厂
  const createMockRepository = () => ({
    find: jest.fn(),
    findOne: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    createQueryBuilder: jest.fn(() => ({
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      getMany: jest.fn(),
      getOne: jest.fn(),
    })),
  });

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [HomepageModule],
    })
      .overrideProvider(getRepositoryToken(User))
      .useValue(createMockRepository())
      .overrideProvider(getRepositoryToken(Story))
      .useValue(createMockRepository())
      .overrideProvider(getRepositoryToken(Character))
      .useValue(createMockRepository())
      .overrideProvider(getRepositoryToken(Bookmark))
      .useValue(createMockRepository())
      .overrideProvider(getRepositoryToken(TimelineEvent))
      .useValue(createMockRepository())
      .overrideProvider(getRepositoryToken(ReferenceCollection))
      .useValue(createMockRepository())
      .overrideProvider(getRepositoryToken(UserFollow))
      .useValue(createMockRepository())
      .overrideProvider(getRepositoryToken(FriendGroup))
      .useValue(createMockRepository())
      .compile();
  });

  afterEach(async () => {
    if (module) {
      await module.close();
    }
  });

  describe("模块初始化", () => {
    it("should compile the module", () => {
      expect(module).toBeDefined();
    });

    it("should provide HomepageService", () => {
      const service = module.get<HomepageService>(HomepageService);
      expect(service).toBeDefined();
    });

    it("should provide HomepageController", () => {
      const controller = module.get<HomepageController>(HomepageController);
      expect(controller).toBeDefined();
    });
  });

  describe("依赖注入", () => {
    it("should inject HomepageService into HomepageController", () => {
      const controller = module.get<HomepageController>(HomepageController);
      const service = module.get<HomepageService>(HomepageService);

      expect(controller).toBeDefined();
      expect(service).toBeDefined();
    });

    it("should inject all required repositories", () => {
      const service = module.get<HomepageService>(HomepageService);
      expect(service).toBeDefined();

      // 验证所有实体的Repository都可以获取
      const repositories = [
        getRepositoryToken(User),
        getRepositoryToken(Story),
        getRepositoryToken(Character),
        getRepositoryToken(Bookmark),
        getRepositoryToken(TimelineEvent),
        getRepositoryToken(ReferenceCollection),
        getRepositoryToken(UserFollow),
        getRepositoryToken(FriendGroup),
      ];

      repositories.forEach((token) => {
        const repository = module.get(token);
        expect(repository).toBeDefined();
      });
    });
  });

  describe("模块配置", () => {
    it("should be a valid NestJS module", () => {
      expect(HomepageModule).toBeDefined();
      expect(typeof HomepageModule).toBe("function");
    });

    it("should export HomepageService", () => {
      const service = module.get<HomepageService>(HomepageService);
      expect(service).toBeDefined();
    });
  });

  describe("TypeORM集成", () => {
    it("should configure TypeORM with all required entities", () => {
      // 验证所有实体都已注册
      const entities = [
        User,
        Story,
        Character,
        Bookmark,
        TimelineEvent,
        ReferenceCollection,
        UserFollow,
        FriendGroup,
      ];

      entities.forEach((entity) => {
        expect(entity).toBeDefined();
        expect(typeof entity).toBe("function");
      });
    });
  });
});
