# 有故事APP PostgreSQL 15 生产环境配置
# 针对阿里云ECS优化 (假设2核4GB配置)

# 连接设置
max_connections = 100
superuser_reserved_connections = 3

# 内存设置
shared_buffers = 256MB                    # 25% 内存
effective_cache_size = 1GB                # 75% 内存
work_mem = 4MB                           # 内存/max_connections/3
maintenance_work_mem = 64MB               # 内存/16

# 检查点设置
wal_buffers = 16MB
checkpoint_completion_target = 0.9
checkpoint_timeout = 10min
max_wal_size = 512MB
min_wal_size = 80MB

# 查询规划器
default_statistics_target = 100
random_page_cost = 1.1

# 日志设置
logging_collector = on
log_directory = 'pg_log'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_truncate_on_rotation = on
log_rotation_age = 1d
log_rotation_size = 100MB
log_min_duration_statement = 1000        # 记录超过1秒的查询
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
log_checkpoints = on
log_connections = on
log_disconnections = on
log_lock_waits = on

# 自动清理
autovacuum = on
autovacuum_max_workers = 3
autovacuum_naptime = 20s

# 性能优化
effective_io_concurrency = 2
max_worker_processes = 4
max_parallel_workers_per_gather = 2
max_parallel_workers = 4
max_parallel_maintenance_workers = 2

# 安全设置
ssl = off                                # 容器内通信不需要SSL
password_encryption = scram-sha-256

# 区域设置
lc_messages = 'en_US.utf8'
lc_monetary = 'en_US.utf8'
lc_numeric = 'en_US.utf8'
lc_time = 'en_US.utf8'
default_text_search_config = 'pg_catalog.english'

# 时区
timezone = 'Asia/Shanghai'

# 其他优化
tcp_keepalives_idle = 600
tcp_keepalives_interval = 30
tcp_keepalives_count = 3 