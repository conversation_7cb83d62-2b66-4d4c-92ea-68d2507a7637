/// API配置类
class ApiConfig {
  ApiConfig._();
  
  // 基础配置
  static const String prodBaseUrl = 'https://api.yougushi.com/v1';
  static const String devBaseUrl = 'http://localhost:3000/v1';
  static const String wsUrl = 'wss://api.yougushi.com/ws';
  static const String devWsUrl = 'ws://localhost:3000/ws';
  
  // 当前环境
  static const bool isProduction = bool.fromEnvironment('dart.vm.product');
  static String get baseUrl => isProduction ? prodBaseUrl : devBaseUrl;
  static String get websocketUrl => isProduction ? wsUrl : devWsUrl;
  
  // 超时配置
  static const Duration connectTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  static const Duration sendTimeout = Duration(seconds: 30);
  
  // API版本
  static const String apiVersion = 'v1';
  
  // 请求头
  static const Map<String, String> defaultHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-App-Version': '1.0.0',
    'X-Platform': 'flutter',
  };
}