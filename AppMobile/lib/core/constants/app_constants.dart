/// 应用常量
class AppConstants {
  AppConstants._();
  
  // 应用信息
  static const String appName = 'YGS';
  static const String appChineseName = '有故事';
  static const String appSlogan = '让每个家庭的故事都被记录和传承';
  
  // 分页配置
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // 文件大小限制
  static const int maxImageSize = 10 * 1024 * 1024; // 10MB
  static const int maxVideoSize = 100 * 1024 * 1024; // 100MB
  static const int maxAvatarSize = 5 * 1024 * 1024; // 5MB
  
  // 输入限制
  static const int maxStoryTitleLength = 100;
  static const int maxStoryContentLength = 50000;
  static const int maxCharacterNameLength = 20;
  static const int maxCharacterDescLength = 500;
  static const int maxCommentLength = 1000;
  static const int maxUserNicknameLength = 20;
  static const int maxUserBioLength = 200;
  
  // 验证码相关
  static const int smsCodeLength = 6;
  static const int smsCodeExpireMinutes = 5;
  static const int smsCodeResendSeconds = 60;
  
  // Token相关
  static const int tokenRefreshDays = 14;
  static const int tokenExpireDays = 30;
  
  // AI配额
  static const int freeAiQuota = 5;
  static const int vipAiQuota = 50;
  
  // 点亮系统
  static const int maxLightingStages = 10;
  static const int maxCharactersPerStory = 50;
  
  // 权限级别
  static const List<String> storyPermissionLevels = [
    'private',     // 仅自己可见
    'lighting',    // 点亮人物可见
    'friends',     // 好友可见
    'followers',   // 关注者可见
    'login',       // 登录用户可见
    'public',      // 完全公开
  ];
  
  // 展示控制选项
  static const List<String> displayControlOptions = [
    'show_author',        // 显示作者
    'show_characters',    // 显示人物
    'show_lighting',      // 显示点亮信息
    'allow_comments',     // 允许评论
    'allow_likes',        // 允许点赞
    'allow_collect',      // 允许收藏
    'allow_share',        // 允许分享
    'allow_lighting',     // 允许点亮申请
  ];
  
  // 缓存时间
  static const Duration cacheExpiration = Duration(hours: 24);
  static const Duration shortCacheExpiration = Duration(minutes: 30);
  
  // 动画时长
  static const Duration defaultAnimationDuration = Duration(milliseconds: 300);
  static const Duration longAnimationDuration = Duration(milliseconds: 500);
  static const Duration pageTransitionDuration = Duration(milliseconds: 250);
}