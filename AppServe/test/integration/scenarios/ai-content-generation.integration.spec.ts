/**
 * AI内容生成系统集成测试 - 企业级配额管理和成本控制验证
 *
 * 基于深入分析后端AI模块发现的核心功能：
 * 1. AI生成标题和内容（Mock AI Provider）
 * 2. 每日配额管理（Redis + 数据库）
 * 3. 使用记录和成本追踪
 * 4. 企业级错误处理和降级策略
 * 5. 配额退还机制（失败时）
 * 6. 异常行为检测和限制
 *
 * 核心业务场景：
 * - 正常AI生成流程（标题/内容）
 * - 配额管理和重置机制
 * - 成本控制和统计分析
 * - 多用户并发场景
 * - 错误处理和配额退还
 * - Mock AI Provider集成测试
 */

import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import type { DataSource } from "typeorm";
import { getDataSourceToken } from "@nestjs/typeorm";
import { AppModule } from "../../../src/app.module";
import { IntegrationTestDataFactory } from "../setup/database-setup";
import type { User } from "../../../src/modules/users/entities/user.entity";
import { AiUsageLog } from "../../../src/modules/ai/entities/ai-usage-log.entity";
import { AiService } from "../../../src/modules/ai/ai.service";
import { EnhancedRedisService } from "../../../src/common/services/enhanced-redis.service";
import { MockAIProvider } from "../../../src/modules/ai/providers/mock-ai.provider";
import { UsersService } from "../../../src/modules/users/users.service";
import { JwtAuthGuard } from "../../../src/modules/auth/guards/jwt-auth.guard";

/**
 * AI内容生成集成测试 - 企业级配额和成本控制
 */
describe("AI Content Generation - Enterprise Integration Tests", () => {
  let app: TestingModule;
  let dataSource: DataSource;
  let dataFactory: IntegrationTestDataFactory;
  let aiService: AiService;
  let redisService: EnhancedRedisService;
  let mockAIProvider: MockAIProvider;
  let mockUsersService: UsersService;

  // 企业级测试用户
  let normalUser: User;
  let mockHeavyUser: User;
  let mockRestrictedUser: User;
  let newUser: User;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({
        canActivate: jest.fn().mockReturnValue(true),
      })
      .compile();

    app = moduleFixture;
    dataSource = moduleFixture.get<DataSource>(getDataSourceToken());
    dataFactory = new IntegrationTestDataFactory(dataSource);
    aiService = moduleFixture.get<AiService>(AiService);
    redisService =
      moduleFixture.get<EnhancedRedisService>(EnhancedRedisService);
    mockAIProvider = moduleFixture.get<MockAIProvider>(MockAIProvider);
    mockUsersService = moduleFixture.get<UsersService>(UsersService);

    // 创建测试用户矩阵
    normalUser = await dataFactory.createUser({
      phone: "13800138001",
      nickname: "正常用户",
      aiQuotaRemaining: 3,
      aiQuotaResetDate: new Date(),
    });

    mockHeavyUser = await dataFactory.createUser({
      phone: "13800138002",
      nickname: "重度使用用户",
      aiQuotaRemaining: 0, // 已用完配额
      aiQuotaResetDate: new Date(),
    });

    mockRestrictedUser = await dataFactory.createUser({
      phone: "13800138003",
      nickname: "受限用户",
      aiQuotaRemaining: 1,
      anomalyStatus: "restricted",
    });

    newUser = await dataFactory.createUser({
      phone: "13800138004",
      nickname: "新注册用户",
      aiQuotaRemaining: 3,
      aiQuotaResetDate: new Date(),
    });
  });

  describe("企业级AI生成功能核心流程", () => {
    it("应该成功执行完整的标题生成流程并记录所有相关数据", async () => {
      const startTime = Date.now();
      const keywords = ["大学", "友情", "回忆"];

      // 1. 验证用户配额状态
      const initialQuota = await aiService.getRemainingQuota(normalUser.id);
      expect(initialQuota).toBe(3);

      // 2. 执行标题生成
      const generatedTitle = await aiService.generateStoryTitle(
        normalUser.id,
        keywords,
      );

      // 3. 验证生成结果
      expect(generatedTitle).toBeDefined();
      expect(typeof generatedTitle).toBe("string");
      expect(generatedTitle.length).toBeGreaterThan(0);
      expect(generatedTitle.length).toBeLessThanOrEqual(50);

      // 4. 验证配额消耗
      const remainingQuota = await aiService.getRemainingQuota(normalUser.id);
      expect(remainingQuota).toBe(2);

      // 5. 验证Redis中的配额记录
      const today = new Date().toISOString().split("T")[0];
      const quotaKey = `ai_quota:${normalUser.id}:${today}`;
      const usedCount = await redisService.get(quotaKey);
      expect(parseInt(usedCount || "0")).toBe(1);

      // 6. 验证数据库使用日志
      const usageLogs = await dataSource.getRepository(AiUsageLog).find({
        where: {
          userId: normalUser.id,
          featureType: "title_generation",
        },
        order: { createdAt: "DESC" },
      });

      expect(usageLogs).toHaveLength(1);
      const log = usageLogs[0];
      expect(log.success).toBe(true);
      expect(log.promptTokens).toBeGreaterThan(0);
      expect(log.completionTokens).toBeGreaterThan(0);
      expect(log.costEstimate).toBeGreaterThan(0);

      // 7. 验证性能要求
      const executionTime = Date.now() - startTime;
      expect(executionTime).toBeLessThan(5000); // 5秒SLA

      console.log(
        `AI标题生成完成，耗时: ${executionTime}ms, 标题: "${generatedTitle}"`,
      );
    });

    it("应该成功执行完整的内容生成流程并保证内容质量", async () => {
      const prompt = "写一个关于大学友情的温馨故事";
      const startTime = Date.now();

      // 1. 执行内容生成
      const generatedContent = await aiService.generateStoryContent(
        normalUser.id,
        prompt,
      );

      // 2. 验证生成内容质量
      expect(generatedContent).toBeDefined();
      expect(typeof generatedContent).toBe("string");
      expect(generatedContent.length).toBeGreaterThan(50); // 最少50字符
      expect(generatedContent.length).toBeLessThanOrEqual(2000); // 最多2000字符

      // 3. 验证内容安全性（不包含敏感词）
      const sensitiveWords = ["政治", "暴力", "血腥", "恐怖"];
      sensitiveWords.forEach((word) => {
        expect(generatedContent).not.toContain(word);
      });

      // 4. 验证配额消耗
      const remainingQuota = await aiService.getRemainingQuota(normalUser.id);
      expect(remainingQuota).toBe(1); // 从3减到1（之前已消耗1个）

      // 5. 验证成本估算
      const usageLogs = await dataSource.getRepository(AiUsageLog).find({
        where: {
          userId: normalUser.id,
          featureType: "content_generation",
        },
      });

      expect(usageLogs).toHaveLength(1);
      const log = usageLogs[0];
      expect(log.costEstimate).toBeGreaterThan(0);
      expect(log.totalTokens).toBe(log.promptTokens + log.completionTokens);

      const executionTime = Date.now() - startTime;
      console.log(
        `AI内容生成完成，耗时: ${executionTime}ms, 内容长度: ${generatedContent.length}字符`,
      );
    });

    it("应该正确处理Mock AI Provider的各种响应场景", async () => {
      // 1. 测试不同类型的关键词输入
      const testCases = [
        {
          keywords: ["爱情", "青春"],
          expectedTitlePattern: /.*爱情.*|.*青春.*/,
        },
        {
          keywords: ["科幻", "未来"],
          expectedTitlePattern: /.*科幻.*|.*未来.*/,
        },
        {
          keywords: ["历史", "传奇"],
          expectedTitlePattern: /.*历史.*|.*传奇.*/,
        },
      ];

      for (const testCase of testCases) {
        const title = await aiService.generateStoryTitle(
          newUser.id,
          testCase.keywords,
        );

        expect(title).toMatch(testCase.expectedTitlePattern);
        console.log(
          `关键词: [${testCase.keywords.join(", ")}] -> 标题: "${title}"`,
        );
      }

      // 2. 验证Mock AI Provider状态
      const providerInfo = await aiService.getProviderInfo();
      expect(providerInfo.name).toBe("MockAI");
      expect(providerInfo.isAvailable).toBe(true);
      expect(providerInfo.healthStatus).toBeDefined();
    });
  });

  describe("企业级配额管理和限制机制", () => {
    it("应该正确执行每日配额限制和重置机制", async () => {
      // 1. 用户连续使用3次配额
      for (let i = 0; i < 3; i++) {
        await aiService.generateStoryTitle(normalUser.id, [`关键词${i}`]);
      }

      // 2. 验证配额用尽
      const remainingQuota = await aiService.getRemainingQuota(normalUser.id);
      expect(remainingQuota).toBe(0);

      // 3. 尝试第4次使用应该失败
      await expect(
        aiService.generateStoryTitle(normalUser.id, ["超额使用"]),
      ).rejects.toThrow("今日AI生成配额已用完");

      // 4. 测试配额重置功能
      await aiService.resetDailyQuota(normalUser.id);

      const resetQuota = await aiService.getRemainingQuota(normalUser.id);
      expect(resetQuota).toBe(3);

      // 5. 重置后应该可以正常使用
      const titleAfterReset = await aiService.generateStoryTitle(
        normalUser.id,
        ["重置后测试"],
      );
      expect(titleAfterReset).toBeDefined();
    });

    it.skip("应该正确处理受限用户的AI使用限制", async () => {
      // TODO: 实现 usersService.checkUserAiPermission 方法
      // 1. 受限用户尝试使用AI（应该被拒绝或有特殊处理）
      // const result = await usersService.checkUserAiPermission(
      //   restrictedUser.id,
      // );
      // expect(result.canUse).toBe(false);
      // expect(result.reason).toContain("账户受限");
      // 2. 受限用户强制使用AI应该失败
      // await expect(
      //   aiService.generateStoryTitle(restrictedUser.id, ["受限测试"]),
      // ).rejects.toThrow("账户状态异常，无法使用AI功能");
    });

    it("应该正确处理并发配额请求和原子性保证", async () => {
      // 1. 创建多个并发用户
      const concurrentUsers = [];
      for (let i = 0; i < 5; i++) {
        const user = await dataFactory.createUser({
          phone: `1380013800${5 + i}`,
          nickname: `并发用户${i}`,
          aiQuotaRemaining: 1, // 每人只有1次配额
        });
        concurrentUsers.push(user);
      }

      // 2. 所有用户同时请求AI生成
      const concurrentRequests = concurrentUsers.map((user) =>
        aiService.generateStoryTitle(user.id, ["并发测试"]),
      );

      const results = await Promise.allSettled(concurrentRequests);

      // 3. 验证所有请求都成功（配额独立）
      const successCount = results.filter(
        (r) => r.status === "fulfilled",
      ).length;
      expect(successCount).toBe(5);

      // 4. 验证每个用户配额都被正确扣除
      for (const user of concurrentUsers) {
        const remaining = await aiService.getRemainingQuota(user.id);
        expect(remaining).toBe(0);
      }
    });
  });

  describe("错误处理和配额退还机制", () => {
    it("应该在AI生成失败时正确退还配额", async () => {
      // 1. 记录初始配额
      const initialQuota = await aiService.getRemainingQuota(normalUser.id);

      // 2. 模拟AI Provider异常
      const mockError = jest
        .spyOn(mockAIProvider, "generateTitle")
        .mockRejectedValue(new Error("AI服务异常"));

      // 3. 尝试生成标题（应该失败）
      await expect(
        aiService.generateStoryTitle(normalUser.id, ["失败测试"]),
      ).rejects.toThrow("标题生成失败");

      // 4. 验证配额被退还
      const finalQuota = await aiService.getRemainingQuota(normalUser.id);
      expect(finalQuota).toBe(initialQuota);

      // 5. 验证失败日志被正确记录
      const failureLogs = await dataSource.getRepository(AiUsageLog).find({
        where: {
          userId: normalUser.id,
          success: false,
        },
      });

      expect(failureLogs).toHaveLength(1);
      expect(failureLogs[0].completionTokens).toBe(0);

      mockError.mockRestore();
    });

    it("应该正确处理Redis连接异常的降级场景", async () => {
      // 1. 模拟Redis连接失败
      const mockRedisError = jest
        .spyOn(redisService, "incr")
        .mockRejectedValue(new Error("Redis连接失败"));

      // 2. AI生成应该仍然可以工作（降级处理）
      const title = await aiService.generateStoryTitle(normalUser.id, [
        "降级测试",
      ]);

      expect(title).toBeDefined();

      // 3. 验证数据库日志仍然被记录
      const logs = await dataSource.getRepository(AiUsageLog).find({
        where: {
          userId: normalUser.id,
          featureType: "title_generation",
        },
      });

      expect(logs.length).toBeGreaterThan(0);

      mockRedisError.mockRestore();
    });

    it("应该正确处理无效输入参数的验证", async () => {
      // 1. 测试空关键词
      await expect(
        aiService.generateStoryTitle(normalUser.id, []),
      ).rejects.toThrow("关键词不能为空");

      // 2. 测试过多关键词
      const tooManyKeywords = Array.from(
        { length: 10 },
        (_, i) => `关键词${i}`,
      );
      await expect(
        aiService.generateStoryTitle(normalUser.id, tooManyKeywords),
      ).rejects.toThrow("关键词不能超过5个");

      // 3. 测试过长的单个关键词
      await expect(
        aiService.generateStoryTitle(normalUser.id, [
          "这是一个非常长的关键词超过了限制",
        ]),
      ).rejects.toThrow("单个关键词不能超过10个字符");

      // 4. 测试敏感词过滤
      await expect(
        aiService.generateStoryContent(
          normalUser.id,
          "生成一些包含政治敏感内容",
        ),
      ).rejects.toThrow("提示内容包含敏感词");

      // 5. 验证所有失败请求都没有消耗配额
      const finalQuota = await aiService.getRemainingQuota(normalUser.id);
      expect(finalQuota).toBe(3); // 配额未变化
    });
  });

  describe("成本控制和统计分析", () => {
    it("应该正确统计用户AI使用情况和成本分析", async () => {
      // 1. 让用户进行多次AI使用
      await aiService.generateStoryTitle(normalUser.id, ["标题1"]);
      await aiService.generateStoryContent(normalUser.id, "内容生成测试1");
      await aiService.generateStoryTitle(normalUser.id, ["标题2"]);

      // 2. 获取使用统计
      const statistics = await aiService.getUserUsageStatistics(
        normalUser.id,
        30,
      );

      // 3. 验证统计数据
      expect(statistics.totalUsage).toBe(3);
      expect(statistics.successfulUsage).toBe(3);
      expect(statistics.totalCost).toBeGreaterThan(0);
      expect(statistics.averageCostPerRequest).toBeGreaterThan(0);
      expect(statistics.usageByFeature).toHaveProperty("title_generation", 2);
      expect(statistics.usageByFeature).toHaveProperty("content_generation", 1);

      console.log("用户AI使用统计:", JSON.stringify(statistics, null, 2));
    });

    it("应该正确计算Token消耗和成本估算", async () => {
      const keywords = ["测试", "计算", "Token"];
      const prompt =
        "这是一个用于测试Token计算的提示词内容，包含中英文混合文本。";

      // 1. 执行AI生成
      await aiService.generateStoryTitle(normalUser.id, keywords);
      await aiService.generateStoryContent(normalUser.id, prompt);

      // 2. 获取使用日志
      const logs = await dataSource.getRepository(AiUsageLog).find({
        where: { userId: normalUser.id },
        order: { createdAt: "DESC" },
      });

      expect(logs).toHaveLength(2);

      // 3. 验证Token计算
      logs.forEach((log) => {
        expect(log.promptTokens).toBeGreaterThan(0);
        expect(log.completionTokens).toBeGreaterThan(0);
        expect(log.totalTokens).toBe(log.promptTokens + log.completionTokens);

        // 中文字符应该按1.5个Token计算
        expect(log.promptTokens).toBeGreaterThanOrEqual(
          Math.ceil(log.promptTokens * 0.5),
        );

        // 成本估算应该基于总Token数
        const expectedCost = (log.totalTokens / 1000) * 0.01;
        expect(log.costEstimate).toBeCloseTo(expectedCost, 4);
      });
    });
  });

  describe("性能和稳定性测试", () => {
    it("应该在高并发场景下保持稳定性能", async () => {
      const startTime = Date.now();

      // 1. 创建50个并发请求
      const concurrentRequests = Array.from({ length: 50 }, (_, i) =>
        aiService.generateStoryTitle(newUser.id, [`并发${i}`]),
      );

      // 2. 等待所有请求完成（前3个会成功，其余会因配额不足失败）
      const results = await Promise.allSettled(concurrentRequests);

      const totalTime = Date.now() - startTime;

      // 3. 验证性能和结果
      const successCount = results.filter(
        (r) => r.status === "fulfilled",
      ).length;
      const failureCount = results.filter(
        (r) => r.status === "rejected",
      ).length;

      expect(successCount).toBe(3); // 配额限制
      expect(failureCount).toBe(47); // 超出配额的请求
      expect(totalTime).toBeLessThan(10000); // 10秒内完成所有请求

      console.log(
        `并发测试完成: 成功${successCount}个, 失败${failureCount}个, 总耗时${totalTime}ms`,
      );
    });

    it("应该正确处理长时间运行的稳定性", async () => {
      // 模拟长时间运行场景：连续生成内容
      const results = [];

      for (let i = 0; i < 3; i++) {
        const startTime = Date.now();

        const title = await aiService.generateStoryTitle(normalUser.id, [
          `长期测试${i}`,
        ]);

        const endTime = Date.now();
        const duration = endTime - startTime;

        results.push({ title, duration });

        // 每次请求间隔100ms
        await new Promise((resolve) => setTimeout(resolve, 100));
      }

      // 验证所有请求都成功且性能稳定
      results.forEach((result, index) => {
        expect(result.title).toBeDefined();
        expect(result.duration).toBeLessThan(5000);

        console.log(
          `长期测试${index}: "${result.title}" (${result.duration}ms)`,
        );
      });

      // 验证内存和连接没有泄漏（通过最终配额检查）
      const finalQuota = await aiService.getRemainingQuota(normalUser.id);
      expect(finalQuota).toBe(0);
    });
  });

  afterEach(async () => {
    // 清理Redis缓存
    await redisService.clearCache();

    // 清理数据库测试数据
    await dataFactory.clearTestData();
    await app.close();
  });
});
