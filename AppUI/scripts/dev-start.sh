#!/bin/bash

# ===========================================
# 有故事 APP - 智能开发启动脚本
# ===========================================

echo "🚀 启动有故事 APP 开发环境..."

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 检查是否需要设置端口转发
check_port_forwarding() {
    echo -e "${BLUE}🔍 检查端口转发状态...${NC}"
    
    # 检查8081端口
    if curl -s --max-time 2 http://localhost:8081 > /dev/null; then
        echo -e "${GREEN}✅ Metro端口转发正常${NC}"
        METRO_OK=true
    else
        echo -e "${YELLOW}⚠️  Metro端口转发需要设置${NC}"
        METRO_OK=false
    fi
    
    # 检查3000端口
    if curl -s --max-time 2 http://localhost:3000/health > /dev/null; then
        echo -e "${GREEN}✅ API端口转发正常${NC}"
        API_OK=true
    else
        echo -e "${YELLOW}⚠️  API端口转发需要设置${NC}"
        API_OK=false
    fi
    
    # 如果都正常，不需要重新设置
    if [ "$METRO_OK" = true ] && [ "$API_OK" = true ]; then
        echo -e "${GREEN}🎉 端口转发已配置，无需重新设置${NC}"
        return 0
    else
        echo -e "${YELLOW}🔧 需要配置端口转发${NC}"
        return 1
    fi
}

# 设置端口转发
setup_port_forwarding() {
    echo -e "${BLUE}🔄 设置端口转发...${NC}"
    
    # 检查ADB和设备
    if ! command -v adb &> /dev/null; then
        if [ -n "$ANDROID_HOME" ]; then
            export PATH="$ANDROID_HOME/platform-tools:$PATH"
        else
            echo -e "${RED}❌ ADB未找到，请配置Android SDK${NC}"
            return 1
        fi
    fi
    
    # 检查设备连接
    DEVICES=$(adb devices | grep -v "List of devices" | grep -v "^$")
    if [ -z "$DEVICES" ]; then
        echo -e "${RED}❌ 未找到Android设备，请启动模拟器${NC}"
        return 1
    fi
    
    # 设置端口转发
    adb reverse tcp:8081 tcp:8081 2>/dev/null
    adb reverse tcp:3000 tcp:3000 2>/dev/null
    
    echo -e "${GREEN}✅ 端口转发设置完成${NC}"
    return 0
}

# 启动Metro
start_metro() {
    echo -e "${BLUE}📱 启动Metro bundler...${NC}"
    
    # 检查是否已经在运行
    if curl -s --max-time 2 http://localhost:8081 > /dev/null; then
        echo -e "${GREEN}✅ Metro已在运行${NC}"
        return 0
    fi
    
    echo -e "${YELLOW}🔄 启动Metro服务...${NC}"
    npx react-native start --reset-cache &
    METRO_PID=$!
    
    # 等待Metro启动
    for i in {1..30}; do
        if curl -s --max-time 2 http://localhost:8081 > /dev/null; then
            echo -e "${GREEN}✅ Metro启动成功${NC}"
            return 0
        fi
        sleep 1
    done
    
    echo -e "${RED}❌ Metro启动失败${NC}"
    return 1
}

# 检查后端服务
check_backend() {
    echo -e "${BLUE}🖥️  检查后端服务...${NC}"
    
    if curl -s --max-time 3 http://localhost:3000/health > /dev/null; then
        echo -e "${GREEN}✅ 后端服务运行正常${NC}"
        return 0
    else
        echo -e "${YELLOW}⚠️  后端服务未运行${NC}"
        echo -e "${YELLOW}💡 请在另一个终端执行: cd ../AppServe && docker-compose up${NC}"
        return 1
    fi
}

# 显示开发提示
show_dev_tips() {
    echo ""
    echo -e "${GREEN}🎉 开发环境准备就绪！${NC}"
    echo ""
    echo -e "${BLUE}📋 开发提示：${NC}"
    echo -e "  🔧 网络状态检查: ${YELLOW}./scripts/check-network.sh${NC}"
    echo -e "  🔄 重新配置网络: ${YELLOW}./scripts/setup-android-network.sh${NC}"
    echo -e "  📱 启动Android: ${YELLOW}npx react-native run-android${NC}"
    echo -e "  🍎 启动iOS: ${YELLOW}npx react-native run-ios${NC}"
    echo ""
    echo -e "${YELLOW}💡 如果遇到网络问题，运行: ./scripts/check-network.sh${NC}"
}

# 主流程
main() {
    echo -e "${GREEN}============================================${NC}"
    echo -e "${GREEN}  有故事 APP - 智能开发启动${NC}"
    echo -e "${GREEN}============================================${NC}"
    echo ""
    
    # 1. 检查后端服务
    check_backend
    
    # 2. 检查是否需要设置端口转发
    if ! check_port_forwarding; then
        # 如果需要，则设置端口转发
        if ! setup_port_forwarding; then
            exit 1
        fi
        
        # 重新检查
        echo -e "${BLUE}🔍 验证端口转发...${NC}"
        sleep 2
        if ! check_port_forwarding; then
            echo -e "${RED}❌ 端口转发设置失败${NC}"
            exit 1
        fi
    fi
    
    # 3. 启动Metro（可选）
    read -p "是否启动Metro bundler? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        start_metro
    fi
    
    # 4. 显示开发提示
    show_dev_tips
}

# 处理参数
case "${1:-}" in
    --force)
        echo "🔧 强制重新配置端口转发..."
        setup_port_forwarding
        ;;
    --check)
        check_port_forwarding
        ;;
    --metro)
        start_metro
        ;;
    *)
        main
        ;;
esac 