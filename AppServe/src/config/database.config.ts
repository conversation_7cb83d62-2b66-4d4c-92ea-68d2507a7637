import { registerAs } from "@nestjs/config";
import type { DataSourceOptions } from "typeorm";
import { DataSource } from "typeorm";
import { parseIntEnv } from "../common/utils/env.utils";

export const databaseConfig = registerAs("database", () => ({
  type: "postgres",
  host: process.env.DB_HOST || "localhost",
  port: parseIntEnv(process.env.DB_PORT, 5432),
  username: process.env.DB_USERNAME || "postgres",
  password: process.env.DB_PASSWORD || "password",
  database: process.env.DB_NAME || process.env.DB_DATABASE || "story_app_dev",
  synchronize:
    process.env.NODE_ENV === "development" && process.env.AUTO_SYNC === "true",
  logging:
    process.env.DB_LOGGING === "true" || process.env.NODE_ENV === "development",
  // 阿里云RDS SSL连接配置
  ssl:
    process.env.DB_SSL === "true"
      ? {
          rejectUnauthorized: false,
          // 阿里云RDS推荐配置
          sslmode: "require",
        }
      : false,
  entities: [__dirname + "/../**/*.entity{.ts,.js}"],
  migrations: [__dirname + "/../migrations/*{.ts,.js}"],
  autoLoadEntities: true,
  // 云端数据库连接池优化配置
  extra: {
    max: parseIntEnv(process.env.DB_MAX_CONNECTIONS, 10), // 最大连接数
    min: parseIntEnv(process.env.DB_MIN_CONNECTIONS, 2), // 最小连接数
    acquire: parseIntEnv(process.env.DB_CONNECTION_TIMEOUT, 30000), // 获取连接超时时间
    idle: parseIntEnv(process.env.DB_IDLE_TIMEOUT, 10000), // 空闲超时时间
    // 阿里云RDS连接优化
    connectionTimeoutMillis: 30000,
    idleTimeoutMillis: 10000,
    // 网络不稳定时的重连配置
    retry: {
      max: 3,
      timeout: 1000,
    },
  },
}));

// TypeORM CLI配置 - 支持云端数据库
export default new DataSource({
  type: "postgres",
  host: process.env.DB_HOST || "localhost",
  port: parseIntEnv(process.env.DB_PORT, 5432),
  username: process.env.DB_USERNAME || "postgres",
  password: process.env.DB_PASSWORD || "password",
  database: process.env.DB_NAME || process.env.DB_DATABASE || "story_app_dev",
  entities: [__dirname + "/../**/*.entity{.ts,.js}"],
  migrations: [__dirname + "/../migrations/*{.ts,.js}"],
  synchronize: false,
  logging:
    process.env.DB_LOGGING === "true" || process.env.NODE_ENV === "development",
  // 支持阿里云RDS SSL连接
  ssl:
    process.env.DB_SSL === "true"
      ? {
          rejectUnauthorized: false,
          sslmode: "require",
        }
      : false,
} as DataSourceOptions);
