import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import {
  NotFoundException,
  ConflictException,
  ForbiddenException,
} from "@nestjs/common";
import { UsersService } from "./users.service";
import { User } from "./entities/user.entity";
import { UserRelationship } from "./entities/user-relationship.entity";
import type {
  UpdateUserDto,
  UpdateDisplaySettingsDto,
  UserStatisticsDto,
} from "./dto/update-user.dto";

// Mock TypeORM Raw函数
jest.mock("typeorm", () => {
  const actual = jest.requireActual("typeorm");
  return {
    ...actual,
    Raw: jest.fn((fn) => fn),
  };
});

describe("UsersService", () => {
  let service: UsersService;

  // Mock用户实体数据
  const mockUser: User = {
    id: "test-user-id",
    phone: "13800138000",
    email: "<EMAIL>",
    passwordHash: "hashed_password",
    userNumber: "200001",
    nickname: "testuser",
    username: "test_username",
    avatarUrl: "https://example.com/avatar.jpg",
    birthDate: new Date("1990-01-01"),
    bio: "这是一个测试用户",
    aiQuotaRemaining: 3,
    aiQuotaResetDate: new Date(),
    profileDisplaySettings: {
      showBirthday: false,
      showBio: true,
      showStatistics: true,
      showCharacters: true,
      showTimeline: true,
    },
    anomalyStatus: "normal",
    status: "active",
    isActive: true,
    lastLoginAt: new Date(),
    lastLoginIp: "***********",
    failedLoginAttempts: 0,
    lockedUntil: null,
    anomalyWarningCount: 0,
    anomalyRestrictionCount: 0,
    lightingRestrictedUntil: null,
    lastInvalidLightingAttempt: null,
    dailyLightingAttempts: 0,
    lightingAttemptResetDate: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
    // 关联关系（测试时不需要）
    stories: [],
    characters: [],
    lightings: [],
    receivedLightings: [],
    relationships: [],
    relatedRelationships: [],
    shares: [],
    notifications: [],
    refreshTokens: [],
    // 方法实现
    isLocked: jest.fn().mockReturnValue(false),
    shouldLockAccount: jest.fn().mockReturnValue(false),
    isLightingRestricted: jest.fn().mockReturnValue(false),
    isSuspended: jest.fn().mockReturnValue(false),
    canApplyLighting: jest.fn().mockReturnValue(true),
    shouldResetDailyAttempts: jest.fn().mockReturnValue(false),
    shouldTriggerWarning: jest.fn().mockReturnValue(false),
    shouldTriggerRestriction: jest.fn().mockReturnValue(false),
    shouldTriggerSuspension: jest.fn().mockReturnValue(false),
  } as User;

  const mockFriend: User = {
    ...mockUser,
    id: "friend-user-id",
    userNumber: "200002",
    nickname: "frienduser",
    username: "friend_username",
    email: "<EMAIL>",
  } as User;

  const mockRelationship = {
    id: "relationship-id",
    userId: "test-user-id",
    relatedUserId: "friend-user-id",
    relationType: "friend",
    intimacyLevel: 1,
    status: 1,
    createdAt: new Date(),
    updatedAt: new Date(),
    user: mockUser,
    relatedUser: mockFriend,
  };

  // Mock Repository和QueryBuilder - 企业级链式调用Mock
  const mockQueryBuilder = {
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    getMany: jest.fn(),
    getOne: jest.fn(),
    orderBy: jest.fn().mockReturnThis(),
    addOrderBy: jest.fn().mockReturnThis(),
  };

  const mockUserRepository = {
    findOne: jest.fn(),
    findAndCount: jest.fn(),
    createQueryBuilder: jest.fn().mockReturnValue(mockQueryBuilder),
    update: jest.fn(),
    query: jest.fn(),
    save: jest.fn(),
  };

  const mockRelationshipRepository = {
    findOne: jest.fn(),
    findAndCount: jest.fn(),
    save: jest.fn(),
    update: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepository,
        },
        {
          provide: getRepositoryToken(UserRelationship),
          useValue: mockRelationshipRepository,
        },
      ],
    }).compile();

    service = module.get<UsersService>(UsersService);

    // 确保 QueryBuilder Mock 正确设置 - 完整链式调用支持
    mockUserRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);

    // 确保所有QueryBuilder方法都返回自身，支持链式调用
    mockQueryBuilder.where.mockReturnValue(mockQueryBuilder);
    mockQueryBuilder.andWhere.mockReturnValue(mockQueryBuilder);
    mockQueryBuilder.select.mockReturnValue(mockQueryBuilder);
    mockQueryBuilder.limit.mockReturnValue(mockQueryBuilder);
    mockQueryBuilder.orderBy.mockReturnValue(mockQueryBuilder);
    mockQueryBuilder.addOrderBy.mockReturnValue(mockQueryBuilder);
  });

  afterEach(() => {
    jest.clearAllMocks();
    // 重新设置 QueryBuilder Mock - 确保链式调用正常
    mockUserRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);

    // 重置所有QueryBuilder方法的Mock
    mockQueryBuilder.where.mockReturnValue(mockQueryBuilder);
    mockQueryBuilder.andWhere.mockReturnValue(mockQueryBuilder);
    mockQueryBuilder.select.mockReturnValue(mockQueryBuilder);
    mockQueryBuilder.limit.mockReturnValue(mockQueryBuilder);
    mockQueryBuilder.orderBy.mockReturnValue(mockQueryBuilder);
    mockQueryBuilder.addOrderBy.mockReturnValue(mockQueryBuilder);
  });

  describe("findById", () => {
    it("should return user when found", async () => {
      mockUserRepository.findOne.mockResolvedValue(mockUser);

      const result = await service.findById("test-user-id");

      expect(result).toEqual(mockUser);
      expect(mockUserRepository.findOne).toHaveBeenCalledWith({
        where: { id: "test-user-id" },
        select: [
          "id",
          "userNumber",
          "nickname",
          "username",
          "avatarUrl",
          "birthDate",
          "bio",
          "profileDisplaySettings",
          "createdAt",
          "status",
        ],
      });
    });

    it("should throw NotFoundException when user not found", async () => {
      mockUserRepository.findOne.mockResolvedValue(null);

      await expect(service.findById("non-existent-id")).rejects.toThrow(
        NotFoundException,
      );
      await expect(service.findById("non-existent-id")).rejects.toThrow(
        "用户不存在",
      );
    });
  });

  describe("findByUserNumber", () => {
    it("should return user when found by user number", async () => {
      mockUserRepository.findOne.mockResolvedValue(mockUser);

      const result = await service.findByUserNumber("200001");

      expect(result).toEqual(mockUser);
      expect(mockUserRepository.findOne).toHaveBeenCalledWith({
        where: { userNumber: "200001" },
        select: [
          "id",
          "userNumber",
          "nickname",
          "username",
          "avatarUrl",
          "birthDate",
          "bio",
          "profileDisplaySettings",
          "createdAt",
          "status",
        ],
      });
    });

    it("should throw NotFoundException when user not found by user number", async () => {
      mockUserRepository.findOne.mockResolvedValue(null);

      await expect(service.findByUserNumber("999999")).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe("searchUsers", () => {
    const mockSearchResults = [
      {
        id: "user1",
        userNumber: "200001",
        nickname: "test1",
        username: "test1",
        avatarUrl: null,
      },
      {
        id: "user2",
        userNumber: "200002",
        nickname: "test2",
        username: "test2",
        avatarUrl: null,
      },
    ];

    it("should search by exact user number when query is 6 digits", async () => {
      mockQueryBuilder.getMany.mockResolvedValue(mockSearchResults);

      const result = await service.searchUsers("200001");

      expect(result).toEqual(mockSearchResults);
      expect(mockUserRepository.createQueryBuilder).toHaveBeenCalledWith(
        "user",
      );
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        "user.userNumber = :userNumber",
        { userNumber: "200001" },
      );
    });

    it("should search by nickname and username when query is not 6 digits", async () => {
      mockQueryBuilder.getMany.mockResolvedValue(mockSearchResults);

      const result = await service.searchUsers("test");

      expect(result).toEqual(mockSearchResults);
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        "user.nickname ILIKE :query OR user.username ILIKE :query",
        { query: "%test%" },
      );
    });

    it("should limit search results", async () => {
      mockQueryBuilder.getMany.mockResolvedValue(mockSearchResults);

      await service.searchUsers("test", 10);

      expect(mockQueryBuilder.limit).toHaveBeenCalledWith(10);
    });

    it("should filter by active status", async () => {
      mockQueryBuilder.getMany.mockResolvedValue(mockSearchResults);

      await service.searchUsers("test");

      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        "user.nickname ILIKE :query OR user.username ILIKE :query",
        { query: "%test%" },
      );
    });
  });

  describe("updateProfile", () => {
    const updateData: UpdateUserDto = {
      nickname: "newnickname",
      username: "newusername",
      bio: "新的个人简介",
    };

    it("should update user profile successfully", async () => {
      // Mock findById调用
      mockUserRepository.findOne
        .mockResolvedValueOnce(mockUser) // findById调用
        .mockResolvedValueOnce(null) // 昵称重复检查
        .mockResolvedValueOnce(null) // 用户名重复检查
        .mockResolvedValueOnce({ ...mockUser, ...updateData }); // 更新后的findById调用

      mockUserRepository.update.mockResolvedValue({ affected: 1 });

      const result = await service.updateProfile("test-user-id", updateData);

      expect(result).toEqual({ ...mockUser, ...updateData });
      expect(mockUserRepository.update).toHaveBeenCalledWith("test-user-id", {
        ...updateData,
        birthDate: undefined,
      });
    });

    it("should throw ConflictException when nickname already exists", async () => {
      const existingUser = { ...mockFriend, nickname: "newnickname" };

      mockUserRepository.findOne
        .mockResolvedValueOnce(mockUser) // findById调用
        .mockResolvedValueOnce(existingUser); // 昵称重复检查

      await expect(
        service.updateProfile("test-user-id", { nickname: "newnickname" }),
      ).rejects.toThrow(ConflictException);
    });

    it("should throw ConflictException when username already exists", async () => {
      const existingUser = {
        ...mockFriend,
        id: "different-user-id",
        username: "newusername",
      };

      mockUserRepository.findOne
        .mockResolvedValueOnce(mockUser) // findById调用
        .mockResolvedValueOnce(existingUser); // 用户名重复检查

      await expect(
        service.updateProfile("test-user-id", { username: "newusername" }),
      ).rejects.toThrow(ConflictException);
    });

    it("should handle birthDate conversion", async () => {
      const updateDataWithBirthDate = {
        ...updateData,
        birthDate: "1995-06-15",
      };

      mockUserRepository.findOne
        .mockResolvedValueOnce(mockUser)
        .mockResolvedValueOnce(null)
        .mockResolvedValueOnce(null)
        .mockResolvedValueOnce(mockUser);

      await service.updateProfile("test-user-id", updateDataWithBirthDate);

      expect(mockUserRepository.update).toHaveBeenCalledWith("test-user-id", {
        ...updateData,
        birthDate: new Date("1995-06-15"),
      });
    });

    it("should allow updating with same nickname/username", async () => {
      const sameData = {
        nickname: mockUser.nickname,
        username: mockUser.username,
      };

      mockUserRepository.findOne
        .mockResolvedValueOnce(mockUser)
        .mockResolvedValueOnce(mockUser);

      await service.updateProfile("test-user-id", sameData);

      expect(mockUserRepository.update).toHaveBeenCalled();
    });
  });

  describe("updateDisplaySettings", () => {
    const settingsData: UpdateDisplaySettingsDto = {
      profileDisplaySettings: {
        showBirthday: true,
        showBio: false,
        showStatistics: true,
        showCharacters: false,
        showTimeline: true,
      },
    };

    it("should update display settings successfully", async () => {
      mockUserRepository.findOne.mockResolvedValue({
        ...mockUser,
        profileDisplaySettings: settingsData.profileDisplaySettings,
      });
      mockUserRepository.update.mockResolvedValue({ affected: 1 });

      const result = await service.updateDisplaySettings(
        "test-user-id",
        settingsData,
      );

      expect(mockUserRepository.update).toHaveBeenCalledWith("test-user-id", {
        profileDisplaySettings: settingsData.profileDisplaySettings,
      });
      expect(result.profileDisplaySettings).toEqual(
        settingsData.profileDisplaySettings,
      );
    });
  });

  describe("getUserStatistics", () => {
    const mockStatistics = [
      {
        story_count: "5",
        character_count: "10",
        lighting_count: "3",
        following_count: "15",
        followers_count: "8",
      },
    ];

    it("should return user statistics", async () => {
      mockUserRepository.query.mockResolvedValue(mockStatistics);

      const result = await service.getUserStatistics("test-user-id");

      expect(result).toEqual({
        storyCount: 5,
        characterCount: 10,
        lightingCount: 3,
        followingCount: 15,
        followersCount: 8,
      });
      expect(mockUserRepository.query).toHaveBeenCalledWith(
        expect.stringContaining("SELECT"),
        ["test-user-id"],
      );
    });

    it("should handle null statistics", async () => {
      mockUserRepository.query.mockResolvedValue([
        {
          story_count: null,
          character_count: null,
          lighting_count: null,
          following_count: null,
          followers_count: null,
        },
      ]);

      const result = await service.getUserStatistics("test-user-id");

      expect(result).toEqual({
        storyCount: 0,
        characterCount: 0,
        lightingCount: 0,
        followingCount: 0,
        followersCount: 0,
      });
    });
  });

  describe("getUserProfile", () => {
    it("should return full profile for owner", async () => {
      const mockStats: UserStatisticsDto = {
        storyCount: 5,
        characterCount: 10,
        lightingCount: 3,
        followingCount: 15,
        followersCount: 8,
      };

      mockUserRepository.findOne.mockResolvedValue(mockUser);
      mockUserRepository.query.mockResolvedValue([
        {
          story_count: "5",
          character_count: "10",
          lighting_count: "3",
          following_count: "15",
          followers_count: "8",
        },
      ]);

      const result = await service.getUserProfile(
        "test-user-id",
        "test-user-id",
      );

      expect(result).toMatchObject({
        id: mockUser.id,
        userNumber: mockUser.userNumber,
        nickname: mockUser.nickname,
        username: mockUser.username,
        avatarUrl: mockUser.avatarUrl,
        bio: mockUser.bio,
        birthDate: mockUser.birthDate,
        statistics: mockStats,
      });
    });

    it("should filter sensitive info for non-owner based on settings", async () => {
      const userWithRestrictiveSettings = {
        ...mockUser,
        profileDisplaySettings: {
          showBirthday: false,
          showBio: false,
          showStatistics: false,
          showCharacters: true,
          showTimeline: true,
        },
      };

      mockUserRepository.findOne.mockResolvedValue(userWithRestrictiveSettings);

      const result = await service.getUserProfile(
        "test-user-id",
        "other-user-id",
      );

      expect(result).not.toHaveProperty("bio");
      expect(result).not.toHaveProperty("birthDate");
      expect(result).not.toHaveProperty("statistics");
    });

    it("should show allowed info for non-owner when settings permit", async () => {
      const userWithOpenSettings = {
        ...mockUser,
        profileDisplaySettings: {
          showBirthday: true,
          showBio: true,
          showStatistics: true,
          showCharacters: true,
          showTimeline: true,
        },
      };

      mockUserRepository.findOne.mockResolvedValue(userWithOpenSettings);
      mockUserRepository.query.mockResolvedValue([
        {
          story_count: "5",
          character_count: "10",
          lighting_count: "3",
          following_count: "15",
          followers_count: "8",
        },
      ]);

      const result = await service.getUserProfile(
        "test-user-id",
        "other-user-id",
      );

      expect(result).toHaveProperty("bio");
      expect(result).toHaveProperty("birthDate");
      expect(result).toHaveProperty("statistics");
    });
  });

  describe("verifyUser", () => {
    const phoneVerificationData = {
      verificationType: "phone" as const,
      phone: "13800138000",
      verificationCode: "123456",
    };

    const emailVerificationData = {
      verificationType: "email" as const,
      email: "<EMAIL>",
      verificationCode: "654321",
    };

    const identityVerificationData = {
      verificationType: "identity" as const,
      identityNumber: "11010519901201123X",
    };

    const securityVerificationData = {
      verificationType: "security" as const,
      additionalData: {
        password: "correct_password",
        biometric: true,
        twoFactor: "123456",
      },
    };

    beforeEach(() => {
      mockUserRepository.findOne.mockResolvedValue(mockUser);
      mockUserRepository.update.mockResolvedValue({ affected: 1 });
    });

    describe("Phone Verification", () => {
      it("should verify phone successfully with valid data", async () => {
        const result = await service.verifyUser(
          "test-user-id",
          phoneVerificationData,
        );

        expect(result).toEqual({
          verificationType: "phone",
          verificationStatus: "verified",
          verifiedAt: expect.any(String),
          nextSteps: ["手机号验证成功", "可以继续使用其他功能"],
        });

        expect(mockUserRepository.update).toHaveBeenCalledWith("test-user-id", {
          phone: "13800138000",
          phoneVerifiedAt: expect.any(Date),
        });
      });

      it("should throw ConflictException when phone is missing", async () => {
        await expect(
          service.verifyUser("test-user-id", {
            verificationType: "phone",
            verificationCode: "123456",
          }),
        ).rejects.toThrow(ConflictException);
        await expect(
          service.verifyUser("test-user-id", {
            verificationType: "phone",
            verificationCode: "123456",
          }),
        ).rejects.toThrow("手机号和验证码不能为空");
      });

      it("should throw ConflictException when verification code is missing", async () => {
        await expect(
          service.verifyUser("test-user-id", {
            verificationType: "phone",
            phone: "13800138000",
          }),
        ).rejects.toThrow(ConflictException);
      });

      it("should throw ConflictException when phone format is invalid", async () => {
        await expect(
          service.verifyUser("test-user-id", {
            verificationType: "phone",
            phone: "12345678901",
            verificationCode: "123456",
          }),
        ).rejects.toThrow(ConflictException);
        await expect(
          service.verifyUser("test-user-id", {
            verificationType: "phone",
            phone: "12345678901",
            verificationCode: "123456",
          }),
        ).rejects.toThrow("手机号格式不正确");
      });

      it("should throw ConflictException when verification code format is invalid", async () => {
        await expect(
          service.verifyUser("test-user-id", {
            verificationType: "phone",
            phone: "13800138000",
            verificationCode: "12345",
          }),
        ).rejects.toThrow(ConflictException);
        await expect(
          service.verifyUser("test-user-id", {
            verificationType: "phone",
            phone: "13800138000",
            verificationCode: "12345",
          }),
        ).rejects.toThrow("验证码格式不正确");
      });
    });

    describe("Email Verification", () => {
      it("should verify email successfully with valid data", async () => {
        const result = await service.verifyUser(
          "test-user-id",
          emailVerificationData,
        );

        expect(result).toEqual({
          verificationType: "email",
          verificationStatus: "verified",
          verifiedAt: expect.any(String),
          nextSteps: ["邮箱验证成功", "可以用邮箱进行登录和找回密码"],
        });

        expect(mockUserRepository.update).toHaveBeenCalledWith("test-user-id", {
          email: "<EMAIL>",
          emailVerifiedAt: expect.any(Date),
        });
      });

      it("should throw ConflictException when email format is invalid", async () => {
        await expect(
          service.verifyUser("test-user-id", {
            verificationType: "email",
            email: "invalid-email",
            verificationCode: "123456",
          }),
        ).rejects.toThrow(ConflictException);
        await expect(
          service.verifyUser("test-user-id", {
            verificationType: "email",
            email: "invalid-email",
            verificationCode: "123456",
          }),
        ).rejects.toThrow("邮箱格式不正确");
      });

      it("should throw ConflictException when email or verification code is missing", async () => {
        await expect(
          service.verifyUser("test-user-id", {
            verificationType: "email",
            verificationCode: "123456",
          }),
        ).rejects.toThrow(ConflictException);
        await expect(
          service.verifyUser("test-user-id", {
            verificationType: "email",
            verificationCode: "123456",
          }),
        ).rejects.toThrow("邮箱和验证码不能为空");
      });
    });

    describe("Identity Verification", () => {
      it("should verify identity successfully with valid data", async () => {
        mockUserRepository.findOne
          .mockResolvedValueOnce(mockUser) // verifyUser中的findById调用
          .mockResolvedValueOnce(null); // 检查身份证号是否已使用

        const result = await service.verifyUser(
          "test-user-id",
          identityVerificationData,
        );

        expect(result).toEqual({
          verificationType: "identity",
          verificationStatus: "verified",
          verifiedAt: expect.any(String),
          nextSteps: ["身份认证成功", "账户安全等级提升", "可以使用高级功能"],
        });

        expect(mockUserRepository.update).toHaveBeenCalledWith("test-user-id", {
          identityNumber: "11010519901201123X",
          identityVerifiedAt: expect.any(Date),
        });
      });

      it("should throw ConflictException when identity number is missing", async () => {
        await expect(
          service.verifyUser("test-user-id", {
            verificationType: "identity",
          }),
        ).rejects.toThrow(ConflictException);
        await expect(
          service.verifyUser("test-user-id", {
            verificationType: "identity",
          }),
        ).rejects.toThrow("身份证号码不能为空");
      });

      it("should throw ConflictException when identity number format is invalid", async () => {
        await expect(
          service.verifyUser("test-user-id", {
            verificationType: "identity",
            identityNumber: "123456789",
          }),
        ).rejects.toThrow(ConflictException);
        await expect(
          service.verifyUser("test-user-id", {
            verificationType: "identity",
            identityNumber: "123456789",
          }),
        ).rejects.toThrow("身份证号码格式不正确");
      });

      it("should throw ConflictException when identity number is already used by another user", async () => {
        const existingUser = { ...mockUser, id: "other-user-id" };

        mockUserRepository.findOne
          .mockResolvedValueOnce(mockUser) // verifyUser中的findById调用
          .mockResolvedValueOnce(existingUser); // 检查身份证号已被使用

        await expect(
          service.verifyUser("test-user-id", identityVerificationData),
        ).rejects.toThrow(ConflictException);
        await expect(
          service.verifyUser("test-user-id", identityVerificationData),
        ).rejects.toThrow("该身份证号已被其他用户使用");
      });

      it("should allow same user to verify with their own identity number", async () => {
        const existingUser = {
          ...mockUser,
          identityNumber: "11010519901201123X",
        };

        mockUserRepository.findOne
          .mockResolvedValueOnce(mockUser) // verifyUser中的findById调用
          .mockResolvedValueOnce(existingUser); // 检查身份证号，是同一个用户

        const result = await service.verifyUser(
          "test-user-id",
          identityVerificationData,
        );

        expect(result.verificationType).toBe("identity");
        expect(result.verificationStatus).toBe("verified");
      });
    });

    describe("Security Verification", () => {
      it("should verify security successfully with multiple factors", async () => {
        const result = await service.verifyUser(
          "test-user-id",
          securityVerificationData,
        );

        expect(result).toEqual({
          verificationType: "security",
          verificationStatus: "verified",
          verifiedAt: expect.any(String),
          nextSteps: [
            "安全验证成功",
            "已启用 3 种安全验证方式",
            "账户安全性大幅提升",
          ],
        });

        expect(mockUserRepository.update).toHaveBeenCalledWith("test-user-id", {
          securityLevel: 3,
          lastSecurityVerification: expect.any(Date),
        });
      });

      it("should verify security with single factor", async () => {
        const singleFactorData = {
          verificationType: "security" as const,
          additionalData: {
            password: "correct_password",
          },
        };

        const result = await service.verifyUser(
          "test-user-id",
          singleFactorData,
        );

        expect(result.nextSteps).toContain("已启用 1 种安全验证方式");
        expect(mockUserRepository.update).toHaveBeenCalledWith("test-user-id", {
          securityLevel: 1,
          lastSecurityVerification: expect.any(Date),
        });
      });

      it("should throw ConflictException when no security verification methods provided", async () => {
        await expect(
          service.verifyUser("test-user-id", {
            verificationType: "security",
            additionalData: {},
          }),
        ).rejects.toThrow(ConflictException);
        await expect(
          service.verifyUser("test-user-id", {
            verificationType: "security",
            additionalData: {},
          }),
        ).rejects.toThrow("至少需要一种安全验证方式");
      });

      it("should handle missing additionalData gracefully", async () => {
        await expect(
          service.verifyUser("test-user-id", {
            verificationType: "security",
          }),
        ).rejects.toThrow(ConflictException);
      });
    });

    describe("Error Handling", () => {
      it("should throw NotFoundException when user not found", async () => {
        mockUserRepository.findOne.mockResolvedValue(null);

        await expect(
          service.verifyUser("non-existent-user", phoneVerificationData),
        ).rejects.toThrow(NotFoundException);
        await expect(
          service.verifyUser("non-existent-user", phoneVerificationData),
        ).rejects.toThrow("用户不存在");
      });

      it("should throw ConflictException for unsupported verification type", async () => {
        await expect(
          service.verifyUser("test-user-id", {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            verificationType: "unsupported" as any,
          }),
        ).rejects.toThrow(ConflictException);
        await expect(
          service.verifyUser("test-user-id", {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            verificationType: "unsupported" as any,
          }),
        ).rejects.toThrow("不支持的验证类型");
      });

      it("should handle database update errors gracefully", async () => {
        mockUserRepository.update.mockRejectedValue(
          new Error("Database error"),
        );

        await expect(
          service.verifyUser("test-user-id", phoneVerificationData),
        ).rejects.toThrow("Database error");
      });
    });

    describe("Edge Cases", () => {
      it("should handle special characters in phone number verification", async () => {
        const specialCharData = {
          verificationType: "phone" as const,
          phone: "138-0013-8000", // 包含特殊字符
          verificationCode: "123456",
        };

        await expect(
          service.verifyUser("test-user-id", specialCharData),
        ).rejects.toThrow(ConflictException);
      });

      it("should handle very long verification codes", async () => {
        const longCodeData = {
          verificationType: "phone" as const,
          phone: "13800138000",
          verificationCode: "1234567890", // 超长验证码
        };

        await expect(
          service.verifyUser("test-user-id", longCodeData),
        ).rejects.toThrow(ConflictException);
      });

      it("should handle complex email addresses", async () => {
        const complexEmailData = {
          verificationType: "email" as const,
          email: "<EMAIL>",
          verificationCode: "123456",
        };

        const result = await service.verifyUser(
          "test-user-id",
          complexEmailData,
        );

        expect(result.verificationType).toBe("email");
        expect(result.verificationStatus).toBe("verified");
      });

      it("should validate edge case identity numbers", async () => {
        const edgeCaseIdentityData = {
          verificationType: "identity" as const,
          identityNumber: "110105199012311234", // 有效的边界情况
        };

        mockUserRepository.findOne
          .mockResolvedValueOnce(mockUser) // verifyUser中的findById调用
          .mockResolvedValueOnce(null); // 检查身份证号是否已使用

        const result = await service.verifyUser(
          "test-user-id",
          edgeCaseIdentityData,
        );

        expect(result.verificationType).toBe("identity");
        expect(result.verificationStatus).toBe("verified");
      });
    });
  });

  describe("Friend Management", () => {
    describe("isFriend", () => {
      it("should return true when users are friends", async () => {
        mockRelationshipRepository.findOne.mockResolvedValue(mockRelationship);

        const result = await service.isFriend("test-user-id", "friend-user-id");

        expect(result).toBe(true);
        expect(mockRelationshipRepository.findOne).toHaveBeenCalledWith({
          where: {
            userId: "test-user-id",
            relatedUserId: "friend-user-id",
            status: 1,
          },
        });
      });

      it("should return false when users are not friends", async () => {
        mockRelationshipRepository.findOne.mockResolvedValue(null);

        const result = await service.isFriend("test-user-id", "friend-user-id");

        expect(result).toBe(false);
      });
    });

    describe("addFriend", () => {
      it("should add friend successfully when no existing relationship", async () => {
        mockUserRepository.findOne.mockResolvedValue(mockFriend);
        mockRelationshipRepository.findOne.mockResolvedValue(null);
        mockRelationshipRepository.save.mockResolvedValue(mockRelationship);

        await service.addFriend("test-user-id", "friend-user-id");

        expect(mockRelationshipRepository.save).toHaveBeenCalledWith({
          userId: "test-user-id",
          relatedUserId: "friend-user-id",
          relationType: "friend",
          intimacyLevel: 1,
          status: 1,
        });
      });

      it("should reactivate existing inactive relationship", async () => {
        const inactiveRelationship = { ...mockRelationship, status: 2 };

        mockUserRepository.findOne.mockResolvedValue(mockFriend);
        mockRelationshipRepository.findOne.mockResolvedValue(
          inactiveRelationship,
        );
        mockRelationshipRepository.update.mockResolvedValue({ affected: 1 });

        await service.addFriend("test-user-id", "friend-user-id");

        expect(mockRelationshipRepository.update).toHaveBeenCalledWith(
          mockRelationship.id,
          { status: 1 },
        );
      });

      it("should throw ForbiddenException when trying to add self", async () => {
        await expect(
          service.addFriend("test-user-id", "test-user-id"),
        ).rejects.toThrow(ForbiddenException);
        await expect(
          service.addFriend("test-user-id", "test-user-id"),
        ).rejects.toThrow("不能添加自己为朋友");
      });

      it("should throw ConflictException when already friends", async () => {
        mockUserRepository.findOne.mockResolvedValue(mockFriend);
        mockRelationshipRepository.findOne.mockResolvedValue(mockRelationship);

        await expect(
          service.addFriend("test-user-id", "friend-user-id"),
        ).rejects.toThrow(ConflictException);
        await expect(
          service.addFriend("test-user-id", "friend-user-id"),
        ).rejects.toThrow("已经是朋友关系");
      });

      it("should throw NotFoundException when friend does not exist", async () => {
        mockUserRepository.findOne.mockRejectedValue(
          new NotFoundException("用户不存在"),
        );

        await expect(
          service.addFriend("test-user-id", "non-existent-id"),
        ).rejects.toThrow(NotFoundException);
      });
    });

    describe("removeFriend", () => {
      it("should remove friend successfully", async () => {
        mockRelationshipRepository.findOne.mockResolvedValue(mockRelationship);
        mockRelationshipRepository.update.mockResolvedValue({ affected: 1 });

        await service.removeFriend("test-user-id", "friend-user-id");

        expect(mockRelationshipRepository.update).toHaveBeenCalledWith(
          mockRelationship.id,
          { status: 2 },
        );
      });

      it("should throw NotFoundException when relationship does not exist", async () => {
        mockRelationshipRepository.findOne.mockResolvedValue(null);

        await expect(
          service.removeFriend("test-user-id", "friend-user-id"),
        ).rejects.toThrow(NotFoundException);
        await expect(
          service.removeFriend("test-user-id", "friend-user-id"),
        ).rejects.toThrow("朋友关系不存在");
      });
    });

    describe("getFriendsList", () => {
      const mockFriendsList = [
        { ...mockRelationship, relatedUser: mockFriend },
      ];

      it("should return friends list with pagination", async () => {
        mockRelationshipRepository.findAndCount.mockResolvedValue([
          mockFriendsList,
          1,
        ]);

        const result = await service.getFriendsList("test-user-id", 1, 20);

        expect(result).toEqual({
          friends: [
            {
              id: mockFriend.id,
              userNumber: mockFriend.userNumber,
              nickname: mockFriend.nickname,
              username: mockFriend.username,
              avatarUrl: mockFriend.avatarUrl,
              createdAt: mockFriend.createdAt,
            },
          ],
          total: 1,
          hasNext: false,
        });
      });

      it("should handle pagination correctly", async () => {
        mockRelationshipRepository.findAndCount.mockResolvedValue([
          mockFriendsList,
          50,
        ]);

        const result = await service.getFriendsList("test-user-id", 1, 20);

        expect(result.hasNext).toBe(true);
        expect(mockRelationshipRepository.findAndCount).toHaveBeenCalledWith({
          where: { userId: "test-user-id", status: 1 },
          relations: ["relatedUser"],
          skip: 0,
          take: 20,
          order: { createdAt: "DESC" },
        });
      });
    });
  });

  describe("deleteUser", () => {
    it("should soft delete user successfully", async () => {
      mockUserRepository.update.mockResolvedValue({ affected: 1 });

      await service.deleteUser("test-user-id");

      expect(mockUserRepository.update).toHaveBeenCalledWith("test-user-id", {
        status: "deleted",
        isActive: false,
      });
    });
  });

  describe("generateUserNumber", () => {
    it("should generate next sequential user number", async () => {
      const lastUser = { userNumber: "200005" };
      mockUserRepository.findOne
        .mockResolvedValueOnce(lastUser) // 查找最后一个用户
        .mockResolvedValueOnce(null); // 检查号码是否已使用

      const result = await service.generateUserNumber();

      expect(result).toBe("200100");
    });

    it("should start from minimum number when no users exist", async () => {
      mockUserRepository.findOne
        .mockResolvedValueOnce(null) // 没有现有用户
        .mockResolvedValueOnce(null); // 检查号码是否已使用

      const result = await service.generateUserNumber();

      expect(result).toBe("200100");
    });

    it("should skip sensitive number patterns", async () => {
      const lastUser = { userNumber: "266665" }; // 下一个会是666666（敏感）
      mockUserRepository.findOne
        .mockResolvedValueOnce(lastUser)
        .mockResolvedValueOnce(null) // 检查266667是否已使用（跳过666666）
        .mockResolvedValueOnce(null); // 再次检查

      const result = await service.generateUserNumber();

      // 由于包含666，应该跳过到下一个安全的号码
      expect(result).not.toBe("266666");
    });

    it("should throw ConflictException when reaching maximum number", async () => {
      const lastUser = { userNumber: "999999" };
      mockUserRepository.findOne.mockResolvedValue(lastUser);

      await expect(service.generateUserNumber()).rejects.toThrow(
        ConflictException,
      );
      await expect(service.generateUserNumber()).rejects.toThrow(
        "用户号码已用完",
      );
    });

    it("should recursively find next available number when collision occurs", async () => {
      const lastUser = { userNumber: "200099" }; // 下一个会是200100
      const existingUser = { userNumber: "200100" };

      mockUserRepository.findOne
        .mockResolvedValueOnce(lastUser) // 第一次：查找最后一个用户
        .mockResolvedValueOnce(existingUser) // 第一次：检查200100已存在，会递归调用
        .mockResolvedValueOnce(existingUser) // 第二次：递归调用，再次查找最后一个（数据库中现在是200100）
        .mockResolvedValueOnce(null); // 第二次：检查200101可用

      const result = await service.generateUserNumber();

      expect(result).toBe("200101"); // 递归调用找到下一个可用的号码
    });
  });

  describe("isSensitiveNumber", () => {
    it("should detect sensitive number patterns through generateUserNumber", async () => {
      // 通过间接测试来验证敏感数字检测功能
      // 我们通过设置特定的最后用户号来测试敏感数字跳过功能
      const lastUser = { userNumber: "266665" }; // 下一个会是266666（包含666）
      mockUserRepository.findOne
        .mockResolvedValueOnce(lastUser)
        .mockResolvedValueOnce(null); // 266667可用（跳过了666666）

      const result = await service.generateUserNumber();

      // 由于跳过了敏感数字666，应该生成安全的下一个号码
      expect(result).not.toContain("666");
      expect(result.length).toBe(6);
    });
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });
});
