# YGS 企业级安全管理指南

> **版本**: v1.0  
> **最后更新**: 2025-07-27  
> **安全等级**: 企业级标准  
> **适用范围**: 生产环境、测试环境、开发环境

## 🛡️ 企业级环境变量安全策略

### 📊 环境文件分类与Git策略

| 文件名 | 安全级别 | Git策略 | 内容类型 | 用途 |
|--------|----------|---------|----------|------|
| **`.env.example`** | 🟢 **安全** | ✅ **提交** | 配置模板 | 团队开发参考 |
| **`.env.development`** | 🔴 **敏感** | ❌ **禁止提交** | 真实密钥 | 本地开发环境 |
| **`.env.production`** | 🔴 **敏感** | ❌ **禁止提交** | 生产密钥 | 生产部署 |
| **`.env.integration.test`** | 🟡 **脱敏** | ⚠️ **可提交** | 测试配置 | 集成测试 |
| **`.env.e2e.test`** | 🟡 **脱敏** | ⚠️ **可提交** | 测试配置 | E2E测试 |

### 🚨 当前安全风险评估

#### ❌ 发现的安全问题
1. **阿里云AccessKey泄露风险**: 
   - 真实的AccessKey在开发环境文件中明文存储
   - 可能被意外提交到版本控制系统

2. **数据库密码暴露**:
   ```bash
   DB_PASSWORD=Xy668899!  # 生产数据库真实密码
   ```

3. **JWT密钥固定**:
   ```bash
   JWT_SECRET=dev-secret-key-change-in-production  # 固定密钥存在风险
   ```

#### ✅ 已实施的安全措施
1. **脱敏处理**: 测试环境配置使用环境变量引用
2. **Git忽略策略**: 敏感文件添加到.gitignore
3. **分层安全**: 不同环境使用不同的安全策略

## 🔐 企业级密钥管理方案

### 方案1: 环境变量注入 (推荐)

#### 开发环境设置
```bash
# ~/.bashrc 或 ~/.zshrc
export DB_PASSWORD="Xy668899!"
export JWT_SECRET="dev-secret-key-$(date +%s)"
export OSS_ACCESS_KEY_ID="LTAI5t6ykGMH7JKFaAGSrCVh"
export OSS_ACCESS_KEY_SECRET="******************************"
export SMS_ACCESS_KEY_ID="LTAI5tFGM4k9LigsPxKWcaSm"
export SMS_ACCESS_KEY_SECRET="******************************"
```

#### CI/CD环境设置
```yaml
# GitHub Actions / Jenkins
env:
  DB_PASSWORD: ${{ secrets.DB_PASSWORD }}
  JWT_SECRET: ${{ secrets.JWT_SECRET }}
  OSS_ACCESS_KEY_ID: ${{ secrets.OSS_ACCESS_KEY_ID }}
  OSS_ACCESS_KEY_SECRET: ${{ secrets.OSS_ACCESS_KEY_SECRET }}
```

### 方案2: 密钥管理服务 (企业级)

#### 阿里云密钥管理服务 (KMS)
```typescript
// src/config/secrets.config.ts
import { KmsClient } from '@alicloud/kms20160120';

export class SecretsManager {
  async getSecret(secretName: string): Promise<string> {
    const kmsClient = new KmsClient({
      accessKeyId: process.env.KMS_ACCESS_KEY_ID,
      accessKeySecret: process.env.KMS_ACCESS_KEY_SECRET,
      endpoint: 'https://kms.cn-hangzhou.aliyuncs.com',
    });
    
    const response = await kmsClient.getSecretValue({
      secretName,
      versionStage: 'AWSCURRENT',
    });
    
    return JSON.parse(response.body.secretData).value;
  }
}
```

### 方案3: Docker Secrets (容器化部署)

```yaml
# docker-compose.production.yml
version: '3.8'
services:
  app:
    image: ygs-backend:latest
    secrets:
      - db_password
      - jwt_secret
      - oss_keys
    environment:
      DB_PASSWORD_FILE: /run/secrets/db_password
      JWT_SECRET_FILE: /run/secrets/jwt_secret

secrets:
  db_password:
    external: true
  jwt_secret:
    external: true
  oss_keys:
    external: true
```

## 🧪 企业级安全测试标准

### 1. 静态安全扫描

#### 密钥泄露检测
```bash
# 安装 git-secrets
brew install git-secrets

# 配置密钥检测规则
git secrets --register-aws
git secrets --install

# 自定义规则检测阿里云密钥
git secrets --add 'LTAI[0-9A-Za-z]{16,32}'
git secrets --add '[0-9a-zA-Z]{30}' # 阿里云AccessKeySecret格式

# 扫描历史提交
git secrets --scan-history
```

#### 代码安全审计
```bash
# 使用 semgrep 进行安全扫描
npm install -g @semgrep/cli
semgrep --config=auto src/

# 检查硬编码密钥
semgrep --config="r/generic.secrets" src/
```

### 2. 动态安全测试

#### API安全测试
```bash
# 安装 OWASP ZAP
docker run -t owasp/zap2docker-stable zap-baseline.py \
  -t http://localhost:3000/api \
  -r zap-report.html
```

#### 依赖漏洞扫描
```bash
# npm audit
npm audit --audit-level moderate

# 使用 snyk 扫描
npm install -g snyk
snyk test
snyk monitor
```

### 3. 安全配置验证

#### 环境变量安全检查
```typescript
// src/config/security-validator.ts
export class SecurityValidator {
  static validateEnvironment(): void {
    const requiredSecrets = [
      'DB_PASSWORD',
      'JWT_SECRET', 
      'OSS_ACCESS_KEY_SECRET',
      'SMS_ACCESS_KEY_SECRET'
    ];

    const missingSecrets = requiredSecrets.filter(
      secret => !process.env[secret] || process.env[secret].includes('请设置环境变量')
    );

    if (missingSecrets.length > 0) {
      throw new Error(`🚨 安全检查失败: 缺少必需的环境变量 ${missingSecrets.join(', ')}`);
    }

    // 检查是否使用默认密钥
    if (process.env.JWT_SECRET?.includes('dev-secret-key')) {
      console.warn('⚠️ 警告: 使用默认JWT密钥，请在生产环境中更换');
    }
  }
}
```

### 4. 生产环境安全检查清单

#### 部署前检查
- [ ] 所有密钥通过环境变量或密钥管理服务提供
- [ ] 没有硬编码的敏感信息
- [ ] JWT密钥使用强随机值
- [ ] 数据库连接使用SSL
- [ ] API接口启用HTTPS
- [ ] 日志中不包含敏感信息

#### 运行时监控
```typescript
// src/middleware/security-monitor.middleware.ts
@Injectable()
export class SecurityMonitorMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    // 监控可疑请求
    this.detectSuspiciousActivity(req);
    
    // 记录安全事件
    this.logSecurityEvent(req);
    
    next();
  }
  
  private detectSuspiciousActivity(req: Request): void {
    // SQL注入检测
    const sqlInjectionPattern = /('|(\\')|(;|%3B)|(--)|(\*|\%2A)/i;
    if (sqlInjectionPattern.test(req.url)) {
      this.logger.warn(`🚨 检测到可疑SQL注入尝试: ${req.ip} -> ${req.url}`);
    }
    
    // XSS检测
    const xssPattern = /<script[\s\S]*?>[\s\S]*?<\/script>/gi;
    if (xssPattern.test(JSON.stringify(req.body))) {
      this.logger.warn(`🚨 检测到可疑XSS尝试: ${req.ip}`);
    }
  }
}
```

## 📋 安全最佳实践总结

### 开发阶段
1. **绝不提交敏感信息**: 使用.gitignore和pre-commit hooks
2. **使用环境变量**: 所有密钥通过环境变量提供
3. **定期轮换密钥**: 建立密钥轮换机制
4. **最小权限原则**: 只给予必需的最小权限

### 测试阶段
1. **数据脱敏**: 测试环境使用脱敏数据
2. **独立环境**: 测试环境与生产环境完全隔离
3. **安全扫描**: 集成自动化安全扫描工具
4. **漏洞修复**: 及时修复发现的安全漏洞

### 生产阶段
1. **密钥管理服务**: 使用专业的密钥管理服务
2. **网络安全**: 启用HTTPS和网络防火墙
3. **监控告警**: 实时监控安全事件
4. **应急响应**: 建立安全事件应急响应机制

## 🚨 应急响应计划

### 密钥泄露处理流程
1. **立即响应** (5分钟内):
   - 禁用泄露的密钥
   - 生成新的密钥
   - 更新所有使用该密钥的系统

2. **影响评估** (1小时内):
   - 评估泄露范围
   - 检查异常访问记录
   - 通知相关人员

3. **系统恢复** (4小时内):
   - 部署新密钥
   - 验证系统功能
   - 加强监控

4. **事后分析** (24小时内):
   - 分析泄露原因
   - 改进安全措施
   - 更新安全策略

---

**维护团队**: YGS 安全团队  
**技术支持**: 企业级安全架构师  
**紧急联系**: <EMAIL>  
**最后更新**: 2025-07-27