import {
  Controller,
  Get,
  Put,
  Post,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  ParseUUIDPipe,
  ValidationPipe,
  ForbiddenException,
} from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse as SwaggerApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from "@nestjs/swagger";
import { UsersService } from "./users.service";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { AuthRequest } from "../../common/types/request.types";
import {
  UpdateUserDto,
  UpdateDisplaySettingsDto,
  UserStatisticsDto,
  UserProfileDto,
} from "./dto/update-user.dto";

/**
 * 用户控制�?
 * 提供用户管理、个人资料、朋友关系等API
 */
@ApiTags("用户管理")
@Controller("users")
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  /**
   * 测试用户管理标签
   */
  @Get("test")
  @ApiOperation({ summary: "测试用户管理标签" })
  @SwaggerApiResponse({ status: 200, description: "测试成功" })
  async testUserTag() {
    return { message: "测试用户管理标签" };
  }

  /**
   * 获取当前用户资料
   */
  @Get("profile")
  @ApiOperation({ summary: "获取当前用户资料" })
  @SwaggerApiResponse({
    status: 200,
    description: "获取成功",
    type: UserProfileDto,
  })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async getCurrentUserProfile(
    @Request() req: AuthRequest,
  ): Promise<UserProfileDto> {
    return this.usersService.getUserProfile(req.user.id, req.user.id);
  }

  /**
   * 获取指定用户资料
   */
  @Get(":id/profile")
  @ApiOperation({ summary: "获取指定用户资料" })
  @ApiParam({ name: "id", description: "用户ID", type: String })
  @SwaggerApiResponse({
    status: 200,
    description: "获取成功",
    type: UserProfileDto,
  })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async getUserProfile(
    @Param("id", ParseUUIDPipe) userId: string,
    @Request() req: AuthRequest,
  ): Promise<UserProfileDto> {
    return this.usersService.getUserProfile(userId, req.user.id);
  }

  /**
   * 更新个人信息
   */
  @Put("profile")
  @ApiOperation({ summary: "更新个人信息" })
  @SwaggerApiResponse({ status: 200, description: "更新成功" })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async updateProfile(
    @Request() req: AuthRequest,
    @Body(ValidationPipe) updateData: UpdateUserDto,
  ) {
    const user = await this.usersService.updateProfile(req.user.id, updateData);
    return {
      success: true,
      message: "个人信息更新成功",
      data: user,
    };
  }

  /**
   * 更新展示设置
   */
  @Put("profile/display-settings")
  @ApiOperation({ summary: "更新展示设置" })
  @SwaggerApiResponse({ status: 200, description: "更新成功" })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async updateDisplaySettings(
    @Request() req: AuthRequest,
    @Body(ValidationPipe) settingsData: UpdateDisplaySettingsDto,
  ) {
    const user = await this.usersService.updateDisplaySettings(
      req.user.id,
      settingsData,
    );
    return {
      success: true,
      message: "展示设置更新成功",
      data: user.profileDisplaySettings,
    };
  }

  /**
   * 获取用户统计数据
   */
  @Get(":id/statistics")
  @ApiOperation({ summary: "获取用户统计数据" })
  @ApiParam({ name: "id", description: "用户ID", type: String })
  @SwaggerApiResponse({
    status: 200,
    description: "获取成功",
    type: UserStatisticsDto,
  })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async getUserStatistics(
    @Param("id", ParseUUIDPipe) userId: string,
    @Request() req: AuthRequest,
  ): Promise<UserStatisticsDto> {
    // 检查是否有权限查看统计数据
    const user = await this.usersService.findById(userId);
    const isOwner = req.user.id === userId;

    if (!isOwner && !user.profileDisplaySettings.showStatistics) {
      throw new ForbiddenException("没有权限查看统计数据");
    }

    return this.usersService.getUserStatistics(userId);
  }

  /**
   * 搜索用户
   */
  @Get("search")
  @ApiOperation({ summary: "搜索用户" })
  @ApiQuery({ name: "q", description: "搜索关键词", type: String })
  @ApiQuery({
    name: "limit",
    description: "限制数量",
    type: String,
    required: false,
  })
  @SwaggerApiResponse({ status: 200, description: "搜索成功" })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async searchUsers(@Query("q") query: string, @Query("limit") limit?: string) {
    if (!query || query.trim().length < 1) {
      return {
        success: false,
        message: "搜索关键词不能为空",
        data: [],
      };
    }

    const users = await this.usersService.searchUsers(
      query.trim(),
      limit ? parseInt(limit) : 20,
    );

    return {
      success: true,
      message: "搜索完成",
      data: users,
    };
  }

  /**
   * 根据有故事号查找用户
   */
  @Get("by-number/:userNumber")
  @ApiOperation({ summary: "根据有故事号查找用户" })
  @ApiParam({ name: "userNumber", description: "有故事号", type: String })
  @SwaggerApiResponse({ status: 200, description: "查找成功" })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async findByUserNumber(@Param("userNumber") userNumber: string) {
    if (!/^\d{6}$/.test(userNumber)) {
      return {
        success: false,
        message: "有故事号格式不正确",
        data: null,
      };
    }

    try {
      const user = await this.usersService.findByUserNumber(userNumber);
      return {
        success: true,
        message: "用户查找成功",
        data: {
          id: user.id,
          userNumber: user.userNumber,
          nickname: user.nickname,
          avatarUrl: user.avatarUrl,
        },
      };
    } catch (error) {
      return {
        success: false,
        message: "用户不存在",
        data: null,
      };
    }
  }

  /**
   * 添加朋友
   */
  @Post(":id/friends")
  @ApiOperation({ summary: "添加朋友" })
  @ApiParam({ name: "id", description: "朋友ID", type: String })
  @SwaggerApiResponse({ status: 200, description: "添加成功" })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async addFriend(
    @Param("id", ParseUUIDPipe) friendId: string,
    @Request() req: AuthRequest,
  ) {
    await this.usersService.addFriend(req.user.id, friendId);
    return {
      success: true,
      message: "好友添加成功",
    };
  }

  /**
   * 移除朋友
   */
  @Delete(":id/friends")
  @ApiOperation({ summary: "移除朋友" })
  @ApiParam({ name: "id", description: "朋友ID", type: String })
  @SwaggerApiResponse({ status: 200, description: "移除成功" })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async removeFriend(
    @Param("id", ParseUUIDPipe) friendId: string,
    @Request() req: AuthRequest,
  ) {
    await this.usersService.removeFriend(req.user.id, friendId);
    return {
      success: true,
      message: "好友移除成功",
    };
  }

  /**
   * 获取朋友列表
   */
  @Get("friends")
  @ApiOperation({ summary: "获取朋友列表" })
  @ApiQuery({
    name: "page",
    description: "页码",
    type: String,
    required: false,
  })
  @ApiQuery({
    name: "limit",
    description: "每页数量",
    type: String,
    required: false,
  })
  @SwaggerApiResponse({ status: 200, description: "获取成功" })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async getFriendsList(
    @Request() req: AuthRequest,
    @Query("page") page?: string,
    @Query("limit") limit?: string,
  ) {
    const result = await this.usersService.getFriendsList(
      req.user.id,
      page ? parseInt(page) : 1,
      limit ? parseInt(limit) : 20,
    );

    return {
      success: true,
      message: "获取朋友列表成功",
      data: {
        friends: result.friends,
        pagination: {
          total: result.total,
          hasNext: result.hasNext,
          page: page ? parseInt(page) : 1,
          limit: limit ? parseInt(limit) : 20,
        },
      },
    };
  }

  /**
   * 检查朋友关系
   */
  @Get(":id/friends/status")
  @ApiOperation({ summary: "检查朋友关系" })
  @ApiParam({ name: "id", description: "目标用户ID", type: String })
  @SwaggerApiResponse({ status: 200, description: "检查成功" })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async checkFriendStatus(
    @Param("id", ParseUUIDPipe) targetId: string,
    @Request() req: AuthRequest,
  ) {
    const isFriend = await this.usersService.isFriend(req.user.id, targetId);
    return {
      success: true,
      data: {
        isFriend,
        canAddFriend: !isFriend && req.user.id !== targetId,
      },
    };
  }

  /**
   * 删除账户（软删除�?
   */
  @Delete("profile")
  @ApiOperation({ summary: "删除账户" })
  @SwaggerApiResponse({ status: 200, description: "删除成功" })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async deleteAccount(@Request() req: AuthRequest) {
    await this.usersService.deleteUser(req.user.id);
    return {
      success: true,
      message: "账户删除成功",
    };
  }

  /**
   * 用户验证
   */
  @Post("verify")
  @ApiOperation({
    summary: "用户验证",
    description: `
      用户身份验证接口，支持多种验证方式
      
      验证类型：
      - phone: 手机号验证
      - email: 邮箱验证
      - identity: 身份认证验证
      - security: 安全验证
      
      功能特点：
      - 支持验证码验证
      - 支持身份证明文件上传
      - 提供验证状态追踪
      - 安全的验证流程
    `,
  })
  @SwaggerApiResponse({
    status: 200,
    description: "验证成功",
    schema: {
      type: "object",
      properties: {
        success: { type: "boolean" },
        message: { type: "string" },
        data: {
          type: "object",
          properties: {
            verificationType: { type: "string" },
            verificationStatus: { type: "string" },
            verifiedAt: { type: "string" },
            nextSteps: { type: "array", items: { type: "string" } },
          },
        },
      },
    },
  })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async verifyUser(
    @Request() req: AuthRequest,
    @Body(ValidationPipe)
    verificationData: {
      verificationType: "phone" | "email" | "identity" | "security";
      verificationCode?: string;
      phone?: string;
      email?: string;
      identityNumber?: string;
      additionalData?: Record<string, unknown>;
    },
  ) {
    try {
      const result = await this.usersService.verifyUser(
        req.user.id,
        verificationData,
      );

      return {
        success: true,
        message: "用户验证成功",
        data: result,
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "验证失败",
        data: null,
      };
    }
  }
}
