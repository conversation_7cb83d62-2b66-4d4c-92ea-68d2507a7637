import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';
import '../../app/config/api_config.dart';
import '../../app/config/env_config.dart';
import '../errors/exceptions.dart';
import 'interceptors/auth_interceptor.dart';
import 'interceptors/error_interceptor.dart';

/// Dio客户端Provider
final dioProvider = Provider<Dio>((ref) {
  return DioClient.instance;
});

/// Dio网络客户端
class DioClient {
  static Dio? _instance;

  DioClient._();

  static Dio get instance {
    _instance ??= _createDio();
    return _instance!;
  }

  static Dio _createDio() {
    final dio = Dio(
      BaseOptions(
        baseUrl: ApiConfig.baseUrl,
        connectTimeout: ApiConfig.connectTimeout,
        receiveTimeout: ApiConfig.receiveTimeout,
        sendTimeout: ApiConfig.sendTimeout,
        headers: ApiConfig.defaultHeaders,
        responseType: ResponseType.json,
        validateStatus: (status) => status != null && status < 500,
      ),
    );

    // 添加拦截器
    dio.interceptors.addAll([
      // 认证拦截器
      AuthInterceptor(),
      // 错误处理拦截器
      ErrorInterceptor(),
      // 日志拦截器（仅开发环境）
      if (EnvConfig.enableLogging)
        PrettyDioLogger(
          requestHeader: true,
          requestBody: true,
          responseBody: true,
          responseHeader: false,
          compact: false,
          maxWidth: 120,
        ),
    ]);

    return dio;
  }

  /// 处理DioException为AppException
  static AppException handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return const NetworkException(
          message: '连接超时，请检查网络',
          code: 'timeout',
        );
      case DioExceptionType.badResponse:
        return _handleBadResponse(error.response);
      case DioExceptionType.cancel:
        return const NetworkException(
          message: '请求已取消',
          code: 'cancelled',
        );
      case DioExceptionType.connectionError:
        return const NetworkException(
          message: '网络连接失败，请检查网络设置',
          code: 'connection_error',
        );
      default:
        return const UnknownException(
          message: '未知网络错误',
          code: 'unknown_network_error',
        );
    }
  }

  static AppException _handleBadResponse(Response? response) {
    if (response == null) {
      return const ServerException(
        message: '服务器响应为空',
        code: 'empty_response',
      );
    }

    final statusCode = response.statusCode ?? 0;
    final data = response.data;
    
    // 尝试解析错误信息
    String message = '服务器错误';
    String? code;
    dynamic errorData;

    if (data is Map<String, dynamic>) {
      message = data['message'] ?? data['error'] ?? message;
      code = data['code']?.toString();
      errorData = data['data'];
    }

    // 根据状态码返回不同的异常
    if (statusCode == 401) {
      return AuthException(
        message: message,
        code: code ?? 'unauthorized',
        data: errorData,
      );
    } else if (statusCode == 403) {
      return PermissionException(
        message: message,
        code: code ?? 'forbidden',
        data: errorData,
      );
    } else if (statusCode == 404) {
      return BusinessException(
        message: '请求的资源不存在',
        code: code ?? 'not_found',
        data: errorData,
      );
    } else if (statusCode == 422) {
      // 解析验证错误
      Map<String, List<String>>? errors;
      if (data is Map<String, dynamic> && data['errors'] is Map) {
        errors = Map<String, List<String>>.from(
          data['errors'].map((key, value) => MapEntry(
            key,
            value is List ? List<String>.from(value) : [value.toString()],
          )),
        );
      }
      return ValidationException(
        message: message,
        code: code ?? 'validation_error',
        errors: errors,
        data: errorData,
      );
    } else if (statusCode >= 500) {
      return ServerException(
        message: '服务器内部错误',
        statusCode: statusCode,
        code: code ?? 'server_error',
        data: errorData,
      );
    } else {
      return BusinessException(
        message: message,
        code: code ?? 'business_error',
        data: errorData,
      );
    }
  }
}