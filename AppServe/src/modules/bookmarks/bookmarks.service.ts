import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ConflictException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import type { SelectQueryBuilder } from "typeorm";
import { Repository } from "typeorm";
import {
  Bookmark,
  BookmarkCategory,
  BookmarkStatus,
} from "./entities/bookmark.entity";
import type {
  CreateBookmarkDto,
  UpdateBookmarkCategoryDto,
  BookmarkQueryDto,
} from "./dto";
import { BookmarkSortBy } from "./dto";
import type {
  BookmarkResponseDto,
  BookmarkCategoryStatsDto,
  BookmarkStatsResponseDto,
} from "./dto";
import { Story } from "../stories/entities/story.entity";
import { User } from "../users/entities/user.entity";
import type { PaginatedResponseDto } from "../../common/dto/paginated-response.dto";

/**
 * 收藏服务
 * 提供故事收藏的完整功能
 */
@Injectable()
export class BookmarksService {
  constructor(
    @InjectRepository(Bookmark)
    private readonly bookmarkRepository: Repository<Bookmark>,
    @InjectRepository(Story)
    private readonly storyRepository: Repository<Story>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  /**
   * 创建收藏
   */
  async createBookmark(
    createBookmarkDto: CreateBookmarkDto,
    userId: string,
  ): Promise<BookmarkResponseDto> {
    const {
      storyId,
      category = BookmarkCategory.DEFAULT,
      customCategoryName,
      note,
      metadata,
    } = createBookmarkDto;

    // 验证故事是否存在且可访问
    const story = await this.storyRepository.findOne({
      where: { id: storyId },
      relations: ["author"],
    });

    if (!story) {
      throw new NotFoundException("故事不存在");
    }

    // 检查是否已经收藏
    const existingBookmark = await this.bookmarkRepository.findOne({
      where: { userId, storyId },
    });

    if (existingBookmark) {
      if (existingBookmark.status === BookmarkStatus.ACTIVE) {
        throw new ConflictException("已经收藏过该故事");
      } else {
        // 如果之前收藏已删除或失效，重新激活
        existingBookmark.status = BookmarkStatus.ACTIVE;
        existingBookmark.category = category;
        if (customCategoryName !== undefined) {
          existingBookmark.customCategoryName = customCategoryName;
        }
        if (note !== undefined) {
          existingBookmark.note = note;
        }
        if (metadata !== undefined) {
          existingBookmark.metadata = metadata;
        }
        existingBookmark.updatedAt = new Date();

        const updatedBookmark =
          await this.bookmarkRepository.save(existingBookmark);
        return this.buildBookmarkResponse(updatedBookmark, story);
      }
    }

    // 验证自定义分类
    if (category === BookmarkCategory.CUSTOM && !customCategoryName) {
      throw new BadRequestException("使用自定义分类时必须提供分类名称");
    }

    // 创建故事快照
    const storySnapshot = {
      title: story.title,
      summary: story.content,
      coverImage: story.coverImageUrl,
      authorName: story.user?.nickname || "未知作者",
    };

    // 创建收藏
    const bookmark = this.bookmarkRepository.create({
      userId,
      storyId,
      category,
      customCategoryName,
      note,
      storySnapshot,
      metadata,
    });

    const savedBookmark = await this.bookmarkRepository.save(bookmark);
    return this.buildBookmarkResponse(savedBookmark, story);
  }

  /**
   * 获取用户收藏列表
   */
  async getUserBookmarks(
    userId: string,
    query: BookmarkQueryDto,
  ): Promise<PaginatedResponseDto<BookmarkResponseDto>> {
    const {
      page = 1,
      limit = 20,
      sortBy = BookmarkSortBy.CREATED_DESC,
    } = query;
    const skip = (page - 1) * limit;

    const queryBuilder = this.buildBookmarkQuery(userId, query);

    // 应用排序
    this.applySorting(queryBuilder, sortBy);

    // 获取总数
    const total = await queryBuilder.getCount();

    // 获取数据
    const bookmarks = await queryBuilder.skip(skip).take(limit).getMany();

    // 构建响应数据
    const items = bookmarks.map((bookmark) =>
      this.buildBookmarkResponse(bookmark),
    );

    const totalPages = Math.ceil(total / limit);
    return {
      data: items,
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  /**
   * 获取收藏详情
   */
  async getBookmarkDetail(
    bookmarkId: string,
    userId: string,
  ): Promise<BookmarkResponseDto> {
    const bookmark = await this.bookmarkRepository.findOne({
      where: { id: bookmarkId, userId },
      relations: ["story", "story.user"],
    });

    if (!bookmark) {
      throw new NotFoundException("收藏记录不存在");
    }

    return this.buildBookmarkResponse(bookmark);
  }

  /**
   * 更新收藏分类
   */
  async updateBookmarkCategory(
    bookmarkId: string,
    updateDto: UpdateBookmarkCategoryDto,
    userId: string,
  ): Promise<BookmarkResponseDto> {
    const { category, customCategoryName, note } = updateDto;

    const bookmark = await this.bookmarkRepository.findOne({
      where: { id: bookmarkId, userId },
      relations: ["story", "story.user"],
    });

    if (!bookmark) {
      throw new NotFoundException("收藏记录不存在");
    }

    // 验证自定义分类
    if (category === BookmarkCategory.CUSTOM && !customCategoryName) {
      throw new BadRequestException("使用自定义分类时必须提供分类名称");
    }

    // 更新收藏
    bookmark.category = category;
    if (customCategoryName !== undefined) {
      bookmark.customCategoryName = customCategoryName;
    }
    if (note !== undefined) {
      bookmark.note = note;
    }

    const updatedBookmark = await this.bookmarkRepository.save(bookmark);
    return this.buildBookmarkResponse(updatedBookmark);
  }

  /**
   * 删除收藏
   */
  async deleteBookmark(bookmarkId: string, userId: string): Promise<void> {
    const bookmark = await this.bookmarkRepository.findOne({
      where: { id: bookmarkId, userId },
    });

    if (!bookmark) {
      throw new NotFoundException("收藏记录不存在");
    }

    await this.bookmarkRepository.remove(bookmark);
  }

  /**
   * 获取收藏统计
   */
  async getBookmarkStats(userId: string): Promise<BookmarkStatsResponseDto> {
    // 获取基础统计
    const baseStats = await this.bookmarkRepository
      .createQueryBuilder("bookmark")
      .select([
        "COUNT(*) as totalBookmarks",
        "COUNT(CASE WHEN bookmark.status = :activeStatus THEN 1 END) as activeBookmarks",
        "COUNT(CASE WHEN bookmark.status = :archivedStatus THEN 1 END) as archivedBookmarks",
        "COUNT(CASE WHEN bookmark.status = :invalidStatus THEN 1 END) as invalidBookmarks",
      ])
      .where("bookmark.userId = :userId", { userId })
      .setParameter("activeStatus", BookmarkStatus.ACTIVE)
      .setParameter("archivedStatus", BookmarkStatus.ARCHIVED)
      .setParameter("invalidStatus", BookmarkStatus.INVALID)
      .getRawOne();

    // 获取分类统计
    const categoryStats = await this.bookmarkRepository
      .createQueryBuilder("bookmark")
      .select([
        "bookmark.category as category",
        "bookmark.customCategoryName as customCategoryName",
        "COUNT(*) as count",
      ])
      .where("bookmark.userId = :userId", { userId })
      .andWhere("bookmark.status = :status", { status: BookmarkStatus.ACTIVE })
      .groupBy("bookmark.category, bookmark.customCategoryName")
      .getRawMany();

    const categoryStatsDto: BookmarkCategoryStatsDto[] = categoryStats.map(
      (stat) => ({
        category: stat.category,
        customCategoryName: stat.customCategoryName,
        count: parseInt(stat.count),
      }),
    );

    return {
      totalBookmarks: parseInt(baseStats.totalBookmarks) || 0,
      activeBookmarks: parseInt(baseStats.activeBookmarks) || 0,
      archivedBookmarks: parseInt(baseStats.archivedBookmarks) || 0,
      invalidBookmarks: parseInt(baseStats.invalidBookmarks) || 0,
      categoryStats: categoryStatsDto,
    };
  }

  /**
   * 检查用户是否收藏了指定故事
   */
  async isStoryBookmarked(storyId: string, userId: string): Promise<boolean> {
    const bookmark = await this.bookmarkRepository.findOne({
      where: { storyId, userId, status: BookmarkStatus.ACTIVE },
    });

    return !!bookmark;
  }

  /**
   * 批量更新收藏状态（当故事被删除时调用）
   */
  async updateBookmarksOnStoryDelete(storyId: string): Promise<void> {
    await this.bookmarkRepository.update(
      { storyId, status: BookmarkStatus.ACTIVE },
      { status: BookmarkStatus.INVALID },
    );
  }

  /**
   * 构建收藏查询
   */
  private buildBookmarkQuery(
    userId: string,
    query: BookmarkQueryDto,
  ): SelectQueryBuilder<Bookmark> {
    const queryBuilder = this.bookmarkRepository
      .createQueryBuilder("bookmark")
      .leftJoinAndSelect("bookmark.story", "story")
      .leftJoinAndSelect("story.user", "author")
      .where("bookmark.userId = :userId", { userId });

    if (query.category) {
      queryBuilder.andWhere("bookmark.category = :category", {
        category: query.category,
      });
    }

    if (query.customCategoryName) {
      queryBuilder.andWhere(
        "bookmark.customCategoryName = :customCategoryName",
        {
          customCategoryName: query.customCategoryName,
        },
      );
    }

    if (query.status) {
      queryBuilder.andWhere("bookmark.status = :status", {
        status: query.status,
      });
    } else {
      queryBuilder.andWhere("bookmark.status = :status", {
        status: BookmarkStatus.ACTIVE,
      });
    }

    if (query.keyword) {
      queryBuilder.andWhere(
        "(bookmark.storySnapshot->>'title' ILIKE :keyword OR bookmark.note ILIKE :keyword)",
        { keyword: `%${query.keyword}%` },
      );
    }

    return queryBuilder;
  }

  /**
   * 应用排序
   */
  private applySorting(
    queryBuilder: SelectQueryBuilder<Bookmark>,
    sortBy: BookmarkSortBy,
  ): void {
    switch (sortBy) {
      case BookmarkSortBy.CREATED_DESC:
        queryBuilder.orderBy("bookmark.createdAt", "DESC");
        break;
      case BookmarkSortBy.CREATED_ASC:
        queryBuilder.orderBy("bookmark.createdAt", "ASC");
        break;
      case BookmarkSortBy.UPDATED_DESC:
        queryBuilder.orderBy("bookmark.updatedAt", "DESC");
        break;
      case BookmarkSortBy.STORY_TITLE_ASC:
        queryBuilder.orderBy("bookmark.storySnapshot->>'title'", "ASC");
        break;
      default:
        queryBuilder.orderBy("bookmark.createdAt", "DESC");
    }
  }

  /**
   * 构建收藏响应数据
   */
  private buildBookmarkResponse(
    bookmark: Bookmark,
    story?: Story,
  ): BookmarkResponseDto {
    const response: BookmarkResponseDto = {
      id: bookmark.id,
      userId: bookmark.userId,
      storyId: bookmark.storyId,
      category: bookmark.category,
      customCategoryName: bookmark.customCategoryName,
      status: bookmark.status,
      note: bookmark.note,
      storySnapshot: bookmark.storySnapshot,
      metadata: bookmark.metadata,
      createdAt: bookmark.createdAt,
      updatedAt: bookmark.updatedAt,
    };

    // 如果有关联的故事信息，且故事仍然存在
    if (bookmark.story || story) {
      const storyData = story || bookmark.story;
      response.story = {
        id: storyData.id,
        title: storyData.title,
        summary: storyData.content?.substring(0, 200),
        coverImage: storyData.coverImageUrl,
        authorId: storyData.userId,
        authorName:
          storyData.user?.nickname || bookmark.storySnapshot.authorName,
        status: storyData.status.toString(),
        isPublic: storyData.permissionLevel === "public",
      };
    }

    return response;
  }
}
