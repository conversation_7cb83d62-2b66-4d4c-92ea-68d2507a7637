import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import type { ExecutionContext } from "@nestjs/common";
import { ForbiddenException, Logger } from "@nestjs/common";
import { Reflector } from "@nestjs/core";
import { SecurityGuard, Security } from "./security.guard";
import { EnhancedRedisService } from "../services/enhanced-redis.service";
import type { Request } from "express";

// Mock Redis客户端类型
interface MockRedisClient {
  sismember: jest.Mock;
  pipeline: jest.Mock;
  get: jest.Mock;
}

// Mock Pipeline类型
interface MockPipeline {
  incr: jest.Mock;
  expire: jest.Mock;
  exec: jest.Mock;
  sadd: jest.Mock;
  set: jest.Mock;
  get: jest.Mock;
}

describe("SecurityGuard", () => {
  let guard: SecurityGuard;
  let mockReflector: jest.Mocked<Reflector>;
  let mockEnhancedRedisService: jest.Mocked<EnhancedRedisService>;
  let mockRedisClient: MockRedisClient;

  // Mock数据
  const mockRequest: Partial<Request> = {
    url: "/api/test",
    method: "GET",
    headers: {
      "user-agent":
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    },
    query: {},
    body: {},
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    connection: { remoteAddress: "127.0.0.1" } as any,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    socket: { remoteAddress: "127.0.0.1" } as any,
  };

  // Mock HTTP上下文
  const mockHttpContext = {
    getRequest: jest.fn().mockReturnValue(mockRequest),
    getResponse: jest.fn(),
    getNext: jest.fn(),
  };

  const mockExecutionContext = {
    switchToHttp: jest.fn().mockReturnValue(mockHttpContext),
    getHandler: jest.fn(),
    getClass: jest.fn(),
    getArgs: jest.fn(),
    getArgByIndex: jest.fn(),
    switchToRpc: jest.fn(),
    switchToWs: jest.fn(),
    getType: jest.fn(),
  } as unknown as ExecutionContext;

  beforeEach(async () => {
    // Mock Redis客户端
    mockRedisClient = {
      sismember: jest.fn(),
      pipeline: jest.fn(() => ({
        sadd: jest.fn(),
        set: jest.fn(),
        incr: jest.fn(),
        expire: jest.fn(),
        exec: jest.fn(),
      })),
      get: jest.fn(),
    };

    const mockEnhancedRedisServiceMethods = {
      getClient: jest.fn().mockReturnValue(mockRedisClient),
    };

    const mockReflectorMethods = {
      get: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SecurityGuard,
        {
          provide: Reflector,
          useValue: mockReflectorMethods,
        },
        {
          provide: EnhancedRedisService,
          useValue: mockEnhancedRedisServiceMethods,
        },
      ],
    }).compile();

    guard = module.get<SecurityGuard>(SecurityGuard);
    mockReflector = module.get(Reflector) as jest.Mocked<Reflector>;
    mockEnhancedRedisService = module.get(
      EnhancedRedisService,
    ) as jest.Mocked<EnhancedRedisService>;

    // 重置Mock状态
    mockHttpContext.getRequest.mockReturnValue(mockRequest);
    (mockExecutionContext.switchToHttp as jest.Mock).mockReturnValue(
      mockHttpContext,
    );

    // 设置日志级别为静默
    jest.spyOn(Logger.prototype, "debug").mockImplementation(() => {});
    jest.spyOn(Logger.prototype, "warn").mockImplementation(() => {});
    jest.spyOn(Logger.prototype, "error").mockImplementation(() => {});
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("canActivate", () => {
    it("应该在没有安全配置时直接通过", async () => {
      mockReflector.get.mockReturnValue(undefined);

      const result = await guard.canActivate(mockExecutionContext);

      expect(result).toBe(true);
      expect(mockReflector.get).toHaveBeenCalledWith(
        Security,
        mockExecutionContext.getHandler(),
      );
    });

    it("应该在低风险请求时通过安全检查", async () => {
      const securityConfig = {
        level: "MEDIUM" as const,
        checkSQLInjection: true,
        checkXSS: true,
        rateLimitPerMinute: 60,
        rateLimitPerHour: 1000,
      };

      mockReflector.get.mockReturnValue(securityConfig);
      mockRedisClient.sismember.mockResolvedValue(0); // 不在黑名单

      // Mock速率限制检查
      const mockPipeline: MockPipeline = {
        incr: jest.fn(),
        expire: jest.fn(),
        exec: jest.fn().mockResolvedValue([
          [null, 10], // 分钟计数
          [null, "OK"], // 分钟过期
          [null, 100], // 小时计数
          [null, "OK"], // 小时过期
        ]),
        sadd: jest.fn(),
        set: jest.fn(),
        get: jest.fn(),
      };
      mockRedisClient.pipeline.mockReturnValue(mockPipeline);

      // Mock IP信息和访问统计
      const mockGetInfoPipeline: MockPipeline = {
        get: jest.fn(),
        exec: jest.fn().mockResolvedValue([
          [null, "50"], // requests
          [null, "1000"], // last_request
          [null, "5"], // risk_score
          [null, null], // blocked
        ]),
        incr: jest.fn(),
        expire: jest.fn(),
        sadd: jest.fn(),
        set: jest.fn(),
      };

      const mockUpdatePipeline: MockPipeline = {
        incr: jest.fn(),
        expire: jest.fn(),
        set: jest.fn(),
        exec: jest.fn().mockResolvedValue([]),
        sadd: jest.fn(),
        get: jest.fn(),
      };

      mockRedisClient.pipeline
        .mockReturnValueOnce(mockPipeline)
        .mockReturnValueOnce(mockGetInfoPipeline)
        .mockReturnValueOnce(mockUpdatePipeline);

      const result = await guard.canActivate(mockExecutionContext);

      expect(result).toBe(true);
      expect(mockRedisClient.sismember).toHaveBeenCalledWith(
        "security:blacklist",
        "127.0.0.1",
      );
    });

    it("应该在IP被黑名单时拒绝访问", async () => {
      const securityConfig = {
        level: "MEDIUM" as const,
        checkSQLInjection: true,
        checkXSS: true,
      };

      mockReflector.get.mockReturnValue(securityConfig);
      mockRedisClient.sismember.mockResolvedValue(1); // 在黑名单中

      await expect(guard.canActivate(mockExecutionContext)).rejects.toThrow(
        new ForbiddenException("访问被拒绝"),
      );
    });

    it("应该在需要白名单但IP不在白名单时拒绝访问", async () => {
      const securityConfig = {
        level: "MEDIUM" as const,
        requireWhitelist: true,
      };

      mockReflector.get.mockReturnValue(securityConfig);
      mockRedisClient.sismember.mockResolvedValueOnce(0); // 不在黑名单
      mockRedisClient.sismember.mockResolvedValueOnce(0); // 不在白名单

      await expect(guard.canActivate(mockExecutionContext)).rejects.toThrow(
        new ForbiddenException("访问被拒绝"),
      );
    });

    it("应该检测恶意User-Agent", async () => {
      const securityConfig = {
        level: "MEDIUM" as const,
      };

      // 设置恶意User-Agent
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (mockRequest.headers as any)["user-agent"] = "sqlmap/1.0";

      mockReflector.get.mockReturnValue(securityConfig);
      mockRedisClient.sismember.mockResolvedValue(0); // 不在黑名单

      const mockBlockPipeline: MockPipeline = {
        sadd: jest.fn(),
        set: jest.fn(),
        exec: jest.fn().mockResolvedValue([]),
        incr: jest.fn(),
        expire: jest.fn(),
        get: jest.fn(),
      };
      mockRedisClient.pipeline.mockReturnValue(mockBlockPipeline);

      await expect(guard.canActivate(mockExecutionContext)).rejects.toThrow(
        new ForbiddenException("访问被拒绝"),
      );
    });

    it("应该检测SQL注入攻击", async () => {
      const securityConfig = {
        level: "MEDIUM" as const,
        checkSQLInjection: true,
      };

      // 重置为正常User-Agent避免恶意检测
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (mockRequest.headers as any)["user-agent"] =
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36";

      // 设置包含SQL注入的URL
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (mockRequest as any).url = "/api/test?id=1' union select * from users";

      mockReflector.get.mockReturnValue(securityConfig);
      mockRedisClient.sismember.mockResolvedValue(0); // 不在黑名单

      // Mock速率限制检查
      const mockRateLimitPipeline: MockPipeline = {
        incr: jest.fn(),
        expire: jest.fn(),
        exec: jest.fn().mockResolvedValue([
          [null, 10], // 分钟计数
          [null, "OK"], // 分钟过期
          [null, 100], // 小时计数
          [null, "OK"], // 小时过期
        ]),
        sadd: jest.fn(),
        set: jest.fn(),
        get: jest.fn(),
      };

      const mockBlockPipeline: MockPipeline = {
        sadd: jest.fn(),
        set: jest.fn(),
        exec: jest.fn().mockResolvedValue([]),
        incr: jest.fn(),
        expire: jest.fn(),
        get: jest.fn(),
      };

      // Mock风险分析Pipeline
      const mockRiskAnalysisPipeline: MockPipeline = {
        get: jest.fn(),
        exec: jest.fn().mockResolvedValue([
          [null, "50"], // requests
          [null, "1000"], // last_request
          [null, "5"], // risk_score
          [null, null], // blocked
        ]),
        incr: jest.fn(),
        expire: jest.fn(),
        sadd: jest.fn(),
        set: jest.fn(),
      };

      // Mock更新统计Pipeline
      const mockUpdateStatsPipeline: MockPipeline = {
        incr: jest.fn(),
        expire: jest.fn(),
        set: jest.fn(),
        exec: jest.fn().mockResolvedValue([]),
        sadd: jest.fn(),
        get: jest.fn(),
      };

      mockRedisClient.pipeline
        .mockReturnValueOnce(mockRateLimitPipeline)
        .mockReturnValueOnce(mockBlockPipeline)
        .mockReturnValueOnce(mockRiskAnalysisPipeline)
        .mockReturnValueOnce(mockUpdateStatsPipeline);

      await expect(guard.canActivate(mockExecutionContext)).rejects.toThrow(
        new ForbiddenException("请求被拒绝"),
      );
    });

    it("应该检测XSS攻击", async () => {
      const securityConfig = {
        level: "MEDIUM" as const,
        checkXSS: true,
      };

      // 重置为正常User-Agent避免恶意检测
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (mockRequest.headers as any)["user-agent"] =
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36";

      // 重置URL为正常值
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (mockRequest as any).url = "/api/test";

      // 设置包含XSS的请求体
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (mockRequest as any).body = { content: "<script>alert('xss')</script>" };

      mockReflector.get.mockReturnValue(securityConfig);
      mockRedisClient.sismember.mockResolvedValue(0); // 不在黑名单

      // Mock速率限制检查
      const mockRateLimitPipeline: MockPipeline = {
        incr: jest.fn(),
        expire: jest.fn(),
        exec: jest.fn().mockResolvedValue([
          [null, 10], // 分钟计数
          [null, "OK"], // 分钟过期
          [null, 100], // 小时计数
          [null, "OK"], // 小时过期
        ]),
        sadd: jest.fn(),
        set: jest.fn(),
        get: jest.fn(),
      };

      const mockBlockPipeline: MockPipeline = {
        sadd: jest.fn(),
        set: jest.fn(),
        exec: jest.fn().mockResolvedValue([]),
        incr: jest.fn(),
        expire: jest.fn(),
        get: jest.fn(),
      };

      // Mock风险分析Pipeline
      const mockRiskAnalysisPipeline: MockPipeline = {
        get: jest.fn(),
        exec: jest.fn().mockResolvedValue([
          [null, "50"], // requests
          [null, "1000"], // last_request
          [null, "5"], // risk_score
          [null, null], // blocked
        ]),
        incr: jest.fn(),
        expire: jest.fn(),
        sadd: jest.fn(),
        set: jest.fn(),
      };

      // Mock更新统计Pipeline
      const mockUpdateStatsPipeline: MockPipeline = {
        incr: jest.fn(),
        expire: jest.fn(),
        set: jest.fn(),
        exec: jest.fn().mockResolvedValue([]),
        sadd: jest.fn(),
        get: jest.fn(),
      };

      mockRedisClient.pipeline
        .mockReturnValueOnce(mockRateLimitPipeline)
        .mockReturnValueOnce(mockBlockPipeline)
        .mockReturnValueOnce(mockRiskAnalysisPipeline)
        .mockReturnValueOnce(mockUpdateStatsPipeline);

      await expect(guard.canActivate(mockExecutionContext)).rejects.toThrow(
        new ForbiddenException("请求被拒绝"),
      );
    });

    it("应该在速率限制触发时拒绝访问", async () => {
      const securityConfig = {
        level: "MEDIUM" as const,
        rateLimitPerMinute: 10,
      };

      // 重置为正常User-Agent避免恶意检测
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (mockRequest.headers as any)["user-agent"] =
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36";

      // 重置URL和body为正常值
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (mockRequest as any).url = "/api/test";
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (mockRequest as any).body = {};

      mockReflector.get.mockReturnValue(securityConfig);
      mockRedisClient.sismember.mockResolvedValue(0); // 不在黑名单

      // Mock速率限制检查 - 超过限制
      const mockRateLimitPipeline: MockPipeline = {
        incr: jest.fn(),
        expire: jest.fn(),
        exec: jest.fn().mockResolvedValue([
          [null, 15], // 分钟计数 - 超过限制
          [null, "OK"], // 分钟过期
          [null, 100], // 小时计数
          [null, "OK"], // 小时过期
        ]),
        sadd: jest.fn(),
        set: jest.fn(),
        get: jest.fn(),
      };
      mockRedisClient.pipeline.mockReturnValue(mockRateLimitPipeline);

      await expect(guard.canActivate(mockExecutionContext)).rejects.toThrow(
        new ForbiddenException("请求过于频繁，请稍后再试"),
      );
    });

    it("应该在处理错误时允许通过", async () => {
      const securityConfig = {
        level: "MEDIUM" as const,
      };

      mockReflector.get.mockReturnValue(securityConfig);
      mockRedisClient.sismember.mockRejectedValue(new Error("Redis错误"));

      const result = await guard.canActivate(mockExecutionContext);

      expect(result).toBe(true);
    });
  });

  describe("getClientIP", () => {
    it("应该从x-forwarded-for获取IP", async () => {
      const testRequest = {
        headers: { "x-forwarded-for": "***********, ********" },
        connection: { remoteAddress: "127.0.0.1" },
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
      } as any;

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const ip = (guard as any).getClientIP(testRequest);
      expect(ip).toBe("***********");
    });

    it("应该从x-real-ip获取IP", async () => {
      const testRequest = {
        headers: { "x-real-ip": "***********" },
        connection: { remoteAddress: "127.0.0.1" },
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
      } as any;

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const ip = (guard as any).getClientIP(testRequest);
      expect(ip).toBe("***********");
    });

    it("应该从connection.remoteAddress获取IP", async () => {
      const testRequest = {
        headers: {},
        connection: { remoteAddress: "***********" },
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
      } as any;

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const ip = (guard as any).getClientIP(testRequest);
      expect(ip).toBe("***********");
    });

    it("应该使用默认IP", async () => {
      const testRequest = {
        headers: {},
        connection: {},
        socket: {},
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
      } as any;

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const ip = (guard as any).getClientIP(testRequest);
      expect(ip).toBe("127.0.0.1");
    });
  });

  describe("isMaliciousUserAgent", () => {
    it("应该检测恶意User-Agent", async () => {
      const maliciousUserAgents = [
        "sqlmap/1.0",
        "nmap-scanner",
        "nikto/2.1",
        "dirbuster",
        "masscan",
        "ZAP",
        "Burp",
        "wget",
        "curl-bot",
      ];

      for (const userAgent of maliciousUserAgents) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const result = (guard as any).isMaliciousUserAgent(userAgent);
        expect(result).toBe(true);
      }
    });

    it("应该允许正常User-Agent", async () => {
      const normalUserAgents = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36",
      ];

      for (const userAgent of normalUserAgents) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const result = (guard as any).isMaliciousUserAgent(userAgent);
        expect(result).toBe(false);
      }
    });
  });

  describe("detectSQLInjection", () => {
    it("应该检测URL中的SQL注入", async () => {
      const attackRequests = [
        {
          url: "/api/test?id=1' union select * from users",
          query: {},
          body: {},
        },
        { url: "/api/test?name=admin' and 1=1", query: {}, body: {} },
        { url: "/api/test?id=1; drop table users", query: {}, body: {} },
      ];

      for (const request of attackRequests) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const result = (guard as any).detectSQLInjection(request);
        expect(result).toBe(true);
      }
    });

    it("应该检测查询参数中的SQL注入", async () => {
      const attackRequest = {
        url: "/api/test",
        query: { id: "1' union select * from users" },
        body: {},
      };

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = (guard as any).detectSQLInjection(attackRequest);
      expect(result).toBe(true);
    });

    it("应该检测请求体中的SQL注入", async () => {
      const attackRequest = {
        url: "/api/test",
        query: {},
        body: { name: "admin' union select * from users" },
      };

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = (guard as any).detectSQLInjection(attackRequest);
      expect(result).toBe(true);
    });

    it("应该允许正常请求", async () => {
      const normalRequest = {
        url: "/api/test",
        query: { id: "123", name: "test" },
        body: { content: "这是正常的内容" },
      };

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = (guard as any).detectSQLInjection(normalRequest);
      expect(result).toBe(false);
    });
  });

  describe("detectXSS", () => {
    it("应该检测URL中的XSS攻击", async () => {
      const attackRequests = [
        { url: "/api/test?content=<script>alert('xss')</script>" },
        { url: "/api/test?html=<iframe src='evil.com'></iframe>" },
        { url: "/api/test?js=javascript:alert('xss')" },
      ];

      for (const request of attackRequests) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const result = (guard as any).detectXSS(request);
        expect(result).toBe(true);
      }
    });

    it("应该检测查询参数中的XSS攻击", async () => {
      const attackRequest = {
        url: "/api/test",
        query: { content: "<script>alert('xss')</script>" },
      };

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = (guard as any).detectXSS(attackRequest);
      expect(result).toBe(true);
    });

    it("应该检测请求体中的XSS攻击", async () => {
      const attackRequest = {
        url: "/api/test",
        query: {},
        body: { html: "<script>alert('xss')</script>" },
      };

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = (guard as any).detectXSS(attackRequest);
      expect(result).toBe(true);
    });

    it("应该允许正常请求", async () => {
      const normalRequest = {
        url: "/api/test",
        query: { title: "测试标题" },
        body: { content: "这是正常的内容" },
      };

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = (guard as any).detectXSS(normalRequest);
      expect(result).toBe(false);
    });
  });

  it("应该正确注入依赖", () => {
    expect(guard).toBeDefined();
    expect(mockReflector).toBeDefined();
    expect(mockEnhancedRedisService).toBeDefined();
  });
});
