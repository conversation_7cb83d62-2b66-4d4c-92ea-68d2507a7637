import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { JwtService } from "@nestjs/jwt";
import { ConfigService } from "@nestjs/config";
import { getRepositoryToken } from "@nestjs/typeorm";
import {
  BadRequestException,
  UnauthorizedException,
  ConflictException,
  NotFoundException,
} from "@nestjs/common";
import type { Repository } from "typeorm";
import { AuthService } from "./auth.service";
import { User } from "../users/entities/user.entity";
import { RefreshToken } from "../users/entities/refresh-token.entity";
import { SmsService } from "./services/sms.service";
import { OptimizedSessionManagerService } from "./services/session-manager-optimized.service";

// Mock bcryptjs在模块级别
jest.mock("bcryptjs", () => ({
  hash: jest.fn().mockResolvedValue("hashed_password"),
  compare: jest.fn().mockResolvedValue(true),
}));

import { compare as mockCompare } from "bcryptjs";

describe("AuthService", () => {
  let service: AuthService;
  let userRepository: Repository<User>;
  let jwtService: JwtService;
  let smsService: SmsService;
  let module: TestingModule; // 🔥 添加module变量用于调试

  // Mock用户实体
  const mockUser = {
    id: "test-user-id",
    phone: "***********",
    email: "<EMAIL>",
    username: "testuser",
    passwordHash: "hashed_password",
    isActive: true,
    failedLoginAttempts: 0,
    lastLoginAt: new Date(),
    lastLoginIp: null,
    lockedUntil: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    isLocked: jest.fn().mockReturnValue(false),
    updateLoginAttempts: jest.fn(),
    shouldLockAccount: jest.fn().mockReturnValue(false),
  };

  // 定义mock repositories和services
  const mockUserRepository = {
    findOne: jest.fn(),
    save: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    findOneBy: jest.fn(),
  };

  const mockJwtService = {
    signAsync: jest.fn(),
    sign: jest.fn(),
    verify: jest.fn(),
  };

  const mockSmsService = {
    sendVerificationCode: jest.fn(),
    verifyCode: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn((key: string, defaultValue?: unknown) => {
      const config: Record<string, unknown> = {
        JWT_SECRET: "test-secret",
        JWT_EXPIRES_IN: "2700",
        JWT_REFRESH_EXPIRES_IN: "1209600",
        JWT_REFRESH_SECRET: "test-refresh-secret",
        BCRYPT_ROUNDS: "12",
        SALT_ROUNDS: 12,
      };
      // 🔥 如果key不存在，返回defaultValue
      return config[key] !== undefined ? config[key] : defaultValue;
    }),
  };

  const mockRefreshTokenRepository = {
    findOne: jest.fn(),
    save: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
  };

  const mockOptimizedSessionManagerService = {
    validateConcurrentSessions: jest.fn(),
    createSession: jest.fn(),
    validateSession: jest.fn(),
    invalidateSession: jest.fn(),
    invalidateAllUserSessions: jest.fn(),
    getUserSessionStats: jest.fn(),
    getSystemSessionStats: jest.fn(),
    manualCleanupExpiredSessions: jest.fn(),
  };

  beforeEach(async () => {
    module = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepository,
        },
        {
          provide: getRepositoryToken(RefreshToken),
          useValue: mockRefreshTokenRepository,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: SmsService,
          useValue: mockSmsService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: OptimizedSessionManagerService,
          useValue: mockOptimizedSessionManagerService,
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    userRepository = module.get<Repository<User>>(getRepositoryToken(User));
    jwtService = module.get<JwtService>(JwtService);
    smsService = module.get<SmsService>(SmsService);

    // 配置默认Mock返回值
    mockJwtService.signAsync.mockResolvedValue("mock-jwt-token");
    mockOptimizedSessionManagerService.validateConcurrentSessions.mockResolvedValue(
      undefined,
    );
    mockOptimizedSessionManagerService.createSession.mockResolvedValue(
      undefined,
    );
    mockOptimizedSessionManagerService.validateSession.mockResolvedValue({
      id: "mock-refresh-token-id",
      user: mockUser,
      deviceInfo: { ip: "***********" },
    });
    mockOptimizedSessionManagerService.invalidateSession.mockResolvedValue(
      undefined,
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("sendPhoneVerificationCode", () => {
    it("should send verification code successfully", async () => {
      // 设置开发环境
      const originalNodeEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = "development";

      const phone = "***********";
      const mockResponse = {
        success: true,
        message: "验证码发送成功",
        requestId: "test-request-id",
      };

      (smsService.sendVerificationCode as jest.Mock).mockResolvedValue(
        mockResponse,
      );

      const result = await service.sendPhoneVerificationCode(phone);

      expect(result).toEqual({
        success: true,
        message: "验证码发送成功",
        hint: "开发环境：请查看控制台日志获取验证码",
      });
      expect(smsService.sendVerificationCode).toHaveBeenCalledWith(
        phone,
        undefined,
      );

      // 恢复环境变量
      process.env.NODE_ENV = originalNodeEnv;
    });

    it("should throw BadRequestException for invalid phone format", async () => {
      const invalidPhone = "123";

      await expect(
        service.sendPhoneVerificationCode(invalidPhone),
      ).rejects.toThrow(BadRequestException);
      expect(smsService.sendVerificationCode).not.toHaveBeenCalled();
    });
  });

  describe("loginWithPhone", () => {
    it("should login existing user successfully", async () => {
      const phone = "***********";
      const code = "123456";

      (smsService.verifyCode as jest.Mock).mockResolvedValue(true);
      (userRepository.findOne as jest.Mock).mockResolvedValue(mockUser);
      (jwtService.signAsync as jest.Mock)
        .mockResolvedValueOnce("access_token")
        .mockResolvedValueOnce("refresh_token");

      const result = await service.loginWithPhone(phone, code);

      expect(result).toEqual({
        user: mockUser,
        accessToken: "access_token",
        refreshToken: "refresh_token",
        isFirstLogin: false,
      });
      expect(smsService.verifyCode).toHaveBeenCalledWith(
        phone,
        code,
        undefined,
      );
      expect(userRepository.findOne).toHaveBeenCalledWith({ where: { phone } });
    });

    it("should create new user for first-time phone login", async () => {
      const phone = "13800138001";
      const code = "123456";
      const newUser = { ...mockUser, phone, id: "new-user-id" };

      (smsService.verifyCode as jest.Mock).mockResolvedValue(true);
      (userRepository.findOne as jest.Mock).mockResolvedValue(null);
      (userRepository.create as jest.Mock).mockReturnValue(newUser);
      (userRepository.save as jest.Mock).mockResolvedValue(newUser);
      (jwtService.signAsync as jest.Mock)
        .mockResolvedValueOnce("access_token")
        .mockResolvedValueOnce("refresh_token");

      const result = await service.loginWithPhone(phone, code);

      expect(result).toEqual({
        user: newUser,
        accessToken: "access_token",
        refreshToken: "refresh_token",
        isFirstLogin: true,
      });
      expect(userRepository.create).toHaveBeenCalled();
      expect(userRepository.save).toHaveBeenCalled();
    });

    it("should throw UnauthorizedException for invalid verification code", async () => {
      const phone = "***********";
      const code = "wrong_code";

      (smsService.verifyCode as jest.Mock).mockResolvedValue(false);

      await expect(service.loginWithPhone(phone, code)).rejects.toThrow(
        UnauthorizedException,
      );
      expect(userRepository.findOne).not.toHaveBeenCalled();
    });

    it("should throw BadRequestException for invalid phone format", async () => {
      const invalidPhone = "123";
      const code = "123456";

      await expect(service.loginWithPhone(invalidPhone, code)).rejects.toThrow(
        BadRequestException,
      );
      expect(smsService.verifyCode).not.toHaveBeenCalled();
    });

    it("should throw UnauthorizedException for locked account", async () => {
      const phone = "***********";
      const code = "123456";
      const lockedUser = {
        ...mockUser,
        isLocked: jest.fn().mockReturnValue(true),
      };

      (smsService.verifyCode as jest.Mock).mockResolvedValue(true);
      (userRepository.findOne as jest.Mock).mockResolvedValue(lockedUser);

      await expect(service.loginWithPhone(phone, code)).rejects.toThrow(
        UnauthorizedException,
      );
      expect(lockedUser.isLocked).toHaveBeenCalled();
    });
  });

  describe("registerWithEmail", () => {
    it("should register new user successfully", async () => {
      const email = "<EMAIL>";
      const password = "password123";
      const username = "testuser";

      (userRepository.findOne as jest.Mock).mockResolvedValue(null);
      (userRepository.create as jest.Mock).mockReturnValue(mockUser);
      (userRepository.save as jest.Mock).mockResolvedValue(mockUser);
      (jwtService.signAsync as jest.Mock)
        .mockResolvedValueOnce("access_token")
        .mockResolvedValueOnce("refresh_token");

      const result = await service.registerWithEmail(email, password, username);

      expect(result).toEqual({
        user: mockUser,
        accessToken: "access_token",
        refreshToken: "refresh_token",
      });
      expect(userRepository.findOne).toHaveBeenCalledWith({ where: { email } });
      expect(userRepository.create).toHaveBeenCalled();
      expect(userRepository.save).toHaveBeenCalled();
    });

    it("should throw BadRequestException for invalid email format", async () => {
      const invalidEmail = "invalid-email";
      const password = "password123";

      await expect(
        service.registerWithEmail(invalidEmail, password),
      ).rejects.toThrow(BadRequestException);
      expect(userRepository.findOne).not.toHaveBeenCalled();
    });

    it("should throw BadRequestException for weak password", async () => {
      const email = "<EMAIL>";
      const weakPassword = "123";

      await expect(
        service.registerWithEmail(email, weakPassword),
      ).rejects.toThrow(BadRequestException);
      expect(userRepository.findOne).not.toHaveBeenCalled();
    });

    it("should throw ConflictException for existing email", async () => {
      const email = "<EMAIL>";
      const password = "password123";

      (userRepository.findOne as jest.Mock).mockResolvedValue(mockUser);

      await expect(service.registerWithEmail(email, password)).rejects.toThrow(
        ConflictException,
      );
      expect(userRepository.create).not.toHaveBeenCalled();
    });
  });

  describe("loginWithEmail", () => {
    it("should login successfully with valid credentials", async () => {
      const email = "<EMAIL>";
      const password = "password123";
      const clientIP = "***********";

      // 🔥 关键：确保bcrypt compare返回true表示密码正确
      (mockCompare as jest.Mock).mockResolvedValue(true);

      (userRepository.findOne as jest.Mock).mockResolvedValue(mockUser);
      (jwtService.signAsync as jest.Mock)
        .mockResolvedValueOnce("access_token")
        .mockResolvedValueOnce("refresh_token");

      const result = await service.loginWithEmail(email, password, clientIP);

      expect(result).toEqual({
        user: mockUser,
        accessToken: "access_token",
        refreshToken: "refresh_token",
      });
      expect(userRepository.findOne).toHaveBeenCalledWith({ where: { email } });
    });

    it("should throw BadRequestException for invalid email format", async () => {
      const invalidEmail = "invalid-email";
      const password = "password123";

      await expect(
        service.loginWithEmail(invalidEmail, password),
      ).rejects.toThrow(BadRequestException);
    });

    it("should throw UnauthorizedException for non-existing user", async () => {
      const email = "<EMAIL>";
      const password = "password123";

      (userRepository.findOne as jest.Mock).mockResolvedValue(null);

      await expect(service.loginWithEmail(email, password)).rejects.toThrow(
        UnauthorizedException,
      );
    });

    it("should throw UnauthorizedException for locked account", async () => {
      const email = "<EMAIL>";
      const password = "password123";
      const lockedUser = {
        ...mockUser,
        isLocked: jest.fn().mockReturnValue(true),
      };

      (userRepository.findOne as jest.Mock).mockResolvedValue(lockedUser);

      await expect(service.loginWithEmail(email, password)).rejects.toThrow(
        UnauthorizedException,
      );
    });

    it("should throw UnauthorizedException for user without password", async () => {
      const email = "<EMAIL>";
      const password = "password123";
      const userWithoutPassword = { ...mockUser, passwordHash: null };

      (userRepository.findOne as jest.Mock).mockResolvedValue(
        userWithoutPassword,
      );

      await expect(service.loginWithEmail(email, password)).rejects.toThrow(
        UnauthorizedException,
      );
    });

    it("should throw UnauthorizedException for wrong password", async () => {
      const email = "<EMAIL>";
      const password = "wrongpassword";
      const userWithFailedAttempts = {
        ...mockUser,
        failedLoginAttempts: 0,
        shouldLockAccount: jest.fn().mockReturnValue(false),
      };

      (userRepository.findOne as jest.Mock).mockResolvedValue(
        userWithFailedAttempts,
      );
      (userRepository.save as jest.Mock).mockResolvedValue(
        userWithFailedAttempts,
      );
      (mockCompare as jest.Mock).mockResolvedValueOnce(false);

      await expect(service.loginWithEmail(email, password)).rejects.toThrow(
        UnauthorizedException,
      );
      expect(userRepository.save).toHaveBeenCalledWith(userWithFailedAttempts);
    });

    it("should lock account after failed login attempts", async () => {
      const email = "<EMAIL>";
      const password = "wrongpassword";
      const userToLock = {
        ...mockUser,
        failedLoginAttempts: 4,
        shouldLockAccount: jest.fn().mockReturnValue(true),
        lockedUntil: null,
      };

      (userRepository.findOne as jest.Mock).mockResolvedValue(userToLock);
      (userRepository.save as jest.Mock).mockResolvedValue(userToLock);
      (mockCompare as jest.Mock).mockResolvedValueOnce(false);

      await expect(service.loginWithEmail(email, password)).rejects.toThrow(
        UnauthorizedException,
      );
      expect(userToLock.shouldLockAccount).toHaveBeenCalled();
      expect(userRepository.save).toHaveBeenCalledWith(userToLock);
      expect(userToLock.lockedUntil).toBeDefined();
    });
  });

  describe("setEmailAndPassword", () => {
    it("should set email and password successfully", async () => {
      const userId = "test-user-id";
      const email = "<EMAIL>";
      const password = "newpassword123";

      // 🔥 关键修复：直接设置ConfigService Mock
      const configService = module.get<ConfigService>(ConfigService);
      configService.get = jest.fn((key: string, defaultValue?: unknown) => {
        const config: Record<string, unknown> = {
          JWT_SECRET: "test-secret",
          JWT_EXPIRES_IN: "2700",
          JWT_REFRESH_EXPIRES_IN: "1209600",
          JWT_REFRESH_SECRET: "test-refresh-secret",
          BCRYPT_ROUNDS: "12",
          SALT_ROUNDS: 12,
        };
        return config[key] !== undefined ? config[key] : defaultValue;
      });

      // 🔥 确保bcryptjs Mock正确设置
      // eslint-disable-next-line @typescript-eslint/no-var-requires
      const bcrypt = require("bcryptjs");
      bcrypt.hash.mockResolvedValue("hashed_password");

      (userRepository.findOne as jest.Mock).mockResolvedValue(null);
      (userRepository.findOne as jest.Mock)
        .mockResolvedValueOnce(null)
        .mockResolvedValueOnce(mockUser);

      const result = await service.setEmailAndPassword(userId, email, password);

      expect(result).toEqual(mockUser);
      expect(userRepository.update).toHaveBeenCalledWith(userId, {
        email,
        passwordHash: "hashed_password",
      });
    });

    it("should throw BadRequestException for invalid email format", async () => {
      const userId = "test-user-id";
      const invalidEmail = "invalid-email";
      const password = "password123";

      await expect(
        service.setEmailAndPassword(userId, invalidEmail, password),
      ).rejects.toThrow(BadRequestException);
    });

    it("should throw BadRequestException for weak password", async () => {
      const userId = "test-user-id";
      const email = "<EMAIL>";
      const weakPassword = "123";

      await expect(
        service.setEmailAndPassword(userId, email, weakPassword),
      ).rejects.toThrow(BadRequestException);
    });

    it("should throw ConflictException if email is used by another user", async () => {
      const userId = "test-user-id";
      const email = "<EMAIL>";
      const password = "password123";
      const existingUser = { ...mockUser, id: "another-user-id" };

      (userRepository.findOne as jest.Mock).mockResolvedValue(existingUser);

      await expect(
        service.setEmailAndPassword(userId, email, password),
      ).rejects.toThrow(ConflictException);
    });

    it("should throw NotFoundException if user not found after update", async () => {
      const userId = "test-user-id";
      const email = "<EMAIL>";
      const password = "password123";

      (userRepository.findOne as jest.Mock)
        .mockResolvedValueOnce(null) // 检查邮箱冲突
        .mockResolvedValueOnce(null); // 更新后查找用户

      await expect(
        service.setEmailAndPassword(userId, email, password),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe("refreshToken", () => {
    it("should refresh tokens successfully", async () => {
      const refreshToken = "valid_refresh_token";
      const payload = { sub: "test-user-id" };

      // 🔥 关键修复：直接设置ConfigService Mock
      const configService = module.get<ConfigService>(ConfigService);
      configService.get = jest.fn((key: string, defaultValue?: unknown) => {
        const config: Record<string, unknown> = {
          JWT_SECRET: "test-secret",
          JWT_EXPIRES_IN: "2700",
          JWT_REFRESH_EXPIRES_IN: "1209600",
          JWT_REFRESH_SECRET: "test-refresh-secret",
          BCRYPT_ROUNDS: "12",
          SALT_ROUNDS: 12,
        };
        return config[key] !== undefined ? config[key] : defaultValue;
      });

      (jwtService.verify as jest.Mock).mockReturnValue(payload);
      (userRepository.findOne as jest.Mock).mockResolvedValue(mockUser);
      (jwtService.signAsync as jest.Mock)
        .mockResolvedValueOnce("new_access_token")
        .mockResolvedValueOnce("new_refresh_token");

      const result = await service.refreshToken(refreshToken);

      expect(result).toEqual({
        accessToken: "new_access_token",
        refreshToken: "new_refresh_token",
      });
      expect(jwtService.verify).toHaveBeenCalledWith(refreshToken, {
        secret: "test-refresh-secret",
      });
    });

    it("should throw UnauthorizedException for invalid refresh token", async () => {
      const invalidRefreshToken = "invalid_token";

      (jwtService.verify as jest.Mock).mockImplementation(() => {
        throw new Error("Invalid token");
      });

      await expect(service.refreshToken(invalidRefreshToken)).rejects.toThrow(
        UnauthorizedException,
      );
    });

    it("should throw UnauthorizedException for non-existing user", async () => {
      const refreshToken = "valid_refresh_token";
      const payload = { sub: "non-existing-user-id" };

      (jwtService.verify as jest.Mock).mockReturnValue(payload);
      (userRepository.findOne as jest.Mock).mockResolvedValue(null);

      await expect(service.refreshToken(refreshToken)).rejects.toThrow(
        UnauthorizedException,
      );
    });

    it("should throw UnauthorizedException for inactive user", async () => {
      const refreshToken = "valid_refresh_token";
      const payload = { sub: "test-user-id" };
      const inactiveUser = { ...mockUser, isActive: false };

      (jwtService.verify as jest.Mock).mockReturnValue(payload);
      (userRepository.findOne as jest.Mock).mockResolvedValue(inactiveUser);

      await expect(service.refreshToken(refreshToken)).rejects.toThrow(
        UnauthorizedException,
      );
    });
  });

  describe("validateToken", () => {
    it("should validate token successfully", async () => {
      const token = "valid_token";
      const payload = { sub: "test-user-id" };

      (jwtService.verify as jest.Mock).mockReturnValue(payload);
      (userRepository.findOne as jest.Mock).mockResolvedValue(mockUser);

      const result = await service.validateToken(token);

      expect(result).toEqual(mockUser);
      expect(jwtService.verify).toHaveBeenCalledWith(token);
    });

    it("should throw UnauthorizedException for invalid token", async () => {
      const invalidToken = "invalid_token";

      (jwtService.verify as jest.Mock).mockImplementation(() => {
        throw new Error("Invalid token");
      });

      await expect(service.validateToken(invalidToken)).rejects.toThrow(
        UnauthorizedException,
      );
    });

    it("should throw UnauthorizedException for non-existing user", async () => {
      const token = "valid_token";
      const payload = { sub: "non-existing-user-id" };

      (jwtService.verify as jest.Mock).mockReturnValue(payload);
      (userRepository.findOne as jest.Mock).mockResolvedValue(null);

      await expect(service.validateToken(token)).rejects.toThrow(
        UnauthorizedException,
      );
    });

    it("should throw UnauthorizedException for inactive user", async () => {
      const token = "valid_token";
      const payload = { sub: "test-user-id" };
      const inactiveUser = { ...mockUser, isActive: false };

      (jwtService.verify as jest.Mock).mockReturnValue(payload);
      (userRepository.findOne as jest.Mock).mockResolvedValue(inactiveUser);

      await expect(service.validateToken(token)).rejects.toThrow(
        UnauthorizedException,
      );
    });
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  describe("Private Methods Tests", () => {
    // 测试私有方法通过公共方法调用
    describe("isValidPhone", () => {
      it("should validate Chinese phone numbers correctly", async () => {
        // 通过调用sendPhoneVerificationCode来测试isValidPhone
        await expect(
          service.sendPhoneVerificationCode("invalid_phone"),
        ).rejects.toThrow("手机号格式不正确");

        // 测试有效手机号
        mockSmsService.sendVerificationCode.mockResolvedValue({
          success: true,
          message: "验证码发送成功",
        });

        const result = await service.sendPhoneVerificationCode("***********");
        expect(result.success).toBe(true);
      });

      it("should reject invalid phone formats", async () => {
        const invalidPhones = ["123", "1234567890", "abc123", ""];

        for (const phone of invalidPhones) {
          await expect(
            service.sendPhoneVerificationCode(phone),
          ).rejects.toThrow("手机号格式不正确");
        }
      });
    });

    describe("isValidEmail", () => {
      it("should validate email formats correctly", async () => {
        // 测试无效邮箱
        await expect(
          service.registerWithEmail("invalid_email", "password123"),
        ).rejects.toThrow("邮箱格式不正确");

        // 测试有效邮箱
        mockUserRepository.findOne.mockResolvedValue(null);
        mockUserRepository.save.mockResolvedValue(mockUser);

        const result = await service.registerWithEmail(
          "<EMAIL>",
          "password123",
        );
        expect(result.user).toBeDefined();
      });

      it("should reject invalid email formats", async () => {
        // 测试各种无效邮箱格式
        await expect(
          service.registerWithEmail("invalid", "password123"),
        ).rejects.toThrow("邮箱格式不正确");

        await expect(
          service.registerWithEmail("test@", "password123"),
        ).rejects.toThrow("邮箱格式不正确");

        await expect(
          service.registerWithEmail("@example.com", "password123"),
        ).rejects.toThrow("邮箱格式不正确");

        await expect(
          service.registerWithEmail("test@example", "password123"),
        ).rejects.toThrow("邮箱格式不正确");

        await expect(
          service.registerWithEmail("test <EMAIL>", "password123"),
        ).rejects.toThrow("邮箱格式不正确");
      });
    });

    describe("isValidPassword", () => {
      it("should validate password strength correctly", async () => {
        // 测试弱密码
        await expect(
          service.registerWithEmail("<EMAIL>", "weak"),
        ).rejects.toThrow("密码强度不够，至少8位包含字母和数字");

        await expect(
          service.registerWithEmail("<EMAIL>", "12345678"),
        ).rejects.toThrow("密码强度不够，至少8位包含字母和数字");

        await expect(
          service.registerWithEmail("<EMAIL>", "password"),
        ).rejects.toThrow("密码强度不够，至少8位包含字母和数字");
      });
    });

    describe("generateTokens", () => {
      it("should generate access and refresh tokens", async () => {
        // 通过refreshToken测试generateTokens
        mockUserRepository.findOne.mockResolvedValue(mockUser);
        mockJwtService.verify.mockReturnValue({ sub: "test-user-id" });
        mockJwtService.signAsync.mockResolvedValueOnce("new-access-token");
        mockJwtService.signAsync.mockResolvedValueOnce("new-refresh-token");

        const result = await service.refreshToken("valid-refresh-token");

        expect(result.accessToken).toBe("new-access-token");
        expect(result.refreshToken).toBe("new-refresh-token");
        expect(mockJwtService.signAsync).toHaveBeenCalledTimes(2);
      });
    });

    describe("hashPassword", () => {
      it("should hash passwords correctly", async () => {
        // 🔥 关键修复：直接设置ConfigService Mock
        const configService = module.get<ConfigService>(ConfigService);
        configService.get = jest.fn((key: string, defaultValue?: unknown) => {
          const config: Record<string, unknown> = {
            JWT_SECRET: "test-secret",
            JWT_EXPIRES_IN: "2700",
            JWT_REFRESH_EXPIRES_IN: "1209600",
            JWT_REFRESH_SECRET: "test-refresh-secret",
            BCRYPT_ROUNDS: "12",
            SALT_ROUNDS: 12,
          };
          return config[key] !== undefined ? config[key] : defaultValue;
        });

        // 通过registerWithEmail测试hashPassword
        mockUserRepository.findOne.mockResolvedValue(null);
        mockUserRepository.save.mockResolvedValue(mockUser);

        await service.registerWithEmail("<EMAIL>", "password123");

        // 验证hash函数被调用
        // eslint-disable-next-line @typescript-eslint/no-var-requires
        const bcrypt = require("bcryptjs");
        expect(bcrypt.hash).toHaveBeenCalledWith("password123", 12);
      });
    });

    describe("verifyPassword", () => {
      it("should verify passwords correctly", async () => {
        // 通过loginWithEmail测试verifyPassword
        mockUserRepository.findOne.mockResolvedValue(mockUser);
        mockJwtService.signAsync.mockResolvedValueOnce("access-token");
        mockJwtService.signAsync.mockResolvedValueOnce("refresh-token");

        (mockCompare as jest.Mock).mockResolvedValue(true);

        const result = await service.loginWithEmail(
          "<EMAIL>",
          "password123",
        );

        expect(result.user).toBeDefined();
        expect(mockCompare).toHaveBeenCalledWith(
          "password123",
          "hashed_password",
        );
      });

      it("should reject wrong passwords", async () => {
        const userWithFailedAttempts = {
          ...mockUser,
          failedLoginAttempts: 0,
          shouldLockAccount: jest.fn().mockReturnValue(false),
        };

        mockUserRepository.findOne.mockResolvedValue(userWithFailedAttempts);
        mockUserRepository.save.mockResolvedValue(userWithFailedAttempts);
        (mockCompare as jest.Mock).mockResolvedValue(false);

        await expect(
          service.loginWithEmail("<EMAIL>", "wrong_password"),
        ).rejects.toThrow("邮箱或密码错误");
      });
    });

    describe("handleFailedLogin", () => {
      it("should handle failed login attempts correctly", async () => {
        const userWithFailedAttempts = {
          ...mockUser,
          failedLoginAttempts: 4,
          shouldLockAccount: jest.fn().mockReturnValue(false),
        };

        mockUserRepository.findOne.mockResolvedValue(userWithFailedAttempts);
        mockUserRepository.save.mockResolvedValue(userWithFailedAttempts);
        (mockCompare as jest.Mock).mockResolvedValue(false);

        await expect(
          service.loginWithEmail("<EMAIL>", "wrong_password"),
        ).rejects.toThrow("邮箱或密码错误");

        expect(mockUserRepository.save).toHaveBeenCalledWith(
          userWithFailedAttempts,
        );
        expect(userWithFailedAttempts.failedLoginAttempts).toBe(5);
      });
    });

    describe("handleSuccessfulLogin", () => {
      it("should handle successful login correctly", async () => {
        const userWithFailedAttempts = {
          ...mockUser,
          failedLoginAttempts: 2,
          lockedUntil: null,
          lastLoginAt: new Date(),
          lastLoginIp: null,
        };

        mockUserRepository.findOne.mockResolvedValue(userWithFailedAttempts);
        mockUserRepository.save.mockResolvedValue(userWithFailedAttempts);
        mockJwtService.signAsync.mockResolvedValueOnce("access-token");
        mockJwtService.signAsync.mockResolvedValueOnce("refresh-token");
        (mockCompare as jest.Mock).mockResolvedValue(true);

        const result = await service.loginWithEmail(
          "<EMAIL>",
          "password123",
        );

        expect(result.user).toBeDefined();
        expect(mockUserRepository.save).toHaveBeenCalledWith(
          userWithFailedAttempts,
        );
        expect(userWithFailedAttempts.failedLoginAttempts).toBe(0);
        expect(userWithFailedAttempts.lockedUntil).toBe(null);
        expect(userWithFailedAttempts.lastLoginAt).toBeDefined();
      });
    });

    describe("extractClientInfo", () => {
      it("should extract client information correctly", async () => {
        // 通过loginWithPhone测试extractClientInfo
        const clientInfo = {
          ip: "127.0.0.1",
          userAgent: "test-user-agent",
          connection: { remoteAddress: "***********" },
        };

        mockUserRepository.findOne.mockResolvedValue(mockUser);
        mockSmsService.verifyCode.mockResolvedValue(true);
        mockJwtService.signAsync.mockResolvedValueOnce("access-token");
        mockJwtService.signAsync.mockResolvedValueOnce("refresh-token");

        const result = await service.loginWithPhone(
          "***********",
          "1234",
          clientInfo,
        );

        expect(result.user).toBeDefined();
        expect(mockSmsService.verifyCode).toHaveBeenCalledWith(
          "***********",
          "1234",
          undefined,
        );
      });
    });
  });
});
