import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ConflictException,
} from "@nestjs/common";
import { EntityUpdateHelper } from "../../common/helpers/entity-update.helper";
import { InjectRepository } from "@nestjs/typeorm";
import type { SelectQueryBuilder } from "typeorm";
import { Repository } from "typeorm";
import {
  Report,
  ReportType,
  ReportStatus,
  ReportSeverity,
} from "./entities/report.entity";
import { User } from "../users/entities/user.entity";
import { Story } from "../stories/entities/story.entity";
import { Comment } from "../comments/entities/comment.entity";
import type {
  CreateReportDto,
  UpdateReportDto,
  ReportQueryDto,
  ReportResponseDto,
  ReportStatsResponseDto,
} from "./dto";
import type { PaginatedResponseDto } from "../../common/dto/paginated-response.dto";

@Injectable()
export class ReportsService {
  constructor(
    @InjectRepository(Report)
    private readonly reportRepository: Repository<Report>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Story)
    private readonly storyRepository: Repository<Story>,
    @InjectRepository(Comment)
    private readonly commentRepository: Repository<Comment>,
  ) {}

  /**
   * 创建举报
   */
  async createReport(
    createReportDto: CreateReportDto,
    reporterId: string,
  ): Promise<ReportResponseDto> {
    const { reportType, targetId, reason, description } = createReportDto;

    // 验证举报者存在
    const reporter = await this.userRepository.findOne({
      where: { id: reporterId },
    });
    if (!reporter) {
      throw new NotFoundException("举报者不存在");
    }

    // 验证目标内容存在
    await this.validateTargetExists(reportType, targetId);

    // 检查是否已经举报过相同内容
    const existingReport = await this.reportRepository.findOne({
      where: {
        reportType,
        targetId,
        reporterId,
      },
    });

    if (existingReport) {
      throw new ConflictException("您已经举报过该内容");
    }

    // 获取内容快照
    const contentSnapshot = await this.getContentSnapshot(reportType, targetId);

    // 创建举报记录
    const report = this.reportRepository.create({
      reportType,
      targetId,
      reporterId,
      reason,
      description,
      contentSnapshot,
      severity: this.calculateSeverity(reason),
    });

    const savedReport = await this.reportRepository.save(report);

    return this.formatReportResponse(savedReport);
  }

  /**
   * 获取举报列表（分页）
   */
  async getReports(
    queryDto: ReportQueryDto,
  ): Promise<PaginatedResponseDto<ReportResponseDto>> {
    const {
      page = 1,
      limit = 10,
      reportType,
      reason,
      status,
      severity,
      reporterId,
      reviewerId,
      startDate,
      endDate,
      sortBy = "createdAt",
      sortOrder = "DESC",
    } = queryDto;

    const queryBuilder = this.buildReportQuery();

    // 应用筛选条件
    if (reportType) {
      queryBuilder.andWhere("report.reportType = :reportType", {
        reportType,
      });
    }

    if (reason) {
      queryBuilder.andWhere("report.reason = :reason", { reason });
    }

    if (status) {
      queryBuilder.andWhere("report.status = :status", { status });
    }

    if (severity) {
      queryBuilder.andWhere("report.severity = :severity", { severity });
    }

    if (reporterId) {
      queryBuilder.andWhere("report.reporterId = :reporterId", {
        reporterId,
      });
    }

    if (reviewerId) {
      queryBuilder.andWhere("report.reviewerId = :reviewerId", {
        reviewerId,
      });
    }

    if (startDate) {
      queryBuilder.andWhere("report.createdAt >= :startDate", {
        startDate,
      });
    }

    if (endDate) {
      queryBuilder.andWhere("report.createdAt <= :endDate", {
        endDate,
      });
    }

    // 排序
    const validSortFields = ["createdAt", "updatedAt", "status", "severity"];
    const sortField = validSortFields.includes(sortBy) ? sortBy : "createdAt";
    queryBuilder.orderBy(`report.${sortField}`, sortOrder);

    // 分页
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    const [reports, total] = await queryBuilder.getManyAndCount();

    const formattedReports = reports.map((report) =>
      this.formatReportResponse(report),
    );

    const totalPages = Math.ceil(total / limit);
    return {
      data: formattedReports,
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  /**
   * 获取举报详情
   */
  async getReportById(id: string): Promise<ReportResponseDto> {
    const report = await this.reportRepository
      .createQueryBuilder("report")
      .leftJoinAndSelect("report.reporter", "reporter")
      .leftJoinAndSelect("report.reviewer", "reviewer")
      .where("report.id = :id", { id })
      .getOne();

    if (!report) {
      throw new NotFoundException("举报记录不存在");
    }

    return this.formatReportResponse(report);
  }

  /**
   * 更新举报状态（管理员操作）
   */
  async updateReport(
    id: string,
    updateReportDto: UpdateReportDto,
    reviewerId: string,
  ): Promise<ReportResponseDto> {
    const { status, severity, reviewNote } = updateReportDto;

    const report = await this.reportRepository.findOne({ where: { id } });
    if (!report) {
      throw new NotFoundException("举报记录不存在");
    }

    // 验证审核员存在
    const reviewer = await this.userRepository.findOne({
      where: { id: reviewerId },
    });
    if (!reviewer) {
      throw new NotFoundException("审核员不存在");
    }

    // 企业级更新数据构建 - 使用链式构建器模式
    const updateData = EntityUpdateHelper.buildUpdateData<Report>()
      .setIf(status !== undefined, "status", status!)
      .setIf(status !== undefined, "reviewerId", reviewerId)
      .setIf(status !== undefined, "reviewedAt", new Date())
      .setIf(severity !== undefined, "severity", severity!)
      .setIf(reviewNote !== undefined, "reviewNote", reviewNote!)
      .build();

    // 企业级安全更新操作
    await EntityUpdateHelper.safeUpdate(this.reportRepository, id, updateData);

    const updatedReport = await this.reportRepository
      .createQueryBuilder("report")
      .leftJoinAndSelect("report.reporter", "reporter")
      .leftJoinAndSelect("report.reviewer", "reviewer")
      .where("report.id = :id", { id })
      .getOne();

    return this.formatReportResponse(updatedReport!);
  }

  /**
   * 批量处理举报
   */
  async batchUpdateReports(
    reportIds: string[],
    status: ReportStatus,
    reviewerId: string,
    reviewNote?: string,
  ): Promise<number> {
    // 验证审核员存在
    const reviewer = await this.userRepository.findOne({
      where: { id: reviewerId },
    });
    if (!reviewer) {
      throw new NotFoundException("审核员不存在");
    }

    const result = await this.reportRepository.update(
      { id: { $in: reportIds } as any }, // eslint-disable-line @typescript-eslint/no-explicit-any
      {
        status,
        reviewerId,
        reviewedAt: new Date(),
        reviewNote,
      },
    );

    return result.affected || 0;
  }

  /**
   * 获取举报统计数据
   */
  async getReportStats(): Promise<ReportStatsResponseDto> {
    const [
      totalReports,
      pendingReports,
      underReviewReports,
      resolvedReports,
      rejectedReports,
      escalatedReports,
      storyReports,
      commentReports,
      userReports,
      todayReports,
      thisWeekReports,
      thisMonthReports,
    ] = await Promise.all([
      this.reportRepository.count(),
      this.reportRepository.count({
        where: { status: ReportStatus.PENDING },
      }),
      this.reportRepository.count({
        where: { status: ReportStatus.UNDER_REVIEW },
      }),
      this.reportRepository.count({
        where: { status: ReportStatus.RESOLVED },
      }),
      this.reportRepository.count({
        where: { status: ReportStatus.REJECTED },
      }),
      this.reportRepository.count({
        where: { status: ReportStatus.ESCALATED },
      }),
      this.reportRepository.count({
        where: { reportType: ReportType.STORY },
      }),
      this.reportRepository.count({
        where: { reportType: ReportType.COMMENT },
      }),
      this.reportRepository.count({
        where: { reportType: ReportType.USER },
      }),
      this.reportRepository
        .createQueryBuilder("report")
        .where("DATE(report.createdAt) = CURRENT_DATE")
        .getCount(),
      this.reportRepository
        .createQueryBuilder("report")
        .where("report.createdAt >= DATE_TRUNC('week', CURRENT_DATE)")
        .getCount(),
      this.reportRepository
        .createQueryBuilder("report")
        .where("report.createdAt >= DATE_TRUNC('month', CURRENT_DATE)")
        .getCount(),
    ]);

    // 获取按原因统计
    const reasonStats = await this.reportRepository
      .createQueryBuilder("report")
      .select("report.reason", "reason")
      .addSelect("COUNT(*)", "count")
      .groupBy("report.reason")
      .getRawMany();

    // 获取按严重程度统计
    const severityStats = await this.reportRepository
      .createQueryBuilder("report")
      .select("report.severity", "severity")
      .addSelect("COUNT(*)", "count")
      .groupBy("report.severity")
      .getRawMany();

    // 计算平均处理时间
    const avgProcessingTimeResult = await this.reportRepository
      .createQueryBuilder("report")
      .select(
        "AVG(EXTRACT(EPOCH FROM (report.reviewedAt - report.createdAt))/3600)",
        "avgHours",
      )
      .where("report.reviewedAt IS NOT NULL")
      .getRawOne();

    const avgProcessingTime = parseFloat(
      avgProcessingTimeResult?.avgHours || "0",
    );

    return {
      totalReports,
      pendingReports,
      underReviewReports,
      resolvedReports,
      rejectedReports,
      escalatedReports,
      storyReports,
      commentReports,
      userReports,
      reasonStats: reasonStats.map((item) => ({
        reason: item.reason,
        count: parseInt(item.count, 10),
        percentage: totalReports > 0 ? (item.count / totalReports) * 100 : 0,
      })),
      severityStats: severityStats.map((item) => ({
        severity: item.severity,
        count: parseInt(item.count, 10),
        percentage: totalReports > 0 ? (item.count / totalReports) * 100 : 0,
      })),
      avgProcessingTime: Math.round(avgProcessingTime * 100) / 100,
      todayReports,
      thisWeekReports,
      thisMonthReports,
    };
  }

  /**
   * 验证目标内容是否存在
   */
  private async validateTargetExists(
    reportType: ReportType,
    targetId: string,
  ): Promise<void> {
    switch (reportType) {
      case ReportType.STORY:
        const story = await this.storyRepository.findOne({
          where: { id: targetId },
        });
        if (!story) {
          throw new NotFoundException("要举报的故事不存在");
        }
        break;

      case ReportType.COMMENT:
        const comment = await this.commentRepository.findOne({
          where: { id: targetId },
        });
        if (!comment) {
          throw new NotFoundException("要举报的评论不存在");
        }
        break;

      case ReportType.USER:
        const user = await this.userRepository.findOne({
          where: { id: targetId },
        });
        if (!user) {
          throw new NotFoundException("要举报的用户不存在");
        }
        break;

      default:
        throw new BadRequestException("不支持的举报类型");
    }
  }

  /**
   * 获取内容快照
   */
  private async getContentSnapshot(
    reportType: ReportType,
    targetId: string,
  ): Promise<Record<string, unknown> | undefined> {
    switch (reportType) {
      case ReportType.STORY:
        const story = await this.storyRepository
          .createQueryBuilder("story")
          .leftJoinAndSelect("story.user", "author")
          .where("story.id = :id", { id: targetId })
          .getOne();

        if (story) {
          return {
            title: story.title,
            content: story.content?.substring(0, 200) + "...",
            authorName: story.user?.nickname || "未知作者",
            url: `/stories/${story.id}`,
          };
        }
        break;

      case ReportType.COMMENT:
        const comment = await this.commentRepository
          .createQueryBuilder("comment")
          .leftJoinAndSelect("comment.author", "author")
          .where("comment.id = :id", { id: targetId })
          .getOne();

        if (comment) {
          return {
            content: comment.content,
            authorName: comment.author?.nickname || "未知用户",
            url: `/comments/${comment.id}`,
          };
        }
        break;

      case ReportType.USER:
        const user = await this.userRepository.findOne({
          where: { id: targetId },
        });

        if (user) {
          return {
            title: `用户: ${user.nickname || user.username}`,
            authorName: user.nickname || user.username,
            url: `/users/${user.id}`,
          };
        }
        break;
    }

    return undefined;
  }

  /**
   * 根据举报原因计算严重程度
   */
  private calculateSeverity(reason: string): ReportSeverity {
    const criticalReasons = ["harassment", "violence", "hate_speech"];
    const highReasons = ["fraud", "personal_info_leak"];
    const mediumReasons = ["inappropriate_content", "copyright_violation"];

    if (criticalReasons.includes(reason)) {
      return ReportSeverity.CRITICAL;
    } else if (highReasons.includes(reason)) {
      return ReportSeverity.HIGH;
    } else if (mediumReasons.includes(reason)) {
      return ReportSeverity.MEDIUM;
    }

    return ReportSeverity.LOW;
  }

  /**
   * 构建举报查询
   */
  private buildReportQuery(): SelectQueryBuilder<Report> {
    return this.reportRepository
      .createQueryBuilder("report")
      .leftJoinAndSelect("report.reporter", "reporter")
      .leftJoinAndSelect("report.reviewer", "reviewer");
  }

  /**
   * 格式化举报响应
   */
  private formatReportResponse(report: Report): ReportResponseDto {
    return {
      id: report.id,
      reportType: report.reportType,
      targetId: report.targetId,
      reporterId: report.reporterId,
      reporterName: report.reporter?.nickname || "匿名用户",
      reason: report.reason,
      description: report.description,
      status: report.status,
      severity: report.severity,
      reviewerId: report.reviewerId,
      reviewerName: report.reviewer?.nickname,
      reviewNote: report.reviewNote,
      reviewedAt: report.reviewedAt,
      contentSnapshot: report.contentSnapshot as any, // eslint-disable-line @typescript-eslint/no-explicit-any
      metadata: report.metadata,
      createdAt: report.createdAt,
      updatedAt: report.updatedAt,
    };
  }
}
