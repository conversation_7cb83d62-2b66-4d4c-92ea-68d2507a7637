# ============================================
# YGS混合环境Docker配置
# 环境说明：本地Redis + 阿里云RDS PostgreSQL
# 用途：统一的开发和调试环境
# ============================================

version: '3.8'

services:
  # Redis缓存服务 - 本地Docker容器
  redis:
    image: redis:7-alpine
    platform: linux/arm64/v8
    container_name: ygs_redis
    restart: unless-stopped
    command: redis-server --requirepass ${REDIS_PASSWORD:-redis_password} --appendonly yes --ignore-warnings ARM64-COW-BUG
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf
    ports:
      - "127.0.0.1:6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - ygs_network

volumes:
  redis_data:
    driver: local
    name: ygs_redis_data

networks:
  ygs_network:
    driver: bridge
    name: ygs_network

# 注意事项：
# 1. PostgreSQL数据库使用阿里云RDS，配置在.env文件中
# 2. 应用直接在本机运行：npm run start:dev
# 3. 仅Redis通过Docker容器运行
# 4. 启动命令：npm run docker:redis