import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../themes/app_colors.dart';
import '../themes/app_typography.dart';

/// 错误状态组件
class ErrorState extends StatelessWidget {
  final String message;
  final String? details;
  final VoidCallback? onRetry;

  const ErrorState({
    super.key,
    required this.message,
    this.details,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(32.w), // 使用ScreenUtil响应式内边距
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64.sp, // 使用ScreenUtil响应式图标大小
              color: AppColors.error,
            ),
            SizedBox(height: 16.h), // 使用ScreenUtil响应式间距
            Text(
              message,
              style: AppTypography.titleLarge,
              textAlign: TextAlign.center,
            ),
            if (details != null) ...[
              SizedBox(height: 8.h), // 使用ScreenUtil响应式间距
              Text(
                details!,
                style: AppTypography.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
            ],
            if (onRetry != null) ...[
              SizedBox(height: 24.h), // 使用ScreenUtil响应式间距
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: const Text('重试'),
              ),
            ],
          ],
        ),
      ),
    );
  }
}