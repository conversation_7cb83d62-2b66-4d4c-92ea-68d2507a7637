import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// 企业级动态主色提取服务
/// 基于Material Design 3标准，提供图像主色提取和自适应遮罩生成
class DynamicColorService {
  static final DynamicColorService _instance = DynamicColorService._internal();
  factory DynamicColorService() => _instance;
  DynamicColorService._internal();

  // 主色缓存 - 避免重复计算
  final Map<String, ColorScheme> _colorCache = {};
  final Map<String, List<Color>> _gradientCache = {};

  /// 从图像提取主色并生成ColorScheme
  /// 使用Flutter内置的Material Color Utilities
  Future<ColorScheme> extractColorSchemeFromImage({
    required ImageProvider imageProvider,
    required String cacheKey,
    Brightness brightness = Brightness.light,
  }) async {
    // 检查缓存
    if (_colorCache.containsKey(cacheKey)) {
      return _colorCache[cacheKey]!;
    }

    try {
      // 使用Material Design 3的主色提取算法
      final colorScheme = await ColorScheme.fromImageProvider(
        provider: imageProvider,
        brightness: brightness,
      );

      // 缓存结果
      _colorCache[cacheKey] = colorScheme;
      return colorScheme;
    } catch (e) {
      // 降级处理：返回默认配色方案
      debugPrint('主色提取失败，使用默认配色: $e');
      final fallbackScheme = ColorScheme.fromSeed(
        seedColor: const Color(0xFF2A2A39),
        brightness: brightness,
      );
      _colorCache[cacheKey] = fallbackScheme;
      return fallbackScheme;
    }
  }

  /// 基于主色生成自适应遮罩渐变
  /// 参考网易云音乐的视觉效果，创建和谐的渐变遮罩
  List<Color> generateAdaptiveMaskGradient({
    required ColorScheme colorScheme,
    required String cacheKey,
    double opacity = 0.6,
  }) {
    // 检查缓存
    final gradientCacheKey = '${cacheKey}_$opacity';
    if (_gradientCache.containsKey(gradientCacheKey)) {
      return _gradientCache[gradientCacheKey]!;
    }

    // 基于主色生成渐变
    final primaryColor = colorScheme.primary;
    final secondaryColor = colorScheme.secondary;
    final tertiaryColor = colorScheme.tertiary;

    // 企业级渐变算法：创建深度和层次感
    final List<Color> gradient = [
      // 顶部：主色的深色变体，营造深度
      _adjustColorBrightness(primaryColor, -0.3).withValues(alpha: opacity * 0.9),
      
      // 中部：主色和次色的混合，保持和谐
      Color.lerp(primaryColor, secondaryColor, 0.3)!.withValues(alpha: opacity * 0.7),
      
      // 底部：三级色的亮色变体，增加视觉趣味
      _adjustColorBrightness(tertiaryColor, 0.2).withValues(alpha: opacity * 0.5),
      
      // 最底部：几乎透明，确保文字可读性
      tertiaryColor.withValues(alpha: opacity * 0.2),
    ];

    // 缓存结果
    _gradientCache[gradientCacheKey] = gradient;
    return gradient;
  }

  /// 生成单主色渐变 - Spotify/网易云音乐风格
  /// 基于一个主色创建深浅渐变，简洁而统一
  List<Color> generateSingleColorGradient({
    required ColorScheme colorScheme,
    required String cacheKey,
    double opacity = 0.6,
  }) {
    // 检查缓存
    final gradientCacheKey = '${cacheKey}_single_$opacity';
    if (_gradientCache.containsKey(gradientCacheKey)) {
      return _gradientCache[gradientCacheKey]!;
    }

    // 使用主色作为基础
    final baseColor = colorScheme.primary;
    
    // Spotify风格单色渐变算法：基于主色的亮度变化
    final List<Color> gradient = [
      // 顶部：主色的深色变体 (减少30%亮度)
      _adjustColorBrightness(baseColor, -0.3).withValues(alpha: opacity * 0.9),
      
      // 中上：主色的稍深变体 (减少15%亮度)
      _adjustColorBrightness(baseColor, -0.15).withValues(alpha: opacity * 0.8),
      
      // 中下：主色的稍亮变体 (增加10%亮度)
      _adjustColorBrightness(baseColor, 0.1).withValues(alpha: opacity * 0.6),
      
      // 底部：主色的亮色变体 (增加25%亮度)
      _adjustColorBrightness(baseColor, 0.25).withValues(alpha: opacity * 0.4),
    ];

    // 缓存结果
    _gradientCache[gradientCacheKey] = gradient;
    return gradient;
  }

  /// 为特定主题生成专属遮罩
  /// 结合主题特征和主色，创建独特的视觉体验
  Widget buildThematicMask({
    required ColorScheme colorScheme,
    required String themeTitle,
    required Widget child,
    double opacity = 0.5,
  }) {
    // 生成自适应渐变
    final gradientColors = generateAdaptiveMaskGradient(
      colorScheme: colorScheme,
      cacheKey: themeTitle,
      opacity: opacity,
    );

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: gradientColors,
          stops: const [0.0, 0.3, 0.7, 1.0], // 精确控制渐变分布
        ),
      ),
      child: child,
    );
  }

  /// 调整颜色亮度的辅助方法
  Color _adjustColorBrightness(Color color, double amount) {
    final hsl = HSLColor.fromColor(color);
    final lightness = (hsl.lightness + amount).clamp(0.0, 1.0);
    return hsl.withLightness(lightness).toColor();
  }

  /// 获取主色的对比色（用于文字等）
  Color getContrastColor(ColorScheme colorScheme) {
    // 使用Material Design 3的对比色算法
    return colorScheme.onPrimary;
  }

  /// 清除缓存（内存管理）
  void clearCache() {
    _colorCache.clear();
    _gradientCache.clear();
  }

  /// 获取缓存统计信息（调试用）
  Map<String, int> getCacheStats() {
    return {
      'colorCache': _colorCache.length,
      'gradientCache': _gradientCache.length,
    };
  }
}

/// 动态主色提取状态管理
class DynamicColorState {
  final ColorScheme? colorScheme;
  final List<Color>? gradientColors;
  final bool isLoading;
  final String? error;

  const DynamicColorState({
    this.colorScheme,
    this.gradientColors,
    this.isLoading = false,
    this.error,
  });

  DynamicColorState copyWith({
    ColorScheme? colorScheme,
    List<Color>? gradientColors,
    bool? isLoading,
    String? error,
  }) {
    return DynamicColorState(
      colorScheme: colorScheme ?? this.colorScheme,
      gradientColors: gradientColors ?? this.gradientColors,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }
}