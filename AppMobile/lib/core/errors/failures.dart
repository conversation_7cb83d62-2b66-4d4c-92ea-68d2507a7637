import 'package:freezed_annotation/freezed_annotation.dart';

part 'failures.freezed.dart';

/// 失败处理基类
@freezed
class Failure with _$Failure {
  const factory Failure.network({
    @Default('网络连接失败') String message,
    String? code,
    dynamic data,
  }) = NetworkFailure;

  const factory Failure.server({
    @Default('服务器错误') String message,
    String? code,
    int? statusCode,
    dynamic data,
  }) = ServerFailure;

  const factory Failure.auth({
    @Default('认证失败') String message,
    String? code,
    dynamic data,
  }) = AuthFailure;

  const factory Failure.validation({
    @Default('验证失败') String message,
    String? code,
    Map<String, List<String>>? errors,
    dynamic data,
  }) = ValidationFailure;

  const factory Failure.cache({
    @Default('缓存错误') String message,
    String? code,
    dynamic data,
  }) = CacheFailure;

  const factory Failure.permission({
    @Default('权限不足') String message,
    String? code,
    dynamic data,
  }) = PermissionFailure;

  const factory Failure.business({
    @Default('业务错误') String message,
    String? code,
    dynamic data,
  }) = BusinessFailure;

  const factory Failure.unknown({
    @Default('未知错误') String message,
    String? code,
    dynamic data,
  }) = UnknownFailure;

  const factory Failure.timeout({
    @Default('请求超时') String message,
    String? code,
    dynamic data,
  }) = TimeoutFailure;

  const factory Failure.notFound({
    @Default('资源不存在') String message,
    String? code,
    dynamic data,
  }) = NotFoundFailure;
}

/// Failure扩展方法
extension FailureX on Failure {
  /// 获取用户友好的错误消息
  String get userMessage {
    return when(
      network: (message, _, __) => message,
      server: (message, _, __, ___) => message,
      auth: (message, _, __) => message,
      validation: (message, _, __, ___) => message,
      cache: (message, _, __) => message,
      permission: (message, _, __) => message,
      business: (message, _, __) => message,
      unknown: (message, _, __) => message,
      timeout: (message, _, __) => message,
      notFound: (message, _, __) => message,
    );
  }

  /// 是否需要重新登录
  bool get requiresRelogin {
    return maybeWhen(
      auth: (_, code, __) => code == 'token_expired' || code == 'invalid_token',
      orElse: () => false,
    );
  }

  /// 是否可以重试
  bool get canRetry {
    return maybeWhen(
      network: (_, __, ___) => true,
      timeout: (_, __, ___) => true,
      server: (_, __, statusCode, ___) =>
          statusCode != null && statusCode >= 500,
      orElse: () => false,
    );
  }
}