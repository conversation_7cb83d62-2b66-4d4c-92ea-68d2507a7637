#!/usr/bin/env node

/**
 * 自动化测试覆盖率监控脚本
 * 监控项目的测试覆盖率变化，确保代码质量
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 配置参数
const CONFIG = {
  coverageThreshold: {
    statements: 85,
    branches: 85,
    functions: 85,
    lines: 85
  },
  authThreshold: {
    statements: 90,
    branches: 90,
    functions: 90,
    lines: 90
  },
  lightingThreshold: {
    statements: 90,
    branches: 90,
    functions: 90,
    lines: 90
  },
  reportPath: path.join(__dirname, '../..', 'coverage/coverage-summary.json'),
  historyPath: path.join(__dirname, '../..', 'coverage/history.json'),
  outputPath: path.join(__dirname, '../..', 'coverage/coverage-report.md')
};

class CoverageMonitor {
  constructor() {
    this.timestamp = new Date().toISOString();
    this.coverageData = null;
    this.history = this.loadHistory();
  }

  /**
   * 加载历史覆盖率数据
   */
  loadHistory() {
    try {
      if (fs.existsSync(CONFIG.historyPath)) {
        return JSON.parse(fs.readFileSync(CONFIG.historyPath, 'utf8'));
      }
    } catch (error) {
      console.warn('无法加载历史数据:', error.message);
    }
    return [];
  }

  /**
   * 保存历史覆盖率数据
   */
  saveHistory() {
    try {
      fs.writeFileSync(CONFIG.historyPath, JSON.stringify(this.history, null, 2));
    } catch (error) {
      console.error('保存历史数据失败:', error.message);
    }
  }

  /**
   * 运行测试并收集覆盖率数据
   */
  runTests() {
    console.log('🔍 正在运行测试并收集覆盖率数据...');
    
    try {
      // 运行单元测试
      execSync('npm run test:unit', { stdio: 'inherit', cwd: path.join(__dirname, '../..') });
      
      // 读取覆盖率报告
      if (fs.existsSync(CONFIG.reportPath)) {
        this.coverageData = JSON.parse(fs.readFileSync(CONFIG.reportPath, 'utf8'));
        console.log('✅ 测试覆盖率数据收集完成');
      } else {
        throw new Error('覆盖率报告文件不存在');
      }
    } catch (error) {
      console.error('❌ 测试执行失败:', error.message);
      process.exit(1);
    }
  }

  /**
   * 分析覆盖率数据
   */
  analyzeCoverage() {
    if (!this.coverageData || !this.coverageData.total) {
      console.error('❌ 无效的覆盖率数据');
      return;
    }

    const total = this.coverageData.total;
    const currentCoverage = {
      timestamp: this.timestamp,
      statements: total.statements.pct,
      branches: total.branches.pct,
      functions: total.functions.pct,
      lines: total.lines.pct
    };

    // 添加到历史记录
    this.history.push(currentCoverage);
    
    // 只保留最近30次的记录
    if (this.history.length > 30) {
      this.history = this.history.slice(-30);
    }

    console.log('📊 当前覆盖率统计:');
    console.log(`  语句覆盖率: ${currentCoverage.statements}%`);
    console.log(`  分支覆盖率: ${currentCoverage.branches}%`);
    console.log(`  函数覆盖率: ${currentCoverage.functions}%`);
    console.log(`  行覆盖率: ${currentCoverage.lines}%`);

    // 检查是否达到门槛
    this.checkThresholds(currentCoverage);
    
    // 检查趋势
    this.checkTrends(currentCoverage);
  }

  /**
   * 检查覆盖率门槛
   */
  checkThresholds(coverage) {
    const failures = [];
    
    // 检查全局门槛
    Object.keys(CONFIG.coverageThreshold).forEach(key => {
      if (coverage[key] < CONFIG.coverageThreshold[key]) {
        failures.push(`${key}: ${coverage[key]}% < ${CONFIG.coverageThreshold[key]}%`);
      }
    });

    // 检查特定模块门槛
    if (this.coverageData['src/modules/auth']) {
      this.checkModuleThreshold('auth', CONFIG.authThreshold);
    }
    
    if (this.coverageData['src/modules/lighting']) {
      this.checkModuleThreshold('lighting', CONFIG.lightingThreshold);
    }

    if (failures.length > 0) {
      console.log('\n❌ 覆盖率门槛检查失败:');
      failures.forEach(failure => console.log(`  ${failure}`));
    } else {
      console.log('\n✅ 覆盖率门槛检查通过');
    }
  }

  /**
   * 检查模块覆盖率门槛
   */
  checkModuleThreshold(moduleName, threshold) {
    const moduleData = this.coverageData[`src/modules/${moduleName}`];
    if (!moduleData) return;

    Object.keys(threshold).forEach(key => {
      if (moduleData[key] && moduleData[key].pct < threshold[key]) {
        console.log(`❌ ${moduleName}模块 ${key}: ${moduleData[key].pct}% < ${threshold[key]}%`);
      }
    });
  }

  /**
   * 检查覆盖率趋势
   */
  checkTrends(current) {
    if (this.history.length < 2) return;

    const previous = this.history[this.history.length - 2];
    const trends = {};

    Object.keys(current).forEach(key => {
      if (key !== 'timestamp') {
        const change = current[key] - previous[key];
        trends[key] = {
          current: current[key],
          previous: previous[key],
          change: change,
          direction: change > 0 ? '↗️' : change < 0 ? '↘️' : '➡️'
        };
      }
    });

    console.log('\n📈 覆盖率趋势分析:');
    Object.entries(trends).forEach(([key, trend]) => {
      console.log(`  ${key}: ${trend.previous}% → ${trend.current}% (${trend.change > 0 ? '+' : ''}${trend.change.toFixed(2)}%) ${trend.direction}`);
    });
  }

  /**
   * 生成覆盖率报告
   */
  generateReport() {
    const report = this.buildMarkdownReport();
    
    try {
      fs.writeFileSync(CONFIG.outputPath, report);
      console.log(`\n📝 覆盖率报告已生成: ${CONFIG.outputPath}`);
    } catch (error) {
      console.error('生成报告失败:', error.message);
    }
  }

  /**
   * 构建 Markdown 报告
   */
  buildMarkdownReport() {
    const current = this.history[this.history.length - 1];
    const previous = this.history.length > 1 ? this.history[this.history.length - 2] : null;
    
    let report = `# 测试覆盖率监控报告\n\n`;
    report += `**生成时间**: ${this.timestamp}\n\n`;
    
    // 当前覆盖率
    report += `## 📊 当前覆盖率\n\n`;
    report += `| 指标 | 覆盖率 | 门槛 | 状态 |\n`;
    report += `|------|-------|------|------|\n`;
    
    Object.entries(CONFIG.coverageThreshold).forEach(([key, threshold]) => {
      const status = current[key] >= threshold ? '✅' : '❌';
      report += `| ${key} | ${current[key]}% | ${threshold}% | ${status} |\n`;
    });
    
    // 趋势分析
    if (previous) {
      report += `\n## 📈 变化趋势\n\n`;
      report += `| 指标 | 上次 | 本次 | 变化 |\n`;
      report += `|------|------|------|------|\n`;
      
      Object.keys(CONFIG.coverageThreshold).forEach(key => {
        const change = current[key] - previous[key];
        const direction = change > 0 ? '↗️' : change < 0 ? '↘️' : '➡️';
        report += `| ${key} | ${previous[key]}% | ${current[key]}% | ${change > 0 ? '+' : ''}${change.toFixed(2)}% ${direction} |\n`;
      });
    }
    
    // 历史记录
    report += `\n## 📚 历史记录\n\n`;
    report += `最近10次测试覆盖率:\n\n`;
    
    const recentHistory = this.history.slice(-10);
    recentHistory.forEach((record, index) => {
      const date = new Date(record.timestamp).toLocaleDateString();
      report += `${index + 1}. **${date}**: 语句${record.statements}%, 分支${record.branches}%, 函数${record.functions}%, 行${record.lines}%\n`;
    });
    
    return report;
  }

  /**
   * 运行完整监控流程
   */
  run() {
    console.log('🚀 开始测试覆盖率监控...\n');
    
    this.runTests();
    this.analyzeCoverage();
    this.generateReport();
    this.saveHistory();
    
    console.log('\n🎉 测试覆盖率监控完成!');
  }
}

// 执行监控
if (require.main === module) {
  const monitor = new CoverageMonitor();
  monitor.run();
}

module.exports = CoverageMonitor;