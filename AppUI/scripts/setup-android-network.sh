#!/bin/bash

# ===========================================
# 有故事 APP - Android 网络配置脚本
# ===========================================

echo "🚀 开始配置 Android 模拟器网络连接..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查 adb 是否可用
check_adb() {
    if ! command -v adb &> /dev/null; then
        if [ -n "$ANDROID_HOME" ]; then
            export PATH="$ANDROID_HOME/platform-tools:$PATH"
        else
            echo -e "${RED}❌ ADB 未找到，请确保 Android SDK 已安装并配置环境变量${NC}"
            exit 1
        fi
    fi
    echo -e "${GREEN}✅ ADB 可用${NC}"
}

# 检查设备连接
check_device() {
    echo -e "${BLUE}📱 检查设备连接...${NC}"
    DEVICES=$(adb devices | grep -v "List of devices" | grep -v "^$")
    
    if [ -z "$DEVICES" ]; then
        echo -e "${RED}❌ 未找到连接的设备或模拟器${NC}"
        echo -e "${YELLOW}💡 请确保：${NC}"
        echo -e "   1. Android 模拟器正在运行"
        echo -e "   2. USB 调试已启用"
        echo -e "   3. 设备已正确连接"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 设备已连接${NC}"
    echo "$DEVICES"
}

# 设置端口转发
setup_port_forwarding() {
    echo -e "${BLUE}🔄 设置端口转发...${NC}"
    
    # Metro bundler 端口
    echo -e "${YELLOW}  设置 Metro bundler 端口转发 (8081)...${NC}"
    adb reverse tcp:8081 tcp:8081
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}  ✅ Metro bundler 端口转发设置成功${NC}"
    else
        echo -e "${RED}  ❌ Metro bundler 端口转发设置失败${NC}"
    fi
    
    # API 服务端口
    echo -e "${YELLOW}  设置 API 服务端口转发 (3000)...${NC}"
    adb reverse tcp:3000 tcp:3000
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}  ✅ API 服务端口转发设置成功${NC}"
    else
        echo -e "${RED}  ❌ API 服务端口转发设置失败${NC}"
    fi
}

# 测试网络连接
test_network() {
    echo -e "${BLUE}🌐 测试网络连接...${NC}"
    
    # 测试 Metro bundler
    echo -e "${YELLOW}  测试 Metro bundler 连接...${NC}"
    if curl -s --max-time 5 http://localhost:8081 > /dev/null; then
        echo -e "${GREEN}  ✅ Metro bundler 连接正常${NC}"
    else
        echo -e "${RED}  ❌ Metro bundler 连接失败${NC}"
        echo -e "${YELLOW}  💡 请运行：npx react-native start${NC}"
    fi
    
    # 测试 API 服务
    echo -e "${YELLOW}  测试 API 服务连接...${NC}"
    if curl -s --max-time 5 http://localhost:3000/health > /dev/null; then
        echo -e "${GREEN}  ✅ API 服务连接正常${NC}"
    else
        echo -e "${RED}  ❌ API 服务连接失败${NC}"
        echo -e "${YELLOW}  💡 请检查后端服务是否运行：docker-compose ps${NC}"
    fi
}

# 显示配置信息
show_config() {
    echo -e "${BLUE}📋 网络配置信息：${NC}"
    echo -e "  Platform: ${GREEN}Android${NC}"
    echo -e "  Metro bundler: ${GREEN}http://localhost:8081${NC}"
    echo -e "  API 服务: ${GREEN}http://********:3000/api/v1${NC}"
    echo -e "  端口转发:"
    echo -e "    - 8081:8081 (Metro)"
    echo -e "    - 3000:3000 (API)"
}

# 主函数
main() {
    echo -e "${GREEN}============================================${NC}"
    echo -e "${GREEN}  有故事 APP - Android 网络配置${NC}"
    echo -e "${GREEN}============================================${NC}"
    echo ""
    
    check_adb
    check_device
    setup_port_forwarding
    test_network
    show_config
    
    echo ""
    echo -e "${GREEN}🎉 Android 网络配置完成！${NC}"
    echo -e "${YELLOW}📝 如果仍有问题，请检查：${NC}"
    echo -e "   1. 防火墙设置"
    echo -e "   2. Metro bundler 是否运行"
    echo -e "   3. 后端服务是否启动"
    echo -e "   4. 模拟器网络设置"
}

# 运行主函数
main "$@" 