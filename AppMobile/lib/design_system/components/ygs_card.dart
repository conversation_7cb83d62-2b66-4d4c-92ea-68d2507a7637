import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../tokens/ygs_colors.dart';
import '../tokens/ygs_spacing.dart';

/// YGS设计系统 - 卡片组件
/// 基于Figma设计规范v1.0.0
class YgsCard extends StatelessWidget {
  const YgsCard({
    required this.child,
    this.padding,
    this.margin,
    this.borderRadius,
    this.backgroundColor,
    this.elevation = YgsCardElevation.medium,
    this.border,
    this.onTap,
    super.key,
  });

  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final BorderRadius? borderRadius;
  final Color? backgroundColor;
  final YgsCardElevation elevation;
  final Border? border;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      decoration: BoxDecoration(
        color: backgroundColor ?? YgsColors.backgroundCard,
        borderRadius: borderRadius ?? YgsSpacing.borderRadiusLarge,
        border: border,
        boxShadow: _getShadow(),
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: borderRadius ?? YgsSpacing.borderRadiusLarge,
        child: InkWell(
          onTap: onTap,
          borderRadius: borderRadius ?? YgsSpacing.borderRadiusLarge,
          child: Container(
            padding: padding ?? YgsSpacing.paddingCard,
            child: child,
          ),
        ),
      ),
    );
  }

  /// 获取阴影效果
  List<BoxShadow> _getShadow() {
    switch (elevation) {
      case YgsCardElevation.none:
        return [];
      case YgsCardElevation.low:
        return [
          BoxShadow(
            color: YgsColors.divider.withValues(alpha: 0.2),
            blurRadius: 4.r,
            offset: Offset(0, 2.h),
            spreadRadius: 0,
          ),
        ];
      case YgsCardElevation.medium:
        return YgsColors.cardShadow;
      case YgsCardElevation.high:
        return YgsColors.elevatedShadow;
    }
  }
}

/// 卡片高度枚举
enum YgsCardElevation {
  /// 无阴影
  none,
  
  /// 低阴影
  low,
  
  /// 中阴影
  medium,
  
  /// 高阴影
  high,
}