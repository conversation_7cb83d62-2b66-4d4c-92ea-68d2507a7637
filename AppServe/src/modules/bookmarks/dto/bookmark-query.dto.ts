import {
  IsOptional,
  IsString,
  IsEnum,
  IsNumberString,
  <PERSON>,
  <PERSON>,
} from "class-validator";
import { Type } from "class-transformer";
import { BookmarkCategory, BookmarkStatus } from "../entities/bookmark.entity";

export enum BookmarkSortBy {
  CREATED_DESC = "created_desc",
  CREATED_ASC = "created_asc",
  UPDATED_DESC = "updated_desc",
  STORY_TITLE_ASC = "story_title_asc",
}

/**
 * 收藏查询DTO
 */
export class BookmarkQueryDto {
  @IsOptional()
  @IsEnum(BookmarkCategory, {
    message: "收藏分类必须是有效的分类选项",
  })
  category?: BookmarkCategory;

  @IsOptional()
  @IsString({
    message: "自定义分类名称必须是字符串",
  })
  customCategoryName?: string;

  @IsOptional()
  @IsEnum(BookmarkStatus, {
    message: "收藏状态必须是有效的状态选项",
  })
  status?: BookmarkStatus;

  @IsOptional()
  @IsString({
    message: "搜索关键词必须是字符串",
  })
  keyword?: string;

  @IsOptional()
  @IsNumberString(
    {},
    {
      message: "页码必须是数字字符串",
    },
  )
  @Type(() => Number)
  @Min(1, {
    message: "页码最小值为1",
  })
  page?: number = 1;

  @IsOptional()
  @IsNumberString(
    {},
    {
      message: "每页数量必须是数字字符串",
    },
  )
  @Type(() => Number)
  @Min(1, {
    message: "每页数量最小值为1",
  })
  @Max(100, {
    message: "每页数量最大值为100",
  })
  limit?: number = 20;

  @IsOptional()
  @IsEnum(BookmarkSortBy, {
    message: "排序方式必须是有效的排序选项",
  })
  sortBy?: BookmarkSortBy = BookmarkSortBy.CREATED_DESC;
}
