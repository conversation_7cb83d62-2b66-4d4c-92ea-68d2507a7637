import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  <PERSON>inCol<PERSON>n,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from "typeorm";
import { User } from "../../users/entities/user.entity";
import { Story } from "../../stories/entities/story.entity";
import { Comment } from "../../comments/entities/comment.entity";

export enum ReportType {
  STORY = "story",
  COMMENT = "comment",
  USER = "user",
}

export enum ReportReason {
  SPAM = "spam",
  INAPPROPRIATE_CONTENT = "inappropriate_content",
  HARASSMENT = "harassment",
  COPYRIGHT_VIOLATION = "copyright_violation",
  PERSONAL_INFO_LEAK = "personal_info_leak",
  FRAUD = "fraud",
  VIOLENCE = "violence",
  HATE_SPEECH = "hate_speech",
  OTHER = "other",
}

export enum ReportStatus {
  PENDING = "pending",
  UNDER_REVIEW = "under_review",
  RESOLVED = "resolved",
  REJECTED = "rejected",
  ESCALATED = "escalated",
}

export enum ReportSeverity {
  LOW = "low",
  MEDIUM = "medium",
  HIGH = "high",
  CRITICAL = "critical",
}

/**
 * 举报实体
 * 用户举报不当内容或行为的记录
 */
@Entity("reports")
@Index(["reportType", "targetId", "status"])
@Index(["reporterId", "createdAt"])
@Index(["status", "severity", "createdAt"])
@Index(["reviewerId", "status"])
export class Report {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({
    type: "enum",
    enum: ReportType,
    comment: "举报类型：story-故事，comment-评论，user-用户",
  })
  reportType: ReportType;

  @Column({
    type: "uuid",
    comment: "被举报目标的ID（故事ID、评论ID或用户ID）",
  })
  targetId: string;

  @Column({
    type: "uuid",
    comment: "举报者用户ID",
  })
  reporterId: string;

  @Column({
    type: "enum",
    enum: ReportReason,
    comment: "举报原因",
  })
  reason: ReportReason;

  @Column({
    type: "text",
    nullable: true,
    comment: "详细说明",
  })
  description: string;

  @Column({
    type: "enum",
    enum: ReportStatus,
    default: ReportStatus.PENDING,
    comment: "处理状态",
  })
  status: ReportStatus;

  @Column({
    type: "enum",
    enum: ReportSeverity,
    default: ReportSeverity.MEDIUM,
    comment: "严重程度",
  })
  severity: ReportSeverity;

  @Column({
    type: "uuid",
    nullable: true,
    comment: "审核员用户ID",
  })
  reviewerId: string;

  @Column({
    type: "text",
    nullable: true,
    comment: "审核结果说明",
  })
  reviewNote: string;

  @Column({
    type: "timestamp with time zone",
    nullable: true,
    comment: "审核时间",
  })
  reviewedAt: Date;

  @Column({
    type: "jsonb",
    nullable: true,
    comment: "举报时的内容快照",
  })
  contentSnapshot: {
    title?: string;
    content?: string;
    authorName?: string;
    url?: string;
  };

  @Column({
    type: "jsonb",
    nullable: true,
    comment: "扩展字段",
  })
  metadata: Record<string, unknown>;

  @CreateDateColumn({
    type: "timestamp with time zone",
    comment: "举报时间",
  })
  createdAt: Date;

  @UpdateDateColumn({
    type: "timestamp with time zone",
    comment: "更新时间",
  })
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => User, { eager: false })
  @JoinColumn({ name: "reporterId" })
  reporter: User;

  @ManyToOne(() => User, { eager: false })
  @JoinColumn({ name: "reviewerId" })
  reviewer?: User;

  // 根据举报类型动态关联不同实体
  // 注意：这些关联不会创建实际的外键约束，因为 targetId 是多态字段
  @ManyToOne(() => Story, { eager: false, createForeignKeyConstraints: false })
  @JoinColumn({ name: "targetId" })
  targetStory?: Story;

  @ManyToOne(() => Comment, {
    eager: false,
    createForeignKeyConstraints: false,
  })
  @JoinColumn({ name: "targetId" })
  targetComment?: Comment;

  @ManyToOne(() => User, { eager: false, createForeignKeyConstraints: false })
  @JoinColumn({ name: "targetId" })
  targetUser?: User;
}
