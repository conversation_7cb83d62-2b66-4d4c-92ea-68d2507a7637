import { Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { UsersController } from "./users.controller";
import { UsersService } from "./users.service";
import { NotificationsController } from "./notifications.controller";
import { NotificationsService } from "./notifications.service";
import { User } from "./entities/user.entity";
import { UserRelationship } from "./entities/user-relationship.entity";
import { RefreshToken } from "./entities/refresh-token.entity";
import { Notification } from "./entities/notification.entity";
import { AuthModule } from "../auth/auth.module";
import { UploadModule } from "../upload/upload.module";

/**
 * 用户模块 - v1.0.0
 * 提供用户管理、个人资料、朋友关系、通知等核心功能
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      User,
      UserRelationship,
      RefreshToken,
      Notification,
    ]),
    AuthModule, // 导入AuthModule以使用JwtAuthGuard
    UploadModule, // 导入UploadModule以使用文件上传服务
  ],
  controllers: [UsersController, NotificationsController],
  providers: [UsersService, NotificationsService],
  exports: [UsersService, NotificationsService], // 导出服务供其他模块使用
})
export class UsersModule {}
