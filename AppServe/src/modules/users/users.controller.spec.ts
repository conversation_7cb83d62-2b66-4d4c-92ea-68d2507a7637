import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { ForbiddenException } from "@nestjs/common";
import { UsersController } from "./users.controller";
import { UsersService } from "./users.service";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import type {
  UpdateUserDto,
  UpdateDisplaySettingsDto,
  UserStatisticsDto,
  UserProfileDto,
} from "./dto/update-user.dto";
import type { User } from "./entities/user.entity";

describe("UsersController", () => {
  let controller: UsersController;
  let usersService: UsersService;

  // Mock用户数据
  const mockUser: User = {
    id: "test-user-id",
    phone: "13800138000",
    email: "<EMAIL>",
    passwordHash: "hashed_password",
    userNumber: "200001",
    nickname: "testuser",
    username: "test_username",
    avatarUrl: "https://example.com/avatar.jpg",
    birthDate: new Date("1990-01-01"),
    bio: "这是一个测试用户",
    aiQuotaRemaining: 3,
    aiQuotaResetDate: new Date(),
    profileDisplaySettings: {
      showBirthday: false,
      showBio: true,
      showStatistics: true,
      showCharacters: true,
      showTimeline: true,
    },
    anomalyStatus: "normal",
    status: "active",
    isActive: true,
    lastLoginAt: new Date(),
    lastLoginIp: "***********",
    failedLoginAttempts: 0,
    lockedUntil: null,
    anomalyWarningCount: 0,
    anomalyRestrictionCount: 0,
    lightingRestrictedUntil: null,
    lastInvalidLightingAttempt: null,
    dailyLightingAttempts: 0,
    lightingAttemptResetDate: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
  } as User;

  const mockUserProfile: UserProfileDto = {
    id: mockUser.id,
    userNumber: mockUser.userNumber,
    nickname: mockUser.nickname,
    username: mockUser.username,
    avatarUrl: mockUser.avatarUrl,
    birthDate: mockUser.birthDate,
    bio: mockUser.bio,
    profileDisplaySettings: mockUser.profileDisplaySettings,
    createdAt: mockUser.createdAt,
    statistics: {
      storyCount: 5,
      characterCount: 10,
      lightingCount: 3,
      followingCount: 15,
      followersCount: 8,
    },
  };

  const mockStatistics: UserStatisticsDto = {
    storyCount: 5,
    characterCount: 10,
    lightingCount: 3,
    followingCount: 15,
    followersCount: 8,
  };

  const mockRequest = {
    user: {
      sub: "test-user-id",
      phone: "13800138000",
      email: "<EMAIL>",
    },
  };

  const mockFriendsList = {
    friends: [
      {
        id: "friend-1",
        userNumber: "200002",
        nickname: "friend1",
        username: "friend_1",
        avatarUrl: null,
        createdAt: new Date(),
      },
    ],
    total: 1,
    hasNext: false,
  };

  const mockSearchResults = [
    {
      id: "user-1",
      userNumber: "200001",
      nickname: "testuser",
      username: "test_username",
      avatarUrl: null,
    },
  ];

  beforeEach(async () => {
    const mockUsersService = {
      getUserProfile: jest.fn(),
      updateProfile: jest.fn(),
      updateDisplaySettings: jest.fn(),
      getUserStatistics: jest.fn(),
      findById: jest.fn(),
      searchUsers: jest.fn(),
      findByUserNumber: jest.fn(),
      addFriend: jest.fn(),
      removeFriend: jest.fn(),
      getFriendsList: jest.fn(),
      isFriend: jest.fn(),
      deleteUser: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [UsersController],
      providers: [
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({
        canActivate: jest.fn().mockReturnValue(true),
      })
      .compile();

    controller = module.get<UsersController>(UsersController);
    usersService = module.get<UsersService>(UsersService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("testUserTag", () => {
    it("should return test message", async () => {
      const result = await controller.testUserTag();
      expect(result).toEqual({ message: "测试用户管理标签" });
    });
  });

  describe("getCurrentUserProfile", () => {
    it("should return current user profile", async () => {
      (usersService.getUserProfile as jest.Mock).mockResolvedValue(
        mockUserProfile,
      );

      const result = await controller.getCurrentUserProfile(mockRequest);

      expect(result).toEqual(mockUserProfile);
      expect(usersService.getUserProfile).toHaveBeenCalledWith(
        mockRequest.user.sub,
        mockRequest.user.sub,
      );
    });
  });

  describe("getUserProfile", () => {
    it("should return specified user profile", async () => {
      const targetUserId = "target-user-id";
      (usersService.getUserProfile as jest.Mock).mockResolvedValue(
        mockUserProfile,
      );

      const result = await controller.getUserProfile(targetUserId, mockRequest);

      expect(result).toEqual(mockUserProfile);
      expect(usersService.getUserProfile).toHaveBeenCalledWith(
        targetUserId,
        mockRequest.user.sub,
      );
    });
  });

  describe("updateProfile", () => {
    const updateData: UpdateUserDto = {
      nickname: "newnickname",
      bio: "新的个人简介",
    };

    it("should update user profile successfully", async () => {
      const updatedUser = { ...mockUser, ...updateData };
      (usersService.updateProfile as jest.Mock).mockResolvedValue(updatedUser);

      const result = await controller.updateProfile(mockRequest, updateData);

      expect(result).toEqual({
        success: true,
        message: "个人信息更新成功",
        data: updatedUser,
      });
      expect(usersService.updateProfile).toHaveBeenCalledWith(
        mockRequest.user.sub,
        updateData,
      );
    });

    it("should handle service errors", async () => {
      (usersService.updateProfile as jest.Mock).mockRejectedValue(
        new Error("昵称已被使用"),
      );

      await expect(
        controller.updateProfile(mockRequest, updateData),
      ).rejects.toThrow("昵称已被使用");
    });
  });

  describe("updateDisplaySettings", () => {
    const settingsData: UpdateDisplaySettingsDto = {
      profileDisplaySettings: {
        showBirthday: true,
        showBio: false,
        showStatistics: true,
        showCharacters: false,
        showTimeline: true,
      },
    };

    it("should update display settings successfully", async () => {
      const updatedUser = {
        ...mockUser,
        profileDisplaySettings: settingsData.profileDisplaySettings,
      };
      (usersService.updateDisplaySettings as jest.Mock).mockResolvedValue(
        updatedUser,
      );

      const result = await controller.updateDisplaySettings(
        mockRequest,
        settingsData,
      );

      expect(result).toEqual({
        success: true,
        message: "展示设置更新成功",
        data: settingsData.profileDisplaySettings,
      });
      expect(usersService.updateDisplaySettings).toHaveBeenCalledWith(
        mockRequest.user.sub,
        settingsData,
      );
    });
  });

  describe("getUserStatistics", () => {
    it("should return statistics for owner", async () => {
      (usersService.findById as jest.Mock).mockResolvedValue(mockUser);
      (usersService.getUserStatistics as jest.Mock).mockResolvedValue(
        mockStatistics,
      );

      const result = await controller.getUserStatistics(
        mockRequest.user.sub,
        mockRequest,
      );

      expect(result).toEqual(mockStatistics);
      expect(usersService.getUserStatistics).toHaveBeenCalledWith(
        mockRequest.user.sub,
      );
    });

    it("should return statistics for non-owner when allowed", async () => {
      const targetUserId = "target-user-id";
      const userWithOpenSettings = {
        ...mockUser,
        profileDisplaySettings: {
          ...mockUser.profileDisplaySettings,
          showStatistics: true,
        },
      };

      (usersService.findById as jest.Mock).mockResolvedValue(
        userWithOpenSettings,
      );
      (usersService.getUserStatistics as jest.Mock).mockResolvedValue(
        mockStatistics,
      );

      const result = await controller.getUserStatistics(
        targetUserId,
        mockRequest,
      );

      expect(result).toEqual(mockStatistics);
    });

    it("should throw ForbiddenException when statistics are private", async () => {
      const targetUserId = "target-user-id";
      const userWithPrivateSettings = {
        ...mockUser,
        profileDisplaySettings: {
          ...mockUser.profileDisplaySettings,
          showStatistics: false,
        },
      };

      (usersService.findById as jest.Mock).mockResolvedValue(
        userWithPrivateSettings,
      );

      await expect(
        controller.getUserStatistics(targetUserId, mockRequest),
      ).rejects.toThrow(ForbiddenException);
      await expect(
        controller.getUserStatistics(targetUserId, mockRequest),
      ).rejects.toThrow("没有权限查看统计数据");
    });
  });

  describe("searchUsers", () => {
    it("should search users successfully", async () => {
      (usersService.searchUsers as jest.Mock).mockResolvedValue(
        mockSearchResults,
      );

      const result = await controller.searchUsers("test");

      expect(result).toEqual({
        success: true,
        message: "搜索完成",
        data: mockSearchResults,
      });
      expect(usersService.searchUsers).toHaveBeenCalledWith("test", 20);
    });

    it("should search users with custom limit", async () => {
      (usersService.searchUsers as jest.Mock).mockResolvedValue(
        mockSearchResults,
      );

      const result = await controller.searchUsers("test", "10");

      expect(result).toEqual({
        success: true,
        message: "搜索完成",
        data: mockSearchResults,
      });
      expect(usersService.searchUsers).toHaveBeenCalledWith("test", 10);
    });

    it("should return error for empty query", async () => {
      const result = await controller.searchUsers("");

      expect(result).toEqual({
        success: false,
        message: "搜索关键词不能为空",
        data: [],
      });
      expect(usersService.searchUsers).not.toHaveBeenCalled();
    });

    it("should return error for whitespace-only query", async () => {
      const result = await controller.searchUsers("   ");

      expect(result).toEqual({
        success: false,
        message: "搜索关键词不能为空",
        data: [],
      });
      expect(usersService.searchUsers).not.toHaveBeenCalled();
    });

    it("should trim query before searching", async () => {
      (usersService.searchUsers as jest.Mock).mockResolvedValue(
        mockSearchResults,
      );

      await controller.searchUsers("  test  ");

      expect(usersService.searchUsers).toHaveBeenCalledWith("test", 20);
    });
  });

  describe("findByUserNumber", () => {
    it("should find user by valid user number", async () => {
      (usersService.findByUserNumber as jest.Mock).mockResolvedValue(mockUser);

      const result = await controller.findByUserNumber("200001");

      expect(result).toEqual({
        success: true,
        message: "用户查找成功",
        data: {
          id: mockUser.id,
          userNumber: mockUser.userNumber,
          nickname: mockUser.nickname,
          avatarUrl: mockUser.avatarUrl,
        },
      });
      expect(usersService.findByUserNumber).toHaveBeenCalledWith("200001");
    });

    it("should return error for invalid user number format", async () => {
      const result = await controller.findByUserNumber("12345");

      expect(result).toEqual({
        success: false,
        message: "有故事号格式不正确",
        data: null,
      });
      expect(usersService.findByUserNumber).not.toHaveBeenCalled();
    });

    it("should return error for non-numeric user number", async () => {
      const result = await controller.findByUserNumber("abc123");

      expect(result).toEqual({
        success: false,
        message: "有故事号格式不正确",
        data: null,
      });
      expect(usersService.findByUserNumber).not.toHaveBeenCalled();
    });

    it("should handle user not found", async () => {
      (usersService.findByUserNumber as jest.Mock).mockRejectedValue(
        new Error("用户不存在"),
      );

      const result = await controller.findByUserNumber("999999");

      expect(result).toEqual({
        success: false,
        message: "用户不存在",
        data: null,
      });
    });
  });

  describe("Friend Management", () => {
    describe("addFriend", () => {
      it("should add friend successfully", async () => {
        const friendId = "friend-user-id";
        (usersService.addFriend as jest.Mock).mockResolvedValue(undefined);

        const result = await controller.addFriend(friendId, mockRequest);

        expect(result).toEqual({
          success: true,
          message: "好友添加成功",
        });
        expect(usersService.addFriend).toHaveBeenCalledWith(
          mockRequest.user.sub,
          friendId,
        );
      });

      it("should handle service errors", async () => {
        const friendId = "friend-user-id";
        (usersService.addFriend as jest.Mock).mockRejectedValue(
          new Error("不能添加自己为朋友"),
        );

        await expect(
          controller.addFriend(friendId, mockRequest),
        ).rejects.toThrow("不能添加自己为朋友");
      });
    });

    describe("removeFriend", () => {
      it("should remove friend successfully", async () => {
        const friendId = "friend-user-id";
        (usersService.removeFriend as jest.Mock).mockResolvedValue(undefined);

        const result = await controller.removeFriend(friendId, mockRequest);

        expect(result).toEqual({
          success: true,
          message: "好友移除成功",
        });
        expect(usersService.removeFriend).toHaveBeenCalledWith(
          mockRequest.user.sub,
          friendId,
        );
      });

      it("should handle service errors", async () => {
        const friendId = "friend-user-id";
        (usersService.removeFriend as jest.Mock).mockRejectedValue(
          new Error("朋友关系不存在"),
        );

        await expect(
          controller.removeFriend(friendId, mockRequest),
        ).rejects.toThrow("朋友关系不存在");
      });
    });

    describe("getFriendsList", () => {
      it("should get friends list with default pagination", async () => {
        (usersService.getFriendsList as jest.Mock).mockResolvedValue(
          mockFriendsList,
        );

        const result = await controller.getFriendsList(mockRequest);

        expect(result).toEqual({
          success: true,
          message: "获取朋友列表成功",
          data: {
            friends: mockFriendsList.friends,
            pagination: {
              total: mockFriendsList.total,
              hasNext: mockFriendsList.hasNext,
              page: 1,
              limit: 20,
            },
          },
        });
        expect(usersService.getFriendsList).toHaveBeenCalledWith(
          mockRequest.user.sub,
          1,
          20,
        );
      });

      it("should get friends list with custom pagination", async () => {
        (usersService.getFriendsList as jest.Mock).mockResolvedValue(
          mockFriendsList,
        );

        const result = await controller.getFriendsList(mockRequest, "2", "10");

        expect(result).toEqual({
          success: true,
          message: "获取朋友列表成功",
          data: {
            friends: mockFriendsList.friends,
            pagination: {
              total: mockFriendsList.total,
              hasNext: mockFriendsList.hasNext,
              page: 2,
              limit: 10,
            },
          },
        });
        expect(usersService.getFriendsList).toHaveBeenCalledWith(
          mockRequest.user.sub,
          2,
          10,
        );
      });
    });

    describe("checkFriendStatus", () => {
      it("should return friend status when users are friends", async () => {
        const targetId = "target-user-id";
        (usersService.isFriend as jest.Mock).mockResolvedValue(true);

        const result = await controller.checkFriendStatus(
          targetId,
          mockRequest,
        );

        expect(result).toEqual({
          success: true,
          data: {
            isFriend: true,
            canAddFriend: false,
          },
        });
        expect(usersService.isFriend).toHaveBeenCalledWith(
          mockRequest.user.sub,
          targetId,
        );
      });

      it("should return friend status when users are not friends", async () => {
        const targetId = "target-user-id";
        (usersService.isFriend as jest.Mock).mockResolvedValue(false);

        const result = await controller.checkFriendStatus(
          targetId,
          mockRequest,
        );

        expect(result).toEqual({
          success: true,
          data: {
            isFriend: false,
            canAddFriend: true,
          },
        });
      });

      it("should not allow adding self as friend", async () => {
        (usersService.isFriend as jest.Mock).mockResolvedValue(false);

        const result = await controller.checkFriendStatus(
          mockRequest.user.sub,
          mockRequest,
        );

        expect(result).toEqual({
          success: true,
          data: {
            isFriend: false,
            canAddFriend: false,
          },
        });
      });
    });
  });

  describe("deleteAccount", () => {
    it("should delete account successfully", async () => {
      (usersService.deleteUser as jest.Mock).mockResolvedValue(undefined);

      const result = await controller.deleteAccount(mockRequest);

      expect(result).toEqual({
        success: true,
        message: "账户删除成功",
      });
      expect(usersService.deleteUser).toHaveBeenCalledWith(
        mockRequest.user.sub,
      );
    });

    it("should handle service errors", async () => {
      (usersService.deleteUser as jest.Mock).mockRejectedValue(
        new Error("删除失败"),
      );

      await expect(controller.deleteAccount(mockRequest)).rejects.toThrow(
        "删除失败",
      );
    });
  });

  describe("error handling", () => {
    it("should propagate service errors correctly", async () => {
      (usersService.getUserProfile as jest.Mock).mockRejectedValue(
        new Error("用户不存在"),
      );

      await expect(
        controller.getCurrentUserProfile(mockRequest),
      ).rejects.toThrow("用户不存在");
    });

    it("should handle undefined request objects gracefully", async () => {
      (usersService.searchUsers as jest.Mock).mockResolvedValue(
        mockSearchResults,
      );

      const result = await controller.searchUsers("test");

      expect(result.success).toBe(true);
    });
  });

  describe("input validation", () => {
    it("should validate UUID format in path parameters", async () => {
      // 这个测试通过ParseUUIDPipe在运行时进行验证
      // 这里我们测试正常的UUID格式
      const validUUID = "f47ac10b-58cc-4372-a567-0e02b2c3d479";
      (usersService.getUserProfile as jest.Mock).mockResolvedValue(
        mockUserProfile,
      );

      const result = await controller.getUserProfile(validUUID, mockRequest);

      expect(result).toEqual(mockUserProfile);
      expect(usersService.getUserProfile).toHaveBeenCalledWith(
        validUUID,
        mockRequest.user.sub,
      );
    });

    it("should handle pagination parameter conversion", async () => {
      (usersService.getFriendsList as jest.Mock).mockResolvedValue(
        mockFriendsList,
      );

      await controller.getFriendsList(mockRequest, "invalid", "invalid");

      // parseInt('invalid') returns NaN, 所以应该使用默认值
      expect(usersService.getFriendsList).toHaveBeenCalledWith(
        mockRequest.user.sub,
        NaN,
        NaN,
      );
    });
  });

  describe("API response format consistency", () => {
    it("should return consistent success response format", async () => {
      (usersService.updateProfile as jest.Mock).mockResolvedValue(mockUser);

      const result = await controller.updateProfile(mockRequest, {
        nickname: "test",
      });

      expect(result).toHaveProperty("success", true);
      expect(result).toHaveProperty("message");
      expect(result).toHaveProperty("data");
    });

    it("should return consistent error response format for validation", async () => {
      const result = await controller.searchUsers("");

      expect(result).toHaveProperty("success", false);
      expect(result).toHaveProperty("message");
      expect(result).toHaveProperty("data");
    });
  });

  it("should be defined", () => {
    expect(controller).toBeDefined();
  });
});
