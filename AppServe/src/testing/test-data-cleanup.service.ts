/**
 * 企业级测试数据清理服务
 *
 * 功能：
 * - E2E和集成测试后的数据清理
 * - 云端RDS数据库安全清理
 * - OSS测试文件清理
 * - Redis测试缓存清理
 *
 * 安全措施：
 * - 强制测试环境检查
 * - 白名单数据库验证
 * - 清理操作日志记录
 */

import { Injectable, Logger } from "@nestjs/common";
import { InjectDataSource } from "@nestjs/typeorm";
import type { EntityManager } from "typeorm";
import { DataSource } from "typeorm";
import { ConfigService } from "@nestjs/config";

@Injectable()
export class TestDataCleanupService {
  private readonly logger = new Logger(TestDataCleanupService.name);

  // 安全白名单：只允许清理测试数据库
  private readonly SAFE_TEST_DATABASES = [
    "ygs-dev-integration-test",
    "ygs-e2e-test",
  ];

  constructor(
    @InjectDataSource()
    private readonly dataSource: DataSource,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 安全检查：确保当前环境为测试环境
   */
  private validateTestEnvironment(): void {
    const nodeEnv = this.configService.get<string>("NODE_ENV");
    const dbName = this.configService.get<string>("DB_NAME");

    // 检查环境变量
    if (!nodeEnv || !nodeEnv.includes("test")) {
      throw new Error("❌ 安全检查失败：非测试环境禁止执行数据清理");
    }

    // 检查数据库名称
    if (!this.SAFE_TEST_DATABASES.includes(dbName)) {
      throw new Error(`❌ 安全检查失败：数据库 ${dbName} 不在安全清理白名单中`);
    }

    this.logger.log(`✅ 安全检查通过：环境=${nodeEnv}, 数据库=${dbName}`);
  }

  /**
   * 清理所有测试数据
   */
  async cleanupAllTestData(): Promise<void> {
    this.validateTestEnvironment();

    const startTime = Date.now();
    this.logger.log("🧹 开始企业级测试数据清理...");

    try {
      await this.dataSource.transaction(async (manager) => {
        // 按依赖关系顺序清理表
        const cleanupTables = [
          // 关联表优先清理
          "character_lightings",
          "light_requests",
          "story_characters",
          "friend_group_members",
          "user_follows",
          "user_relationships",
          "comment_likes",
          "story_references",
          "story_shares",
          "share_access_logs",
          "bookmarks",
          "notifications",
          "reports",

          // 主数据表
          "comments",
          "story_chapters",
          "characters",
          "stories",
          "friend_groups",
          "themes",
          "timeline_events",
          "reference_collections",
          "shares",
          "ai_usage_logs",
          "refresh_tokens",
          "users",
        ];

        let totalCleaned = 0;
        for (const table of cleanupTables) {
          try {
            const result = await manager.query(`DELETE FROM "${table}"`);
            const cleanedCount = result[1] || 0;
            totalCleaned += cleanedCount;

            if (cleanedCount > 0) {
              this.logger.log(`  ✅ ${table}: 清理 ${cleanedCount} 条记录`);
            }
          } catch (error) {
            // 某些表可能不存在，继续清理其他表
            this.logger.warn(`  ⚠️ ${table}: ${error.message}`);
          }
        }

        // 重置序列
        await this.resetSequences(manager);

        this.logger.log(`🎉 数据清理完成，共清理 ${totalCleaned} 条记录`);
      });
    } catch (error) {
      this.logger.error(`❌ 数据清理失败: ${error.message}`);
      throw error;
    }

    const duration = Date.now() - startTime;
    this.logger.log(`⏱️ 清理耗时: ${duration}ms`);
  }

  /**
   * 重置数据库序列
   */
  private async resetSequences(manager: EntityManager): Promise<void> {
    try {
      // 获取所有序列
      const sequences = await manager.query(`
        SELECT schemaname, sequencename 
        FROM pg_sequences 
        WHERE schemaname = 'public'
      `);

      for (const seq of sequences) {
        await manager.query(
          `ALTER SEQUENCE "${seq.sequencename}" RESTART WITH 1`,
        );
      }

      this.logger.log(`  🔄 重置 ${sequences.length} 个序列`);
    } catch (error) {
      this.logger.warn(`⚠️ 序列重置失败: ${error.message}`);
    }
  }

  /**
   * 清理特定用户的测试数据
   */
  async cleanupUserTestData(userId: string): Promise<void> {
    this.validateTestEnvironment();

    this.logger.log(`🧹 清理用户 ${userId} 的测试数据...`);

    await this.dataSource.transaction(async (manager) => {
      // 清理用户相关数据
      await manager.query(
        "DELETE FROM character_lightings WHERE requester_user_id = $1 OR creator_user_id = $1",
        [userId],
      );
      await manager.query(
        "DELETE FROM light_requests WHERE requester_id = $1",
        [userId],
      );
      await manager.query("DELETE FROM stories WHERE user_id = $1", [userId]);
      await manager.query(
        "DELETE FROM user_follows WHERE follower_id = $1 OR following_id = $1",
        [userId],
      );
      await manager.query("DELETE FROM friend_groups WHERE owner_id = $1", [
        userId,
      ]);
      await manager.query("DELETE FROM users WHERE id = $1", [userId]);
    });

    this.logger.log(`✅ 用户 ${userId} 数据清理完成`);
  }

  /**
   * 获取当前测试数据统计
   */
  async getTestDataStats(): Promise<Record<string, number>> {
    this.validateTestEnvironment();

    const tables = [
      "users",
      "stories",
      "characters",
      "light_requests",
      "character_lightings",
    ];
    const stats: Record<string, number> = {};

    for (const table of tables) {
      try {
        const result = await this.dataSource.query(
          `SELECT COUNT(*) as count FROM "${table}"`,
        );
        stats[table] = parseInt(result[0].count);
      } catch (error) {
        stats[table] = 0;
      }
    }

    return stats;
  }

  /**
   * 验证数据库是否已清理干净
   */
  async verifyCleanup(): Promise<boolean> {
    const stats = await this.getTestDataStats();
    const totalRecords = Object.values(stats).reduce(
      (sum, count) => sum + count,
      0,
    );

    if (totalRecords === 0) {
      this.logger.log("✅ 数据库清理验证通过：所有测试数据已清理");
      return true;
    } else {
      this.logger.warn(`⚠️ 数据库清理验证失败：仍有 ${totalRecords} 条记录`);
      return false;
    }
  }
}
