import {
  IsEnum,
  IsOptional,
  IsString,
  IsInt,
  IsDateString,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>ength,
} from "class-validator";
import { Type } from "class-transformer";
import { ShareType } from "../entities/share.entity";

/**
 * 创建分享DTO
 */
export class CreateShareDto {
  @IsEnum(ShareType, {
    message: "分享类型必须是 public 或 limited",
  })
  shareType: ShareType;

  @IsOptional()
  @IsString({
    message: "访问密码必须是字符串",
  })
  @MaxLength(20, {
    message: "访问密码不能超过20个字符",
  })
  password?: string;

  @IsOptional()
  @IsInt({
    message: "访问次数限制必须是整数",
  })
  @Type(() => Number)
  @Min(-1, {
    message: "访问次数限制最小值为-1（无限制）",
  })
  @Max(1000000, {
    message: "访问次数限制最大值为1000000",
  })
  accessLimit?: number = -1;

  @IsOptional()
  @IsDateString(
    {},
    {
      message: "过期时间必须是有效的日期格式",
    },
  )
  expiresAt?: string;

  @IsOptional()
  @IsString({
    message: "分享描述必须是字符串",
  })
  @MaxLength(500, {
    message: "分享描述不能超过500个字符",
  })
  description?: string;

  @IsOptional()
  @Type(() => Object)
  metadata?: Record<string, unknown>;
}
