/**
 * 企业级故事种子数据
 *
 * 基于 YGS v1.0.0 六层权限体系设计
 * 涵盖所有权限级别和业务场景
 */

import { USER_ROLES } from "./enhanced-user.seeder";

export interface EnhancedStorySeedData {
  authorUsername: string;
  title: string;
  content: string;
  coverImageUrl?: string;
  status: "DRAFT" | "PUBLISHED" | "ARCHIVED";
  permissionLevel: "private" | "friends" | "public" | "characters_only";
  viewCount: number;
  likeCount: number;
  commentCount: number;
  storyDate?: string;
  location?: string;
  images?: string[];
  allowComments: boolean;
  allowLikes: boolean;
  allowSharing: boolean;
  aiSafetyScore?: number;
  tags: string[];
  theme: string;
}

export const ENHANCED_STORY_SEED_DATA: EnhancedStorySeedData[] = [
  // ========== 张明远的故事系列 ==========
  // 1. 公开故事 - 大学回忆
  {
    authorUsername: USER_ROLES.ZHANG_MINGYUAN,
    title: "那年夏天，我们约定十年后再聚",
    content: `时间回到2015年6月，那是我们大学的最后一个夏天。

宿舍里，我们四个人围坐在一起，桌上摆着几瓶啤酒和一些花生米。窗外的梧桐树郁郁葱葱，知了声此起彼伏。

"十年后，我们还会是朋友吗？"李明突然问道，眼神里有些迷茫。

王强拍拍他的肩膀："废话！咱们可是穿一条裤子的兄弟！"

赵华推了推眼镜："我提议，十年后的今天，不管大家在哪里，都要聚一次。"

我举起酒瓶："好！就这么定了！2025年6月15日，不见不散！"

四个酒瓶碰在一起，发出清脆的声响。那一刻，我们都相信这个约定会实现。

现在已经过去了十年，李明在深圳做了程序员，王强在北京创业，赵华留在老家当了老师，而我在上海从事金融工作。

虽然平时联系不多，但那个夏天的约定，一直藏在心底最柔软的地方。

距离约定的日子还有几个月，我已经开始期待了。`,
    coverImageUrl:
      "https://ygs-oss-dev.oss-cn-hangzhou.aliyuncs.com/stories/university_memory.jpg",
    status: "PUBLISHED",
    permissionLevel: "public",
    viewCount: 1580,
    likeCount: 236,
    commentCount: 45,
    storyDate: "2015-06-15",
    location: "某大学男生宿舍",
    images: [
      "https://ygs-oss-dev.oss-cn-hangzhou.aliyuncs.com/stories/dorm_photo.jpg",
      "https://ygs-oss-dev.oss-cn-hangzhou.aliyuncs.com/stories/graduation_photo.jpg",
    ],
    allowComments: true,
    allowLikes: true,
    allowSharing: true,
    aiSafetyScore: 0.95,
    tags: ["大学", "友情", "约定", "青春", "回忆"],
    theme: "友情",
  },

  // 2. 好友可见 - 创业经历
  {
    authorUsername: USER_ROLES.ZHANG_MINGYUAN,
    title: "创业路上，感谢有你们",
    content: `2020年初，我辞去了稳定的工作，决定创业。

那段时间真的很难，产品一次次被否定，资金链几度断裂，团队成员来了又走。最难的时候，我一个人坐在空荡荡的办公室里，看着窗外的万家灯火，第一次怀疑自己的选择。

就在我准备放弃的时候，大学室友王强给我打来电话："听说你在创业？需要帮忙吗？"

接下来的日子里，王强介绍了几个靠谱的投资人，李明帮我优化了技术架构，赵华虽然远在老家，也通过各种方式给予支持。

最让我感动的是，当我资金最紧张的时候，他们三个竟然偷偷凑了20万，说是"天使投资"，还开玩笑说要占股份。

现在公司已经步入正轨，虽然还不算成功，但至少活下来了。每当深夜加班的时候，我都会想起他们的支持。

有些友情，经得起时间的考验，也扛得住现实的压力。`,
    status: "PUBLISHED",
    permissionLevel: "friends",
    viewCount: 324,
    likeCount: 89,
    commentCount: 23,
    storyDate: "2020-03-15",
    location: "上海",
    allowComments: true,
    allowLikes: true,
    allowSharing: false,
    aiSafetyScore: 0.98,
    tags: ["创业", "友情", "坚持", "感恩"],
    theme: "奋斗",
  },

  // 3. 仅人物可见 - 最私密的故事
  {
    authorUsername: USER_ROLES.ZHANG_MINGYUAN,
    title: "李明，还记得那个雨夜吗",
    content: `这个故事只想让你看到。

2014年11月的一个雨夜，我失恋了。整个人像是被掏空了一样，一个人坐在宿舍楼顶，雨水混着眼泪，分不清哪是哪。

你不知道从哪里找到我，什么都没说，就那样陪我坐在雨里。

后来你说："兄弟，天还没塌，咱们还有梦想要实现呢。"

那一夜，我们聊了很多，关于理想，关于未来，关于如何成为更好的自己。你说，失恋不可怕，可怕的是失去前进的勇气。

第二天，我们都感冒了，但心情却好了很多。

这件事我从来没有对别人说过，连王强和赵华都不知道。但我一直记得，在我最脆弱的时候，是你陪我度过了最难的一夜。

谢谢你，我的兄弟。`,
    status: "PUBLISHED",
    permissionLevel: "characters_only",
    viewCount: 12,
    likeCount: 3,
    commentCount: 1,
    storyDate: "2014-11-20",
    location: "大学宿舍楼顶",
    allowComments: true,
    allowLikes: true,
    allowSharing: false,
    aiSafetyScore: 0.99,
    tags: ["友情", "陪伴", "感动", "秘密"],
    theme: "友情",
  },

  // ========== 王建国的故事系列 ==========
  // 4. 公开故事 - 师生情
  {
    authorUsername: USER_ROLES.WANG_JIANGUO,
    title: "小雨，你现在过得还好吗",
    content: `翻看旧照片时，看到了2010年的毕业照，你就站在第二排左边第三个位置，笑得很灿烂。

你是我教过的最特别的学生。不是因为成绩最好，而是因为你的坚韧。

还记得你家里出事的那段时间吗？父亲生病住院，母亲需要照顾，你一边打工一边学习，成绩却从来没有落下。

有一次批改作业到很晚，发现你的作业本上有泪痕。第二天我找你谈话，你却笑着说："老师，我没事，我能挺过去的。"

高考那天，我特意早起为你们送考。看着你自信地走进考场，我知道你一定可以的。

后来你考上了理想的大学，毕业后成为了一名医生。前年春节，你特意回来看我，说要感谢我当年的鼓励。

其实，该说谢谢的是我。是你让我明白，做老师最大的幸福，就是看着学生成长为独当一面的大人。

小雨，希望你在远方一切都好。`,
    coverImageUrl:
      "https://ygs-oss-dev.oss-cn-hangzhou.aliyuncs.com/stories/graduation_2010.jpg",
    status: "PUBLISHED",
    permissionLevel: "public",
    viewCount: 2341,
    likeCount: 567,
    commentCount: 89,
    storyDate: "2010-06-08",
    location: "某中学",
    images: [
      "https://ygs-oss-dev.oss-cn-hangzhou.aliyuncs.com/stories/classroom.jpg",
    ],
    allowComments: true,
    allowLikes: true,
    allowSharing: true,
    aiSafetyScore: 0.97,
    tags: ["师生", "成长", "坚强", "感动"],
    theme: "成长",
  },

  // 5. 关注者可见 - 教育反思
  {
    authorUsername: USER_ROLES.WANG_JIANGUO,
    title: "三十年教书，我最后悔的一件事",
    content: `做了三十年老师，桃李满天下，但心里一直有个结。

那是2005年，班上有个叫小军的男孩，成绩不好，还经常调皮捣蛋。我年轻气盛，经常当众批评他，有一次甚至说他"朽木不可雕"。

期末的时候，小军转学了。临走前，他妈妈来学校，哭着说孩子在家经常做噩梦，梦里都是被老师骂的场景。

那一刻，我如遭雷击。我意识到，我的言语可能毁掉了一个孩子的自信。

后来我改变了教育方式，学会了尊重每一个孩子的个性。但小军的事，始终是我心里的一根刺。

前年教师节，收到一个陌生来电。电话那头，一个男人说："王老师，我是小军。我现在开了一家汽修厂，生活很好。当年的事，我早就不记恨您了。"

挂掉电话，我哭了很久。

这件事教会我：教育的本质不是灌输，而是点燃。每个孩子都是独特的火种，需要的只是合适的方式。`,
    status: "PUBLISHED",
    permissionLevel: "friends",
    viewCount: 856,
    likeCount: 234,
    commentCount: 67,
    storyDate: "2005-12-30",
    location: "某中学办公室",
    allowComments: true,
    allowLikes: true,
    allowSharing: true,
    aiSafetyScore: 0.93,
    tags: ["教育", "反思", "成长", "救赎"],
    theme: "成长",
  },

  // ========== 吴私密的故事系列 ==========
  // 6. 私密故事 - 个人日记
  {
    authorUsername: USER_ROLES.WU_SIMI,
    title: "写给十年后的自己",
    content: `今天是2025年7月27日，我35岁了。

十年前的今天，25岁的我刚刚大学毕业，满怀憧憬地踏入社会。那时的我，有很多梦想：环游世界、写一本书、找到真爱、事业有成...

现在回头看，有些梦想实现了，有些还在路上，有些已经改变了。

我没有环游世界，但去了十几个国家；没有写成一本书，但一直在坚持写作；找到了爱情，虽然平淡但很幸福；事业算不上大成，但也小有成就。

最重要的是，我学会了接纳不完美的自己。不再追求别人眼中的成功，而是寻找内心的平静。

如果可以对25岁的自己说一句话，我想说：别着急，该来的都会来。人生不是百米冲刺，而是一场马拉松。

写给十年后45岁的自己：希望你依然保持好奇心，依然相信美好，依然在自己选择的路上坚定前行。`,
    status: "PUBLISHED",
    permissionLevel: "private",
    viewCount: 1,
    likeCount: 0,
    commentCount: 0,
    storyDate: "2025-07-27",
    location: "家中书房",
    allowComments: false,
    allowLikes: false,
    allowSharing: false,
    aiSafetyScore: 1.0,
    tags: ["日记", "成长", "反思", "未来"],
    theme: "成长",
  },

  // 7. 分组可见 - 家庭故事
  {
    authorUsername: USER_ROLES.WU_SIMI,
    title: "妈妈，对不起，我撒谎了",
    content: `这个故事只想让家人看到。

上个月你问我为什么这么晚回家，我说是加班。其实不是。

我去医院了。体检报告出来，有个指标不太好，医生建议复查。那一刻，我突然很害怕，害怕让你担心，害怕看到你焦虑的样子。

所以我选择了隐瞒。一个人去做各种检查，一个人在医院走廊里等结果，一个人消化所有的恐惧和不安。

还好，复查结果显示只是虚惊一场，可能是最近压力太大导致的。医生说注意休息，定期复查就好。

我知道瞒着你是不对的。你常说，一家人最重要的是坦诚。可是妈妈，我已经35岁了，还是想在你面前表现得很坚强，不想让你为我操心。

写下这些，是想告诉你，我很好，请不要担心。也想告诉自己，下次遇到困难，要学会和家人分享，而不是独自承担。

我爱你，妈妈。`,
    status: "PUBLISHED",
    permissionLevel: "friends",
    viewCount: 5,
    likeCount: 3,
    commentCount: 2,
    storyDate: "2025-06-15",
    location: "医院",
    allowComments: true,
    allowLikes: true,
    allowSharing: false,
    aiSafetyScore: 0.96,
    tags: ["家庭", "亲情", "坦白", "成长"],
    theme: "亲情",
  },

  // ========== 其他用户的故事 ==========
  // 8. 草稿状态 - 未发布
  {
    authorUsername: USER_ROLES.ZHAO_XIAOFANG,
    title: "今天遇到一只流浪猫",
    content: `下班路上遇到一只橘猫，看起来饿了很久。

买了火腿肠喂它，它吃得很急，一边吃一边警惕地看着四周。

想起小时候家里养的猫，也是橘色的，叫小橘。那时候我还小，每天放学第一件事就是找小橘玩。

后来搬家了，小橘送给了邻居。现在想想，还挺想念它的。

（这个故事还没写完，明天继续...）`,
    status: "DRAFT",
    permissionLevel: "public",
    viewCount: 0,
    likeCount: 0,
    commentCount: 0,
    storyDate: "2025-07-26",
    location: "下班路上",
    allowComments: true,
    allowLikes: true,
    allowSharing: true,
    tags: ["日常", "宠物", "回忆"],
    theme: "日常",
  },

  // 9. 归档状态 - 过去的故事
  {
    authorUsername: USER_ROLES.LI_XIAOHONG,
    title: "那些年，我们一起追过的剧",
    content: `大学时期，宿舍里最快乐的时光就是一起追剧。

还记得《来自星星的你》播出的时候，我们四个女生挤在一张床上，抱着电脑，备好零食，等着更新。

每次看到感人的情节，宿舍里就响起此起彼伏的抽泣声。看到搞笑的地方，又会笑得前仰后合。

现在各自工作了，很难再有这样的时光。偶尔在群里聊起当年追过的剧，还是会会心一笑。

青春真好，有人陪你一起哭，一起笑，一起做梦。`,
    status: "ARCHIVED",
    permissionLevel: "public",
    viewCount: 567,
    likeCount: 123,
    commentCount: 34,
    storyDate: "2014-02-20",
    location: "大学宿舍",
    allowComments: false, // 归档后关闭评论
    allowLikes: true,
    allowSharing: true,
    aiSafetyScore: 0.98,
    tags: ["大学", "友情", "青春", "追剧"],
    theme: "友情",
  },
];

// 导出故事标题映射，方便人物数据引用
export const STORY_TITLES = {
  UNIVERSITY_PROMISE: "那年夏天，我们约定十年后再聚",
  STARTUP_JOURNEY: "创业路上，感谢有你们",
  RAINY_NIGHT: "李明，还记得那个雨夜吗",
  STUDENT_XIAOYU: "小雨，你现在过得还好吗",
  TEACHING_REGRET: "三十年教书，我最后悔的一件事",
  LETTER_TO_FUTURE: "写给十年后的自己",
  SORRY_MOM: "妈妈，对不起，我撒谎了",
  STRAY_CAT: "今天遇到一只流浪猫",
  DRAMA_MEMORIES: "那些年，我们一起追过的剧",
};
