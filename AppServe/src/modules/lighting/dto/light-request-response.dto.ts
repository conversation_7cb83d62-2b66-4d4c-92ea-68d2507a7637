import { ApiProperty } from "@nestjs/swagger";
import type { LightRequest } from "../../characters/entities/light-request.entity";

export class LightRequestResponseDto {
  @ApiProperty({
    description: "申请ID",
    example: "uuid-string",
  })
  id: string;

  @ApiProperty({
    description: "故事ID",
    example: "uuid-string",
  })
  storyId: string;

  @ApiProperty({
    description: "故事标题",
    example: "我们的青春回忆",
  })
  storyTitle: string;

  @ApiProperty({
    description: "人物ID",
    example: "uuid-string",
  })
  characterId: string;

  @ApiProperty({
    description: "人物姓名",
    example: "张三",
  })
  characterName: string;

  @ApiProperty({
    description: "申请者ID",
    example: "uuid-string",
  })
  requesterId: string;

  @ApiProperty({
    description: "申请者昵称",
    example: "小明",
  })
  requesterNickname: string;

  @ApiProperty({
    description: "申请状态",
    enum: ["pending", "confirmed", "rejected"],
    example: "pending",
  })
  status: string;

  @ApiProperty({
    description: "申请理由",
    example: "这就是我，我想点亮这个人物",
  })
  reason: string;

  @ApiProperty({
    description: "申请时间",
    example: "2025-07-08T12:00:00.000Z",
  })
  createdAt: Date;

  @ApiProperty({
    description: "处理时间",
    example: "2025-07-08T15:30:00.000Z",
    required: false,
  })
  processedAt?: Date;

  @ApiProperty({
    description: "是否自动通过",
    example: false,
    required: false,
  })
  isAutoApproved?: boolean;

  @ApiProperty({
    description: "响应消息",
    example: "申请已提交，等待审批",
    required: false,
  })
  message?: string;

  @ApiProperty({
    description: "脱敏手机号（用户选择发送时才有值）",
    example: "138****8000",
    required: false,
  })
  phoneVerification?: string | null;

  @ApiProperty({
    description: "用户是否选择发送手机号",
    example: true,
    required: false,
  })
  hasPhoneVerification?: boolean;

  constructor(lightRequest: LightRequest) {
    this.id = lightRequest.id;
    this.storyId = lightRequest.storyId;
    this.storyTitle = lightRequest.story?.title || "";
    this.characterId = lightRequest.characterId;
    this.characterName = lightRequest.character?.name || "";
    this.requesterId = lightRequest.requesterId;
    this.requesterNickname =
      lightRequest.requester?.nickname ||
      lightRequest.requester?.username ||
      "";
    this.status = lightRequest.status;
    this.reason = lightRequest.message || "";
    this.createdAt = lightRequest.createdAt;
    this.processedAt = lightRequest.processedAt;
    this.phoneVerification = lightRequest.phoneVerification;
    this.hasPhoneVerification = lightRequest.hasPhoneVerification;
  }
}
