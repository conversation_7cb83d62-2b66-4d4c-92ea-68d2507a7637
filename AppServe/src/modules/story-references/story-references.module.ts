import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { StoryReferencesController } from "./story-references.controller";
import { StoryReferencesService } from "./story-references.service";
import { StoryReference } from "./entities/story-reference.entity";
import { ReferenceCollection } from "./entities/reference-collection.entity";
import { User } from "../users/entities/user.entity";
import { Story } from "../stories/entities/story.entity";
import { AuthModule } from "../auth/auth.module";

@Module({
  imports: [
    TypeOrmModule.forFeature([
      StoryReference,
      ReferenceCollection,
      User,
      Story,
    ]),
    AuthModule,
  ],
  controllers: [StoryReferencesController],
  providers: [StoryReferencesService],
  exports: [StoryReferencesService],
})
export class StoryReferencesModule {}
