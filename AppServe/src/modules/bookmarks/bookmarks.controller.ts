import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Req,
  HttpStatus,
} from "@nestjs/common";
import type { Request } from "express";
import { BookmarksService } from "./bookmarks.service";
import {
  CreateBookmarkDto,
  UpdateBookmarkCategoryDto,
  BookmarkQueryDto,
} from "./dto";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import type { ApiResponseDto } from "../../common/dto/api-response.dto";
import type { PaginatedResponseDto } from "../../common/dto/paginated-response.dto";
import type { BookmarkResponseDto, BookmarkStatsResponseDto } from "./dto";

/**
 * 收藏控制器
 * 提供故事收藏相关的API接口
 */
@Controller("bookmarks")
@UseGuards(JwtAuthGuard)
export class BookmarksController {
  constructor(private readonly bookmarksService: BookmarksService) {}

  /**
   * 创建收藏
   * POST /bookmarks
   */
  @Post()
  async createBookmark(
    @Body() createBookmarkDto: CreateBookmarkDto,
    @Req() req: Request & { user: { id: string } },
  ): Promise<ApiResponseDto<BookmarkResponseDto>> {
    const bookmark = await this.bookmarksService.createBookmark(
      createBookmarkDto,
      req.user.id,
    );

    return {
      success: true,
      statusCode: HttpStatus.CREATED,
      message: "收藏创建成功",
      data: bookmark,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 获取收藏列表
   * GET /bookmarks
   */
  @Get()
  async getUserBookmarks(
    @Query() query: BookmarkQueryDto,
    @Req() req: Request & { user: { id: string } },
  ): Promise<ApiResponseDto<PaginatedResponseDto<BookmarkResponseDto>>> {
    const bookmarks = await this.bookmarksService.getUserBookmarks(
      req.user.id,
      query,
    );

    return {
      success: true,
      statusCode: HttpStatus.OK,
      message: "收藏列表获取成功",
      data: bookmarks,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 获取收藏详情
   * GET /bookmarks/:id
   */
  @Get(":id")
  async getBookmarkDetail(
    @Param("id") bookmarkId: string,
    @Req() req: Request & { user: { id: string } },
  ): Promise<ApiResponseDto<BookmarkResponseDto>> {
    const bookmark = await this.bookmarksService.getBookmarkDetail(
      bookmarkId,
      req.user.id,
    );

    return {
      success: true,
      statusCode: HttpStatus.OK,
      message: "收藏详情获取成功",
      data: bookmark,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 更新收藏分类
   * PUT /bookmarks/:id/category
   */
  @Put(":id/category")
  async updateBookmarkCategory(
    @Param("id") bookmarkId: string,
    @Body() updateDto: UpdateBookmarkCategoryDto,
    @Req() req: Request & { user: { id: string } },
  ): Promise<ApiResponseDto<BookmarkResponseDto>> {
    const bookmark = await this.bookmarksService.updateBookmarkCategory(
      bookmarkId,
      updateDto,
      req.user.id,
    );

    return {
      success: true,
      statusCode: HttpStatus.OK,
      message: "收藏分类更新成功",
      data: bookmark,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 删除收藏
   * DELETE /bookmarks/:id
   */
  @Delete(":id")
  async deleteBookmark(
    @Param("id") bookmarkId: string,
    @Req() req: Request & { user: { id: string } },
  ): Promise<ApiResponseDto<null>> {
    await this.bookmarksService.deleteBookmark(bookmarkId, req.user.id);

    return {
      success: true,
      statusCode: HttpStatus.OK,
      message: "收藏删除成功",
      data: null,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 获取收藏统计
   * GET /bookmarks/stats
   */
  @Get("/stats")
  async getBookmarkStats(
    @Req() req: Request & { user: { id: string } },
  ): Promise<ApiResponseDto<BookmarkStatsResponseDto>> {
    const stats = await this.bookmarksService.getBookmarkStats(req.user.id);

    return {
      success: true,
      statusCode: HttpStatus.OK,
      message: "收藏统计获取成功",
      data: stats,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 检查故事收藏状态
   * GET /bookmarks/check/:storyId
   */
  @Get("/check/:storyId")
  async checkBookmarkStatus(
    @Param("storyId") storyId: string,
    @Req() req: Request & { user: { id: string } },
  ): Promise<ApiResponseDto<{ isBookmarked: boolean }>> {
    const isBookmarked = await this.bookmarksService.isStoryBookmarked(
      storyId,
      req.user.id,
    );

    return {
      success: true,
      statusCode: HttpStatus.OK,
      message: "收藏状态检查完成",
      data: { isBookmarked },
      timestamp: new Date().toISOString(),
    };
  }
}
