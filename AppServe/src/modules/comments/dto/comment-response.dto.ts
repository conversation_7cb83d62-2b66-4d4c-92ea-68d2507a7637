import type {
  CommentTargetType,
  CommentStatus,
} from "../entities/comment.entity";

/**
 * 评论基础响应DTO
 */
export class CommentResponseDto {
  id: string;
  targetType: CommentTargetType;
  targetId: string;
  content: string;
  authorId: string;
  authorName: string;
  authorAvatar?: string;
  status: CommentStatus;
  likeCount: number;
  replyCount: number;
  rootCommentId?: string;
  parentCommentId?: string;
  depth: number;
  isLiked: boolean;
  metadata?: Record<string, unknown>;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 评论详情响应DTO（包含回复）
 */
export class CommentDetailResponseDto extends CommentResponseDto {
  replies: CommentResponseDto[];
  parentComment?: {
    id: string;
    authorName: string;
    content: string;
  };
}

/**
 * 评论点赞响应DTO
 */
export class CommentLikeResponseDto {
  commentId: string;
  isLiked: boolean;
  likeCount: number;
}

/**
 * 评论统计响应DTO
 */
export class CommentStatsResponseDto {
  totalComments: number;
  totalLikes: number;
  totalReplies: number;
  activeComments: number;
}
