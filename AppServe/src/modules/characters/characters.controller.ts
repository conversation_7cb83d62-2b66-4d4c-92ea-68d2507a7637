import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  HttpCode,
  ParseUUIDPipe,
  BadRequestException,
  ForbiddenException,
  UseInterceptors,
  UploadedFile,
} from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse as SwaggerApiResponse,
  ApiBearerAuth,
  ApiParam,
} from "@nestjs/swagger";
import { FileInterceptor } from "@nestjs/platform-express";
import { MultipartFile } from "@fastify/multipart";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { CharactersService } from "./characters.service";
import type { CharacterStatisticsDto } from "./dto";
import {
  CreateCharacterDto,
  UpdateCharacterDto,
  CharacterResponseDto,
  CharacterQueryDto,
  ValidateCharacterNameDto,
} from "./dto";
import type { PaginatedResponse } from "../../common/dto/paginated-response.dto";
import { ApiResponse } from "../../common/dto/api-response.dto";
import { AuthRequest } from "../../common/types/request.types";

/**
 * 人物控制器
 * 处理人物相关的HTTP请求
 */
@ApiTags("人物管理")
@Controller("characters")
@UseGuards(JwtAuthGuard)
export class CharactersController {
  constructor(private readonly charactersService: CharactersService) {}

  /**
   * 创建人物
   * POST /characters
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: "创建人物" })
  @SwaggerApiResponse({
    status: 201,
    description: "创建成功",
    type: ApiResponse,
  })
  @ApiBearerAuth()
  async createCharacter(
    @Request() req: AuthRequest,
    @Body() createCharacterDto: CreateCharacterDto,
  ): Promise<ApiResponse<CharacterResponseDto>> {
    try {
      const character = await this.charactersService.createCharacter(
        req.user.id,
        createCharacterDto,
      );
      const characterResponse = new CharacterResponseDto(character);

      return new ApiResponse(
        true,
        "人物创建成功",
        characterResponse,
        HttpStatus.CREATED,
      );
    } catch (error) {
      throw new BadRequestException(
        error instanceof Error ? error.message : "操作失败",
      );
    }
  }

  /**
   * 更新人物
   * PUT /characters/:id
   */
  @Put(":id")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "更新人物" })
  @ApiParam({ name: "id", description: "人物ID", type: String })
  @SwaggerApiResponse({
    status: 200,
    description: "更新成功",
    type: ApiResponse,
  })
  @ApiBearerAuth()
  async updateCharacter(
    @Request() req: AuthRequest,
    @Param("id", ParseUUIDPipe) id: string,
    @Body() updateCharacterDto: UpdateCharacterDto,
  ): Promise<ApiResponse<CharacterResponseDto>> {
    try {
      const character = await this.charactersService.updateCharacter(
        id,
        updateCharacterDto,
        req.user.id,
      );
      const characterResponse = new CharacterResponseDto(character);

      return new ApiResponse(
        true,
        "人物更新成功",
        characterResponse,
        HttpStatus.OK,
      );
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }
      throw new BadRequestException(
        error instanceof Error ? error.message : "操作失败",
      );
    }
  }

  /**
   * 删除人物
   * DELETE /characters/:id
   */
  @Delete(":id")
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: "删除人物" })
  @ApiParam({ name: "id", description: "人物ID", type: String })
  @SwaggerApiResponse({ status: 204, description: "删除成功" })
  @ApiBearerAuth()
  async deleteCharacter(
    @Request() req: AuthRequest,
    @Param("id", ParseUUIDPipe) id: string,
  ): Promise<void> {
    try {
      await this.charactersService.deleteCharacter(id, req.user.id);
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }
      throw new BadRequestException(
        error instanceof Error ? error.message : "操作失败",
      );
    }
  }

  /**
   * 获取人物详情
   * GET /characters/:id
   */
  @Get(":id")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "获取人物详情" })
  @ApiParam({ name: "id", description: "人物ID", type: String })
  @SwaggerApiResponse({
    status: 200,
    description: "获取成功",
    type: ApiResponse,
  })
  @ApiBearerAuth()
  async getCharacterById(
    @Request() req: AuthRequest,
    @Param("id", ParseUUIDPipe) id: string,
  ): Promise<ApiResponse<CharacterResponseDto>> {
    try {
      const character = await this.charactersService.findById(id);
      const characterResponse = new CharacterResponseDto(character);

      return new ApiResponse(
        true,
        "获取人物成功",
        characterResponse,
        HttpStatus.OK,
      );
    } catch (error) {
      throw new BadRequestException(
        error instanceof Error ? error.message : "操作失败",
      );
    }
  }

  /**
   * 获取人物列表
   * GET /characters
   */
  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "获取人物列表" })
  @SwaggerApiResponse({
    status: 200,
    description: "获取成功",
    type: ApiResponse,
  })
  @ApiBearerAuth()
  async getCharacters(
    @Request() req: AuthRequest,
    @Query() query: CharacterQueryDto,
  ): Promise<ApiResponse<PaginatedResponse<CharacterResponseDto>>> {
    try {
      let result: PaginatedResponse<CharacterResponseDto>;

      // 如果指定了创建者ID，获取该创建者的人物
      if (query.creatorId) {
        // 如果是查询自己的人物，返回所有状态
        if (query.creatorId === req.user.id) {
          result = await this.charactersService.getUserCharacters(
            req.user.id,
            query,
          );
        } else {
          // 查询其他用户的人物，只返回可访问的
          result = await this.charactersService.getAccessibleCharacters(
            { ...query, creatorId: query.creatorId },
            req.user.id,
          );
        }
      } else {
        // 获取可访问的人物列表
        result = await this.charactersService.getAccessibleCharacters(
          query,
          req.user.id,
        );
      }

      return new ApiResponse(true, "获取人物列表成功", result, HttpStatus.OK);
    } catch (error) {
      throw new BadRequestException(
        error instanceof Error ? error.message : "操作失败",
      );
    }
  }

  /**
   * 获取我的人物列表
   * GET /characters/my
   */
  @Get("my")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "获取我的人物列表" })
  @SwaggerApiResponse({
    status: 200,
    description: "获取成功",
    type: ApiResponse,
  })
  @ApiBearerAuth()
  async getMyCharacters(
    @Request() req: AuthRequest,
    @Query() query: CharacterQueryDto,
  ): Promise<ApiResponse<PaginatedResponse<CharacterResponseDto>>> {
    try {
      const result = await this.charactersService.getUserCharacters(
        req.user.id,
        query,
      );

      return new ApiResponse(
        true,
        "获取我的人物列表成功",
        result,
        HttpStatus.OK,
      );
    } catch (error) {
      throw new BadRequestException(
        error instanceof Error ? error.message : "操作失败",
      );
    }
  }

  /**
   * 搜索人物
   * GET /characters/search
   */
  @Get("search")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "搜索人物" })
  @SwaggerApiResponse({
    status: 200,
    description: "搜索成功",
    type: ApiResponse,
  })
  @ApiBearerAuth()
  async searchCharacters(
    @Request() req: AuthRequest,
    @Query() query: CharacterQueryDto,
  ): Promise<ApiResponse<PaginatedResponse<CharacterResponseDto>>> {
    try {
      const result = await this.charactersService.searchCharacters(
        query,
        req.user.id,
      );

      return new ApiResponse(true, "搜索人物成功", result, HttpStatus.OK);
    } catch (error) {
      throw new BadRequestException(
        error instanceof Error ? error.message : "操作失败",
      );
    }
  }

  /**
   * 获取热门人物
   * GET /characters/popular
   */
  @Get("popular")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "获取热门人物" })
  @SwaggerApiResponse({
    status: 200,
    description: "获取成功",
    type: ApiResponse,
  })
  @ApiBearerAuth()
  async getPopularCharacters(
    @Request() req: AuthRequest,
    @Query() query: CharacterQueryDto,
  ): Promise<ApiResponse<PaginatedResponse<CharacterResponseDto>>> {
    try {
      const result = await this.charactersService.getPopularCharacters(query);

      return new ApiResponse(true, "获取热门人物成功", result, HttpStatus.OK);
    } catch (error) {
      throw new BadRequestException(
        error instanceof Error ? error.message : "操作失败",
      );
    }
  }

  /**
   * 获取人物统计
   * GET /characters/statistics
   */
  @Get("statistics")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "获取人物统计" })
  @SwaggerApiResponse({
    status: 200,
    description: "获取成功",
    type: ApiResponse,
  })
  @ApiBearerAuth()
  async getCharacterStatistics(
    @Request() req: AuthRequest,
  ): Promise<ApiResponse<CharacterStatisticsDto>> {
    try {
      const result = await this.charactersService.getUserCharacterStatistics(
        req.user.id,
      );

      return new ApiResponse(true, "获取人物统计成功", result, HttpStatus.OK);
    } catch (error) {
      throw new BadRequestException(
        error instanceof Error ? error.message : "操作失败",
      );
    }
  }

  /**
   * 验证人物名称
   * POST /characters/validate-name
   */
  @Post("validate-name")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "验证人物名称" })
  @SwaggerApiResponse({ status: 200, description: "验证成功" })
  @ApiBearerAuth()
  async validateCharacterName(
    @Request() req: AuthRequest,
    @Body() validateDto: ValidateCharacterNameDto,
  ): Promise<ApiResponse<{ isAvailable: boolean }>> {
    try {
      const isAvailable = await this.charactersService.validateCharacterName(
        req.user.id,
        validateDto.name,
      );

      return new ApiResponse(
        true,
        "名称验证完成",
        { isAvailable },
        HttpStatus.OK,
      );
    } catch (error) {
      throw new BadRequestException(
        error instanceof Error ? error.message : "操作失败",
      );
    }
  }

  /**
   * 获取点亮人物列表
   * GET /characters/lighted
   */
  @Get("lighted")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: "获取点亮人物列表",
    description: `
      获取当前用户已点亮的人物列表
      
      功能特点：
      - 支持分页查询
      - 支持按点亮时间排序
      - 包含人物的基本信息和点亮状态
      - 显示点亮的故事信息
    `,
  })
  @SwaggerApiResponse({
    status: 200,
    description: "获取成功",
    type: ApiResponse,
  })
  @ApiBearerAuth()
  async getLightedCharacters(
    @Request() req: AuthRequest,
    @Query() query: CharacterQueryDto,
  ): Promise<ApiResponse<PaginatedResponse<CharacterResponseDto>>> {
    try {
      const result = await this.charactersService.getLightedCharacters(
        req.user.id,
        query,
      );

      return new ApiResponse(
        true,
        "获取点亮人物列表成功",
        result,
        HttpStatus.OK,
      );
    } catch (error) {
      throw new BadRequestException(
        error instanceof Error ? error.message : "操作失败",
      );
    }
  }

  /**
   * 更新人物点亮状态
   * PUT /characters/:id/lighting-status
   */
  @Put(":id/lighting-status")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "更新人物点亮状态" })
  @ApiParam({ name: "id", description: "人物ID", type: String })
  @SwaggerApiResponse({ status: 200, description: "更新成功" })
  @ApiBearerAuth()
  async updateLightingStatus(
    @Request() req: AuthRequest,
    @Param("id", ParseUUIDPipe) id: string,
  ): Promise<ApiResponse<null>> {
    try {
      await this.charactersService.updateLightingStatus(id);

      return new ApiResponse(true, "点亮状态更新成功", null, HttpStatus.OK);
    } catch (error) {
      throw new BadRequestException(
        error instanceof Error ? error.message : "操作失败",
      );
    }
  }

  /**
   * 上传人物头像
   * POST /characters/:id/avatar
   */
  @Post(":id/avatar")
  @HttpCode(HttpStatus.OK)
  @UseInterceptors(FileInterceptor("avatar"))
  @ApiOperation({ summary: "上传人物头像" })
  @ApiParam({ name: "id", description: "人物ID", type: String })
  @SwaggerApiResponse({ status: 200, description: "上传成功" })
  @ApiBearerAuth()
  async uploadCharacterAvatar(
    @Request() req: AuthRequest,
    @Param("id", ParseUUIDPipe) id: string,
    @UploadedFile() file: MultipartFile,
  ): Promise<ApiResponse<{ avatarUrl: string }>> {
    try {
      if (!file) {
        throw new BadRequestException("请上传头像图片");
      }

      // 验证文件类型
      const allowedTypes = ["image/jpeg", "image/png", "image/webp"];
      if (!allowedTypes.includes(file.mimetype)) {
        throw new BadRequestException("只支持 JPEG、PNG、WebP 格式的图片");
      }

      // 验证文件大小（2MB）
      const buffer = await file.toBuffer();
      if (buffer.length > 2 * 1024 * 1024) {
        throw new BadRequestException("图片大小不能超过2MB");
      }

      // 这里应该实现文件上传逻辑
      // 暂时返回模拟URL
      const avatarUrl = `https://example.com/uploads/characters/${id}-${Date.now()}.jpg`;

      // 更新人物头像
      await this.charactersService.updateCharacter(
        id,
        { avatarUrl },
        req.user.id,
      );

      return new ApiResponse(
        true,
        "头像上传成功",
        { avatarUrl },
        HttpStatus.OK,
      );
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }
      throw new BadRequestException(
        error instanceof Error ? error.message : "操作失败",
      );
    }
  }
}
