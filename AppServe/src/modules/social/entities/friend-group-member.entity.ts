import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  Index,
  Unique,
} from "typeorm";
import { User } from "../../users/entities/user.entity";
import { FriendGroup } from "./friend-group.entity";

export enum MemberRole {
  MEMBER = "member",
  ADMIN = "admin",
}

export enum MemberStatus {
  ACTIVE = "active",
  INVITED = "invited",
  DECLINED = "declined",
  REMOVED = "removed",
}

/**
 * 好友分组成员实体
 * 记录用户在好友分组中的成员关系
 */
@Entity("friend_group_members")
@Unique(["groupId", "userId"])
@Index(["groupId", "status"])
@Index(["userId", "role"])
@Index(["addedAt"])
export class FriendGroupMember {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({
    type: "uuid",
    comment: "分组ID",
  })
  groupId: string;

  @Column({
    type: "uuid",
    comment: "用户ID",
  })
  userId: string;

  @Column({
    type: "enum",
    enum: MemberRole,
    default: MemberRole.MEMBER,
    comment: "成员角色",
  })
  role: MemberRole;

  @Column({
    type: "enum",
    enum: MemberStatus,
    default: MemberStatus.ACTIVE,
    comment: "成员状态",
  })
  status: MemberStatus;

  @Column({
    type: "uuid",
    comment: "添加者用户ID",
  })
  addedBy: string;

  @Column({
    type: "text",
    nullable: true,
    comment: "添加备注",
  })
  note: string;

  @Column({
    type: "jsonb",
    nullable: true,
    comment: "成员专属权限覆盖",
  })
  permissionOverrides: {
    canViewProfile?: boolean;
    canViewStories?: boolean;
    canComment?: boolean;
    canShare?: boolean;
    canMessage?: boolean;
  };

  @Column({
    type: "jsonb",
    nullable: true,
    comment: "扩展字段",
  })
  metadata: Record<string, unknown>;

  @CreateDateColumn({
    type: "timestamp with time zone",
    comment: "添加时间",
  })
  addedAt: Date;

  // 关联关系
  @ManyToOne(() => FriendGroup, (group) => group.members, { eager: false })
  @JoinColumn({ name: "groupId" })
  group: FriendGroup;

  @ManyToOne(() => User, { eager: false })
  @JoinColumn({ name: "userId" })
  user: User;

  @ManyToOne(() => User, { eager: false })
  @JoinColumn({ name: "addedBy" })
  adder: User;
}
