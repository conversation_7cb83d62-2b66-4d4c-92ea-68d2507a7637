/**
 * 企业级人物种子数据
 *
 * 基于 YGS v1.0.0 人物点亮系统设计
 * 涵盖各种人物关系和点亮状态
 */

import { STORY_TITLES } from "./enhanced-story.seeder";

export interface EnhancedCharacterSeedData {
  storyTitle: string;
  name: string;
  description: string;
  avatarUrl?: string;
  gender?: "male" | "female" | "other";
  relationship: string;
  customRelationship?: string;
  isLighted: boolean;
  lighterUsername?: string; // 点亮者用户名
  tags: string[];
}

export const ENHANCED_CHARACTER_SEED_DATA: EnhancedCharacterSeedData[] = [
  // ========== 《那年夏天，我们约定十年后再聚》的人物 ==========
  {
    storyTitle: STORY_TITLES.UNIVERSITY_PROMISE,
    name: "李明",
    description:
      "大学室友，现在在深圳做程序员。性格内向但很靠谱，是宿舍里的技术担当。当年最爱熬夜写代码，经常帮大家修电脑。",
    avatarUrl:
      "https://ygs-oss-dev.oss-cn-hangzhou.aliyuncs.com/characters/liming.jpg",
    gender: "male",
    relationship: "室友",
    isLighted: true,
    lighterUsername: "character_lighter_li", // 被李小红点亮
    tags: ["程序员", "内向", "靠谱", "技术宅"],
  },
  {
    storyTitle: STORY_TITLES.UNIVERSITY_PROMISE,
    name: "王强",
    description:
      "大学室友，现在北京创业。宿舍里的开心果，总是充满正能量。创业虽然辛苦，但他从不抱怨。",
    avatarUrl:
      "https://ygs-oss-dev.oss-cn-hangzhou.aliyuncs.com/characters/wangqiang.jpg",
    gender: "male",
    relationship: "室友",
    isLighted: false,
    tags: ["创业者", "乐观", "正能量"],
  },
  {
    storyTitle: STORY_TITLES.UNIVERSITY_PROMISE,
    name: "赵华",
    description:
      "大学室友，现在老家当老师。学霸型人物，当年的学习委员。虽然选择了平凡的道路，但活得很充实。",
    gender: "male",
    relationship: "室友",
    isLighted: true,
    lighterUsername: "lighter_master_zhou", // 被周发现点亮
    tags: ["老师", "学霸", "稳重"],
  },

  // ========== 《创业路上，感谢有你们》的人物 ==========
  {
    storyTitle: STORY_TITLES.STARTUP_JOURNEY,
    name: "王强",
    description:
      "大学兄弟，在我最困难的时候给予了巨大帮助。不仅介绍投资人，还在精神上一直支持我。",
    gender: "male",
    relationship: "挚友",
    isLighted: false,
    tags: ["创业伙伴", "贵人", "兄弟"],
  },
  {
    storyTitle: STORY_TITLES.STARTUP_JOURNEY,
    name: "李明",
    description:
      "技术大牛，帮我优化了整个技术架构。虽然他工作很忙，但还是抽时间帮我解决技术难题。",
    gender: "male",
    relationship: "挚友",
    isLighted: false,
    tags: ["技术顾问", "无私奉献"],
  },
  {
    storyTitle: STORY_TITLES.STARTUP_JOURNEY,
    name: "赵华",
    description:
      "虽然远在老家，但通过各种方式支持我。他的鼓励让我在最黑暗的时候看到了光。",
    gender: "male",
    relationship: "挚友",
    isLighted: false,
    tags: ["精神支柱", "远方的支持"],
  },

  // ========== 《李明，还记得那个雨夜吗》的人物 ==========
  {
    storyTitle: STORY_TITLES.RAINY_NIGHT,
    name: "明哥",
    description:
      "那个雨夜，谢谢你的陪伴。你让我明白，真正的朋友，是在你最脆弱的时候依然陪在身边的人。",
    gender: "male",
    relationship: "挚友",
    customRelationship: "生死之交",
    isLighted: true,
    lighterUsername: "character_lighter_li", // 被李小红点亮（她就是故事中的李明）
    tags: ["知己", "守护者", "倾听者"],
  },

  // ========== 《小雨，你现在过得还好吗》的人物 ==========
  {
    storyTitle: STORY_TITLES.STUDENT_XIAOYU,
    name: "小雨",
    description:
      "我教过的最坚强的学生。家庭困难但从不放弃，现在已经成为了一名优秀的医生。是她让我明白了教育的意义。",
    avatarUrl:
      "https://ygs-oss-dev.oss-cn-hangzhou.aliyuncs.com/characters/xiaoyu.jpg",
    gender: "female",
    relationship: "学生",
    isLighted: true,
    lighterUsername: "lighter_master_zhou", // 被周发现点亮
    tags: ["坚强", "医生", "励志"],
  },
  {
    storyTitle: STORY_TITLES.STUDENT_XIAOYU,
    name: "小雨的母亲",
    description:
      "一位伟大的母亲，在丈夫生病期间独自撑起整个家。她的坚韧深深影响了小雨。",
    gender: "female",
    relationship: "家长",
    isLighted: false,
    tags: ["伟大母亲", "坚强"],
  },

  // ========== 《三十年教书，我最后悔的一件事》的人物 ==========
  {
    storyTitle: STORY_TITLES.TEACHING_REGRET,
    name: "小军",
    description:
      "那个被我伤害过的孩子，现在已经成长为一个成功的汽修厂老板。他的宽容让我学会了如何成为更好的老师。",
    gender: "male",
    relationship: "学生",
    isLighted: false,
    tags: ["宽容", "成长", "救赎"],
  },
  {
    storyTitle: STORY_TITLES.TEACHING_REGRET,
    name: "小军的母亲",
    description:
      "一位担心孩子的母亲。她的眼泪让我意识到，老师的一句话可能影响孩子的一生。",
    gender: "female",
    relationship: "家长",
    isLighted: false,
    tags: ["母爱", "保护者"],
  },

  // ========== 《妈妈，对不起，我撒谎了》的人物 ==========
  {
    storyTitle: STORY_TITLES.SORRY_MOM,
    name: "妈妈",
    description:
      "永远为我操心的妈妈。对不起，我不应该瞒着你。你的爱让我有勇气面对一切困难。",
    gender: "female",
    relationship: "母亲",
    customRelationship: "最爱的人",
    isLighted: false,
    tags: ["母爱", "理解", "包容"],
  },

  // ========== 《那些年，我们一起追过的剧》的人物 ==========
  {
    storyTitle: STORY_TITLES.DRAMA_MEMORIES,
    name: "小美",
    description:
      "宿舍里的追剧达人，总是第一个发现好剧。现在在电视台工作，梦想成真了。",
    gender: "female",
    relationship: "室友",
    isLighted: false,
    tags: ["追剧达人", "梦想家"],
  },
  {
    storyTitle: STORY_TITLES.DRAMA_MEMORIES,
    name: "晓晓",
    description:
      "爱哭鬼，看到感人情节就忍不住流泪。现在是一名幼师，依然保持着那份纯真。",
    gender: "female",
    relationship: "室友",
    isLighted: true,
    lighterUsername: "character_lighter_li", // 李小红认出了自己
    tags: ["感性", "纯真", "幼师"],
  },
  {
    storyTitle: STORY_TITLES.DRAMA_MEMORIES,
    name: "阿琳",
    description: "理性派，总是会分析剧情逻辑。现在是一名律师，还是那么较真。",
    gender: "female",
    relationship: "室友",
    isLighted: false,
    tags: ["理性", "律师", "较真"],
  },
];

// 导出人物名称映射，方便点亮申请数据引用
export const CHARACTER_NAMES = {
  // 已被点亮的人物
  LIMING_UNIVERSITY: "李明", // 大学故事中的李明（被李小红点亮）
  ZHAOHUA_UNIVERSITY: "赵华", // 大学故事中的赵华（被周发现点亮）
  LIMING_RAINY: "明哥", // 雨夜故事中的李明（被李小红点亮）
  XIAOYU: "小雨", // 被周发现点亮
  XIAOXIAO: "晓晓", // 被李小红点亮

  // 未被点亮的热门人物
  WANGQIANG: "王强",
  XIAOJUN: "小军",
  MOTHER: "妈妈",

  // 其他人物
  XIAOMEI: "小美",
  ALIN: "阿琳",
};
