import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  Index,
} from "typeorm";
import { Share } from "./share.entity";

/**
 * 分享访问日志实体
 * 记录分享链接的访问情况
 */
@Entity("share_access_logs")
@Index(["shareId", "createdAt"])
@Index(["ipAddress", "createdAt"])
export class ShareAccessLog {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({
    type: "uuid",
    nullable: true,
    comment: "分享记录ID",
  })
  shareId: string | null;

  @Column({
    type: "inet",
    comment: "访问者IP地址",
  })
  ipAddress: string;

  @Column({
    type: "text",
    nullable: true,
    comment: "用户代理字符串",
  })
  userAgent: string;

  @Column({
    type: "text",
    nullable: true,
    comment: "引荐来源URL",
  })
  referer: string;

  @Column({
    type: "uuid",
    nullable: true,
    comment: "访问用户ID（如果已登录）",
  })
  userId: string;

  @Column({
    type: "varchar",
    length: 10,
    nullable: true,
    comment: "访问设备类型",
  })
  deviceType: string;

  @Column({
    type: "boolean",
    default: true,
    comment: "访问是否成功",
  })
  isSuccessful: boolean;

  @Column({
    type: "text",
    nullable: true,
    comment: "失败原因（如密码错误、访问受限等）",
  })
  failureReason: string;

  @Column({
    type: "jsonb",
    nullable: true,
    comment: "扩展信息",
  })
  metadata: Record<string, unknown>;

  @CreateDateColumn({
    type: "timestamp with time zone",
    comment: "访问时间",
  })
  createdAt: Date;

  // 关联关系
  @ManyToOne(() => Share, { eager: false })
  @JoinColumn({ name: "shareId" })
  share: Share;
}
