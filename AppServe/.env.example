# YGS 后端环境配置模板
# 复制此文件为 .env 并填写实际配置值

# 环境标识
NODE_ENV=development # development | test | production

# 服务器配置
PORT=3000
API_PREFIX=api
API_VERSION=v1
GLOBAL_PREFIX=/api/v1

# 数据库配置 (阿里云 RDS PostgreSQL)
DB_HOST=your-rds-host.pg.rds.aliyuncs.com
DB_PORT=5432
DB_USER=your_username
DB_PASSWORD=your_password
DB_NAME=ygs-dev # ygs-dev | ygs-test | ygs-prod

# Redis 配置 (本地或云端)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_DB=0

# JWT 认证配置
JWT_SECRET=your-jwt-secret-key
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your-refresh-secret-key
JWT_REFRESH_EXPIRES_IN=30d

# 阿里云 OSS 配置
OSS_REGION=oss-cn-hangzhou
OSS_ACCESS_KEY_ID=your_access_key_id
OSS_ACCESS_KEY_SECRET=your_access_key_secret
OSS_BUCKET=ygs-oss-dev # ygs-oss-dev | ygs-oss-prod
OSS_ENDPOINT=https://oss-cn-hangzhou.aliyuncs.com
OSS_INTERNAL_ENDPOINT=https://oss-cn-hangzhou-internal.aliyuncs.com
OSS_ACCELERATE_ENDPOINT=https://oss-accelerate.aliyuncs.com

# 阿里云短信配置
SMS_ACCESS_KEY_ID=your_sms_access_key_id
SMS_ACCESS_KEY_SECRET=your_sms_access_key_secret
SMS_SIGN_NAME=your_sign_name
SMS_TEMPLATE_CODE=your_template_code
SMS_VIP_TEST_ENABLED=true
SMS_VIP_TEST_PHONES=16675158665,13800138888

# CDN 配置
CDN_DOMAIN=https://cdn.example.com

# AI 服务配置 (可选)
AI_PROVIDER=mock # mock | openai | aliyun
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_BASE_URL=https://api.openai.com/v1

# 日志配置
LOG_LEVEL=debug # error | warn | info | debug
LOG_DIR=./logs

# 速率限制
THROTTLE_TTL=60
THROTTLE_LIMIT=100

# 其他配置
TZ=Asia/Shanghai
UPLOAD_MAX_FILE_SIZE=10485760 # 10MB
UPLOAD_ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,webp