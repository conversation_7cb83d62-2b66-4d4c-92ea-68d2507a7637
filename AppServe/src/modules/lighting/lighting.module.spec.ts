import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import { JwtService } from "@nestjs/jwt";
import { ConfigService } from "@nestjs/config";
import { DataSource } from "typeorm";
import { LightingService } from "./lighting.service";
import { LightingController } from "./lighting.controller";
import { LightRequest } from "../characters/entities/light-request.entity";
import { CharacterLighting } from "../characters/entities/character-lighting.entity";
import { Character } from "../characters/entities/character.entity";
import { Story } from "../stories/entities/story.entity";
import { User } from "../users/entities/user.entity";
import { UsersService } from "../users/users.service";
import { NotificationsService } from "../users/notifications.service";
import { SmsService } from "../auth/services/sms.service";
import { EnhancedRedisService } from "../../common/services/enhanced-redis.service";
import { AuthService } from "../auth/auth.service";

describe("LightingModule", () => {
  let module: TestingModule;
  let lightingService: LightingService;
  let lightingController: LightingController;

  // Mock Repository基础方法
  const mockRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
    findOneBy: jest.fn(),
    save: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    createQueryBuilder: jest.fn(() => ({
      select: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      getMany: jest.fn(),
      getOne: jest.fn(),
      leftJoinAndSelect: jest.fn().mockReturnThis(),
      innerJoinAndSelect: jest.fn().mockReturnThis(),
    })),
  };

  // Mock服务
  const mockUsersService = {
    findById: jest.fn(),
    findByPhone: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
  };

  const mockNotificationsService = {
    createNotification: jest.fn(),
    sendNotification: jest.fn(),
  };

  const mockSmsService = {
    sendVerificationCode: jest.fn(),
    verifyCode: jest.fn(),
  };

  const mockJwtService = {
    signAsync: jest.fn(),
    sign: jest.fn(),
    verify: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn((key: string) => {
      const config: Record<string, string> = {
        JWT_SECRET: "test-secret",
        JWT_EXPIRES_IN: "2700",
        JWT_REFRESH_EXPIRES_IN: "1209600",
        JWT_REFRESH_SECRET: "test-refresh-secret",
        BCRYPT_ROUNDS: "12",
      };
      return config[key];
    }),
  };

  // Mock DataSource
  const mockDataSource = {
    transaction: jest.fn(),
    createQueryRunner: jest.fn(),
    getRepository: jest.fn(),
    manager: {
      save: jest.fn(),
      remove: jest.fn(),
      find: jest.fn(),
      findOne: jest.fn(),
      transaction: jest.fn(),
    },
  };

  // Mock EnhancedRedisService
  const mockEnhancedRedisService = {
    set: jest.fn(),
    get: jest.fn(),
    del: jest.fn(),
    hset: jest.fn(),
    hget: jest.fn(),
    hdel: jest.fn(),
    exists: jest.fn(),
    expire: jest.fn(),
    flushall: jest.fn(),
  };

  // Mock AuthService
  const mockAuthService = {
    login: jest.fn(),
    logout: jest.fn(),
    refreshToken: jest.fn(),
    validateUser: jest.fn(),
    createUser: jest.fn(),
    updateUser: jest.fn(),
    register: jest.fn(),
    sendVerificationCode: jest.fn(),
    verifyCode: jest.fn(),
  };

  beforeEach(async () => {
    module = await Test.createTestingModule({
      providers: [
        LightingService,
        LightingController,
        {
          provide: getRepositoryToken(LightRequest),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(CharacterLighting),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(Character),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(Story),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(User),
          useValue: mockRepository,
        },
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
        {
          provide: NotificationsService,
          useValue: mockNotificationsService,
        },
        {
          provide: SmsService,
          useValue: mockSmsService,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: DataSource,
          useValue: mockDataSource,
        },
        {
          provide: EnhancedRedisService,
          useValue: mockEnhancedRedisService,
        },
        {
          provide: AuthService,
          useValue: mockAuthService,
        },
      ],
    }).compile();

    lightingService = module.get<LightingService>(LightingService);
    lightingController = module.get<LightingController>(LightingController);
  });

  afterEach(async () => {
    if (module) {
      await module.close();
    }
    jest.clearAllMocks();
  });

  describe("模块实例化", () => {
    it("应该能够正确实例化模块", () => {
      expect(module).toBeDefined();
    });

    it("应该正确提供 LightingService", () => {
      expect(lightingService).toBeDefined();
      expect(lightingService).toBeInstanceOf(LightingService);
    });

    it("应该正确提供 LightingController", () => {
      expect(lightingController).toBeDefined();
      expect(lightingController).toBeInstanceOf(LightingController);
    });
  });

  describe("模块依赖注入", () => {
    it("应该正确注入所有 Repository 依赖", () => {
      const lightRequestRepo = module.get(getRepositoryToken(LightRequest));
      const characterLightingRepo = module.get(
        getRepositoryToken(CharacterLighting),
      );
      const characterRepo = module.get(getRepositoryToken(Character));
      const storyRepo = module.get(getRepositoryToken(Story));
      const userRepo = module.get(getRepositoryToken(User));

      expect(lightRequestRepo).toBeDefined();
      expect(characterLightingRepo).toBeDefined();
      expect(characterRepo).toBeDefined();
      expect(storyRepo).toBeDefined();
      expect(userRepo).toBeDefined();
    });

    it("应该正确注入外部模块服务", () => {
      const usersService = module.get<UsersService>(UsersService);
      const jwtService = module.get<JwtService>(JwtService);
      const configService = module.get<ConfigService>(ConfigService);

      expect(usersService).toBeDefined();
      expect(jwtService).toBeDefined();
      expect(configService).toBeDefined();
    });
  });

  describe("模块导出", () => {
    it("应该正确导出 LightingService", () => {
      // 验证服务可以被外部模块使用
      expect(lightingService).toBeDefined();
      expect(lightingService).toBeInstanceOf(LightingService);
    });
  });

  describe("模块配置", () => {
    it("应该正确配置 TypeORM 实体", () => {
      // 验证模块能够正确配置所有必要的数据库实体
      expect(module.get(getRepositoryToken(LightRequest))).toBeDefined();
      expect(module.get(getRepositoryToken(CharacterLighting))).toBeDefined();
      expect(module.get(getRepositoryToken(Character))).toBeDefined();
      expect(module.get(getRepositoryToken(Story))).toBeDefined();
      expect(module.get(getRepositoryToken(User))).toBeDefined();
    });

    it("应该正确处理 forwardRef 依赖", () => {
      // 验证循环依赖处理正确
      const usersService = module.get<UsersService>(UsersService);
      expect(usersService).toBeDefined();
    });
  });
});
