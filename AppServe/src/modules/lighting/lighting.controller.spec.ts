import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { JwtService } from "@nestjs/jwt";
import { LightingController } from "./lighting.controller";
import { LightingService } from "./lighting.service";
import { AuthService } from "../auth/auth.service";
import type { SubmitLightRequestDto } from "./dto";
import type { AuthRequest } from "../../common/types/request.types";

describe("LightingController", () => {
  let controller: LightingController;
  let mockLightingService: jest.Mocked<LightingService>;

  // Mock数据
  const mockUser = { id: "user-1", username: "testuser", userNumber: "100001" };
  const mockAuthRequest = { user: mockUser } as AuthRequest;

  const mockLightRequestResponse = {
    id: "request-1",
    storyId: "story-1",
    storyTitle: "测试故事",
    characterId: "character-1",
    characterName: "测试角色",
    requesterId: "user-1",
    requesterNickname: "测试用户",
    status: "pending",
    reason: "测试理由",
    createdAt: new Date(),
  };

  const mockLightingStatus = {
    isLighted: false,
    lighterId: undefined,
    lighterNickname: undefined,
    lightedAt: undefined,
    canApplyLighting: true,
    cannotApplyReason: undefined,
    userRequestStatus: "none",
  };

  const mockValidationResult = {
    isValid: true,
    characterExists: true,
    phoneMatches: true,
    noDuplicateApplication: true,
    characterNotLightedByOthers: true,
    userStatusNormal: true,
    rateLimitValid: true,
    rejectionReason: undefined,
    cooldownSeconds: undefined,
    userAnomalyStatus: "normal",
    remainingDailyAttempts: 15,
  };

  beforeEach(async () => {
    const mockServiceMethods = {
      submitLightRequest: jest.fn(),
      getPendingRequests: jest.fn(),
      getUserRequests: jest.fn(),
      getLightingStatus: jest.fn(),
      validateLightingRequestDetailed: jest.fn(),
      confirmLightRequest: jest.fn(),
      rejectLightRequest: jest.fn(),
      getUserLightedCharacters: jest.fn(),
      getCharacterLightings: jest.fn(),
      getLightingHistory: jest.fn(),
      getUserLightingStats: jest.fn(),
      cleanupExpiredRequests: jest.fn(),
      syncLightingStatus: jest.fn(),
    };

    const mockAuthService = {
      validateUser: jest.fn(),
      login: jest.fn(),
      register: jest.fn(),
      sendVerificationCode: jest.fn(),
      verifyCode: jest.fn(),
      refreshToken: jest.fn(),
      logout: jest.fn(),
      validateToken: jest.fn(),
    };

    const mockJwtService = {
      sign: jest.fn(),
      signAsync: jest.fn(),
      verify: jest.fn(),
      verifyAsync: jest.fn(),
      decode: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [LightingController],
      providers: [
        {
          provide: LightingService,
          useValue: mockServiceMethods,
        },
        {
          provide: AuthService,
          useValue: mockAuthService,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
      ],
    }).compile();

    controller = module.get<LightingController>(LightingController);
    mockLightingService = module.get(
      LightingService,
    ) as jest.Mocked<LightingService>;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("submitLightRequest", () => {
    it("应该成功提交点亮申请", async () => {
      const submitDto: SubmitLightRequestDto = {
        phoneNumber: "13800138000",
        reason: "我是这个角色的原型",
      };

      mockLightingService.submitLightRequest.mockResolvedValue(
        mockLightRequestResponse,
      );

      const result = await controller.submitLightRequest(
        mockAuthRequest,
        "story-1",
        "character-1",
        submitDto,
      );

      expect(mockLightingService.submitLightRequest).toHaveBeenCalledWith(
        "story-1",
        "character-1",
        "user-1",
        submitDto,
      );
      expect(result.success).toBe(true);
      expect(result.message).toBe("点亮申请提交成功");
      expect(result.data).toBe(mockLightRequestResponse);
      expect(result.statusCode).toBe(201);
    });

    it("应该包含时间戳", async () => {
      const submitDto: SubmitLightRequestDto = {
        phoneNumber: "13800138000",
        reason: "我是这个角色的原型",
      };

      mockLightingService.submitLightRequest.mockResolvedValue(
        mockLightRequestResponse,
      );

      const result = await controller.submitLightRequest(
        mockAuthRequest,
        "story-1",
        "character-1",
        submitDto,
      );

      expect(result.timestamp).toBeDefined();
      expect(typeof result.timestamp).toBe("string");
    });
  });

  describe("getPendingRequests", () => {
    it("应该获取待处理申请列表", async () => {
      const mockRequests = [mockLightRequestResponse];
      mockLightingService.getPendingRequests.mockResolvedValue(mockRequests);

      const result = await controller.getPendingRequests(mockAuthRequest);

      expect(mockLightingService.getPendingRequests).toHaveBeenCalledWith(
        "user-1",
      );
      expect(result.success).toBe(true);
      expect(result.message).toBe("获取待处理申请成功");
      expect(result.data).toBe(mockRequests);
      expect(result.statusCode).toBe(200);
    });
  });

  describe("getUserRequests", () => {
    it("应该获取用户申请记录", async () => {
      const mockRequests = [mockLightRequestResponse];
      mockLightingService.getUserRequests.mockResolvedValue(mockRequests);

      const result = await controller.getUserRequests(mockAuthRequest);

      expect(mockLightingService.getUserRequests).toHaveBeenCalledWith(
        "user-1",
      );
      expect(result.success).toBe(true);
      expect(result.message).toBe("获取申请记录成功");
      expect(result.data).toBe(mockRequests);
      expect(result.statusCode).toBe(200);
    });
  });

  describe("getLightingStatus", () => {
    it("应该获取人物点亮状态（不包含用户状态）", async () => {
      mockLightingService.getLightingStatus.mockResolvedValue(
        mockLightingStatus,
      );

      const result = await controller.getLightingStatus(
        mockAuthRequest,
        "character-1",
      );

      expect(mockLightingService.getLightingStatus).toHaveBeenCalledWith(
        "character-1",
        undefined,
      );
      expect(result.success).toBe(true);
      expect(result.message).toBe("查询点亮状态成功");
      expect(result.data).toBe(mockLightingStatus);
    });

    it("应该获取人物点亮状态（包含用户状态）", async () => {
      mockLightingService.getLightingStatus.mockResolvedValue(
        mockLightingStatus,
      );

      const result = await controller.getLightingStatus(
        mockAuthRequest,
        "character-1",
        true,
      );

      expect(mockLightingService.getLightingStatus).toHaveBeenCalledWith(
        "character-1",
        "user-1",
      );
      expect(result.success).toBe(true);
      expect(result.data).toBe(mockLightingStatus);
    });
  });

  describe("checkCanApplyLighting", () => {
    it("应该检查是否可以申请点亮", async () => {
      const mockStatus = {
        ...mockLightingStatus,
        canApplyLighting: true,
        cannotApplyReason: undefined,
      };
      mockLightingService.getLightingStatus.mockResolvedValue(mockStatus);

      const result = await controller.checkCanApplyLighting(
        mockAuthRequest,
        "character-1",
      );

      expect(mockLightingService.getLightingStatus).toHaveBeenCalledWith(
        "character-1",
        "user-1",
      );
      expect(result.success).toBe(true);
      expect(result.message).toBe("检查完成");
      expect(result.data.canApply).toBe(true);
      expect(result.data.reason).toBeUndefined();
    });

    it("应该返回不能申请的原因", async () => {
      const mockStatus = {
        ...mockLightingStatus,
        canApplyLighting: false,
        cannotApplyReason: "该角色已被点亮",
      };
      mockLightingService.getLightingStatus.mockResolvedValue(mockStatus);

      const result = await controller.checkCanApplyLighting(
        mockAuthRequest,
        "character-1",
      );

      expect(result.data.canApply).toBe(false);
      expect(result.data.reason).toBe("该角色已被点亮");
    });
  });

  describe("validateLightingRequest", () => {
    it("应该验证点亮申请条件", async () => {
      mockLightingService.validateLightingRequestDetailed.mockResolvedValue(
        mockValidationResult,
      );

      const result = await controller.validateLightingRequest(
        mockAuthRequest,
        "story-1",
        "character-1",
      );

      expect(
        mockLightingService.validateLightingRequestDetailed,
      ).toHaveBeenCalledWith("user-1", "story-1", "character-1");
      expect(result.success).toBe(true);
      expect(result.message).toBe("验证完成");
      expect(result.data).toBe(mockValidationResult);
    });
  });

  describe("confirmLightRequest", () => {
    it("应该确认点亮申请", async () => {
      mockLightingService.confirmLightRequest.mockResolvedValue(undefined);

      const result = await controller.confirmLightRequest(
        mockAuthRequest,
        "request-1",
      );

      expect(mockLightingService.confirmLightRequest).toHaveBeenCalledWith(
        "request-1",
        "user-1",
      );
      expect(result.success).toBe(true);
      expect(result.message).toBe("点亮申请确认成功");
      expect(result.data).toEqual({});
      expect(result.statusCode).toBe(200);
    });
  });

  describe("rejectLightRequest", () => {
    it("应该拒绝点亮申请（无原因）", async () => {
      mockLightingService.rejectLightRequest.mockResolvedValue(undefined);

      const result = await controller.rejectLightRequest(
        mockAuthRequest,
        "request-1",
      );

      expect(mockLightingService.rejectLightRequest).toHaveBeenCalledWith(
        "request-1",
        "user-1",
        undefined,
      );
      expect(result.success).toBe(true);
      expect(result.message).toBe("点亮申请已拒绝");
      expect(result.data).toEqual({});
    });

    it("应该拒绝点亮申请（带原因）", async () => {
      mockLightingService.rejectLightRequest.mockResolvedValue(undefined);
      const body = { reason: "不符合角色设定" };

      const result = await controller.rejectLightRequest(
        mockAuthRequest,
        "request-1",
        body,
      );

      expect(mockLightingService.rejectLightRequest).toHaveBeenCalledWith(
        "request-1",
        "user-1",
        "不符合角色设定",
      );
      expect(result.success).toBe(true);
      expect(result.message).toBe("点亮申请已拒绝");
    });
  });

  describe("getUserLightedCharacters", () => {
    it("应该获取用户点亮的人物集", async () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const mockCharacters = [
        {
          id: "lighting-1",
          characterId: "character-1",
          storyId: "story-1",
          lighterUserId: "user-1",
          creatorUserId: "user-2",
          requestId: "request-1",
          status: "active" as const,
          confirmedAt: new Date(),
          createdAt: new Date(),
          character: null,
          story: null,
          lighterUser: null,
          creatorUser: null,
          request: null,
        },
        {
          id: "lighting-2",
          characterId: "character-2",
          storyId: "story-2",
          lighterUserId: "user-1",
          creatorUserId: "user-3",
          requestId: "request-2",
          status: "active" as const,
          confirmedAt: new Date(),
          createdAt: new Date(),
          character: null,
          story: null,
          lighterUser: null,
          creatorUser: null,
          request: null,
        },
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
      ] as any;
      mockLightingService.getUserLightedCharacters.mockResolvedValue(
        mockCharacters,
      );

      const result = await controller.getUserLightedCharacters("user-1");

      expect(mockLightingService.getUserLightedCharacters).toHaveBeenCalledWith(
        "user-1",
      );
      expect(result.success).toBe(true);
      expect(result.message).toBe("获取点亮集成功");
      expect(result.data).toBe(mockCharacters);
    });
  });

  describe("getCharacterLightings", () => {
    it("应该获取人物的点亮记录", async () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const mockLightings = [
        {
          id: "lighting-1",
          characterId: "character-1",
          storyId: "story-1",
          lighterUserId: "user-1",
          creatorUserId: "user-2",
          requestId: "request-1",
          status: "active" as const,
          confirmedAt: new Date(),
          createdAt: new Date(),
          character: null,
          story: null,
          lighterUser: null,
          creatorUser: null,
          request: null,
        },
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
      ] as any;
      mockLightingService.getCharacterLightings.mockResolvedValue(
        mockLightings,
      );

      const result = await controller.getCharacterLightings("character-1");

      expect(mockLightingService.getCharacterLightings).toHaveBeenCalledWith(
        "character-1",
      );
      expect(result.success).toBe(true);
      expect(result.message).toBe("获取人物点亮记录成功");
      expect(result.data).toBe(mockLightings);
    });
  });

  describe("getLightingHistory", () => {
    it("应该获取用户的点亮历史", async () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const mockHistory = {
        lightedByUser: [
          {
            id: "lighting-1",
            characterId: "character-1",
            storyId: "story-1",
            lighterUserId: "user-1",
            creatorUserId: "user-2",
            requestId: "request-1",
            status: "active" as const,
            confirmedAt: new Date(),
            createdAt: new Date(),
            character: null,
            story: null,
            lighterUser: null,
            creatorUser: null,
            request: null,
          },
        ],
        lightedByOthers: [
          {
            id: "lighting-2",
            characterId: "character-2",
            storyId: "story-2",
            lighterUserId: "user-2",
            creatorUserId: "user-1",
            requestId: "request-2",
            status: "active" as const,
            confirmedAt: new Date(),
            createdAt: new Date(),
            character: null,
            story: null,
            lighterUser: null,
            creatorUser: null,
            request: null,
          },
        ],
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
      } as any;
      mockLightingService.getLightingHistory.mockResolvedValue(mockHistory);

      const result = await controller.getLightingHistory("user-1");

      expect(mockLightingService.getLightingHistory).toHaveBeenCalledWith(
        "user-1",
      );
      expect(result.success).toBe(true);
      expect(result.message).toBe("获取点亮历史成功");
      expect(result.data).toBe(mockHistory);
    });
  });

  describe("getUserLightingStats", () => {
    it("应该获取用户点亮统计数据", async () => {
      const mockStats = {
        totalLighted: 5,
        totalReceived: 3,
        pendingRequests: 1,
        thisMonthLighted: 2,
      };
      mockLightingService.getUserLightingStats.mockResolvedValue(mockStats);

      const result = await controller.getUserLightingStats("user-1");

      expect(mockLightingService.getUserLightingStats).toHaveBeenCalledWith(
        "user-1",
      );
      expect(result.success).toBe(true);
      expect(result.message).toBe("获取统计数据成功");
      expect(result.data).toBe(mockStats);
    });
  });

  describe("cleanupExpiredRequests", () => {
    it("应该清理过期申请", async () => {
      const mockResult = { cleaned: 5 };
      mockLightingService.cleanupExpiredRequests.mockResolvedValue(mockResult);

      const result = await controller.cleanupExpiredRequests();

      expect(mockLightingService.cleanupExpiredRequests).toHaveBeenCalled();
      expect(result.success).toBe(true);
      expect(result.message).toBe("已清理 5 个过期申请");
      expect(result.data).toBe(mockResult);
    });
  });

  describe("syncLightingStatus", () => {
    it("应该同步点亮状态", async () => {
      const mockResult = { synced: 10 };
      mockLightingService.syncLightingStatus.mockResolvedValue(mockResult);

      const result = await controller.syncLightingStatus();

      expect(mockLightingService.syncLightingStatus).toHaveBeenCalled();
      expect(result.success).toBe(true);
      expect(result.message).toBe("已同步 10 个人物状态");
      expect(result.data).toBe(mockResult);
    });
  });

  it("应该正确注入LightingService", () => {
    expect(controller).toBeDefined();
    expect(mockLightingService).toBeDefined();
  });
});
