# YGS社交功能产品文档

> **文档版本**: v1.0  
> **创建日期**: 2025-07-31  
> **文档目的**: 详细定义YGS多用户社交功能的交互流程、角色权限和业务规则  
> **适用范围**: 前端开发团队，产品验收标准  
> **文档状态**: 待人类确认

---

## 一、文档概述

### 1.1 社交功能范围
本文档涵盖YGS平台的多用户社交功能，包括：
- **点亮系统**：核心社交验证机制（申请、确认、权益）
- **社交关系**：关注、粉丝、好友体系
- **内容互动**：评论、点赞、收藏、分享
- **消息通知**：系统通知、互动提醒、点亮通知
- **用户发现**：搜索、推荐、热门排行

### 1.2 角色定义
- **故事作者**：创建故事和人物的用户
- **申请者**：申请点亮人物的用户
- **点亮用户**：成功点亮人物的用户
- **普通用户**：浏览和互动的一般用户
- **好友**：互相关注的用户

---

## 二、点亮系统（核心功能）

### 2.1 点亮系统概述

#### 2.1.1 系统价值
```yaml
核心创新:
  - 真实身份验证机制
  - 虚拟内容与现实身份结合
  - 建立可信的社交关系
  
业务目标:
  - 提高内容可信度
  - 增强用户粘性
  - 形成独特社交网络
```

#### 2.1.2 核心约束
```yaml
双重排他性:
  1. 人物唯一性: 每个人物只能被一个用户点亮
  2. 用户唯一性: 每个用户对每个作者只能点亮一个人物
  
申请限制:
  - 不能申请自己创建的人物
  - 不能申请自己故事中的人物
  - 每日申请上限: 20次
  - 申请有效期: 7天
```

### 2.2 申请者视角流程

#### 2.2.1 发现可点亮人物
```yaml
入口场景:
  1. 故事详情页人物卡片
  2. 用户主页人物集
  3. 搜索结果中的人物
  4. 热门人物推荐
  
人物卡片展示:
  未点亮状态:
    - 灰色头像框
    - "未点亮"标签
    - 点击显示"申请点亮"按钮
    
  已被他人点亮:
    - 彩色头像框
    - 显示点亮者昵称
    - 点击查看点亮者主页
    
  已被自己点亮:
    - 金色头像框
    - "已点亮"标签
    - 显示点亮时间
```

#### 2.2.2 点亮申请流程（10阶段可视化）

**Stage 1: 申请介绍页**
```yaml
页面内容:
  标题: "申请点亮{人物名称}"
  
  说明文字:
    - 什么是点亮系统
    - 点亮后的权益
    - 申请注意事项
    
  人物信息展示:
    - 人物头像、姓名、关系
    - 所属故事信息
    - 作者信息
    
  操作按钮:
    - "开始申请"（主按钮）
    - "了解更多"（文字按钮）
```

**Stage 2: 资格检查**
```yaml
检查项目:
  1. 登录状态检查
  2. 申请限制检查:
     - 是否达到每日上限
     - 是否有待处理申请
     - 账号状态是否正常
  3. 人物状态检查:
     - 是否已被点亮
     - 是否正在申请中
     
检查结果:
  通过: 进入下一步
  
  不通过: 显示具体原因
    - "请先登录"→ 跳转登录
    - "今日申请次数已达上限"
    - "该人物已被其他用户点亮"
    - "您已有待处理的申请"
```

**Stage 3: 关系验证选择**
```yaml
选项设计:
  选项1: 我认识故事中的这个人
    说明: "通过手机号验证真实身份"
    
  选项2: 我就是故事中的这个人
    说明: "验证后获得最高权限"
    
  选项3: 仅申请查看权限
    说明: "不验证身份，仅获得基础权益"
    
交互设计:
  - 单选按钮组
  - 选中后显示详细说明
  - 根据选择进入不同流程
```

**Stage 4: 手机号验证（可选）**
```yaml
触发条件: 选择需要身份验证的选项

界面设计:
  说明文字: 
    "提供手机号后4位，帮助作者确认您的身份"
    "作者将看到: 138****{后4位}"
    
  输入框:
    - 完整手机号输入
    - 格式验证
    - 脱敏显示预览
    
  隐私说明:
    - 手机号加密存储
    - 仅展示脱敏信息
    - 可选择跳过
```

**Stage 5: 申请理由填写**
```yaml
界面设计:
  标题: "告诉作者您为什么要点亮这个人物"
  
  文本框:
    placeholder: "请输入申请理由（10-200字）"
    字数统计: "0/200"
    最少10字才能提交
    
  快捷理由模板:
    - "我是故事中的这个人"
    - "这是我的{关系}，想要查看相关故事"
    - "我认识这个人，想了解更多"
    - 自定义输入
    
  参考提示:
    - 真诚说明您与人物的关系
    - 说明点亮后的用途
    - 作者更容易通过真实的申请
```

**Stage 6: 信息确认**
```yaml
确认内容:
  申请信息汇总:
    - 申请人物: {头像+姓名+关系}
    - 所属故事: {标题}
    - 故事作者: {昵称}
    - 验证方式: {是否提供手机号}
    - 申请理由: {完整展示}
    - 有效期限: 7天
    
  注意事项:
    - 申请提交后无法修改
    - 7天内作者未处理将自动过期
    - 一次只能申请一个人物
    
  操作按钮:
    - "确认提交"
    - "返回修改"
```

**Stage 7: 提交处理**
```yaml
提交流程:
  1. 显示加载动画
  2. 调用API提交申请
  3. 返回申请结果
  
成功处理:
  显示成功页面:
    - 成功图标动画
    - "申请已提交"
    - "请等待作者确认"
    - 预计处理时间
    
  后续操作:
    - "查看申请进度"
    - "继续浏览"
    - "设置提醒"
```

**Stage 8: 等待确认**
```yaml
申请状态页:
  状态展示:
    - 进度条: 已提交 → 审核中 → 已确认/已拒绝
    - 剩余时间: "剩余6天23小时"
    - 作者信息: 最近在线时间
    
  可选操作:
    - 撤回申请（24小时内）
    - 发送提醒（仅一次）
    - 查看其他申请
```

**Stage 9: 结果通知**
```yaml
确认通过:
  推送通知:
    标题: "点亮申请已通过"
    内容: "恭喜！您已成功点亮{人物名称}"
    
  结果页面:
    - 成功动画
    - 点亮权益说明
    - 查看人物详情
    - 分享喜悦
    
申请被拒:
  无推送通知（情感保护）
  
  查看时显示:
    - 申请未通过
    - 不显示拒绝原因
    - 可以申请其他人物
    
申请过期:
  状态更新:
    - 显示"已过期"
    - 可重新申请
```

**Stage 10: 权益获得**
```yaml
立即生效的权益:
  1. 身份标识:
     - 个人主页显示点亮人物
     - 评论时显示特殊标识
     
  2. 内容权限:
     - 永久访问"仅人物可见"的故事
     - 查看人物相关的所有故事
     
  3. 社交特权:
     - 可以引用相关故事
     - 互动权重提升
     
权益展示:
  - 权益列表页
  - 使用引导
  - 特权标识说明
```

#### 2.2.3 申请管理
```yaml
我的申请列表:
  列表分类:
    - 待确认: 显示剩余时间
    - 已通过: 显示点亮时间
    - 未通过: 可重新申请
    - 已过期: 显示过期时间
    
  列表功能:
    - 撤回申请（24小时内）
    - 查看详情
    - 重新申请
    
  排序选项:
    - 按申请时间
    - 按剩余时间
    - 按状态分组
```

### 2.3 故事作者视角流程

#### 2.3.1 收到申请通知
```yaml
通知形式:
  1. App推送通知
  2. 消息中心红点
  3. 首页提醒卡片
  
通知内容:
  标题: "收到新的点亮申请"
  内容: "{申请者昵称}申请点亮您故事中的{人物名称}"
  操作: "立即查看"
  
批量通知:
  多个申请: "您有3个待处理的点亮申请"
```

#### 2.3.2 申请列表管理
```yaml
列表入口:
  - 消息中心 → 点亮申请
  - 个人中心 → 申请管理
  - 人物详情 → 申请记录
  
列表展示:
  申请卡片:
    - 申请者信息: 头像、昵称、有故事号
    - 申请人物: 人物名称、所属故事
    - 申请时间: 相对时间显示
    - 剩余时间: 倒计时提醒
    - 验证信息: 是否提供手机号（脱敏显示）
    
  筛选功能:
    - 按人物筛选
    - 按故事筛选
    - 按时间排序
    - 只看待处理
```

#### 2.3.3 申请详情查看
```yaml
详情内容:
  申请者信息:
    - 头像、昵称、有故事号
    - 注册时间、认证状态
    - 故事数量、被点亮数
    - 共同好友（如有）
    
  申请信息:
    - 申请的人物和故事
    - 申请理由（完整）
    - 身份验证信息（如有）
    - 申请时间、剩余时间
    
  历史记录:
    - 该用户的其他申请
    - 之前的互动记录
    
  参考信息:
    - 系统风险提示
    - 相似申请对比
```

#### 2.3.4 处理申请

**确认申请**
```yaml
确认前检查:
  - 再次确认人物信息
  - 查看排他性提示
  - 了解确认后果
  
确认弹窗:
  标题: "确认点亮申请"
  
  内容: 
    "确认后，{申请者}将成为{人物名称}的点亮者"
    "• 获得人物相关故事的永久访问权"
    "• 其他用户将无法再点亮此人物"
    "• 人物关系将被锁定保护"
    
  按钮:
    - "确认"（主按钮）
    - "再想想"（次按钮）
    
确认后处理:
  1. 更新人物点亮状态
  2. 发送成功通知给申请者
  3. 更新相关权限
  4. 记录操作日志
```

**拒绝申请**
```yaml
拒绝原因选择:
  预设原因:
    - "信息不符"
    - "暂不开放"
    - "其他原因"
    
  自定义原因:
    - 可选填写
    - 仅内部记录
    - 不发送给申请者
    
拒绝确认:
  提示: "拒绝后对方不会收到通知"
  
拒绝后处理:
  1. 更新申请状态
  2. 不发送通知（情感保护）
  3. 申请者可重新申请
```

#### 2.3.5 批量处理
```yaml
批量操作:
  - 全选/反选
  - 批量确认（需二次确认）
  - 批量拒绝
  - 批量标记
  
安全限制:
  - 批量确认需要逐个检查
  - 防止误操作
```

### 2.4 点亮后的持续互动

#### 2.4.1 新故事通知
```yaml
触发场景:
  作者发布新故事，包含已被点亮的人物
  
通知内容:
  推送: "{作者}发布了关于{人物}的新故事"
  
特殊机制:
  - 自动建立点亮关系
  - 无需重新申请
  - 通知用户确认知晓
```

#### 2.4.2 点亮者权益使用
```yaml
故事访问:
  权限标识:
    - 金色钥匙图标
    - "点亮者特权"标签
    
  访问记录:
    - 记录查看时间
    - 生成访问报告
    
引用功能:
  操作入口: 故事详情页"引用"按钮
  
  引用流程:
    1. 确认引用信息
    2. 选择引用分类
    3. 添加引用备注
    4. 成功加入引用集
    
  管理功能:
    - 查看引用集
    - 编辑分类
    - 移除引用
```

#### 2.4.3 社交标识展示
```yaml
评论区标识:
  显示效果:
    - 昵称后的特殊徽章
    - 鼠标悬停显示"该用户是{人物名称}"
    
  排序权重:
    - 点亮者评论优先展示
    - 作者更容易看到
    
个人主页展示:
  点亮集模块:
    - 展示所有点亮的人物
    - 按时间/作者分组
    - 点击查看详情
```

---

## 三、社交关系系统

### 3.1 关注与粉丝

#### 3.1.1 关注机制
```yaml
关注入口:
  - 用户主页关注按钮
  - 故事详情页作者信息
  - 搜索结果用户卡片
  - 推荐用户列表
  
关注流程:
  1. 点击关注按钮
  2. 按钮变为"已关注"
  3. 对方粉丝数+1
  4. 自己关注数+1
  
关注限制:
  - 最多关注2000人
  - 不能关注自己
  - 被拉黑无法关注
```

#### 3.1.2 粉丝管理
```yaml
粉丝列表:
  展示内容:
    - 用户头像、昵称
    - 互关标识
    - 关注时间
    - 快捷操作按钮
    
  操作功能:
    - 回关（一键互关）
    - 移除粉丝
    - 查看主页
    
  排序选项:
    - 最新关注
    - 互动最多
    - 活跃粉丝
```

#### 3.1.3 好友关系
```yaml
好友定义:
  条件: 互相关注的用户
  
好友特权:
  - 查看好友可见内容
  - 消息免打扰白名单
  - 动态优先展示
  
好友列表:
  - 显示最近互动
  - 在线状态（可选）
  - 快捷私信入口
```

### 3.2 用户分组

#### 3.2.1 分组管理
```yaml
创建分组:
  限制: 最多20个分组
  
  创建流程:
    1. 点击"新建分组"
    2. 输入分组名称（1-10字）
    3. 选择分组成员
    4. 确认创建
    
默认分组:
  - 特别关注
  - 家人
  - 朋友
  - 同事
```

#### 3.2.2 分组应用
```yaml
内容权限:
  - 发布时选择分组可见
  - 分组成员变更自动生效
  
消息管理:
  - 分组群发消息
  - 分组消息过滤
  
动态展示:
  - 按分组筛选动态
  - 分组优先级设置
```

---

## 四、内容互动功能

### 4.1 评论系统

#### 4.1.1 评论结构
```yaml
评论层级:
  一级评论: 直接评论故事
  二级评论: 回复一级评论
  三级评论: 回复二级评论
  限制: 最多三级，再深显示为同级
  
评论展示:
  - 热门评论置顶
  - 作者评论标识
  - 点亮者评论标识
  - 时间倒序排列
```

#### 4.1.2 发表评论
```yaml
评论输入:
  输入框:
    placeholder: "写下你的想法..."
    字数限制: 1-500字
    表情支持: 常用emoji
    
  @提及功能:
    触发: 输入@符号
    显示: 可@用户列表
    范围: 作者+已评论用户
    
发送前检查:
  - 敏感词过滤
  - 重复内容检测
  - 发送频率限制
  
发送后处理:
  - 即时显示
  - 通知被@用户
  - 更新评论计数
```

#### 4.1.3 评论互动
```yaml
点赞评论:
  - 单击点赞/取消
  - 显示点赞数
  - 点赞记录
  
回复评论:
  - 点击回复按钮
  - 自动@被回复者
  - 保持话题连贯
  
举报评论:
  - 长按显示菜单
  - 选择举报原因
  - 提交举报
  
删除评论:
  - 仅自己的评论
  - 二次确认
  - 软删除机制
```

#### 4.1.4 评论通知
```yaml
通知类型:
  - 收到新评论
  - 评论被回复
  - 评论被点赞
  - 被@提及
  
通知内容:
  - 评论者信息
  - 评论内容摘要
  - 相关故事标题
  - 快捷回复入口
```

### 4.2 点赞系统

#### 4.2.1 点赞机制
```yaml
点赞对象:
  - 故事点赞
  - 评论点赞
  - 人物点赞（预留）
  
点赞效果:
  - 心形动画
  - 数字跳动
  - 震动反馈（可选）
  
取消点赞:
  - 再次点击取消
  - 灰色心形图标
  - 数字减少
```

#### 4.2.2 点赞记录
```yaml
个人记录:
  - 我点赞的内容
  - 按时间排序
  - 快速跳转
  
被赞记录:
  - 谁赞了我
  - 时间线展示
  - 一键回赞
```

### 4.3 收藏系统

#### 4.3.1 收藏功能
```yaml
收藏入口:
  - 故事详情页
  - 长按故事卡片
  - 滑动操作（可选）
  
收藏分类:
  默认收藏夹:
    - 我的收藏
    
  自定义收藏夹:
    - 最多20个
    - 自定义名称
    - 公开/私密设置
    
收藏时:
  1. 选择收藏夹
  2. 添加标签（可选）
  3. 写备注（可选）
  4. 确认收藏
```

#### 4.3.2 收藏管理
```yaml
收藏夹管理:
  - 新建/编辑/删除
  - 批量移动
  - 导出功能（预留）
  
收藏内容管理:
  - 按时间/标题排序
  - 标签筛选
  - 全文搜索
  - 批量操作
```

### 4.4 分享系统

#### 4.4.1 分享渠道
```yaml
社交平台:
  - 微信好友/朋友圈
  - QQ好友/空间  
  - 微博
  - 小红书（预留）
  
其他方式:
  - 复制链接
  - 生成海报
  - 扫码分享
```

#### 4.4.2 分享内容定制
```yaml
分享卡片:
  包含内容:
    - 故事标题
    - 内容摘要
    - 作者信息
    - 精美配图
    
分享海报:
  模板选择:
    - 简约风格
    - 文艺风格
    - 个性风格
    
  自定义元素:
    - 选择背景
    - 调整文字
    - 添加二维码
```

---

## 五、消息通知系统

### 5.1 通知分类

#### 5.1.1 系统通知
```yaml
类型:
  - 官方公告
  - 版本更新
  - 活动通知
  - 安全提醒
  
展示方式:
  - 重要通知弹窗
  - 普通通知列表
  - 小红点提示
```

#### 5.1.2 点亮通知
```yaml
类型:
  - 收到点亮申请
  - 申请被确认
  - 新故事包含已点亮人物
  - 点亮周年纪念
  
特殊处理:
  - 申请被拒绝不通知
  - 可设置免打扰
```

#### 5.1.3 互动通知
```yaml
类型:
  - 被关注
  - 被评论
  - 被点赞
  - 被@提及
  - 被收藏（可选）
  
聚合规则:
  - 相同类型聚合
  - 显示最新3条
  - 查看全部入口
```

### 5.2 通知设置

#### 5.2.1 通知开关
```yaml
总开关:
  - 接收新消息通知
  
分类开关:
  - 点亮申请通知
  - 评论通知
  - 点赞通知
  - 关注通知
  - 系统通知
  
免打扰时段:
  - 设置时间段
  - 临时免打扰
```

#### 5.2.2 通知方式
```yaml
推送通知:
  - 系统推送
  - 应用内弹窗
  - 角标数字
  
声音/震动:
  - 通知音效
  - 震动反馈
  - 静音模式
```

### 5.3 消息中心

#### 5.3.1 消息列表
```yaml
分类标签:
  - 全部
  - 点亮
  - 评论
  - 点赞
  - 关注
  - 系统
  
列表展示:
  - 未读标识
  - 时间显示
  - 内容预览
  - 快捷操作
```

#### 5.3.2 消息操作
```yaml
单条操作:
  - 标记已读
  - 删除消息
  - 查看详情
  
批量操作:
  - 全部已读
  - 批量删除
  - 清空消息
```

---

## 六、用户发现功能

### 6.1 搜索功能

#### 6.1.1 搜索入口
```yaml
全局搜索:
  - 首页搜索框
  - 发现页搜索
  - 快捷键唤起
  
搜索范围:
  - 用户（昵称/有故事号）
  - 故事（标题/内容）
  - 人物（姓名/关系）
  - 话题标签
```

#### 6.1.2 搜索体验
```yaml
搜索建议:
  - 实时搜索建议
  - 历史搜索记录
  - 热门搜索推荐
  
搜索结果:
  - 分类展示
  - 关键词高亮
  - 结果筛选
  - 加载更多
```

### 6.2 推荐系统

#### 6.2.1 用户推荐
```yaml
推荐依据:
  - 共同关注
  - 相似兴趣
  - 活跃用户
  - 新用户扶持
  
推荐展示:
  - 推荐理由
  - 用户卡片
  - 一键关注
  - 不感兴趣
```

#### 6.2.2 内容推荐
```yaml
推荐算法:
  - 基于关注
  - 基于兴趣
  - 基于互动
  - 基于时间
  
推荐优化:
  - 去重机制
  - 多样性保证
  - 新内容扶持
```

### 6.3 热门排行

#### 6.3.1 排行榜类型
```yaml
故事排行:
  - 今日热门
  - 本周热门
  - 月度精选
  - 新秀榜单
  
用户排行:
  - 创作达人
  - 人气作者
  - 新晋作者
  - 互动达人
  
人物排行:
  - 热门人物
  - 新增人物
  - 点亮最多
```

#### 6.3.2 排行展示
```yaml
榜单设计:
  - 排名变化
  - 数据展示
  - 快捷关注
  - 查看详情
  
更新机制:
  - 实时更新
  - 定时刷新
  - 缓存策略
```

---

## 七、多角色交互场景

### 7.1 典型场景一：完整点亮流程

```yaml
场景描述:
  小明看到小红的故事中有"大学同学-张三"，
  小明就是张三本人，想要点亮这个人物。
  
流程步骤:
  1. 小明浏览故事，看到人物"张三"
  2. 点击人物卡片，选择"申请点亮"
  3. 选择"我就是故事中的这个人"
  4. 提供手机号后4位验证
  5. 填写申请理由："我就是张三，想看看老同学写的关于我的故事"
  6. 提交申请
  
  7. 小红收到通知："小明申请点亮您故事中的'张三'"
  8. 查看申请详情，看到手机号后4位匹配
  9. 确认申请理由真实
  10. 点击"确认"通过申请
  
  11. 小明收到通知："恭喜！您已成功点亮'张三'"
  12. 小明获得所有相关故事的永久访问权
  13. 小明在个人主页展示这个点亮身份
  14. 其他用户无法再申请点亮"张三"
```

### 7.2 典型场景二：社交互动链

```yaml
场景描述:
  用户A发布故事 → 用户B点亮人物 → 
  用户C看到B的点亮 → C关注A → 
  形成社交链条
  
互动效果:
  1. 增强内容可信度
  2. 扩大社交网络  
  3. 提升用户粘性
  4. 形成良性循环
```

### 7.3 典型场景三：权限传递

```yaml
场景描述:
  作者设置故事为"仅人物可见"，
  只有点亮了故事中人物的用户才能查看。
  
权限验证:
  1. 用户访问故事
  2. 系统检查权限设置
  3. 验证是否点亮相关人物
  4. 有权限则显示完整内容
  5. 无权限则提示申请点亮
```

---

## 八、安全与隐私保护

### 8.1 隐私保护机制

#### 8.1.1 信息脱敏
```yaml
手机号保护:
  - 申请时: 仅显示后4位
  - 存储时: 加密存储
  - 展示时: 138****1234
  
真实身份保护:
  - 不强制实名
  - 可选择匿名申请
  - 身份信息加密
```

#### 8.1.2 情感保护
```yaml
核心原则:
  - 只发送正面通知
  - 拒绝不通知
  - 删除不提醒
  
具体应用:
  - 点亮申请被拒绝：不通知
  - 取消关注：不通知
  - 删除评论：不通知
  - 移除好友：不通知
```

### 8.2 行为规范

#### 8.2.1 防骚扰机制
```yaml
申请限制:
  - 每日上限20次
  - 同一人物仅能申请一次
  - 频繁申请自动限制
  
互动限制:
  - 评论频率限制
  - 私信频率限制
  - 关注数量限制
```

#### 8.2.2 违规处理
```yaml
举报机制:
  - 一键举报
  - 举报原因选择
  - 证据截图上传
  
处理流程:
  1. 接收举报
  2. 人工审核
  3. 处理决定
  4. 结果通知
  
处罚措施:
  - 警告提醒
  - 功能限制
  - 账号封禁
```

---

## 九、数据统计与分析

### 9.1 用户数据统计

#### 9.1.1 个人数据
```yaml
基础数据:
  - 故事数量
  - 人物数量  
  - 获赞总数
  - 被收藏数
  
社交数据:
  - 关注数
  - 粉丝数
  - 好友数
  - 点亮数
  
互动数据:
  - 评论数
  - 点赞数
  - 分享数
  - 浏览量
```

#### 9.1.2 数据展示
```yaml
可视化展示:
  - 数据概览卡片
  - 趋势图表
  - 排名变化
  - 成就系统
  
隐私控制:
  - 可选择公开/隐藏
  - 部分数据仅自己可见
```

### 9.2 内容数据分析

#### 9.2.1 故事数据
```yaml
浏览数据:
  - 总浏览量
  - 独立访客
  - 平均停留时间
  - 完读率
  
互动数据:
  - 点赞率
  - 评论率
  - 收藏率
  - 分享率
  
用户画像:
  - 读者性别分布
  - 年龄分布
  - 地域分布
  - 兴趣标签
```

#### 9.2.2 人物数据
```yaml
点亮数据:
  - 申请次数
  - 通过率
  - 点亮者信息
  - 相关故事数
  
影响力数据:
  - 曝光次数
  - 互动次数
  - 引用次数
```

---

## 十、异常处理

### 10.1 点亮系统异常

#### 10.1.1 申请异常
```yaml
重复申请:
  - 提示: "您已申请过该人物"
  - 引导: 查看申请进度
  
人物已被点亮:
  - 提示: "该人物已被其他用户点亮"
  - 显示: 点亮者信息（如公开）
  
申请超限:
  - 提示: "今日申请次数已达上限"
  - 显示: 明日可继续
```

#### 10.1.2 权限异常
```yaml
权限变更:
  - 故事权限改变
  - 及时更新访问权限
  - 通知受影响用户
  
权限冲突:
  - 优先级判断
  - 清晰的提示
  - 申诉渠道
```

### 10.2 社交功能异常

#### 10.2.1 关系异常
```yaml
关注失败:
  - 用户不存在
  - 被对方拉黑
  - 关注数超限
  
好友异常:
  - 单方取消关注
  - 账号状态异常
  - 及时更新状态
```

#### 10.2.2 互动异常
```yaml
评论失败:
  - 内容违规
  - 权限不足
  - 网络异常
  
消息异常:
  - 发送失败
  - 接收延迟
  - 重试机制
```

---

## 十一、性能优化

### 11.1 加载优化
- 社交数据缓存
- 增量更新策略
- 预加载机制
- 懒加载实现

### 11.2 交互优化
- 乐观更新UI
- 本地状态管理
- 防抖节流处理
- 动画性能优化

### 11.3 推送优化
- 消息聚合
- 静默推送
- 优先级管理
- 推送去重

---

## 十二、测试场景

### 12.1 功能测试场景
1. 完整的点亮申请流程
2. 多角色切换测试
3. 权限验证测试
4. 并发申请处理
5. 异常情况恢复

### 12.2 性能测试场景
1. 大量申请并发
2. 消息推送压力
3. 数据加载性能
4. 内存泄漏检测

### 12.3 用户体验测试
1. 新用户引导流程
2. 操作反馈及时性
3. 错误提示友好性
4. 多语言支持（预留）

---

**文档说明**: 本文档详细定义了YGS平台多用户社交功能的完整交互流程和实现细节。重点说明了核心创新功能"点亮系统"的十阶段申请流程，以及相关的社交互动机制。所有设计都遵循情感保护原则，确保用户获得积极的社交体验。

**重要提醒**:
1. 点亮系统的双重排他性约束必须严格执行
2. 情感保护机制（只发正面通知）必须贯彻始终
3. 所有社交功能都需要考虑隐私保护
4. 性能优化要特别关注实时通知的及时性

**下一步**: 请人类团队确认以上社交功能设计，特别关注：
- 点亮流程的10个阶段是否完整合理
- 多角色交互的业务逻辑是否清晰
- 隐私保护和情感保护机制是否到位
- 是否有遗漏的社交场景

确认后将基于三份实施文档（页面架构、基础功能、社交功能）开始Flutter前端开发。