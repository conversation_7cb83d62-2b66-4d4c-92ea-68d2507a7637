import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import type { VerificationAttempt } from "./verification-security.service";
import { VerificationSecurityService } from "./verification-security.service";
import { EnhancedRedisService } from "./enhanced-redis.service";

describe("VerificationSecurityService", () => {
  let service: VerificationSecurityService;
  let mockRedisClient: jest.Mocked<{
    get: jest.MockedFunction<(key: string) => Promise<string | null>>;
    setex: jest.MockedFunction<
      (key: string, seconds: number, value: string) => Promise<void>
    >;
    del: jest.MockedFunction<(key: string) => Promise<number>>;
    incr: jest.MockedFunction<(key: string) => Promise<number>>;
    expire: jest.MockedFunction<
      (key: string, seconds: number) => Promise<boolean>
    >;
    pipeline: jest.MockedFunction<
      () => {
        incr: jest.MockedFunction<(key: string) => object>;
        expire: jest.MockedFunction<(key: string, seconds: number) => object>;
        del: jest.MockedFunction<(key: string) => object>;
        exec: jest.MockedFunction<() => Promise<unknown[]>>;
      }
    >;
  }>;

  // Mock 数据
  const mockPhone = "13800138000";
  const mockIP = "***********";
  const mockDeviceFingerprint = "test-device-fingerprint";
  const mockCode = "1234";

  const mockAttempt: VerificationAttempt = {
    phone: mockPhone,
    code: mockCode,
    timestamp: Date.now(),
    ip: mockIP,
    deviceFingerprint: mockDeviceFingerprint,
    success: true,
  };

  beforeEach(async () => {
    // 创建Redis客户端mock
    mockRedisClient = {
      get: jest.fn(),
      setex: jest.fn(),
      del: jest.fn(),
      incr: jest.fn(),
      expire: jest.fn(),
      pipeline: jest.fn(),
    } as jest.Mocked<typeof mockRedisClient>;

    // 创建pipeline mock
    const mockPipeline = {
      incr: jest.fn().mockReturnThis(),
      expire: jest.fn().mockReturnThis(),
      del: jest.fn().mockReturnThis(),
      exec: jest.fn().mockResolvedValue([]),
    };

    mockRedisClient.pipeline.mockReturnValue(mockPipeline);

    // 创建EnhancedRedisService mock - 只需要getClient方法
    const mockEnhancedRedisService = {
      getClient: jest.fn().mockReturnValue(mockRedisClient),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        VerificationSecurityService,
        {
          provide: EnhancedRedisService,
          useValue: mockEnhancedRedisService,
        },
      ],
    }).compile();

    service = module.get<VerificationSecurityService>(
      VerificationSecurityService,
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("checkVerificationAttempt", () => {
    it("should allow verification when all checks pass", async () => {
      // 所有检查都返回允许
      mockRedisClient.get.mockResolvedValue(null);

      const result = await service.checkVerificationAttempt(
        mockPhone,
        mockIP,
        mockDeviceFingerprint,
      );

      expect(result).toEqual({
        allowed: true,
      });
    });

    it("should block when phone is locked", async () => {
      const lockoutData = {
        reason: "验证失败次数过多",
        lockedAt: Date.now(),
        expiresAt: Date.now() + 60 * 60 * 1000, // 1小时后过期
      };

      mockRedisClient.get
        .mockResolvedValueOnce(JSON.stringify(lockoutData)) // phone lockout check
        .mockResolvedValue(null); // 其他检查

      const result = await service.checkVerificationAttempt(
        mockPhone,
        mockIP,
        mockDeviceFingerprint,
      );

      expect(result.allowed).toBe(false);
      expect(result.reason).toContain("手机号已被锁定");
      expect(result.lockoutTime).toBeGreaterThan(0);
    });

    it("should block when IP is locked", async () => {
      const lockoutData = {
        reason: "IP验证失败次数过多",
        lockedAt: Date.now(),
        expiresAt: Date.now() + 60 * 60 * 1000,
      };

      mockRedisClient.get
        .mockResolvedValueOnce(null) // phone not locked
        .mockResolvedValueOnce(JSON.stringify(lockoutData)) // IP locked
        .mockResolvedValue(null); // 其他检查

      const result = await service.checkVerificationAttempt(
        mockPhone,
        mockIP,
        mockDeviceFingerprint,
      );

      expect(result.allowed).toBe(false);
      expect(result.reason).toContain("IP已被锁定");
    });

    it("should block when code attempts exceeded", async () => {
      mockRedisClient.get
        .mockResolvedValueOnce(null) // phone not locked
        .mockResolvedValueOnce(null) // IP not locked
        .mockResolvedValueOnce("3") // code attempts = 3 (max reached)
        .mockResolvedValue(null);

      const result = await service.checkVerificationAttempt(
        mockPhone,
        mockIP,
        mockDeviceFingerprint,
      );

      expect(result.allowed).toBe(false);
      expect(result.reason).toContain("验证码已失效");
      expect(result.remainingAttempts).toBe(0);
    });

    it("should block when hourly phone limit exceeded", async () => {
      // 当前小时时间戳，用于生成限制key
      // 当前小时时间戳，用于生成限制key
      Math.floor(Date.now() / (60 * 60 * 1000));

      mockRedisClient.get
        .mockResolvedValueOnce(null) // phone not locked
        .mockResolvedValueOnce(null) // IP not locked
        .mockResolvedValueOnce("2") // code attempts = 2 (under limit)
        .mockResolvedValueOnce("10") // phone hourly attempts = 10 (limit reached)
        .mockResolvedValue(null);

      const result = await service.checkVerificationAttempt(
        mockPhone,
        mockIP,
        mockDeviceFingerprint,
      );

      expect(result.allowed).toBe(false);
      expect(result.reason).toContain("手机号验证尝试过于频繁");
    });

    it("should block when hourly IP limit exceeded", async () => {
      mockRedisClient.get
        .mockResolvedValueOnce(null) // phone not locked
        .mockResolvedValueOnce(null) // IP not locked
        .mockResolvedValueOnce("2") // code attempts = 2
        .mockResolvedValueOnce("5") // phone hourly = 5
        .mockResolvedValueOnce("20") // IP hourly = 20 (limit reached)
        .mockResolvedValue(null);

      const result = await service.checkVerificationAttempt(
        mockPhone,
        mockIP,
        mockDeviceFingerprint,
      );

      expect(result.allowed).toBe(false);
      expect(result.reason).toContain("IP验证尝试过于频繁");
    });

    it("should allow verification with remaining attempts", async () => {
      mockRedisClient.get
        .mockResolvedValueOnce(null) // phone not locked
        .mockResolvedValueOnce(null) // IP not locked
        .mockResolvedValueOnce("1") // code attempts = 1
        .mockResolvedValueOnce("5") // phone hourly = 5
        .mockResolvedValueOnce("10") // IP hourly = 10
        .mockResolvedValue(null);

      const result = await service.checkVerificationAttempt(
        mockPhone,
        mockIP,
        mockDeviceFingerprint,
      );

      expect(result.allowed).toBe(true);
    });

    it("should handle expired lockouts correctly", async () => {
      const expiredLockoutData = {
        reason: "验证失败次数过多",
        lockedAt: Date.now() - 2 * 60 * 60 * 1000, // 2小时前锁定
        expiresAt: Date.now() - 60 * 60 * 1000, // 1小时前就应该过期
      };

      mockRedisClient.get
        .mockResolvedValueOnce(JSON.stringify(expiredLockoutData)) // expired phone lockout
        .mockResolvedValue(null);

      const result = await service.checkVerificationAttempt(
        mockPhone,
        mockIP,
        mockDeviceFingerprint,
      );

      expect(result.allowed).toBe(true); // 过期的锁定应该被忽略
    });

    it("should handle check errors gracefully", async () => {
      mockRedisClient.get.mockRejectedValue(new Error("Redis error"));

      const result = await service.checkVerificationAttempt(
        mockPhone,
        mockIP,
        mockDeviceFingerprint,
      );

      expect(result.allowed).toBe(true); // 错误时默认允许
    });
  });

  describe("recordVerificationAttempt", () => {
    it("should record successful verification attempt", async () => {
      const successfulAttempt = { ...mockAttempt, success: true };

      await service.recordVerificationAttempt(successfulAttempt);

      expect(mockRedisClient.pipeline).toHaveBeenCalled();

      // 验证pipeline调用
      const mockPipeline = mockRedisClient.pipeline();
      expect(mockPipeline.incr).toHaveBeenCalledWith(
        `verification:attempts:${mockPhone}`,
      );
      expect(mockPipeline.expire).toHaveBeenCalled();
      expect(mockPipeline.del).toHaveBeenCalledWith(
        `verification:failures:${mockPhone}`,
      );
      expect(mockPipeline.exec).toHaveBeenCalled();
    });

    it("should record failed verification attempt", async () => {
      const failedAttempt = { ...mockAttempt, success: false };

      mockRedisClient.incr
        .mockResolvedValueOnce(2) // failure count for phone
        .mockResolvedValueOnce(3); // failure count for IP

      await service.recordVerificationAttempt(failedAttempt);

      expect(mockRedisClient.pipeline).toHaveBeenCalled();
      expect(mockRedisClient.incr).toHaveBeenCalledWith(
        `verification:failures:${mockPhone}`,
      );
      expect(mockRedisClient.incr).toHaveBeenCalledWith(
        `verification:failures:ip:${mockIP}`,
      );
    });

    it("should lockout phone after 5 failed attempts", async () => {
      const failedAttempt = { ...mockAttempt, success: false };

      mockRedisClient.incr
        .mockResolvedValueOnce(5) // 5th failure for phone
        .mockResolvedValueOnce(3); // IP failure count

      await service.recordVerificationAttempt(failedAttempt);

      expect(mockRedisClient.setex).toHaveBeenCalledWith(
        `verification:lockout:phone:${mockPhone}`,
        60 * 60, // 1 hour
        expect.stringContaining("验证失败次数过多"),
      );
    });

    it("should lockout IP after 10 failed attempts", async () => {
      const failedAttempt = { ...mockAttempt, success: false };

      mockRedisClient.incr
        .mockResolvedValueOnce(3) // phone failure count
        .mockResolvedValueOnce(10); // 10th failure for IP

      await service.recordVerificationAttempt(failedAttempt);

      expect(mockRedisClient.setex).toHaveBeenCalledWith(
        `verification:lockout:ip:${mockIP}`,
        60 * 60, // 1 hour
        expect.stringContaining("IP验证失败次数过多"),
      );
    });

    it("should handle recording errors gracefully", async () => {
      // 配置pipeline返回正常对象，但exec方法抛出错误
      const mockPipeline = {
        incr: jest.fn().mockReturnThis(),
        expire: jest.fn().mockReturnThis(),
        del: jest.fn().mockReturnThis(),
        exec: jest.fn().mockRejectedValue(new Error("Pipeline error")),
      };

      mockRedisClient.pipeline.mockReturnValue(mockPipeline);

      // 应该不抛出异常，错误被内部捕获
      await expect(
        service.recordVerificationAttempt(mockAttempt),
      ).resolves.not.toThrow();
    });
  });

  describe("invalidateCode", () => {
    it("should invalidate verification code", async () => {
      const reason = "尝试次数过多";

      await service.invalidateCode(mockPhone, reason);

      expect(mockRedisClient.del).toHaveBeenCalledWith(
        `verification:code:${mockPhone}`,
      );
    });
  });

  describe("getCodeAttemptCount", () => {
    it("should return current attempt count", async () => {
      mockRedisClient.get.mockResolvedValue("2");

      const count = await service.getCodeAttemptCount(mockPhone);

      expect(count).toBe(2);
      expect(mockRedisClient.get).toHaveBeenCalledWith(
        `verification:attempts:${mockPhone}`,
      );
    });

    it("should return 0 when no attempts recorded", async () => {
      mockRedisClient.get.mockResolvedValue(null);

      const count = await service.getCodeAttemptCount(mockPhone);

      expect(count).toBe(0);
    });

    it("should handle non-numeric values", async () => {
      mockRedisClient.get.mockResolvedValue("invalid");

      const count = await service.getCodeAttemptCount(mockPhone);

      expect(count).toBeNaN(); // parseInt('invalid') returns NaN
    });
  });

  describe("getSecurityStats", () => {
    it("should return security statistics for unlocked phone", async () => {
      mockRedisClient.get
        .mockResolvedValueOnce("2") // attempt count
        .mockResolvedValueOnce(null); // no lockout

      const stats = await service.getSecurityStats(mockPhone);

      expect(stats).toEqual({
        attemptCount: 2,
        isLocked: false,
        remainingAttempts: 1, // 3 - 2 = 1
      });
    });

    it("should return security statistics for locked phone", async () => {
      const lockoutData = {
        reason: "验证失败次数过多",
        lockedAt: Date.now(),
        expiresAt: Date.now() + 60 * 60 * 1000,
      };

      mockRedisClient.get
        .mockResolvedValueOnce("3") // attempt count
        .mockResolvedValueOnce(JSON.stringify(lockoutData)); // locked

      const stats = await service.getSecurityStats(mockPhone);

      expect(stats).toEqual({
        attemptCount: 3,
        isLocked: true,
        remainingAttempts: 0, // Math.max(0, 3 - 3) = 0
      });
    });

    it("should handle attempt count exceeding maximum", async () => {
      mockRedisClient.get
        .mockResolvedValueOnce("5") // attempt count > max
        .mockResolvedValueOnce(null); // no lockout

      const stats = await service.getSecurityStats(mockPhone);

      expect(stats).toEqual({
        attemptCount: 5,
        isLocked: false,
        remainingAttempts: 0, // Math.max(0, 3 - 5) = 0
      });
    });
  });

  describe("edge cases and error handling", () => {
    it("should handle malformed lockout data", async () => {
      mockRedisClient.get
        .mockResolvedValueOnce("invalid-json") // malformed lockout data
        .mockResolvedValue(null);

      // 应该捕获JSON解析错误并默认允许
      const result = await service.checkVerificationAttempt(
        mockPhone,
        mockIP,
        mockDeviceFingerprint,
      );

      expect(result.allowed).toBe(true);
    });

    it("should handle Redis connection errors in all methods", async () => {
      mockRedisClient.get.mockRejectedValue(
        new Error("Redis connection error"),
      );
      mockRedisClient.incr.mockRejectedValue(
        new Error("Redis connection error"),
      );
      mockRedisClient.del.mockRejectedValue(
        new Error("Redis connection error"),
      );

      // 所有方法都应该优雅处理错误
      await expect(
        service.checkVerificationAttempt(
          mockPhone,
          mockIP,
          mockDeviceFingerprint,
        ),
      ).resolves.toEqual({ allowed: true });

      await expect(
        service.recordVerificationAttempt(mockAttempt),
      ).resolves.not.toThrow();

      await expect(
        service.invalidateCode(mockPhone, "test"),
      ).resolves.not.toThrow();

      await expect(service.getCodeAttemptCount(mockPhone)).resolves.toBe(0);
    });

    it("should handle zero and negative timestamps", async () => {
      const attemptWithZeroTimestamp = { ...mockAttempt, timestamp: 0 };

      await expect(
        service.recordVerificationAttempt(attemptWithZeroTimestamp),
      ).resolves.not.toThrow();

      const attemptWithNegativeTimestamp = { ...mockAttempt, timestamp: -1000 };

      await expect(
        service.recordVerificationAttempt(attemptWithNegativeTimestamp),
      ).resolves.not.toThrow();
    });

    it("should handle empty string values in Redis", async () => {
      mockRedisClient.get.mockResolvedValue("");

      const count = await service.getCodeAttemptCount(mockPhone);
      expect(count).toBe(0); // parseInt('') returns NaN, '' || '0' = '0'
    });

    it("should handle concurrent access scenarios", async () => {
      // 模拟并发场景：同时检查和记录
      const promises = [
        service.checkVerificationAttempt(
          mockPhone,
          mockIP,
          mockDeviceFingerprint,
        ),
        service.recordVerificationAttempt(mockAttempt),
        service.getCodeAttemptCount(mockPhone),
      ];

      await expect(Promise.all(promises)).resolves.not.toThrow();
    });
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });
});
