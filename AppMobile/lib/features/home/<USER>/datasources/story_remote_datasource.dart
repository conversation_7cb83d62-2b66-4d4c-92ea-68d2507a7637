import '../../domain/entities/story_entity.dart';

/// 故事远程数据源 - 负责API调用
class StoryRemoteDataSource {
  /// 获取故事列表
  Future<StoryListResponse> getStories({
    int page = 1,
    int limit = 10,
    String? category,
  }) async {
    // TODO: 实现实际的API调用
    // 目前使用mock数据源
    throw UnimplementedError('待实现API调用');
  }

  /// 获取公开故事列表
  Future<StoryListResponse> getPublicStories({
    int page = 1,
    int limit = 10,
  }) async {
    // TODO: 实现实际的API调用
    throw UnimplementedError('待实现API调用');
  }

  /// 点赞故事
  Future<void> likeStory(String storyId) async {
    // TODO: 实现实际的API调用
    throw UnimplementedError('待实现API调用');
  }
}