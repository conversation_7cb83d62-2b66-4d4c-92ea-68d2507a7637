{"name": "AppUI", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint . --fix", "lint:check": "eslint .", "type-check": "tsc --noEmit", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-navigation/bottom-tabs": "^7.3.14", "@react-navigation/native": "^7.1.10", "@react-navigation/native-stack": "^7.3.14", "@react-navigation/stack": "^7.3.3", "@reduxjs/toolkit": "^2.8.2", "@tanstack/react-query": "^5.80.6", "axios": "^1.9.0", "react": "19.0.0", "react-native": "0.79.3", "react-native-gesture-handler": "^2.25.0", "react-native-keychain": "^10.0.0", "react-native-reanimated": "^3.18.0", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.11.1", "react-native-vector-icons": "^10.2.0", "react-redux": "^9.2.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "18.0.0", "@react-native-community/cli-platform-android": "18.0.0", "@react-native-community/cli-platform-ios": "18.0.0", "@react-native/babel-preset": "0.79.3", "@react-native/eslint-config": "0.79.3", "@react-native/metro-config": "0.79.3", "@react-native/typescript-config": "0.79.3", "@types/jest": "^29.5.13", "@types/react": "^19.0.0", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^19.0.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.0.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix --max-warnings 0", "prettier --write"]}}