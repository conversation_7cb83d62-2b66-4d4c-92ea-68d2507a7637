# YGS 资源管理系统使用指南

## 📖 概述

YGS资源管理系统提供了统一的图标、图片和其他资源访问方式，支持类型安全、响应式适配和性能优化。

## 🎨 资源分类

### 1. Tab图标（核心导航）
已整理到 `assets/icons/tab/` 目录，直接使用：

```dart
import 'package:app_mobile/design_system/ygs_design_system.dart';

// 使用Tab图标组件
YgsTabIcon(
  tabType: YgsTabType.home,
  isActive: true,
  size: 24,
)

// 或直接使用路径
YgsImage(
  assetPath: YgsAssets.tabHomeActive,
  width: 24.w,
  height: 24.h,
)
```

### 2. 基础图标（功能性图标）
保存在temp目录，通过资源管理系统访问：

```dart
// 社交功能图标
YgsImage(
  assetPath: YgsAssets.iconLikeDefault,
  width: 20.w,
  height: 20.h,
)

// 操作图标
YgsImage(
  assetPath: YgsAssets.iconAdd,
  width: 24.w,
  height: 24.h,
)
```

### 3. 主题图标（装饰性图标）
用于主题卡片和故事卡片的装饰：

```dart
// 方形版本（用于主题卡片）
YgsThemeIcon(
  themeKey: 'about-dream',
  useCircleVersion: false,
  size: 32.w,
)

// 圆形版本（用于故事卡片）
YgsThemeIcon(
  themeKey: 'about-dream',
  useCircleVersion: true,
  size: 32.w,
)
```

### 4. 头像系统
提供多样化的用户头像选择：

```dart
// 编号头像
YgsAvatar(
  avatarKey: 'avatar-01',
  size: 48.w,
  circular: true,
)

// 年龄性别分类头像
YgsAvatar(
  avatarKey: 'young-male',
  size: 48.w,
  circular: true,
)
```

### 5. 主题故事封面
用于主题故事的封面展示：

```dart
final coverPath = YgsAssetsUtil.getStoryCover('about-dream');
if (coverPath != null) {
  YgsImage(
    assetPath: coverPath,
    width: 200.w,
    height: 120.h,
    fit: BoxFit.cover,
  );
}
```

### 6. 主题模块卡片
主题选择的卡片样式：

```dart
final modulePath = YgsAssetsUtil.getThemeModule('career-growth');
if (modulePath != null) {
  YgsImage(
    assetPath: modulePath,
    width: 160.w,
    height: 80.h,
    fit: BoxFit.cover,
  );
}
```

## 🛠️ API 参考

### YgsAssets 类
提供所有资源路径常量：

```dart
// Tab图标
YgsAssets.tabHomeActive
YgsAssets.tabHomeDefault
YgsAssets.tabNewsActive
YgsAssets.tabNewsDefault
YgsAssets.tabStoryActive
YgsAssets.tabStoryDefault
YgsAssets.tabMyActive  
YgsAssets.tabMyDefault

// 基础图标
YgsAssets.iconAdd
YgsAssets.iconClose
YgsAssets.iconSearch
YgsAssets.iconShare
YgsAssets.iconReturn
YgsAssets.iconLikeDefault
YgsAssets.iconLikeActive
YgsAssets.iconFavoritesDefault
YgsAssets.iconFavoritesActive
YgsAssets.iconCommentDefault

// 资源映射
YgsAssets.themeIcons      // 主题图标映射
YgsAssets.avatars         // 头像映射
YgsAssets.storyCover      // 故事封面映射
YgsAssets.themeModules    // 主题模块映射
```

### YgsImage 组件
响应式图片组件，支持错误处理：

```dart
YgsImage(
  assetPath: 'assets/path/to/image.png',
  width: 100.w,              // 响应式宽度
  height: 100.h,             // 响应式高度
  fit: BoxFit.cover,         // 适应方式
  responsive: true,          // 启用响应式（默认true）
  placeholder: Widget?,      // 加载占位符
  errorWidget: Widget?,      // 错误占位符
)
```

### YgsTabIcon 组件
专用Tab图标组件：

```dart
YgsTabIcon(
  tabType: YgsTabType.home,  // Tab类型
  isActive: false,           // 是否激活
  size: 24.w,               // 图标尺寸
)
```

### YgsThemeIcon 组件
主题装饰图标组件：

```dart
YgsThemeIcon(
  themeKey: 'about-dream',   // 主题key
  useCircleVersion: false,   // 使用圆形版本
  size: 32.w,               // 图标尺寸
)
```

### YgsAvatar 组件
用户头像组件：

```dart
YgsAvatar(
  avatarKey: 'avatar-01',    // 头像key
  size: 48.w,               // 头像尺寸
  circular: true,           // 是否圆形
)
```

### YgsAssetsUtil 工具类
资源访问工具方法：

```dart
// 获取主题图标路径
YgsAssetsUtil.getThemeIcon('about-dream', useCircleVersion: true)

// 获取头像路径
YgsAssetsUtil.getAvatar('avatar-01')

// 获取故事封面路径
YgsAssetsUtil.getStoryCover('about-dream')

// 获取主题模块路径
YgsAssetsUtil.getThemeModule('career-growth', useAlt: false)

// 列出可用资源keys
YgsAssetsUtil.getAvailableThemeKeys()
YgsAssetsUtil.getAvailableAvatarKeys()
```

## 📁 目录结构

```
assets/
├── icons/
│   ├── tab/                 # 核心Tab图标（已整理）
│   │   ├── tab-home-active.png
│   │   └── ...
│   └── basic/               # 基础图标（待整理）
├── temp/                    # 临时资源库（按需使用）
│   ├── 基础图标/
│   ├── 主题图标/
│   ├── 主题故事封面/
│   ├── 主题模块/
│   ├── 头像/
│   └── 标签栏icon/
└── images/                  # 其他图片资源
```

## 🚀 最佳实践

### 1. 响应式使用
始终使用 `.w` 和 `.h` 进行响应式适配：

```dart
YgsImage(
  assetPath: YgsAssets.iconAdd,
  width: 24.w,   // 响应式宽度
  height: 24.h,  // 响应式高度
)
```

### 2. 错误处理
为重要图片提供错误占位符：

```dart
YgsImage(
  assetPath: avatarPath,
  width: 48.w,
  height: 48.h,
  errorWidget: Container(
    width: 48.w,
    height: 48.h,
    color: Colors.grey[300],
    child: Icon(Icons.person),
  ),
)
```

### 3. 性能优化
对于列表中的图片，考虑使用缓存：

```dart
import 'package:cached_network_image/cached_network_image.dart';

// 对于网络图片使用缓存
CachedNetworkImage(
  imageUrl: networkImageUrl,
  width: 100.w,
  height: 100.h,
  placeholder: (context, url) => YgsImage(
    assetPath: YgsAssets.iconLoading,
    width: 20.w,
    height: 20.h,
  ),
)
```

### 4. 主题适配
根据不同场景选择合适的图标版本：

```dart
// 主题卡片使用方形图标
YgsThemeIcon(
  themeKey: 'about-dream',
  useCircleVersion: false,  // 方形版本
  size: 32.w,
)

// 故事卡片使用圆形图标  
YgsThemeIcon(
  themeKey: 'about-dream',
  useCircleVersion: true,   // 圆形版本
  size: 32.w,
)
```

## 🔄 维护和更新

### 添加新资源
1. 将新资源放入对应的temp子目录
2. 在 `YgsAssets` 中添加路径映射
3. 更新 `pubspec.yaml` 中的assets配置
4. 创建对应的组件或工具方法

### 资源整理
当某类资源使用频率较高时：
1. 从temp目录移动到正式目录
2. 更新 `YgsAssets` 中的路径常量
3. 创建专用组件简化使用

### 性能监控
定期检查：
- 资源文件大小和加载性能
- 未使用的资源文件清理
- 图片格式优化（PNG vs SVG vs WebP）