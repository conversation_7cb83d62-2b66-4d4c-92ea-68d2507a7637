import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/services/dynamic_color_service.dart';

/// 混合玻璃拟态主题卡片 - Glassmorphism + Floor Fade结合方案
/// 最佳实践：玻璃图标 + 底部自然淡出的完美结合
class HybridGlassThemeCard extends ConsumerStatefulWidget {
  final String title;
  final String subtitle;
  final String iconPath;
  final String backgroundImagePath;
  final VoidCallback? onTap;

  const HybridGlassThemeCard({
    super.key,
    required this.title,
    required this.subtitle,
    required this.iconPath,
    required this.backgroundImagePath,
    this.onTap,
  });

  @override
  ConsumerState<HybridGlassThemeCard> createState() => _HybridGlassThemeCardState();
}

class _HybridGlassThemeCardState extends ConsumerState<HybridGlassThemeCard> {
  final DynamicColorService _colorService = DynamicColorService();
  ColorScheme? _colorScheme;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _extractDynamicColors();
  }

  /// 异步提取动态颜色用于主题化
  Future<void> _extractDynamicColors() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final imageProvider = AssetImage(widget.backgroundImagePath);
      final colorScheme = await _colorService.extractColorSchemeFromImage(
        imageProvider: imageProvider,
        cacheKey: widget.title,
        brightness: Brightness.dark,
      );

      setState(() {
        _colorScheme = colorScheme;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _error = '主色提取失败: $e';
      });
      debugPrint('HybridGlassThemeCard: 主色提取失败 - ${widget.title}: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      child: Container(
        width: 345.w,
        height: 240.h,
        child: Stack(
          children: [
            // 完整卡片容器
            Container(
              width: 345.w,
              height: 240.h,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16.r),
              ),
              clipBehavior: Clip.hardEdge,
              child: Stack(
                children: [
                  // 背景图片层
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16.r),
                        image: DecorationImage(
                          image: AssetImage(widget.backgroundImagePath),
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  ),
                  
                  // Floor Fade底部渐变层
                  Positioned.fill(
                    child: _buildFloorFadeOverlay(),
                  ),
                  
                  // 超轻量级玻璃图标
                  Positioned(
                    left: 36.w,
                    top: 28.h,
                    child: _buildUltraLightGlassIcon(),
                  ),
                  
                  // 主标题
                  Positioned(
                    left: 24.w,
                    right: 24.w,
                    bottom: 44.h,
                    child: _buildMainTitle(),
                  ),
                  
                  // 副标题（已有X个故事）- 替代右下角指示器
                  Positioned(
                    right: 16.w,
                    bottom: 16.h,
                    child: _buildStoryCount(),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建Floor Fade渐变叠加层
  /// 保持上半部分照片完整性，底部自然淡出
  Widget _buildFloorFadeOverlay() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.r),
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            // 上半部分：完全透明，保持照片原貌
            Colors.transparent,
            Colors.transparent,
            
            // 中部：开始渐变，使用动态主色
            _getFloorFadeColor().withValues(alpha: 0.05),
            
            // 底部：较深的渐变，确保文字可读性
            _getFloorFadeColor().withValues(alpha: 0.65),
            _getFloorFadeColor().withValues(alpha: 0.8),
          ],
          stops: const [0.0, 0.35, 0.55, 0.8, 1.0],
        ),
      ),
    );
  }

  /// 获取Floor Fade渐变颜色
  Color _getFloorFadeColor() {
    if (_isLoading || _error != null || _colorScheme == null) {
      return Colors.black; // 降级：使用纯黑色
    }

    // 使用提取主色的深色变体
    final primaryColor = _colorScheme!.primary;
    final hsl = HSLColor.fromColor(primaryColor);
    return hsl.withLightness(0.12).toColor(); // 非常深的主色变体
  }

  /// 构建纯净原始图标
  Widget _buildIconByPath(String iconPath) {
    return Image.asset(
      iconPath,
      width: 40.w,
      height: 40.w,
      color: Colors.white, // 白色着色
      fit: BoxFit.contain,
      filterQuality: FilterQuality.high,
      errorBuilder: (context, error, stackTrace) {
        return Icon(
          Icons.palette_outlined,
          color: Colors.white,
          size: 32.w,
        );
      },
    );
  }

  /// 构建清透玻璃拟态图标
  /// 全新设计：真正的玻璃质感
  Widget _buildUltraLightGlassIcon() {
    return Container(
      width: 64.w,
      height: 64.w,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16.r),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 8.0, sigmaY: 8.0),
          child: Container(
            decoration: BoxDecoration(
              // 真正的玻璃质感：纯透明背景 + 微妙高光
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.white.withValues(alpha: 0.15), // 左上高光
                  Colors.white.withValues(alpha: 0.02), // 右下暗部
                ],
              ),
              borderRadius: BorderRadius.circular(16.r),
              // 极细玻璃边缘
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.2),
                width: 0.8.w,
              ),
            ),
            child: Center(
              child: _buildIconByPath(widget.iconPath),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建主标题
  Widget _buildMainTitle() {
    return Text(
      widget.title,
      style: TextStyle(
        fontFamily: 'PingFang SC',
        fontSize: 30.sp,
        fontWeight: FontWeight.w700,
        color: Colors.white,
        height: 1.0,
        letterSpacing: -0.3.w,
        shadows: [
          Shadow(
            offset: Offset(0, 2.h),
            blurRadius: 4.r,
            color: Colors.black.withValues(alpha: 0.4),
          ),
        ],
      ),
    );
  }

  /// 构建故事数量信息
  Widget _buildStoryCount() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Text(
        widget.subtitle,
        style: TextStyle(
          fontFamily: 'PingFang SC',
          fontSize: 14.sp, // 从12.sp增大到14.sp
          fontWeight: FontWeight.w500,
          color: Colors.white.withValues(alpha: 0.9),
          shadows: [
            Shadow(
              offset: Offset(0, 1.h),
              blurRadius: 2.r,
              color: Colors.black.withValues(alpha: 0.3),
            ),
          ],
        ),
      ),
    );
  }


  @override
  void dispose() {
    super.dispose();
  }
}