import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { BookmarksController } from "./bookmarks.controller";
import { BookmarksService } from "./bookmarks.service";
import { Bookmark } from "./entities/bookmark.entity";
import { Story } from "../stories/entities/story.entity";
import { User } from "../users/entities/user.entity";
import { AuthModule } from "../auth/auth.module";

/**
 * 收藏模块
 * 提供故事收藏的完整功能
 */
@Module({
  imports: [TypeOrmModule.forFeature([Bookmark, Story, User]), AuthModule],
  controllers: [BookmarksController],
  providers: [BookmarksService],
  exports: [BookmarksService],
})
export class BookmarksModule {}
