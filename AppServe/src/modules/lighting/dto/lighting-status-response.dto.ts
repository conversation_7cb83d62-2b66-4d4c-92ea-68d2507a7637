import { ApiProperty } from "@nestjs/swagger";

export class LightingStatusResponseDto {
  @ApiProperty({
    description: "是否已点亮",
    example: true,
  })
  isLighted: boolean;

  @ApiProperty({
    description: "点亮者用户ID",
    example: "uuid-string",
    required: false,
  })
  lighterId?: string;

  @ApiProperty({
    description: "点亮者昵称",
    example: "小明",
    required: false,
  })
  lighterNickname?: string;

  @ApiProperty({
    description: "点亮时间",
    example: "2025-07-08T15:30:00.000Z",
    required: false,
  })
  lightedAt?: Date;

  @ApiProperty({
    description: "当前用户是否可以申请点亮",
    example: true,
  })
  canApplyLighting: boolean;

  @ApiProperty({
    description: "不能申请的原因",
    example: "您已点亮过该用户的其他人物",
    required: false,
  })
  cannotApplyReason?: string;

  @ApiProperty({
    description: "当前用户的申请状态",
    enum: ["none", "pending", "confirmed", "rejected"],
    example: "none",
  })
  userRequestStatus: string;
}
