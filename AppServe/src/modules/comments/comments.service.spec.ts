/* eslint-disable @typescript-eslint/no-explicit-any, @typescript-eslint/no-unused-vars */
import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import {
  NotFoundException,
  ForbiddenException,
  BadRequestException,
} from "@nestjs/common";
import type { Repository } from "typeorm";

import { CommentsService } from "./comments.service";
import { Comment } from "./entities/comment.entity";
import { CommentLike } from "./entities/comment-like.entity";
import { Story } from "../stories/entities/story.entity";
import { User } from "../users/entities/user.entity";
import type {
  CreateCommentDto,
  UpdateCommentDto,
  CommentQueryDto,
} from "./dto";
import {
  CommentStatus,
  CommentType,
  CommentSortBy,
} from "./entities/comment.entity";

describe("CommentsService - 企业级单元测试", () => {
  let service: CommentsService;
  let mockCommentRepository: jest.Mocked<Repository<Comment>>;
  let mockCommentLikeRepository: jest.Mocked<Repository<CommentLike>>;
  let mockStoryRepository: jest.Mocked<Repository<Story>>;
  let mockUserRepository: jest.Mocked<Repository<User>>;

  // 测试数据工厂
  const mockUser = {
    id: "user-001",
    username: "testuser",
    nickname: "测试用户",
    isActive: true,
  };

  const mockStory = {
    id: "story-001",
    title: "测试故事",
    userId: "author-001",
    user: { id: "author-001", nickname: "故事作者" },
    status: "published",
    permissionLevel: "public",
  };

  const mockComment = {
    id: "comment-001",
    storyId: "story-001",
    userId: "user-001",
    parentId: null,
    content: "这是一条测试评论",
    type: CommentType.TEXT,
    status: CommentStatus.PUBLISHED,
    likes: 5,
    replies: 2,
    level: 0,
    createdAt: new Date(),
    updatedAt: new Date(),
    user: mockUser,
    story: mockStory,
  };

  const mockReply = {
    id: "reply-001",
    storyId: "story-001",
    userId: "user-002",
    parentId: "comment-001",
    content: "这是一条回复",
    type: CommentType.TEXT,
    status: CommentStatus.PUBLISHED,
    likes: 1,
    replies: 0,
    level: 1,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CommentsService,
        {
          provide: getRepositoryToken(Comment),
          useValue: {
            findOne: jest.fn(),
            find: jest.fn(),
            save: jest.fn(),
            create: jest.fn(),
            update: jest.fn(),
            createQueryBuilder: jest.fn().mockReturnValue({
              leftJoinAndSelect: jest.fn().mockReturnThis(),
              where: jest.fn().mockReturnThis(),
              andWhere: jest.fn().mockReturnThis(),
              orderBy: jest.fn().mockReturnThis(),
              skip: jest.fn().mockReturnThis(),
              take: jest.fn().mockReturnThis(),
              getManyAndCount: jest.fn(),
              getOne: jest.fn(),
              getCount: jest.fn(),
            }),
          },
        },
        {
          provide: getRepositoryToken(CommentLike),
          useValue: {
            findOne: jest.fn(),
            save: jest.fn(),
            create: jest.fn(),
            delete: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(Story),
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(User),
          useValue: {
            findOne: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<CommentsService>(CommentsService);
    mockCommentRepository = module.get(getRepositoryToken(Comment));
    mockCommentLikeRepository = module.get(getRepositoryToken(CommentLike));
    mockStoryRepository = module.get(getRepositoryToken(Story));
    mockUserRepository = module.get(getRepositoryToken(User));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("createComment - 创建评论", () => {
    const createCommentDto: CreateCommentDto = {
      storyId: "story-001",
      content: "这是一条新评论",
      type: CommentType.TEXT,
    };

    it("应该成功创建评论", async () => {
      // Arrange
      mockStoryRepository.findOne.mockResolvedValue(mockStory as any);
      mockUserRepository.findOne.mockResolvedValue(mockUser as any);
      mockCommentRepository.create.mockReturnValue(mockComment as any);
      mockCommentRepository.save.mockResolvedValue(mockComment as any);

      // Act
      const result = await service.createComment(createCommentDto, "user-001");

      // Assert
      expect(mockStoryRepository.findOne).toHaveBeenCalledWith({
        where: { id: "story-001" },
        relations: ["user"],
      });
      expect(mockCommentRepository.create).toHaveBeenCalledWith({
        storyId: "story-001",
        userId: "user-001",
        content: "这是一条新评论",
        type: CommentType.TEXT,
        level: 0,
        parentId: null,
      });
      expect(result).toEqual(
        expect.objectContaining({
          id: "comment-001",
          content: "这是一条新评论",
        }),
      );
    });

    it("当故事不存在时应该抛出NotFoundException", async () => {
      // Arrange
      mockStoryRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.createComment(createCommentDto, "user-001"),
      ).rejects.toThrow(NotFoundException);
      expect(mockStoryRepository.findOne).toHaveBeenCalled();
    });

    it("应该正确创建回复评论", async () => {
      // Arrange
      const replyDto: CreateCommentDto = {
        ...createCommentDto,
        parentId: "comment-001",
      };
      const mockParentComment = { ...mockComment, level: 0 };

      mockStoryRepository.findOne.mockResolvedValue(mockStory as any);
      mockCommentRepository.findOne.mockResolvedValue(mockParentComment as any);
      mockCommentRepository.create.mockReturnValue(mockReply as any);
      mockCommentRepository.save.mockResolvedValue(mockReply as any);

      // Act
      const result = await service.createComment(replyDto, "user-002");

      // Assert
      expect(mockCommentRepository.findOne).toHaveBeenCalledWith({
        where: { id: "comment-001" },
      });
      expect(mockCommentRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          parentId: "comment-001",
          level: 1,
        }),
      );
    });

    it("当父评论不存在时应该抛出NotFoundException", async () => {
      // Arrange
      const replyDto: CreateCommentDto = {
        ...createCommentDto,
        parentId: "non-existent",
      };

      mockStoryRepository.findOne.mockResolvedValue(mockStory as any);
      mockCommentRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(service.createComment(replyDto, "user-001")).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe("getComments - 获取评论列表", () => {
    const queryDto: CommentQueryDto = {
      page: 1,
      limit: 10,
      sortBy: CommentSortBy.CREATED_DESC,
    };

    it("应该成功获取评论列表", async () => {
      // Arrange
      const mockQueryBuilder = mockCommentRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        [mockComment],
        1,
      ]);

      // Act
      const result = await service.getComments("story-001", queryDto);

      // Assert
      expect(mockCommentRepository.createQueryBuilder).toHaveBeenCalled();
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledTimes(2);
      expect(result).toEqual({
        data: expect.arrayContaining([
          expect.objectContaining({
            id: "comment-001",
          }),
        ]),
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      });
    });

    it("应该正确应用状态筛选", async () => {
      // Arrange
      const queryWithStatus = { ...queryDto, status: CommentStatus.PUBLISHED };
      const mockQueryBuilder = mockCommentRepository.createQueryBuilder();

      // Act
      await service.getComments("story-001", queryWithStatus);

      // Assert
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "comment.status = :status",
        { status: CommentStatus.PUBLISHED },
      );
    });

    it("应该正确应用用户筛选", async () => {
      // Arrange
      const queryWithUser = { ...queryDto, userId: "user-001" };
      const mockQueryBuilder = mockCommentRepository.createQueryBuilder();

      // Act
      await service.getComments("story-001", queryWithUser);

      // Assert
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "comment.userId = :userId",
        { userId: "user-001" },
      );
    });
  });

  describe("updateComment - 更新评论", () => {
    const updateDto: UpdateCommentDto = {
      content: "更新后的评论内容",
    };

    it("应该成功更新评论", async () => {
      // Arrange
      mockCommentRepository.findOne.mockResolvedValue(mockComment as any);
      const updatedComment = { ...mockComment, content: "更新后的评论内容" };
      mockCommentRepository.save.mockResolvedValue(updatedComment as any);

      // Act
      const result = await service.updateComment(
        "comment-001",
        updateDto,
        "user-001",
      );

      // Assert
      expect(mockCommentRepository.findOne).toHaveBeenCalledWith({
        where: { id: "comment-001" },
        relations: ["user", "story"],
      });
      expect(result.content).toBe("更新后的评论内容");
    });

    it("当评论不存在时应该抛出NotFoundException", async () => {
      // Arrange
      mockCommentRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.updateComment("non-existent", updateDto, "user-001"),
      ).rejects.toThrow(NotFoundException);
    });

    it("当用户不是评论作者时应该抛出ForbiddenException", async () => {
      // Arrange
      mockCommentRepository.findOne.mockResolvedValue(mockComment as any);

      // Act & Assert
      await expect(
        service.updateComment("comment-001", updateDto, "other-user"),
      ).rejects.toThrow(ForbiddenException);
    });

    it("当评论已删除时应该抛出BadRequestException", async () => {
      // Arrange
      const deletedComment = { ...mockComment, status: CommentStatus.DELETED };
      mockCommentRepository.findOne.mockResolvedValue(deletedComment as any);

      // Act & Assert
      await expect(
        service.updateComment("comment-001", updateDto, "user-001"),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe("deleteComment - 删除评论", () => {
    it("应该成功删除评论", async () => {
      // Arrange
      mockCommentRepository.findOne.mockResolvedValue(mockComment as any);
      const deletedComment = { ...mockComment, status: CommentStatus.DELETED };
      mockCommentRepository.save.mockResolvedValue(deletedComment as any);

      // Act
      const result = await service.deleteComment("comment-001", "user-001");

      // Assert
      expect(result.status).toBe(CommentStatus.DELETED);
    });

    it("当评论不存在时应该抛出NotFoundException", async () => {
      // Arrange
      mockCommentRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.deleteComment("non-existent", "user-001"),
      ).rejects.toThrow(NotFoundException);
    });

    it("当用户不是评论作者时应该抛出ForbiddenException", async () => {
      // Arrange
      mockCommentRepository.findOne.mockResolvedValue(mockComment as any);

      // Act & Assert
      await expect(
        service.deleteComment("comment-001", "other-user"),
      ).rejects.toThrow(ForbiddenException);
    });
  });

  describe("toggleCommentLike - 切换评论点赞", () => {
    it("应该成功添加点赞", async () => {
      // Arrange
      mockCommentRepository.findOne.mockResolvedValue(mockComment as any);
      mockCommentLikeRepository.findOne.mockResolvedValue(null);
      mockCommentLikeRepository.create.mockReturnValue({} as any);
      mockCommentLikeRepository.save.mockResolvedValue({} as any);
      mockCommentRepository.save.mockResolvedValue({
        ...mockComment,
        likes: 6,
      } as any);

      // Act
      const result = await service.toggleCommentLike("comment-001", "user-002");

      // Assert
      expect(result).toEqual({
        liked: true,
        likesCount: 6,
      });
    });

    it("应该成功取消点赞", async () => {
      // Arrange
      const existingLike = { id: "like-001", userId: "user-002" };
      mockCommentRepository.findOne.mockResolvedValue(mockComment as any);
      mockCommentLikeRepository.findOne.mockResolvedValue(existingLike as any);
      mockCommentRepository.save.mockResolvedValue({
        ...mockComment,
        likes: 4,
      } as any);

      // Act
      const result = await service.toggleCommentLike("comment-001", "user-002");

      // Assert
      expect(mockCommentLikeRepository.delete).toHaveBeenCalledWith("like-001");
      expect(result).toEqual({
        liked: false,
        likesCount: 4,
      });
    });

    it("当评论不存在时应该抛出NotFoundException", async () => {
      // Arrange
      mockCommentRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.toggleCommentLike("non-existent", "user-001"),
      ).rejects.toThrow(NotFoundException);
    });

    it("用户不能给自己的评论点赞", async () => {
      // Arrange
      mockCommentRepository.findOne.mockResolvedValue(mockComment as any);

      // Act & Assert
      await expect(
        service.toggleCommentLike("comment-001", "user-001"),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe("getCommentReplies - 获取评论回复", () => {
    const queryDto: CommentQueryDto = {
      page: 1,
      limit: 5,
      sortBy: CommentSortBy.CREATED_ASC,
    };

    it("应该成功获取评论回复", async () => {
      // Arrange
      const mockQueryBuilder = mockCommentRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        [mockReply],
        1,
      ]);

      // Act
      const result = await service.getCommentReplies("comment-001", queryDto);

      // Assert
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        "comment.parentId = :parentId",
        { parentId: "comment-001" },
      );
      expect(result.data).toHaveLength(1);
    });
  });

  describe("私有方法测试", () => {
    it("buildCommentResponse - 应该正确构建评论响应", () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = (service as any).buildCommentResponse(mockComment);

      expect(result).toEqual(
        expect.objectContaining({
          id: "comment-001",
          content: "这是一条测试评论",
          type: CommentType.TEXT,
          likes: 5,
          replies: 2,
          user: expect.objectContaining({
            id: "user-001",
            nickname: "测试用户",
          }),
        }),
      );
    });

    it("calculateCommentLevel - 应该正确计算评论层级", () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      expect((service as any).calculateCommentLevel(0)).toBe(1);
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      expect((service as any).calculateCommentLevel(1)).toBe(2);
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      expect((service as any).calculateCommentLevel(2)).toBe(2); // 最大层级限制
    });
  });

  describe("错误边界测试", () => {
    it("应该处理数据库连接错误", async () => {
      // Arrange
      mockCommentRepository.createQueryBuilder.mockImplementation(() => {
        throw new Error("数据库连接失败");
      });

      // Act & Assert
      await expect(
        service.getComments("story-001", { page: 1, limit: 10 }),
      ).rejects.toThrow("数据库连接失败");
    });

    it("应该处理无效的评论内容", async () => {
      // Arrange
      const invalidDto: CreateCommentDto = {
        storyId: "story-001",
        content: "", // 空内容
        type: CommentType.TEXT,
      };
      mockStoryRepository.findOne.mockResolvedValue(mockStory as any);

      // Act & Assert
      await expect(
        service.createComment(invalidDto, "user-001"),
      ).rejects.toThrow(BadRequestException);
    });
  });
});
