import 'package:dartz/dartz.dart';
import '../../domain/entities/story_entity.dart';
import '../datasources/story_remote_datasource.dart';
import '../datasources/story_local_datasource.dart';

/// 故事仓库实现 - 高性能缓存策略
class StoryRepositoryImpl {
  final StoryRemoteDataSource _remoteDataSource;
  final StoryLocalDataSource _localDataSource;

  const StoryRepositoryImpl(
    this._remoteDataSource,
    this._localDataSource,
  );

  /// 获取故事列表 - 实现分页、缓存和预加载
  Future<Either<String, StoryListResponse>> getStories({
    int page = 1,
    int limit = 10,
    String? category,
    bool useCache = true,
  }) async {
    try {
      // 优先从缓存获取
      if (useCache && page == 1) {
        final cachedStories = await _localDataSource.getCachedStories(
          category: category,
          limit: limit,
        );
        if (cachedStories.isNotEmpty) {
          // 后台刷新缓存
          _refreshCacheInBackground(category: category, limit: limit);
          return Right(StoryListResponse(
            stories: cachedStories,
            totalCount: cachedStories.length,
            page: 1,
            limit: limit,
            hasNextPage: true,
            hasPreviousPage: false,
          ));
        }
      }

      // 从远程获取数据
      final response = await _remoteDataSource.getStories(
        page: page,
        limit: limit,
        category: category,
      );

      // 缓存第一页数据
      if (page == 1) {
        await _localDataSource.cacheStories(response.stories, category: category);
      }

      return Right(response);
    } catch (e) {
      return Left('获取故事列表失败: ${e.toString()}');
    }
  }

  /// 获取公开故事列表
  Future<Either<String, StoryListResponse>> getPublicStories({
    int page = 1,
    int limit = 10,
  }) async {
    try {
      final response = await _remoteDataSource.getPublicStories(
        page: page,
        limit: limit,
      );
      return Right(response);
    } catch (e) {
      return Left('获取公开故事列表失败: ${e.toString()}');
    }
  }

  /// 点赞故事
  Future<Either<String, bool>> likeStory(String storyId) async {
    try {
      await _remoteDataSource.likeStory(storyId);
      // 更新本地缓存
      await _localDataSource.updateStoryLikeStatus(storyId, true);
      return const Right(true);
    } catch (e) {
      return Left('点赞失败: ${e.toString()}');
    }
  }

  /// 后台刷新缓存
  Future<void> _refreshCacheInBackground({
    String? category,
    int limit = 10,
  }) async {
    try {
      final response = await _remoteDataSource.getStories(
        page: 1,
        limit: limit,
        category: category,
      );
      await _localDataSource.cacheStories(response.stories, category: category);
    } catch (e) {
      // 静默处理错误
      print('后台缓存刷新失败: $e');
    }
  }
}