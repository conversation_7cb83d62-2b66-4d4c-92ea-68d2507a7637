import type {
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from "@nestjs/common";
import { Injectable } from "@nestjs/common";
import type { Observable } from "rxjs";
import { map } from "rxjs/operators";

/**
 * 企业级API响应标准接口
 */
export interface ApiResponse<T> {
  code: number;
  message: string;
  data?: T;
  timestamp: number;
}

/**
 * 企业级响应转换拦截器
 *
 * 功能特性：
 * - 统一响应格式
 * - 简洁数据结构
 * - 符合RESTful标准
 * - 避免过度嵌套
 */
@Injectable()
export class TransformInterceptor<T>
  implements NestInterceptor<T, ApiResponse<T>>
{
  intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Observable<ApiResponse<T>> {
    return next.handle().pipe(
      map((data) => {
        // 如果控制器已经返回了标准格式，直接使用
        if (data && typeof data === "object" && "code" in data) {
          return {
            ...data,
            timestamp: Date.now(),
          };
        }

        // 如果控制器返回了自定义格式，保持原格式
        if (data && typeof data === "object" && "success" in data) {
          return {
            ...data,
            timestamp: Date.now(),
          };
        }

        // 默认包装
        return {
          code: 200,
          message: "success",
          data,
          timestamp: Date.now(),
        };
      }),
    );
  }
}
