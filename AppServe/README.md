# YGS (有故事) 后端服务

> 基于真实社交关系的故事分享平台后端服务  
> **技术栈**: NestJS 11 + Fastify 5 + PostgreSQL + Redis + OSS | **版本**: v1.0.0

## 🚀 快速开始

```bash
# 1. 环境配置（混合模式：本地Redis + 阿里云RDS）
npm run env:dev && npm run docker:start

# 2. 数据库初始化
npm run migration:run && npm run seed:all

# 3. 启动开发服务器  
npm run start:dev

# 4. 验证服务
curl http://localhost:3000/api/v1/health
open http://localhost:3000/api/docs
```

## ✨ 核心特性

- 🎯 **人物点亮系统** - 通过手机验证建立真实社交连接
- 🏗️ **企业级架构** - 高并发、可扩展的微服务架构
- 🔐 **安全认证体系** - JWT双令牌 + 多层防护机制
- 📁 **智能存储** - OSS + CDN + 安全访问控制
- 📱 **智能短信** - 4位验证码 + 设备指纹 + VIP测试
- 🔒 **类型安全** - 零any使用的严格TypeScript体系

## 📚 核心文档导航

> **重要**: 所有核心技术文档已整理至 `/docs/` 目录，按功能模块精准分类

| 文档                                                             | 描述                                                             | 适用场景                                 |
| ---------------------------------------------------------------- | ---------------------------------------------------------------- | ---------------------------------------- |
| 🛠️ [**开发部署指南**](docs/DEVELOPMENT.md)                       | 环境搭建、启动命令、部署流程、问题解决                           | 新人入门、环境配置、部署操作             |
| 🔧 [**开发环境配置指南**](docs/DEVELOPMENT_ENVIRONMENT_SETUP.md) | 混合模式配置、本地服务+云端资源、快速启动指南                    | 环境初始化、开发调试、前后端联调         |
| 🏗️ [**架构技术文档**](docs/ARCHITECTURE.md)                      | 系统架构、核心技术、OSS+CDN、手机验证、会话管理、安全机制        | 技术深入、架构理解、认证系统、特性开发   |
| 📁 [**代码结构说明**](docs/CODE_STRUCTURE.md)                    | 目录结构、模块功能、文件路径、代码规范                           | 代码导航、模块理解、开发规范             |
| 🔗 [**API接口规范**](../对接文档/API接口文档.md)                  | 122个接口完整规范、企业级标准、前后端对接权威依据                 | 前后端对接、技术实现、协作规范           |
| 🧪 [**企业级测试完整指南**](docs/COMPREHENSIVE_TESTING_GUIDE.md) | 测试金字塔、单元测试、集成测试、E2E测试、质量标准、技术细节      | 完整测试方案、质量体系建设、测试执行指导 |
| 🎯 [**前后端通用测试标准**](../CLAUDE.md#🧪-企业级测试开发标准强制执行) | 企业级测试架构、Mock策略、前后端测试规范、质量检查清单           | 测试架构设计、前后端测试标准、质量保证   |
| 📱 [**阿里云短信配置**](docs/ALIYUN_SMS_SETUP.md)                | 短信服务集成、VIP账号、智能路由配置、4位验证码、设备指纹安全验证 | 短信功能配置、真实短信测试、安全机制验证 |
| 🌱 [**企业级数据生成指南**](docs/DATA_SEEDING_GUIDE.md)         | 完整测试数据生成、6用户角色、人物点亮业务场景、前后端联调       | 前后端联调、接口测试、功能验证、演示展示 |
| 📚 [**文档管理标准**](../CLAUDE.md#-全项目文档管理标准)          | 全项目文档规范、合并规则、同步机制                               | 团队协作、规范执行、质量保证             |
| 🧪 [**test/ 测试目录**](docs/COMPREHENSIVE_TESTING_GUIDE.md#🧪-test-目录架构说明)                  | 企业级测试架构，配置文件，测试脚本，完整测试环境                  | 测试环境配置、测试执行、质量验证         |

### 📖 文档使用指南

**开发流程**: 环境配置 → 架构理解 → 代码开发 → 测试验证 → 联调部署

1. **🔧 环境配置** → [开发环境配置指南](docs/DEVELOPMENT_ENVIRONMENT_SETUP.md) + [开发部署指南](docs/DEVELOPMENT.md)
2. **🏗️ 架构理解** → [架构技术文档](docs/ARCHITECTURE.md) + [代码结构说明](docs/CODE_STRUCTURE.md)  
3. **🔗 API开发** → [API接口规范](../对接文档/API接口文档.md) (122个接口权威标准)
4. **🧪 测试开发** → [企业级测试完整指南](docs/COMPREHENSIVE_TESTING_GUIDE.md)
5. **🌱 联调调试** → [企业级数据生成指南](docs/DATA_SEEDING_GUIDE.md) + [阿里云短信配置](docs/ALIYUN_SMS_SETUP.md)

> 📊 **项目进度状态**: 查看 [项目执行文档](../项目文档/项目执行-v1.0.0.md)  
> 📋 **团队协作规范**: 遵循 [CLAUDE.md开发规范](../CLAUDE.md)

## 🔧 开发环境要求

### 🛠️ 技术栈约束（强制遵守）
- **框架**: NestJS 11 + Fastify 5 (严禁随意更改)  
- **数据库**: 阿里云 RDS PostgreSQL (禁止本地PostgreSQL)
- **缓存**: Redis (Docker容器运行)
- **Node.js**: ≥22.16.0 | **npm**: ≥11.0.0

### 🏗️ 混合模式架构
```
本地环境: NestJS服务 + Docker Redis
云端资源: 阿里云RDS + OSS存储 + CDN + 短信服务
```

### ⚡ 质量标准（强制执行）
- **TypeScript**: 零any使用，严格类型安全
- **ESLint**: 0错误0警告，100%合规
- **测试覆盖率**: ≥80% (目标85%+)
- **超时管理**: 服务启动≤10秒，数据库连接≤15秒

## 🔧 常用开发命令

### 环境管理
```bash
npm run env:dev          # 切换开发环境（混合模式）
npm run env:check        # 查看当前环境配置
npm run docker:start     # 启动混合环境（推荐）
npm run docker:redis     # 仅启动Redis容器
npm run docker:stop      # 停止Docker服务
npm run rds:test         # 测试RDS连接
```

### 数据库操作
```bash
npm run migration:run    # 执行数据库迁移
npm run seed:all         # 生成完整测试数据
npm run seed:clean       # 清理所有测试数据
npm run seed:validate    # 验证数据完整性
```

### 开发调试
```bash
npm run start:dev        # 开发模式（热重载）
npm run start:debug      # 调试模式启动
npm run health:check     # 服务健康检查
```

### 质量检查
```bash
npm run lint             # ESLint检查
npm run type-check       # TypeScript类型检查
npm run test:unit        # 单元测试+覆盖率
npm run test:integration # 集成测试
npm run test:e2e         # 端到端测试
```

## 📑 API接口文档

### Swagger交互式文档
```bash
# 访问在线API文档
open http://localhost:3000/api/docs

# 获取OpenAPI规范
curl http://localhost:3000/api/docs-json > api-spec.json
```

### 前后端联调测试账号
```bash
# 生成完整测试数据
npm run seed:all

# 可用测试账号
# 📝 故事作者: 13800138001 / Test123456!
# 👤 点亮用户: 13800138002 / Test123456!  
# 👁️ 普通用户: 13800138003 / Test123456!
# 🔧 VIP测试: 16675158665 / Test123456!
```

## 🏗️ 技术架构

| 技术组件 | 版本 | 用途 | 配置文档 |
|---------|------|------|----------|
| NestJS | 11 | 后端框架 | [架构文档](docs/ARCHITECTURE.md) |
| Fastify | 5 | HTTP引擎 | [架构文档](docs/ARCHITECTURE.md) |
| PostgreSQL | - | RDS数据库 | [环境配置](docs/DEVELOPMENT_ENVIRONMENT_SETUP.md) |
| Redis | - | 缓存/会话 | [环境配置](docs/DEVELOPMENT_ENVIRONMENT_SETUP.md) |
| 阿里云OSS | - | 对象存储 | [架构文档](docs/ARCHITECTURE.md) |

## 📚 文档管理规范

### 🎯 核心原则
- **统一存放**: 所有文档统一在 `docs/` 目录管理
- **合并优先**: 新文档优先与核心文档合并，避免冗余
- **实时同步**: 代码变更强制同步更新相关文档
- **链接验证**: README导航表格必须保持链接有效性

### 📋 核心文档清单
- `ARCHITECTURE.md` - 系统架构和技术文档
- `CODE_STRUCTURE.md` - 代码结构和规范说明  
- `DEVELOPMENT.md` / `DEVELOPMENT_ENVIRONMENT_SETUP.md` - 环境配置和部署
- `COMPREHENSIVE_TESTING_GUIDE.md` - 测试指南和质量标准
- `../对接文档/API接口文档.md` - API接口规范统一管理

## 🔄 文档联动更新机制

### ⚡ 强制联动触发场景

| 代码变更类型 | 必须更新的文档 | 联动检查清单 |
|-------------|---------------|-------------|
| **新增API接口** | `../对接文档/API接口文档.md` | ✅ 接口规范 ✅ Swagger注释 ✅ 测试用例 |
| **修改数据库结构** | `ARCHITECTURE.md` + `CODE_STRUCTURE.md` | ✅ 实体关系图 ✅ 迁移脚本说明 ✅ 业务影响 |
| **新增/修改模块** | `CODE_STRUCTURE.md` + `ARCHITECTURE.md` | ✅ 目录结构 ✅ 模块职责 ✅ 依赖关系 |
| **环境配置变更** | `DEVELOPMENT_ENVIRONMENT_SETUP.md` | ✅ 环境变量 ✅ 启动命令 ✅ 故障排除 |
| **测试文件变更** | `COMPREHENSIVE_TESTING_GUIDE.md` | ✅ test/目录结构 ✅ 测试覆盖率 ✅ 测试命令 |
| **部署脚本修改** | `DEVELOPMENT.md` | ✅ 部署流程 ✅ 环境切换 ✅ 配置说明 |

### 📋 联动更新检查清单

#### 🔧 开发阶段 (每次代码提交前)
```bash
# 1. 代码质量检查
npm run lint && npm run type-check

# 2. 文档同步检查
- [ ] 相关文档已同步更新
- [ ] README导航表格链接有效
- [ ] 代码注释与实际功能一致
- [ ] API文档与代码实现对应

# 3. 测试文档同步
- [ ] test/目录结构变更已更新到测试文档
- [ ] 新增测试文件已添加到覆盖率统计
- [ ] 测试命令说明保持最新
```

#### 🧪 测试阶段 (测试开发完成后)
根据 [CLAUDE.md测试文档同步流程](../CLAUDE.md#🧪-测试开发完成后强制文档同步流程) 执行：
1. **测试文档**: 更新 `COMPREHENSIVE_TESTING_GUIDE.md` 中的test/目录结构和测试状态
2. **README文档**: 确保测试导航链接有效
3. **项目文档**: 同步更新项目执行进度
4. **一致性验证**: 确保所有文档中的数据同步

#### 🚀 发布阶段 (版本发布前)
```bash
# 强制文档一致性检查
- [ ] 所有核心文档版本号统一
- [ ] README文档导航表格100%有效
- [ ] test/目录实际结构与文档描述一致
- [ ] API文档与Swagger输出对应
- [ ] 环境配置文档与实际配置文件同步
```

### 🚨 违规处理机制

**轻微违规** (文档描述不准确、链接失效):
- 立即修复文档内容
- 验证修复效果
- 记录问题到开发日志

**严重违规** (代码变更未同步文档、结构描述错误):
- 立即停止后续开发
- 完成所有相关文档更新
- 执行完整的文档一致性检查
- 通过review后方可继续

### 📊 文档质量监控

**自动化检查** (推荐集成到CI/CD):
```bash
# 链接有效性检查
npm run docs:validate-links

# 文档同步检查  
npm run docs:check-sync

# test/目录结构一致性检查
npm run test:check-docs-sync
```

> 详细规范请查看: [CLAUDE.md文档管理标准](../CLAUDE.md#-全项目文档管理标准)

## 🤝 开发团队

**YGS开发团队** - 专注于创新社交产品的技术实现

---

**最后更新**: 2025-07-28  
**文档版本**: v3.1 (联动机制完善版本)  
**项目版本**: v1.0.0 企业级完整产品
