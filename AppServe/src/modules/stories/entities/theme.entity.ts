import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  OneToMany,
  Index,
} from "typeorm";
import { Story } from "./story.entity";

/**
 * 主题实体
 * 用于故事分类和内容组织
 */
@Entity("themes")
@Index(["sortOrder"]) // 排序索引
@Index(["isActive"]) // 激活状态索引
export class Theme {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ length: 50, unique: true, comment: "主题名称" })
  name: string;

  @Column({ type: "text", nullable: true, comment: "主题描述" })
  description: string;

  @Column({ length: 10, nullable: true, comment: "主题图标(emoji)" })
  icon: string;

  @Column({ length: 7, nullable: true, comment: "主题颜色(HEX)" })
  color: string;

  @Column({ name: "sort_order", default: 0, comment: "排序顺序" })
  sortOrder: number;

  @Column({ name: "is_active", default: true, comment: "是否激活" })
  isActive: boolean;

  @CreateDateColumn({ name: "created_at", comment: "创建时间" })
  createdAt: Date;

  // 关联关系
  @OneToMany(() => Story, (story) => story.theme)
  stories: Story[];

  // 业务方法
  getDisplayInfo() {
    return {
      id: this.id,
      name: this.name,
      icon: this.icon,
      color: this.color,
      description: this.description,
    };
  }
}
