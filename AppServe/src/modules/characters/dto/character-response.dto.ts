import type { Character } from "../entities/character.entity";
import type { ImageService } from "../../image/image.service";

/**
 * 人物响应DTO
 * 用于API返回的人物数据结构
 */
export class CharacterResponseDto {
  id: string;
  name: string;
  description: string;
  avatarUrl: string;
  gender: string;
  relationship: string;
  customRelationship: string;
  isLighted: boolean;
  lightingCount: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;

  // 创建者信息
  creator: {
    id: string;
    nickname: string;
    avatar: string;
    userNumber: string;
  };

  // 关联故事数量
  storiesCount: number;

  // 点亮用户信息（如果已点亮）
  lightingUser?: {
    id: string;
    nickname: string;
    avatar: string;
    userNumber: string;
    lightedAt: Date;
  };

  constructor(character: Character, imageService?: ImageService) {
    this.id = character.id;
    this.name = character.name;
    this.description = character.description;
    this.gender = character.gender;
    this.relationship = character.relationship;
    this.customRelationship = character.customRelationship;
    this.isLighted = character.isLighted;
    this.lightingCount = character.lightingCount;
    this.isActive = character.isActive;
    this.createdAt = character.createdAt;
    this.updatedAt = character.updatedAt;

    // 智能处理人物头像URL - 根据关联故事的权限级别优化访问方式
    if (imageService && character.avatarUrl) {
      // 人物头像的访问级别基于其关联故事的最高权限级别
      const _accessLevel = this.determineCharacterImageAccessLevel(character);
      // 注意：这里需要在实际使用时进行异步处理
      this.avatarUrl = character.avatarUrl; // 临时保持原有逻辑
    } else {
      this.avatarUrl = character.avatarUrl;
    }

    // 填充创建者信息
    if (character.creator) {
      this.creator = {
        id: character.creator.id,
        nickname: character.creator.nickname,
        avatar: character.creator.avatarUrl, // 用户头像通常是公开的
        userNumber: character.creator.userNumber,
      };
    }

    // 填充关联故事数量
    this.storiesCount = character.stories ? character.stories.length : 0;

    // 填充点亮用户信息
    if (character.lightings && character.lightings.length > 0) {
      const lighting = character.lightings[0]; // 假设只有一个点亮用户
      if (lighting.lighterUser) {
        this.lightingUser = {
          id: lighting.lighterUser.id,
          nickname: lighting.lighterUser.nickname,
          avatar: lighting.lighterUser.avatarUrl, // 用户头像通常是公开的
          userNumber: lighting.lighterUser.userNumber,
          lightedAt: lighting.confirmedAt,
        };
      }
    }
  }

  /**
   * 确定人物图片的访问级别
   * 基于人物关联故事的权限级别来决定
   */
  private determineCharacterImageAccessLevel(
    character: Character,
  ): "PUBLIC" | "FRIENDS" | "PRIVATE" {
    // 如果人物关联多个故事，取最宽松的权限级别（优先公开展示）
    if (character.stories && character.stories.length > 0) {
      const hasPublicStory = character.stories.some(
        (story) => story.permissionLevel === "public",
      );
      const hasFriendsStory = character.stories.some(
        (story) => story.permissionLevel === "friends",
      );

      if (hasPublicStory) return "PUBLIC";
      if (hasFriendsStory) return "FRIENDS";
      return "PRIVATE";
    }
    // 默认情况下，人物头像设为好友可见
    return "FRIENDS";
  }
}
