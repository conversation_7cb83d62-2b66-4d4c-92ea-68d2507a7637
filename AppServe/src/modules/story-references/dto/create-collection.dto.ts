import {
  IsString,
  IsEnum,
  IsOptional,
  IsArray,
  IsBoolean,
  IsInt,
  IsUrl,
  ValidateNested,
  MaxLength,
  Min<PERSON>eng<PERSON>,
  Min,
} from "class-validator";
import { Type } from "class-transformer";
import {
  CollectionType,
  CollectionVisibility,
} from "../entities/reference-collection.entity";

class CollectionSettingsDto {
  @IsOptional()
  @IsBoolean({
    message: "自动添加相似内容设置必须是布尔值",
  })
  autoAddSimilar?: boolean = false;

  @IsOptional()
  @IsBoolean({
    message: "新引用通知设置必须是布尔值",
  })
  notifyOnNewReference?: boolean = true;

  @IsOptional()
  @IsBoolean({
    message: "公开贡献设置必须是布尔值",
  })
  allowPublicContribution?: boolean = false;

  @IsOptional()
  @IsBoolean({
    message: "内容审核设置必须是布尔值",
  })
  moderationRequired?: boolean = false;
}

/**
 * 创建引用集合DTO
 */
export class CreateCollectionDto {
  @IsString({
    message: "集合名称必须是字符串",
  })
  @MinLength(1, {
    message: "集合名称不能为空",
  })
  @MaxLength(100, {
    message: "集合名称不能超过100个字符",
  })
  name: string;

  @IsOptional()
  @IsString({
    message: "集合描述必须是字符串",
  })
  @MaxLength(1000, {
    message: "集合描述不能超过1000个字符",
  })
  description?: string;

  @IsOptional()
  @IsEnum(CollectionType, {
    message: "集合类型必须是有效的选项",
  })
  collectionType?: CollectionType = CollectionType.GENERAL;

  @IsOptional()
  @IsEnum(CollectionVisibility, {
    message: "可见性设置必须是有效的选项",
  })
  visibility?: CollectionVisibility = CollectionVisibility.PRIVATE;

  @IsOptional()
  @IsUrl(
    {
      require_protocol: true,
      protocols: ["http", "https"],
    },
    {
      message: "封面图片必须是有效的URL",
    },
  )
  coverImage?: string;

  @IsOptional()
  @IsString({
    message: "颜色主题必须是字符串",
  })
  @MaxLength(50, {
    message: "颜色主题不能超过50个字符",
  })
  colorTheme?: string;

  @IsOptional()
  @IsArray({
    message: "标签必须是数组",
  })
  @IsString({
    each: true,
    message: "每个标签必须是字符串",
  })
  @MaxLength(30, {
    each: true,
    message: "每个标签不能超过30个字符",
  })
  tags?: string[];

  @IsOptional()
  @IsInt({
    message: "排序权重必须是整数",
  })
  @Type(() => Number)
  @Min(0, {
    message: "排序权重不能为负数",
  })
  sortOrder?: number = 0;

  @IsOptional()
  @IsBoolean({
    message: "协作编辑设置必须是布尔值",
  })
  allowCollaboration?: boolean = true;

  @IsOptional()
  @ValidateNested()
  @Type(() => CollectionSettingsDto)
  settings?: CollectionSettingsDto;

  @IsOptional()
  @Type(() => Object)
  metadata?: Record<string, unknown>;
}
