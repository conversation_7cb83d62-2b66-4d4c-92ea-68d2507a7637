import type { Story } from "../entities/story.entity";
import type { ImageService } from "../../image/image.service";

/**
 * 故事响应DTO
 * 用于返回故事信息给前端
 */
export class StoryResponseDto {
  id: string;
  title: string;
  content: string;
  coverImageUrl: string;
  status: number;
  viewCount: number;
  likeCount: number;
  commentCount: number;
  storyDate: Date;
  location: string;
  images: string[];
  permissionLevel: string;
  allowComments: boolean;
  allowLikes: boolean;
  allowSharing: boolean;
  aiSafetyScore: number;
  createdAt: Date;
  updatedAt: Date;

  // 作者信息
  user: {
    id: string;
    nickname: string;
    avatar: string;
    userNumber: string;
  };

  // 主题信息
  theme?: {
    id: string;
    name: string;
    icon: string;
    color: string;
  };

  // 人物信息
  characters: Array<{
    id: string;
    name: string;
    avatar: string;
    isLighted: boolean;
  }>;

  constructor(story: Story, imageService?: ImageService) {
    this.id = story.id;
    this.title = story.title;
    this.content = story.content;
    this.status = story.status;
    this.viewCount = story.viewCount;
    this.likeCount = story.likeCount;
    this.commentCount = story.commentCount;
    this.storyDate = story.storyDate;
    this.location = story.location;
    this.permissionLevel = story.permissionLevel;
    this.allowComments = story.allowComments;
    this.allowLikes = story.allowLikes;
    this.allowSharing = story.allowSharing;
    this.aiSafetyScore = story.aiSafetyScore;
    this.createdAt = story.createdAt;
    this.updatedAt = story.updatedAt;

    // 智能处理图片URL - 根据故事可见性优化访问方式
    if (imageService && story.coverImageUrl) {
      // 根据故事权限级别决定图片访问策略
      const _accessLevel = this.getImageAccessLevel(story.permissionLevel);
      // 注意：这里需要在实际使用时进行异步处理
      this.coverImageUrl = story.coverImageUrl; // 临时保持原有逻辑
    } else {
      this.coverImageUrl = story.coverImageUrl;
    }

    // 优化故事内容图片URLs
    if (imageService && story.images) {
      const _accessLevel = this.getImageAccessLevel(story.permissionLevel);
      // 注意：这里需要在实际使用时进行异步处理
      this.images = story.images; // 临时保持原有逻辑
    } else {
      this.images = story.images;
    }

    // 填充关联数据
    if (story.user) {
      this.user = {
        id: story.user.id,
        nickname: story.user.nickname,
        avatar: story.user.avatarUrl, // 头像通常是公开的，使用CDN直接访问
        userNumber: story.user.userNumber,
      };
    }

    if (story.theme) {
      this.theme = {
        id: story.theme.id,
        name: story.theme.name,
        icon: story.theme.icon,
        color: story.theme.color,
      };
    }

    if (story.characters) {
      this.characters = story.characters.map((character) => ({
        id: character.id,
        name: character.name,
        avatar: character.avatarUrl, // 人物头像根据故事权限级别处理
        isLighted: character.isLighted,
      }));
    }
  }

  /**
   * 根据故事权限级别确定图片访问级别
   */
  private getImageAccessLevel(
    permissionLevel: string,
  ): "PUBLIC" | "FRIENDS" | "PRIVATE" {
    switch (permissionLevel) {
      case "public":
        return "PUBLIC";
      case "friends":
        return "FRIENDS";
      case "private":
        return "PRIVATE";
      default:
        return "PUBLIC";
    }
  }
}
