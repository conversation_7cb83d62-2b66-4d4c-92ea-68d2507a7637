import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import type { ExecutionContext } from "@nestjs/common";
import { ForbiddenException, NotFoundException } from "@nestjs/common";
import { Reflector } from "@nestjs/core";
import {
  StoryAccessGuard,
  StoryEditGuard,
  StoryActionGuard,
} from "./story-access.guard";
import { StoriesService } from "../stories.service";
import { StoryPermissionService } from "../services/story-permission.service";

describe("StoryAccessGuard", () => {
  let guard: StoryAccessGuard;
  let mockStoriesService: jest.Mocked<StoriesService>;
  let mockStoryPermissionService: jest.Mocked<StoryPermissionService>;
  let mockReflector: jest.Mocked<Reflector>;

  // Mock数据
  const mockUser = {
    id: "user-1",
    username: "testuser",
    email: "<EMAIL>",
  };

  const mockStory = {
    id: "story-1",
    title: "测试故事",
    content: "故事内容",
    userId: "user-1",
    coverImageUrl: null,
    status: 2,
    viewCount: 0,
    likeCount: 0,
    themeId: null,
    storyDate: null,
    location: null,
    images: [],
    permissionLevel: "public" as const,
    allowComments: true,
    allowLikes: true,
    isArchived: false,
    tags: [],
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockRequest = {
    params: { id: "story-1" },
    user: mockUser,
    story: undefined,
  };

  // Mock HTTP上下文 - 企业级ExecutionContext Mock
  const mockHttpContext = {
    getRequest: jest.fn().mockReturnValue(mockRequest),
    getResponse: jest.fn(),
    getNext: jest.fn(),
  };

  const mockExecutionContext = {
    switchToHttp: jest.fn().mockReturnValue(mockHttpContext),
    getHandler: jest.fn(),
    getClass: jest.fn(),
    getArgs: jest.fn(),
    getArgByIndex: jest.fn(),
    switchToRpc: jest.fn(),
    switchToWs: jest.fn(),
    getType: jest.fn(),
  } as unknown as ExecutionContext;

  beforeEach(async () => {
    const mockStoriesServiceMethods = {
      findById: jest.fn(),
    };

    const mockStoryPermissionServiceMethods = {
      validateAccess: jest.fn(),
      validateEditAccess: jest.fn(),
      validateLikeAccess: jest.fn(),
      validateCommentAccess: jest.fn(),
      validateShareAccess: jest.fn(),
    };

    const mockReflectorMethods = {
      get: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        StoryAccessGuard,
        {
          provide: StoriesService,
          useValue: mockStoriesServiceMethods,
        },
        {
          provide: StoryPermissionService,
          useValue: mockStoryPermissionServiceMethods,
        },
        {
          provide: Reflector,
          useValue: mockReflectorMethods,
        },
      ],
    }).compile();

    guard = module.get<StoryAccessGuard>(StoryAccessGuard);
    mockStoriesService = module.get(
      StoriesService,
    ) as jest.Mocked<StoriesService>;
    mockStoryPermissionService = module.get(
      StoryPermissionService,
    ) as jest.Mocked<StoryPermissionService>;
    mockReflector = module.get(Reflector) as jest.Mocked<Reflector>;

    // 重置mockRequest
    mockRequest.params = { id: "story-1" };
    mockRequest.user = mockUser;
    mockRequest.story = undefined;

    // 重置Mock状态
    mockHttpContext.getRequest.mockReturnValue(mockRequest);
    (mockExecutionContext.switchToHttp as jest.Mock).mockReturnValue(
      mockHttpContext,
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("canActivate", () => {
    it("应该在有权限访问故事时通过", async () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      mockStoriesService.findById.mockResolvedValue(mockStory as any);
      mockStoryPermissionService.validateAccess.mockResolvedValue(true);

      const result = await guard.canActivate(mockExecutionContext);

      expect(result).toBe(true);
      expect(mockStoriesService.findById).toHaveBeenCalledWith("story-1");
      expect(mockStoryPermissionService.validateAccess).toHaveBeenCalledWith(
        mockStory,
        mockUser,
      );
      expect(mockRequest.story).toBe(mockStory);
    });

    it("应该在故事ID为空时抛出NotFoundException", async () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      mockRequest.params.id = undefined as any;

      await expect(guard.canActivate(mockExecutionContext)).rejects.toThrow(
        new NotFoundException("故事ID不能为空"),
      );
    });

    it("应该在用户未登录时抛出ForbiddenException", async () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      mockRequest.user = undefined as any;

      await expect(guard.canActivate(mockExecutionContext)).rejects.toThrow(
        new ForbiddenException("用户未登录"),
      );
    });

    it("应该在故事不存在时抛出NotFoundException", async () => {
      mockStoriesService.findById.mockRejectedValue(
        new NotFoundException("故事不存在"),
      );

      await expect(guard.canActivate(mockExecutionContext)).rejects.toThrow(
        new NotFoundException("故事不存在"),
      );
    });

    it("应该在无权限访问时抛出ForbiddenException", async () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      mockStoriesService.findById.mockResolvedValue(mockStory as any);
      mockStoryPermissionService.validateAccess.mockResolvedValue(false);

      await expect(guard.canActivate(mockExecutionContext)).rejects.toThrow(
        new ForbiddenException("权限验证失败"),
      );
    });

    it("应该在权限验证出错时抛出ForbiddenException", async () => {
      mockStoriesService.findById.mockRejectedValue(new Error("数据库错误"));

      await expect(guard.canActivate(mockExecutionContext)).rejects.toThrow(
        new ForbiddenException("权限验证失败"),
      );
    });
  });

  it("应该正确注入依赖", () => {
    expect(guard).toBeDefined();
    expect(mockStoriesService).toBeDefined();
    expect(mockStoryPermissionService).toBeDefined();
    expect(mockReflector).toBeDefined();
  });
});

describe("StoryEditGuard", () => {
  let guard: StoryEditGuard;
  let mockStoriesService: jest.Mocked<StoriesService>;
  let mockStoryPermissionService: jest.Mocked<StoryPermissionService>;

  // Mock数据
  const mockUser = {
    id: "user-1",
    username: "testuser",
    email: "<EMAIL>",
  };

  const mockStory = {
    id: "story-1",
    title: "测试故事",
    content: "故事内容",
    userId: "user-1",
    coverImageUrl: null,
    status: 2,
    viewCount: 0,
    likeCount: 0,
    themeId: null,
    storyDate: null,
    location: null,
    images: [],
    permissionLevel: "public" as const,
    allowComments: true,
    allowLikes: true,
    isArchived: false,
    tags: [],
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockRequest = {
    params: { id: "story-1" },
    user: mockUser,
    story: undefined,
  };

  // Mock HTTP上下文
  const mockHttpContext = {
    getRequest: jest.fn().mockReturnValue(mockRequest),
    getResponse: jest.fn(),
    getNext: jest.fn(),
  };

  const mockExecutionContext = {
    switchToHttp: jest.fn().mockReturnValue(mockHttpContext),
    getHandler: jest.fn(),
    getClass: jest.fn(),
    getArgs: jest.fn(),
    getArgByIndex: jest.fn(),
    switchToRpc: jest.fn(),
    switchToWs: jest.fn(),
    getType: jest.fn(),
  } as unknown as ExecutionContext;

  beforeEach(async () => {
    const mockStoriesServiceMethods = {
      findById: jest.fn(),
    };

    const mockStoryPermissionServiceMethods = {
      validateEditAccess: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        StoryEditGuard,
        {
          provide: StoriesService,
          useValue: mockStoriesServiceMethods,
        },
        {
          provide: StoryPermissionService,
          useValue: mockStoryPermissionServiceMethods,
        },
      ],
    }).compile();

    guard = module.get<StoryEditGuard>(StoryEditGuard);
    mockStoriesService = module.get(
      StoriesService,
    ) as jest.Mocked<StoriesService>;
    mockStoryPermissionService = module.get(
      StoryPermissionService,
    ) as jest.Mocked<StoryPermissionService>;

    // 重置mockRequest
    mockRequest.params = { id: "story-1" };
    mockRequest.user = mockUser;
    mockRequest.story = undefined;

    // 重置Mock状态
    mockHttpContext.getRequest.mockReturnValue(mockRequest);
    (mockExecutionContext.switchToHttp as jest.Mock).mockReturnValue(
      mockHttpContext,
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("canActivate", () => {
    it("应该在有编辑权限时通过", async () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      mockStoriesService.findById.mockResolvedValue(mockStory as any);
      mockStoryPermissionService.validateEditAccess.mockResolvedValue(true);

      const result = await guard.canActivate(mockExecutionContext);

      expect(result).toBe(true);
      expect(mockStoriesService.findById).toHaveBeenCalledWith("story-1");
      expect(
        mockStoryPermissionService.validateEditAccess,
      ).toHaveBeenCalledWith(mockStory, mockUser);
      expect(mockRequest.story).toBe(mockStory);
    });

    it("应该在故事ID为空时抛出NotFoundException", async () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      mockRequest.params.id = undefined as any;

      await expect(guard.canActivate(mockExecutionContext)).rejects.toThrow(
        new NotFoundException("故事ID不能为空"),
      );
    });

    it("应该在用户未登录时抛出ForbiddenException", async () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      mockRequest.user = undefined as any;

      await expect(guard.canActivate(mockExecutionContext)).rejects.toThrow(
        new ForbiddenException("用户未登录"),
      );
    });

    it("应该在无编辑权限时抛出ForbiddenException", async () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      mockStoriesService.findById.mockResolvedValue(mockStory as any);
      mockStoryPermissionService.validateEditAccess.mockResolvedValue(false);

      await expect(guard.canActivate(mockExecutionContext)).rejects.toThrow(
        new ForbiddenException("权限验证失败"),
      );
    });

    it("应该在权限验证出错时抛出ForbiddenException", async () => {
      mockStoriesService.findById.mockRejectedValue(new Error("数据库错误"));

      await expect(guard.canActivate(mockExecutionContext)).rejects.toThrow(
        new ForbiddenException("权限验证失败"),
      );
    });
  });

  it("应该正确注入依赖", () => {
    expect(guard).toBeDefined();
    expect(mockStoriesService).toBeDefined();
    expect(mockStoryPermissionService).toBeDefined();
  });
});

describe("StoryActionGuard", () => {
  let guard: StoryActionGuard;
  let mockStoriesService: jest.Mocked<StoriesService>;
  let mockStoryPermissionService: jest.Mocked<StoryPermissionService>;
  let mockReflector: jest.Mocked<Reflector>;

  // Mock数据
  const mockUser = {
    id: "user-1",
    username: "testuser",
    email: "<EMAIL>",
  };

  const mockStory = {
    id: "story-1",
    title: "测试故事",
    content: "故事内容",
    userId: "user-1",
    coverImageUrl: null,
    status: 2,
    viewCount: 0,
    likeCount: 0,
    themeId: null,
    storyDate: null,
    location: null,
    images: [],
    permissionLevel: "public" as const,
    allowComments: true,
    allowLikes: true,
    isArchived: false,
    tags: [],
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockRequest = {
    params: { id: "story-1" },
    user: mockUser,
    story: undefined,
  };

  // Mock HTTP上下文
  const mockHttpContext = {
    getRequest: jest.fn().mockReturnValue(mockRequest),
    getResponse: jest.fn(),
    getNext: jest.fn(),
  };

  const mockExecutionContext = {
    switchToHttp: jest.fn().mockReturnValue(mockHttpContext),
    getHandler: jest.fn(),
    getClass: jest.fn(),
    getArgs: jest.fn(),
    getArgByIndex: jest.fn(),
    switchToRpc: jest.fn(),
    switchToWs: jest.fn(),
    getType: jest.fn(),
  } as unknown as ExecutionContext;

  beforeEach(async () => {
    const mockStoriesServiceMethods = {
      findById: jest.fn(),
    };

    const mockStoryPermissionServiceMethods = {
      validateAccess: jest.fn(),
      validateLikeAccess: jest.fn(),
      validateCommentAccess: jest.fn(),
      validateShareAccess: jest.fn(),
    };

    const mockReflectorMethods = {
      get: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        StoryActionGuard,
        {
          provide: StoriesService,
          useValue: mockStoriesServiceMethods,
        },
        {
          provide: StoryPermissionService,
          useValue: mockStoryPermissionServiceMethods,
        },
        {
          provide: Reflector,
          useValue: mockReflectorMethods,
        },
      ],
    }).compile();

    guard = module.get<StoryActionGuard>(StoryActionGuard);
    mockStoriesService = module.get(
      StoriesService,
    ) as jest.Mocked<StoriesService>;
    mockStoryPermissionService = module.get(
      StoryPermissionService,
    ) as jest.Mocked<StoryPermissionService>;
    mockReflector = module.get(Reflector) as jest.Mocked<Reflector>;

    // 重置mockRequest
    mockRequest.params = { id: "story-1" };
    mockRequest.user = mockUser;
    mockRequest.story = undefined;

    // 重置Mock状态
    mockHttpContext.getRequest.mockReturnValue(mockRequest);
    (mockExecutionContext.switchToHttp as jest.Mock).mockReturnValue(
      mockHttpContext,
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("canActivate", () => {
    it("应该在有点赞权限时通过", async () => {
      mockReflector.get.mockReturnValue("like");
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      mockStoriesService.findById.mockResolvedValue(mockStory as any);
      mockStoryPermissionService.validateLikeAccess.mockResolvedValue(true);

      const result = await guard.canActivate(mockExecutionContext);

      expect(result).toBe(true);
      expect(mockReflector.get).toHaveBeenCalledWith(
        "action",
        mockExecutionContext.getHandler(),
      );
      expect(mockStoriesService.findById).toHaveBeenCalledWith("story-1");
      expect(
        mockStoryPermissionService.validateLikeAccess,
      ).toHaveBeenCalledWith(mockStory, mockUser);
      expect(mockRequest.story).toBe(mockStory);
    });

    it("应该在有评论权限时通过", async () => {
      mockReflector.get.mockReturnValue("comment");
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      mockStoriesService.findById.mockResolvedValue(mockStory as any);
      mockStoryPermissionService.validateCommentAccess.mockResolvedValue(true);

      const result = await guard.canActivate(mockExecutionContext);

      expect(result).toBe(true);
      expect(
        mockStoryPermissionService.validateCommentAccess,
      ).toHaveBeenCalledWith(mockStory, mockUser);
    });

    it("应该在有分享权限时通过", async () => {
      mockReflector.get.mockReturnValue("share");
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      mockStoriesService.findById.mockResolvedValue(mockStory as any);
      mockStoryPermissionService.validateShareAccess.mockResolvedValue(true);

      const result = await guard.canActivate(mockExecutionContext);

      expect(result).toBe(true);
      expect(
        mockStoryPermissionService.validateShareAccess,
      ).toHaveBeenCalledWith(mockStory, mockUser);
    });

    it("应该在默认操作类型时使用基础访问权限验证", async () => {
      mockReflector.get.mockReturnValue("unknown");
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      mockStoriesService.findById.mockResolvedValue(mockStory as any);
      mockStoryPermissionService.validateAccess.mockResolvedValue(true);

      const result = await guard.canActivate(mockExecutionContext);

      expect(result).toBe(true);
      expect(mockStoryPermissionService.validateAccess).toHaveBeenCalledWith(
        mockStory,
        mockUser,
      );
    });

    it("应该在故事ID为空时抛出NotFoundException", async () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      mockRequest.params.id = undefined as any;

      await expect(guard.canActivate(mockExecutionContext)).rejects.toThrow(
        new NotFoundException("故事ID不能为空"),
      );
    });

    it("应该在用户未登录时抛出ForbiddenException", async () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      mockRequest.user = undefined as any;

      await expect(guard.canActivate(mockExecutionContext)).rejects.toThrow(
        new ForbiddenException("用户未登录"),
      );
    });

    it("应该在无点赞权限时抛出ForbiddenException", async () => {
      mockReflector.get.mockReturnValue("like");
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      mockStoriesService.findById.mockResolvedValue(mockStory as any);
      mockStoryPermissionService.validateLikeAccess.mockResolvedValue(false);

      await expect(guard.canActivate(mockExecutionContext)).rejects.toThrow(
        new ForbiddenException("权限验证失败"),
      );
    });

    it("应该在无评论权限时抛出ForbiddenException", async () => {
      mockReflector.get.mockReturnValue("comment");
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      mockStoriesService.findById.mockResolvedValue(mockStory as any);
      mockStoryPermissionService.validateCommentAccess.mockResolvedValue(false);

      await expect(guard.canActivate(mockExecutionContext)).rejects.toThrow(
        new ForbiddenException("权限验证失败"),
      );
    });

    it("应该在无分享权限时抛出ForbiddenException", async () => {
      mockReflector.get.mockReturnValue("share");
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      mockStoriesService.findById.mockResolvedValue(mockStory as any);
      mockStoryPermissionService.validateShareAccess.mockResolvedValue(false);

      await expect(guard.canActivate(mockExecutionContext)).rejects.toThrow(
        new ForbiddenException("权限验证失败"),
      );
    });

    it("应该在权限验证出错时抛出ForbiddenException", async () => {
      mockReflector.get.mockReturnValue("like");
      mockStoriesService.findById.mockRejectedValue(new Error("数据库错误"));

      await expect(guard.canActivate(mockExecutionContext)).rejects.toThrow(
        new ForbiddenException("权限验证失败"),
      );
    });
  });

  describe("getActionName", () => {
    it("应该返回正确的操作名称", () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      expect((guard as any).getActionName("like")).toBe("点赞");
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      expect((guard as any).getActionName("comment")).toBe("评论");
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      expect((guard as any).getActionName("share")).toBe("分享");
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      expect((guard as any).getActionName("unknown")).toBe("访问");
    });
  });

  it("应该正确注入依赖", () => {
    expect(guard).toBeDefined();
    expect(mockStoriesService).toBeDefined();
    expect(mockStoryPermissionService).toBeDefined();
    expect(mockReflector).toBeDefined();
  });
});
