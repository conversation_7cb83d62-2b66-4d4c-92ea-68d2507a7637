import {
  IsString,
  <PERSON><PERSON><PERSON>al,
  IsEnum,
  IsBoolean,
  IsArray,
  IsDateString,
  IsUUI<PERSON>,
} from "class-validator";
import { Transform } from "class-transformer";

/**
 * 更新故事DTO
 * 用于更新故事信息的数据传输对象
 */
export class UpdateStoryDto {
  @IsOptional()
  @IsString()
  title?: string;

  @IsOptional()
  @IsString()
  content?: string;

  @IsOptional()
  @IsString()
  coverImageUrl?: string;

  @IsOptional()
  @IsUUID()
  themeId?: string;

  @IsOptional()
  @IsDateString()
  storyDate?: string;

  @IsOptional()
  @IsString()
  location?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  images?: string[];

  @IsOptional()
  @IsEnum(["private", "friends", "public", "characters_only"])
  permissionLevel?: "private" | "friends" | "public" | "characters_only";

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === "true" || value === true)
  allowComments?: boolean;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === "true" || value === true)
  allowLikes?: boolean;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === "true" || value === true)
  allowSharing?: boolean;

  @IsOptional()
  @IsArray()
  @IsUUID(undefined, { each: true })
  characterIds?: string[];

  @IsOptional()
  @IsEnum([1, 2, 3])
  status?: number; // 1=草稿，2=发布，3=归档
}
