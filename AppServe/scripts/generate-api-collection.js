#!/usr/bin/env node

/**
 * API接口集合生成器
 * 基于Swagger/OpenAPI定义生成Postman和Thunder Client接口集合
 * 包含测试数据和环境变量配置
 */

const fs = require('fs');
const path = require('path');
const https = require('https');
const http = require('http');

console.log('🚀 YGS API接口集合生成器');
console.log('=' .repeat(50));

/**
 * 获取API定义
 */
async function fetchApiDefinition(url) {
  return new Promise((resolve, reject) => {
    const client = url.startsWith('https') ? https : http;
    
    client.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const apiSpec = JSON.parse(data);
          resolve(apiSpec);
        } catch (error) {
          reject(new Error(`解析API定义失败: ${error.message}`));
        }
      });
    }).on('error', (error) => {
      reject(new Error(`获取API定义失败: ${error.message}`));
    });
  });
}

/**
 * 生成Postman集合
 */
function generatePostmanCollection(apiSpec) {
  const collection = {
    info: {
      name: 'YGS API Collection',
      description: '有故事APP后端API接口集合 - 包含完整的业务流程测试',
      version: '1.0.0',
      schema: 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json'
    },
    variable: [
      {
        key: 'baseUrl',
        value: 'http://localhost:3000',
        type: 'string'
      },
      {
        key: 'accessToken',
        value: '',
        type: 'string'
      },
      {
        key: 'refreshToken',
        value: '',
        type: 'string'
      }
    ],
    auth: {
      type: 'bearer',
      bearer: [
        {
          key: 'token',
          value: '{{accessToken}}',
          type: 'string'
        }
      ]
    },
    item: []
  };

  // 认证模块
  const authFolder = {
    name: '🔐 认证系统',
    description: 'JWT认证、手机验证码、邮箱登录',
    item: [
      {
        name: '邮箱注册',
        request: {
          method: 'POST',
          header: [
            {
              key: 'Content-Type',
              value: 'application/json'
            }
          ],
          body: {
            mode: 'raw',
            raw: JSON.stringify({
              email: '<EMAIL>',
              password: 'password123',
              username: 'newuser'
            }, null, 2)
          },
          url: {
            raw: '{{baseUrl}}/auth/register/email',
            host: ['{{baseUrl}}'],
            path: ['auth', 'register', 'email']
          }
        },
        event: [
          {
            listen: 'test',
            script: {
              exec: [
                'pm.test("注册成功", function () {',
                '    pm.response.to.have.status(201);',
                '    const response = pm.response.json();',
                '    pm.expect(response.success).to.be.true;',
                '    pm.expect(response.data.tokens.accessToken).to.not.be.empty;',
                '    ',
                '    // 保存令牌',
                '    pm.collectionVariables.set("accessToken", response.data.tokens.accessToken);',
                '    pm.collectionVariables.set("refreshToken", response.data.tokens.refreshToken);',
                '});'
              ]
            }
          }
        ]
      },
      {
        name: '邮箱登录',
        request: {
          method: 'POST',
          header: [
            {
              key: 'Content-Type',
              value: 'application/json'
            }
          ],
          body: {
            mode: 'raw',
            raw: JSON.stringify({
              email: '<EMAIL>',
              password: 'test123456'
            }, null, 2)
          },
          url: {
            raw: '{{baseUrl}}/auth/login/email',
            host: ['{{baseUrl}}'],
            path: ['auth', 'login', 'email']
          }
        },
        event: [
          {
            listen: 'test',
            script: {
              exec: [
                'pm.test("登录成功", function () {',
                '    pm.response.to.have.status(200);',
                '    const response = pm.response.json();',
                '    pm.expect(response.success).to.be.true;',
                '    ',
                '    // 更新令牌',
                '    pm.collectionVariables.set("accessToken", response.data.tokens.accessToken);',
                '    pm.collectionVariables.set("refreshToken", response.data.tokens.refreshToken);',
                '});'
              ]
            }
          }
        ]
      },
      {
        name: '发送短信验证码',
        request: {
          method: 'POST',
          header: [
            {
              key: 'Content-Type',
              value: 'application/json'
            }
          ],
          body: {
            mode: 'raw',
            raw: JSON.stringify({
              phone: '13812345001'
            }, null, 2)
          },
          url: {
            raw: '{{baseUrl}}/auth/send-sms',
            host: ['{{baseUrl}}'],
            path: ['auth', 'send-sms']
          }
        }
      },
      {
        name: '手机验证码登录',
        request: {
          method: 'POST',
          header: [
            {
              key: 'Content-Type',
              value: 'application/json'
            }
          ],
          body: {
            mode: 'raw',
            raw: JSON.stringify({
              phone: '13812345001',
              code: '123456'
            }, null, 2)
          },
          url: {
            raw: '{{baseUrl}}/auth/login/phone',
            host: ['{{baseUrl}}'],
            path: ['auth', 'login', 'phone']
          }
        }
      },
      {
        name: '刷新令牌',
        request: {
          method: 'POST',
          header: [
            {
              key: 'Content-Type',
              value: 'application/json'
            }
          ],
          body: {
            mode: 'raw',
            raw: JSON.stringify({
              refreshToken: '{{refreshToken}}'
            }, null, 2)
          },
          url: {
            raw: '{{baseUrl}}/auth/refresh',
            host: ['{{baseUrl}}'],
            path: ['auth', 'refresh']
          }
        }
      },
      {
        name: '获取用户信息',
        request: {
          method: 'GET',
          header: [],
          url: {
            raw: '{{baseUrl}}/auth/profile',
            host: ['{{baseUrl}}'],
            path: ['auth', 'profile']
          }
        }
      },
      {
        name: '登出',
        request: {
          method: 'POST',
          header: [],
          url: {
            raw: '{{baseUrl}}/auth/logout',
            host: ['{{baseUrl}}'],
            path: ['auth', 'logout']
          }
        }
      }
    ]
  };

  // 故事管理模块
  const storiesFolder = {
    name: '📖 故事管理',
    description: '故事CRUD操作、可见性控制',
    item: [
      {
        name: '获取故事列表',
        request: {
          method: 'GET',
          header: [],
          url: {
            raw: '{{baseUrl}}/stories?page=1&limit=20&visibility=public',
            host: ['{{baseUrl}}'],
            path: ['stories'],
            query: [
              { key: 'page', value: '1' },
              { key: 'limit', value: '20' },
              { key: 'visibility', value: 'public' }
            ]
          }
        }
      },
      {
        name: '创建新故事',
        request: {
          method: 'POST',
          header: [
            {
              key: 'Content-Type',
              value: 'application/json'
            }
          ],
          body: {
            mode: 'raw',
            raw: JSON.stringify({
              title: '我的测试故事',
              content: '这是一个测试故事，包含人物张小明和李小红。他们在学校里发生了有趣的事情...',
              visibility: 'public',
              mood: 'joyful',
              metadata: {
                location: '学校',
                timeframe: '某个春天',
                genre: '校园故事',
                tags: ['友情', '学校', '青春']
              }
            }, null, 2)
          },
          url: {
            raw: '{{baseUrl}}/stories',
            host: ['{{baseUrl}}'],
            path: ['stories']
          }
        },
        event: [
          {
            listen: 'test',
            script: {
              exec: [
                'pm.test("故事创建成功", function () {',
                '    pm.response.to.have.status(201);',
                '    const response = pm.response.json();',
                '    pm.expect(response.success).to.be.true;',
                '    ',
                '    // 保存故事ID',
                '    pm.collectionVariables.set("storyId", response.data.id);',
                '});'
              ]
            }
          }
        ]
      },
      {
        name: '获取故事详情',
        request: {
          method: 'GET',
          header: [],
          url: {
            raw: '{{baseUrl}}/stories/{{storyId}}',
            host: ['{{baseUrl}}'],
            path: ['stories', '{{storyId}}']
          }
        }
      },
      {
        name: '更新故事',
        request: {
          method: 'PATCH',
          header: [
            {
              key: 'Content-Type',
              value: 'application/json'
            }
          ],
          body: {
            mode: 'raw',
            raw: JSON.stringify({
              title: '更新的故事标题',
              visibility: 'friends'
            }, null, 2)
          },
          url: {
            raw: '{{baseUrl}}/stories/{{storyId}}',
            host: ['{{baseUrl}}'],
            path: ['stories', '{{storyId}}']
          }
        }
      }
    ]
  };

  // 人物点亮系统模块
  const lightingFolder = {
    name: '💡 人物点亮系统',
    description: 'YGS核心创新功能 - 人物点亮流程',
    item: [
      {
        name: '获取故事人物列表',
        request: {
          method: 'GET',
          header: [],
          url: {
            raw: '{{baseUrl}}/stories/{{storyId}}/characters',
            host: ['{{baseUrl}}'],
            path: ['stories', '{{storyId}}', 'characters']
          }
        },
        event: [
          {
            listen: 'test',
            script: {
              exec: [
                'pm.test("获取人物列表成功", function () {',
                '    pm.response.to.have.status(200);',
                '    const response = pm.response.json();',
                '    if (response.data.length > 0) {',
                '        pm.collectionVariables.set("characterId", response.data[0].id);',
                '    }',
                '});'
              ]
            }
          }
        ]
      },
      {
        name: '申请点亮人物',
        request: {
          method: 'POST',
          header: [],
          url: {
            raw: '{{baseUrl}}/lighting/apply/{{characterId}}',
            host: ['{{baseUrl}}'],
            path: ['lighting', 'apply', '{{characterId}}']
          }
        },
        event: [
          {
            listen: 'test',
            script: {
              exec: [
                'pm.test("申请提交成功", function () {',
                '    pm.response.to.have.status(201);',
                '    const response = pm.response.json();',
                '    pm.expect(response.success).to.be.true;',
                '    ',
                '    // 保存申请ID',
                '    pm.collectionVariables.set("lightRequestId", response.data.id);',
                '});'
              ]
            }
          }
        ]
      },
      {
        name: '查看我的申请',
        request: {
          method: 'GET',
          header: [],
          url: {
            raw: '{{baseUrl}}/lighting/applications',
            host: ['{{baseUrl}}'],
            path: ['lighting', 'applications']
          }
        }
      },
      {
        name: '查看待处理申请（作者）',
        request: {
          method: 'GET',
          header: [],
          url: {
            raw: '{{baseUrl}}/lighting/pending',
            host: ['{{baseUrl}}'],
            path: ['lighting', 'pending']
          }
        }
      },
      {
        name: '批准点亮申请',
        request: {
          method: 'POST',
          header: [
            {
              key: 'Content-Type',
              value: 'application/json'
            }
          ],
          body: {
            mode: 'raw',
            raw: JSON.stringify({
              approved: true
            }, null, 2)
          },
          url: {
            raw: '{{baseUrl}}/lighting/approve/{{lightRequestId}}',
            host: ['{{baseUrl}}'],
            path: ['lighting', 'approve', '{{lightRequestId}}']
          }
        }
      },
      {
        name: '查看人物点亮状态',
        request: {
          method: 'GET',
          header: [],
          url: {
            raw: '{{baseUrl}}/lighting/status/{{characterId}}',
            host: ['{{baseUrl}}'],
            path: ['lighting', 'status', '{{characterId}}']
          }
        }
      }
    ]
  };

  // 文件上传模块
  const uploadFolder = {
    name: '📁 文件上传',
    description: '图片上传、头像管理',
    item: [
      {
        name: '上传头像',
        request: {
          method: 'POST',
          header: [],
          body: {
            mode: 'formdata',
            formdata: [
              {
                key: 'file',
                type: 'file',
                src: []
              }
            ]
          },
          url: {
            raw: '{{baseUrl}}/upload/single?type=avatar',
            host: ['{{baseUrl}}'],
            path: ['upload', 'single'],
            query: [
              { key: 'type', value: 'avatar' }
            ]
          }
        }
      },
      {
        name: '上传故事图片',
        request: {
          method: 'POST',
          header: [],
          body: {
            mode: 'formdata',
            formdata: [
              {
                key: 'file',
                type: 'file',
                src: []
              }
            ]
          },
          url: {
            raw: '{{baseUrl}}/upload/single?type=story',
            host: ['{{baseUrl}}'],
            path: ['upload', 'single'],
            query: [
              { key: 'type', value: 'story' }
            ]
          }
        }
      }
    ]
  };

  // AI功能模块
  const aiFolder = {
    name: '🤖 AI功能',
    description: 'AI内容生成、配额管理',
    item: [
      {
        name: '生成故事标题',
        request: {
          method: 'POST',
          header: [
            {
              key: 'Content-Type',
              value: 'application/json'
            }
          ],
          body: {
            mode: 'raw',
            raw: JSON.stringify({
              content: '今天是我生日，朋友们为我准备了一个惊喜派对...',
              mood: 'joyful',
              genre: '生活故事'
            }, null, 2)
          },
          url: {
            raw: '{{baseUrl}}/ai/generate/title',
            host: ['{{baseUrl}}'],
            path: ['ai', 'generate', 'title']
          }
        }
      },
      {
        name: '生成故事内容',
        request: {
          method: 'POST',
          header: [
            {
              key: 'Content-Type',
              value: 'application/json'
            }
          ],
          body: {
            mode: 'raw',
            raw: JSON.stringify({
              prompt: '写一个关于大学生活的温馨故事',
              mood: 'warm',
              length: 'medium'
            }, null, 2)
          },
          url: {
            raw: '{{baseUrl}}/ai/generate/content',
            host: ['{{baseUrl}}'],
            path: ['ai', 'generate', 'content']
          }
        }
      },
      {
        name: '查看AI配额',
        request: {
          method: 'GET',
          header: [],
          url: {
            raw: '{{baseUrl}}/ai/quota',
            host: ['{{baseUrl}}'],
            path: ['ai', 'quota']
          }
        }
      }
    ]
  };

  // 健康检查模块
  const healthFolder = {
    name: '❤️ 健康检查',
    description: '系统健康状态监控',
    item: [
      {
        name: '系统健康检查',
        request: {
          method: 'GET',
          header: [],
          url: {
            raw: '{{baseUrl}}/health',
            host: ['{{baseUrl}}'],
            path: ['health']
          }
        }
      },
      {
        name: '数据库健康检查',
        request: {
          method: 'GET',
          header: [],
          url: {
            raw: '{{baseUrl}}/health/database',
            host: ['{{baseUrl}}'],
            path: ['health', 'database']
          }
        }
      },
      {
        name: 'Redis健康检查',
        request: {
          method: 'GET',
          header: [],
          url: {
            raw: '{{baseUrl}}/health/redis',
            host: ['{{baseUrl}}'],
            path: ['health', 'redis']
          }
        }
      }
    ]
  };

  // 添加所有文件夹到集合
  collection.item.push(authFolder);
  collection.item.push(storiesFolder);
  collection.item.push(lightingFolder);
  collection.item.push(uploadFolder);
  collection.item.push(aiFolder);
  collection.item.push(healthFolder);

  return collection;
}

/**
 * 生成Thunder Client集合
 */
function generateThunderCollection(apiSpec) {
  return {
    client: "Thunder Client",
    collectionName: "YGS API Collection",
    dateExported: new Date().toISOString(),
    version: "1.1",
    folders: [
      {
        id: "auth-folder",
        name: "🔐 认证系统",
        containerId: "",
        created: new Date().toISOString(),
        sortNum: 10000
      },
      {
        id: "stories-folder", 
        name: "📖 故事管理",
        containerId: "",
        created: new Date().toISOString(),
        sortNum: 20000
      },
      {
        id: "lighting-folder",
        name: "💡 人物点亮系统",
        containerId: "",
        created: new Date().toISOString(),
        sortNum: 30000
      }
    ],
    requests: [
      {
        id: "login-email",
        name: "邮箱登录",
        url: "{{baseUrl}}/auth/login/email",
        method: "POST",
        sortNum: 10000,
        created: new Date().toISOString(),
        modified: new Date().toISOString(),
        headers: [
          {
            name: "Content-Type",
            value: "application/json"
          }
        ],
        params: [],
        body: {
          type: "json",
          raw: JSON.stringify({
            email: "<EMAIL>",
            password: "test123456"
          }, null, 2),
          form: []
        },
        tests: []
      }
    ]
  };
}

/**
 * 主函数
 */
async function main() {
  try {
    console.log('📡 获取API定义...');
    
    // 尝试从本地服务器获取API定义
    let apiSpec;
    try {
      apiSpec = await fetchApiDefinition('http://localhost:3000/api-json');
      console.log('✅ 从本地服务器获取API定义成功');
    } catch (error) {
      console.warn('⚠️ 无法从本地服务器获取API定义，使用默认模板');
      apiSpec = { info: { title: 'YGS API', version: '1.0.0' } };
    }

    console.log('🔨 生成Postman集合...');
    const postmanCollection = generatePostmanCollection(apiSpec);
    
    console.log('⚡ 生成Thunder Client集合...');
    const thunderCollection = generateThunderCollection(apiSpec);

    // 确保输出目录存在
    const outputDir = path.join(__dirname, '..', 'api-collections');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // 写入文件
    const postmanPath = path.join(outputDir, 'YGS-API-Postman.json');
    const thunderPath = path.join(outputDir, 'YGS-API-Thunder.json');
    
    fs.writeFileSync(postmanPath, JSON.stringify(postmanCollection, null, 2));
    fs.writeFileSync(thunderPath, JSON.stringify(thunderCollection, null, 2));

    console.log('✅ 接口集合生成完成！');
    console.log('-' .repeat(50));
    console.log(`📁 Postman集合: ${postmanPath}`);
    console.log(`⚡ Thunder集合: ${thunderPath}`);
    console.log('');
    console.log('📋 使用说明:');
    console.log('1. Postman: 导入 YGS-API-Postman.json 文件');
    console.log('2. Thunder Client: 导入 YGS-API-Thunder.json 文件');
    console.log('3. 修改 baseUrl 变量为实际的服务器地址');
    console.log('4. 先执行登录接口获取访问令牌');
    console.log('5. 令牌会自动保存到环境变量中');
    console.log('');
    console.log('🔧 测试数据说明:');
    console.log('• 测试邮箱: <EMAIL>, <EMAIL>, <EMAIL>');
    console.log('• 测试密码: test123456');
    console.log('• 测试手机: 13812345001, 13812345002, 13812345003');
    console.log('• 验证码: 123456 (开发环境)');

  } catch (error) {
    console.error('❌ 生成失败:', error.message);
    process.exit(1);
  }
}

// 运行主函数
main();