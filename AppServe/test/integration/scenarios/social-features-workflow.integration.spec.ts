/**
 * 社交功能完整业务流程集成测试 - 企业级社交连接验证
 *
 * 核心功能验证：
 * 1. 用户关注和粉丝关系管理
 * 2. 好友申请和验证流程
 * 3. 社交分组和权限控制
 * 4. 动态发布和时间线管理
 * 5. 互动功能（点赞、评论、分享）
 * 6. 隐私设置和可见性控制
 * 7. 社交推荐算法验证
 * 8. 通知和消息系统集成
 *
 * 测试场景：
 * - 完整社交关系建立流程
 * - 多层级隐私权限验证
 * - 社交推荐和发现机制
 * - 互动数据统计和分析
 * - 反垃圾和安全防护
 * - 性能优化和缓存策略
 */

import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import type { DataSource } from "typeorm";
import { getDataSourceToken } from "@nestjs/typeorm";
import request from "supertest";
import type { INestApplication } from "@nestjs/common";
import { AppModule } from "../../../src/app.module";
import { IntegrationTestDataFactory } from "../setup/database-setup";
import type { User } from "../../../src/modules/users/entities/user.entity";
import type { Story } from "../../../src/modules/stories/entities/story.entity";
import { UsersService } from "../../../src/modules/users/users.service";
import { StoriesService } from "../../../src/modules/stories/stories.service";
import { EnhancedRedisService } from "../../../src/common/services/enhanced-redis.service";
import { JwtAuthGuard } from "../../../src/modules/auth/guards/jwt-auth.guard";

describe("Social Features Workflow - Enterprise Integration Tests", () => {
  let app: INestApplication;
  let module: TestingModule;
  let dataSource: DataSource;
  let dataFactory: IntegrationTestDataFactory;
  let testUsersService: UsersService;
  let testStoriesService: StoriesService;
  let testRedisService: EnhancedRedisService;

  // 测试用户社交网络
  let userA: User;
  let userB: User;
  let userC: User;
  let userD: User;
  let influencerUser: User;
  let privateUser: User;

  // 测试内容
  let publicStory: Story;
  let testFriendsOnlyStory: Story;
  let testPrivateStory: Story;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({
        canActivate: jest.fn().mockReturnValue(true),
      })
      .compile();

    app = module.createNestApplication();
    await app.init();

    dataSource = module.get<DataSource>(getDataSourceToken());
    dataFactory = new IntegrationTestDataFactory(dataSource);
    testUsersService = module.get<UsersService>(UsersService);
    testStoriesService = module.get<StoriesService>(StoriesService);
    testRedisService = module.get<EnhancedRedisService>(EnhancedRedisService);

    // 清理测试环境
    await dataFactory.clearDatabase();
  });

  afterAll(async () => {
    await dataFactory.clearDatabase();
    await app.close();
  });

  beforeEach(async () => {
    // 为每个测试创建干净的数据环境
    await dataFactory.clearTestData();

    // 创建测试用户网络
    userA = await dataFactory.createUser({
      phone: "13800138001",
      nickname: "用户A",
      isPhoneVerified: true,
      bio: "这是用户A的个人简介",
    });

    userB = await dataFactory.createUser({
      phone: "13800138002",
      nickname: "用户B",
      isPhoneVerified: true,
      bio: "这是用户B的个人简介",
    });

    userC = await dataFactory.createUser({
      phone: "13800138003",
      nickname: "用户C",
      isPhoneVerified: true,
      bio: "这是用户C的个人简介",
    });

    userD = await dataFactory.createUser({
      phone: "13800138004",
      nickname: "用户D",
      isPhoneVerified: true,
      bio: "这是用户D的个人简介",
    });

    influencerUser = await dataFactory.createUser({
      phone: "13800138888",
      nickname: "影响者用户",
      isPhoneVerified: true,
      isVipUser: true,
      bio: "这是一个有影响力的用户",
    });

    privateUser = await dataFactory.createUser({
      phone: "13800138999",
      nickname: "隐私用户",
      isPhoneVerified: true,
      privacySettings: {
        profileVisibility: "friends_only",
        allowFollowers: false,
        allowDirectMessages: false,
      },
    });

    // 创建测试内容
    publicStory = await dataFactory.createStory({
      title: "公开故事",
      content: "这是一个公开的故事内容...",
      // status: "published", // 使用数字状态值
      visibility: "public",
      authorId: userA.id,
    });

    testFriendsOnlyStory = await dataFactory.createStory({
      title: "好友可见故事",
      content: "这是一个仅好友可见的故事...",
      // status: "published", // 使用数字状态值
      visibility: "friends",
      authorId: userB.id,
    });

    testPrivateStory = await dataFactory.createStory({
      title: "私人故事",
      content: "这是一个私人故事...",
      // status: "published", // 使用数字状态值
      visibility: "private",
      authorId: privateUser.id,
    });
  });

  describe("👥 用户关注和好友关系管理", () => {
    it("should complete follow and friendship workflow", async () => {
      // 1. 用户A关注用户B
      const followResponse = await request(app.getHttpServer())
        .post(`/social/follow/${userB.id}`)
        .set("Authorization", `Bearer ${await getAuthToken(userA)}`)
        .expect(201);

      expect(followResponse.body.success).toBe(true);
      expect(followResponse.body.data.relationship).toBe("following");

      // 2. 验证关注关系建立
      const followersResponse = await request(app.getHttpServer())
        .get(`/social/users/${userB.id}/followers`)
        .set("Authorization", `Bearer ${await getAuthToken(userB)}`)
        .expect(200);

      expect(followersResponse.body.data.followers).toHaveLength(1);
      expect(followersResponse.body.data.followers[0].id).toBe(userA.id);

      // 3. 验证关注列表
      const followingResponse = await request(app.getHttpServer())
        .get(`/social/users/${userA.id}/following`)
        .set("Authorization", `Bearer ${await getAuthToken(userA)}`)
        .expect(200);

      expect(followingResponse.body.data.following).toHaveLength(1);
      expect(followingResponse.body.data.following[0].id).toBe(userB.id);

      // 4. 用户B也关注用户A（成为互相关注/好友）
      await request(app.getHttpServer())
        .post(`/social/follow/${userA.id}`)
        .set("Authorization", `Bearer ${await getAuthToken(userB)}`)
        .expect(201);

      // 5. 验证互相关注关系
      const relationshipResponse = await request(app.getHttpServer())
        .get(`/social/relationship/${userB.id}`)
        .set("Authorization", `Bearer ${await getAuthToken(userA)}`)
        .expect(200);

      expect(relationshipResponse.body.data.status).toBe("mutual_following");
      expect(relationshipResponse.body.data.isFriend).toBe(true);

      // 6. 测试取消关注
      await request(app.getHttpServer())
        .delete(`/social/follow/${userB.id}`)
        .set("Authorization", `Bearer ${await getAuthToken(userA)}`)
        .expect(200);

      // 7. 验证关注关系已取消
      const unfollowCheckResponse = await request(app.getHttpServer())
        .get(`/social/relationship/${userB.id}`)
        .set("Authorization", `Bearer ${await getAuthToken(userA)}`)
        .expect(200);

      expect(unfollowCheckResponse.body.data.status).toBe("no_relationship");
    });

    it("should handle friend requests and approval workflow", async () => {
      // 1. 用户A向私密用户发送好友申请
      const friendRequestResponse = await request(app.getHttpServer())
        .post(`/social/friend-request/${privateUser.id}`)
        .set("Authorization", `Bearer ${await getAuthToken(userA)}`)
        .send({
          message: "你好，我想和你成为好友",
        })
        .expect(201);

      expect(friendRequestResponse.body.data.status).toBe("pending");

      // 2. 私密用户查看待处理的好友申请
      const pendingRequestsResponse = await request(app.getHttpServer())
        .get("/social/friend-requests/pending")
        .set("Authorization", `Bearer ${await getAuthToken(privateUser)}`)
        .expect(200);

      expect(pendingRequestsResponse.body.data.requests).toHaveLength(1);
      expect(pendingRequestsResponse.body.data.requests[0].senderId).toBe(
        userA.id,
      );

      // 3. 私密用户接受好友申请
      const acceptResponse = await request(app.getHttpServer())
        .post(`/social/friend-request/${userA.id}/accept`)
        .set("Authorization", `Bearer ${await getAuthToken(privateUser)}`)
        .expect(200);

      expect(acceptResponse.body.data.status).toBe("accepted");

      // 4. 验证好友关系建立
      const friendsResponse = await request(app.getHttpServer())
        .get("/social/friends")
        .set("Authorization", `Bearer ${await getAuthToken(userA)}`)
        .expect(200);

      expect(friendsResponse.body.data.friends).toHaveLength(1);
      expect(friendsResponse.body.data.friends[0].id).toBe(privateUser.id);

      // 5. 测试好友申请拒绝
      await request(app.getHttpServer())
        .post(`/social/friend-request/${privateUser.id}`)
        .set("Authorization", `Bearer ${await getAuthToken(userC)}`)
        .send({
          message: "想成为好友",
        })
        .expect(201);

      await request(app.getHttpServer())
        .post(`/social/friend-request/${userC.id}/reject`)
        .set("Authorization", `Bearer ${await getAuthToken(privateUser)}`)
        .expect(200);

      // 6. 验证拒绝后的状态
      const rejectedRelationResponse = await request(app.getHttpServer())
        .get(`/social/relationship/${privateUser.id}`)
        .set("Authorization", `Bearer ${await getAuthToken(userC)}`)
        .expect(200);

      expect(rejectedRelationResponse.body.data.status).toBe(
        "request_rejected",
      );
    });

    it("should manage social groups and privacy controls", async () => {
      // 1. 建立好友关系
      await establishFriendship(userA, userB);
      await establishFriendship(userA, userC);

      // 2. 创建社交分组
      const groupResponse = await request(app.getHttpServer())
        .post("/social/groups")
        .set("Authorization", `Bearer ${await getAuthToken(userA)}`)
        .send({
          name: "大学同学",
          description: "大学时期的同学们",
          privacyLevel: "friends_only",
        })
        .expect(201);

      const groupId = groupResponse.body.data.id;

      // 3. 将好友添加到分组
      await request(app.getHttpServer())
        .post(`/social/groups/${groupId}/members`)
        .set("Authorization", `Bearer ${await getAuthToken(userA)}`)
        .send({
          userIds: [userB.id, userC.id],
        })
        .expect(200);

      // 4. 验证分组成员
      const groupMembersResponse = await request(app.getHttpServer())
        .get(`/social/groups/${groupId}/members`)
        .set("Authorization", `Bearer ${await getAuthToken(userA)}`)
        .expect(200);

      expect(groupMembersResponse.body.data.members).toHaveLength(2);

      // 5. 发布仅分组可见的内容
      const groupStoryResponse = await request(app.getHttpServer())
        .post("/stories")
        .set("Authorization", `Bearer ${await getAuthToken(userA)}`)
        .send({
          title: "分组专享故事",
          content: "这个故事只有大学同学分组可以看到",
          visibility: "group_only",
          visibleToGroups: [groupId],
        })
        .expect(201);

      // 6. 验证分组成员可以看到内容
      const visibleStoriesForB = await request(app.getHttpServer())
        .get("/stories")
        .set("Authorization", `Bearer ${await getAuthToken(userB)}`)
        .query({ visibility: "all" })
        .expect(200);

      const groupStory = visibleStoriesForB.body.data.stories.find(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (s: any) => s.id === groupStoryResponse.body.data.story.id,
      );
      expect(groupStory).toBeDefined();

      // 7. 验证非分组成员看不到内容
      const visibleStoriesForD = await request(app.getHttpServer())
        .get("/stories")
        .set("Authorization", `Bearer ${await getAuthToken(userD)}`)
        .query({ visibility: "all" })
        .expect(200);

      const hiddenStory = visibleStoriesForD.body.data.stories.find(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (s: any) => s.id === groupStoryResponse.body.data.story.id,
      );
      expect(hiddenStory).toBeUndefined();
    });
  });

  describe("💬 互动功能和内容参与", () => {
    it("should handle likes, comments, and shares", async () => {
      // 1. 用户B点赞用户A的公开故事
      const likeResponse = await request(app.getHttpServer())
        .post(`/social/stories/${publicStory.id}/like`)
        .set("Authorization", `Bearer ${await getAuthToken(userB)}`)
        .expect(201);

      expect(likeResponse.body.success).toBe(true);

      // 2. 验证点赞计数
      const storyStatsResponse = await request(app.getHttpServer())
        .get(`/stories/${publicStory.id}/stats`)
        .expect(200);

      expect(storyStatsResponse.body.data.likeCount).toBe(1);

      // 3. 用户C评论故事
      const commentResponse = await request(app.getHttpServer())
        .post(`/social/stories/${publicStory.id}/comments`)
        .set("Authorization", `Bearer ${await getAuthToken(userC)}`)
        .send({
          content: "这个故事写得很好！",
          commentType: "text",
        })
        .expect(201);

      expect(commentResponse.body.data.id).toBeDefined();

      // 4. 获取故事评论列表
      const commentsResponse = await request(app.getHttpServer())
        .get(`/social/stories/${publicStory.id}/comments`)
        .expect(200);

      expect(commentsResponse.body.data.comments).toHaveLength(1);
      expect(commentsResponse.body.data.comments[0].content).toBe(
        "这个故事写得很好！",
      );

      // 5. 回复评论
      const commentId = commentResponse.body.data.id;
      const replyResponse = await request(app.getHttpServer())
        .post(`/social/comments/${commentId}/replies`)
        .set("Authorization", `Bearer ${await getAuthToken(userA)}`)
        .send({
          content: "谢谢你的夸奖！",
        })
        .expect(201);

      expect(replyResponse.body.data.parentCommentId).toBe(commentId);

      // 6. 分享故事
      const shareResponse = await request(app.getHttpServer())
        .post(`/social/stories/${publicStory.id}/share`)
        .set("Authorization", `Bearer ${await getAuthToken(userD)}`)
        .send({
          platform: "internal",
          message: "推荐大家看看这个故事",
        })
        .expect(201);

      expect(shareResponse.body.data.shareCount).toBe(1);

      // 7. 验证分享统计
      const updatedStatsResponse = await request(app.getHttpServer())
        .get(`/stories/${publicStory.id}/stats`)
        .expect(200);

      expect(updatedStatsResponse.body.data.shareCount).toBe(1);
      expect(updatedStatsResponse.body.data.commentCount).toBe(1);

      // 8. 取消点赞测试
      await request(app.getHttpServer())
        .delete(`/social/stories/${publicStory.id}/like`)
        .set("Authorization", `Bearer ${await getAuthToken(userB)}`)
        .expect(200);

      const finalStatsResponse = await request(app.getHttpServer())
        .get(`/stories/${publicStory.id}/stats`)
        .expect(200);

      expect(finalStatsResponse.body.data.likeCount).toBe(0);
    });

    it("should handle content moderation and reporting", async () => {
      // 1. 发布包含不当内容的评论
      const inappropriateCommentResponse = await request(app.getHttpServer())
        .post(`/social/stories/${publicStory.id}/comments`)
        .set("Authorization", `Bearer ${await getAuthToken(userD)}`)
        .send({
          content: "这是一条包含不当内容的评论",
          commentType: "text",
        })
        .expect(201);

      const commentId = inappropriateCommentResponse.body.data.id;

      // 2. 其他用户举报这条评论
      const reportResponse = await request(app.getHttpServer())
        .post(`/social/comments/${commentId}/report`)
        .set("Authorization", `Bearer ${await getAuthToken(userB)}`)
        .send({
          reason: "inappropriate_content",
          description: "这条评论包含不当内容",
        })
        .expect(201);

      expect(reportResponse.body.data.reportId).toBeDefined();

      // 3. 多个用户举报同一内容
      await request(app.getHttpServer())
        .post(`/social/comments/${commentId}/report`)
        .set("Authorization", `Bearer ${await getAuthToken(userC)}`)
        .send({
          reason: "inappropriate_content",
          description: "确认不当内容",
        })
        .expect(201);

      // 4. 验证内容被自动隐藏（达到举报阈值）
      const commentsAfterReportResponse = await request(app.getHttpServer())
        .get(`/social/stories/${publicStory.id}/comments`)
        .expect(200);

      const reportedComment =
        commentsAfterReportResponse.body.data.comments.find(
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          (c: any) => c.id === commentId,
        );
      expect(reportedComment?.isHidden).toBe(true);

      // 5. 管理员审核举报
      const moderationResponse = await request(app.getHttpServer())
        .post(`/admin/moderation/comments/${commentId}/review`)
        .set("Authorization", `Bearer ${await getAuthToken(influencerUser)}`)
        .send({
          action: "remove",
          reason: "违反社区规范",
        })
        .expect(200);

      expect(moderationResponse.body.data.action).toBe("removed");

      // 6. 验证评论已被删除
      const finalCommentsResponse = await request(app.getHttpServer())
        .get(`/social/stories/${publicStory.id}/comments`)
        .expect(200);

      const deletedComment = finalCommentsResponse.body.data.comments.find(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (c: any) => c.id === commentId,
      );
      expect(deletedComment).toBeUndefined();
    });
  });

  describe("🔔 通知和消息系统", () => {
    it("should manage notifications for social interactions", async () => {
      // 1. 建立好友关系以便接收通知
      await establishFriendship(userA, userB);

      // 2. 用户B点赞用户A的故事，触发通知
      await request(app.getHttpServer())
        .post(`/social/stories/${publicStory.id}/like`)
        .set("Authorization", `Bearer ${await getAuthToken(userB)}`)
        .expect(201);

      // 3. 用户A查看通知
      const notificationsResponse = await request(app.getHttpServer())
        .get("/notifications")
        .set("Authorization", `Bearer ${await getAuthToken(userA)}`)
        .expect(200);

      expect(
        notificationsResponse.body.data.notifications.length,
      ).toBeGreaterThan(0);

      const likeNotification =
        notificationsResponse.body.data.notifications.find(
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          (n: any) => n.type === "story_liked",
        );
      expect(likeNotification).toBeDefined();
      expect(likeNotification.fromUserId).toBe(userB.id);

      // 4. 标记通知为已读
      const notificationId = likeNotification.id;
      await request(app.getHttpServer())
        .patch(`/notifications/${notificationId}/read`)
        .set("Authorization", `Bearer ${await getAuthToken(userA)}`)
        .expect(200);

      // 5. 验证通知已读状态
      const updatedNotificationsResponse = await request(app.getHttpServer())
        .get("/notifications")
        .set("Authorization", `Bearer ${await getAuthToken(userA)}`)
        .expect(200);

      const readNotification =
        updatedNotificationsResponse.body.data.notifications.find(
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          (n: any) => n.id === notificationId,
        );
      expect(readNotification.isRead).toBe(true);

      // 6. 获取未读通知计数
      const unreadCountResponse = await request(app.getHttpServer())
        .get("/notifications/unread-count")
        .set("Authorization", `Bearer ${await getAuthToken(userA)}`)
        .expect(200);

      expect(unreadCountResponse.body.data.unreadCount).toBeDefined();
    });

    it("should handle private messaging between users", async () => {
      // 1. 建立好友关系
      await establishFriendship(userA, userB);

      // 2. 用户A向用户B发送私信
      const messageResponse = await request(app.getHttpServer())
        .post("/messages/send")
        .set("Authorization", `Bearer ${await getAuthToken(userA)}`)
        .send({
          recipientId: userB.id,
          content: "你好，最近怎么样？",
          messageType: "text",
        })
        .expect(201);

      expect(messageResponse.body.data.id).toBeDefined();

      // 3. 用户B查看收到的消息
      const receivedMessagesResponse = await request(app.getHttpServer())
        .get("/messages/conversations")
        .set("Authorization", `Bearer ${await getAuthToken(userB)}`)
        .expect(200);

      expect(receivedMessagesResponse.body.data.conversations).toHaveLength(1);
      expect(
        receivedMessagesResponse.body.data.conversations[0].lastMessage.content,
      ).toBe("你好，最近怎么样？");

      // 4. 用户B回复消息
      const conversationId =
        receivedMessagesResponse.body.data.conversations[0].id;
      await request(app.getHttpServer())
        .post(`/messages/conversations/${conversationId}/reply`)
        .set("Authorization", `Bearer ${await getAuthToken(userB)}`)
        .send({
          content: "我很好，谢谢关心！",
          messageType: "text",
        })
        .expect(201);

      // 5. 获取对话历史
      const conversationHistoryResponse = await request(app.getHttpServer())
        .get(`/messages/conversations/${conversationId}/messages`)
        .set("Authorization", `Bearer ${await getAuthToken(userA)}`)
        .expect(200);

      expect(conversationHistoryResponse.body.data.messages).toHaveLength(2);

      // 6. 标记消息为已读
      await request(app.getHttpServer())
        .patch(`/messages/conversations/${conversationId}/read`)
        .set("Authorization", `Bearer ${await getAuthToken(userB)}`)
        .expect(200);

      // 7. 验证未读消息计数
      const unreadMessagesResponse = await request(app.getHttpServer())
        .get("/messages/unread-count")
        .set("Authorization", `Bearer ${await getAuthToken(userB)}`)
        .expect(200);

      expect(unreadMessagesResponse.body.data.unreadCount).toBe(0);
    });
  });

  describe("🎯 社交推荐和发现", () => {
    it("should provide personalized user recommendations", async () => {
      // 1. 建立一些社交关系
      await establishFriendship(userA, userB);
      await establishFriendship(userB, userC);

      // 2. 模拟用户行为（阅读、点赞等）
      await request(app.getHttpServer())
        .post(`/social/stories/${publicStory.id}/like`)
        .set("Authorization", `Bearer ${await getAuthToken(userB)}`)
        .expect(201);

      await request(app.getHttpServer())
        .post(`/social/stories/${publicStory.id}/like`)
        .set("Authorization", `Bearer ${await getAuthToken(userC)}`)
        .expect(201);

      // 3. 获取好友推荐
      const friendRecommendationsResponse = await request(app.getHttpServer())
        .get("/social/recommendations/friends")
        .set("Authorization", `Bearer ${await getAuthToken(userA)}`)
        .query({ limit: 5 })
        .expect(200);

      expect(
        friendRecommendationsResponse.body.data.recommendations,
      ).toBeDefined();
      expect(
        friendRecommendationsResponse.body.data.recommendations.length,
      ).toBeGreaterThan(0);

      // 4. 验证推荐包含共同好友信息
      const recommendation =
        friendRecommendationsResponse.body.data.recommendations[0];
      expect(recommendation.userId).toBeDefined();
      expect(recommendation.recommendationScore).toBeGreaterThan(0);
      expect(recommendation.reasons).toContain("共同好友");

      // 5. 获取内容推荐
      const contentRecommendationsResponse = await request(app.getHttpServer())
        .get("/social/recommendations/content")
        .set("Authorization", `Bearer ${await getAuthToken(userA)}`)
        .query({ limit: 10 })
        .expect(200);

      expect(contentRecommendationsResponse.body.data.stories).toBeDefined();

      // 6. 获取热门用户
      const trendingUsersResponse = await request(app.getHttpServer())
        .get("/social/trending/users")
        .query({ period: "week", limit: 10 })
        .expect(200);

      expect(trendingUsersResponse.body.data.users).toBeDefined();
      expect(trendingUsersResponse.body.data.users.length).toBeGreaterThan(0);
    });

    it("should analyze social network and influence metrics", async () => {
      // 1. 建立复杂的社交网络
      await establishFriendship(userA, userB);
      await establishFriendship(userA, userC);
      await establishFriendship(userB, userC);
      await establishFriendship(userC, userD);

      // 2. 获取用户的社交网络分析
      const socialNetworkResponse = await request(app.getHttpServer())
        .get(`/social/users/${userA.id}/network-analysis`)
        .set("Authorization", `Bearer ${await getAuthToken(userA)}`)
        .expect(200);

      expect(socialNetworkResponse.body.data.networkSize).toBeGreaterThan(0);
      expect(socialNetworkResponse.body.data.influenceScore).toBeDefined();
      expect(socialNetworkResponse.body.data.bridgeConnections).toBeDefined();

      // 3. 获取平台社交统计
      const platformStatsResponse = await request(app.getHttpServer())
        .get("/social/platform-stats")
        .set("Authorization", `Bearer ${await getAuthToken(influencerUser)}`)
        .expect(200);

      expect(platformStatsResponse.body.data.totalUsers).toBeGreaterThan(0);
      expect(platformStatsResponse.body.data.totalConnections).toBeGreaterThan(
        0,
      );
      expect(platformStatsResponse.body.data.networkDensity).toBeDefined();
      expect(
        platformStatsResponse.body.data.averageConnectionsPerUser,
      ).toBeDefined();

      // 4. 获取用户活跃度分析
      const activityAnalysisResponse = await request(app.getHttpServer())
        .get(`/social/users/${userA.id}/activity-analysis`)
        .set("Authorization", `Bearer ${await getAuthToken(userA)}`)
        .query({ timeframe: "month" })
        .expect(200);

      expect(activityAnalysisResponse.body.data.postsCount).toBeDefined();
      expect(
        activityAnalysisResponse.body.data.interactionsCount,
      ).toBeDefined();
      expect(activityAnalysisResponse.body.data.engagementRate).toBeDefined();
    });
  });

  // 辅助函数：获取用户认证令牌
  async function getAuthToken(user: User): Promise<string> {
    const loginResponse = await request(app.getHttpServer())
      .post("/auth/login")
      .send({
        phone: user.phone,
        password: "Test123456!", // 默认测试密码
      });

    return loginResponse.body.data.accessToken;
  }

  // 辅助函数：建立好友关系
  async function establishFriendship(
    userOne: User,
    userTwo: User,
  ): Promise<void> {
    // 用户1向用户2发送好友申请
    await request(app.getHttpServer())
      .post(`/social/friend-request/${userTwo.id}`)
      .set("Authorization", `Bearer ${await getAuthToken(userOne)}`)
      .send({
        message: "想成为好友",
      });

    // 用户2接受好友申请
    await request(app.getHttpServer())
      .post(`/social/friend-request/${userOne.id}/accept`)
      .set("Authorization", `Bearer ${await getAuthToken(userTwo)}`);
  }
});
