import type { MigrationInterface, QueryRunner } from "typeorm";

export class CreateCoreTables1600000000000 implements MigrationInterface {
  name = "CreateCoreTables1600000000000";

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 创建用户表
    await queryRunner.query(`
      CREATE TABLE "users" (
        "id" uuid NOT NULL DEFAULT gen_random_uuid(),
        "phone" varchar(11) NOT NULL,
        "username" varchar(50),
        "avatar_url" varchar,
        "birth_date" date,
        "is_active" boolean NOT NULL DEFAULT true,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "UQ_users_phone" UNIQUE ("phone"),
        CONSTRAINT "PK_users_id" PRIMARY KEY ("id")
      );
      COMMENT ON COLUMN "users"."phone" IS '手机号';
      COMMENT ON COLUMN "users"."username" IS '用户名';
      COMMENT ON COLUMN "users"."avatar_url" IS '头像URL';
      COMMENT ON COLUMN "users"."birth_date" IS '生日';
      COMMENT ON COLUMN "users"."is_active" IS '账户是否激活';
      COMMENT ON COLUMN "users"."created_at" IS '创建时间';
      COMMENT ON COLUMN "users"."updated_at" IS '更新时间';
    `);

    // 创建故事表
    await queryRunner.query(`
      CREATE TABLE "stories" (
        "id" uuid NOT NULL DEFAULT gen_random_uuid(),
        "user_id" uuid NOT NULL,
        "title" varchar(255) NOT NULL,
        "content" text,
        "cover_image_url" varchar,
        "status" smallint NOT NULL DEFAULT '1',
        "view_count" integer NOT NULL DEFAULT '0',
        "like_count" integer NOT NULL DEFAULT '0',
        "privacy_level" integer NOT NULL DEFAULT '1',
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_stories_id" PRIMARY KEY ("id")
      );
      COMMENT ON COLUMN "stories"."title" IS '故事标题';
      COMMENT ON COLUMN "stories"."content" IS '故事内容';
      COMMENT ON COLUMN "stories"."cover_image_url" IS '封面图片URL';
      COMMENT ON COLUMN "stories"."status" IS '状态：1草稿 2发布 3归档';
      COMMENT ON COLUMN "stories"."view_count" IS '浏览次数';
      COMMENT ON COLUMN "stories"."like_count" IS '点赞次数';
      COMMENT ON COLUMN "stories"."privacy_level" IS '隐私等级：1公开 2好友 3私密';
      COMMENT ON COLUMN "stories"."created_at" IS '创建时间';
      COMMENT ON COLUMN "stories"."updated_at" IS '更新时间';
    `);

    // 创建故事章节表
    await queryRunner.query(`
      CREATE TABLE "story_chapters" (
        "id" uuid NOT NULL DEFAULT gen_random_uuid(),
        "story_id" uuid NOT NULL,
        "title" varchar(255),
        "content" text NOT NULL,
        "chapter_order" integer NOT NULL,
        "image_urls" text array,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_story_chapters_id" PRIMARY KEY ("id")
      );
      COMMENT ON COLUMN "story_chapters"."title" IS '章节标题';
      COMMENT ON COLUMN "story_chapters"."content" IS '章节内容';
      COMMENT ON COLUMN "story_chapters"."chapter_order" IS '章节排序';
      COMMENT ON COLUMN "story_chapters"."image_urls" IS '章节图片URLs';
      COMMENT ON COLUMN "story_chapters"."created_at" IS '创建时间';
    `);

    // 创建人物表
    await queryRunner.query(`
      CREATE TABLE "characters" (
        "id" uuid NOT NULL DEFAULT gen_random_uuid(),
        "creator_id" uuid NOT NULL,
        "name" varchar(100) NOT NULL,
        "description" text,
        "avatar_url" varchar,
        "relation_type" varchar(20),
        "personality_traits" text,
        "physical_description" text,
        "is_active" boolean NOT NULL DEFAULT true,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_characters_id" PRIMARY KEY ("id")
      );
      COMMENT ON COLUMN "characters"."name" IS '人物姓名';
      COMMENT ON COLUMN "characters"."description" IS '人物描述';
      COMMENT ON COLUMN "characters"."avatar_url" IS '人物头像URL';
      COMMENT ON COLUMN "characters"."relation_type" IS '关系类型';
      COMMENT ON COLUMN "characters"."personality_traits" IS '性格特征';
      COMMENT ON COLUMN "characters"."physical_description" IS '外貌描述';
      COMMENT ON COLUMN "characters"."is_active" IS '是否激活';
      COMMENT ON COLUMN "characters"."created_at" IS '创建时间';
    `);

    // 创建故事人物关联表
    await queryRunner.query(`
      CREATE TABLE "story_characters" (
        "story_id" uuid NOT NULL,
        "character_id" uuid NOT NULL,
        "mention_count" integer NOT NULL DEFAULT '1',
        CONSTRAINT "PK_story_characters" PRIMARY KEY ("story_id", "character_id")
      );
    `);

    // 创建人物点亮记录表
    await queryRunner.query(`
      CREATE TABLE "character_lightings" (
        "id" uuid NOT NULL DEFAULT gen_random_uuid(),
        "character_id" uuid NOT NULL,
        "lighter_user_id" uuid NOT NULL,
        "creator_user_id" uuid NOT NULL,
        "status" smallint NOT NULL DEFAULT '1',
        "lighting_message" text,
        "confirmed_at" TIMESTAMP,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_character_lightings_id" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_character_lightings_character_lighter" UNIQUE ("character_id", "lighter_user_id")
      );
      COMMENT ON COLUMN "character_lightings"."status" IS '状态：1待确认 2已确认 3已拒绝';
      COMMENT ON COLUMN "character_lightings"."lighting_message" IS '点亮时的留言';
      COMMENT ON COLUMN "character_lightings"."confirmed_at" IS '确认时间';
      COMMENT ON COLUMN "character_lightings"."created_at" IS '创建时间';
    `);

    // 创建用户关系表
    await queryRunner.query(`
      CREATE TABLE "user_relationships" (
        "id" uuid NOT NULL DEFAULT gen_random_uuid(),
        "user_id" uuid NOT NULL,
        "related_user_id" uuid NOT NULL,
        "character_id" uuid,
        "lighting_id" uuid,
        "relation_type" varchar(20),
        "intimacy_level" smallint NOT NULL DEFAULT '1',
        "status" smallint NOT NULL DEFAULT '1',
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_user_relationships_id" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_user_relationships_user_related_character" UNIQUE ("user_id", "related_user_id", "character_id")
      );
      COMMENT ON COLUMN "user_relationships"."relation_type" IS '关系类型：family, friend, colleague等';
      COMMENT ON COLUMN "user_relationships"."intimacy_level" IS '亲密度等级：1-5，控制访问权限';
      COMMENT ON COLUMN "user_relationships"."status" IS '状态：1已确认 2已取消';
      COMMENT ON COLUMN "user_relationships"."created_at" IS '创建时间';
    `);

    // 创建故事分享记录表
    await queryRunner.query(`
      CREATE TABLE "story_shares" (
        "id" uuid NOT NULL DEFAULT gen_random_uuid(),
        "story_id" uuid NOT NULL,
        "sharer_id" uuid NOT NULL,
        "target_character_id" uuid,
        "share_type" varchar(20) NOT NULL,
        "share_content" text,
        "share_token" varchar(64),
        "view_count" integer NOT NULL DEFAULT '0',
        "expires_at" TIMESTAMP,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_story_shares_id" PRIMARY KEY ("id")
      );
      COMMENT ON COLUMN "story_shares"."share_type" IS '分享类型：link, poster, excerpt';
      COMMENT ON COLUMN "story_shares"."share_content" IS '分享的具体内容或链接';
      COMMENT ON COLUMN "story_shares"."share_token" IS '分享访问令牌';
      COMMENT ON COLUMN "story_shares"."view_count" IS '查看次数';
      COMMENT ON COLUMN "story_shares"."expires_at" IS '过期时间';
      COMMENT ON COLUMN "story_shares"."created_at" IS '创建时间';
    `);

    // 创建通知表
    await queryRunner.query(`
      CREATE TABLE "notifications" (
        "id" uuid NOT NULL DEFAULT gen_random_uuid(),
        "user_id" uuid NOT NULL,
        "type" varchar(50) NOT NULL,
        "title" varchar(255) NOT NULL,
        "content" text,
        "related_id" varchar,
        "action_url" varchar,
        "is_read" boolean NOT NULL DEFAULT false,
        "read_at" TIMESTAMP,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_notifications_id" PRIMARY KEY ("id")
      );
      COMMENT ON COLUMN "notifications"."type" IS '通知类型：character_lighting, relationship_confirmed, story_shared等';
      COMMENT ON COLUMN "notifications"."title" IS '通知标题';
      COMMENT ON COLUMN "notifications"."content" IS '通知内容';
      COMMENT ON COLUMN "notifications"."related_id" IS '关联的记录ID';
      COMMENT ON COLUMN "notifications"."action_url" IS '点击跳转的URL';
      COMMENT ON COLUMN "notifications"."is_read" IS '是否已读';
      COMMENT ON COLUMN "notifications"."read_at" IS '已读时间';
      COMMENT ON COLUMN "notifications"."created_at" IS '创建时间';
    `);

    // 添加外键约束
    await queryRunner.query(
      `ALTER TABLE "stories" ADD CONSTRAINT "FK_stories_user" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "story_chapters" ADD CONSTRAINT "FK_story_chapters_story" FOREIGN KEY ("story_id") REFERENCES "stories"("id") ON DELETE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "characters" ADD CONSTRAINT "FK_characters_creator" FOREIGN KEY ("creator_id") REFERENCES "users"("id") ON DELETE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "story_characters" ADD CONSTRAINT "FK_story_characters_story" FOREIGN KEY ("story_id") REFERENCES "stories"("id") ON DELETE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "story_characters" ADD CONSTRAINT "FK_story_characters_character" FOREIGN KEY ("character_id") REFERENCES "characters"("id") ON DELETE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "character_lightings" ADD CONSTRAINT "FK_character_lightings_character" FOREIGN KEY ("character_id") REFERENCES "characters"("id") ON DELETE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "character_lightings" ADD CONSTRAINT "FK_character_lightings_lighter_user" FOREIGN KEY ("lighter_user_id") REFERENCES "users"("id") ON DELETE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "character_lightings" ADD CONSTRAINT "FK_character_lightings_creator_user" FOREIGN KEY ("creator_user_id") REFERENCES "users"("id") ON DELETE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_relationships" ADD CONSTRAINT "FK_user_relationships_user" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_relationships" ADD CONSTRAINT "FK_user_relationships_related_user" FOREIGN KEY ("related_user_id") REFERENCES "users"("id") ON DELETE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_relationships" ADD CONSTRAINT "FK_user_relationships_character" FOREIGN KEY ("character_id") REFERENCES "characters"("id")`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_relationships" ADD CONSTRAINT "FK_user_relationships_lighting" FOREIGN KEY ("lighting_id") REFERENCES "character_lightings"("id")`,
    );
    await queryRunner.query(
      `ALTER TABLE "story_shares" ADD CONSTRAINT "FK_story_shares_story" FOREIGN KEY ("story_id") REFERENCES "stories"("id") ON DELETE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "story_shares" ADD CONSTRAINT "FK_story_shares_sharer" FOREIGN KEY ("sharer_id") REFERENCES "users"("id") ON DELETE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "story_shares" ADD CONSTRAINT "FK_story_shares_target_character" FOREIGN KEY ("target_character_id") REFERENCES "characters"("id")`,
    );
    await queryRunner.query(
      `ALTER TABLE "notifications" ADD CONSTRAINT "FK_notifications_user" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE`,
    );

    // 创建关键索引
    await queryRunner.query(
      `CREATE INDEX "idx_stories_user_id" ON "stories" ("user_id")`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_stories_status_created" ON "stories" ("status", "created_at" DESC)`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_chapters_story_order" ON "story_chapters" ("story_id", "chapter_order")`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_characters_creator" ON "characters" ("creator_id")`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_character_lightings_character" ON "character_lightings" ("character_id")`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_character_lightings_lighter" ON "character_lightings" ("lighter_user_id")`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_character_lightings_status" ON "character_lightings" ("status", "created_at" DESC)`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_relationships_users" ON "user_relationships" ("user_id", "related_user_id")`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_story_shares_story" ON "story_shares" ("story_id")`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_notifications_user_unread" ON "notifications" ("user_id", "is_read", "created_at" DESC)`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 删除索引
    await queryRunner.query(`DROP INDEX "idx_notifications_user_unread"`);
    await queryRunner.query(`DROP INDEX "idx_story_shares_story"`);
    await queryRunner.query(`DROP INDEX "idx_relationships_users"`);
    await queryRunner.query(`DROP INDEX "idx_character_lightings_status"`);
    await queryRunner.query(`DROP INDEX "idx_character_lightings_lighter"`);
    await queryRunner.query(`DROP INDEX "idx_character_lightings_character"`);
    await queryRunner.query(`DROP INDEX "idx_characters_creator"`);
    await queryRunner.query(`DROP INDEX "idx_chapters_story_order"`);
    await queryRunner.query(`DROP INDEX "idx_stories_status_created"`);
    await queryRunner.query(`DROP INDEX "idx_stories_user_id"`);

    // 删除外键约束
    await queryRunner.query(
      `ALTER TABLE "notifications" DROP CONSTRAINT "FK_notifications_user"`,
    );
    await queryRunner.query(
      `ALTER TABLE "story_shares" DROP CONSTRAINT "FK_story_shares_target_character"`,
    );
    await queryRunner.query(
      `ALTER TABLE "story_shares" DROP CONSTRAINT "FK_story_shares_sharer"`,
    );
    await queryRunner.query(
      `ALTER TABLE "story_shares" DROP CONSTRAINT "FK_story_shares_story"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_relationships" DROP CONSTRAINT "FK_user_relationships_lighting"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_relationships" DROP CONSTRAINT "FK_user_relationships_character"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_relationships" DROP CONSTRAINT "FK_user_relationships_related_user"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_relationships" DROP CONSTRAINT "FK_user_relationships_user"`,
    );
    await queryRunner.query(
      `ALTER TABLE "character_lightings" DROP CONSTRAINT "FK_character_lightings_creator_user"`,
    );
    await queryRunner.query(
      `ALTER TABLE "character_lightings" DROP CONSTRAINT "FK_character_lightings_lighter_user"`,
    );
    await queryRunner.query(
      `ALTER TABLE "character_lightings" DROP CONSTRAINT "FK_character_lightings_character"`,
    );
    await queryRunner.query(
      `ALTER TABLE "story_characters" DROP CONSTRAINT "FK_story_characters_character"`,
    );
    await queryRunner.query(
      `ALTER TABLE "story_characters" DROP CONSTRAINT "FK_story_characters_story"`,
    );
    await queryRunner.query(
      `ALTER TABLE "characters" DROP CONSTRAINT "FK_characters_creator"`,
    );
    await queryRunner.query(
      `ALTER TABLE "story_chapters" DROP CONSTRAINT "FK_story_chapters_story"`,
    );
    await queryRunner.query(
      `ALTER TABLE "stories" DROP CONSTRAINT "FK_stories_user"`,
    );

    // 删除表
    await queryRunner.query(`DROP TABLE "notifications"`);
    await queryRunner.query(`DROP TABLE "story_shares"`);
    await queryRunner.query(`DROP TABLE "user_relationships"`);
    await queryRunner.query(`DROP TABLE "character_lightings"`);
    await queryRunner.query(`DROP TABLE "story_characters"`);
    await queryRunner.query(`DROP TABLE "characters"`);
    await queryRunner.query(`DROP TABLE "story_chapters"`);
    await queryRunner.query(`DROP TABLE "stories"`);
    await queryRunner.query(`DROP TABLE "users"`);
  }
}
