import { Module } from "@nestjs/common";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { TypeOrmModule } from "@nestjs/typeorm";
import { CacheModule } from "@nestjs/cache-manager";
import { JwtModule } from "@nestjs/jwt";
import { ThrottlerModule } from "@nestjs/throttler";

// 配置文件
import { databaseConfig } from "./config/database.config";
import { CacheConfigService } from "./config/cache.config";
import { ossConfig } from "./config/oss.config";

// 实体导入 - 企业级显式导入确保测试环境兼容性
import { User } from "./modules/users/entities/user.entity";
import { RefreshToken } from "./modules/users/entities/refresh-token.entity";
import { Notification } from "./modules/users/entities/notification.entity";
import { UserRelationship } from "./modules/users/entities/user-relationship.entity";
import { Story } from "./modules/stories/entities/story.entity";
import { StoryChapter } from "./modules/stories/entities/story-chapter.entity";
import { StoryShare } from "./modules/stories/entities/story-share.entity";
import { Theme } from "./modules/stories/entities/theme.entity";
import { Character } from "./modules/characters/entities/character.entity";
import { CharacterLighting } from "./modules/characters/entities/character-lighting.entity";
import { LightRequest } from "./modules/characters/entities/light-request.entity";
import { AiUsageLog } from "./modules/ai/entities/ai-usage-log.entity";
import { Comment } from "./modules/comments/entities/comment.entity";
import { CommentLike } from "./modules/comments/entities/comment-like.entity";
import { Bookmark } from "./modules/bookmarks/entities/bookmark.entity";
import { Share } from "./modules/shares/entities/share.entity";
import { ShareAccessLog } from "./modules/shares/entities/share-access-log.entity";
import { Report } from "./modules/reports/entities/report.entity";
import { UserFollow } from "./modules/social/entities/user-follow.entity";
import { FriendGroup } from "./modules/social/entities/friend-group.entity";
import { FriendGroupMember } from "./modules/social/entities/friend-group-member.entity";
import { StoryReference } from "./modules/story-references/entities/story-reference.entity";
import { ReferenceCollection } from "./modules/story-references/entities/reference-collection.entity";
import { TimelineEvent } from "./modules/timeline/entities/timeline-event.entity";

// 模块
import { CommonModule } from "./common/common.module";
import { AuthModule } from "./modules/auth/auth.module";
import { UsersModule } from "./modules/users/users.module";
import { StoriesModule } from "./modules/stories/stories.module";
import { CharactersModule } from "./modules/characters/characters.module";
import { LightingModule } from "./modules/lighting/lighting.module";
import { AIModule } from "./modules/ai/ai.module";
import { HealthModule } from "./modules/health/health.module";
import { UploadModule } from "./modules/upload/upload.module";
import { ImageModule } from "./modules/image/image.module";
import { DatabaseSeederModule } from "./database/database-seeder.module";
import { CommentsModule } from "./modules/comments/comments.module";
import { BookmarksModule } from "./modules/bookmarks/bookmarks.module";
import { SharesModule } from "./modules/shares/shares.module";
import { ReportsModule } from "./modules/reports/reports.module";
import { SocialModule } from "./modules/social/social.module";
import { StoryReferencesModule } from "./modules/story-references/story-references.module";
import { TimelineModule } from "./modules/timeline/timeline.module";
import { HomepageModule } from "./modules/homepage/homepage.module";

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      load: [databaseConfig, ossConfig],
    }),

    // 数据库模块
    TypeOrmModule.forRootAsync({
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        type: "postgres" as const,
        host: configService.get<string>("DB_HOST", "localhost"),
        port: configService.get<number>("DB_PORT", 5432),
        username: configService.get<string>("DB_USERNAME", "postgres"),
        password: configService.get<string>("DB_PASSWORD", "password"),
        database: configService.get<string>("DB_NAME", "story_app"),
        entities: [
          User,
          RefreshToken,
          Notification,
          UserRelationship,
          Story,
          StoryChapter,
          StoryShare,
          Theme,
          Character,
          CharacterLighting,
          LightRequest,
          AiUsageLog,
          Comment,
          CommentLike,
          Bookmark,
          Share,
          ShareAccessLog,
          Report,
          UserFollow,
          FriendGroup,
          FriendGroupMember,
          StoryReference,
          ReferenceCollection,
          TimelineEvent,
        ],
        synchronize: configService.get<string>("NODE_ENV") !== "production",
        logging: configService.get<string>("NODE_ENV") === "development",
      }),
    }),

    // 官方缓存模块 - 使用Keyv + Redis
    CacheModule.registerAsync({
      isGlobal: true,
      useClass: CacheConfigService,
    }),

    // JWT模块
    JwtModule.register({
      global: true,
      secret: process.env.JWT_SECRET || "your-secret-key",
      signOptions: { expiresIn: "15m" },
    }),

    // 限流模块
    ThrottlerModule.forRoot([
      {
        name: "short",
        ttl: 1000,
        limit: 3,
      },
      {
        name: "medium",
        ttl: 10000,
        limit: 20,
      },
      {
        name: "long",
        ttl: 60000,
        limit: 100,
      },
    ]),

    // 公共模块
    CommonModule,

    // 业务模块
    AuthModule,
    UsersModule,
    StoriesModule,
    CharactersModule,
    LightingModule,
    AIModule,
    HealthModule,
    UploadModule,
    ImageModule,

    // 内容互动模块
    CommentsModule,
    BookmarksModule,
    SharesModule,
    ReportsModule,
    SocialModule,
    StoryReferencesModule,
    TimelineModule,
    HomepageModule,

    // 数据播种器模块（用于测试数据生成）
    DatabaseSeederModule,
  ],
  providers: [CacheConfigService],
})
export class AppModule {}
