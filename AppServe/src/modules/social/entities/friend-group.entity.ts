import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  OneToMany,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from "typeorm";
import { User } from "../../users/entities/user.entity";
import { FriendGroupMember } from "./friend-group-member.entity";

export enum GroupType {
  CUSTOM = "custom",
  FAMILY = "family",
  FRIENDS = "friends",
  COLLEAGUES = "colleagues",
  CLASSMATES = "classmates",
  OTHERS = "others",
}

export enum GroupVisibility {
  PUBLIC = "public",
  PRIVATE = "private",
  FRIENDS_ONLY = "friends_only",
}

/**
 * 好友分组实体
 * 用户创建的好友分组
 */
@Entity("friend_groups")
@Index(["ownerId", "groupType"])
@Index(["ownerId", "name"])
export class FriendGroup {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({
    type: "varchar",
    length: 50,
    comment: "分组名称",
  })
  name: string;

  @Column({
    type: "text",
    nullable: true,
    comment: "分组描述",
  })
  description: string;

  @Column({
    type: "uuid",
    comment: "分组所有者用户ID",
  })
  ownerId: string;

  @Column({
    type: "enum",
    enum: GroupType,
    default: GroupType.CUSTOM,
    comment: "分组类型",
  })
  groupType: GroupType;

  @Column({
    type: "enum",
    enum: GroupVisibility,
    default: GroupVisibility.PRIVATE,
    comment: "分组可见性",
  })
  visibility: GroupVisibility;

  @Column({
    type: "varchar",
    length: 50,
    nullable: true,
    comment: "分组颜色标签",
  })
  colorTag: string;

  @Column({
    type: "int",
    default: 0,
    comment: "分组成员数量",
  })
  memberCount: number;

  @Column({
    type: "int",
    default: 0,
    comment: "排序权重",
  })
  sortOrder: number;

  @Column({
    type: "boolean",
    default: true,
    comment: "是否启用",
  })
  isActive: boolean;

  @Column({
    type: "jsonb",
    nullable: true,
    comment: "分组权限设置",
  })
  permissions: {
    canViewProfile?: boolean;
    canViewStories?: boolean;
    canComment?: boolean;
    canShare?: boolean;
    canMessage?: boolean;
  };

  @Column({
    type: "jsonb",
    nullable: true,
    comment: "扩展字段",
  })
  metadata: Record<string, unknown>;

  @CreateDateColumn({
    type: "timestamp with time zone",
    comment: "创建时间",
  })
  createdAt: Date;

  @UpdateDateColumn({
    type: "timestamp with time zone",
    comment: "更新时间",
  })
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => User, { eager: false })
  @JoinColumn({ name: "ownerId" })
  owner: User;

  @OneToMany(() => FriendGroupMember, (member) => member.group)
  members: FriendGroupMember[];
}
