import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  Unique,
} from "typeorm";
import { User } from "../../users/entities/user.entity";
import { Story } from "../../stories/entities/story.entity";

export enum BookmarkStatus {
  ACTIVE = "active",
  ARCHIVED = "archived",
  INVALID = "invalid", // 目标内容被删除时设置为无效
}

export enum BookmarkCategory {
  DEFAULT = "default",
  FAVORITES = "favorites",
  READ_LATER = "read_later",
  INSPIRATION = "inspiration",
  REFERENCE = "reference",
  CUSTOM = "custom",
}

/**
 * 收藏实体
 * 用户收藏故事的记录
 */
@Entity("bookmarks")
@Unique(["userId", "storyId"])
@Index(["userId", "category", "status"])
@Index(["userId", "createdAt"])
@Index(["storyId"])
export class Bookmark {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({
    type: "uuid",
    comment: "用户ID",
  })
  userId: string;

  @Column({
    type: "uuid",
    comment: "故事ID",
  })
  storyId: string;

  @Column({
    type: "enum",
    enum: BookmarkCategory,
    default: BookmarkCategory.DEFAULT,
    comment: "收藏分类",
  })
  category: BookmarkCategory;

  @Column({
    type: "varchar",
    length: 100,
    nullable: true,
    comment: "自定义分类名称（当category为custom时使用）",
  })
  customCategoryName: string;

  @Column({
    type: "enum",
    enum: BookmarkStatus,
    default: BookmarkStatus.ACTIVE,
    comment: "收藏状态",
  })
  status: BookmarkStatus;

  @Column({
    type: "text",
    nullable: true,
    comment: "收藏备注",
  })
  note: string;

  @Column({
    type: "jsonb",
    nullable: true,
    comment: "收藏时的故事快照（标题、摘要等）",
  })
  storySnapshot: {
    title: string;
    summary?: string;
    coverImage?: string;
    authorName: string;
  };

  @Column({
    type: "jsonb",
    nullable: true,
    comment: "扩展字段",
  })
  metadata: Record<string, unknown>;

  @CreateDateColumn({
    type: "timestamp with time zone",
    comment: "收藏时间",
  })
  createdAt: Date;

  @UpdateDateColumn({
    type: "timestamp with time zone",
    comment: "更新时间",
  })
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => User, { eager: false })
  @JoinColumn({ name: "userId" })
  user: User;

  @ManyToOne(() => Story, { eager: false })
  @JoinColumn({ name: "storyId" })
  story: Story;
}
