import { Module, forwardRef } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { CharactersService } from "./characters.service";
import { CharactersController } from "./characters.controller";
import { Character } from "./entities/character.entity";
import { CharacterLighting } from "./entities/character-lighting.entity";
import { LightRequest } from "./entities/light-request.entity";
import { User } from "../users/entities/user.entity";
import { Story } from "../stories/entities/story.entity";
import { AuthModule } from "../auth/auth.module";

/**
 * 人物模块 - v1.0.0
 * 负责人物的创建、管理、点亮状态维护和业务逻辑处理
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      Character,
      CharacterLighting,
      LightRequest,
      User,
      Story,
    ]),
    forwardRef(() => AuthModule), // 修复JwtAuthGuard依赖问题
  ],
  controllers: [CharactersController],
  providers: [CharactersService],
  exports: [CharactersService],
})
export class CharactersModule {}
