import 'package:flutter/material.dart';

/// YGS设计系统 - 颜色令牌
/// 基于Figma设计规范v1.0.0严格定义
class YgsColors {
  YgsColors._(); // 防止实例化

  // ========== 主品牌色 ==========
  /// 品牌主色 #E22626 - 红色
  static const Color brandPrimary = Color(0xFFE22626);
  
  /// 品牌辅助色 #507DAF - 蓝色
  static const Color brandSecondary = Color(0xFF507DAF);
  
  /// 品牌主色上的文字颜色
  static const Color onBrandPrimary = Color(0xFFFFFFFF);

  // ========== 文字颜色层级 - 严格按照Figma设计稿 ==========
  /// 字体颜色01 #151523 - 主要文字/激活状态
  static const Color textPrimary = Color(0xFF151523);
  
  /// 字体颜色03 #868691 - 次要文字/未激活状态
  static const Color textSecondary = Color(0xFF868691);
  
  /// 字体颜色04 #B6B6BD - 辅助文字
  static const Color textTertiary = Color(0xFFB6B6BD);
  
  /// 深色文字 #2A2A39 - 深灰色
  static const Color textDark = Color(0xFF2A2A39);

  // ========== 背景颜色 ==========
  /// 主背景色 #F4F7FA - 浅蓝灰
  static const Color backgroundPrimary = Color(0xFFF4F7FA);
  
  /// 次要背景色 #E8F0F8 - 稍深的浅蓝灰
  static const Color backgroundSecondary = Color(0xFFE8F0F8);
  
  /// 卡片背景色 #FFFFFF - 纯白
  static const Color backgroundCard = Color(0xFFFFFFFF);
  
  /// 毛玻璃背景色 - 严格按照Figma设计稿 rgba(246,248,252,0.8)
  static const Color backgroundGlass = Color.fromRGBO(246, 248, 252, 0.8);

  // ========== 边界和分割线 ==========
  /// 边框颜色 #DEE1E5 - 浅灰
  static const Color borderPrimary = Color(0xFFDEE1E5);
  
  /// 分割线颜色 #EDF0F6 - 极浅灰
  static const Color divider = Color(0xFFEDF0F6);

  // ========== 状态颜色 ==========
  /// 成功色 #11C625 - 绿色
  static const Color successPrimary = Color(0xFF11C625);
  
  /// 错误色 #E22626 - 红色
  static const Color errorPrimary = Color(0xFFE22626);
  
  /// 警告色 #FF9500 - 橙色
  static const Color warningPrimary = Color(0xFFFF9500);
  
  /// 信息色 #507DAF - 蓝色
  static const Color infoPrimary = Color(0xFF507DAF);

  // ========== 状态颜色上的文字 ==========
  static const Color onSuccessPrimary = Color(0xFFFFFFFF);
  static const Color onErrorPrimary = Color(0xFFFFFFFF);
  static const Color onWarningPrimary = Color(0xFFFFFFFF);
  static const Color onInfoPrimary = Color(0xFFFFFFFF);

  // ========== 渐变色定义 ==========
  /// 背景渐变 - 用于登录页等场景，基于Figma设计稿优化
  static const LinearGradient backgroundGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    stops: [0.0, 0.3], // 粉色区域集中在上部30%
    colors: [
      Color(0xFFFDF6F6), // 更淡的粉色 #FDF6F6
      Color(0xFFFFFFFF), // 白色 #FFFFFF
    ],
  );

  /// 品牌渐变 - 用于Logo等品牌元素
  static const LinearGradient brandGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFE22626),
      Color(0xFFB71C1C),
    ],
  );

  // ========== 阴影定义 ==========
  /// 卡片阴影
  static final List<BoxShadow> cardShadow = [
    BoxShadow(
      color: const Color(0xFFEDF0F6).withValues(alpha: 0.4),
      blurRadius: 12,
      offset: const Offset(0, 6),
      spreadRadius: 2,
    ),
  ];

  /// 按钮阴影
  static final List<BoxShadow> buttonShadow = [
    BoxShadow(
      color: const Color(0xFFEDF0F6).withValues(alpha: 0.4),
      blurRadius: 16,
      offset: const Offset(0, 8),
      spreadRadius: 4,
    ),
  ];

  /// 高阶阴影 - 用于浮动元素
  static final List<BoxShadow> elevatedShadow = [
    BoxShadow(
      color: const Color(0xFFEDF0F6).withValues(alpha: 0.6),
      blurRadius: 24,
      offset: const Offset(0, 12),
      spreadRadius: 8,
    ),
  ];
}