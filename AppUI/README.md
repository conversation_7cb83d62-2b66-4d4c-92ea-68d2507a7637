# 有故事 APP - 前端项目 (AppUI)

## 📱 项目概述

**有故事 APP v1.0.0** 是一个专注于个人故事创作与 AI 辅助的移动应用前端项目。本项目采用企业级 React Native 架构，支持 iOS 和 Android 平台。

### 核心特性

- 🔐 **安全认证**: JWT + 手机验证码登录
- 📖 **故事创作**: 富文本编辑器 + AI 写作助手
- 👥 **人物管理**: 创建人物档案，支持 @人物功能
- 🏠 **首页展示**: 亲圈故事流，个性化推荐
- 🎨 **现代UI**: 遵循 iOS/Android 设计规范

## 🏗️ 技术架构

### 技术栈

```json
{
  "框架": "React Native 0.79.3",
  "语言": "TypeScript 5.0.4",
  "状态管理": "Redux Toolkit",
  "导航": "React Navigation 6.x",
  "UI组件": "React Native Paper",
  "网络请求": "Axios",
  "本地存储": "AsyncStorage + Keychain",
  "手势处理": "React Native Gesture Handler"
}
```

### 项目结构

```
AppUI/
├── src/                          # 源代码目录
│   ├── components/               # 组件库
│   │   ├── ui/                   # 基础UI组件
│   │   ├── story/                # 故事相关组件
│   │   └── character/            # 人物相关组件
│   ├── screens/                  # 页面组件
│   │   ├── auth/                 # 认证页面
│   │   ├── home/                 # 首页
│   │   ├── story/                # 故事页面
│   │   ├── character/            # 人物页面
│   │   └── profile/              # 个人中心
│   ├── navigation/               # 导航配置
│   │   ├── RootNavigator.tsx     # 根导航
│   │   ├── AuthNavigator.tsx     # 认证导航
│   │   └── MainNavigator.tsx     # 主导航
│   ├── store/                    # Redux状态管理
│   │   ├── index.ts              # Store配置
│   │   └── slices/               # 功能切片
│   │       ├── authSlice.ts      # 认证状态
│   │       ├── storySlice.ts     # 故事状态
│   │       └── characterSlice.ts # 人物状态
│   ├── services/                 # 业务服务
│   │   └── api.ts                # API客户端
│   ├── utils/                    # 工具函数
│   │   └── storage.ts            # 存储工具
│   ├── hooks/                    # 自定义Hooks
│   ├── types/                    # TypeScript类型
│   │   └── index.ts              # 类型定义
│   └── constants/                # 常量配置
│       └── index.ts              # 应用常量
├── android/                      # Android项目文件
├── ios/                          # iOS项目文件
├── App.tsx                       # 应用入口
├── package.json                  # 依赖配置
└── README.md                     # 项目文档
```

## 🚀 快速开始

### 环境要求

- Node.js >= 18.16.0
- React Native CLI
- Android Studio (Android 开发)
- Xcode (iOS 开发)

### 安装依赖

```bash
# 进入项目目录
cd AppUI

# 安装 Node.js 依赖
npm install

# iOS 依赖安装 (仅 macOS)
cd ios && pod install && cd ..
```

### 运行项目

```bash
# 启动 Metro 服务器
npm start

# 运行 Android 版本
npm run android

# 运行 iOS 版本 (仅 macOS)
npm run ios
```

## 📋 开发脚本

```bash
# 启动开发服务器
npm start

# Android 调试
npm run android

# iOS 调试
npm run ios

# 代码检查
npm run lint

# 运行测试
npm test

# 类型检查
npx tsc --noEmit
```

## 🔧 配置说明

### API 配置

在 `src/constants/index.ts` 中配置 API 基础地址：

```typescript
export const API_BASE_URL = 'http://localhost:3000/api/v1'
```

### 主题配置

主题配置位于 `src/constants/index.ts`：

```typescript
export const THEME = {
  colors: {
    primary: '#007AFF',
    secondary: '#5856D6',
    background: '#FFFFFF',
    // ...更多颜色
  },
  spacing: {
    xs: 4, sm: 8, md: 16, lg: 24, xl: 32
  },
  typography: {
    h1: 28, h2: 24, h3: 20, body: 16, caption: 12
  }
}
```

## 🏛️ 状态管理

### Redux Store 结构

```typescript
interface RootState {
  auth: AuthState      // 认证状态
  story: StoryState    // 故事状态
  character: CharacterState // 人物状态
}
```

### 使用示例

```typescript
import { useAppDispatch, useAppSelector } from '../store'
import { login, selectIsAuthenticated } from '../store/slices/authSlice'

const MyComponent = () => {
  const dispatch = useAppDispatch()
  const isAuthenticated = useAppSelector(selectIsAuthenticated)
  
  const handleLogin = () => {
    dispatch(login({ phone: '13800138000', code: '123456' }))
  }
}
```

## 🌐 API 集成

### HTTP 客户端

使用 Axios 封装的企业级 API 客户端：

```typescript
import { apiClient } from '../services/api'

// GET 请求
const stories = await apiClient.get('/stories')

// POST 请求
const newStory = await apiClient.post('/stories', {
  title: '我的故事',
  content: '故事内容...'
})
```

### 错误处理

API 客户端内置统一错误处理：

- 401: 自动清除 Token，跳转登录
- 422: 显示验证错误信息
- 500+: 显示服务器错误信息

## 📱 导航结构

```
RootNavigator
├── AuthNavigator (未认证)
│   └── LoginScreen
└── MainNavigator (已认证)
    ├── HomeScreen (首页)
    ├── StoryScreen (故事)
    ├── CharacterScreen (人物)
    └── ProfileScreen (个人中心)
```

## 🔒 安全存储

### Token 管理

使用 React Native Keychain 安全存储敏感信息：

```typescript
import { setToken, getToken, removeToken } from '../utils/storage'

// 保存 Token
await setToken('your_access_token')

// 获取 Token
const token = await getToken()

// 删除 Token
await removeToken()
```

### 一般数据存储

使用 AsyncStorage 存储非敏感数据：

```typescript
import { setStorageItem, getStorageItem } from '../utils/storage'

// 保存数据
await setStorageItem('user_settings', { theme: 'dark' })

// 获取数据
const settings = await getStorageItem('user_settings')
```

## 🎨 UI 组件规范

### 设计原则

1. **一致性**: 遵循统一的设计语言
2. **可访问性**: 支持无障碍功能
3. **响应式**: 适配不同屏幕尺寸
4. **性能**: 优化组件渲染性能

### 组件命名

- 页面组件: `XxxScreen.tsx`
- 业务组件: `XxxComponent.tsx`
- UI组件: `XxxUI.tsx`

## 🧪 测试策略

> **🎯 企业级测试标准**: 详见 [CLAUDE.md企业级测试开发标准](../CLAUDE.md#🧪-企业级测试开发标准强制执行) - 前后端通用测试架构与质量标准

### 测试层级

1. **单元测试**: Redux Slice、工具函数
2. **集成测试**: API 服务、导航流程
3. **E2E测试**: 关键用户流程

### 运行测试

```bash
# 运行所有测试
npm test

# 监听模式
npm test -- --watch

# 覆盖率报告
npm test -- --coverage
```

## 📦 构建发布

### Debug 构建

```bash
# Android Debug APK
cd android && ./gradlew assembleDebug

# iOS Debug
npm run ios
```

### Release 构建

```bash
# Android Release APK
cd android && ./gradlew assembleRelease

# iOS Release
# 使用 Xcode 打包
```

## 🔍 调试工具

### 开发工具

- **React Native Debugger**: 调试 React Native 应用
- **Flipper**: Facebook 官方调试工具
- **Redux DevTools**: Redux 状态调试

### 日志管理

```typescript
import { DEBUG } from '../constants'

if (DEBUG.ENABLE_LOGS) {
  console.log('Debug information')
}
```

## 📈 性能优化

### 优化策略

1. **组件优化**: 使用 React.memo、useMemo
2. **列表优化**: FlatList 虚拟化
3. **图片优化**: 使用 FastImage
4. **Bundle 分析**: Metro Bundle Analyzer

### 监控指标

- 应用启动时间
- 页面渲染时间
- 内存使用情况
- 网络请求延迟

## 🤝 开发规范

### 代码规范

- 使用 ESLint + Prettier
- 遵循 TypeScript 严格模式
- 统一的命名规范
- 完善的类型定义

### Git 提交规范

```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建/配置更新
```

## 🆘 常见问题

### 依赖问题

Q: 安装依赖时出现版本冲突？
A: 使用 `npm install --legacy-peer-deps`

### 编译问题

Q: Android 编译失败？
A: 清理缓存 `cd android && ./gradlew clean`

Q: iOS 编译失败？
A: 重新安装 Pods `cd ios && pod deintegrate && pod install`

### 运行问题

Q: Metro 服务器启动失败？
A: 清理缓存 `npx react-native start --reset-cache`

## 📚 相关文档导航

- **📋 产品文档**: [产品需求规范](../产品文档/README.md) - 业务需求与产品功能规范
- **🔧 项目文档**: [项目执行情况](../项目文档/README.md) - 开发进度与风险管控
- **🔧 后端服务**: [后端API文档](../AppServe/README.md) - 后端接口与技术文档
- **🤖 开发规范**: [AI助手规范](../CLAUDE.md) - 全项目开发约束和质量标准

## 📞 技术支持

- **项目文档**: 详见项目根目录文档体系
- **后端集成**: 参考 [API接口规范](../对接文档/API接口文档.md)（122个接口完整标准）
- **开发规范**: 遵循 [CLAUDE.md](../CLAUDE.md) 开发约束
- **开发团队**: YGS前端开发团队

## 📄 许可证

本项目采用 MIT 许可证，详见 [LICENSE](LICENSE) 文件。

---

**有故事 APP** - 让每个家庭的故事都被记录和传承 ❤️
