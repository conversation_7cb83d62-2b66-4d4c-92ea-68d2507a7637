/**
 * 分享模块 - 企业级单元测试
 */

import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { SharesModule } from "./shares.module";
import { SharesService } from "./shares.service";
import { SharesController } from "./shares.controller";

describe("SharesModule - 企业级模块测试", () => {
  let module: TestingModule;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [SharesModule],
    })
      .overrideProvider(SharesService)
      .useValue({
        findAll: jest.fn(),
        findOne: jest.fn(),
        create: jest.fn(),
        update: jest.fn(),
        remove: jest.fn(),
      })
      .compile();
  });

  afterEach(async () => {
    if (module) {
      await module.close();
    }
  });

  describe("模块初始化", () => {
    it("should compile the module", () => {
      expect(module).toBeDefined();
    });

    it("should provide SharesService", () => {
      const service = module.get<SharesService>(SharesService);
      expect(service).toBeDefined();
    });

    it("should provide SharesController", () => {
      const controller = module.get<SharesController>(SharesController);
      expect(controller).toBeDefined();
    });
  });

  describe("依赖注入", () => {
    it("should inject SharesService into SharesController", () => {
      const controller = module.get<SharesController>(SharesController);
      const service = module.get<SharesService>(SharesService);

      expect(controller).toBeDefined();
      expect(service).toBeDefined();
    });
  });

  describe("模块配置", () => {
    it("should be a valid NestJS module", () => {
      expect(SharesModule).toBeDefined();
      expect(typeof SharesModule).toBe("function");
    });
  });
});
