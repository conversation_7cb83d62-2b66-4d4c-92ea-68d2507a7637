import {
  Injectable,
  BadRequestException,
  ConflictException,
  NotFoundException,
  ForbiddenException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import type { EntityManager } from "typeorm";
import { Repository, DataSource, <PERSON><PERSON><PERSON>, In, <PERSON><PERSON><PERSON> } from "typeorm";
import { LightRequest } from "../characters/entities/light-request.entity";
import { CharacterLighting } from "../characters/entities/character-lighting.entity";
import { Character } from "../characters/entities/character.entity";
import { Story } from "../stories/entities/story.entity";
import { User } from "../users/entities/user.entity";
import { NotificationsService } from "../users/notifications.service";
import { NotificationType } from "../users/entities/notification.entity";
import { EnhancedRedisService } from "../../common/services/enhanced-redis.service";
import type { SubmitLightRequestDto, LightingStatusResponseDto } from "./dto";
import { LightRequestResponseDto } from "./dto";
import { LightingValidationResultDto } from "./dto/lighting-validation-result.dto";
// 删除未使用的 ServiceResult, UserPayload 导入

@Injectable()
export class LightingService {
  constructor(
    @InjectRepository(LightRequest)
    private lightRequestRepository: Repository<LightRequest>,
    @InjectRepository(CharacterLighting)
    private characterLightingRepository: Repository<CharacterLighting>,
    @InjectRepository(Character)
    private characterRepository: Repository<Character>,
    @InjectRepository(Story)
    private storyRepository: Repository<Story>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private dataSource: DataSource,
    private notificationsService: NotificationsService,
    private enhancedRedisService: EnhancedRedisService,
  ) {}

  /**
   * 提交点亮申请
   */
  async submitLightRequest(
    storyId: string,
    characterId: string,
    requesterId: string,
    data: SubmitLightRequestDto,
  ): Promise<LightRequestResponseDto> {
    // 获取故事和人物信息
    const story = await this.storyRepository.findOne({
      where: { id: storyId },
      relations: ["user"],
    });

    if (!story) {
      throw new NotFoundException("故事不存在");
    }

    const character = await this.characterRepository.findOne({
      where: { id: characterId },
      relations: ["creator"],
    });

    if (!character) {
      throw new NotFoundException("人物不存在");
    }

    // 校验人物是否属于该故事
    const storyCharacter = await this.characterRepository
      .createQueryBuilder("character")
      .innerJoin("character.stories", "story")
      .where("character.id = :characterId", { characterId: characterId })
      .andWhere("story.id = :storyId", { storyId: storyId })
      .getOne();

    if (!storyCharacter) {
      throw new BadRequestException("该人物不属于指定故事");
    }

    // 校验申请者不能是故事作者
    if (story.userId === requesterId) {
      throw new BadRequestException("不能申请点亮自己故事中的人物");
    }

    // 校验申请者不能是人物创作者
    if (character.creatorId === requesterId) {
      throw new BadRequestException("不能申请点亮自己创建的人物");
    }

    // 执行核心业务校验
    await this.validateLightingRequest(requesterId, story.userId, characterId);

    // 自动获取用户注册手机号
    const user = await this.userRepository.findOne({
      where: { id: requesterId },
      select: ["phone"],
    });

    if (!user) {
      throw new NotFoundException("用户不存在");
    }

    // 根据用户选择决定是否包含脱敏手机号
    let phoneVerification: string | null = null;
    if (data.includePhone && user.phone) {
      phoneVerification = this.maskPhoneNumber(user.phone);
    }

    // 检查是否已有待处理的申请
    const existingRequest = await this.lightRequestRepository.findOne({
      where: {
        requesterId,
        characterId: characterId,
        status: "pending",
      },
    });

    if (existingRequest) {
      throw new ConflictException("您已提交过该人物的点亮申请，请等待处理");
    }

    // 🔥 企业级自动审批逻辑：检查是否为重复点亮（已点亮用户申请同一人物）
    const isAutoApproval = await this.shouldAutoApprove(
      requesterId,
      characterId,
    );

    // 创建点亮申请
    const lightRequest = this.lightRequestRepository.create({
      requesterId,
      storyId: storyId,
      characterId: characterId,
      storyAuthorId: story.userId,
      phoneVerification, // 可能为null
      hasPhoneVerification: !!data.includePhone,
      message: data.reason,
      status: isAutoApproval ? "approved" : "pending",
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7天后过期
      processedAt: isAutoApproval ? new Date() : undefined,
    });

    const savedRequest = await this.lightRequestRepository.save(lightRequest);

    // 如果自动通过，立即创建点亮关系
    if (isAutoApproval) {
      await this.confirmLightingInternal(savedRequest);

      // 发送正面通知给申请者
      await this.notificationsService.createNotification({
        userId: requesterId,
        type: NotificationType.CHARACTER_LIGHTING,
        title: "点亮申请已自动通过",
        content: `您对人物"${character.name}"的点亮申请已自动确认`,
        relatedId: savedRequest.id,
      });

      // 获取完整的申请信息用于返回DTO
      const autoApprovedRequest = await this.lightRequestRepository.findOne({
        where: { id: savedRequest.id },
        relations: ["story", "character", "requester"],
      });

      if (!autoApprovedRequest) {
        throw new NotFoundException("自动通过的申请记录不存在");
      }

      const responseDto = new LightRequestResponseDto(autoApprovedRequest);
      responseDto.isAutoApproved = true;
      responseDto.message = "申请已自动通过，无需等待审批";

      return responseDto;
    }

    // 发送通知给故事作者
    await this.notificationsService.createNotification({
      userId: story.userId,
      type: NotificationType.CHARACTER_LIGHTING,
      title: "收到新的人物点亮申请",
      content: `用户申请点亮您故事"${story.title}"中的人物"${character.name}"`,
      relatedId: savedRequest.id,
    });

    // 返回响应
    const responseRequest = await this.lightRequestRepository.findOne({
      where: { id: savedRequest.id },
      relations: ["story", "character", "requester"],
    });

    if (!responseRequest) {
      throw new NotFoundException("创建的申请记录不存在");
    }

    return new LightRequestResponseDto(responseRequest);
  }

  /**
   * 校验点亮申请业务规则（增强版）
   */
  async validateLightingRequest(
    requesterId: string,
    storyAuthorId: string,
    characterId: string,
  ): Promise<void> {
    // 1. 获取申请者用户信息（包含异常状态）
    const requester = await this.userRepository.findOne({
      where: { id: requesterId },
    });

    if (!requester) {
      throw new NotFoundException("申请用户不存在");
    }

    // 2. 校验用户异常状态
    await this.validateUserAnomalyStatus(requester);

    // 3. 校验点亮频率限制
    await this.validateLightingFrequency(requester);

    // 4. 校验人物点亮排他性约束
    await this.validateCharacterExclusivity(requesterId, characterId);

    // 5. 🔥 企业级用户点亮限制验证：每个用户只能点亮每个故事作者的一个人物实体
    await this.validateUserLightingLimit(
      requesterId,
      storyAuthorId,
      characterId,
    );

    // 6. 🔥 企业级重复申请检查：防止重复申请同一人物实体
    await this.validateNoDuplicateCharacterApplication(
      requesterId,
      characterId,
    );

    // 7. 校验是否有待处理的其他申请
    const pendingRequest = await this.lightRequestRepository.findOne({
      where: {
        requesterId,
        storyAuthorId,
        status: "pending",
      },
    });

    if (pendingRequest) {
      throw new ConflictException("您有待该用户处理的点亮申请，请等待处理完成");
    }
  }

  /**
   * 校验用户异常状态
   */
  private async validateUserAnomalyStatus(user: User): Promise<void> {
    // 检查用户是否被暂停
    if (user.isSuspended()) {
      throw new ForbiddenException("您的账户已被暂停，无法提交点亮申请");
    }

    // 检查用户是否被限制
    if (user.isLightingRestricted()) {
      const restrictedUntil =
        user.lightingRestrictedUntil?.toLocaleString("zh-CN");
      throw new ForbiddenException(
        `您当前被限制提交点亮申请，限制将于 ${restrictedUntil} 解除`,
      );
    }

    // 检查用户是否可以申请点亮
    if (!user.canApplyLighting()) {
      throw new ForbiddenException("您当前无法提交点亮申请，请检查账户状态");
    }
  }

  /**
   * 🔥 企业级点亮申请频率验证
   * 优化为每日5个不同人物实体的限制
   */
  private async validateLightingFrequency(user: User): Promise<void> {
    // 获取今日已申请的不同人物实体数量
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // 查询今日已申请的不同人物实体
    const todayApplications = await this.lightRequestRepository
      .createQueryBuilder("request")
      .select("DISTINCT request.characterId", "characterId")
      .where("request.requesterId = :userId", { userId: user.id })
      .andWhere("request.createdAt >= :today", { today })
      .andWhere("request.createdAt < :tomorrow", { tomorrow })
      .getRawMany();

    // 检查每日申请限制（5个不同人物实体）
    if (todayApplications.length >= 5) {
      await this.recordInvalidApplication(user, "DAILY_LIMIT_EXCEEDED");
      throw new BadRequestException(
        "您今日已申请5个不同人物的点亮，已达每日上限，请明日再试",
      );
    }

    // 检查短时间内申请频率（防止恶意刷请求）
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    const recentApplications = await this.lightRequestRepository.count({
      where: {
        requesterId: user.id,
        createdAt: MoreThan(fiveMinutesAgo),
      },
    });

    if (recentApplications >= 2) {
      await this.recordInvalidApplication(user, "FREQUENCY_TOO_HIGH");
      throw new BadRequestException("您的申请过于频繁，请等待5分钟后再试");
    }
  }

  /**
   * 🔥 企业级人物点亮排他性约束验证
   * 基于Character表的lighterUserId字段实现排他性控制
   */
  private async validateCharacterExclusivity(
    requesterId: string,
    characterId: string,
  ): Promise<boolean> {
    // 直接从Character表查询点亮状态 - 企业级性能优化
    const character = await this.characterRepository.findOne({
      where: { id: characterId },
      relations: ["lighterUser"],
    });

    if (!character) {
      throw new NotFoundException("人物不存在");
    }

    // 情况1: 人物未被点亮 - 允许申请
    if (!character.lighterUserId) {
      return true;
    }

    // 情况2: 申请者就是点亮者 - 允许重复申请（不同故事中的确认）
    if (character.lighterUserId === requesterId) {
      return true; // 返回true表示可以申请，但会自动通过审批
    }

    // 情况3: 人物已被他人点亮 - 拒绝申请
    const requester = await this.userRepository.findOne({
      where: { id: requesterId },
    });
    if (requester) {
      await this.recordInvalidApplication(
        requester,
        "CHARACTER_ALREADY_LIGHTED",
      );
    }

    throw new ConflictException(
      `该人物已被用户"${character.lighterUser?.nickname || "其他用户"}"认证，无法重复申请`,
    );
  }

  /**
   * 🔥 企业级用户点亮限制验证
   * 确保每个用户只能点亮每个故事作者的一个人物实体
   */
  private async validateUserLightingLimit(
    requesterId: string,
    storyAuthorId: string,
    characterId: string,
  ): Promise<void> {
    // 查询申请者已点亮的该故事作者创建的所有人物
    const lightedCharacters = await this.characterRepository.find({
      where: {
        creatorId: storyAuthorId,
        lighterUserId: requesterId,
      },
    });

    // 如果已点亮人物且不是当前申请的人物，则拒绝申请
    if (lightedCharacters.length > 0) {
      const lightedCharacter = lightedCharacters[0];

      // 如果申请的是已点亮的同一个人物，允许（重复点亮）
      if (lightedCharacter.id === characterId) {
        return; // 允许重复点亮同一人物
      }

      // 申请不同的人物，拒绝申请
      const requester = await this.userRepository.findOne({
        where: { id: requesterId },
      });
      if (requester) {
        await this.recordInvalidApplication(requester, "ALREADY_LIGHTED_OTHER");
      }

      throw new ConflictException(
        `您已点亮过该用户的人物"${lightedCharacter.name}"，每个用户只能点亮一个人物实体`,
      );
    }
  }

  /**
   * 🔥 企业级申请重复检查
   * 防止用户重复申请同一人物实体（无论在哪个故事中）
   */
  private async validateNoDuplicateCharacterApplication(
    requesterId: string,
    characterId: string,
  ): Promise<void> {
    // 检查是否已经申请过该人物实体（任何状态的申请）
    const existingRequest = await this.lightRequestRepository.findOne({
      where: {
        requesterId,
        characterId,
      },
    });

    if (existingRequest) {
      throw new ConflictException(
        `您已申请过该人物的点亮，无法重复申请。申请状态：${existingRequest.getStatusDescription()}`,
      );
    }
  }

  /**
   * 🔥 企业级自动审批判断
   * 当已点亮用户申请同一人物时，自动通过审批
   */
  private async shouldAutoApprove(
    requesterId: string,
    characterId: string,
  ): Promise<boolean> {
    const character = await this.characterRepository.findOne({
      where: { id: characterId },
    });

    // 如果人物已被申请者点亮，则自动通过
    return character?.lighterUserId === requesterId;
  }

  /**
   * 🔥 企业级内部点亮确认方法
   * 用于自动审批和手动审批的统一处理
   */
  private async confirmLightingInternal(request: LightRequest): Promise<void> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 更新人物点亮状态
      const character = await queryRunner.manager.findOne(Character, {
        where: { id: request.characterId },
      });

      if (character && !character.lighterUserId) {
        // 首次点亮：设置点亮用户和首次点亮时间
        await queryRunner.manager.update(Character, request.characterId, {
          isLighted: true,
          lighterUserId: request.requesterId,
          firstLightedAt: new Date(),
          lightingCount: 1,
        });
      } else if (character && character.lighterUserId === request.requesterId) {
        // 重复点亮：增加点亮次数
        await queryRunner.manager.update(Character, request.characterId, {
          lightingCount: character.lightingCount + 1,
        });
      }

      // 创建点亮关系记录
      const characterLighting = queryRunner.manager.create(CharacterLighting, {
        characterId: request.characterId,
        storyId: request.storyId,
        lighterUserId: request.requesterId,
        creatorUserId: request.storyAuthorId,
        requestId: request.id,
        status: "active",
        confirmedAt: new Date(),
      });

      await queryRunner.manager.save(characterLighting);
      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * 记录无效申请并更新用户异常状态
   */
  private async recordInvalidApplication(
    user: User,
    reason:
      | "ALREADY_LIGHTED_OTHER"
      | "DAILY_LIMIT_EXCEEDED"
      | "HOURLY_INVALID_LIMIT_EXCEEDED"
      | "CHARACTER_ALREADY_LIGHTED"
      | "FREQUENCY_TOO_HIGH",
  ): Promise<void> {
    const now = new Date();

    // 更新用户的无效申请记录
    await this.userRepository.update(user.id, {
      lastInvalidLightingAttempt: now,
      dailyLightingAttempts: () => "daily_lighting_attempts + 1",
    });

    // 检查是否需要触发警告或限制
    await this.checkAndUpdateAnomalyStatus(user.id, reason);
  }

  /**
   * 检查并更新用户异常状态
   */
  private async checkAndUpdateAnomalyStatus(
    userId: string,
    _reason: string,
  ): Promise<void> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) return;

    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

    // 检查1小时内无效申请次数
    const recentInvalidCount = await this.countRecentInvalidAttempts(
      userId,
      oneHourAgo,
    );

    // 判断是否触发警告
    if (user.shouldTriggerWarning() || recentInvalidCount >= 3) {
      await this.triggerUserWarning(user);
    }

    // 判断是否触发限制
    if (user.shouldTriggerRestriction() || recentInvalidCount >= 5) {
      await this.triggerUserRestriction(user);
    }

    // 判断是否触发暂停
    if (user.shouldTriggerSuspension()) {
      await this.triggerUserSuspension(user);
    }
  }

  /**
   * 统计最近无效申请次数
   */
  private async countRecentInvalidAttempts(
    _userId: string,
    _since: Date,
  ): Promise<number> {
    // 这里可以通过日志表或其他方式统计，简化实现中假设返回0
    // 在实际实现中，应该有专门的申请记录表来跟踪
    return 0;
  }

  /**
   * 触发用户警告
   */
  private async triggerUserWarning(user: User): Promise<void> {
    if (user.anomalyStatus === "normal") {
      await this.userRepository.update(user.id, {
        anomalyStatus: "warning",
        anomalyWarningCount: () => "anomaly_warning_count + 1",
      });

      // 发送警告通知
      await this.notificationsService.createNotification({
        userId: user.id,
        type: NotificationType.SYSTEM_NOTICE,
        title: "申请频率警告",
        content:
          "您的点亮申请过于频繁，请仔细确认后再申请。继续违规操作可能导致账户受限。",
        relatedId: undefined,
      });
    }
  }

  /**
   * 触发用户限制
   */
  private async triggerUserRestriction(user: User): Promise<void> {
    const restrictionEndTime = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7天后

    await this.userRepository.update(user.id, {
      anomalyStatus: "restricted",
      anomalyRestrictionCount: () => "anomaly_restriction_count + 1",
      lightingRestrictedUntil: restrictionEndTime,
    });

    // 发送限制通知
    await this.notificationsService.createNotification({
      userId: user.id,
      type: NotificationType.SYSTEM_NOTICE,
      title: "账户功能受限",
      content: `由于频繁提交无效申请，您的点亮申请功能已被限制7天，将于 ${restrictionEndTime.toLocaleString("zh-CN")} 自动恢复。`,
      relatedId: undefined,
    });
  }

  /**
   * 触发用户暂停
   */
  private async triggerUserSuspension(user: User): Promise<void> {
    await this.userRepository.update(user.id, {
      anomalyStatus: "suspended",
      isActive: false,
    });

    // 发送暂停通知
    await this.notificationsService.createNotification({
      userId: user.id,
      type: NotificationType.SYSTEM_NOTICE,
      title: "账户已暂停",
      content: "由于多次违规操作，您的账户已被暂停。如有疑问，请联系客服。",
      relatedId: undefined,
    });
  }

  /**
   * 详细验证点亮申请条件（不抛出异常，返回详细结果）
   */
  async validateLightingRequestDetailed(
    requesterId: string,
    storyId: string,
    characterId: string,
  ): Promise<LightingValidationResultDto> {
    const result = new LightingValidationResultDto({
      isValid: false,
      characterExists: false,
      phoneMatches: false,
      noDuplicateApplication: false,
      characterNotLightedByOthers: false,
      userStatusNormal: false,
      rateLimitValid: false,
      userAnomalyStatus: "normal",
      remainingDailyAttempts: 0,
    });

    try {
      // 1. 检查用户是否存在
      const requester = await this.userRepository.findOne({
        where: { id: requesterId },
      });

      if (!requester) {
        result.rejectionReason = "申请用户不存在";
        return result;
      }

      result.userAnomalyStatus = requester.anomalyStatus;

      // 2. 检查故事和人物是否存在
      const [story, character] = await Promise.all([
        this.storyRepository.findOne({
          where: { id: storyId },
          relations: ["user"],
        }),
        this.characterRepository.findOne({
          where: { id: characterId },
          relations: ["creator"],
        }),
      ]);

      if (!story) {
        result.rejectionReason = "故事不存在";
        return result;
      }

      if (!character) {
        result.rejectionReason = "人物不存在";
        return result;
      }

      result.characterExists = true;

      // 3. 校验人物是否属于该故事
      const storyCharacter = await this.characterRepository
        .createQueryBuilder("character")
        .innerJoin("character.stories", "story")
        .where("character.id = :characterId", { characterId })
        .andWhere("story.id = :storyId", { storyId })
        .getOne();

      if (!storyCharacter) {
        result.rejectionReason = "该人物不属于指定故事";
        return result;
      }

      // 4. 校验申请者不能是故事作者或人物创作者
      if (story.userId === requesterId) {
        result.rejectionReason = "不能申请点亮自己故事中的人物";
        return result;
      }

      if (character.creatorId === requesterId) {
        result.rejectionReason = "不能申请点亮自己创建的人物";
        return result;
      }

      // 5. 校验用户异常状态
      if (requester.isSuspended()) {
        result.rejectionReason = "您的账户已被暂停，无法提交点亮申请";
        return result;
      }

      if (requester.isLightingRestricted()) {
        const restrictedUntil =
          requester.lightingRestrictedUntil?.toLocaleString("zh-CN");
        result.rejectionReason = `您当前被限制提交点亮申请，限制将于 ${restrictedUntil} 解除`;
        return result;
      }

      if (requester.canApplyLighting()) {
        result.userStatusNormal = true;
      } else {
        result.rejectionReason = "您当前无法提交点亮申请，请检查账户状态";
        return result;
      }

      // 6. 校验点亮频率限制
      if (requester.shouldResetDailyAttempts()) {
        await this.userRepository.update(requester.id, {
          dailyLightingAttempts: 0,
          lightingAttemptResetDate: new Date(),
        });
        requester.dailyLightingAttempts = 0;
      }

      result.remainingDailyAttempts = Math.max(
        0,
        20 - requester.dailyLightingAttempts,
      );

      if (requester.dailyLightingAttempts >= 20) {
        result.rejectionReason =
          "您今日的点亮申请次数已达上限（20次），请明日再试";
        return result;
      }

      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
      if (
        requester.lastInvalidLightingAttempt &&
        requester.lastInvalidLightingAttempt > oneHourAgo
      ) {
        const recentInvalidAttempts = await this.countRecentInvalidAttempts(
          requester.id,
          oneHourAgo,
        );
        if (recentInvalidAttempts >= 3) {
          result.rejectionReason = "您在1小时内无效申请次数过多，请稍后再试";
          result.cooldownSeconds = Math.ceil(
            (requester.lastInvalidLightingAttempt.getTime() +
              60 * 60 * 1000 -
              Date.now()) /
              1000,
          );
          return result;
        }
      }

      result.rateLimitValid = true;

      // 7. 校验人物点亮排他性约束
      const existingLighting = await this.characterLightingRepository.findOne({
        where: { characterId },
        relations: ["lighterUser"],
      });

      if (existingLighting) {
        if (existingLighting.lighterUserId === requesterId) {
          result.characterNotLightedByOthers = true; // 同一用户可以重新申请
        } else {
          result.rejectionReason = `该人物已被用户"${existingLighting.lighterUser?.nickname || "其他用户"}"认证，无法重复申请`;
          return result;
        }
      } else {
        result.characterNotLightedByOthers = true;
      }

      // 8. 校验是否已点亮过该故事作者的其他人物
      const existingUserLighting =
        await this.characterLightingRepository.findOne({
          where: {
            lighterUserId: requesterId,
            creatorUserId: story.userId,
          },
        });

      if (existingUserLighting) {
        result.rejectionReason =
          "您已点亮过该用户的其他人物，每个用户只能点亮一个人物";
        return result;
      }

      // 9. 校验是否有待处理的申请
      const pendingRequest = await this.lightRequestRepository.findOne({
        where: {
          requesterId,
          storyAuthorId: story.userId,
          status: "pending",
        },
      });

      if (pendingRequest) {
        result.rejectionReason = "您有待该用户处理的点亮申请，请等待处理完成";
        return result;
      }

      result.noDuplicateApplication = true;

      // 10. 校验手机号（这里简化为true，实际需要验证）
      result.phoneMatches = true;

      // 所有验证通过
      result.isValid = true;
    } catch (error) {
      result.rejectionReason = `验证过程中发生错误: ${error instanceof Error ? error.message : String(error)}`;
    }

    return result;
  }

  /**
   * 校验手机号一致性
   * @deprecated 此方法已废弃，新逻辑通过脱敏手机号处理用户隐私
   */
  async validatePhoneVerification(
    userId: string,
    phoneNumber: string,
  ): Promise<boolean> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      select: ["phone"],
    });

    if (!user) {
      throw new NotFoundException("用户不存在");
    }

    if (user.phone !== phoneNumber) {
      throw new BadRequestException("提供的手机号与账户绑定的手机号不一致");
    }

    return true;
  }

  /**
   * 获取用户的待处理申请（作为故事作者）
   */
  async getPendingRequests(
    storyAuthorId: string,
  ): Promise<LightRequestResponseDto[]> {
    const requests = await this.lightRequestRepository.find({
      where: {
        storyAuthorId,
        status: "pending",
      },
      relations: ["story", "character", "requester"],
      order: { createdAt: "DESC" },
    });

    return requests.map((request) => new LightRequestResponseDto(request));
  }

  /**
   * 获取用户的所有申请记录（作为申请者）
   */
  async getUserRequests(
    requesterId: string,
  ): Promise<LightRequestResponseDto[]> {
    const requests = await this.lightRequestRepository.find({
      where: { requesterId },
      relations: ["story", "character", "requester"],
      order: { createdAt: "DESC" },
    });

    return requests.map((request) => new LightRequestResponseDto(request));
  }

  /**
   * 获取人物的点亮状态（带Redis缓存）
   */
  async getLightingStatus(
    characterId: string,
    currentUserId?: string,
  ): Promise<LightingStatusResponseDto> {
    // 尝试从Redis缓存获取
    const cacheKey = `character_lighting_status:${characterId}`;
    let lighting =
      await this.enhancedRedisService.getCache<CharacterLighting | null>(
        cacheKey,
      );

    if (lighting === null) {
      // 缓存未命中，查询数据库
      lighting = await this.characterLightingRepository.findOne({
        where: { characterId },
        relations: ["lighterUser"],
      });

      // 缓存结果（5分钟）
      if (lighting) {
        await this.enhancedRedisService.setCache(
          cacheKey,
          lighting,
          5 * 60 * 1000,
        );
      }
    }

    const result: LightingStatusResponseDto = {
      isLighted: !!lighting,
      canApplyLighting: false,
      userRequestStatus: "none",
    };

    if (lighting) {
      result.lighterId = lighting.lighterUserId;
      result.lighterNickname =
        lighting.lighterUser?.nickname || lighting.lighterUser?.username;
      result.lightedAt = lighting.confirmedAt;
    }

    // 如果提供了当前用户ID，检查申请权限
    if (currentUserId) {
      await this.checkUserLightingEligibility(
        characterId,
        currentUserId,
        result,
      );
    }

    return result;
  }

  /**
   * 检查用户是否可以申请点亮
   */
  private async checkUserLightingEligibility(
    characterId: string,
    userId: string,
    result: LightingStatusResponseDto,
  ): Promise<void> {
    // 获取人物和故事信息
    const character = await this.characterRepository.findOne({
      where: { id: characterId },
      relations: ["stories"],
    });

    if (!character?.stories || character.stories.length === 0) {
      result.cannotApplyReason = "人物信息不完整";
      return;
    }

    // 获取人物所属的第一个故事（通常一个人物只属于一个故事）
    const story = character.stories[0];

    // 不能申请点亮自己的人物
    if (character.creatorId === userId) {
      result.cannotApplyReason = "不能申请点亮自己创建的人物";
      return;
    }

    // 不能申请点亮自己故事中的人物
    if (story.userId === userId) {
      result.cannotApplyReason = "不能申请点亮自己故事中的人物";
      return;
    }

    // 检查是否已点亮该故事作者的其他人物
    const existingLighting = await this.characterLightingRepository.findOne({
      where: {
        lighterUserId: userId,
        creatorUserId: story.userId,
      },
    });

    if (existingLighting) {
      result.cannotApplyReason =
        "您已点亮过该用户的其他人物，每个用户只能点亮一个人物";
      return;
    }

    // 检查当前用户的申请状态
    const userRequest = await this.lightRequestRepository.findOne({
      where: {
        requesterId: userId,
        characterId,
      },
      order: { createdAt: "DESC" },
    });

    if (userRequest) {
      result.userRequestStatus = userRequest.status;
      if (userRequest.status === "pending") {
        result.cannotApplyReason = "您已提交申请，请等待处理";
        return;
      } else if (userRequest.status === "rejected") {
        result.cannotApplyReason = "您的申请已被拒绝";
        return;
      } else if (userRequest.status === "approved") {
        result.cannotApplyReason = "您已成功点亮此人物";
        return;
      }
    }

    // 如果人物未被点亮且用户符合条件，可以申请
    if (!result.isLighted) {
      result.canApplyLighting = true;
    } else {
      result.cannotApplyReason = "该人物已被其他用户点亮";
    }
  }

  /**
   * 确认点亮申请（企业级分布式锁实现）
   */
  async confirmLightRequest(
    requestId: string,
    confirmerId: string,
  ): Promise<void> {
    // 获取分布式锁，防止并发处理同一申请
    const lockKey = `light_request_confirm:${requestId}`;
    const lock = await this.enhancedRedisService.acquireLock(lockKey, 30000); // 30秒锁定

    if (!lock) {
      throw new ConflictException("该申请正在被处理中，请稍后再试");
    }

    try {
      // 开启数据库事务
      await this.dataSource.transaction(async (manager: EntityManager) => {
        // 验证申请状态
        const request = await manager.findOne(LightRequest, {
          where: { id: requestId, status: "pending" },
          relations: ["story", "character", "requester"],
        });

        if (!request) {
          throw new NotFoundException("申请不存在或已被处理");
        }

        // 验证确认者权限（必须是故事作者）
        if (request.storyAuthorId !== confirmerId) {
          throw new ForbiddenException("只有故事作者可以确认点亮申请");
        }

        // 验证申请是否过期
        if (request.isExpired()) {
          throw new BadRequestException("申请已过期");
        }

        // 🔥 企业级人物点亮状态验证：基于Character表的lighterUserId
        const character = await manager.findOne(Character, {
          where: { id: request.characterId },
        });

        if (!character) {
          throw new NotFoundException("人物不存在");
        }

        // 检查人物是否已被其他用户点亮
        if (
          character.lighterUserId &&
          character.lighterUserId !== request.requesterId
        ) {
          throw new ConflictException(`该人物已被其他用户点亮，无法重复确认`);
        }

        // 更新申请状态
        await manager.update(LightRequest, requestId, {
          status: "approved",
          processedAt: new Date(),
        });

        // 🔥 使用统一的内部确认逻辑（会自动处理人物状态更新和点亮关系创建）
        request.status = "approved";
        await this.confirmLightingInternal(request);

        // 发送确认通知（正面通知）
        await this.notificationsService.createNotification({
          userId: request.requester.id,
          type: NotificationType.CHARACTER_LIGHTING,
          title: "人物点亮成功",
          content: `您成功点亮了故事"${request.story.title}"中的人物"${request.character.name}"`,
          relatedId: requestId,
        });

        // 清除相关缓存
        await this.clearLightingCache(
          request.characterId,
          request.requester.id,
          request.storyAuthorId,
        );
      });
    } finally {
      // 释放分布式锁
      await this.enhancedRedisService.releaseLock(lockKey, lock);
    }
  }

  /**
   * 拒绝点亮申请（情绪保护 - 不发送拒绝通知）
   */
  async rejectLightRequest(
    requestId: string,
    rejecterId: string,
    reason?: string,
  ): Promise<void> {
    // 验证申请状态
    const request = await this.lightRequestRepository.findOne({
      where: { id: requestId, status: "pending" },
      relations: ["story", "character", "requester"],
    });

    if (!request) {
      throw new NotFoundException("申请不存在或已被处理");
    }

    // 验证拒绝者权限（必须是故事作者）
    if (request.storyAuthorId !== rejecterId) {
      throw new ForbiddenException("只有故事作者可以拒绝点亮申请");
    }

    // 验证申请是否过期
    if (request.isExpired()) {
      throw new BadRequestException("申请已过期");
    }

    // 更新申请状态为已拒绝
    await this.lightRequestRepository.update(requestId, {
      status: "rejected",
      processedAt: new Date(),
      // 可以记录拒绝原因，但不发送给申请者（情绪保护）
      message: reason ? `拒绝原因: ${reason}` : request.message,
    });

    // 情绪保护：不发送拒绝通知给申请者
    // 申请者可以通过查询申请状态了解结果，但不会收到主动推送
  }

  /**
   * 获取用户点亮的人物（点亮集）
   */
  async getUserLightedCharacters(userId: string): Promise<CharacterLighting[]> {
    return await this.characterLightingRepository.find({
      where: { lighterUserId: userId },
      relations: ["character", "story", "creatorUser"],
      order: { confirmedAt: "DESC" },
    });
  }

  /**
   * 获取人物的所有点亮记录
   */
  async getCharacterLightings(
    characterId: string,
  ): Promise<CharacterLighting[]> {
    return await this.characterLightingRepository.find({
      where: { characterId },
      relations: ["lighterUser", "story", "character"],
      order: { confirmedAt: "DESC" },
    });
  }

  /**
   * 获取用户的点亮历史（包括作为创作者被点亮的记录）
   */
  async getLightingHistory(userId: string): Promise<{
    lightedByUser: CharacterLighting[];
    lightedByOthers: CharacterLighting[];
  }> {
    const [lightedByUser, lightedByOthers] = await Promise.all([
      // 用户点亮的人物
      this.characterLightingRepository.find({
        where: { lighterUserId: userId },
        relations: ["character", "story", "creatorUser"],
        order: { confirmedAt: "DESC" },
      }),
      // 用户创作的人物被其他人点亮
      this.characterLightingRepository.find({
        where: { creatorUserId: userId },
        relations: ["character", "story", "lighterUser"],
        order: { confirmedAt: "DESC" },
      }),
    ]);

    return {
      lightedByUser,
      lightedByOthers,
    };
  }

  /**
   * 获取用户统计数据
   */
  async getUserLightingStats(userId: string): Promise<{
    totalLighted: number;
    totalReceived: number;
    pendingRequests: number;
    thisMonthLighted: number;
  }> {
    // 验证用户是否存在
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException("用户不存在");
    }

    const now = new Date();
    const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);

    try {
      const [totalLighted, totalReceived, pendingRequests, thisMonthLighted] =
        await Promise.all([
          // 用户点亮的总数
          this.characterLightingRepository.count({
            where: { lighterUserId: userId },
          }),
          // 用户创作的人物被点亮总数
          this.characterLightingRepository.count({
            where: { creatorUserId: userId },
          }),
          // 待处理申请数
          this.lightRequestRepository.count({
            where: {
              storyAuthorId: userId,
              status: "pending",
            },
          }),
          // 本月点亮数
          this.characterLightingRepository.count({
            where: {
              lighterUserId: userId,
              confirmedAt: MoreThan(thisMonthStart),
            },
          }),
        ]);

      return {
        totalLighted,
        totalReceived,
        pendingRequests,
        thisMonthLighted,
      };
    } catch (error) {
      throw new BadRequestException(
        `获取用户统计数据失败: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  /**
   * 清理过期申请（定时任务）
   */
  async cleanupExpiredRequests(): Promise<{ cleaned: number }> {
    const result = await this.lightRequestRepository.update(
      {
        status: "pending",
        expiresAt: LessThan(new Date()),
      },
      {
        status: "expired",
        processedAt: new Date(),
      },
    );

    return { cleaned: result.affected || 0 };
  }

  /**
   * 同步点亮状态（检查数据一致性）
   */
  async syncLightingStatus(): Promise<{ synced: number }> {
    // 查找应该标记为已点亮但未标记的人物
    const unSyncedCharacters = await this.characterRepository
      .createQueryBuilder("character")
      .leftJoin(
        "character_lightings",
        "lighting",
        "lighting.character_id = character.id",
      )
      .where("character.is_lighted = false")
      .andWhere("lighting.id IS NOT NULL")
      .getMany();

    // 更新这些人物的状态
    if (unSyncedCharacters.length > 0) {
      const characterIds = unSyncedCharacters.map((char) => char.id);
      await this.characterRepository.update(
        { id: In(characterIds) },
        { isLighted: true },
      );
    }

    // 查找标记为已点亮但实际未点亮的人物
    const overSyncedCharacters = await this.characterRepository
      .createQueryBuilder("character")
      .leftJoin(
        "character_lightings",
        "lighting",
        "lighting.character_id = character.id",
      )
      .where("character.is_lighted = true")
      .andWhere("lighting.id IS NULL")
      .getMany();

    // 更新这些人物的状态
    if (overSyncedCharacters.length > 0) {
      const characterIds = overSyncedCharacters.map((char) => char.id);
      await this.characterRepository.update(
        { id: In(characterIds) },
        { isLighted: false },
      );
    }

    return { synced: unSyncedCharacters.length + overSyncedCharacters.length };
  }

  /**
   * 清除点亮相关缓存
   */
  private async clearLightingCache(
    characterId: string,
    lighterId: string,
    creatorId: string,
  ): Promise<void> {
    const cacheKeys = [
      `character_lighting_status:${characterId}`,
      `user_lighted_characters:${lighterId}`,
      `user_lighting_stats:${lighterId}`,
      `user_lighting_stats:${creatorId}`,
      `character_lightings:${characterId}`,
    ];

    await Promise.all(
      cacheKeys.map((key) => this.enhancedRedisService.delCache(key)),
    );
  }

  /**
   * 🔥 企业级新故事通知功能：处理新故事发布时已点亮人物的通知
   * 当已被点亮的人物出现在同一作者的新故事中时，自动通知相关用户
   */
  async handleNewStoryPublished(
    storyId: string,
    authorId: string,
  ): Promise<void> {
    // 1. 获取故事中的所有人物
    const storyCharacters = await this.characterRepository
      .createQueryBuilder("character")
      .innerJoin("character.stories", "story")
      .where("story.id = :storyId", { storyId })
      .andWhere("character.creatorId = :authorId", { authorId })
      .getMany();

    if (storyCharacters.length === 0) {
      return;
    }

    // 2. 找出已被点亮的人物及其点亮者
    const lightedCharacterIds = storyCharacters
      .filter((c) => c.isLighted && c.lighterUserId)
      .map((c) => ({
        characterId: c.id,
        characterName: c.name,
        lighterId: c.lighterUserId!,
      }));

    if (lightedCharacterIds.length === 0) {
      return;
    }

    // 3. 获取故事信息
    const story = await this.storyRepository.findOne({
      where: { id: storyId },
      relations: ["user"],
    });

    if (!story) {
      return;
    }

    // 4. 为每个已点亮的人物发送通知
    for (const lightedChar of lightedCharacterIds) {
      // 检查该用户是否已经在新故事中点亮了该人物
      const existingLightingInNewStory =
        await this.characterLightingRepository.findOne({
          where: {
            storyId: storyId,
            characterId: lightedChar.characterId,
            lighterUserId: lightedChar.lighterId,
            status: "active",
          },
        });

      // 如果已经点亮，跳过通知
      if (existingLightingInNewStory) {
        continue;
      }

      // 5. 创建通知
      await this.notificationsService.createNotification({
        userId: lightedChar.lighterId,
        type: NotificationType.CHARACTER_LIGHTING,
        title: "您点亮过的人物出现在新故事中",
        content: `您点亮过的人物"${lightedChar.characterName}"，出现在作者${story.user?.nickname || ""}的新故事《${story.title}》中，是否确认点亮新故事中的人物？`,
        relatedId: storyId,
        // 可以在actionUrl中编码额外信息
        actionUrl: `/stories/${storyId}/characters/${lightedChar.characterId}/quick-light`,
      });
    }
  }

  /**
   * 🔥 企业级快速点亮确认：处理用户对新故事中已点亮人物的快速确认
   * 跳过作者审批，直接建立点亮关系
   */
  async handleQuickLightingConfirmation(
    userId: string,
    characterId: string,
    storyId: string,
  ): Promise<void> {
    return this.dataSource.transaction(async (manager) => {
      // 1. 验证用户已在其他故事点亮该人物
      const character = await manager.findOne(Character, {
        where: {
          id: characterId,
          lighterUserId: userId,
        },
      });

      if (!character) {
        throw new BadRequestException("您未点亮过该人物或人物不存在");
      }

      // 2. 验证故事存在且人物属于该故事
      const story = await manager
        .createQueryBuilder(Story, "story")
        .innerJoin("story.characters", "character")
        .where("story.id = :storyId", { storyId })
        .andWhere("character.id = :characterId", { characterId })
        .getOne();

      if (!story) {
        throw new BadRequestException("故事不存在或人物不属于该故事");
      }

      // 3. 检查新故事中是否已有点亮关系
      const existingLighting = await manager.findOne(CharacterLighting, {
        where: {
          lighterUserId: userId,
          characterId: characterId,
          storyId: storyId,
        },
      });

      if (existingLighting) {
        if (existingLighting.status === "active") {
          throw new ConflictException("您已在该故事中点亮此人物");
        }
        // 如果存在但已取消，重新激活
        await manager.update(CharacterLighting, existingLighting.id, {
          status: "active",
          confirmedAt: new Date(),
        });

        // 更新人物点亮计数
        await manager.increment(
          Character,
          { id: characterId },
          "lightingCount",
          1,
        );
      } else {
        // 4. 创建新的点亮关系（跳过申请流程）
        const lighting = manager.create(CharacterLighting, {
          characterId,
          storyId,
          lighterUserId: userId,
          creatorUserId: character.creatorId,
          status: "active",
          confirmedAt: new Date(),
        });

        await manager.save(lighting);

        // 更新人物点亮计数
        await manager.increment(
          Character,
          { id: characterId },
          "lightingCount",
          1,
        );
      }

      // 5. 发送成功通知
      await this.notificationsService.createNotification({
        userId: userId,
        type: NotificationType.CHARACTER_LIGHTING,
        title: "点亮确认成功",
        content: `您已成功在新故事《${story.title}》中延续点亮关系`,
        relatedId: storyId,
      });

      // 6. 清除相关缓存
      await this.clearLightingCache(characterId, userId, character.creatorId);
    });
  }

  /**
   * 检查用户点亮限制（带缓存）
   */
  async checkUserLightingLimit(
    requesterId: string,
    storyAuthorId: string,
  ): Promise<void> {
    // 缓存键
    const cacheKey = `user_lighting_limit:${requesterId}:${storyAuthorId}`;

    // 先检查缓存
    let hasExisting =
      await this.enhancedRedisService.getCache<boolean>(cacheKey);

    if (hasExisting === null) {
      // 缓存未命中，查询数据库
      const existingLighting = await this.characterLightingRepository.findOne({
        where: {
          lighterUserId: requesterId,
          creatorUserId: storyAuthorId,
        },
      });

      hasExisting = !!existingLighting;

      // 缓存结果（10分钟）
      await this.enhancedRedisService.setCache(
        cacheKey,
        hasExisting,
        10 * 60 * 1000,
      );
    }

    if (hasExisting) {
      throw new BadRequestException(
        "您已点亮过该用户的其他人物，每个用户只能点亮一个人物",
      );
    }
  }

  async clearFailedHistory(_reason: string): Promise<void> {
    // 实现清除失败历史的逻辑
    // reason 参数当前未使用，添加下划线前缀
  }

  async getActiveLightingByTime(
    _userId: string,
    _since: Date,
  ): Promise<unknown[]> {
    // userId 和 since 参数当前未使用，添加下划线前缀
    return [];
  }

  /**
   * 手机号脱敏处理
   * 将手机号转换为 138****8000 格式
   */
  private maskPhoneNumber(phone: string): string {
    if (!phone || phone.length < 7) {
      return phone;
    }

    // 处理带国家码的手机号（如 +8613800138000）
    const cleanPhone = phone.replace(/^\+86/, "");

    if (cleanPhone.length >= 7) {
      const prefix = cleanPhone.substring(0, 3);
      const suffix = cleanPhone.substring(cleanPhone.length - 4);
      return `${prefix}****${suffix}`;
    }

    return phone;
  }
}
