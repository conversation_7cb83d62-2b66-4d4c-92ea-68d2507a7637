import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../repositories/auth_repository.dart';

/// 发送短信验证码用例
class SendSmsCode {
  final AuthRepository repository;

  SendSmsCode(this.repository);

  Future<Either<Failure, void>> call(String phone) async {
    // 验证手机号格式
    if (!_isValidPhone(phone)) {
      return const Left(ValidationFailure(
        message: '请输入正确的手机号',
        code: 'invalid_phone',
      ));
    }

    return repository.sendSmsCode(phone);
  }

  bool _isValidPhone(String phone) {
    final phoneRegex = RegExp(r'^1[3-9]\d{9}$');
    return phoneRegex.hasMatch(phone);
  }
}