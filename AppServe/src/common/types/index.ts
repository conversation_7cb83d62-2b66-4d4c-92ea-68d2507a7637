/**
 * 共享类型定义索引文件
 * 统一导出所有类型定义
 */

// 请求相关类型
export * from "./request.types";

// 错误处理类型
export * from "./error.types";

// 服务层类型
export * from "./service.types";

// 业务特定类型
// export * from '../../../database/interfaces/seed-data.interface';

/**
 * 通用工具类型
 */

/**
 * 使所有属性可选
 */
export type PartialBy<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

/**
 * 使指定属性必需
 */
export type RequiredBy<T, K extends keyof T> = Omit<T, K> &
  Required<Pick<T, K>>;

/**
 * 深度部分类型
 */
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends Record<string, unknown>
    ? DeepPartial<T[P]>
    : T[P];
};

/**
 * 排除null和undefined
 */
export type NonNullable<T> = T extends null | undefined ? never : T;

/**
 * ID类型 - 统一使用UUID
 */
export type EntityId = string;

/**
 * 时间戳类型
 */
export type Timestamp = Date | string;

/**
 * JSON值类型
 */
export type JsonValue =
  | string
  | number
  | boolean
  | null
  | JsonObject
  | JsonArray;
export interface JsonObject {
  [key: string]: JsonValue;
}
export interface JsonArray extends Array<JsonValue> {}

/**
 * 环境类型
 */
export type Environment = "development" | "testing" | "staging" | "production";

/**
 * 日志级别类型
 */
export type LogLevel = "error" | "warn" | "info" | "debug" | "verbose";

/**
 * OSS客户端类型
 */
export interface OSSClient {
  put(
    objectName: string,
    file: Buffer,
    options?: OSSPutOptions,
  ): Promise<OSSPutResult>;
  get(objectName: string, options?: OSSGetOptions): Promise<OSSGetResult>;
  signatureUrl(objectName: string, options?: OSSSignatureOptions): string;
  delete(objectName: string): Promise<void>;
  deleteMulti(objectNames: string[]): Promise<{ deleted: string[] }>;
  head(objectName: string): Promise<{
    status: number;
    headers: Record<string, string>;
    res: { headers: Record<string, string> };
  }>;
  [key: string]: unknown;
}

export interface OSSPutOptions {
  headers?: Record<string, string>;
  meta?: Record<string, string>;
}

export interface OSSPutResult {
  name: string;
  url: string;
  res: {
    status: number;
    headers: Record<string, string>;
  };
}

export interface OSSGetOptions {
  headers?: Record<string, string>;
}

export interface OSSGetResult {
  content: Buffer;
  res: {
    status: number;
    headers: Record<string, string>;
  };
}

export interface OSSSignatureOptions {
  expires?: number;
  method?: string;
}
