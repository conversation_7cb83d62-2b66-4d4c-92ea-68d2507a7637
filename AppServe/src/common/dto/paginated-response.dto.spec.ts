/**
 * 分页响应DTO测试 - 企业级DTO验证
 * 测试分页数据结构和计算逻辑
 */

import { PaginatedResponse } from "./paginated-response.dto";

describe("PaginatedResponse - 企业级DTO测试", () => {
  interface TestItem {
    id: number;
    name: string;
  }

  const mockItems: TestItem[] = [
    { id: 1, name: "Item 1" },
    { id: 2, name: "Item 2" },
    { id: 3, name: "Item 3" },
  ];

  describe("构造函数", () => {
    it("should create paginated response with correct properties", () => {
      const total = 10;
      const page = 2;
      const limit = 3;

      const response = new PaginatedResponse(mockItems, total, page, limit);

      expect(response.items).toEqual(mockItems);
      expect(response.total).toBe(total);
      expect(response.page).toBe(page);
      expect(response.limit).toBe(limit);
    });

    it("should calculate totalPages correctly", () => {
      // 测试整除情况
      let response = new PaginatedResponse(mockItems, 12, 1, 3);
      expect(response.totalPages).toBe(4);

      // 测试不整除情况
      response = new PaginatedResponse(mockItems, 10, 1, 3);
      expect(response.totalPages).toBe(4);

      // 测试单页情况
      response = new PaginatedResponse(mockItems, 2, 1, 3);
      expect(response.totalPages).toBe(1);

      // 测试空结果情况
      response = new PaginatedResponse([], 0, 1, 10);
      expect(response.totalPages).toBe(0);
    });

    it("should calculate hasNext correctly", () => {
      // 有下一页
      let response = new PaginatedResponse(mockItems, 10, 1, 3);
      expect(response.hasNext).toBe(true);

      // 最后一页
      response = new PaginatedResponse(mockItems, 10, 4, 3);
      expect(response.hasNext).toBe(false);

      // 单页情况
      response = new PaginatedResponse(mockItems, 3, 1, 10);
      expect(response.hasNext).toBe(false);
    });

    it("should calculate hasPrev correctly", () => {
      // 第一页
      let response = new PaginatedResponse(mockItems, 10, 1, 3);
      expect(response.hasPrev).toBe(false);

      // 第二页
      response = new PaginatedResponse(mockItems, 10, 2, 3);
      expect(response.hasPrev).toBe(true);

      // 最后一页
      response = new PaginatedResponse(mockItems, 10, 4, 3);
      expect(response.hasPrev).toBe(true);
    });
  });

  describe("边界条件测试", () => {
    it("should handle empty items array", () => {
      const response = new PaginatedResponse([], 0, 1, 10);

      expect(response.items).toEqual([]);
      expect(response.total).toBe(0);
      expect(response.totalPages).toBe(0);
      expect(response.hasNext).toBe(false);
      expect(response.hasPrev).toBe(false);
    });

    it("should handle single item", () => {
      const singleItem = [{ id: 1, name: "Single Item" }];
      const response = new PaginatedResponse(singleItem, 1, 1, 10);

      expect(response.items).toEqual(singleItem);
      expect(response.totalPages).toBe(1);
      expect(response.hasNext).toBe(false);
      expect(response.hasPrev).toBe(false);
    });

    it("should handle large datasets", () => {
      const response = new PaginatedResponse(mockItems, 1000, 50, 10);

      expect(response.totalPages).toBe(100);
      expect(response.hasNext).toBe(true);
      expect(response.hasPrev).toBe(true);
    });

    it("should handle limit of 1", () => {
      const response = new PaginatedResponse(mockItems, 10, 5, 1);

      expect(response.totalPages).toBe(10);
      expect(response.hasNext).toBe(true);
      expect(response.hasPrev).toBe(true);
    });
  });

  describe("分页逻辑验证", () => {
    it("should correctly identify first page", () => {
      const response = new PaginatedResponse(mockItems, 100, 1, 10);

      expect(response.page).toBe(1);
      expect(response.hasPrev).toBe(false);
      expect(response.hasNext).toBe(true);
    });

    it("should correctly identify middle page", () => {
      const response = new PaginatedResponse(mockItems, 100, 5, 10);

      expect(response.page).toBe(5);
      expect(response.hasPrev).toBe(true);
      expect(response.hasNext).toBe(true);
    });

    it("should correctly identify last page", () => {
      const response = new PaginatedResponse(mockItems, 100, 10, 10);

      expect(response.page).toBe(10);
      expect(response.hasPrev).toBe(true);
      expect(response.hasNext).toBe(false);
    });

    it("should handle page beyond total pages", () => {
      const response = new PaginatedResponse([], 10, 15, 10);

      expect(response.totalPages).toBe(1);
      expect(response.hasNext).toBe(false);
      expect(response.hasPrev).toBe(true);
    });
  });

  describe("类型安全验证", () => {
    it("should maintain type safety for generic items", () => {
      interface User {
        id: number;
        username: string;
        email: string;
      }

      const users: User[] = [
        { id: 1, username: "user1", email: "<EMAIL>" },
        { id: 2, username: "user2", email: "<EMAIL>" },
      ];

      const response = new PaginatedResponse<User>(users, 2, 1, 10);

      expect(response.items).toEqual(users);
      expect(response.items[0].username).toBe("user1");
      expect(response.items[0].email).toBe("<EMAIL>");
    });

    it("should work with different item types", () => {
      const numbers = [1, 2, 3, 4, 5];
      const response = new PaginatedResponse<number>(numbers, 100, 1, 5);

      expect(response.items).toEqual(numbers);
      expect(typeof response.items[0]).toBe("number");
    });
  });

  describe("数学计算准确性", () => {
    it("should handle decimal divisions correctly", () => {
      // 总数不能被limit整除的情况
      const testCases = [
        { total: 7, limit: 3, expectedPages: 3 }, // 7/3 = 2.33 -> 3页
        { total: 11, limit: 4, expectedPages: 3 }, // 11/4 = 2.75 -> 3页
        { total: 1, limit: 10, expectedPages: 1 }, // 1/10 = 0.1 -> 1页
        { total: 15, limit: 5, expectedPages: 3 }, // 15/5 = 3 -> 3页(整除)
      ];

      testCases.forEach(({ total, limit, expectedPages }) => {
        const response = new PaginatedResponse([], total, 1, limit);
        expect(response.totalPages).toBe(expectedPages);
      });
    });

    it("should handle zero division safely", () => {
      const response = new PaginatedResponse([], 0, 1, 10);
      expect(response.totalPages).toBe(0);
      expect(isNaN(response.totalPages)).toBe(false);
    });
  });

  describe("实际使用场景", () => {
    it("should work in typical API pagination scenario", () => {
      // 模拟API返回的分页数据
      const apiItems = Array.from({ length: 20 }, (_, i) => ({
        id: i + 1,
        title: `Article ${i + 1}`,
        content: `Content for article ${i + 1}`,
      }));

      const currentPageItems = apiItems.slice(10, 20); // 第2页，每页10条
      const response = new PaginatedResponse(
        currentPageItems,
        100, // 总记录数
        2, // 当前页
        10, // 每页条数
      );

      expect(response.items).toHaveLength(10);
      expect(response.total).toBe(100);
      expect(response.page).toBe(2);
      expect(response.limit).toBe(10);
      expect(response.totalPages).toBe(10);
      expect(response.hasNext).toBe(true);
      expect(response.hasPrev).toBe(true);
    });
  });
});
