/**
 * 环境变量解析工具
 * 提供类型安全的环境变量访问
 */

/**
 * 安全解析整数环境变量
 */
export function parseIntEnv(
  envVar: string | undefined,
  defaultValue: number,
): number {
  if (!envVar) return defaultValue;
  const parsed = parseInt(envVar, 10);
  return isNaN(parsed) ? defaultValue : parsed;
}

/**
 * 安全解析浮点数环境变量
 */
export function parseFloatEnv(
  envVar: string | undefined,
  defaultValue: number,
): number {
  if (!envVar) return defaultValue;
  const parsed = parseFloat(envVar);
  return isNaN(parsed) ? defaultValue : parsed;
}

/**
 * 安全解析布尔环境变量
 */
export function parseBooleanEnv(
  envVar: string | undefined,
  defaultValue: boolean,
): boolean {
  if (!envVar) return defaultValue;
  return envVar.toLowerCase() === "true" || envVar === "1";
}

/**
 * 安全获取字符串环境变量
 */
export function getStringEnv(
  envVar: string | undefined,
  defaultValue: string,
): string {
  return envVar || defaultValue;
}

/**
 * 获取必需的环境变量，不存在时抛出错误
 */
export function getRequiredEnv(key: string): string {
  const value = process.env[key];
  if (!value) {
    throw new Error(`Required environment variable ${key} is not set`);
  }
  return value;
}

/**
 * 获取必需的整数环境变量，不存在时抛出错误
 */
export function getRequiredIntEnv(key: string): number {
  const value = getRequiredEnv(key);
  const parsed = parseInt(value, 10);
  if (isNaN(parsed)) {
    throw new Error(`Environment variable ${key} must be a valid integer`);
  }
  return parsed;
}
