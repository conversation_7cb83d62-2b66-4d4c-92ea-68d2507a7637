import { Injectable, Logger } from "@nestjs/common";
import type {
  HealthCheckResult,
  HealthIndicatorResult,
} from "@nestjs/terminus";
import {
  HealthCheckService,
  HealthCheck,
  TypeOrmHealthIndicator,
  MemoryHealthIndicator,
  DiskHealthIndicator,
} from "@nestjs/terminus";
import { EnhancedRedisService } from "../../common/services/enhanced-redis.service";
import type Redis from "ioredis";
import type { DataSource } from "typeorm";

// 健康状态类型定义
type HealthStatus = "up" | "down" | "shutting_down";

// 健康检查结果类型（暂时未使用，保留用于后续扩展）
// interface HealthCheckIndicatorResult {
//   status: HealthStatus;
//   [key: string]: unknown;
// }

export interface CustomHealthIndicator {
  isHealthy(key: string): Promise<HealthIndicatorResult>;
}

@Injectable()
export class RedisHealthIndicator implements CustomHealthIndicator {
  private readonly logger = new Logger(RedisHealthIndicator.name);
  private redis: Redis;

  constructor(private enhancedRedisService: EnhancedRedisService) {
    try {
      this.redis = this.enhancedRedisService.getClient();
    } catch (error) {
      this.logger.warn("Redis connection not available for health checks");
    }
  }

  async isHealthy(key: string): Promise<HealthIndicatorResult> {
    const startTime = Date.now();

    try {
      if (!this.redis) {
        return {
          [key]: {
            status: "down",
            responseTime: 0,
            error: "Redis client not initialized",
            details: {
              host: process.env.REDIS_HOST || "localhost",
              port: process.env.REDIS_PORT || "6379",
            },
          },
        };
      }

      // 使用SET/GET测试代替PING，避免认证问题
      const testKey = `health_check_${Date.now()}`;
      await this.redis.set(testKey, "test", "EX", 10);
      const result = await this.redis.get(testKey);
      await this.redis.del(testKey);

      if (result !== "test") {
        throw new Error("Redis read/write test failed");
      }

      const responseTime = Date.now() - startTime;

      // 尝试获取Redis信息，如果失败就返回基础信息
      let version = "unknown";
      let memoryInfo = {
        used: "unknown",
        peak: "unknown",
        fragmentation: "unknown",
      };

      try {
        const info = await this.redis.info("server");
        const memory = await this.redis.info("memory");
        version = this.parseRedisVersion(info);
        memoryInfo = this.parseRedisMemoryInfo(memory);
      } catch (infoError) {
        this.logger.warn(
          "无法获取Redis详细信息，使用基础健康检查",
          infoError instanceof Error ? infoError.message : String(infoError),
        );
      }

      return {
        [key]: {
          status: "up",
          responseTime,
          version,
          memory: memoryInfo,
          details: {
            host: process.env.REDIS_HOST || "localhost",
            port: process.env.REDIS_PORT || "6379",
            mode: "read_write_test",
          },
        },
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.logger.warn(
        `Redis健康检查失败: ${error instanceof Error ? error.message : String(error)}`,
      );

      // 对于健康检查，我们返回warning状态而不是抛出错误
      return {
        [key]: {
          status: "down",
          responseTime,
          error: error instanceof Error ? error.message : String(error),
          details: {
            host: process.env.REDIS_HOST || "localhost",
            port: process.env.REDIS_PORT || "6379",
            lastChecked: new Date().toISOString(),
          },
        },
      };
    }
  }

  private parseRedisVersion(info: string): string {
    const lines = info.split("\r\n");
    for (const line of lines) {
      if (line.startsWith("redis_version:")) {
        return line.split(":")[1];
      }
    }
    return "unknown";
  }

  private parseRedisMemoryInfo(info: string): {
    used: string;
    peak: string;
    fragmentation: string;
  } {
    const lines = info.split("\r\n");
    let used = "unknown";
    let peak = "unknown";
    let fragmentation = "unknown";

    for (const line of lines) {
      if (line.startsWith("used_memory_human:")) {
        used = line.split(":")[1];
      } else if (line.startsWith("used_memory_peak_human:")) {
        peak = line.split(":")[1];
      } else if (line.startsWith("mem_fragmentation_ratio:")) {
        fragmentation = line.split(":")[1];
      }
    }

    return { used, peak, fragmentation };
  }
}

@Injectable()
export class HealthService {
  private readonly logger = new Logger(HealthService.name);

  constructor(
    private health: HealthCheckService,
    private db: TypeOrmHealthIndicator,
    private memory: MemoryHealthIndicator,
    private disk: DiskHealthIndicator,
    private redisIndicator: RedisHealthIndicator,
  ) {}

  @HealthCheck()
  async getHealth(): Promise<HealthCheckResult> {
    return this.health.check([
      // 数据库健康检查 - 关键服务
      () => this.db.pingCheck("database", { timeout: 1000 }),

      // Redis健康检查 - 关键服务
      () => this.redisIndicator.isHealthy("redis"),

      // 内存健康检查 - 警告阈值1GB，错误阈值2GB
      () => this.memory.checkHeap("memory_heap", 1024 * 1024 * 1024),
      () => this.memory.checkRSS("memory_rss", 2 * 1024 * 1024 * 1024),

      // 磁盘健康检查 - 90%使用率警告
      () =>
        this.disk.checkStorage("disk_health", {
          path: "/",
          thresholdPercent: 0.9,
        }),
    ]);
  }

  /**
   * 详细健康检查 - 包含性能指标
   */
  @HealthCheck()
  async getDetailedHealth(): Promise<HealthCheckResult> {
    const performanceData = await this.getPerformanceMetrics();

    return this.health.check([
      // 基础健康检查
      () => this.db.pingCheck("database", { timeout: 1000 }),
      () => this.redisIndicator.isHealthy("redis"),
      () => this.memory.checkHeap("memory_heap", 1024 * 1024 * 1024),
      () => this.memory.checkRSS("memory_rss", 2 * 1024 * 1024 * 1024),
      () =>
        this.disk.checkStorage("disk_health", {
          path: "/",
          thresholdPercent: 0.9,
        }),

      // 性能指标检查
      () => this.checkPerformanceMetrics(performanceData),

      // 应用程序特定检查
      () => this.checkApplicationHealth(),
    ]);
  }

  /**
   * 获取性能指标
   */
  private async getPerformanceMetrics(): Promise<{
    averageResponseTime: number;
    requestsPerSecond: number;
    errorRate: number;
  }> {
    try {
      const redis = this.redisIndicator["enhancedRedisService"].getClient();
      if (!redis) {
        return {
          averageResponseTime: -1,
          requestsPerSecond: 0,
          errorRate: 0,
        };
      }

      const today = new Date().toDateString();

      const pipe = redis.pipeline();
      pipe.get(`performance:requests:${today}`);
      pipe.get(`performance:errors:${today}`);
      pipe.lrange(`performance:response_time_samples:${today}`, 0, 99);

      const results = await pipe.exec();

      const totalRequests = parseInt((results?.[0]?.[1] as string) || "0");
      const totalErrors = parseInt((results?.[1]?.[1] as string) || "0");
      const responseTimes = (results?.[2]?.[1] as string[])
        .map((t) => parseInt(t))
        .filter((t) => !isNaN(t));

      const errorRate =
        totalRequests > 0 ? (totalErrors / totalRequests) * 100 : 0;
      const averageResponseTime =
        responseTimes.length > 0
          ? responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length
          : 0;

      const requestsPerSecond = totalRequests / (24 * 60 * 60);

      return {
        averageResponseTime,
        requestsPerSecond,
        errorRate,
      };
    } catch (error) {
      this.logger.error(
        "获取性能指标失败",
        error instanceof Error ? error.stack : String(error),
      );
      return {
        averageResponseTime: -1,
        requestsPerSecond: 0,
        errorRate: 100,
      };
    }
  }

  /**
   * 检查性能指标健康状态
   */
  private async checkPerformanceMetrics(metrics: {
    averageResponseTime: number;
    errorRate: number;
  }): Promise<HealthIndicatorResult> {
    const isHealthy =
      metrics.averageResponseTime < 1000 && // 平均响应时间小于1秒
      metrics.errorRate < 5; // 错误率小于5%

    if (!isHealthy) {
      throw new Error(
        `Performance degraded: avg response time ${metrics.averageResponseTime}ms, error rate ${metrics.errorRate}%`,
      );
    }

    return {
      performance: {
        status: "up",
        ...metrics,
      },
    };
  }

  /**
   * 应用程序特定健康检查
   */
  private async checkApplicationHealth(): Promise<HealthIndicatorResult> {
    try {
      // 检查关键配置
      const requiredEnvVars = ["DB_HOST", "REDIS_HOST", "JWT_SECRET"];
      const missingVars = requiredEnvVars.filter(
        (varName) => !process.env[varName],
      );

      if (missingVars.length > 0) {
        throw new Error(
          `Missing required environment variables: ${missingVars.join(", ")}`,
        );
      }

      // 检查应用程序运行时间
      const uptime = process.uptime();

      return {
        application: {
          status: "up",
          uptime: Math.floor(uptime),
          environment: process.env.NODE_ENV || "development",
          version: process.env.npm_package_version || "1.0.0",
          nodeVersion: process.version,
          platform: process.platform,
          arch: process.arch,
        },
      };
    } catch (error) {
      this.logger.error(
        "应用程序健康检查失败",
        error instanceof Error ? error.stack : String(error),
      );
      throw error;
    }
  }

  /**
   * 记录性能指标
   */
  async recordPerformanceMetric(
    responseTime: number,
    isError: boolean = false,
  ): Promise<void> {
    try {
      const redis = this.redisIndicator["enhancedRedisService"].getClient();
      if (!redis) {
        return;
      }

      const today = new Date().toDateString();

      const pipe = redis.pipeline();
      pipe.incr(`performance:requests:${today}`);
      pipe.expire(`performance:requests:${today}`, 7 * 24 * 60 * 60);

      if (isError) {
        pipe.incr(`performance:errors:${today}`);
        pipe.expire(`performance:errors:${today}`, 7 * 24 * 60 * 60);
      }

      pipe.lpush(`performance:response_time_samples:${today}`, responseTime);
      pipe.ltrim(`performance:response_time_samples:${today}`, 0, 999);
      pipe.expire(`performance:response_time_samples:${today}`, 24 * 60 * 60);

      await pipe.exec();
    } catch (error) {
      this.logger.error(
        "记录性能指标失败",
        error instanceof Error ? error.stack : String(error),
      );
    }
  }

  async checkDatabaseHealth(): Promise<HealthIndicatorResult> {
    const start = Date.now();
    let status: HealthStatus = "up";
    let details: Record<string, unknown> = {};

    try {
      const result = await this.db.pingCheck("database", { timeout: 1000 });
      details = { pingResult: result };
    } catch (error) {
      status = "down";
      details = this.formatError(error);
    }

    return {
      database: {
        status,
        details,
        responseTime: Date.now() - start,
      },
    };
  }

  async checkRedisHealth(): Promise<HealthIndicatorResult> {
    const start = Date.now();
    let status: HealthStatus = "up";
    let details: Record<string, unknown> = {};

    try {
      const result = await this.redisIndicator.isHealthy("redis");
      details = result;
    } catch (error) {
      status = "down";
      details = this.formatError(error);
    }

    return {
      redis: {
        status,
        details,
        responseTime: Date.now() - start,
      },
    };
  }

  private formatConnectionInfo(
    connection: DataSource,
  ): Record<string, unknown> {
    const info: Record<string, unknown> = {};
    if (connection.options.type === "postgres") {
      info.host = connection.options.host;
      info.port = connection.options.port;
      info.database = connection.options.database;
      info.user = connection.options.username;
      // PostgreSQL连接选项没有version字段
    } else if (connection.options.type === "mysql") {
      info.host = connection.options.host;
      info.port = connection.options.port;
      info.database = connection.options.database;
      info.user = connection.options.username;
      // MySQL连接选项没有version字段
    }
    return info;
  }

  private formatError(error: unknown): Record<string, unknown> {
    if (error instanceof Error) {
      return {
        error: error.message,
        stack: error.stack,
      };
    }
    return {
      error: String(error),
    };
  }

  private validateDetailedHealthData(data: unknown): Record<string, unknown> {
    if (typeof data !== "object" || data === null) {
      return {
        status: "down",
        error: "Invalid data format",
      };
    }
    return data as Record<string, unknown>;
  }
}
