#!/usr/bin/env node

/**
 * API测试执行脚本
 * 集成化的接口测试和数据生成工作流
 */

const { exec, spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🧪 YGS API 综合测试套件');
console.log('=' .repeat(60));

// 配置选项
const config = {
  generateData: true,      // 是否生成测试数据
  runE2ETests: true,       // 是否运行端到端测试
  generateCollections: true, // 是否生成接口集合
  startServer: false,      // 是否启动测试服务器
  cleanup: false,          // 测试完成后是否清理数据
};

// 解析命令行参数
const args = process.argv.slice(2);
args.forEach(arg => {
  switch (arg) {
    case '--no-data':
      config.generateData = false;
      break;
    case '--no-tests':
      config.runE2ETests = false;
      break;
    case '--no-collections':
      config.generateCollections = false;
      break;
    case '--start-server':
      config.startServer = true;
      break;
    case '--cleanup':
      config.cleanup = true;
      break;
    case '--help':
      showHelp();
      process.exit(0);
  }
});

/**
 * 显示帮助信息
 */
function showHelp() {
  console.log('🔧 使用方法:');
  console.log('  npm run test:full              # 完整测试流程');
  console.log('  npm run test:full -- --no-data # 跳过数据生成');
  console.log('  npm run test:full -- --cleanup # 测试后清理数据');
  console.log('');
  console.log('📋 可用参数:');
  console.log('  --no-data        跳过测试数据生成');
  console.log('  --no-tests       跳过端到端测试');
  console.log('  --no-collections 跳过接口集合生成');
  console.log('  --start-server   启动测试服务器');
  console.log('  --cleanup        测试完成后清理数据');
  console.log('  --help           显示此帮助信息');
}

/**
 * 执行命令并返回Promise
 */
function executeCommand(command, description, options = {}) {
  return new Promise((resolve, reject) => {
    console.log(`\n🚀 ${description}...`);
    console.log(`📝 执行: ${command}`);
    console.log('-' .repeat(50));
    
    const child = exec(command, {
      cwd: path.dirname(__dirname),
      ...options
    });
    
    child.stdout.on('data', (data) => {
      process.stdout.write(data);
    });
    
    child.stderr.on('data', (data) => {
      process.stderr.write(data);
    });
    
    child.on('close', (code) => {
      if (code === 0) {
        console.log(`✅ ${description} 完成`);
        resolve();
      } else {
        console.log(`❌ ${description} 失败 (退出码: ${code})`);
        reject(new Error(`${description} 失败`));
      }
    });
  });
}

/**
 * 检查服务器状态
 */
async function checkServerStatus() {
  return new Promise((resolve) => {
    const http = require('http');
    const req = http.get('http://localhost:3000/health', (res) => {
      resolve(res.statusCode === 200);
    });
    
    req.on('error', () => {
      resolve(false);
    });
    
    req.setTimeout(3000, () => {
      req.destroy();
      resolve(false);
    });
  });
}

/**
 * 等待服务器启动
 */
async function waitForServer(maxAttempts = 30) {
  console.log('⏳ 等待服务器启动...');
  
  for (let i = 0; i < maxAttempts; i++) {
    const isRunning = await checkServerStatus();
    if (isRunning) {
      console.log('✅ 服务器已启动');
      return true;
    }
    
    console.log(`   尝试 ${i + 1}/${maxAttempts}...`);
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  throw new Error('服务器启动超时');
}

/**
 * 生成测试报告
 */
function generateTestReport(results) {
  const reportPath = path.join(__dirname, '..', 'test-results', 'test-report.md');
  const reportDir = path.dirname(reportPath);
  
  if (!fs.existsSync(reportDir)) {
    fs.mkdirSync(reportDir, { recursive: true });
  }
  
  const timestamp = new Date().toISOString();
  const report = `# YGS API 测试报告

**生成时间**: ${timestamp}
**测试环境**: 开发环境

## 📊 测试结果汇总

${results.map(result => `
### ${result.name}
- **状态**: ${result.success ? '✅ 成功' : '❌ 失败'}
- **执行时间**: ${result.duration}ms
- **说明**: ${result.description}
${result.error ? `- **错误**: ${result.error}` : ''}
`).join('\n')}

## 🔧 测试配置

- 生成测试数据: ${config.generateData ? '是' : '否'}
- 运行端到端测试: ${config.runE2ETests ? '是' : '否'}
- 生成接口集合: ${config.generateCollections ? '是' : '否'}
- 启动测试服务器: ${config.startServer ? '是' : '否'}

## 📋 下一步操作

1. 查看详细的测试日志
2. 使用生成的Postman/Thunder集合进行手动测试
3. 根据测试结果修复发现的问题

---
*报告由 YGS API 测试套件自动生成*
`;

  fs.writeFileSync(reportPath, report);
  console.log(`📄 测试报告已生成: ${reportPath}`);
}

/**
 * 主执行函数
 */
async function main() {
  const results = [];
  const startTime = Date.now();
  
  try {
    console.log('🎯 测试配置:');
    console.log(`   📊 生成测试数据: ${config.generateData ? '是' : '否'}`);
    console.log(`   🧪 运行端到端测试: ${config.runE2ETests ? '是' : '否'}`);
    console.log(`   📁 生成接口集合: ${config.generateCollections ? '是' : '否'}`);
    console.log(`   🚀 启动测试服务器: ${config.startServer ? '是' : '否'}`);
    console.log(`   🧹 测试后清理: ${config.cleanup ? '是' : '否'}`);

    // 1. 检查或启动服务器
    if (config.startServer) {
      console.log('\n🚀 启动开发服务器...');
      spawn('npm', ['run', 'start:dev'], {
        cwd: path.dirname(__dirname),
        detached: true,
        stdio: 'ignore'
      });
      
      await waitForServer();
    } else {
      console.log('\n🔍 检查服务器状态...');
      const isRunning = await checkServerStatus();
      if (!isRunning) {
        console.log('⚠️ 服务器未运行，请先启动服务器:');
        console.log('   npm run start:dev');
        process.exit(1);
      }
      console.log('✅ 服务器正在运行');
    }

    // 2. 生成测试数据
    if (config.generateData) {
      const dataStart = Date.now();
      try {
        await executeCommand('npm run seed:all', '生成测试数据');
        results.push({
          name: '测试数据生成',
          success: true,
          duration: Date.now() - dataStart,
          description: '成功生成用户、故事、人物等测试数据'
        });
      } catch (error) {
        results.push({
          name: '测试数据生成',
          success: false,
          duration: Date.now() - dataStart,
          description: '生成测试数据失败',
          error: error.message
        });
      }
    }

    // 3. 运行端到端测试
    if (config.runE2ETests) {
      const testStart = Date.now();
      try {
        await executeCommand('npm run test:e2e', '运行端到端测试');
        results.push({
          name: 'API端到端测试',
          success: true,
          duration: Date.now() - testStart,
          description: '所有API接口测试通过'
        });
      } catch (error) {
        results.push({
          name: 'API端到端测试',
          success: false,
          duration: Date.now() - testStart,
          description: '部分API测试失败',
          error: error.message
        });
      }
    }

    // 4. 生成接口集合
    if (config.generateCollections) {
      const collectionStart = Date.now();
      try {
        await executeCommand('npm run api:collection', '生成接口集合');
        results.push({
          name: '接口集合生成',
          success: true,
          duration: Date.now() - collectionStart,
          description: '成功生成Postman和Thunder Client集合'
        });
      } catch (error) {
        results.push({
          name: '接口集合生成',
          success: false,
          duration: Date.now() - collectionStart,
          description: '接口集合生成失败',
          error: error.message
        });
      }
    }

    // 5. 清理测试数据
    if (config.cleanup) {
      const cleanupStart = Date.now();
      try {
        await executeCommand('npm run seed:clean', '清理测试数据');
        results.push({
          name: '测试数据清理',
          success: true,
          duration: Date.now() - cleanupStart,
          description: '成功清理测试数据'
        });
      } catch (error) {
        results.push({
          name: '测试数据清理',
          success: false,
          duration: Date.now() - cleanupStart,
          description: '清理测试数据失败',
          error: error.message
        });
      }
    }

    // 生成测试报告
    generateTestReport(results);

    const totalTime = Date.now() - startTime;
    const successCount = results.filter(r => r.success).length;
    const totalCount = results.length;

    console.log('\n🎉 测试流程完成！');
    console.log('=' .repeat(60));
    console.log(`📊 总体结果: ${successCount}/${totalCount} 成功`);
    console.log(`⏱️  总耗时: ${Math.round(totalTime / 1000)}秒`);
    
    if (successCount === totalCount) {
      console.log('🎉 所有测试都通过了！');
      console.log('');
      console.log('📋 下一步你可以:');
      console.log('1. 使用生成的接口集合进行手动测试');
      console.log('2. 启动前端应用进行联调');
      console.log('3. 查看 http://localhost:3000/api 的Swagger文档');
      console.log('');
      console.log('📁 相关文件:');
      console.log('• 接口集合: api-collections/');
      console.log('• 测试报告: test-results/test-report.md');
      console.log('• API文档: http://localhost:3000/api');
    } else {
      console.log('⚠️ 部分测试失败，请查看详细日志');
      process.exit(1);
    }

  } catch (error) {
    console.error('❌ 测试流程失败:', error.message);
    process.exit(1);
  }
}

// 运行主函数
main();