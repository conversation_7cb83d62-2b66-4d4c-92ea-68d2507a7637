import {
  IsString,
  IsOptional,
  IsEnum,
  IsNotEmpty,
  IsUrl,
  <PERSON><PERSON>eng<PERSON>,
} from "class-validator";

/**
 * 创建人物DTO
 * 用于创建新人物的数据传输对象
 */
export class CreateCharacterDto {
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  name: string;

  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @IsOptional()
  @IsUrl()
  avatarUrl?: string;

  @IsOptional()
  @IsEnum(["男", "女", "未知"])
  gender?: string;

  @IsOptional()
  @IsEnum(["家人", "朋友", "同事", "同学", "恋人", "配偶", "其他"])
  relationship?: string;

  @IsOptional()
  @IsString()
  @MaxLength(100)
  customRelationship?: string;
}
