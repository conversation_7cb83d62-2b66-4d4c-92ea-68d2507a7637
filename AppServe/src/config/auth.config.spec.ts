/**
 * 认证配置测试 - 企业级配置验证
 * 测试环境变量解析和默认值设置
 */

import { authConfig } from "./auth.config";

// Mock env.utils
jest.mock("../common/utils/env.utils", () => ({
  getStringEnv: jest.fn((value: string | undefined, defaultValue: string) => {
    // Mock在测试环境下总是返回默认值
    return defaultValue;
  }),
  parseIntEnv: jest.fn((value: string | undefined, defaultValue: number) => {
    // Mock在测试环境下总是返回默认值
    return defaultValue;
  }),
}));

describe("AuthConfig - 企业级配置测试", () => {
  beforeEach(() => {
    // 重置所有Mock
    jest.clearAllMocks();
  });

  describe("JWT配置", () => {
    it("should provide default JWT secret", () => {
      const config = authConfig();

      expect(config.jwtSecret).toBe(
        "story-app-secret-key-change-in-production",
      );
      expect(config.jwtExpiresIn).toBe("7d");
    });

    it("should provide JWT refresh token configuration", () => {
      const config = authConfig();

      expect(config.jwtRefreshSecret).toBe("story-app-refresh-secret-key");
      expect(config.jwtRefreshExpiresIn).toBe("30d");
    });
  });

  describe("密码加密配置", () => {
    it("should provide bcrypt rounds configuration", () => {
      const config = authConfig();

      expect(config.bcryptRounds).toBe(12);
      expect(typeof config.bcryptRounds).toBe("number");
    });
  });

  describe("短信服务配置", () => {
    it("should provide SMS provider configuration", () => {
      const config = authConfig();

      expect(config.smsProvider).toBe("aliyun");
      expect(config.smsSignName).toBe("有故事APP");
      expect(config.smsTemplateCode).toBe("SMS_123456789");
    });

    it("should provide SMS credentials with empty defaults", () => {
      const config = authConfig();

      expect(config.smsAccessKeyId).toBe("");
      expect(config.smsAccessKeySecret).toBe("");
    });
  });

  describe("验证码配置", () => {
    it("should provide verification code expiration", () => {
      const config = authConfig();

      expect(config.verificationCodeExpiration).toBe(300);
      expect(typeof config.verificationCodeExpiration).toBe("number");
    });
  });

  describe("配置完整性验证", () => {
    it("should contain all required configuration keys", () => {
      const config = authConfig();

      const requiredKeys = [
        "jwtSecret",
        "jwtExpiresIn",
        "jwtRefreshSecret",
        "jwtRefreshExpiresIn",
        "bcryptRounds",
        "smsProvider",
        "smsAccessKeyId",
        "smsAccessKeySecret",
        "smsSignName",
        "smsTemplateCode",
        "verificationCodeExpiration",
      ];

      requiredKeys.forEach((key) => {
        expect(config).toHaveProperty(key);
      });
    });

    it("should return consistent configuration object", () => {
      const config1 = authConfig();
      const config2 = authConfig();

      expect(config1).toEqual(config2);
    });
  });

  describe("环境变量处理", () => {
    it("should use environment variables when available", () => {
      const {
        getStringEnv,
        parseIntEnv,
        // eslint-disable-next-line @typescript-eslint/no-var-requires
      } = require("../common/utils/env.utils");

      // 验证环境变量处理函数被调用
      authConfig();

      expect(getStringEnv).toHaveBeenCalledWith(
        process.env.JWT_SECRET,
        "story-app-secret-key-change-in-production",
      );
      expect(getStringEnv).toHaveBeenCalledWith(
        process.env.JWT_EXPIRES_IN,
        "7d",
      );
      expect(parseIntEnv).toHaveBeenCalledWith(process.env.BCRYPT_ROUNDS, 12);
      expect(parseIntEnv).toHaveBeenCalledWith(
        process.env.VERIFICATION_CODE_EXPIRATION,
        300,
      );
    });
  });
});
