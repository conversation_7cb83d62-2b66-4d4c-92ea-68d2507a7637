import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { Logger } from "@nestjs/common";
import type { HealthCheckResult } from "@nestjs/terminus";
import {
  HealthCheckService,
  TypeOrmHealthIndicator,
  MemoryHealthIndicator,
  DiskHealthIndicator,
} from "@nestjs/terminus";
import { HealthService, RedisHealthIndicator } from "./health.service";
import { EnhancedRedisService } from "../../common/services/enhanced-redis.service";

describe("HealthService", () => {
  let service: HealthService;
  let mockHealthCheckService: jest.Mocked<HealthCheckService>;
  let mockDbIndicator: jest.Mocked<TypeOrmHealthIndicator>;
  let mockMemoryIndicator: jest.Mocked<MemoryHealthIndicator>;
  let mockDiskIndicator: jest.Mocked<DiskHealthIndicator>;
  let mockRedisIndicator: jest.Mocked<RedisHealthIndicator>;

  const mockHealthCheckResult: HealthCheckResult = {
    status: "ok",
    info: {
      database: { status: "up" },
      redis: { status: "up" },
      memory_heap: { status: "up" },
      memory_rss: { status: "up" },
      disk_health: { status: "up" },
    },
    error: {},
    details: {
      database: { status: "up" },
      redis: { status: "up" },
      memory_heap: { status: "up" },
      memory_rss: { status: "up" },
      disk_health: { status: "up" },
    },
  };

  beforeEach(async () => {
    const mockHealthCheckServiceMethods = {
      check: jest.fn(),
    };

    const mockDbIndicatorMethods = {
      pingCheck: jest.fn(),
    };

    const mockMemoryIndicatorMethods = {
      checkHeap: jest.fn(),
      checkRSS: jest.fn(),
    };

    const mockDiskIndicatorMethods = {
      checkStorage: jest.fn(),
    };

    const mockRedisIndicatorMethods = {
      isHealthy: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        HealthService,
        {
          provide: HealthCheckService,
          useValue: mockHealthCheckServiceMethods,
        },
        {
          provide: TypeOrmHealthIndicator,
          useValue: mockDbIndicatorMethods,
        },
        {
          provide: MemoryHealthIndicator,
          useValue: mockMemoryIndicatorMethods,
        },
        {
          provide: DiskHealthIndicator,
          useValue: mockDiskIndicatorMethods,
        },
        {
          provide: RedisHealthIndicator,
          useValue: mockRedisIndicatorMethods,
        },
      ],
    }).compile();

    service = module.get<HealthService>(HealthService);
    mockHealthCheckService = module.get(HealthCheckService);
    mockDbIndicator = module.get(TypeOrmHealthIndicator);
    mockMemoryIndicator = module.get(MemoryHealthIndicator);
    mockDiskIndicator = module.get(DiskHealthIndicator);
    mockRedisIndicator = module.get(RedisHealthIndicator);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("应该正确实例化", () => {
    expect(service).toBeDefined();
  });

  describe("getHealth", () => {
    it("应该返回健康检查结果", async () => {
      mockHealthCheckService.check.mockResolvedValue(mockHealthCheckResult);

      const result = await service.getHealth();

      expect(result).toEqual(mockHealthCheckResult);
      expect(mockHealthCheckService.check).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.any(Function),
          expect.any(Function),
          expect.any(Function),
          expect.any(Function),
          expect.any(Function),
        ]),
      );
    });

    it("应该调用数据库健康检查", async () => {
      mockHealthCheckService.check.mockImplementation(async (checks) => {
        for (const check of checks) {
          await check();
        }
        return mockHealthCheckResult;
      });

      mockDbIndicator.pingCheck.mockResolvedValue({
        database: { status: "up" as const },
      });

      await service.getHealth();

      expect(mockDbIndicator.pingCheck).toHaveBeenCalledWith("database", {
        timeout: 1000,
      });
    });

    it("应该调用Redis健康检查", async () => {
      mockHealthCheckService.check.mockImplementation(async (checks) => {
        for (const check of checks) {
          await check();
        }
        return mockHealthCheckResult;
      });

      mockRedisIndicator.isHealthy.mockResolvedValue({
        redis: { status: "up" as const },
      });

      await service.getHealth();

      expect(mockRedisIndicator.isHealthy).toHaveBeenCalledWith("redis");
    });

    it("应该调用内存健康检查", async () => {
      mockHealthCheckService.check.mockImplementation(async (checks) => {
        for (const check of checks) {
          await check();
        }
        return mockHealthCheckResult;
      });

      mockMemoryIndicator.checkHeap.mockResolvedValue({
        memory_heap: { status: "up" as const },
      });
      mockMemoryIndicator.checkRSS.mockResolvedValue({
        memory_rss: { status: "up" as const },
      });

      await service.getHealth();

      expect(mockMemoryIndicator.checkHeap).toHaveBeenCalledWith(
        "memory_heap",
        1024 * 1024 * 1024,
      );
      expect(mockMemoryIndicator.checkRSS).toHaveBeenCalledWith(
        "memory_rss",
        2 * 1024 * 1024 * 1024,
      );
    });

    it("应该调用磁盘健康检查", async () => {
      mockHealthCheckService.check.mockImplementation(async (checks) => {
        for (const check of checks) {
          await check();
        }
        return mockHealthCheckResult;
      });

      mockDiskIndicator.checkStorage.mockResolvedValue({
        disk_health: { status: "up" as const },
      });

      await service.getHealth();

      expect(mockDiskIndicator.checkStorage).toHaveBeenCalledWith(
        "disk_health",
        {
          path: "/",
          thresholdPercent: 0.9,
        },
      );
    });
  });

  describe("getDetailedHealth", () => {
    it("应该返回详细健康检查结果", async () => {
      mockHealthCheckService.check.mockResolvedValue(mockHealthCheckResult);

      // Mock Redis client for performance metrics
      const mockRedisClient = {
        pipeline: jest.fn().mockReturnValue({
          get: jest.fn().mockReturnThis(),
          lrange: jest.fn().mockReturnThis(),
          exec: jest.fn().mockResolvedValue([
            [null, "100"],
            [null, "5"],
            [null, ["200", "300", "400"]],
          ]),
        }),
      };

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (mockRedisIndicator as any).enhancedRedisService = {
        getClient: jest.fn().mockReturnValue(mockRedisClient),
      };

      const result = await service.getDetailedHealth();

      expect(result).toEqual(mockHealthCheckResult);
      expect(mockHealthCheckService.check).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.any(Function),
          expect.any(Function),
          expect.any(Function),
          expect.any(Function),
          expect.any(Function),
          expect.any(Function),
          expect.any(Function),
        ]),
      );
    });

    it("应该处理性能指标获取失败", async () => {
      mockHealthCheckService.check.mockResolvedValue(mockHealthCheckResult);

      // Mock Redis client failure
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (mockRedisIndicator as any).enhancedRedisService = {
        getClient: jest.fn().mockReturnValue(null),
      };

      const result = await service.getDetailedHealth();

      expect(result).toEqual(mockHealthCheckResult);
    });
  });

  describe("recordPerformanceMetric", () => {
    it("应该记录性能指标", async () => {
      const mockPipeline = {
        incr: jest.fn().mockReturnThis(),
        expire: jest.fn().mockReturnThis(),
        lpush: jest.fn().mockReturnThis(),
        ltrim: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue([]),
      };

      const mockRedisClient = {
        pipeline: jest.fn().mockReturnValue(mockPipeline),
      };

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (mockRedisIndicator as any).enhancedRedisService = {
        getClient: jest.fn().mockReturnValue(mockRedisClient),
      };

      await service.recordPerformanceMetric(200, false);

      expect(mockRedisClient.pipeline).toHaveBeenCalled();
      expect(mockPipeline.incr).toHaveBeenCalled();
      expect(mockPipeline.lpush).toHaveBeenCalled();
      expect(mockPipeline.exec).toHaveBeenCalled();
    });

    it("应该记录错误指标", async () => {
      const mockPipeline = {
        incr: jest.fn().mockReturnThis(),
        expire: jest.fn().mockReturnThis(),
        lpush: jest.fn().mockReturnThis(),
        ltrim: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue([]),
      };

      const mockRedisClient = {
        pipeline: jest.fn().mockReturnValue(mockPipeline),
      };

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (mockRedisIndicator as any).enhancedRedisService = {
        getClient: jest.fn().mockReturnValue(mockRedisClient),
      };

      await service.recordPerformanceMetric(500, true);

      expect(mockPipeline.incr).toHaveBeenCalledTimes(2); // requests and errors
    });

    it("应该处理Redis客户端不可用的情况", async () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (mockRedisIndicator as any).enhancedRedisService = {
        getClient: jest.fn().mockReturnValue(null),
      };

      await expect(
        service.recordPerformanceMetric(200, false),
      ).resolves.toBeUndefined();
    });
  });

  describe("checkDatabaseHealth", () => {
    it("应该返回数据库健康状态", async () => {
      const mockResult = { database: { status: "up" as const } };
      mockDbIndicator.pingCheck.mockResolvedValue(mockResult);

      const result = await service.checkDatabaseHealth();

      expect(result).toEqual({
        database: {
          status: "up",
          details: { pingResult: mockResult },
          responseTime: expect.any(Number),
        },
      });
    });

    it("应该处理数据库健康检查失败", async () => {
      const mockError = new Error("Database connection failed");
      mockDbIndicator.pingCheck.mockRejectedValue(mockError);

      const result = await service.checkDatabaseHealth();

      expect(result).toEqual({
        database: {
          status: "down",
          details: {
            error: "Database connection failed",
            stack: expect.any(String),
          },
          responseTime: expect.any(Number),
        },
      });
    });
  });

  describe("checkRedisHealth", () => {
    it("应该返回Redis健康状态", async () => {
      const mockResult = { redis: { status: "up" as const } };
      mockRedisIndicator.isHealthy.mockResolvedValue(mockResult);

      const result = await service.checkRedisHealth();

      expect(result).toEqual({
        redis: {
          status: "up",
          details: mockResult,
          responseTime: expect.any(Number),
        },
      });
    });

    it("应该处理Redis健康检查失败", async () => {
      const mockError = new Error("Redis connection failed");
      mockRedisIndicator.isHealthy.mockRejectedValue(mockError);

      const result = await service.checkRedisHealth();

      expect(result).toEqual({
        redis: {
          status: "down",
          details: {
            error: "Redis connection failed",
            stack: expect.any(String),
          },
          responseTime: expect.any(Number),
        },
      });
    });
  });

  describe("getPerformanceMetrics", () => {
    it("应该返回性能指标", async () => {
      const mockPipeline = {
        get: jest.fn().mockReturnThis(),
        lrange: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue([
          [null, "100"],
          [null, "5"],
          [null, ["200", "300", "400"]],
        ]),
      };

      const mockRedisClient = {
        pipeline: jest.fn().mockReturnValue(mockPipeline),
      };

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (mockRedisIndicator as any).enhancedRedisService = {
        getClient: jest.fn().mockReturnValue(mockRedisClient),
      };

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = await (service as any).getPerformanceMetrics();

      expect(result).toEqual({
        averageResponseTime: 300,
        requestsPerSecond: expect.any(Number),
        errorRate: 5,
      });
    });

    it("应该处理Redis客户端不可用", async () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (mockRedisIndicator as any).enhancedRedisService = {
        getClient: jest.fn().mockReturnValue(null),
      };

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = await (service as any).getPerformanceMetrics();

      expect(result).toEqual({
        averageResponseTime: -1,
        requestsPerSecond: 0,
        errorRate: 0,
      });
    });

    it("应该处理Redis错误", async () => {
      const mockPipeline = {
        get: jest.fn().mockReturnThis(),
        lrange: jest.fn().mockReturnThis(),
        exec: jest.fn().mockRejectedValue(new Error("Redis error")),
      };

      const mockRedisClient = {
        pipeline: jest.fn().mockReturnValue(mockPipeline),
      };

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (mockRedisIndicator as any).enhancedRedisService = {
        getClient: jest.fn().mockReturnValue(mockRedisClient),
      };

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = await (service as any).getPerformanceMetrics();

      expect(result).toEqual({
        averageResponseTime: -1,
        requestsPerSecond: 0,
        errorRate: 100,
      });
    });
  });

  describe("checkPerformanceMetrics", () => {
    it("应该返回健康状态当指标正常", async () => {
      const metrics = {
        averageResponseTime: 500,
        errorRate: 2,
      };

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = await (service as any).checkPerformanceMetrics(metrics);

      expect(result).toEqual({
        performance: {
          status: "up",
          averageResponseTime: 500,
          errorRate: 2,
        },
      });
    });

    it("应该抛出错误当响应时间过长", async () => {
      const metrics = {
        averageResponseTime: 1500,
        errorRate: 2,
      };

      await expect(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (service as any).checkPerformanceMetrics(metrics),
      ).rejects.toThrow(
        "Performance degraded: avg response time 1500ms, error rate 2%",
      );
    });

    it("应该抛出错误当错误率过高", async () => {
      const metrics = {
        averageResponseTime: 500,
        errorRate: 10,
      };

      await expect(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (service as any).checkPerformanceMetrics(metrics),
      ).rejects.toThrow(
        "Performance degraded: avg response time 500ms, error rate 10%",
      );
    });
  });

  describe("checkApplicationHealth", () => {
    it("应该返回应用程序健康状态", async () => {
      const originalEnv = process.env;
      process.env = {
        ...originalEnv,
        DB_HOST: "localhost",
        REDIS_HOST: "localhost",
        JWT_SECRET: "test-secret",
        NODE_ENV: "test",
        npm_package_version: "1.0.0",
      };

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = await (service as any).checkApplicationHealth();

      expect(result).toEqual({
        application: {
          status: "up",
          uptime: expect.any(Number),
          environment: "test",
          version: "1.0.0",
          nodeVersion: process.version,
          platform: process.platform,
          arch: process.arch,
        },
      });

      process.env = originalEnv;
    });

    it("应该抛出错误当缺少必需的环境变量", async () => {
      const originalEnv = process.env;
      process.env = {
        ...originalEnv,
        DB_HOST: undefined,
        REDIS_HOST: undefined,
        JWT_SECRET: undefined,
      };

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      await expect((service as any).checkApplicationHealth()).rejects.toThrow(
        "Missing required environment variables: DB_HOST, REDIS_HOST, JWT_SECRET",
      );

      process.env = originalEnv;
    });
  });
});

describe("RedisHealthIndicator", () => {
  let indicator: RedisHealthIndicator;
  let mockEnhancedRedisService: jest.Mocked<EnhancedRedisService>;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let mockRedisClient: any;

  beforeEach(async () => {
    mockRedisClient = {
      set: jest.fn(),
      get: jest.fn(),
      del: jest.fn(),
      info: jest.fn(),
    };

    mockEnhancedRedisService = {
      getClient: jest.fn().mockReturnValue(mockRedisClient),
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RedisHealthIndicator,
        {
          provide: EnhancedRedisService,
          useValue: mockEnhancedRedisService,
        },
      ],
    }).compile();

    indicator = module.get<RedisHealthIndicator>(RedisHealthIndicator);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("应该正确实例化", () => {
    expect(indicator).toBeDefined();
  });

  describe("isHealthy", () => {
    it("应该返回健康状态", async () => {
      mockRedisClient.set.mockResolvedValue("OK");
      mockRedisClient.get.mockResolvedValue("test");
      mockRedisClient.del.mockResolvedValue(1);
      mockRedisClient.info.mockResolvedValue("redis_version:6.2.0\r\n");

      const result = await indicator.isHealthy("redis");

      expect(result).toEqual({
        redis: {
          status: "up",
          responseTime: expect.any(Number),
          version: "6.2.0",
          memory: {
            used: "unknown",
            peak: "unknown",
            fragmentation: "unknown",
          },
          details: {
            host: "localhost",
            port: "6379",
            mode: "read_write_test",
          },
        },
      });
    });

    it("应该处理Redis客户端未初始化", async () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      mockEnhancedRedisService.getClient.mockReturnValue(null as any);

      const newIndicator = new RedisHealthIndicator(mockEnhancedRedisService);
      const result = await newIndicator.isHealthy("redis");

      expect(result).toEqual({
        redis: {
          status: "down",
          responseTime: 0,
          error: "Redis client not initialized",
          details: {
            host: "localhost",
            port: "6379",
          },
        },
      });
    });

    it("应该处理读写测试失败", async () => {
      mockRedisClient.set.mockResolvedValue("OK");
      mockRedisClient.get.mockResolvedValue("wrong_value");
      mockRedisClient.del.mockResolvedValue(1);

      const result = await indicator.isHealthy("redis");

      expect(result).toEqual({
        redis: {
          status: "down",
          responseTime: expect.any(Number),
          error: "Redis read/write test failed",
          details: {
            host: "localhost",
            port: "6379",
            lastChecked: expect.any(String),
          },
        },
      });
    });

    it("应该处理Redis连接错误", async () => {
      mockRedisClient.set.mockRejectedValue(new Error("Connection failed"));

      const result = await indicator.isHealthy("redis");

      expect(result).toEqual({
        redis: {
          status: "down",
          responseTime: expect.any(Number),
          error: "Connection failed",
          details: {
            host: "localhost",
            port: "6379",
            lastChecked: expect.any(String),
          },
        },
      });
    });

    it("应该处理info命令失败", async () => {
      mockRedisClient.set.mockResolvedValue("OK");
      mockRedisClient.get.mockResolvedValue("test");
      mockRedisClient.del.mockResolvedValue(1);
      mockRedisClient.info.mockRejectedValue(new Error("INFO command failed"));

      const result = await indicator.isHealthy("redis");

      expect(result).toEqual({
        redis: {
          status: "up",
          responseTime: expect.any(Number),
          version: "unknown",
          memory: {
            used: "unknown",
            peak: "unknown",
            fragmentation: "unknown",
          },
          details: {
            host: "localhost",
            port: "6379",
            mode: "read_write_test",
          },
        },
      });
    });

    it("应该正确解析Redis内存信息", async () => {
      mockRedisClient.set.mockResolvedValue("OK");
      mockRedisClient.get.mockResolvedValue("test");
      mockRedisClient.del.mockResolvedValue(1);
      mockRedisClient.info
        .mockResolvedValueOnce("redis_version:6.2.0\r\n")
        .mockResolvedValueOnce(
          "used_memory_human:1.5M\r\nused_memory_peak_human:2.0M\r\nmem_fragmentation_ratio:1.2\r\n",
        );

      const result = await indicator.isHealthy("redis");

      expect(result).toEqual({
        redis: {
          status: "up",
          responseTime: expect.any(Number),
          version: "6.2.0",
          memory: {
            used: "1.5M",
            peak: "2.0M",
            fragmentation: "1.2",
          },
          details: {
            host: "localhost",
            port: "6379",
            mode: "read_write_test",
          },
        },
      });
    });

    it("应该使用环境变量中的Redis主机和端口", async () => {
      const originalEnv = process.env;
      process.env = {
        ...originalEnv,
        REDIS_HOST: "redis.example.com",
        REDIS_PORT: "6380",
      };

      mockRedisClient.set.mockResolvedValue("OK");
      mockRedisClient.get.mockResolvedValue("test");
      mockRedisClient.del.mockResolvedValue(1);
      mockRedisClient.info.mockResolvedValue("redis_version:6.2.0\r\n");

      const result = await indicator.isHealthy("redis");

      expect(result.redis.details).toEqual({
        host: "redis.example.com",
        port: "6380",
        mode: "read_write_test",
      });

      process.env = originalEnv;
    });

    it("应该处理构造函数中的Redis客户端获取错误", () => {
      const mockServiceWithError = {
        getClient: jest.fn().mockImplementation(() => {
          throw new Error("Redis connection failed during init");
        }),
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
      } as any;

      // 创建一个新实例来测试构造函数错误处理
      const loggerSpy = jest
        .spyOn(Logger.prototype, "warn")
        .mockImplementation();
      const indicatorWithError = new RedisHealthIndicator(mockServiceWithError);

      expect(indicatorWithError).toBeDefined();
      expect(loggerSpy).toHaveBeenCalledWith(
        "Redis connection not available for health checks",
      );

      loggerSpy.mockRestore();
    });
  });
});
