import {
  Controller,
  Get,
  Post,
  Put,
  Body,
  Param,
  Query,
  UseGuards,
  Req,
  HttpStatus,
} from "@nestjs/common";
import type { Request } from "express";
import { ReportsService } from "./reports.service";
import { CreateReportDto, UpdateReportDto, ReportQueryDto } from "./dto";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { ApiResponse } from "../../common/dto/api-response.dto";
import type { PaginatedResponseDto } from "../../common/dto/paginated-response.dto";
import type { ReportResponseDto, ReportStatsResponseDto } from "./dto";

/**
 * 举报控制器
 * 提供内容举报相关的API接口
 */
@Controller("reports")
export class ReportsController {
  constructor(private readonly reportsService: ReportsService) {}

  /**
   * 创建举报
   * POST /reports
   */
  @Post()
  @UseGuards(JwtAuthGuard)
  async createReport(
    @Body() createReportDto: CreateReportDto,
    @Req() req: Request & { user: { id: string } },
  ): Promise<ApiResponse<ReportResponseDto>> {
    const report = await this.reportsService.createReport(
      createReportDto,
      req.user.id,
    );

    return ApiResponse.success(report, "举报提交成功", HttpStatus.CREATED);
  }

  /**
   * 获取举报列表（分页）
   * GET /reports
   */
  @Get()
  @UseGuards(JwtAuthGuard)
  async getReports(
    @Query() queryDto: ReportQueryDto,
  ): Promise<ApiResponse<PaginatedResponseDto<ReportResponseDto>>> {
    const result = await this.reportsService.getReports(queryDto);

    return ApiResponse.success(result, "获取举报列表成功", HttpStatus.OK);
  }

  /**
   * 获取举报统计数据
   * GET /reports/stats
   */
  @Get("stats")
  @UseGuards(JwtAuthGuard)
  async getReportStats(): Promise<ApiResponse<ReportStatsResponseDto>> {
    const stats = await this.reportsService.getReportStats();

    return ApiResponse.success(stats, "获取举报统计成功", HttpStatus.OK);
  }

  /**
   * 获取举报详情
   * GET /reports/:id
   */
  @Get(":id")
  @UseGuards(JwtAuthGuard)
  async getReportById(
    @Param("id") id: string,
  ): Promise<ApiResponse<ReportResponseDto>> {
    const report = await this.reportsService.getReportById(id);

    return ApiResponse.success(report, "获取举报详情成功", HttpStatus.OK);
  }

  /**
   * 更新举报状态（管理员操作）
   * PUT /reports/:id
   */
  @Put(":id")
  @UseGuards(JwtAuthGuard)
  async updateReport(
    @Param("id") id: string,
    @Body() updateReportDto: UpdateReportDto,
    @Req() req: Request & { user: { id: string } },
  ): Promise<ApiResponse<ReportResponseDto>> {
    const report = await this.reportsService.updateReport(
      id,
      updateReportDto,
      req.user.id,
    );

    return ApiResponse.success(report, "举报状态更新成功", HttpStatus.OK);
  }
}
