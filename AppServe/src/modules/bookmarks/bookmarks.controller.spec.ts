/* eslint-disable @typescript-eslint/no-explicit-any, @typescript-eslint/no-unused-vars */
import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { HttpStatus } from "@nestjs/common";
import type { Request } from "express";

import { BookmarksController } from "./bookmarks.controller";
import { BookmarksService } from "./bookmarks.service";
import { BookmarkCategory, BookmarkStatus } from "./entities/bookmark.entity";
import type {
  CreateBookmarkDto,
  UpdateBookmarkCategoryDto,
  BookmarkQueryDto,
  BookmarkResponseDto,
  BookmarkStatsResponseDto,
} from "./dto";
import { BookmarkSortBy } from "./dto";

describe("BookmarksController - 企业级单元测试", () => {
  let controller: BookmarksController;
  let mockBookmarksService: jest.Mocked<BookmarksService>;

  // 测试数据工厂
  const mockUser = { id: "user-001", nickname: "测试用户" };
  const mockRequest = { user: mockUser } as Request & { user: { id: string } };

  const mockBookmarkResponse: BookmarkResponseDto = {
    id: "bookmark-001",
    userId: "user-001",
    storyId: "story-001",
    category: BookmarkCategory.FAVORITE,
    customCategoryName: null,
    status: BookmarkStatus.ACTIVE,
    note: "很棒的故事",
    storySnapshot: {
      title: "测试故事",
      summary: "这是一个测试故事的内容",
      coverImage: "https://example.com/cover.jpg",
      authorName: "故事作者",
    },
    metadata: { source: "web", device: "desktop" },
    createdAt: new Date(),
    updatedAt: new Date(),
    story: {
      id: "story-001",
      title: "测试故事",
      summary: "这是一个测试故事的内容",
      coverImage: "https://example.com/cover.jpg",
      authorId: "author-001",
      authorName: "故事作者",
      status: "published",
      isPublic: true,
    },
  };

  const mockPaginatedResponse = {
    data: [mockBookmarkResponse],
    total: 1,
    page: 1,
    limit: 10,
    totalPages: 1,
    hasNext: false,
    hasPrev: false,
  };

  const mockStatsResponse: BookmarkStatsResponseDto = {
    totalBookmarks: 10,
    activeBookmarks: 8,
    archivedBookmarks: 1,
    invalidBookmarks: 1,
    categoryStats: [
      {
        category: BookmarkCategory.FAVORITE,
        customCategoryName: null,
        count: 3,
      },
      {
        category: BookmarkCategory.TO_READ,
        customCategoryName: null,
        count: 2,
      },
    ],
  };

  beforeEach(async () => {
    const mockService = {
      createBookmark: jest.fn(),
      getUserBookmarks: jest.fn(),
      getBookmarkDetail: jest.fn(),
      updateBookmarkCategory: jest.fn(),
      deleteBookmark: jest.fn(),
      getBookmarkStats: jest.fn(),
      isStoryBookmarked: jest.fn(),
      updateBookmarksOnStoryDelete: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [BookmarksController],
      providers: [
        {
          provide: BookmarksService,
          useValue: mockService,
        },
      ],
    }).compile();

    controller = module.get<BookmarksController>(BookmarksController);
    mockBookmarksService = module.get(BookmarksService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("createBookmark - POST /bookmarks", () => {
    const createBookmarkDto: CreateBookmarkDto = {
      storyId: "story-001",
      category: BookmarkCategory.FAVORITE,
      note: "很棒的故事",
      metadata: { source: "web", device: "desktop" },
    };

    it("应该成功创建收藏", async () => {
      // Arrange
      mockBookmarksService.createBookmark.mockResolvedValue(
        mockBookmarkResponse,
      );

      // Act
      const result = await controller.createBookmark(
        createBookmarkDto,
        mockRequest,
      );

      // Assert
      expect(mockBookmarksService.createBookmark).toHaveBeenCalledWith(
        createBookmarkDto,
        "user-001",
      );
      expect(result).toEqual({
        success: true,
        message: "收藏成功",
        data: mockBookmarkResponse,
        statusCode: HttpStatus.CREATED,
        timestamp: expect.any(String),
      });
    });

    it("应该正确处理自定义分类", async () => {
      // Arrange
      const customCategoryDto = {
        ...createBookmarkDto,
        category: BookmarkCategory.CUSTOM,
        customCategoryName: "学习资料",
      };
      const customResponse = {
        ...mockBookmarkResponse,
        category: BookmarkCategory.CUSTOM,
        customCategoryName: "学习资料",
      };
      mockBookmarksService.createBookmark.mockResolvedValue(customResponse);

      // Act
      const result = await controller.createBookmark(
        customCategoryDto,
        mockRequest,
      );

      // Assert
      expect(result.data.category).toBe(BookmarkCategory.CUSTOM);
      expect(result.data.customCategoryName).toBe("学习资料");
    });

    it("应该正确处理服务层错误", async () => {
      // Arrange
      mockBookmarksService.createBookmark.mockRejectedValue(
        new Error("故事不存在"),
      );

      // Act & Assert
      await expect(
        controller.createBookmark(createBookmarkDto, mockRequest),
      ).rejects.toThrow("故事不存在");
    });
  });

  describe("getUserBookmarks - GET /bookmarks", () => {
    const queryDto: BookmarkQueryDto = {
      page: 1,
      limit: 10,
      sortBy: BookmarkSortBy.CREATED_DESC,
    };

    it("应该成功获取用户收藏列表", async () => {
      // Arrange
      mockBookmarksService.getUserBookmarks.mockResolvedValue(
        mockPaginatedResponse,
      );

      // Act
      const result = await controller.getUserBookmarks(queryDto, mockRequest);

      // Assert
      expect(mockBookmarksService.getUserBookmarks).toHaveBeenCalledWith(
        "user-001",
        queryDto,
      );
      expect(result).toEqual({
        success: true,
        message: "获取收藏列表成功",
        data: mockPaginatedResponse,
        statusCode: HttpStatus.OK,
        timestamp: expect.any(String),
      });
    });

    it("应该正确传递查询参数", async () => {
      // Arrange
      const customQuery = {
        page: 2,
        limit: 5,
        category: BookmarkCategory.TO_READ,
        status: BookmarkStatus.ACTIVE,
        keyword: "测试",
        sortBy: BookmarkSortBy.STORY_TITLE_ASC,
      } as BookmarkQueryDto;
      mockBookmarksService.getUserBookmarks.mockResolvedValue(
        mockPaginatedResponse,
      );

      // Act
      await controller.getUserBookmarks(customQuery, mockRequest);

      // Assert
      expect(mockBookmarksService.getUserBookmarks).toHaveBeenCalledWith(
        "user-001",
        customQuery,
      );
    });

    it("应该处理空结果", async () => {
      // Arrange
      const emptyResponse = {
        ...mockPaginatedResponse,
        data: [],
        total: 0,
      };
      mockBookmarksService.getUserBookmarks.mockResolvedValue(emptyResponse);

      // Act
      const result = await controller.getUserBookmarks(queryDto, mockRequest);

      // Assert
      expect(result.data.total).toBe(0);
      expect(result.data.data).toEqual([]);
    });
  });

  describe("getBookmarkDetail - GET /bookmarks/:id", () => {
    it("应该成功获取收藏详情", async () => {
      // Arrange
      mockBookmarksService.getBookmarkDetail.mockResolvedValue(
        mockBookmarkResponse,
      );

      // Act
      const result = await controller.getBookmarkDetail(
        "bookmark-001",
        mockRequest,
      );

      // Assert
      expect(mockBookmarksService.getBookmarkDetail).toHaveBeenCalledWith(
        "bookmark-001",
        "user-001",
      );
      expect(result).toEqual({
        success: true,
        message: "获取收藏详情成功",
        data: mockBookmarkResponse,
        statusCode: HttpStatus.OK,
        timestamp: expect.any(String),
      });
    });

    it("应该正确处理收藏不存在的情况", async () => {
      // Arrange
      mockBookmarksService.getBookmarkDetail.mockRejectedValue(
        new Error("收藏记录不存在"),
      );

      // Act & Assert
      await expect(
        controller.getBookmarkDetail("non-existent", mockRequest),
      ).rejects.toThrow("收藏记录不存在");
    });
  });

  describe("updateBookmarkCategory - PUT /bookmarks/:id", () => {
    const updateDto: UpdateBookmarkCategoryDto = {
      category: BookmarkCategory.TO_READ,
      note: "待读列表",
    };

    it("应该成功更新收藏分类", async () => {
      // Arrange
      const updatedResponse = {
        ...mockBookmarkResponse,
        category: BookmarkCategory.TO_READ,
        note: "待读列表",
      };
      mockBookmarksService.updateBookmarkCategory.mockResolvedValue(
        updatedResponse,
      );

      // Act
      const result = await controller.updateBookmarkCategory(
        "bookmark-001",
        updateDto,
        mockRequest,
      );

      // Assert
      expect(mockBookmarksService.updateBookmarkCategory).toHaveBeenCalledWith(
        "bookmark-001",
        updateDto,
        "user-001",
      );
      expect(result).toEqual({
        success: true,
        message: "收藏分类更新成功",
        data: updatedResponse,
        statusCode: HttpStatus.OK,
        timestamp: expect.any(String),
      });
    });

    it("应该正确处理自定义分类更新", async () => {
      // Arrange
      const customUpdateDto = {
        category: BookmarkCategory.CUSTOM,
        customCategoryName: "技术文章",
        note: "技术学习相关",
      };
      const customResponse = {
        ...mockBookmarkResponse,
        category: BookmarkCategory.CUSTOM,
        customCategoryName: "技术文章",
        note: "技术学习相关",
      };
      mockBookmarksService.updateBookmarkCategory.mockResolvedValue(
        customResponse,
      );

      // Act
      const result = await controller.updateBookmarkCategory(
        "bookmark-001",
        customUpdateDto,
        mockRequest,
      );

      // Assert
      expect(result.data.category).toBe(BookmarkCategory.CUSTOM);
      expect(result.data.customCategoryName).toBe("技术文章");
    });

    it("应该正确处理权限错误", async () => {
      // Arrange
      mockBookmarksService.updateBookmarkCategory.mockRejectedValue(
        new Error("收藏记录不存在"),
      );

      // Act & Assert
      await expect(
        controller.updateBookmarkCategory(
          "bookmark-001",
          updateDto,
          mockRequest,
        ),
      ).rejects.toThrow("收藏记录不存在");
    });
  });

  describe("deleteBookmark - DELETE /bookmarks/:id", () => {
    it("应该成功删除收藏", async () => {
      // Arrange
      mockBookmarksService.deleteBookmark.mockResolvedValue();

      // Act
      const result = await controller.deleteBookmark(
        "bookmark-001",
        mockRequest,
      );

      // Assert
      expect(mockBookmarksService.deleteBookmark).toHaveBeenCalledWith(
        "bookmark-001",
        "user-001",
      );
      expect(result).toEqual({
        success: true,
        message: "取消收藏成功",
        statusCode: HttpStatus.OK,
        timestamp: expect.any(String),
      });
    });

    it("应该正确处理删除不存在的收藏", async () => {
      // Arrange
      mockBookmarksService.deleteBookmark.mockRejectedValue(
        new Error("收藏记录不存在"),
      );

      // Act & Assert
      await expect(
        controller.deleteBookmark("non-existent", mockRequest),
      ).rejects.toThrow("收藏记录不存在");
    });
  });

  describe("getBookmarkStats - GET /bookmarks/stats", () => {
    it("应该成功获取收藏统计", async () => {
      // Arrange
      mockBookmarksService.getBookmarkStats.mockResolvedValue(
        mockStatsResponse,
      );

      // Act
      const result = await controller.getBookmarkStats(mockRequest);

      // Assert
      expect(mockBookmarksService.getBookmarkStats).toHaveBeenCalledWith(
        "user-001",
      );
      expect(result).toEqual({
        success: true,
        message: "获取收藏统计成功",
        data: mockStatsResponse,
        statusCode: HttpStatus.OK,
        timestamp: expect.any(String),
      });
    });

    it("应该正确返回分类统计数据", async () => {
      // Arrange
      const detailedStats = {
        ...mockStatsResponse,
        categoryStats: [
          {
            category: BookmarkCategory.FAVORITE,
            customCategoryName: null,
            count: 5,
          },
          {
            category: BookmarkCategory.CUSTOM,
            customCategoryName: "学习资料",
            count: 3,
          },
        ],
      };
      mockBookmarksService.getBookmarkStats.mockResolvedValue(detailedStats);

      // Act
      const result = await controller.getBookmarkStats(mockRequest);

      // Assert
      expect(result.data.categoryStats).toHaveLength(2);
      expect(result.data.categoryStats[0].count).toBe(5);
      expect(result.data.categoryStats[1].customCategoryName).toBe("学习资料");
    });

    it("应该处理空统计数据", async () => {
      // Arrange
      const emptyStats = {
        totalBookmarks: 0,
        activeBookmarks: 0,
        archivedBookmarks: 0,
        invalidBookmarks: 0,
        categoryStats: [],
      };
      mockBookmarksService.getBookmarkStats.mockResolvedValue(emptyStats);

      // Act
      const result = await controller.getBookmarkStats(mockRequest);

      // Assert
      expect(result.data.totalBookmarks).toBe(0);
      expect(result.data.categoryStats).toEqual([]);
    });
  });

  describe("checkStoryBookmarkStatus - GET /bookmarks/check/:storyId", () => {
    it("已收藏的故事应该返回true", async () => {
      // Arrange
      mockBookmarksService.isStoryBookmarked.mockResolvedValue(true);

      // Act
      const result = await controller.checkStoryBookmarkStatus(
        "story-001",
        mockRequest,
      );

      // Assert
      expect(mockBookmarksService.isStoryBookmarked).toHaveBeenCalledWith(
        "story-001",
        "user-001",
      );
      expect(result).toEqual({
        success: true,
        message: "查询收藏状态成功",
        data: { isBookmarked: true },
        statusCode: HttpStatus.OK,
        timestamp: expect.any(String),
      });
    });

    it("未收藏的故事应该返回false", async () => {
      // Arrange
      mockBookmarksService.isStoryBookmarked.mockResolvedValue(false);

      // Act
      const result = await controller.checkStoryBookmarkStatus(
        "story-002",
        mockRequest,
      );

      // Assert
      expect(result.data).toEqual({ isBookmarked: false });
    });

    it("应该正确处理无效的故事ID", async () => {
      // Arrange
      mockBookmarksService.isStoryBookmarked.mockRejectedValue(
        new Error("无效的故事ID"),
      );

      // Act & Assert
      await expect(
        controller.checkStoryBookmarkStatus("invalid-id", mockRequest),
      ).rejects.toThrow("无效的故事ID");
    });
  });

  describe("错误处理和边界测试", () => {
    it("应该正确处理服务层抛出的各种异常", async () => {
      // Arrange
      const errors = [
        "故事不存在",
        "收藏记录不存在",
        "已经收藏过该故事",
        "使用自定义分类时必须提供分类名称",
      ];

      for (const error of errors) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        mockBookmarksService.createBookmark.mockRejectedValue(new Error(error));

        // Act & Assert
        await expect(
          controller.createBookmark(
            {
              storyId: "story-001",
              category: BookmarkCategory.DEFAULT,
            },
            mockRequest,
          ),
        ).rejects.toThrow(error);
      }
    });

    it("应该处理无效的UUID格式", async () => {
      // Arrange
      mockBookmarksService.getBookmarkDetail.mockRejectedValue(
        new Error("无效的ID格式"),
      );

      // Act & Assert
      await expect(
        controller.getBookmarkDetail("invalid-uuid", mockRequest),
      ).rejects.toThrow("无效的ID格式");
    });

    it("应该处理请求中缺少用户信息的情况", async () => {
      // Arrange
      const invalidRequest = {} as Request & { user: { id: string } };

      // Act & Assert
      await expect(
        controller.createBookmark(
          {
            storyId: "story-001",
            category: BookmarkCategory.DEFAULT,
          },
          invalidRequest,
        ),
      ).rejects.toThrow();
    });
  });

  describe("ApiResponse 格式验证", () => {
    it("所有成功响应应该包含正确的 ApiResponse 格式", async () => {
      // Arrange
      mockBookmarksService.createBookmark.mockResolvedValue(
        mockBookmarkResponse,
      );

      // Act
      const result = await controller.createBookmark(
        {
          storyId: "story-001",
          category: BookmarkCategory.FAVORITE,
        },
        mockRequest,
      );

      // Assert
      expect(result).toEqual({
        success: true,
        message: expect.any(String),
        data: expect.any(Object),
        statusCode: expect.any(Number),
        timestamp: expect.any(String),
      });
    });

    it("应该包含正确的HTTP状态码", async () => {
      // Arrange
      mockBookmarksService.createBookmark.mockResolvedValue(
        mockBookmarkResponse,
      );
      mockBookmarksService.getUserBookmarks.mockResolvedValue(
        mockPaginatedResponse,
      );

      // Act
      const createResult = await controller.createBookmark(
        {
          storyId: "story-001",
          category: BookmarkCategory.FAVORITE,
        },
        mockRequest,
      );
      const getResult = await controller.getUserBookmarks({}, mockRequest);

      // Assert
      expect(createResult.statusCode).toBe(HttpStatus.CREATED);
      expect(getResult.statusCode).toBe(HttpStatus.OK);
    });

    it("删除操作应该返回正确的响应格式", async () => {
      // Arrange
      mockBookmarksService.deleteBookmark.mockResolvedValue();

      // Act
      const result = await controller.deleteBookmark(
        "bookmark-001",
        mockRequest,
      );

      // Assert
      expect(result).toEqual({
        success: true,
        message: "取消收藏成功",
        statusCode: HttpStatus.OK,
        timestamp: expect.any(String),
      });
      expect(result.data).toBeUndefined(); // 删除操作不返回data
    });
  });

  describe("分页参数处理", () => {
    it("应该正确处理默认分页参数", async () => {
      // Arrange
      mockBookmarksService.getUserBookmarks.mockResolvedValue(
        mockPaginatedResponse,
      );

      // Act
      await controller.getUserBookmarks({}, mockRequest);

      // Assert
      expect(mockBookmarksService.getUserBookmarks).toHaveBeenCalledWith(
        "user-001",
        expect.objectContaining({
          page: undefined, // 由服务层设置默认值
          limit: undefined,
        }),
      );
    });

    it("应该正确验证分页参数范围", async () => {
      // Arrange
      const invalidQuery = {
        page: -1,
        limit: 0,
      } as BookmarkQueryDto;

      // 模拟服务层验证
      mockBookmarksService.getUserBookmarks.mockRejectedValue(
        new Error("无效的分页参数"),
      );

      // Act & Assert
      await expect(
        controller.getUserBookmarks(invalidQuery, mockRequest),
      ).rejects.toThrow("无效的分页参数");
    });
  });
});
