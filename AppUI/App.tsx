/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 *
 * @format
 */

import React from 'react';
import { StatusBar, View, StyleSheet } from 'react-native';
import { Provider } from 'react-redux';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { SafeAreaProvider } from 'react-native-safe-area-context';

// Redux Store
import { store } from './src/store';

// 导航组件
import RootNavigator from './src/navigation/RootNavigator';

// 常量
import { THEME } from './src/constants';

/**
 * 有故事 APP 主应用组件
 * 
 * 功能特性：
 * - Redux 状态管理
 * - React Navigation 导航
 * - 手势处理支持
 * - 安全区域适配
 * - 企业级架构设计
 */
const App: React.FC = () => {
  return (
    <GestureHandlerRootView style={styles.container}>
      <SafeAreaProvider>
        <Provider store={store}>
          <StatusBar
            barStyle="dark-content"
            backgroundColor={THEME.colors.background}
            translucent={false}
          />
          <View style={styles.appContainer}>
            <RootNavigator />
          </View>
        </Provider>
      </SafeAreaProvider>
    </GestureHandlerRootView>
  );
};

// ===== 样式定义 =====
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  appContainer: {
    flex: 1,
    backgroundColor: THEME.colors.background,
  },
});

export default App;
