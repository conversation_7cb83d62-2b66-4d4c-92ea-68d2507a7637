import {
  Injectable,
  NotFoundException,
  ConflictException,
  ForbiddenException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import {
  StoryReference,
  ReferenceStatus,
  ReferenceVisibility,
} from "./entities/story-reference.entity";
import {
  ReferenceCollection,
  CollectionType,
  CollectionStatus,
  CollectionVisibility,
} from "./entities/reference-collection.entity";
import { User } from "../users/entities/user.entity";
import { Story } from "../stories/entities/story.entity";
import type {
  CreateReferenceDto,
  CreateCollectionDto,
  ReferenceQueryDto,
  CollectionQueryDto,
  ReferenceResponseDto,
  CollectionResponseDto,
  ReferenceStatsResponseDto,
} from "./dto";
import type { PaginatedResponseDto } from "../../common/dto/paginated-response.dto";

@Injectable()
export class StoryReferencesService {
  constructor(
    @InjectRepository(StoryReference)
    private readonly referenceRepository: Repository<StoryReference>,
    @InjectRepository(ReferenceCollection)
    private readonly collectionRepository: Repository<ReferenceCollection>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Story)
    private readonly storyRepository: Repository<Story>,
  ) {}

  // ==================== 故事引用管理 ====================

  /**
   * 创建故事引用
   */
  async createReference(
    createReferenceDto: CreateReferenceDto,
    userId: string,
  ): Promise<ReferenceResponseDto> {
    const {
      storyId,
      collectionId,
      title,
      annotation,
      quotedSegments,
      visibility = ReferenceVisibility.PRIVATE,
      tags,
      importance = 3,
    } = createReferenceDto;

    // 验证故事存在
    const story = await this.storyRepository.findOne({
      where: { id: storyId },
    });
    if (!story) {
      throw new NotFoundException("故事不存在");
    }

    // 验证集合存在且有权限
    if (collectionId) {
      const collection = await this.collectionRepository.findOne({
        where: { id: collectionId },
      });
      if (!collection) {
        throw new NotFoundException("引用集合不存在");
      }
      if (collection.ownerId !== userId && !collection.allowCollaboration) {
        throw new ForbiddenException("无权限向该集合添加引用");
      }
    }

    // 检查是否已经引用过该故事
    const existingReference = await this.referenceRepository.findOne({
      where: { userId, storyId },
    });
    if (
      existingReference &&
      existingReference.status === ReferenceStatus.ACTIVE
    ) {
      throw new ConflictException("已经引用过该故事");
    }

    // 获取故事快照
    const storySnapshot = await this.getStorySnapshot(storyId);

    // 创建引用
    const reference = this.referenceRepository.create({
      userId,
      storyId,
      collectionId,
      title,
      annotation,
      quotedSegments,
      visibility,
      tags: tags || [],
      importance,
      storySnapshot,
    });

    const savedReference = await this.referenceRepository.save(reference);

    // 更新集合引用数量
    if (collectionId) {
      await this.updateCollectionStats(collectionId);
    }

    return this.formatReferenceResponse(savedReference, userId);
  }

  /**
   * 获取用户的引用列表
   */
  async getUserReferences(
    userId: string,
    queryDto: ReferenceQueryDto,
    currentUserId?: string,
  ): Promise<PaginatedResponseDto<ReferenceResponseDto>> {
    const {
      page = 1,
      limit = 20,
      collectionId,
      status = ReferenceStatus.ACTIVE,
      visibility,
      tags,
      minImportance,
      search,
      sortBy = "createdAt",
      sortOrder = "DESC",
    } = queryDto;

    const queryBuilder = this.referenceRepository
      .createQueryBuilder("reference")
      .leftJoinAndSelect("reference.user", "user")
      .leftJoinAndSelect("reference.story", "story")
      .leftJoinAndSelect("reference.collection", "collection")
      .where("reference.userId = :userId", { userId })
      .andWhere("reference.status = :status", { status });

    // 权限检查：只能看到公开的或自己的引用
    if (currentUserId !== userId) {
      queryBuilder.andWhere("reference.visibility = :publicVisibility", {
        publicVisibility: ReferenceVisibility.PUBLIC,
      });
    }

    // 应用筛选条件
    if (collectionId) {
      queryBuilder.andWhere("reference.collectionId = :collectionId", {
        collectionId,
      });
    }

    if (visibility) {
      queryBuilder.andWhere("reference.visibility = :visibility", {
        visibility,
      });
    }

    if (tags && tags.length > 0) {
      queryBuilder.andWhere("reference.tags && :tags", { tags });
    }

    if (minImportance) {
      queryBuilder.andWhere("reference.importance >= :minImportance", {
        minImportance,
      });
    }

    if (search) {
      queryBuilder.andWhere(
        "(reference.title ILIKE :search OR reference.annotation ILIKE :search OR story.title ILIKE :search)",
        { search: `%${search}%` },
      );
    }

    // 排序
    const validSortFields = [
      "createdAt",
      "updatedAt",
      "importance",
      "likesCount",
      "readCount",
    ];
    const sortField = validSortFields.includes(sortBy) ? sortBy : "createdAt";
    queryBuilder.orderBy(`reference.${sortField}`, sortOrder);

    // 分页
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    const [references, total] = await queryBuilder.getManyAndCount();

    const formattedReferences = await Promise.all(
      references.map((ref) => this.formatReferenceResponse(ref, currentUserId)),
    );

    const totalPages = Math.ceil(total / limit);
    return {
      data: formattedReferences,
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  /**
   * 获取引用详情
   */
  async getReferenceById(
    referenceId: string,
    currentUserId?: string,
  ): Promise<ReferenceResponseDto> {
    const reference = await this.referenceRepository
      .createQueryBuilder("reference")
      .leftJoinAndSelect("reference.user", "user")
      .leftJoinAndSelect("reference.story", "story")
      .leftJoinAndSelect("reference.collection", "collection")
      .where("reference.id = :referenceId", { referenceId })
      .getOne();

    if (!reference) {
      throw new NotFoundException("引用不存在");
    }

    // 权限检查
    if (
      reference.visibility === ReferenceVisibility.PRIVATE &&
      reference.userId !== currentUserId
    ) {
      throw new ForbiddenException("无权限查看该引用");
    }

    // 更新阅读统计
    if (currentUserId && currentUserId !== reference.userId) {
      await this.updateReadStats(referenceId);
    }

    return this.formatReferenceResponse(reference, currentUserId);
  }

  // ==================== 引用集合管理 ====================

  /**
   * 创建引用集合
   */
  async createCollection(
    createCollectionDto: CreateCollectionDto,
    ownerId: string,
  ): Promise<CollectionResponseDto> {
    const {
      name,
      description,
      collectionType = CollectionType.GENERAL,
      visibility = CollectionVisibility.PRIVATE,
      coverImage,
      colorTheme,
      tags,
      sortOrder = 0,
      allowCollaboration = true,
      settings,
    } = createCollectionDto;

    // 检查同名集合
    const existingCollection = await this.collectionRepository.findOne({
      where: { ownerId, name, status: CollectionStatus.ACTIVE },
    });

    if (existingCollection) {
      throw new ConflictException("已存在同名的集合");
    }

    // 创建集合
    const collection = this.collectionRepository.create({
      name,
      description,
      ownerId,
      collectionType,
      visibility,
      coverImage,
      colorTheme,
      tags: tags || [],
      sortOrder,
      allowCollaboration,
      settings: settings || {},
    });

    const savedCollection = await this.collectionRepository.save(collection);

    return this.formatCollectionResponse(savedCollection, ownerId);
  }

  /**
   * 获取用户的引用集合列表
   */
  async getUserCollections(
    userId: string,
    queryDto: CollectionQueryDto,
    currentUserId?: string,
  ): Promise<PaginatedResponseDto<CollectionResponseDto>> {
    const {
      page = 1,
      limit = 20,
      collectionType,
      status = CollectionStatus.ACTIVE,
      visibility,
      tags,
      isFeatured,
      search,
      sortBy = "lastContentUpdate",
      sortOrder = "DESC",
    } = queryDto;

    const queryBuilder = this.collectionRepository
      .createQueryBuilder("collection")
      .leftJoinAndSelect("collection.owner", "owner")
      .where("collection.ownerId = :userId", { userId })
      .andWhere("collection.status = :status", { status });

    // 权限检查：只能看到公开的或自己的集合
    if (currentUserId !== userId) {
      queryBuilder.andWhere("collection.visibility = :publicVisibility", {
        publicVisibility: CollectionVisibility.PUBLIC,
      });
    }

    // 应用筛选条件
    if (collectionType) {
      queryBuilder.andWhere("collection.collectionType = :collectionType", {
        collectionType,
      });
    }

    if (visibility) {
      queryBuilder.andWhere("collection.visibility = :visibility", {
        visibility,
      });
    }

    if (tags && tags.length > 0) {
      queryBuilder.andWhere("collection.tags && :tags", { tags });
    }

    if (isFeatured !== undefined) {
      queryBuilder.andWhere("collection.isFeatured = :isFeatured", {
        isFeatured,
      });
    }

    if (search) {
      queryBuilder.andWhere(
        "(collection.name ILIKE :search OR collection.description ILIKE :search)",
        { search: `%${search}%` },
      );
    }

    // 排序
    const validSortFields = [
      "lastContentUpdate",
      "createdAt",
      "referenceCount",
      "likesCount",
      "sortOrder",
    ];
    const sortField = validSortFields.includes(sortBy)
      ? sortBy
      : "lastContentUpdate";
    queryBuilder.orderBy(`collection.${sortField}`, sortOrder);

    // 分页
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    const [collections, total] = await queryBuilder.getManyAndCount();

    const formattedCollections = await Promise.all(
      collections.map((collection) =>
        this.formatCollectionResponse(collection, currentUserId),
      ),
    );

    const totalPages = Math.ceil(total / limit);
    return {
      data: formattedCollections,
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  /**
   * 获取引用统计数据
   */
  async getReferenceStats(userId: string): Promise<ReferenceStatsResponseDto> {
    const [
      totalReferences,
      totalCollections,
      publicReferences,
      privateReferences,
      totalLikes,
    ] = await Promise.all([
      this.referenceRepository.count({
        where: { userId, status: ReferenceStatus.ACTIVE },
      }),
      this.collectionRepository.count({
        where: { ownerId: userId, status: CollectionStatus.ACTIVE },
      }),
      this.referenceRepository.count({
        where: {
          userId,
          status: ReferenceStatus.ACTIVE,
          visibility: ReferenceVisibility.PUBLIC,
        },
      }),
      this.referenceRepository.count({
        where: {
          userId,
          status: ReferenceStatus.ACTIVE,
          visibility: ReferenceVisibility.PRIVATE,
        },
      }),
      this.referenceRepository
        .createQueryBuilder("reference")
        .select("SUM(reference.likesCount)", "totalLikes")
        .where("reference.userId = :userId", { userId })
        .andWhere("reference.status = :status", {
          status: ReferenceStatus.ACTIVE,
        })
        .getRawOne()
        .then((result) => parseInt(result?.totalLikes || "0", 10)),
    ]);

    // 获取按集合类型统计
    const collectionStats = await this.collectionRepository
      .createQueryBuilder("collection")
      .select("collection.collectionType", "collectionType")
      .addSelect("COUNT(*)", "count")
      .addSelect("SUM(collection.referenceCount)", "referenceCount")
      .where("collection.ownerId = :userId", { userId })
      .andWhere("collection.status = :status", {
        status: CollectionStatus.ACTIVE,
      })
      .groupBy("collection.collectionType")
      .getRawMany();

    // 获取按重要性统计
    const importanceStats = await this.referenceRepository
      .createQueryBuilder("reference")
      .select("reference.importance", "importance")
      .addSelect("COUNT(*)", "count")
      .where("reference.userId = :userId", { userId })
      .andWhere("reference.status = :status", {
        status: ReferenceStatus.ACTIVE,
      })
      .groupBy("reference.importance")
      .getRawMany();

    const totalForPercentage = importanceStats.reduce(
      (sum, stat) => sum + parseInt(stat.count, 10),
      0,
    );

    // 获取热门标签
    const topTags = await this.referenceRepository
      .createQueryBuilder("reference")
      .select("UNNEST(reference.tags)", "tag")
      .addSelect("COUNT(*)", "count")
      .addSelect("MAX(reference.createdAt)", "recentUse")
      .where("reference.userId = :userId", { userId })
      .andWhere("reference.status = :status", {
        status: ReferenceStatus.ACTIVE,
      })
      .groupBy("tag")
      .orderBy("count", "DESC")
      .limit(10)
      .getRawMany();

    // 获取最活跃的集合
    const topCollections = await this.collectionRepository
      .createQueryBuilder("collection")
      .where("collection.ownerId = :userId", { userId })
      .andWhere("collection.status = :status", {
        status: CollectionStatus.ACTIVE,
      })
      .orderBy("collection.referenceCount", "DESC")
      .limit(5)
      .getMany();

    // 计算阅读统计
    const readingStatsResult = await this.referenceRepository
      .createQueryBuilder("reference")
      .select("SUM(reference.readCount)", "totalReadCount")
      .addSelect("AVG(reference.importance)", "averageImportance")
      .where("reference.userId = :userId", { userId })
      .andWhere("reference.status = :status", {
        status: ReferenceStatus.ACTIVE,
      })
      .getRawOne();

    const mostReadReference = await this.referenceRepository
      .createQueryBuilder("reference")
      .where("reference.userId = :userId", { userId })
      .andWhere("reference.status = :status", {
        status: ReferenceStatus.ACTIVE,
      })
      .orderBy("reference.readCount", "DESC")
      .getOne();

    return {
      userId,
      totalReferences,
      totalCollections,
      publicReferences,
      privateReferences,
      totalLikes,
      totalViews: 0, // 需要实现浏览记录功能
      totalShares: 0, // 需要实现分享功能
      collectionStats: collectionStats.map((stat) => ({
        collectionType: stat.collectionType,
        count: parseInt(stat.count, 10),
        referenceCount: parseInt(stat.referenceCount || "0", 10),
      })),
      importanceStats: importanceStats.map((stat) => ({
        importance: stat.importance,
        count: parseInt(stat.count, 10),
        percentage:
          totalForPercentage > 0
            ? (parseInt(stat.count, 10) / totalForPercentage) * 100
            : 0,
      })),
      topTags: topTags.map((tag) => ({
        tag: tag.tag,
        count: parseInt(tag.count, 10),
        recentUse: tag.recentUse,
      })),
      topCollections: topCollections.map((collection) => ({
        collectionId: collection.id,
        collectionName: collection.name,
        referenceCount: collection.referenceCount,
        likesCount: collection.likesCount,
        lastUpdate: collection.lastContentUpdate || collection.updatedAt,
      })),
      recentActivity: [], // 需要实现活动记录功能
      readingStats: {
        totalReadCount: parseInt(readingStatsResult?.totalReadCount || "0", 10),
        averageImportance: parseFloat(
          readingStatsResult?.averageImportance || "0",
        ),
        mostReadReference: mostReadReference
          ? {
              referenceId: mostReadReference.id,
              title: mostReadReference.title,
              readCount: mostReadReference.readCount,
            }
          : undefined,
        readingTrend: [], // 需要实现趋势统计
      },
    };
  }

  // ==================== 私有辅助方法 ====================

  /**
   * 获取故事快照
   */
  private async getStorySnapshot(
    storyId: string,
  ): Promise<Record<string, unknown>> {
    const story = await this.storyRepository
      .createQueryBuilder("story")
      .leftJoinAndSelect("story.user", "author")
      .where("story.id = :storyId", { storyId })
      .getOne();

    if (!story) {
      return {};
    }

    return {
      title: story.title,
      summary: story.content,
      authorId: (story as any).userId, // eslint-disable-line @typescript-eslint/no-explicit-any
      authorName: story.user?.nickname || "未知作者",
      publishedAt: (story as any).createdAt, // eslint-disable-line @typescript-eslint/no-explicit-any
      chapterCount: 0, // 需要根据实际章节实体实现
    };
  }

  /**
   * 更新集合统计信息
   */
  private async updateCollectionStats(collectionId: string): Promise<void> {
    const referenceCount = await this.referenceRepository.count({
      where: { collectionId, status: ReferenceStatus.ACTIVE },
    });

    await this.collectionRepository.update(collectionId, {
      referenceCount,
      lastContentUpdate: new Date(),
    });
  }

  /**
   * 更新阅读统计
   */
  private async updateReadStats(referenceId: string): Promise<void> {
    await this.referenceRepository.increment(
      { id: referenceId },
      "readCount",
      1,
    );
    await this.referenceRepository.update(referenceId, {
      lastReadAt: new Date(),
    });
  }

  /**
   * 格式化引用响应
   */
  private async formatReferenceResponse(
    reference: StoryReference,
    currentUserId?: string,
  ): Promise<ReferenceResponseDto> {
    return {
      id: reference.id,
      userId: reference.userId,
      userName: reference.user?.nickname || "未知用户",
      storyId: reference.storyId,
      storyTitle: reference.story?.title || "未知故事",
      storyAuthorName: (reference.story?.user as any)?.nickname || "未知作者", // eslint-disable-line @typescript-eslint/no-explicit-any
      collectionId: reference.collectionId,
      collectionName: reference.collection?.name,
      title: reference.title,
      annotation: reference.annotation,
      quotedSegments: reference.quotedSegments as any, // eslint-disable-line @typescript-eslint/no-explicit-any
      status: reference.status,
      visibility: reference.visibility,
      tags: reference.tags,
      importance: reference.importance,
      likesCount: reference.likesCount,
      commentsCount: reference.commentsCount,
      storySnapshot: reference.storySnapshot as any, // eslint-disable-line @typescript-eslint/no-explicit-any
      lastReadAt: reference.lastReadAt,
      readCount: reference.readCount,
      metadata: reference.metadata,
      createdAt: reference.createdAt,
      updatedAt: reference.updatedAt,
      isLiked: false, // 需要实现点赞功能
      canEdit: currentUserId === reference.userId,
      canDelete: currentUserId === reference.userId,
    };
  }

  /**
   * 格式化集合响应
   */
  private async formatCollectionResponse(
    collection: ReferenceCollection,
    currentUserId?: string,
  ): Promise<CollectionResponseDto> {
    // 获取最新的几个引用预览
    const recentReferences = await this.referenceRepository
      .createQueryBuilder("reference")
      .leftJoinAndSelect("reference.story", "story")
      .where("reference.collectionId = :collectionId", {
        collectionId: collection.id,
      })
      .andWhere("reference.status = :status", {
        status: ReferenceStatus.ACTIVE,
      })
      .orderBy("reference.createdAt", "DESC")
      .limit(3)
      .getMany();

    return {
      id: collection.id,
      name: collection.name,
      description: collection.description,
      ownerId: collection.ownerId,
      ownerName: collection.owner?.nickname || "未知用户",
      ownerAvatar: (collection.owner as any)?.avatarUrl, // eslint-disable-line @typescript-eslint/no-explicit-any
      collectionType: collection.collectionType,
      status: collection.status,
      visibility: collection.visibility,
      coverImage: collection.coverImage,
      colorTheme: collection.colorTheme,
      tags: collection.tags,
      referenceCount: collection.referenceCount,
      followersCount: collection.followersCount,
      likesCount: collection.likesCount,
      sortOrder: collection.sortOrder,
      isFeatured: collection.isFeatured,
      allowCollaboration: collection.allowCollaboration,
      settings: collection.settings as any, // eslint-disable-line @typescript-eslint/no-explicit-any
      lastContentUpdate: collection.lastContentUpdate,
      statistics: collection.statistics as any, // eslint-disable-line @typescript-eslint/no-explicit-any
      metadata: collection.metadata,
      createdAt: collection.createdAt,
      updatedAt: collection.updatedAt,
      isFollowing: false, // 需要实现关注功能
      isLiked: false, // 需要实现点赞功能
      canEdit: currentUserId === collection.ownerId,
      canContribute:
        currentUserId === collection.ownerId || collection.allowCollaboration,
      recentReferences: recentReferences.map((ref) => ({
        id: ref.id,
        title: ref.title,
        storyTitle: ref.story?.title || "未知故事",
        createdAt: ref.createdAt,
      })),
    };
  }
}
