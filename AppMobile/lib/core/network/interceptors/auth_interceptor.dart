import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../../constants/storage_keys.dart';
import '../../constants/api_constants.dart';

/// 认证拦截器
class AuthInterceptor extends Interceptor {
  static const _storage = FlutterSecureStorage();
  static bool _isRefreshing = false;
  static final List<RequestOptions> _pendingRequests = [];

  @override
  void onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    // 排除不需要认证的接口
    if (_isAuthExcludedPath(options.path)) {
      return handler.next(options);
    }

    // 如果正在刷新token，将请求加入队列
    if (_isRefreshing) {
      _pendingRequests.add(options);
      return;
    }

    // 获取token并添加到请求头
    final token = await _storage.read(key: StorageKeys.accessToken);
    if (token != null) {
      options.headers['Authorization'] = 'Bearer $token';
    }

    handler.next(options);
  }

  @override
  void onError(
    DioException err,
    ErrorInterceptorHandler handler,
  ) async {
    // 处理401错误
    if (err.response?.statusCode == 401 && !_isAuthExcludedPath(err.requestOptions.path)) {
      if (_isRefreshing) {
        // 如果已经在刷新，将请求加入队列
        _pendingRequests.add(err.requestOptions);
        return;
      }

      _isRefreshing = true;

      try {
        // 尝试刷新token
        final refreshToken = await _storage.read(key: StorageKeys.refreshToken);
        if (refreshToken != null) {
          final dio = Dio(BaseOptions(
            baseUrl: err.requestOptions.baseUrl,
            connectTimeout: err.requestOptions.connectTimeout,
            receiveTimeout: err.requestOptions.receiveTimeout,
            sendTimeout: err.requestOptions.sendTimeout,
          ));
          final response = await dio.post(
            ApiConstants.refreshToken,
            data: {'refreshToken': refreshToken},
          );

          if (response.statusCode == 200) {
            final newAccessToken = response.data['data']['accessToken'];
            final newRefreshToken = response.data['data']['refreshToken'];

            // 保存新的token
            await _storage.write(
              key: StorageKeys.accessToken,
              value: newAccessToken,
            );
            await _storage.write(
              key: StorageKeys.refreshToken,
              value: newRefreshToken,
            );

            // 重试原始请求
            err.requestOptions.headers['Authorization'] = 'Bearer $newAccessToken';
            final cloneReq = await dio.fetch(err.requestOptions);

            // 处理等待队列中的请求
            for (final pendingRequest in _pendingRequests) {
              pendingRequest.headers['Authorization'] = 'Bearer $newAccessToken';
              dio.fetch(pendingRequest);
            }
            _pendingRequests.clear();

            return handler.resolve(cloneReq);
          }
        }
      } catch (e) {
        // 刷新失败，清除token
        await _clearAuthData();
      } finally {
        _isRefreshing = false;
        _pendingRequests.clear();
      }
    }

    handler.next(err);
  }

  /// 判断是否是不需要认证的路径
  bool _isAuthExcludedPath(String path) {
    final excludedPaths = [
      ApiConstants.loginPhone,
      ApiConstants.loginEmail,
      ApiConstants.sendSmsCode,
      ApiConstants.refreshToken,
    ];

    return excludedPaths.any((excluded) => path.contains(excluded));
  }

  /// 清除认证数据
  Future<void> _clearAuthData() async {
    await _storage.delete(key: StorageKeys.accessToken);
    await _storage.delete(key: StorageKeys.refreshToken);
    await _storage.delete(key: StorageKeys.userId);
    await _storage.delete(key: StorageKeys.userPhone);
  }
}