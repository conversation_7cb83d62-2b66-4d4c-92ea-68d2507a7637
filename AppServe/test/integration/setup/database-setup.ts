/**
 * 企业级集成测试数据库设置
 *
 * 设计原则:
 * 1. 真实PostgreSQL环境，验证实际数据库交互
 * 2. 事务隔离，每个测试独立的数据环境
 * 3. 种子数据管理，支持复杂业务场景测试
 * 4. 性能优化，快速数据重置机制
 */

import type { DataSource } from "typeorm";
import { Test } from "@nestjs/testing";
import type { TestingModule } from "@nestjs/testing";
import { getDataSourceToken } from "@nestjs/typeorm";
import { AppModule } from "@/app.module";
import { JwtAuthGuard } from "@/modules/auth/guards/jwt-auth.guard";
import * as Redis from "ioredis";
import { config } from "dotenv";
import { resolve } from "path";

// 全局测试变量
declare global {
  /* eslint-disable no-var */
  var testDataSource: DataSource;
  var testRedis: Redis.Redis;
  var testApp: TestingModule;
  /* eslint-enable no-var */
}

/**
 * 每个测试文件执行前的设置
 */
beforeAll(async () => {
  console.log("🔧 集成测试模块初始化...");

  // 加载集成测试专用环境配置
  config({ path: resolve(__dirname, "../../../.env.integration.test") });
  console.log("✅ 已加载集成测试环境配置");

  // 验证关键环境变量
  console.log("🔍 环境变量检查:", {
    NODE_ENV: process.env.NODE_ENV,
    REDIS_HOST: process.env.REDIS_HOST,
    REDIS_PORT: process.env.REDIS_PORT,
    REDIS_PASSWORD: process.env.REDIS_PASSWORD ? "已设置" : "未设置",
    DB_NAME: process.env.DB_NAME,
  });

  // 1. 创建测试应用模块（Mock守卫避免依赖注入问题）
  const moduleFixture: TestingModule = await Test.createTestingModule({
    imports: [AppModule],
  })
    .overrideGuard(JwtAuthGuard)
    .useValue({
      canActivate: jest.fn().mockReturnValue(true),
    })
    .compile();

  global.testApp = moduleFixture;

  // 2. 获取数据库连接
  global.testDataSource = moduleFixture.get<DataSource>(getDataSourceToken());

  // 3. 初始化Redis连接
  global.testRedis = new Redis.Redis({
    host: process.env.REDIS_HOST || "localhost",
    port: parseInt(process.env.REDIS_PORT || "6379", 10),
    password: process.env.REDIS_PASSWORD,
    keyPrefix: "integration-test:",
    db: 15, // 使用专用测试数据库
  });

  // 4. 数据库同步（仅测试环境）
  if (process.env.NODE_ENV === "integration-test") {
    await global.testDataSource.synchronize(true); // 强制同步，清空现有数据
    console.log("✅ 测试数据库同步完成");
  }

  console.log("🎯 集成测试环境就绪: 真实数据库 + Redis缓存");
});

/**
 * 每个测试文件执行后的清理
 */
afterAll(async () => {
  console.log("🧹 集成测试模块清理...");

  try {
    // 1. 清理Redis缓存
    if (global.testRedis) {
      await global.testRedis.flushdb();
      await global.testRedis.disconnect();
      console.log("✅ Redis测试缓存已清理");
    }

    // 2. 清理数据库数据（保留结构）
    if (global.testDataSource && global.testDataSource.isInitialized) {
      const entities = global.testDataSource.entityMetadatas;

      // 按依赖关系逆序清理表
      for (const entity of entities.reverse()) {
        const repository = global.testDataSource.getRepository(entity.name);
        await repository.delete({});
      }

      await global.testDataSource.destroy();
      console.log("✅ 测试数据库已清理");
    }

    // 3. 关闭测试应用
    if (global.testApp) {
      await global.testApp.close();
      console.log("✅ 测试应用已关闭");
    }
  } catch (error) {
    console.warn("⚠️ 集成测试清理警告:", (error as Error).message);
  }
});

/**
 * 每个测试用例前的数据重置
 */
beforeEach(async () => {
  // 为每个测试用例提供干净的数据环境
  if (global.testDataSource && global.testRedis) {
    // 快速清理Redis缓存
    await global.testRedis.flushdb();

    // 清理核心业务表数据（性能优化）
    const coreEntities = [
      "character_lightings",
      "light_requests",
      "stories",
      "characters",
      "users",
    ];

    for (const entityName of coreEntities) {
      try {
        await global.testDataSource.query(`DELETE FROM ${entityName}`);
      } catch (error) {
        // 某些表可能不存在，忽略错误继续
        console.debug(
          `清理表 ${entityName} 时的警告:`,
          (error as Error).message,
        );
      }
    }
  }
});

/**
 * 企业级测试数据工厂
 */
export class IntegrationTestDataFactory {
  constructor(private dataSource: DataSource) {}

  /**
   * 清理数据库
   */
  async clearDatabase(): Promise<void> {
    if (!this.dataSource.isInitialized) {
      console.warn("数据源未初始化，跳过数据库清理");
      return;
    }

    try {
      const queryRunner = this.dataSource.createQueryRunner();
      await queryRunner.connect();

      // 获取所有表名
      const tables = await queryRunner.getTables();

      // PostgreSQL使用TRUNCATE CASCADE清理数据
      for (const table of tables.reverse()) {
        try {
          await queryRunner.query(
            `TRUNCATE TABLE "${table.name}" RESTART IDENTITY CASCADE`,
          );
        } catch (error) {
          // 如果TRUNCATE失败，使用DELETE
          await queryRunner.query(`DELETE FROM "${table.name}"`);
        }
      }

      await queryRunner.release();
      console.log("✅ 数据库清理完成");
    } catch (error) {
      console.warn("⚠️ 数据库清理警告:", (error as Error).message);
    }
  }

  /**
   * 清理测试数据
   */
  async clearTestData(): Promise<void> {
    try {
      const coreEntities = [
        "character_lightings",
        "light_requests",
        "stories",
        "characters",
        "users",
      ];

      for (const entityName of coreEntities) {
        try {
          await this.dataSource.query(`DELETE FROM ${entityName}`);
        } catch (error) {
          console.debug(
            `清理表 ${entityName} 时的警告:`,
            (error as Error).message,
          );
        }
      }
    } catch (error) {
      console.warn("⚠️ 测试数据清理警告:", (error as Error).message);
    }
  }

  /**
   * 创建测试用户
   */
  async createUser(userData: {
    phone: string;
    nickname?: string;
    email?: string;
    password?: string;
    bio?: string;
    isPhoneVerified?: boolean;
    isVipUser?: boolean;
    role?: string;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    privacySettings?: any;
    aiQuotaRemaining?: number;
    aiQuotaResetDate?: Date;
    anomalyStatus?: string;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  }): Promise<any> {
    const userRepository = this.dataSource.getRepository("User");

    const user = userRepository.create({
      phone: userData.phone,
      nickname: userData.nickname || "测试用户",
      email: userData.email,
      bio: userData.bio || "这是一个测试用户",
      passwordHash: userData.password
        ? `hashed_${userData.password}`
        : "hashed_Test123456!",
      userNumber: `${Math.floor(100000 + Math.random() * 900000)}`,
      isPhoneVerified: userData.isPhoneVerified ?? true,
      isVipUser: userData.isVipUser ?? false,
      role: userData.role || "user",
      privacySettings: userData.privacySettings || {},
      status: "active",
      isActive: true,
      aiQuotaRemaining: userData.aiQuotaRemaining ?? 3,
      aiQuotaResetDate: userData.aiQuotaResetDate ?? new Date(),
      anomalyStatus: userData.anomalyStatus ?? "normal",
      profileDisplaySettings: {
        showBirthday: false,
        showBio: true,
        showStatistics: true,
        showCharacters: true,
        showTimeline: true,
      },
      failedLoginAttempts: 0,
      anomalyWarningCount: 0,
      anomalyRestrictionCount: 0,
      dailyLightingAttempts: 0,
      lightingAttemptResetDate: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    return await userRepository.save(user);
  }

  /**
   * 创建测试故事
   */
  async createStory(storyData: {
    title: string;
    content: string;
    authorId: string;
    status?: string;
    visibility?: string;
    category?: string;
    tags?: string[];
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  }): Promise<any> {
    const storyRepository = this.dataSource.getRepository("Story");

    const story = storyRepository.create({
      title: storyData.title,
      content: storyData.content,
      userId: storyData.authorId,
      status: storyData.status || 1, // StoryStatus.DRAFT
      permissionLevel: storyData.visibility || "private",
      category: storyData.category || "other",
      tags: storyData.tags || [],
      viewCount: 0,
      likeCount: 0,
      shareCount: 0,
      commentCount: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    return await storyRepository.save(story);
  }

  /**
   * 创建测试人物
   */
  async createCharacter(characterData: {
    name: string;
    creatorId: string;
    storyId?: string;
    relationship?: string;
    description?: string;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  }): Promise<any> {
    const characterRepository = this.dataSource.getRepository("Character");

    const character = characterRepository.create({
      name: characterData.name,
      creatorId: characterData.creatorId,
      storyId: characterData.storyId,
      relationship: characterData.relationship || "朋友",
      description: characterData.description || "测试人物",
      isLighted: false,
      lightingCount: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    return await characterRepository.save(character);
  }

  /**
   * 创建人物点亮关系
   */
  async createCharacterLighting(lightingData: {
    characterId: string;
    lighterUserId: string;
    storyId?: string;
    creatorUserId?: string;
    confirmedAt?: Date;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  }): Promise<any> {
    const lightingRepository =
      this.dataSource.getRepository("CharacterLighting");

    const lighting = lightingRepository.create({
      characterId: lightingData.characterId,
      lighterUserId: lightingData.lighterUserId,
      storyId: lightingData.storyId || "default-story-id",
      creatorUserId: lightingData.creatorUserId || "default-creator-id",
      status: "active",
      confirmedAt: lightingData.confirmedAt || new Date(),
      createdAt: new Date(),
    });

    return await lightingRepository.save(lighting);
  }
}
