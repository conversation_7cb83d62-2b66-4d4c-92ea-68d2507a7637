import { Injectable, Logger } from "@nestjs/common";
import { EnhancedRedisService } from "./enhanced-redis.service";
import type Redis from "ioredis";

export interface VerificationAttempt {
  phone: string;
  code: string;
  timestamp: number;
  ip: string;
  deviceFingerprint: string;
  success: boolean;
}

export interface SecurityCheckResult {
  allowed: boolean;
  reason?: string;
  remainingAttempts?: number;
  lockoutTime?: number;
}

/**
 * 验证安全服务
 *
 * 功能特性：
 * - 验证失败次数限制
 * - 恶意尝试检测
 * - 自动锁定机制
 * - 验证码失效处理
 */
@Injectable()
export class VerificationSecurityService {
  private readonly logger = new Logger(VerificationSecurityService.name);
  private redis: Redis;

  // 安全配置
  private readonly MAX_ATTEMPTS_PER_CODE = 3; // 每个验证码最多尝试3次
  private readonly MAX_ATTEMPTS_PER_PHONE_HOUR = 10; // 每小时最多尝试10次
  private readonly MAX_ATTEMPTS_PER_IP_HOUR = 20; // 每IP每小时最多20次
  private readonly LOCKOUT_DURATION = 60 * 60; // 锁定1小时

  constructor(private enhancedRedisService: EnhancedRedisService) {
    this.redis = this.enhancedRedisService.getClient();
  }

  /**
   * 检查验证尝试是否被允许
   */
  async checkVerificationAttempt(
    phone: string,
    ip: string,
    _deviceFingerprint: string,
  ): Promise<SecurityCheckResult> {
    try {
      // 1. 检查手机号锁定状态
      const phoneLockCheck = await this.checkPhoneLockout(phone);
      if (!phoneLockCheck.allowed) {
        return phoneLockCheck;
      }

      // 2. 检查IP锁定状态
      const ipLockCheck = await this.checkIPLockout(ip);
      if (!ipLockCheck.allowed) {
        return ipLockCheck;
      }

      // 3. 检查当前验证码尝试次数
      const codeAttemptCheck = await this.checkCodeAttempts(phone);
      if (!codeAttemptCheck.allowed) {
        return codeAttemptCheck;
      }

      // 4. 检查小时限制
      const hourlyLimitCheck = await this.checkHourlyLimits(phone, ip);
      if (!hourlyLimitCheck.allowed) {
        return hourlyLimitCheck;
      }

      return { allowed: true };
    } catch (error) {
      this.logger.error(
        `验证安全检查失败: ${phone}`,
        error instanceof Error ? error.stack : String(error),
      );
      return { allowed: true }; // 安全检查失败时默认允许
    }
  }

  /**
   * 记录验证尝试
   */
  async recordVerificationAttempt(attempt: VerificationAttempt): Promise<void> {
    const now = Date.now();
    const hour = Math.floor(now / (60 * 60 * 1000));

    try {
      // 记录验证尝试
      await this.recordAttempt(attempt);

      if (!attempt.success) {
        // 失败尝试处理
        await this.handleFailedAttempt(attempt, hour);
      } else {
        // 成功时清理失败记录
        await this.clearFailureRecords(attempt.phone);
      }
    } catch (error) {
      this.logger.error(
        `记录验证尝试失败: ${attempt.phone}`,
        error instanceof Error ? error.stack : String(error),
      );
    }
  }

  /**
   * 使验证码失效
   */
  async invalidateCode(phone: string, reason: string): Promise<void> {
    try {
      const key = `verification:code:${phone}`;
      await this.redis.del(key);

      this.logger.log(`验证码已失效: ${phone} - 原因: ${reason}`);
    } catch (error) {
      this.logger.error(
        `验证码失效操作失败: ${phone}`,
        error instanceof Error ? error.stack : String(error),
      );
      // 失效操作失败不抛异常，继续执行
    }
  }

  /**
   * 检查验证码尝试次数
   */
  async getCodeAttemptCount(phone: string): Promise<number> {
    try {
      const key = `verification:attempts:${phone}`;
      const count = await this.redis.get(key);
      return parseInt(count || "0");
    } catch (error) {
      this.logger.error(
        `获取验证码尝试次数失败: ${phone}`,
        error instanceof Error ? error.stack : String(error),
      );
      return 0; // 错误时返回0
    }
  }

  /**
   * 检查手机号锁定状态
   */
  private async checkPhoneLockout(phone: string): Promise<SecurityCheckResult> {
    const key = `verification:lockout:phone:${phone}`;
    const lockout = await this.redis.get(key);

    if (lockout) {
      const lockoutData = JSON.parse(lockout);
      const remainingTime = Math.ceil(
        (lockoutData.expiresAt - Date.now()) / 1000,
      );

      if (remainingTime > 0) {
        return {
          allowed: false,
          reason: `手机号已被锁定，请${Math.ceil(remainingTime / 60)}分钟后重试`,
          lockoutTime: remainingTime,
        };
      }
    }

    return { allowed: true };
  }

  /**
   * 检查IP锁定状态
   */
  private async checkIPLockout(ip: string): Promise<SecurityCheckResult> {
    const key = `verification:lockout:ip:${ip}`;
    const lockout = await this.redis.get(key);

    if (lockout) {
      const lockoutData = JSON.parse(lockout);
      const remainingTime = Math.ceil(
        (lockoutData.expiresAt - Date.now()) / 1000,
      );

      if (remainingTime > 0) {
        return {
          allowed: false,
          reason: `IP已被锁定，请${Math.ceil(remainingTime / 60)}分钟后重试`,
          lockoutTime: remainingTime,
        };
      }
    }

    return { allowed: true };
  }

  /**
   * 检查当前验证码尝试次数
   */
  private async checkCodeAttempts(phone: string): Promise<SecurityCheckResult> {
    const attemptCount = await this.getCodeAttemptCount(phone);

    if (attemptCount >= this.MAX_ATTEMPTS_PER_CODE) {
      // 验证码失效
      await this.invalidateCode(phone, "尝试次数过多");

      return {
        allowed: false,
        reason: "验证码已失效，请重新获取",
        remainingAttempts: 0,
      };
    }

    return {
      allowed: true,
      remainingAttempts: this.MAX_ATTEMPTS_PER_CODE - attemptCount,
    };
  }

  /**
   * 检查小时限制
   */
  private async checkHourlyLimits(
    phone: string,
    ip: string,
  ): Promise<SecurityCheckResult> {
    const hour = Math.floor(Date.now() / (60 * 60 * 1000));

    // 检查手机号小时限制
    const phoneHourKey = `verification:hour:phone:${phone}:${hour}`;
    const phoneHourCount = parseInt(
      (await this.redis.get(phoneHourKey)) || "0",
    );

    if (phoneHourCount >= this.MAX_ATTEMPTS_PER_PHONE_HOUR) {
      return {
        allowed: false,
        reason: "手机号验证尝试过于频繁，请1小时后重试",
      };
    }

    // 检查IP小时限制
    const ipHourKey = `verification:hour:ip:${ip}:${hour}`;
    const ipHourCount = parseInt((await this.redis.get(ipHourKey)) || "0");

    if (ipHourCount >= this.MAX_ATTEMPTS_PER_IP_HOUR) {
      return {
        allowed: false,
        reason: "IP验证尝试过于频繁，请1小时后重试",
      };
    }

    return { allowed: true };
  }

  /**
   * 记录验证尝试
   */
  private async recordAttempt(attempt: VerificationAttempt): Promise<void> {
    const hour = Math.floor(attempt.timestamp / (60 * 60 * 1000));
    const pipe = this.redis.pipeline();

    // 增加当前验证码尝试次数
    const attemptKey = `verification:attempts:${attempt.phone}`;
    pipe.incr(attemptKey);
    pipe.expire(attemptKey, 5 * 60); // 5分钟过期

    // 增加小时统计
    const phoneHourKey = `verification:hour:phone:${attempt.phone}:${hour}`;
    const ipHourKey = `verification:hour:ip:${attempt.ip}:${hour}`;

    pipe.incr(phoneHourKey);
    pipe.expire(phoneHourKey, 60 * 60); // 1小时过期

    pipe.incr(ipHourKey);
    pipe.expire(ipHourKey, 60 * 60); // 1小时过期

    await pipe.exec();
  }

  /**
   * 处理失败尝试
   */
  private async handleFailedAttempt(
    attempt: VerificationAttempt,
    _hour: number,
  ): Promise<void> {
    // 记录失败次数
    const failureKey = `verification:failures:${attempt.phone}`;
    const failureCount = await this.redis.incr(failureKey);
    await this.redis.expire(failureKey, 60 * 60); // 1小时过期

    // 检查是否需要锁定
    if (failureCount >= 5) {
      await this.lockoutPhone(attempt.phone, "验证失败次数过多");
    }

    // 检查IP失败次数
    const ipFailureKey = `verification:failures:ip:${attempt.ip}`;
    const ipFailureCount = await this.redis.incr(ipFailureKey);
    await this.redis.expire(ipFailureKey, 60 * 60); // 1小时过期

    if (ipFailureCount >= 10) {
      await this.lockoutIP(attempt.ip, "IP验证失败次数过多");
    }
  }

  /**
   * 锁定手机号
   */
  private async lockoutPhone(phone: string, reason: string): Promise<void> {
    const key = `verification:lockout:phone:${phone}`;
    const lockoutData = {
      reason,
      lockedAt: Date.now(),
      expiresAt: Date.now() + this.LOCKOUT_DURATION * 1000,
    };

    await this.redis.setex(
      key,
      this.LOCKOUT_DURATION,
      JSON.stringify(lockoutData),
    );

    this.logger.warn(`手机号已锁定: ${phone} - ${reason}`);
  }

  /**
   * 锁定IP
   */
  private async lockoutIP(ip: string, reason: string): Promise<void> {
    const key = `verification:lockout:ip:${ip}`;
    const lockoutData = {
      reason,
      lockedAt: Date.now(),
      expiresAt: Date.now() + this.LOCKOUT_DURATION * 1000,
    };

    await this.redis.setex(
      key,
      this.LOCKOUT_DURATION,
      JSON.stringify(lockoutData),
    );

    this.logger.warn(`IP已锁定: ${ip} - ${reason}`);
  }

  /**
   * 清理失败记录
   */
  private async clearFailureRecords(phone: string): Promise<void> {
    const pipe = this.redis.pipeline();

    pipe.del(`verification:failures:${phone}`);
    pipe.del(`verification:attempts:${phone}`);

    await pipe.exec();
  }

  /**
   * 获取安全统计信息
   */
  async getSecurityStats(phone: string): Promise<{
    attemptCount: number;
    isLocked: boolean;
    remainingAttempts: number;
  }> {
    const attemptCount = await this.getCodeAttemptCount(phone);
    const lockCheck = await this.checkPhoneLockout(phone);

    return {
      attemptCount,
      isLocked: !lockCheck.allowed,
      remainingAttempts: Math.max(0, this.MAX_ATTEMPTS_PER_CODE - attemptCount),
    };
  }
}
