import { registerAs } from "@nestjs/config";
import type { WinstonModuleOptions } from "nest-winston";
import * as winston from "winston";
import "winston-daily-rotate-file";

export const loggerConfig = registerAs("logger", (): WinstonModuleOptions => {
  const logDir = process.env.LOG_DIRECTORY || "./logs";

  // 自定义格式
  const customFormat = winston.format.combine(
    winston.format.timestamp({ format: "YYYY-MM-DD HH:mm:ss.SSS" }),
    winston.format.errors({ stack: true }),
    winston.format.json(),
    winston.format.printf((info) => {
      const { timestamp, level, message, context, trace, ...meta } = info;

      const logObj = {
        timestamp,
        level: level.toUpperCase(),
        context: context || "Application",
        message,
        ...(trace && typeof trace === "string" ? { trace } : {}),
        ...(Object.keys(meta).length && { meta }),
        // 添加链路追踪ID
        ...(process.env.REQUEST_ID && { requestId: process.env.REQUEST_ID }),
        // 添加服务实例信息
        instance: process.env.INSTANCE_ID || process.env.HOSTNAME || "unknown",
        service: "story-app-backend",
        version: process.env.npm_package_version || "1.0.0",
      };

      return JSON.stringify(logObj);
    }),
  );

  const transports: winston.transport[] = [];

  // 控制台输出（开发环境彩色，生产环境JSON）
  if (process.env.LOG_CONSOLE !== "false") {
    transports.push(
      new winston.transports.Console({
        level: process.env.LOG_LEVEL || "info",
        format:
          process.env.NODE_ENV === "production"
            ? customFormat
            : winston.format.combine(
                winston.format.colorize(),
                winston.format.simple(),
              ),
      }),
    );
  }

  // 文件输出（仅生产环境或明确启用）
  if (
    process.env.LOG_FILE === "true" ||
    process.env.NODE_ENV === "production"
  ) {
    // 应用日志轮转
    transports.push(
      new winston.transports.DailyRotateFile({
        filename: `${logDir}/app-%DATE%.log`,
        datePattern: "YYYY-MM-DD",
        maxSize: process.env.LOG_MAX_SIZE || "100m",
        maxFiles: process.env.LOG_MAX_FILES || "30d",
        format: customFormat,
        level: "info",
      }),
    );

    // 错误日志单独记录
    transports.push(
      new winston.transports.DailyRotateFile({
        filename: `${logDir}/error-%DATE%.log`,
        datePattern: "YYYY-MM-DD",
        maxSize: process.env.LOG_MAX_SIZE || "100m",
        maxFiles: process.env.LOG_MAX_FILES || "30d",
        format: customFormat,
        level: "error",
      }),
    );

    // 性能日志
    transports.push(
      new winston.transports.DailyRotateFile({
        filename: `${logDir}/performance-%DATE%.log`,
        datePattern: "YYYY-MM-DD",
        maxSize: "50m",
        maxFiles: "7d",
        format: customFormat,
        level: "warn",
      }),
    );
  }

  return {
    level: process.env.LOG_LEVEL || "info",
    format: customFormat,
    transports,
    // 异常处理
    exceptionHandlers: [
      new winston.transports.File({
        filename: `${logDir}/exceptions.log`,
        format: customFormat,
      }),
    ],
    rejectionHandlers: [
      new winston.transports.File({
        filename: `${logDir}/rejections.log`,
        format: customFormat,
      }),
    ],
    exitOnError: false,
  };
});
