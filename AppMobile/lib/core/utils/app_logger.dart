import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';

/// 应用日志工具类
class AppLogger {
  AppLogger._();

  static const String _tag = 'YGS';

  /// 调试日志
  static void d(
    String message, {
    Object? error,
    StackTrace? stackTrace,
    String? tag,
  }) {
    if (kDebugMode) {
      developer.log(
        message,
        name: tag ?? _tag,
        level: 500, // Debug level
        error: error,
        stackTrace: stackTrace,
      );
    }
  }

  /// 信息日志
  static void i(
    String message, {
    Object? error,
    StackTrace? stackTrace,
    String? tag,
  }) {
    developer.log(
      message,
      name: tag ?? _tag,
      level: 800, // Info level
      error: error,
      stackTrace: stackTrace,
    );
  }

  /// 警告日志
  static void w(
    String message, {
    Object? error,
    StackTrace? stackTrace,
    String? tag,
  }) {
    developer.log(
      message,
      name: tag ?? _tag,
      level: 900, // Warning level
      error: error,
      stackTrace: stackTrace,
    );
  }

  /// 错误日志
  static void e(
    String message, {
    Object? error,
    StackTrace? stackTrace,
    String? tag,
  }) {
    developer.log(
      message,
      name: tag ?? _tag,
      level: 1000, // Error level
      error: error,
      stackTrace: stackTrace,
    );
  }

  /// 致命错误日志
  static void wtf(
    String message, {
    Object? error,
    StackTrace? stackTrace,
    String? tag,
  }) {
    developer.log(
      message,
      name: tag ?? _tag,
      level: 1200, // Fatal level
      error: error,
      stackTrace: stackTrace,
    );
  }

  /// 详细日志（仅调试模式）
  static void v(
    String message, {
    Object? error,
    StackTrace? stackTrace,
    String? tag,
  }) {
    if (kDebugMode) {
      developer.log(
        message,
        name: tag ?? _tag,
        level: 400, // Verbose level
        error: error,
        stackTrace: stackTrace,
      );
    }
  }
}