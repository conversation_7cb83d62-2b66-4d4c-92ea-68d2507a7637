import {
  IsString,
  IsOptional,
  IsEnum,
  IsBoolean,
  IsUrl,
  <PERSON><PERSON><PERSON><PERSON>,
} from "class-validator";

/**
 * 更新人物DTO
 * 用于更新人物信息的数据传输对象
 */
export class UpdateCharacterDto {
  @IsOptional()
  @IsString()
  @MaxLength(100)
  name?: string;

  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @IsOptional()
  @IsUrl()
  avatarUrl?: string;

  @IsOptional()
  @IsEnum(["男", "女", "未知"])
  gender?: string;

  @IsOptional()
  @IsEnum(["家人", "朋友", "同事", "同学", "恋人", "配偶", "其他"])
  relationship?: string;

  @IsOptional()
  @IsString()
  @MaxLength(100)
  customRelationship?: string;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}
