---
description: 
globs: 
alwaysApply: true
---
# YGS (有故事) AI 开发助手规范

> **文档目的**: 规范 AI 开发助手的行为约束和质量标准  
> **适用范围**: 所有使用 Claude 进行的 YGS 项目开发工作  
> **最后更新**: 202执行**: 所有开发规范必须无条件e Code 基础配置

### **: 本项目使用中文作为主要开发语言

- 所有文档、注释、提交信息应使用中文
- Claude Code 响应应使用中文
- 代码变量名可使用英文，但注释必须用中文
- 错误信息和日志应提供中文说明

### ⏰ 时间标准

**重要**: 确保使用正确的当前时间

- **时区**: 北京时间 (UTC+8)
- **时间来源**: 使用环境变量中的 "Today's date" 作为当前日期基准
- **格式标准**: YYYY-MM-DD (如: 2025-07-13)
- **应用场景**: 所有日志记录、文档更新、代码注释中的时间戳

### 📝 开发基础规范

- 优先使用中文进行技术讨论和文档编写
- 保持代码注释的中文一致性
- Git 提交信息使用中文描述功能变更

## 🛠️ 技术栈约束规范

### 🔒 技术栈稳定性原则

**❌ 严禁随意更改技术栈**: 项目技术架构必须保持稳定性

- **坚持现有技术选型**: NestJS 11 + Fastify 5 + PostgreSQL + Redis + React Native
- **优先解决兼容性问题**: 遇到技术问题时，优先通过配置调整、版本适配、代码重构等方式解决
- **禁止轻易替换技术组件**: 不允许因为遇到困难就随意替换框架、数据库、缓存等核心技术组件
- **征得用户同意的例外情况**: 只有在技术问题确实无法通过其他方式解决，且会严重影响项目进展时，才能征得用户明确同意后进行技术栈调整

### 🗄️ 数据库使用强制规范

**❌ 绝对禁止**: 使用本地 PostgreSQL 数据库  
**✅ 唯一选择**: 必须使用阿里云 RDS PostgreSQL 数据库

- **开发环境**: 连接阿里云 RDS
- **测试环境**: 连接阿里云 RDS
- **本地调试**: 连接阿里云 RDS
- **API 测试**: 连接阿里云 RDS

**重要警告**: 如果 RDS 连接有问题，必须解决 RDS 连接问题，不允许切换到本地数据库作为替代方案

## 📋 v0.5.0 开发阶段约束

### 🎯 当前开发阶段

**重要**: 现阶段专注于 v0.5.0 MVP 版本开发

- **开发版本**: v0.5.0 (MVP 可推广完整产品)
- **文档基准**: 严格按照产品文档 v0.5.0 中的规范执行
- **开发重点**: 核心业务功能实现，特别是人物点亮系统

### 📚 必须参考文档

- `产品文档/v0.5.0/后端技术文档.md` - 技术实现标准
- `产品文档/v0.5.0/产品功能清单.md` - 功能需求基准
- `项目文档/项目执行-v0.5.0.md` - 当前项目执行情况
- `产品文档/v0.5.0/业务流程说明.md` - 业务逻辑规范
- `产品文档/v0.5.0/开发计划与执行方案.md` - 开发执行计划

## 🏗️ 企业级开发标准（绝对约束）

### ❌ 严禁降低企业级标准

**重要警告**: 在任何情况下都不允许为了完成开发任务而简化或放弃企业级标准。这些标准是项目成功的基础，必须无条件遵守。

### 🔒 不可妥协的核心原则

1. **问题解决优先原则**: 遇到任何问题都必须彻底解决根本原因，绝对禁止绕开问题或使用临时解决方案
2. **代码质量不可降低**: 测试覆盖率必须 ≥80%，TypeScript 编译 0 错误 0 警告
3. **架构设计不可简化**: 必须考虑扩展性、维护性、可测试性
4. **安全标准不可松懈**: 认证、授权、数据验证、防护机制必须完整
5. **性能要求不可忽视**: 数据库优化、缓存策略、并发处理必须到位
6. **文档规范不可省略**: 代码注释、API 文档、技术文档必须完整

### 🎯 架构师视角（强制执行）

**设计模式**: 必须采用行业标准的设计模式（工厂、策略、观察者等）  
**模块化**: 代码结构必须清晰，模块边界明确，单一职责原则  
**可扩展性**: 每个模块都必须支持后续功能扩展而无需重构  
**可维护性**: 代码必须易于理解、修改和调试

**性能架构**:

- 数据库查询必须优化（索引、连接池、查询优化）
- 缓存策略必须完善（Redis 分布式缓存、本地缓存）
- 并发处理必须安全（锁机制、事务管理、状态一致性）

**安全架构**:

- 认证系统必须完善（JWT + 刷新令牌、多因素认证）
- 权限控制必须细粒度（RBAC、资源级权限）
- 数据验证必须严格（输入验证、SQL 注入防护、XSS 防护）

**监控体系**: 日志、指标、健康检查、错误追踪必须完整

### 🚀 产品总监视角（强制执行）

- **用户体验**: 功能实现必须站在用户角度考虑，不能为了开发便利而牺牲用户体验
- **产品演进**: 必须为后续功能扩展预留接口和扩展点
- **数据驱动**: 必须实现数据埋点和统计，支持产品决策
- **稳定性优先**: 核心功能必须稳定可靠，容错机制完善
- **商业价值**: 每个功能都必须有明确的商业价值和用户价值

## 🛡️ 质量保证体系（强制执行）

### 📊 测试标准

- 单元测试覆盖率 ≥80%
- 集成测试覆盖核心业务流程
- 端到端测试覆盖关键用户场景
- 性能测试确保系统负载能力

### 🔍 代码审查

- 所有代码必须经过严格的代码审查
- 必须检查设计模式、性能、安全性
- 必须验证是否符合企业级标准

### 🔄 持续集成

- 自动化构建、测试、部署流程
- 代码提交必须通过所有质量检查
- 性能回归测试必须通过

## ⚡ 开发执行要求（绝对约束）

### ❌ 严禁为了进度而牺牲质量

**重要警告**: 任何情况下都不允许为了完成开发任务而降低代码质量、简化架构设计或忽视安全标准。

### 🎯 强制执行的开发标准

1. **开发计划遵守**: 严格按照 Phase 1-5 的时间节点和交付物执行
2. **代码质量检查**: 每次提交前必须通过以下检查
   ```bash
   npm run lint                 # ESLint检查，0错误0警告
   npm run test                 # 单元测试，100%通过
   npm run test:cov             # 覆盖率≥80%
   npm run build               # TypeScript编译，0错误0警告
   npm run security:check      # 安全漏洞检查
   ```
3. **架构设计审查**: 每个技术决策都必须经过架构设计审查
4. **产品价值验证**: 功能实现必须经过产品价值验证

### 🔒 类型安全规范（2025-07-12 已全面实施）

**❌ 严禁使用 any 类型**: 必须使用具体类型定义，使用 unknown 代替 any  
**✅ 统一类型定义**: 使用`src/common/types/`下的共享类型接口  
**✅ 环境变量安全**: 使用`src/common/utils/env.utils.ts`中的工具函数  
**✅ 请求类型标准**: Controller 中使用`AuthRequest`类型而非 any  
**✅ 空值安全**: 实体字段正确标记 nullable 类型（如`string | null`）  
**✅ Redis 结果检查**: 所有 Redis 操作结果必须进行 null 检查

## 🚨 绝对禁止简化和绕过问题（2025-07-13 强制规范）

### ❌ 严禁任何形式的简化方案

- 不允许为了快速完成任务而简化业务逻辑
- 不允许绕过技术问题采用临时解决方案
- 不允许忽略产品文档中的具体要求
- 不允许降低企业级技术标准

### ✅ 问题解决强制流程

1. **遇到问题立即停止**: 发现任何技术问题、业务逻辑不清楚、实现方案不确定时，必须立即停止当前工作
2. **查阅相关文档**: 比如先阅读 v0.5.0 产品文档中的相关内容
3. **理解业务本质**: 深入理解业务功能的本质和具体实现要求
4. **彻底解决问题**: 找到问题的根本原因并彻底解决，不允许使用变通方案
5. **寻求人类指导**: 如果文档无法解决问题或存在理解困难，必须主动停下来请求人类提供补充说明

## 🤖 Claude 开发助手约束机制

### 📋 开发前必须检查清单

在开始任何开发任务前，Claude 必须确认：

- ✅ 是否已充分理解业务需求和技术要求
- ✅ 是否已设计完整的技术方案（包括架构、数据库、API 设计）
- ✅ 是否已考虑安全性、性能、可扩展性
- ✅ 是否已规划完整的测试策略
- ✅ 是否已明确代码质量标准和验收标准
- ✅ 是否已创建包含完整流程的 TodoList

### ❌ Claude 禁止的行为

- 为了快速完成而跳过测试编写
- 为了简化而忽略错误处理
- 为了方便而硬编码配置
- 为了省事而不写注释
- 为了减少工作量而简化架构设计
- 为了避免复杂而忽略安全考虑
- 为了快速实现而不考虑性能
- 技术开发完成后不执行 Git 提交和文档更新流程

### 🔄 Claude 质量自检流程

每完成一个功能模块，Claude 必须进行自检：

1. **代码质量检查**: 执行所有质量检查命令
2. **类型安全检查**: 确保无 any 类型使用，类型定义完整
3. **架构设计检查**: 模块是否符合企业级架构标准
4. **性能检查**: 是否有性能瓶颈和优化空间
5. **安全检查**: 是否有安全漏洞和风险点
6. **文档检查**: 注释和文档是否完整准确

### 📊 Claude 开发质量承诺

Claude 承诺在开发过程中：

- 始终以企业级标准为基准
- 永远不为了进度而牺牲质量
- 主动识别和解决技术债务
- 持续优化代码质量和架构设计
- 确保每个交付物都达到生产环境标准

### 🔄 新会话 Phase 工作启动规范

**重要**: 新会话开始新 Phase 工作前必须按顺序阅读以下文件

**📖 必读文件清单（按顺序）**:

1. **全局文档机制** - `README.md`（根目录导航文档）
2. **项目规范与配置** - `CLAUDE.md`（本文档）
3. **当前项目状态** - `项目文档/README.md`
4. **功能需求文档** - `产品文档/README.md`
5. **Git 提交历史** - 运行`git log --oneline -5`和`git status`

**🎯 启动检查流程**:

```bash
# 1. 确认开发环境
npm run env:check

# 2. 开始Phase开发工作
```

---

## 📚 项目文档精炼管理机制（2025-07-14 强制规范）

### 🎯 文档精炼原则

**❌ 绝对禁止**: 创建重复、冗余、功能重叠的文档  
**✅ 强制执行**: 文档必须精炼、准确、无冗余，避免项目文档臃肿

### 📋 文档创建约束机制

#### 🚨 创建前强制检查清单
在创建任何新文档前，Claude必须执行以下检查：

1. **📋 重复性检查**: 是否已存在功能相同或重叠的文档
2. **🔗 合并可能性**: 新内容是否可以合并到现有文档
3. **📝 必要性验证**: 是否确实需要独立文档
4. **📊 维护成本评估**: 是否会增加不必要的维护负担

#### ❌ 严禁的文档创建行为
- **重复主题文档**: 同一主题创建多个文档
- **临时文档**: 创建.backup、.temp、.draft等临时文档
- **测试文档**: 创建多个测试方案、指南文档
- **版本文档**: 创建v1.0、v2.0等版本化文档
- **分散文档**: 将统一主题拆分成多个小文档

#### ✅ 推荐的文档管理方式
- **合并优先**: 优先将新内容合并到现有相关文档
- **统一主题**: 同类型内容统一在一个综合文档中
- **及时清理**: 文档合并后立即删除冗余文档
- **定期精简**: 定期审查和精简项目文档

### 🔄 文档合并和精简流程

#### 📋 发现冗余文档时的处理流程
1. **立即分析**: 评估文档间的重叠程度
2. **制定合并方案**: 确定保留哪个文档作为主文档
3. **内容整合**: 将有价值内容合并到主文档
4. **更新导航**: 更新README.md中的文档链接
5. **删除冗余**: 立即删除不需要的文档（不保留备份）

#### 🎯 文档精简标准
- **一个主题一个文档**: 每个主题只保留一个综合文档
- **内容完整性**: 合并后的文档必须涵盖所有重要信息
- **导航清晰**: README导航表格清晰指向唯一文档
- **维护简单**: 减少多文档维护的复杂性

### 📋 README文档管理中心原则

#### 🎯 README双重职能
**README.md不仅是文档导航，更是项目文档管理的中心**

1. **📁 导航功能**: 提供所有项目文档的入口和索引
2. **📊 管理功能**: 记录文档变更、关系说明、重要总结

#### ✅ README必须包含的管理信息
- **文档导航表格**: 所有文档的链接、描述、适用场景
- **文档使用指南**: 不同角色如何使用项目文档
- **文档变更记录**: 重要的文档合并、删除、重构说明
- **项目状态总结**: 当前开发状态、模块完成度、质量指标

#### ❌ 严禁为管理而创建独立文档
- **禁止**: 创建专门的文档总结、变更记录、管理文档
- **原则**: 所有文档管理信息统一在README中维护
- **要求**: README必须承担项目文档的统一管理职责

### 📊 README导航实时性原则

**❌ 绝对禁止**: 文档变更后不同步更新README导航  
**✅ 强制执行**: 文档新增、删除、重大修改后必须立即同步README

### 📋 强制同步触发条件

以下情况必须检查并更新相关README文档：

1. **新增文档文件** - 立即添加到README导航表格
2. **删除文档文件** - 立即从README导航中移除
3. **文档重命名** - 立即更新README中的链接和描述
4. **文档功能重大变更** - 更新README中的描述和适用场景
5. **文档目录结构调整** - 更新所有相关README的路径引用

### 🔄 同步检查清单

每次文档变更后，必须检查以下README文件：

#### 项目级README
- `项目根目录/README.md` - 全局项目导航
- `AppServe/README.md` - 后端文档导航  
- `AppUI/README.md` - 前端文档导航
- `项目文档/README.md` - 项目管理文档导航
- `产品文档/README.md` - 产品需求文档导航

#### 检查项目
- [ ] **链接有效性** - 所有文档链接能够正确访问
- [ ] **描述准确性** - 文档描述与实际内容匹配
- [ ] **分类合理性** - 文档在正确的分类和位置
- [ ] **路径正确性** - 相对路径和绝对路径正确
- [ ] **适用场景** - 使用场景描述准确完整

### ⚡ 强制执行流程

```typescript
// Claude开发助手必须执行的检查流程
interface DocumentSyncProcess {
  // 1. 文档变更检测
  detectChanges(): {
    newFiles: string[];
    deletedFiles: string[];  
    modifiedFiles: string[];
    renamedFiles: { old: string; new: string }[];
  };
  
  // 2. 影响范围分析
  analyzeImpact(changes: DocumentChanges): {
    affectedReadmes: string[];
    requiredUpdates: ReadmeUpdate[];
  };
  
  // 3. 强制同步更新
  updateReadmes(updates: ReadmeUpdate[]): void;
  
  // 4. 验证链接有效性
  validateLinks(readmeFiles: string[]): LinkValidationResult;
}
```

### 📝 README更新标准格式

#### 导航表格标准格式
```markdown
| 文档 | 描述 | 适用场景 |
|------|------|----------|
| 📱 [**阿里云短信配置**](docs/ALIYUN_SMS_SETUP.md) | 短信服务集成、VIP账号、智能路由配置 | 短信功能配置、真实短信测试 |
```

#### 使用指南更新标准
```markdown
7. **📱 短信功能** → 配置 [阿里云短信服务](docs/ALIYUN_SMS_SETUP.md)
```

### 🚨 违规处理机制

**轻微违规** (链接失效、描述不准确):
- 立即修复并记录到开发日志
- 完善检查流程避免重复发生

**严重违规** (新文档未添加导航、重大变更未同步):
- 立即停止当前工作
- 完成所有README同步后方可继续
- 加强后续文档变更的检查机制

### 🔍 自动化检查机制

建议实施的自动化检查：
```bash
# Git提交前自动检查
npm run docs:validate-links
npm run docs:check-readme-sync
npm run docs:lint-navigation
```

### ✅ Claude助手强制约束

Claude开发助手在任何文档操作后必须：

1. **立即检查** - 识别受影响的README文件
2. **主动更新** - 更新所有相关README导航
3. **验证链接** - 确保所有链接有效可访问  
4. **确认描述** - 确保描述准确反映文档内容
5. **报告完成** - 明确告知用户已完成README同步

**示例执行报告**:
```
✅ 已完成文档同步更新：
- 新增文档：docs/ALIYUN_SMS_SETUP.md
- 更新README：AppServe/README.md (添加阿里云短信配置导航)
- 验证链接：所有链接有效
- 更新描述：短信服务集成、VIP账号、智能路由配置
```

---

**最终目标**: 确保每一行代码都达到企业级标准，为项目的长期成功奠定坚实基础

**规范执行**: YGS 开发团队  
**质量监督**: 项目管理与技术架构团队
