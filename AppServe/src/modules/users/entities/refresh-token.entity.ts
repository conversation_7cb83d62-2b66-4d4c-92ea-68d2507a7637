import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  JoinColumn,
} from "typeorm";
import { User } from "./user.entity";

/**
 * 刷新令牌实体
 * 用于JWT令牌轮换机制，提供企业级会话管理
 */
@Entity("refresh_tokens")
@Index(["userId", "isActive"]) // 用户活跃token查询
@Index(["deviceFingerprint"]) // 设备查询
@Index(["expiresAt"]) // 过期时间查询
export class RefreshToken {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ name: "user_id", comment: "用户ID" })
  userId: string;

  @Column({ name: "token_hash", length: 255, comment: "令牌哈希值" })
  tokenHash: string;

  @Column({
    name: "device_fingerprint",
    length: 255,
    nullable: true,
    comment: "设备指纹",
  })
  deviceFingerprint: string;

  @Column({
    name: "device_info",
    type: "jsonb",
    nullable: true,
    comment: "设备信息",
  })
  deviceInfo: {
    userAgent?: string;
    platform?: string;
    version?: string;
    ip?: string;
  };

  @Column({ name: "expires_at", type: "timestamp", comment: "过期时间" })
  expiresAt: Date;

  @CreateDateColumn({ name: "created_at", comment: "创建时间" })
  createdAt: Date;

  @UpdateDateColumn({ name: "last_used_at", comment: "最后使用时间" })
  lastUsedAt: Date;

  @Column({ name: "is_active", default: true, comment: "是否激活" })
  isActive: boolean;

  // 关联关系
  @ManyToOne(() => User, (user) => user.refreshTokens, { onDelete: "CASCADE" })
  @JoinColumn({ name: "user_id" })
  user: User;

  // 业务方法
  isExpired(): boolean {
    return this.expiresAt < new Date();
  }

  isValid(): boolean {
    return this.isActive && !this.isExpired();
  }
}
