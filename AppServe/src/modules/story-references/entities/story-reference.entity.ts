import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  Unique,
} from "typeorm";
import { User } from "../../users/entities/user.entity";
import { Story } from "../../stories/entities/story.entity";
import { ReferenceCollection } from "./reference-collection.entity";

export enum ReferenceStatus {
  ACTIVE = "active",
  ARCHIVED = "archived",
  DELETED = "deleted",
}

export enum ReferenceVisibility {
  PUBLIC = "public",
  FRIENDS_ONLY = "friends_only",
  PRIVATE = "private",
}

/**
 * 故事引用实体
 * 用户对故事的个人引用和评注
 */
@Entity("story_references")
@Unique(["userId", "storyId"])
@Index(["userId", "status", "visibility"])
@Index(["collectionId", "status"])
@Index(["storyId", "visibility"])
@Index(["createdAt"])
export class StoryReference {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({
    type: "uuid",
    comment: "用户ID",
  })
  userId: string;

  @Column({
    type: "uuid",
    comment: "故事ID",
  })
  storyId: string;

  @Column({
    type: "uuid",
    nullable: true,
    comment: "引用集合ID",
  })
  collectionId: string;

  @Column({
    type: "varchar",
    length: 200,
    comment: "引用标题（可自定义）",
  })
  title: string;

  @Column({
    type: "text",
    nullable: true,
    comment: "个人评注",
  })
  annotation: string;

  @Column({
    type: "json",
    nullable: true,
    comment: "引用的具体段落或片段",
  })
  quotedSegments: Array<{
    chapterId?: string;
    startPosition: number;
    endPosition: number;
    text: string;
    note?: string;
  }>;

  @Column({
    type: "enum",
    enum: ReferenceStatus,
    default: ReferenceStatus.ACTIVE,
    comment: "引用状态",
  })
  status: ReferenceStatus;

  @Column({
    type: "enum",
    enum: ReferenceVisibility,
    default: ReferenceVisibility.PRIVATE,
    comment: "可见性设置",
  })
  visibility: ReferenceVisibility;

  @Column({
    type: "simple-array",
    nullable: true,
    comment: "个人标签",
  })
  tags: string[];

  @Column({
    type: "int",
    default: 0,
    comment: "引用重要性评分（1-5）",
  })
  importance: number;

  @Column({
    type: "int",
    default: 0,
    comment: "点赞数",
  })
  likesCount: number;

  @Column({
    type: "int",
    default: 0,
    comment: "评论数",
  })
  commentsCount: number;

  @Column({
    type: "jsonb",
    nullable: true,
    comment: "引用时的故事快照",
  })
  storySnapshot: {
    title: string;
    summary?: string;
    authorId: string;
    authorName: string;
    publishedAt?: Date;
    chapterCount?: number;
  };

  @Column({
    type: "timestamp with time zone",
    nullable: true,
    comment: "最后阅读时间",
  })
  lastReadAt: Date;

  @Column({
    type: "int",
    default: 0,
    comment: "阅读次数",
  })
  readCount: number;

  @Column({
    type: "jsonb",
    nullable: true,
    comment: "扩展字段",
  })
  metadata: Record<string, unknown>;

  @CreateDateColumn({
    type: "timestamp with time zone",
    comment: "引用创建时间",
  })
  createdAt: Date;

  @UpdateDateColumn({
    type: "timestamp with time zone",
    comment: "更新时间",
  })
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => User, { eager: false })
  @JoinColumn({ name: "userId" })
  user: User;

  @ManyToOne(() => Story, { eager: false })
  @JoinColumn({ name: "storyId" })
  story: Story;

  @ManyToOne(() => ReferenceCollection, { eager: false })
  @JoinColumn({ name: "collectionId" })
  collection?: ReferenceCollection;
}
