import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// YGS设计系统 - 间距令牌
/// 基于Figma设计规范v1.0.0，使用flutter_screenutil响应式适配
class YgsSpacing {
  YgsSpacing._(); // 防止实例化

  // ========== 基础间距尺寸 ==========
  /// 超小间距 - 4.h
  static double get xxs => 4.h;
  
  /// 极小间距 - 8.h
  static double get xs => 8.h;
  
  /// 小间距 - 12.h
  static double get sm => 12.h;
  
  /// 中间距 - 16.h
  static double get md => 16.h;
  
  /// 大间距 - 24.h
  static double get lg => 24.h;
  
  /// 特大间距 - 32.h
  static double get xl => 32.h;
  
  /// 超大间距 - 48.h
  static double get xxl => 48.h;
  
  /// 巨大间距 - 64.h
  static double get xxxl => 64.h;

  // ========== 垂直间距组件 ==========
  /// 超小垂直间距
  static SizedBox get verticalSpaceXxs => SizedBox(height: xxs);
  
  /// 极小垂直间距
  static SizedBox get verticalSpaceXs => SizedBox(height: xs);
  
  /// 小垂直间距
  static SizedBox get verticalSpaceSm => SizedBox(height: sm);
  
  /// 中垂直间距
  static SizedBox get verticalSpaceMd => SizedBox(height: md);
  
  /// 大垂直间距
  static SizedBox get verticalSpaceLg => SizedBox(height: lg);
  
  /// 特大垂直间距
  static SizedBox get verticalSpaceXl => SizedBox(height: xl);
  
  /// 超大垂直间距
  static SizedBox get verticalSpaceXxl => SizedBox(height: xxl);
  
  /// 巨大垂直间距
  static SizedBox get verticalSpaceXxxl => SizedBox(height: xxxl);

  // ========== 水平间距组件 ==========
  /// 超小水平间距
  static SizedBox get horizontalSpaceXxs => SizedBox(width: xxs.w);
  
  /// 极小水平间距
  static SizedBox get horizontalSpaceXs => SizedBox(width: xs.w);
  
  /// 小水平间距
  static SizedBox get horizontalSpaceSm => SizedBox(width: sm.w);
  
  /// 中水平间距
  static SizedBox get horizontalSpaceMd => SizedBox(width: md.w);
  
  /// 大水平间距
  static SizedBox get horizontalSpaceLg => SizedBox(width: lg.w);
  
  /// 特大水平间距
  static SizedBox get horizontalSpaceXl => SizedBox(width: xl.w);
  
  /// 超大水平间距
  static SizedBox get horizontalSpaceXxl => SizedBox(width: xxl.w);
  
  /// 巨大水平间距
  static SizedBox get horizontalSpaceXxxl => SizedBox(width: xxxl.w);

  // ========== 内边距 EdgeInsets ==========
  /// 全方向相等内边距
  static EdgeInsets paddingAll(double value) => EdgeInsets.all(value);
  
  /// 水平内边距
  static EdgeInsets paddingHorizontal(double value) => 
      EdgeInsets.symmetric(horizontal: value);
  
  /// 垂直内边距
  static EdgeInsets paddingVertical(double value) => 
      EdgeInsets.symmetric(vertical: value);
  
  /// 对称内边距
  static EdgeInsets paddingSymmetric({
    double horizontal = 0,
    double vertical = 0,
  }) => EdgeInsets.symmetric(
        horizontal: horizontal,
        vertical: vertical,
      );

  // ========== 常用内边距预设 ==========
  /// 小内边距 - 8.w
  static EdgeInsets get paddingSmall => EdgeInsets.all(xs);
  
  /// 中内边距 - 16.w
  static EdgeInsets get paddingMedium => EdgeInsets.all(md);
  
  /// 大内边距 - 24.w
  static EdgeInsets get paddingLarge => EdgeInsets.all(lg);
  
  /// 页面内边距 - 24.w 水平
  static EdgeInsets get paddingPage => EdgeInsets.symmetric(horizontal: lg);
  
  /// 卡片内边距 - 20.w
  static EdgeInsets get paddingCard => EdgeInsets.all(20.w);
  
  /// 按钮内边距 - 20.w 水平, 16.h 垂直
  static EdgeInsets get paddingButton => EdgeInsets.symmetric(
        horizontal: 20.w,
        vertical: md,
      );

  // ========== 圆角半径 ==========
  /// 小圆角 - 8.r
  static double get radiusSmall => 8.r;
  
  /// 中圆角 - 12.r
  static double get radiusMedium => 12.r;
  
  /// 大圆角 - 16.r
  static double get radiusLarge => 16.r;
  
  /// 特大圆角 - 24.r
  static double get radiusXLarge => 24.r;
  
  /// 圆形圆角 - 100.r
  static double get radiusCircle => 100.r;

  // ========== 边框圆角 BorderRadius ==========
  /// 小边框圆角
  static BorderRadius get borderRadiusSmall => 
      BorderRadius.circular(radiusSmall);
  
  /// 中边框圆角
  static BorderRadius get borderRadiusMedium => 
      BorderRadius.circular(radiusMedium);
  
  /// 大边框圆角
  static BorderRadius get borderRadiusLarge => 
      BorderRadius.circular(radiusLarge);
  
  /// 特大边框圆角
  static BorderRadius get borderRadiusXLarge => 
      BorderRadius.circular(radiusXLarge);
  
  /// 圆形边框圆角
  static BorderRadius get borderRadiusCircle => 
      BorderRadius.circular(radiusCircle);

  // ========== 特定组件尺寸 ==========
  /// 输入框高度 - 80.h
  static double get inputHeight => 80.h;
  
  /// 按钮高度 - 70.h
  static double get buttonHeight => 70.h;
  
  /// 中等按钮高度 - 50.h
  static double get buttonHeightMd => 50.h;
  
  /// Tab栏高度 - 168.h
  static double get tabBarHeight => 168.h;
  
  /// App Bar高度 - 88.h
  static double get appBarHeight => 88.h;
  
  /// 卡片边框圆角 - 16.r
  static double get cardBorderRadius => 16.r;
  
  /// 按钮边框圆角 - 16.r
  static double get buttonBorderRadius => 16.r;

  // ========== 安全区域 ==========
  /// 全方向安全区域内边距
  static EdgeInsets safeAreaAll(double value) => EdgeInsets.all(value);

  // ========== 图标尺寸 ==========
  /// 小图标 - 20.w
  static double get iconSm => 20.w;
  
  /// 中图标 - 24.w
  static double get iconMd => 24.w;
  
  /// 大图标 - 32.w
  static double get iconLg => 32.w;
  
  /// 超大图标 - 40.w
  static double get iconXl => 40.w;
  
  /// Tab图标 - 40.w
  static double get iconTab => 40.w;

  // ========== 内容宽度限制 ==========
  /// 最大内容宽度 - 800.w
  static double get maxContentWidth => 800.w;
  
  /// 输入框标准宽度 - 600.w
  static double get inputStandardWidth => 600.w;
  
  /// 验证码框宽度 - 80.w
  static double get codeInputWidth => 80.w;
  
  /// 协议文本宽度 - 555.w
  static double get agreementTextWidth => 555.w;
}