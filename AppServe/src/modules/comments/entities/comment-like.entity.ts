import {
  En<PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  Index,
  Unique,
} from "typeorm";
import { User } from "../../users/entities/user.entity";
import { Comment } from "./comment.entity";

/**
 * 评论点赞实体
 * 记录用户对评论的点赞行为
 */
@Entity("comment_likes")
@Unique(["commentId", "userId"])
@Index(["userId", "createdAt"])
@Index(["commentId"])
export class CommentLike {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({
    type: "uuid",
    comment: "评论ID",
  })
  commentId: string;

  @Column({
    type: "uuid",
    comment: "点赞用户ID",
  })
  userId: string;

  @CreateDateColumn({
    type: "timestamp with time zone",
    comment: "点赞时间",
  })
  createdAt: Date;

  // 关联关系
  @ManyToOne(() => Comment, { eager: false })
  @JoinColumn({ name: "commentId" })
  comment: Comment;

  @ManyToOne(() => User, { eager: false })
  @JoinColumn({ name: "userId" })
  user: User;
}
