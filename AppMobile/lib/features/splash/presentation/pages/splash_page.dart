import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import '../../../../app/routes/app_router.dart';
import '../../../../design_system/tokens/ygs_colors.dart';
import '../../../../design_system/tokens/ygs_spacing.dart';
import '../../../../design_system/tokens/ygs_typography.dart';

/// 启动页 - 基于Figma设计
class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage>
    with SingleTickerProviderStateMixin {
  // 本地 SVG 资源路径
  static const String logoAssetPath = 'assets/images/splash/logo.svg';
  static const String textAssetPath = 'assets/images/splash/text.svg';

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  bool _logoLoaded = false;
  bool _textLoaded = false;

  @override
  void initState() {
    super.initState();

    // 初始化动画控制器
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // 淡入动画
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeIn),
    ));

    // 缩放动画
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOutCubic),
    ));

    // 预加载 SVG 资源
    _preloadAssets();
  }

  Future<void> _preloadAssets() async {
    try {
      // 1. 预加载Logo SVG资源
      await _preloadSvgAsset(logoAssetPath);
      if (mounted) {
        setState(() {
          _logoLoaded = true;
        });
      }

      // 2. 预加载文字SVG资源
      await _preloadSvgAsset(textAssetPath);
      if (mounted) {
        setState(() {
          _textLoaded = true;
        });
      }

      // 3. 并行执行应用初始化任务
      await Future.wait([
        _initializeApp(),
        _checkAppVersion(),
        _preloadCriticalData(),
      ]);

      // 4. 资源加载完成后开始动画
      if (_logoLoaded && _textLoaded) {
        _animationController.forward();

        // 5. 保证最小展示时间3秒（行业标准）
        await Future.delayed(const Duration(milliseconds: 3000));

        // 6. 跳转到下一页面
        if (mounted) {
          context.go(AppPaths.login);
        }
      }
    } catch (e) {
      // 错误处理：显示友好错误信息
      debugPrint('启动页初始化失败: $e');
      await _handleInitializationError(e);
    }
  }

  /// 预加载SVG资源 - 企业级实现
  Future<void> _preloadSvgAsset(String assetPath) async {
    try {
      // 1. 预加载SVG字符串到内存缓存
      final String svgString = await rootBundle.loadString(assetPath);

      // 2. 验证SVG内容完整性和格式
      if (svgString.isEmpty || !svgString.contains('<svg')) {
        throw Exception('SVG内容无效或损坏: $assetPath');
      }

      // 3. 预验证SVG可解析性 - 创建临时实例触发解析
      // flutter_svg在创建SvgPicture时会进行SVG解析，这会缓存解析结果
      SvgPicture.string(
        svgString,
        width: 1.0,
        height: 1.0,
        allowDrawingOutsideViewBox: true,
      );

      // 4. 记录预加载成功，SVG已在flutter_svg内部缓存中
      debugPrint('SVG预加载成功(字符串+解析验证): $assetPath');

      // 5. 额外的性能优化：模拟首次构建过程
      await Future.delayed(const Duration(milliseconds: 1)); // 确保解析完成
    } catch (e) {
      debugPrint('SVG企业级预加载失败: $e，使用降级方案');

      // 降级方案1: 仅预加载字符串
      try {
        await rootBundle.loadString(assetPath);
        debugPrint('SVG字符串预加载成功(降级): $assetPath');
      } catch (fallbackError) {
        debugPrint('SVG所有预加载方案均失败: $fallbackError');
        // 最终保障：运行时加载，虽然可能有轻微延迟但不会崩溃
      }
    }
  }

  /// 应用初始化
  Future<void> _initializeApp() async {
    // 初始化关键服务
    await Future.delayed(const Duration(milliseconds: 200)); // 模拟初始化时间
    debugPrint('应用服务初始化完成');
  }

  /// 检查应用版本
  Future<void> _checkAppVersion() async {
    // 检查是否需要更新
    await Future.delayed(const Duration(milliseconds: 100)); // 模拟版本检查
    debugPrint('版本检查完成');
  }

  /// 预加载关键数据
  Future<void> _preloadCriticalData() async {
    // 预加载用户配置、主题等关键数据
    await Future.delayed(const Duration(milliseconds: 150)); // 模拟数据加载
    debugPrint('关键数据预加载完成');
  }

  /// 初始化错误处理
  Future<void> _handleInitializationError(dynamic error) async {
    // 显示错误信息并提供重试选项
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('应用初始化失败，请检查网络连接'),
          action: SnackBarAction(
            label: '重试',
            onPressed: () => _preloadAssets(),
          ),
        ),
      );

      // 延迟后仍然跳转，避免用户卡在启动页
      await Future.delayed(const Duration(seconds: 2));
      if (mounted) {
        context.go(AppPaths.login);
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: YgsColors.backgroundSecondary, // 使用YGS设计令牌
      body: (_logoLoaded && _textLoaded)
          ? Container(
              width: double.infinity,
              height: double.infinity,
              padding: YgsSpacing.safeAreaAll(YgsSpacing.lg), // 使用YGS安全区域间距
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // 上方文字 - 使用本地 SVG（往上移动）
                  Positioned(
                    top: YgsSpacing.xxxl * 6, // 从8减少到6，往上移动：64.w * 6 = 384.w
                    child: AnimatedBuilder(
                      animation: _animationController,
                      builder: (context, child) {
                        return FadeTransition(
                          opacity: _fadeAnimation,
                          child: Container(
                            width: YgsSpacing.maxContentWidth * 0.43, // 345.w 相当于 800.w * 0.43
                            height: YgsSpacing.buttonHeightMd, // 48.h - 使用标准按钮高度
                            child: SvgPicture.asset(
                              textAssetPath,
                              fit: BoxFit.contain,
                            ),
                          ),
                        );
                      },
                    ),
                  ),

                  // 下方Logo - 使用YGS SVG Logo（往下移动）
                  Positioned(
                    bottom: YgsSpacing.xxxl * 3, // 从4.375减少到3，往下移动：64.w * 3 = 192.w
                    child: AnimatedBuilder(
                      animation: _animationController,
                      builder: (context, child) {
                        return FadeTransition(
                          opacity: _fadeAnimation,
                          child: ScaleTransition(
                            scale: _scaleAnimation,
                            child: SizedBox(
                              width: 120.w, // 缩小logo尺寸与登录页保持一致
                              height: 120.w,
                              child: SvgPicture.asset(
                                logoAssetPath,
                                width: 120.w,
                                height: 120.w,
                                fit: BoxFit.contain,
                                placeholderBuilder: (context) => Container(
                                  width: 120.w,
                                  height: 120.w,
                                  decoration: BoxDecoration(
                                    gradient: YgsColors.brandGradient,
                                    borderRadius: BorderRadius.circular(25.r),
                                    boxShadow: YgsColors.elevatedShadow,
                                  ),
                                  child: Center(
                                    child: Column(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Icon(
                                          Icons.auto_stories,
                                          size: 48.w,
                                          color: YgsColors.onBrandPrimary,
                                        ),
                                        SizedBox(height: 4.h),
                                        Text(
                                          'YGS',
                                          style: YgsTypography.bodyMedium.copyWith(
                                            fontSize: 24.sp, // 放大字体从20.sp到24.sp
                                            fontWeight: FontWeight.bold,
                                            color: YgsColors.onBrandPrimary,
                                            letterSpacing: 2,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            )
          : Center(
              // 加载中显示简单的进度指示器
              child: CircularProgressIndicator(
                color: YgsColors.brandPrimary, // 使用YGS品牌色
                strokeWidth: 2.w,
              ),
            ),
    );
  }
}
