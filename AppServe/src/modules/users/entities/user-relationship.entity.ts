import {
  <PERSON><PERSON><PERSON>,
  PrimaryGenerated<PERSON><PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON>reateDate<PERSON><PERSON><PERSON>n,
  ManyToOne,
  <PERSON>in<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from "typeorm";
import { User } from "./user.entity";
import { Character } from "../../characters/entities/character.entity";
import { CharacterLighting } from "../../characters/entities/character-lighting.entity";

export enum RelationshipStatus {
  CONFIRMED = 1,
  CANCELLED = 2,
}

@Entity("user_relationships")
@Unique(["userId", "relatedUserId", "characterId"])
export class UserRelationship {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ name: "user_id" })
  userId: string;

  @Column({ name: "related_user_id" })
  relatedUserId: string;

  @Column({ name: "character_id", nullable: true })
  characterId: string;

  @Column({ name: "lighting_id", nullable: true })
  lightingId: string;

  @Column({
    name: "relation_type",
    length: 20,
    nullable: true,
    comment: "关系类型：family, friend, colleague等",
  })
  relationType: string;

  @Column({
    name: "intimacy_level",
    type: "smallint",
    default: 1,
    comment: "亲密度等级：1-5，控制访问权限",
  })
  intimacyLevel: number;

  @Column({
    type: "smallint",
    default: RelationshipStatus.CONFIRMED,
    comment: "状态：1已确认 2已取消",
  })
  status: RelationshipStatus;

  @CreateDateColumn({ name: "created_at", comment: "创建时间" })
  createdAt: Date;

  // 关联关系
  @ManyToOne(() => User, (user) => user.relationships, { onDelete: "CASCADE" })
  @JoinColumn({ name: "user_id" })
  user: User;

  @ManyToOne(() => User, (user) => user.relatedRelationships, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "related_user_id" })
  relatedUser: User;

  @ManyToOne(() => Character, { nullable: true })
  @JoinColumn({ name: "character_id" })
  character: Character;

  @ManyToOne(() => CharacterLighting, { nullable: true })
  @JoinColumn({ name: "lighting_id" })
  lighting: CharacterLighting;
}
