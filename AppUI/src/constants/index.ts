/**
 * 有故事 APP - 常量定义
 * 
 * 包含：
 * - 主题配置
 * - 颜色定义
 * - 字体配置
 * - 尺寸规范
 */

// ===== 颜色系统 =====
export const COLORS = {
  // 主色调
  primary: '#007AFF',
  primaryDark: '#0056B3',
  primaryLight: '#66B2FF',
  
  // 次要色调
  secondary: '#5856D6',
  secondaryDark: '#3C3AA3',
  secondaryLight: '#8987E9',
  
  // 状态颜色
  success: '#34C759',
  warning: '#FF9500',
  error: '#FF3B30',
  info: '#007AFF',
  
  // 中性色
  background: '#FFFFFF',
  surface: '#F2F2F7',
  card: '#FFFFFF',
  
  // 文本颜色
  text: '#000000',
  textSecondary: '#6D6D80',
  textTertiary: '#C7C7CC',
  textInverse: '#FFFFFF',
  
  // 边框和分割线
  border: '#C6C6C8',
  separator: '#E5E5EA',
  
  // 透明度变化
  overlay: 'rgba(0, 0, 0, 0.5)',
} as const;

// ===== 字体系统 =====
export const FONTS = {
  sizes: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 30,
    '4xl': 36,
  },
  weights: {
    light: '300' as const,
    normal: '400' as const,
    medium: '500' as const,
    semibold: '600' as const,
    bold: '700' as const,
  },
  families: {
    system: 'System',
    mono: 'Menlo',
  },
} as const;

// ===== 间距系统 =====
export const SPACING = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  '2xl': 40,
  '3xl': 48,
  '4xl': 64,
} as const;

// ===== 圆角系统 =====
export const RADIUS = {
  none: 0,
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  '2xl': 24,
  full: 9999,
} as const;

// ===== 阴影系统 =====
export const SHADOWS = {
  none: {
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  sm: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 4,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 8,
  },
} as const;

// ===== 主题对象 =====
export const THEME = {
  colors: COLORS,
  fonts: FONTS,
  spacing: SPACING,
  radius: RADIUS,
  shadows: SHADOWS,
} as const;

// ===== 类型导出 =====
export type Theme = typeof THEME;
export type Colors = typeof COLORS;
export type Fonts = typeof FONTS;
export type Spacing = typeof SPACING;
export type Radius = typeof RADIUS;
export type Shadows = typeof SHADOWS; 