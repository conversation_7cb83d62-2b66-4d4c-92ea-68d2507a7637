import type {
  ReportType,
  ReportReason,
  ReportStatus,
  ReportSeverity,
} from "../entities/report.entity";

/**
 * 举报响应DTO
 */
export class ReportResponseDto {
  id: string;
  reportType: ReportType;
  targetId: string;
  reporterId: string;
  reporterName: string;
  reason: ReportReason;
  description?: string;
  status: ReportStatus;
  severity: ReportSeverity;
  reviewerId?: string;
  reviewerName?: string;
  reviewNote?: string;
  reviewedAt?: Date;
  contentSnapshot?: {
    title?: string;
    content?: string;
    authorName?: string;
    url?: string;
  };
  metadata?: Record<string, unknown>;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 举报统计响应DTO
 */
export class ReportStatsResponseDto {
  totalReports: number;
  pendingReports: number;
  underReviewReports: number;
  resolvedReports: number;
  rejectedReports: number;
  escalatedReports: number;

  // 按类型统计
  storyReports: number;
  commentReports: number;
  userReports: number;

  // 按原因统计
  reasonStats: Array<{
    reason: ReportReason;
    count: number;
    percentage: number;
  }>;

  // 按严重程度统计
  severityStats: Array<{
    severity: ReportSeverity;
    count: number;
    percentage: number;
  }>;

  // 处理效率统计
  avgProcessingTime: number; // 小时
  todayReports: number;
  thisWeekReports: number;
  thisMonthReports: number;
}
