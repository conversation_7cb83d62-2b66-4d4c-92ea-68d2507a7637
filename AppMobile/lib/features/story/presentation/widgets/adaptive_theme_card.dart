import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/services/dynamic_color_service.dart';
import '../../../../design_system/tokens/ygs_colors.dart';
import '../../../../design_system/tokens/ygs_typography.dart';

/// 自适应主题卡片 - 基于封面图像动态生成遮罩颜色
/// 实现网易云音乐级别的动态色彩体验
class AdaptiveThemeCard extends ConsumerStatefulWidget {
  final String title;
  final String subtitle;
  final String iconPath;
  final String backgroundImagePath;
  final VoidCallback? onTap;

  const AdaptiveThemeCard({
    super.key,
    required this.title,
    required this.subtitle,
    required this.iconPath,
    required this.backgroundImagePath,
    this.onTap,
  });

  @override
  ConsumerState<AdaptiveThemeCard> createState() => _AdaptiveThemeCardState();
}

class _AdaptiveThemeCardState extends ConsumerState<AdaptiveThemeCard> {
  final DynamicColorService _colorService = DynamicColorService();
  DynamicColorState _colorState = const DynamicColorState(isLoading: true);

  @override
  void initState() {
    super.initState();
    _extractDynamicColors();
  }

  /// 异步提取动态颜色
  Future<void> _extractDynamicColors() async {
    try {
      setState(() {
        _colorState = _colorState.copyWith(isLoading: true, error: null);
      });

      // 从背景图像提取主色
      final imageProvider = AssetImage(widget.backgroundImagePath);
      final colorScheme = await _colorService.extractColorSchemeFromImage(
        imageProvider: imageProvider,
        cacheKey: widget.title, // 使用主题标题作为缓存键
        brightness: Brightness.dark, // 暗色主题以确保文字可读性
      );

      // 生成自适应渐变 - 增加遮罩浓度
      final gradientColors = _colorService.generateAdaptiveMaskGradient(
        colorScheme: colorScheme,
        cacheKey: widget.title,
        opacity: 0.8, // 增加遮罩浓度，让背景色彩更明显
      );

      setState(() {
        _colorState = DynamicColorState(
          colorScheme: colorScheme,
          gradientColors: gradientColors,
          isLoading: false,
        );
      });
    } catch (e) {
      setState(() {
        _colorState = _colorState.copyWith(
          isLoading: false,
          error: '主色提取失败: $e',
        );
      });
      debugPrint('AdaptiveThemeCard: 主色提取失败 - ${widget.title}: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      child: Container(
        width: 330.w,
        height: 272.h,
        child: Stack(
          children: [
            // 背景图片层
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16.r),
                  image: DecorationImage(
                    image: AssetImage(widget.backgroundImagePath),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),

            // 动态自适应遮罩层
            Positioned.fill(
              child: _buildAdaptiveMask(),
            ),

            // 主题图标 - 背景色根据主色调整
            Positioned(
              left: 44.w,
              top: 33.h,
              child: _buildAdaptiveIcon(),
            ),

            // 主标题
            Positioned(
              left: 49.w,
              top: 131.h,
              child: Text(
                widget.title,
                style: TextStyle(
                  fontFamily: 'PingFang SC',
                  fontSize: 34.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                  height: 1.0,
                ),
              ),
            ),

            // 副标题
            Positioned(
              left: 44.w,
              top: 193.h,
              child: Text(
                widget.subtitle,
                style: TextStyle(
                  fontFamily: 'PingFang SC',
                  fontSize: 22.sp,
                  fontWeight: FontWeight.w400,
                  color: const Color(0xFFB6B6BD).withValues(alpha: 0.9),
                  height: 1.0,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建自适应遮罩
  Widget _buildAdaptiveMask() {
    if (_colorState.isLoading) {
      // 加载状态：使用默认遮罩
      return Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          color: const Color(0xFF2A2A39).withValues(alpha: 0.5),
        ),
      );
    }

    if (_colorState.error != null || _colorState.gradientColors == null) {
      // 错误状态：使用默认遮罩
      return Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          color: const Color(0xFF2A2A39).withValues(alpha: 0.5),
        ),
      );
    }

    // 成功状态：使用动态渐变遮罩
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.r),
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: _colorState.gradientColors!,
          stops: const [0.0, 0.3, 0.7, 1.0], // 企业级渐变分布
        ),
      ),
    );
  }

  /// 构建透明背景的图标容器
  Widget _buildAdaptiveIcon() {
    return Container(
      width: 88.w,
      height: 88.w,
      decoration: BoxDecoration(
        color: Colors.transparent, // 全透明背景
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: const Color(0xFFFFFFFF).withValues(alpha: 0.3),
          width: 0.5.w,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16.r),
        child: Image.asset(
          widget.iconPath,
          width: 88.w,
          height: 88.w,
          color: const Color(0xFFFFFFFF),
          fit: BoxFit.cover,
          filterQuality: FilterQuality.high,
          errorBuilder: (context, error, stackTrace) {
            return Container(
              width: 88.w,
              height: 88.w,
              color: Colors.transparent, // 错误状态也保持透明
              child: Icon(
                Icons.palette_outlined,
                color: const Color(0xFFFFFFFF),
                size: 72.w,
              ),
            );
          },
        ),
      ),
    );
  }

  @override
  void dispose() {
    // 资源清理 - 企业级内存管理
    super.dispose();
  }
}

/// 动态颜色加载指示器
class DynamicColorLoadingIndicator extends StatelessWidget {
  final double width;
  final double height;

  const DynamicColorLoadingIndicator({
    super.key,
    required this.width,
    required this.height,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.r),
        color: YgsColors.backgroundSecondary,
      ),
      child: Center(
        child: SizedBox(
          width: 24.w,
          height: 24.w,
          child: CircularProgressIndicator(
            strokeWidth: 2.w,
            valueColor: AlwaysStoppedAnimation<Color>(YgsColors.brandPrimary),
          ),
        ),
      ),
    );
  }
}
