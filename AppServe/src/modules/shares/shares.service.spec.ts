/* eslint-disable @typescript-eslint/no-explicit-any, @typescript-eslint/no-unused-vars */
import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import {
  NotFoundException,
  BadRequestException,
  UnauthorizedException,
} from "@nestjs/common";
import type { Repository } from "typeorm";
import { randomBytes } from "crypto";

import { SharesService } from "./shares.service";
import { Share, ShareType, ShareStatus } from "./entities/share.entity";
import { ShareAccessLog } from "./entities/share-access-log.entity";
import { Story } from "../stories/entities/story.entity";
import { User } from "../users/entities/user.entity";
import type { CreateShareDto, ShareAccessDto } from "./dto";

// Mock crypto.randomBytes
jest.mock("crypto", () => ({
  randomBytes: jest.fn(),
}));

describe("SharesService - 企业级单元测试", () => {
  let service: SharesService;
  let mockShareRepository: jest.Mocked<Repository<Share>>;
  let mockShareAccessLogRepository: jest.Mocked<Repository<ShareAccessLog>>;
  let mockStoryRepository: jest.Mocked<Repository<Story>>;
  let mockUserRepository: jest.Mocked<Repository<User>>;

  // 测试数据工厂
  const mockUser = {
    id: "user-001",
    username: "testuser",
    nickname: "测试用户",
    isActive: true,
  };

  const mockStory = {
    id: "story-001",
    title: "测试故事",
    content: "这是一个测试故事的内容",
    userId: "user-001",
    user: mockUser,
    status: "published",
    permissionLevel: "public",
  };

  const mockShare = {
    id: "share-001",
    storyId: "story-001",
    userId: "user-001",
    type: ShareType.PUBLIC,
    status: ShareStatus.ACTIVE,
    shareCode: "ABC123",
    shareUrl: "https://example.com/share/ABC123",
    title: "分享：测试故事",
    description: "这是一个测试故事的内容",
    imageUrl: null,
    accessCount: 10,
    maxAccess: null,
    expiresAt: null,
    settings: {
      allowDownload: false,
      requireLogin: false,
      watermark: true,
    },
    createdAt: new Date(),
    updatedAt: new Date(),
    story: mockStory,
    user: mockUser,
  };

  const mockAccessLog = {
    id: "log-001",
    shareId: "share-001",
    accessorId: "visitor-001",
    ipAddress: "***********",
    userAgent: "Mozilla/5.0",
    referrer: "https://example.com",
    accessedAt: new Date(),
    metadata: { device: "desktop", browser: "chrome" },
  };

  beforeEach(async () => {
    // Mock randomBytes
    (randomBytes as jest.Mock).mockReturnValue(Buffer.from("ABC123"));

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SharesService,
        {
          provide: getRepositoryToken(Share),
          useValue: {
            findOne: jest.fn(),
            save: jest.fn(),
            create: jest.fn(),
            createQueryBuilder: jest.fn().mockReturnValue({
              leftJoinAndSelect: jest.fn().mockReturnThis(),
              where: jest.fn().mockReturnThis(),
              andWhere: jest.fn().mockReturnThis(),
              orderBy: jest.fn().mockReturnThis(),
              skip: jest.fn().mockReturnThis(),
              take: jest.fn().mockReturnThis(),
              getCount: jest.fn(),
              getMany: jest.fn(),
              select: jest.fn().mockReturnThis(),
              getRawOne: jest.fn(),
              getRawMany: jest.fn(),
              groupBy: jest.fn().mockReturnThis(),
            }),
          },
        },
        {
          provide: getRepositoryToken(ShareAccessLog),
          useValue: {
            save: jest.fn(),
            create: jest.fn(),
            createQueryBuilder: jest.fn().mockReturnValue({
              where: jest.fn().mockReturnThis(),
              andWhere: jest.fn().mockReturnThis(),
              select: jest.fn().mockReturnThis(),
              getRawOne: jest.fn(),
              getRawMany: jest.fn(),
              orderBy: jest.fn().mockReturnThis(),
              getMany: jest.fn(),
              getCount: jest.fn(),
            }),
          },
        },
        {
          provide: getRepositoryToken(Story),
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(User),
          useValue: {
            findOne: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<SharesService>(SharesService);
    mockShareRepository = module.get(getRepositoryToken(Share));
    mockShareAccessLogRepository = module.get(
      getRepositoryToken(ShareAccessLog),
    );
    mockStoryRepository = module.get(getRepositoryToken(Story));
    mockUserRepository = module.get(getRepositoryToken(User));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("createShare - 创建分享", () => {
    const createShareDto: CreateShareDto = {
      storyId: "story-001",
      type: ShareType.PUBLIC,
      title: "分享：测试故事",
      description: "这是一个很棒的故事",
      settings: {
        allowDownload: false,
        requireLogin: false,
        watermark: true,
      },
    };

    it("应该成功创建公开分享", async () => {
      // Arrange
      mockStoryRepository.findOne.mockResolvedValue(mockStory as any);
      mockShareRepository.create.mockReturnValue(mockShare as any);
      mockShareRepository.save.mockResolvedValue(mockShare as any);

      // Act
      const result = await service.createShare(createShareDto, "user-001");

      // Assert
      expect(mockStoryRepository.findOne).toHaveBeenCalledWith({
        where: { id: "story-001" },
        relations: ["user"],
      });
      expect(mockShareRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          storyId: "story-001",
          userId: "user-001",
          type: ShareType.PUBLIC,
          shareCode: "ABC123",
          shareUrl: expect.stringContaining("ABC123"),
        }),
      );
      expect(result).toEqual(
        expect.objectContaining({
          id: "share-001",
          shareCode: "ABC123",
          shareUrl: expect.stringContaining("ABC123"),
        }),
      );
    });

    it("应该成功创建私密分享", async () => {
      // Arrange
      const privateShareDto = {
        ...createShareDto,
        type: ShareType.PRIVATE,
        maxAccess: 10,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      };
      const privateShare = {
        ...mockShare,
        type: ShareType.PRIVATE,
        maxAccess: 10,
      };

      mockStoryRepository.findOne.mockResolvedValue(mockStory as any);
      mockShareRepository.create.mockReturnValue(privateShare as any);
      mockShareRepository.save.mockResolvedValue(privateShare as any);

      // Act
      const result = await service.createShare(privateShareDto, "user-001");

      // Assert
      expect(result.type).toBe(ShareType.PRIVATE);
      expect(result.maxAccess).toBe(10);
    });

    it("当故事不存在时应该抛出NotFoundException", async () => {
      // Arrange
      mockStoryRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.createShare(createShareDto, "user-001"),
      ).rejects.toThrow(NotFoundException);
    });

    it("当用户不是故事作者时应该抛出UnauthorizedException", async () => {
      // Arrange
      mockStoryRepository.findOne.mockResolvedValue(mockStory as any);

      // Act & Assert
      await expect(
        service.createShare(createShareDto, "other-user"),
      ).rejects.toThrow(UnauthorizedException);
    });

    it("应该生成唯一的分享码", async () => {
      // Arrange
      mockStoryRepository.findOne.mockResolvedValue(mockStory as any);
      mockShareRepository.create.mockReturnValue(mockShare as any);
      mockShareRepository.save.mockResolvedValue(mockShare as any);

      // 模拟分享码冲突，第一次查询返回存在的分享，第二次返回null
      mockShareRepository.findOne
        .mockResolvedValueOnce({ shareCode: "ABC123" } as any)
        .mockResolvedValueOnce(null);

      // Act
      await service.createShare(createShareDto, "user-001");

      // Assert
      expect(mockShareRepository.findOne).toHaveBeenCalledWith({
        where: { shareCode: "ABC123" },
      });
      expect(randomBytes).toHaveBeenCalledTimes(2); // 重新生成
    });
  });

  describe("getShareByCode - 通过分享码获取分享", () => {
    it("应该成功获取有效的分享", async () => {
      // Arrange
      mockShareRepository.findOne.mockResolvedValue(mockShare as any);

      // Act
      const result = await service.getShareByCode("ABC123");

      // Assert
      expect(mockShareRepository.findOne).toHaveBeenCalledWith({
        where: { shareCode: "ABC123" },
        relations: ["story", "story.user", "user"],
      });
      expect(result).toEqual(
        expect.objectContaining({
          id: "share-001",
          shareCode: "ABC123",
        }),
      );
    });

    it("当分享不存在时应该抛出NotFoundException", async () => {
      // Arrange
      mockShareRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(service.getShareByCode("INVALID")).rejects.toThrow(
        NotFoundException,
      );
    });

    it("当分享已过期时应该抛出BadRequestException", async () => {
      // Arrange
      const expiredShare = {
        ...mockShare,
        expiresAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // 昨天过期
      };
      mockShareRepository.findOne.mockResolvedValue(expiredShare as any);

      // Act & Assert
      await expect(service.getShareByCode("ABC123")).rejects.toThrow(
        BadRequestException,
      );
    });

    it("当分享已停用时应该抛出BadRequestException", async () => {
      // Arrange
      const inactiveShare = { ...mockShare, status: ShareStatus.INACTIVE };
      mockShareRepository.findOne.mockResolvedValue(inactiveShare as any);

      // Act & Assert
      await expect(service.getShareByCode("ABC123")).rejects.toThrow(
        BadRequestException,
      );
    });
  });

  describe("accessShare - 访问分享", () => {
    const accessDto: ShareAccessDto = {
      ipAddress: "***********",
      userAgent: "Mozilla/5.0",
      referrer: "https://example.com",
      metadata: { device: "desktop" },
    };

    it("应该成功访问公开分享", async () => {
      // Arrange
      mockShareRepository.findOne.mockResolvedValue(mockShare as any);
      mockShareAccessLogRepository.create.mockReturnValue(mockAccessLog as any);
      mockShareAccessLogRepository.save.mockResolvedValue(mockAccessLog as any);
      mockShareRepository.save.mockResolvedValue({
        ...mockShare,
        accessCount: 11,
      } as any);

      // Act
      const result = await service.accessShare("ABC123", accessDto);

      // Assert
      expect(mockShareAccessLogRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          shareId: "share-001",
          ipAddress: "***********",
          userAgent: "Mozilla/5.0",
        }),
      );
      expect(result.accessCount).toBe(11);
    });

    it("应该正确处理私密分享的访问限制", async () => {
      // Arrange
      const privateShare = {
        ...mockShare,
        type: ShareType.PRIVATE,
        maxAccess: 10,
        accessCount: 10, // 已达上限
      };
      mockShareRepository.findOne.mockResolvedValue(privateShare as any);

      // Act & Assert
      await expect(service.accessShare("ABC123", accessDto)).rejects.toThrow(
        BadRequestException,
      );
    });

    it("应该记录访问者信息", async () => {
      // Arrange
      const accessWithUser = {
        ...accessDto,
        accessorId: "user-002",
      };
      mockShareRepository.findOne.mockResolvedValue(mockShare as any);
      mockShareAccessLogRepository.create.mockReturnValue(mockAccessLog as any);
      mockShareAccessLogRepository.save.mockResolvedValue(mockAccessLog as any);
      mockShareRepository.save.mockResolvedValue(mockShare as any);

      // Act
      await service.accessShare("ABC123", accessWithUser);

      // Assert
      expect(mockShareAccessLogRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          accessorId: "user-002",
        }),
      );
    });
  });

  describe("getUserShares - 获取用户分享列表", () => {
    const queryDto = {
      page: 1,
      limit: 10,
      type: ShareType.PUBLIC,
    };

    it("应该成功获取用户分享列表", async () => {
      // Arrange
      const mockQueryBuilder = mockShareRepository.createQueryBuilder();
      (mockQueryBuilder.getCount as jest.Mock).mockResolvedValue(1);
      (mockQueryBuilder.getMany as jest.Mock).mockResolvedValue([mockShare]);

      // Act
      const result = await service.getUserShares("user-001", queryDto);

      // Assert
      expect(mockShareRepository.createQueryBuilder).toHaveBeenCalled();
      expect(result).toEqual({
        data: expect.arrayContaining([
          expect.objectContaining({
            id: "share-001",
          }),
        ]),
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      });
    });

    it("应该正确应用类型筛选", async () => {
      // Arrange
      const mockQueryBuilder = mockShareRepository.createQueryBuilder();

      // Act
      await service.getUserShares("user-001", queryDto);

      // Assert
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "share.type = :type",
        { type: ShareType.PUBLIC },
      );
    });
  });

  describe("updateShareStatus - 更新分享状态", () => {
    it("应该成功更新分享状态", async () => {
      // Arrange
      mockShareRepository.findOne.mockResolvedValue(mockShare as any);
      const updatedShare = { ...mockShare, status: ShareStatus.INACTIVE };
      mockShareRepository.save.mockResolvedValue(updatedShare as any);

      // Act
      const result = await service.updateShareStatus(
        "share-001",
        ShareStatus.INACTIVE,
        "user-001",
      );

      // Assert
      expect(result.status).toBe(ShareStatus.INACTIVE);
      expect(mockShareRepository.save).toHaveBeenCalled();
    });

    it("当用户不是分享创建者时应该抛出UnauthorizedException", async () => {
      // Arrange
      mockShareRepository.findOne.mockResolvedValue(mockShare as any);

      // Act & Assert
      await expect(
        service.updateShareStatus(
          "share-001",
          ShareStatus.INACTIVE,
          "other-user",
        ),
      ).rejects.toThrow(UnauthorizedException);
    });
  });

  describe("getShareStats - 获取分享统计", () => {
    it("应该成功获取分享统计数据", async () => {
      // Arrange
      const mockStats = {
        totalShares: "5",
        publicShares: "3",
        privateShares: "2",
        activeShares: "4",
        totalAccess: "100",
      };
      const mockQueryBuilder = mockShareRepository.createQueryBuilder();
      (mockQueryBuilder.getRawOne as jest.Mock).mockResolvedValue(mockStats);

      // Act
      const result = await service.getShareStats("user-001");

      // Assert
      expect(result).toEqual({
        totalShares: 5,
        publicShares: 3,
        privateShares: 2,
        activeShares: 4,
        totalAccess: 100,
      });
    });

    it("应该正确处理空统计数据", async () => {
      // Arrange
      const emptyStats = {
        totalShares: null,
        publicShares: null,
        privateShares: null,
        activeShares: null,
        totalAccess: null,
      };
      const mockQueryBuilder = mockShareRepository.createQueryBuilder();
      (mockQueryBuilder.getRawOne as jest.Mock).mockResolvedValue(emptyStats);

      // Act
      const result = await service.getShareStats("user-001");

      // Assert
      expect(result).toEqual({
        totalShares: 0,
        publicShares: 0,
        privateShares: 0,
        activeShares: 0,
        totalAccess: 0,
      });
    });
  });

  describe("getShareAccessLogs - 获取分享访问日志", () => {
    const queryDto = {
      page: 1,
      limit: 10,
    };

    it("应该成功获取访问日志", async () => {
      // Arrange
      const mockQueryBuilder =
        mockShareAccessLogRepository.createQueryBuilder();
      (mockQueryBuilder.getCount as jest.Mock).mockResolvedValue(1);
      (mockQueryBuilder.getMany as jest.Mock).mockResolvedValue([
        mockAccessLog,
      ]);

      // Act
      const result = await service.getShareAccessLogs(
        "share-001",
        queryDto,
        "user-001",
      );

      // Assert
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        "log.shareId = :shareId",
        { shareId: "share-001" },
      );
      expect(result.total).toBe(1);
      expect(result.data).toHaveLength(1);
    });

    it("当用户不是分享创建者时应该抛出UnauthorizedException", async () => {
      // Arrange
      mockShareRepository.findOne.mockResolvedValue(mockShare as any);

      // Act & Assert
      await expect(
        service.getShareAccessLogs("share-001", queryDto, "other-user"),
      ).rejects.toThrow(UnauthorizedException);
    });
  });

  describe("私有方法测试", () => {
    it("generateShareCode - 应该生成指定长度的分享码", () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const shareCode = (service as any).generateShareCode(6);

      expect(typeof shareCode).toBe("string");
      expect(shareCode).toMatch(/^[A-Z0-9]{6}$/);
    });

    it("buildShareUrl - 应该正确构建分享URL", () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const shareUrl = (service as any).buildShareUrl("ABC123");

      expect(shareUrl).toContain("ABC123");
      expect(shareUrl).toMatch(/^https?:\/\//);
    });

    it("isShareExpired - 应该正确判断分享是否过期", () => {
      const futureDate = new Date(Date.now() + 24 * 60 * 60 * 1000);
      const pastDate = new Date(Date.now() - 24 * 60 * 60 * 1000);

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      expect((service as any).isShareExpired({ expiresAt: null })).toBe(false);
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      expect((service as any).isShareExpired({ expiresAt: futureDate })).toBe(
        false,
      );
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      expect((service as any).isShareExpired({ expiresAt: pastDate })).toBe(
        true,
      );
    });

    it("canAccess - 应该正确判断是否可以访问", () => {
      const publicShare = { type: ShareType.PUBLIC };
      const privateShare = {
        type: ShareType.PRIVATE,
        maxAccess: 10,
        accessCount: 5,
      };
      const fullPrivateShare = {
        type: ShareType.PRIVATE,
        maxAccess: 10,
        accessCount: 10,
      };

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      expect((service as any).canAccess(publicShare)).toBe(true);
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      expect((service as any).canAccess(privateShare)).toBe(true);
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      expect((service as any).canAccess(fullPrivateShare)).toBe(false);
    });
  });

  describe("错误边界测试", () => {
    it("应该处理数据库连接错误", async () => {
      // Arrange
      mockShareRepository.createQueryBuilder.mockImplementation(() => {
        throw new Error("数据库连接失败");
      });

      // Act & Assert
      await expect(
        service.getUserShares("user-001", { page: 1, limit: 10 }),
      ).rejects.toThrow("数据库连接失败");
    });

    it("应该处理分享码生成冲突", async () => {
      // Arrange
      mockStoryRepository.findOne.mockResolvedValue(mockStory as any);
      // 模拟多次冲突
      mockShareRepository.findOne
        .mockResolvedValueOnce({ shareCode: "ABC123" } as any)
        .mockResolvedValueOnce({ shareCode: "ABC123" } as any)
        .mockResolvedValueOnce({ shareCode: "ABC123" } as any)
        .mockResolvedValueOnce({ shareCode: "ABC123" } as any)
        .mockResolvedValueOnce({ shareCode: "ABC123" } as any)
        .mockResolvedValueOnce({ shareCode: "ABC123" } as any); // 超过5次重试

      // Act & Assert
      await expect(
        service.createShare(
          {
            storyId: "story-001",
            type: ShareType.PUBLIC,
          },
          "user-001",
        ),
      ).rejects.toThrow("无法生成唯一的分享码");
    });
  });
});
