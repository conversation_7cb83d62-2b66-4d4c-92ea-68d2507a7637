import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import type { Request } from "express";
import { DeviceFingerprintService } from "./device-fingerprint.service";
import type { DeviceInfo } from "./device-fingerprint.service";
import { EnhancedRedisService } from "./enhanced-redis.service";

describe("DeviceFingerprintService", () => {
  let service: DeviceFingerprintService;
  // enhancedRedisService 变量如果不使用，可以删除或添加下划线前缀
  let _enhancedRedisService: Partial<EnhancedRedisService>;
  let mockRedisClient: { get: jest.Mock; setex: jest.Mock; del: jest.Mock };

  // Mock 数据
  const mockUserId = "test-user-id";
  const mockPhone = "13800138000";
  const mockRequest = {
    headers: {
      "user-agent":
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
      "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
      "accept-encoding": "gzip, deflate, br",
      "x-forwarded-for": "***********",
    },
    connection: {
      remoteAddress: "***********00",
    },
  } as unknown as Request;

  const mockDeviceInfo = {
    userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    platform: "Windows",
    ip: "***********",
    acceptLanguage: "zh-CN,zh;q=0.9,en;q=0.8",
    acceptEncoding: "gzip, deflate, br",
    fingerprint: "mock-fingerprint-hash",
    firstSeen: Date.now(),
    lastUsed: Date.now(),
  };

  beforeEach(async () => {
    // 创建Redis客户端mock
    mockRedisClient = {
      get: jest.fn(),
      setex: jest.fn(),
      del: jest.fn(),
    };

    // 创建EnhancedRedisService mock
    _enhancedRedisService = {
      getClient: jest.fn().mockReturnValue(mockRedisClient),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DeviceFingerprintService,
        {
          provide: EnhancedRedisService,
          useValue: _enhancedRedisService,
        },
      ],
    }).compile();

    service = module.get<DeviceFingerprintService>(DeviceFingerprintService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("generateDeviceFingerprint", () => {
    it("should generate device fingerprint with all headers", () => {
      const result = service.generateDeviceFingerprint(mockRequest);

      expect(result).toEqual({
        userAgent:
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        platform: "Windows",
        ip: "***********",
        acceptLanguage: "zh-CN,zh;q=0.9,en;q=0.8",
        acceptEncoding: "gzip, deflate, br",
        fingerprint: expect.any(String),
      });
      expect(result.fingerprint).toHaveLength(32);
    });

    it("should handle missing headers gracefully", () => {
      const requestWithoutHeaders = {
        headers: {},
        connection: {
          remoteAddress: "127.0.0.1",
        },
      } as unknown as Request;

      const result = service.generateDeviceFingerprint(requestWithoutHeaders);

      expect(result).toEqual({
        userAgent: "",
        platform: "Unknown",
        ip: "127.0.0.1",
        acceptLanguage: "",
        acceptEncoding: "",
        fingerprint: expect.any(String),
      });
    });

    it("should extract correct platform from user agent", () => {
      const testCases = [
        {
          userAgent: "Mozilla/5.0 (Android 10; Mobile; rv:109.0)",
          expectedPlatform: "Android",
        },
        {
          userAgent: "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)",
          expectedPlatform: "iOS",
        },
        {
          userAgent: "Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X)",
          expectedPlatform: "iOS",
        },
        {
          userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)",
          expectedPlatform: "macOS",
        },
        {
          userAgent: "Mozilla/5.0 (X11; Linux x86_64)",
          expectedPlatform: "Linux",
        },
        {
          userAgent: "Unknown Browser",
          expectedPlatform: "Unknown",
        },
      ];

      testCases.forEach(({ userAgent, expectedPlatform }) => {
        const request = {
          headers: { "user-agent": userAgent },
          connection: { remoteAddress: "127.0.0.1" },
        } as unknown as Request;

        const result = service.generateDeviceFingerprint(request);
        expect(result.platform).toBe(expectedPlatform);
      });
    });

    it("should handle different IP sources", () => {
      const testCases = [
        {
          request: {
            headers: { "x-forwarded-for": "***********,***********" },
            connection: { remoteAddress: "********" },
          },
          expectedIP: "***********",
        },
        {
          request: {
            headers: { "x-real-ip": "***********" },
            connection: { remoteAddress: "********" },
          },
          expectedIP: "***********",
        },
        {
          request: {
            headers: {},
            connection: { remoteAddress: "::ffff:***********" },
          },
          expectedIP: "***********",
        },
        {
          request: {
            headers: {},
            socket: { remoteAddress: "********" },
          },
          expectedIP: "********",
        },
      ];

      testCases.forEach(({ request, expectedIP }) => {
        const result = service.generateDeviceFingerprint(
          request as unknown as Request,
        );
        expect(result.ip).toBe(expectedIP);
      });
    });

    it("should generate consistent fingerprints for same input", () => {
      const result1 = service.generateDeviceFingerprint(mockRequest);
      const result2 = service.generateDeviceFingerprint(mockRequest);

      expect(result1.fingerprint).toBe(result2.fingerprint);
    });

    it("should generate different fingerprints for different inputs", () => {
      const request2 = {
        ...mockRequest,
        headers: {
          ...mockRequest.headers,
          "user-agent": "Different User Agent",
        },
      } as Request;

      const result1 = service.generateDeviceFingerprint(mockRequest);
      const result2 = service.generateDeviceFingerprint(request2);

      expect(result1.fingerprint).not.toBe(result2.fingerprint);
    });
  });

  describe("validateDeviceFingerprint", () => {
    it("should validate known device successfully", async () => {
      const knownDevices = [mockDeviceInfo];
      mockRedisClient.get.mockResolvedValueOnce(JSON.stringify(knownDevices));

      const result = await service.validateDeviceFingerprint(
        mockUserId,
        mockDeviceInfo,
      );

      expect(result).toEqual({
        isValid: true,
        isNewDevice: false,
        riskScore: 0,
        reasons: [],
      });
      expect(mockRedisClient.get).toHaveBeenCalledWith(
        `user:devices:${mockUserId}`,
      );
    });

    it("should validate new low-risk device", async () => {
      mockRedisClient.get.mockResolvedValueOnce(null); // No known devices
      mockRedisClient.get.mockResolvedValueOnce("0"); // Recent device count

      const result = await service.validateDeviceFingerprint(
        mockUserId,
        mockDeviceInfo,
      );

      expect(result).toEqual({
        isValid: true,
        isNewDevice: true,
        riskScore: expect.any(Number),
        reasons: expect.any(Array),
      });
      expect(result.riskScore).toBeLessThanOrEqual(50);
    });

    it("should reject high-risk device", async () => {
      const suspiciousDevice = {
        ...mockDeviceInfo,
        userAgent: "curl/7.68.0", // Suspicious user agent (+40分)
        fingerprint: "suspicious-fingerprint",
        ip: "***********", // Different IP for IP change (+20分)
        platform: "Linux", // Different platform (+15分)
      };

      // Mock existing devices with different IP and platform
      const existingDevices = [
        { ...mockDeviceInfo, ip: "***********", platform: "Windows" },
      ];

      mockRedisClient.get.mockResolvedValueOnce(
        JSON.stringify(existingDevices),
      ); // Known devices
      mockRedisClient.get.mockResolvedValueOnce("0"); // Recent device count

      const result = await service.validateDeviceFingerprint(
        mockUserId,
        suspiciousDevice,
      );

      expect(result.isValid).toBe(false);
      expect(result.isNewDevice).toBe(true);
      expect(result.riskScore).toBeGreaterThan(50); // 40 + 20 + 15 = 75
      expect(result.reasons).toContain("可疑User-Agent");
      expect(result.reasons).toContain("IP地址变化");
      expect(result.reasons).toContain("设备平台变化");
    });

    it("should handle validation errors gracefully", async () => {
      mockRedisClient.get.mockRejectedValue(new Error("Redis error"));

      const result = await service.validateDeviceFingerprint(
        mockUserId,
        mockDeviceInfo,
      );

      expect(result).toEqual({
        isValid: true,
        isNewDevice: true,
        riskScore: 30,
        reasons: ["设备验证服务异常"],
      });
    });

    it("should detect IP address changes", async () => {
      const existingDevice = { ...mockDeviceInfo, ip: "********" };
      const newDevice = {
        ...mockDeviceInfo,
        ip: "***********",
        fingerprint: "new-fingerprint",
      };

      mockRedisClient.get.mockResolvedValueOnce(
        JSON.stringify([existingDevice]),
      );
      mockRedisClient.get.mockResolvedValueOnce("0"); // Recent device count

      const result = await service.validateDeviceFingerprint(
        mockUserId,
        newDevice,
      );

      expect(result.reasons).toContain("IP地址变化");
      expect(result.riskScore).toBeGreaterThan(0);
    });

    it("should detect platform changes", async () => {
      const existingDevice = { ...mockDeviceInfo, platform: "Windows" };
      const newDevice = {
        ...mockDeviceInfo,
        platform: "Android",
        fingerprint: "new-fingerprint",
      };

      mockRedisClient.get.mockResolvedValueOnce(
        JSON.stringify([existingDevice]),
      );
      mockRedisClient.get.mockResolvedValueOnce("0"); // Recent device count

      const result = await service.validateDeviceFingerprint(
        mockUserId,
        newDevice,
      );

      expect(result.reasons).toContain("设备平台变化");
      expect(result.riskScore).toBeGreaterThan(0);
    });

    it("should detect frequent device changes", async () => {
      mockRedisClient.get.mockResolvedValueOnce(null); // No known devices
      mockRedisClient.get.mockResolvedValueOnce("5"); // High recent device count

      const result = await service.validateDeviceFingerprint(
        mockUserId,
        mockDeviceInfo,
      );

      expect(result.reasons).toContain("频繁设备变更");
      expect(result.riskScore).toBeGreaterThan(0);
    });
  });

  describe("recordSmsDevice", () => {
    it("should record SMS device information", async () => {
      await service.recordSmsDevice(mockPhone, mockDeviceInfo);

      expect(mockRedisClient.setex).toHaveBeenCalledWith(
        `sms:device:${mockPhone}`,
        24 * 60 * 60,
        expect.stringContaining(mockDeviceInfo.fingerprint),
      );
    });

    it("should include timestamp in device record", async () => {
      const beforeTime = Date.now();
      await service.recordSmsDevice(mockPhone, mockDeviceInfo);
      const afterTime = Date.now();

      const [, , recordStr] = mockRedisClient.setex.mock.calls[0];
      const record = JSON.parse(recordStr);

      expect(record.timestamp).toBeGreaterThanOrEqual(beforeTime);
      expect(record.timestamp).toBeLessThanOrEqual(afterTime);
      expect(record.fingerprint).toBe(mockDeviceInfo.fingerprint);
    });
  });

  describe("validateSmsDevice", () => {
    it("should validate SMS device successfully when consistent", async () => {
      const storedDevice = {
        ...mockDeviceInfo,
        timestamp: Date.now(),
      };

      mockRedisClient.get.mockResolvedValue(JSON.stringify(storedDevice));

      const result = await service.validateSmsDevice(mockPhone, mockDeviceInfo);

      expect(result).toBe(true);
      expect(mockRedisClient.get).toHaveBeenCalledWith(
        `sms:device:${mockPhone}`,
      );
    });

    it("should return false when device fingerprints are inconsistent", async () => {
      const storedDevice = {
        ...mockDeviceInfo,
        fingerprint: "different-fingerprint",
        timestamp: Date.now(),
      };

      mockRedisClient.get.mockResolvedValue(JSON.stringify(storedDevice));

      const result = await service.validateSmsDevice(mockPhone, mockDeviceInfo);

      expect(result).toBe(false);
    });

    it("should return true when no stored device record exists", async () => {
      mockRedisClient.get.mockResolvedValue(null);

      const result = await service.validateSmsDevice(mockPhone, mockDeviceInfo);

      expect(result).toBe(true);
    });

    it("should handle validation errors gracefully", async () => {
      mockRedisClient.get.mockRejectedValue(new Error("Redis error"));

      const result = await service.validateSmsDevice(mockPhone, mockDeviceInfo);

      expect(result).toBe(true); // Default to allow when error occurs
    });

    it("should handle invalid JSON in stored device", async () => {
      mockRedisClient.get.mockResolvedValue("invalid-json");

      const result = await service.validateSmsDevice(mockPhone, mockDeviceInfo);

      expect(result).toBe(true); // Default to allow when parsing fails
    });
  });

  describe("risk analysis", () => {
    it("should identify suspicious user agents", async () => {
      const suspiciousUserAgents = [
        "curl/7.68.0",
        "wget/1.20.3",
        "python-requests/2.25.1",
        "Java/1.8.0_281",
        "GoogleBot/2.1",
        "WebCrawler/5.0",
        "Spider/1.0",
        "Scanner/2.0",
        "", // Empty user agent
      ];

      for (const userAgent of suspiciousUserAgents) {
        const suspiciousDevice = { ...mockDeviceInfo, userAgent };
        mockRedisClient.get.mockResolvedValueOnce(null);
        mockRedisClient.get.mockResolvedValueOnce("0");

        const result = await service.validateDeviceFingerprint(
          mockUserId,
          suspiciousDevice,
        );

        expect(result.reasons).toContain("可疑User-Agent");
        expect(result.riskScore).toBeGreaterThan(30);
      }
    });

    it("should not flag normal user agents as suspicious", async () => {
      const normalUserAgents = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36",
      ];

      for (const userAgent of normalUserAgents) {
        const normalDevice = {
          ...mockDeviceInfo,
          userAgent,
          fingerprint: `fingerprint-${userAgent.length}`,
        };
        mockRedisClient.get.mockResolvedValueOnce(null);
        mockRedisClient.get.mockResolvedValueOnce("0");

        const result = await service.validateDeviceFingerprint(
          mockUserId,
          normalDevice,
        );

        expect(result.reasons).not.toContain("可疑User-Agent");
      }
    });

    it("should limit device records to 10", async () => {
      // Simulate adding the 11th device
      const existingDevices = Array.from({ length: 10 }, (_, i) => ({
        ...mockDeviceInfo,
        fingerprint: `device-${i}`,
        lastUsed: Date.now() - i * 1000, // Different last used times
      }));

      // Mock Redis calls in order:
      // 1. getUserKnownDevices in validateDeviceFingerprint (初次调用)
      mockRedisClient.get.mockResolvedValueOnce(
        JSON.stringify(existingDevices),
      );
      // 2. getRecentDeviceCount in analyzeDeviceRisk
      mockRedisClient.get.mockResolvedValueOnce("0");
      // 3. getUserKnownDevices in recordKnownDevice (获取最新设备列表)
      mockRedisClient.get.mockResolvedValueOnce(
        JSON.stringify(existingDevices),
      );

      // Mock the setex call that will save the updated device list
      mockRedisClient.setex.mockResolvedValue("OK");

      const newDevice = { ...mockDeviceInfo, fingerprint: "new-device" };
      const result = await service.validateDeviceFingerprint(
        mockUserId,
        newDevice,
      );

      // Should be valid and new device (low risk score)
      expect(result.isValid).toBe(true);
      expect(result.isNewDevice).toBe(true);

      // Should save the device list (recordKnownDevice called)
      expect(mockRedisClient.setex).toHaveBeenCalled();

      // Find the setex call that saves device list (not the one for recent device count)
      const setexCalls = mockRedisClient.setex.mock.calls;
      const deviceListCall = setexCalls.find(
        (call) =>
          call[0] === `user:devices:${mockUserId}` &&
          call[1] === 90 * 24 * 60 * 60, // 90 days TTL
      );

      expect(deviceListCall).toBeDefined();
      const [, , savedDevicesStr] = deviceListCall;
      const savedDevices = JSON.parse(savedDevicesStr);

      expect(savedDevices).toHaveLength(10); // Should be limited to 10
      // New device should be at the end after sorting by lastUsed (highest to lowest)
      expect(
        savedDevices.some(
          (device: DeviceInfo) => device.fingerprint === "new-device",
        ),
      ).toBe(true);
    });
  });

  describe("cleanupExpiredDevices", () => {
    it("should execute cleanup process", async () => {
      await service.cleanupExpiredDevices();

      // This method currently only logs, so we just verify it doesn't throw
      expect(true).toBe(true);
    });
  });

  describe("edge cases", () => {
    it("should handle empty request headers", () => {
      const emptyRequest = {
        headers: {},
      } as unknown as Request;

      const result = service.generateDeviceFingerprint(emptyRequest);

      expect(result.userAgent).toBe("");
      expect(result.acceptLanguage).toBe("");
      expect(result.acceptEncoding).toBe("");
      expect(result.ip).toBe("127.0.0.1"); // Default IP
      expect(result.platform).toBe("Unknown");
      expect(result.fingerprint).toBeDefined();
    });

    it("should handle undefined connection and socket", () => {
      const requestWithoutConnection = {
        headers: {},
      } as unknown as Request;

      const result = service.generateDeviceFingerprint(
        requestWithoutConnection,
      );

      expect(result.ip).toBe("127.0.0.1");
    });

    it("should handle malformed x-forwarded-for header", () => {
      const requestWithMalformedHeader = {
        headers: {
          "x-forwarded-for": "",
        },
        connection: {
          remoteAddress: "***********",
        },
      } as unknown as Request;

      const result = service.generateDeviceFingerprint(
        requestWithMalformedHeader,
      );

      expect(result.ip).toBe("***********");
    });

    it("should handle risk score exceeding 100", async () => {
      // Create a device that would score very high
      const highRiskDevice = {
        ...mockDeviceInfo,
        userAgent: "curl/7.68.0", // +40 points
        fingerprint: "high-risk-fingerprint",
        ip: "***********", // Different IP for +20 points
        platform: "Linux", // Different platform for +15 points
      };

      const existingDevices = [
        { ...mockDeviceInfo, ip: "***********", platform: "Windows" },
      ];

      mockRedisClient.get.mockResolvedValueOnce(
        JSON.stringify(existingDevices),
      );
      mockRedisClient.get.mockResolvedValueOnce("5"); // High device count for +25 points

      const result = await service.validateDeviceFingerprint(
        mockUserId,
        highRiskDevice,
      );

      expect(result.riskScore).toBeLessThanOrEqual(100); // Should be capped at 100
    });
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });
});
