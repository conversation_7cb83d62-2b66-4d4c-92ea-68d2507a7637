import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/network/dio_client.dart';
import '../../../../app/di/injection.dart';
import '../../data/datasources/auth_local_datasource.dart';
import '../../data/datasources/auth_remote_datasource.dart';
import '../../data/repositories/auth_repository_impl.dart';
import '../../domain/repositories/auth_repository.dart';
import '../../domain/usecases/login_with_phone.dart';
import '../../domain/usecases/send_sms_code.dart';
import 'auth_state.dart';
import 'auth_notifier.dart';

/// SharedPreferences Provider
final sharedPreferencesProvider = Provider<SharedPreferences>((ref) {
  return getIt<SharedPreferences>();
});

/// FlutterSecureStorage Provider
final secureStorageProvider = Provider<FlutterSecureStorage>((ref) {
  return getIt<FlutterSecureStorage>();
});

/// 认证本地数据源Provider
final authLocalDataSourceProvider = Provider<AuthLocalDataSource>((ref) {
  return AuthLocalDataSourceImpl(
    secureStorage: ref.watch(secureStorageProvider),
    sharedPreferences: ref.watch(sharedPreferencesProvider),
  );
});

/// 认证远程数据源Provider
final authRemoteDataSourceProvider = Provider<AuthRemoteDataSource>((ref) {
  return AuthRemoteDataSourceImpl(
    dio: ref.watch(dioProvider),
  );
});

/// 认证仓库Provider
final authRepositoryProvider = Provider<AuthRepository>((ref) {
  return AuthRepositoryImpl(
    remoteDataSource: ref.watch(authRemoteDataSourceProvider),
    localDataSource: ref.watch(authLocalDataSourceProvider),
  );
});

/// 发送短信验证码用例Provider
final sendSmsCodeUseCaseProvider = Provider<SendSmsCode>((ref) {
  return SendSmsCode(ref.watch(authRepositoryProvider));
});

/// 手机号登录用例Provider
final loginWithPhoneUseCaseProvider = Provider<LoginWithPhone>((ref) {
  return LoginWithPhone(ref.watch(authRepositoryProvider));
});

/// 认证状态Provider
final authStateProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  return AuthNotifier(
    authRepository: ref.watch(authRepositoryProvider),
    loginWithPhone: ref.watch(loginWithPhoneUseCaseProvider),
    sendSmsCode: ref.watch(sendSmsCodeUseCaseProvider),
  );
});

/// 当前用户Provider
final currentUserProvider = Provider((ref) {
  return ref.watch(authStateProvider).user;
});

/// 是否已登录Provider
final isLoggedInProvider = Provider<bool>((ref) {
  return ref.watch(authStateProvider).isAuthenticated;
});