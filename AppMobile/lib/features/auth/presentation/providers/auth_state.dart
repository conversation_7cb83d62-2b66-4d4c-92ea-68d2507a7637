import 'package:freezed_annotation/freezed_annotation.dart';
import '../../data/models/auth_response_models.dart';

part 'auth_state.freezed.dart';

/// 认证状态
@freezed
class AuthState with _$AuthState {
  const factory AuthState.initial() = _Initial;
  const factory AuthState.checking() = _Checking; // 检查登录状态
  const factory AuthState.loading() = _Loading;
  const factory AuthState.authenticated(User user) = _Authenticated;
  const factory AuthState.unauthenticated() = _Unauthenticated;
  const factory AuthState.error(String message, [String? code]) = _Error;
}

/// 认证状态扩展方法
extension AuthStateX on AuthState {
  /// 是否已认证
  bool get isAuthenticated => this is _Authenticated;
  
  /// 是否正在加载
  bool get isLoading => this is _Loading;
  
  /// 获取当前用户
  User? get user => maybeWhen(
    authenticated: (user) => user,
    orElse: () => null,
  );
  
  /// 是否正在检查状态
  bool get isChecking => this is _Checking;
}