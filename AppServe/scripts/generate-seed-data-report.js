#!/usr/bin/env node

/**
 * 生成种子数据详细报告
 * 用于创建种子数据说明文档
 * 创建时间: 2025-07-28
 */

const fs = require('fs');
const path = require('path');

function generateDetailedReport() {
  console.log('📋 正在生成种子数据详细报告...');
  
  // 读取统计数据
  const statsFile = path.join(__dirname, '..', 'database-stats.json');
  if (!fs.existsSync(statsFile)) {
    console.error('❌ 统计数据文件不存在，请先运行数据统计查询');
    return;
  }
  
  const stats = JSON.parse(fs.readFileSync(statsFile, 'utf8'));
  
  // 生成Markdown报告
  let report = `# YGS v1.0.0 种子数据统计报告

> **生成时间**: ${stats.timestamp}  
> **数据库**: ${stats.database}  
> **主机**: ${stats.host}  
> **总记录数**: ${getTotalRecords(stats)} 条

## 📊 数据概览

### 核心数据统计

| 表名 | 记录数 | 主要功能 |
|------|--------|----------|
| **users** | ${stats.tables.users?.count || 0} | 用户账户管理，包含完整的用户信息、权限控制、安全验证等 |
| **stories** | ${stats.tables.stories?.count || 0} | 故事内容管理，支持多种发布状态和权限级别 |
| **characters** | ${stats.tables.characters?.count || 0} | 故事人物管理，支持点亮功能的核心实体 |
| **themes** | ${stats.tables.themes?.count || 0} | 故事主题分类，帮助用户组织和发现内容 |

### 互动功能数据

| 表名 | 记录数 | 主要功能 |
|------|--------|----------|
| **comments** | ${stats.tables.comments?.count || 0} | 用户评论系统，支持多层级回复 |
| **comment_likes** | ${stats.tables.comment_likes?.count || 0} | 评论点赞功能 |
| **bookmarks** | ${stats.tables.bookmarks?.count || 0} | 故事收藏管理，支持分类和标签 |
| **shares** | ${stats.tables.shares?.count || 0} | 故事分享链接管理 |
| **share_access_logs** | ${stats.tables.share_access_logs?.count || 0} | 分享访问记录追踪 |

### 核心业务功能

| 表名 | 记录数 | 主要功能 |
|------|--------|----------|
| **light_requests** | ${stats.tables.light_requests?.count || 0} | 人物点亮申请，YGS平台的核心功能 |
| **character_lightings** | ${stats.tables.character_lightings?.count || 0} | 成功的人物点亮关系记录 |
| **user_follows** | ${stats.tables.user_follows?.count || 0} | 用户关注关系管理 |
| **friend_groups** | ${stats.tables.friend_groups?.count || 0} | 好友分组功能 |

### 系统支持功能

| 表名 | 记录数 | 主要功能 |
|------|--------|----------|
| **notifications** | ${stats.tables.notifications?.count || 0} | 系统通知推送 |
| **timeline_events** | ${stats.tables.timeline_events?.count || 0} | 用户个人时间线管理 |

## 🔍 关键数据样本分析

`;

  // 添加各表的详细信息
  const tableOrder = [
    'users', 'stories', 'characters', 'themes', 
    'light_requests', 'character_lightings',
    'comments', 'bookmarks', 'shares', 'notifications'
  ];

  tableOrder.forEach(tableName => {
    const table = stats.tables[tableName];
    if (!table || table.count === 0) return;

    report += `### ${getTableDisplayName(tableName)} (${table.count} 条记录)\n\n`;
    
    // 表结构信息
    if (table.schema && table.schema.length > 0) {
      report += `**表结构特点**:\n`;
      report += `- 总字段数: ${table.schema.length}\n`;
      
      const keyFields = table.schema.filter(col => 
        col.name.includes('id') || 
        col.name.includes('name') || 
        col.name.includes('title') ||
        col.name.includes('status')
      );
      
      if (keyFields.length > 0) {
        report += `- 关键字段: ${keyFields.slice(0, 5).map(f => f.name).join(', ')}\n`;
      }
    }

    // 示例数据
    if (table.samples && table.samples.length > 0) {
      report += `\n**数据样本**:\n`;
      const sample = table.samples[0];
      
      // 根据表类型显示不同的关键信息
      if (tableName === 'users') {
        report += `- 用户示例: ${sample.nickname || sample.username} (${sample.phone})\n`;
        report += `- 安全等级: ${sample.security_level}, VIP用户: ${sample.is_vip_user ? '是' : '否'}\n`;
        report += `- 验证状态: 手机${sample.is_phone_verified ? '已验证' : '未验证'}, 邮箱${sample.is_email_verified ? '已验证' : '未验证'}\n`;
      } else if (tableName === 'stories') {
        report += `- 故事标题: "${sample.title}"\n`;
        report += `- 发布状态: ${getStoryStatus(sample.status)}, 权限: ${sample.permission_level}\n`;
        report += `- 统计数据: 浏览${sample.view_count}次, 点赞${sample.like_count}次, 评论${sample.comment_count}条\n`;
      } else if (tableName === 'characters') {
        report += `- 人物姓名: "${sample.name}"\n`;
        report += `- 关系类型: ${sample.relationship}, 性别: ${sample.gender}\n`;
        report += `- 点亮状态: ${sample.is_lighted ? '已点亮' : '未点亮'} (被点亮${sample.lighting_count}次)\n`;
      } else if (tableName === 'light_requests') {
        report += `- 申请状态: ${sample.status}\n`;
        report += `- 手机验证: ${sample.has_phone_verification ? '已验证' : '未验证'}\n`;
        report += `- 申请消息: "${sample.message?.substring(0, 50)}..."\n`;
      }
    }

    report += `\n`;
  });

  // 业务场景分析
  report += `## 🎯 业务场景覆盖

### 1. 用户管理场景
- **完整用户生命周期**: 从注册、验证到VIP升级的完整流程
- **安全防护机制**: 包含异常检测、账户锁定、登录失败计数等企业级安全特性
- **个性化设置**: 用户可自定义个人主页展示内容和隐私设置

### 2. 内容创作场景
- **多样化故事类型**: 包含${stats.tables.themes?.count || 0}个不同主题分类
- **灵活权限控制**: 支持私密、好友可见、公开、仅人物可见等多级权限
- **富媒体支持**: 故事可包含图片、位置、时间等多维度信息

### 3. 社交互动场景
- **点亮核心功能**: ${stats.tables.light_requests?.count || 0}个点亮申请，${stats.tables.character_lightings?.count || 0}个成功点亮
- **评论互动系统**: ${stats.tables.comments?.count || 0}条评论，${stats.tables.comment_likes?.count || 0}个点赞
- **社交关系网络**: ${stats.tables.user_follows?.count || 0}个关注关系，${stats.tables.friend_groups?.count || 0}个好友分组

### 4. 内容分享场景
- **多渠道分享**: ${stats.tables.shares?.count || 0}个分享链接，支持公开、密码保护等模式
- **访问追踪**: ${stats.tables.share_access_logs?.count || 0}条访问记录，包含IP、设备、来源等详细信息

### 5. 个人数据管理
- **时间线功能**: ${stats.tables.timeline_events?.count || 0}个个人时间线事件
- **收藏系统**: ${stats.tables.bookmarks?.count || 0}个收藏记录，支持分类和标签管理
- **通知中心**: ${stats.tables.notifications?.count || 0}条系统通知

## 🔑 测试账号信息

### 主要测试账号
1. **创作者张明远**: 13800138001 / Test123456!
   - 角色: 主要内容创建者
   - 特点: 已创建多个故事，具有完整的人物关系网

2. **点亮者李小红**: 13800138002 / Test123456!
   - 角色: 活跃的点亮用户
   - 特点: 成功点亮多个人物，是社交功能的核心参与者

3. **教师王建国**: 13800138003 / Test123456!
   - 角色: 教育工作者用户
   - 特点: 代表专业用户群体，有丰富的人生阅历

4. **VIP陈总**: 16675158665 / Test123456!
   - 角色: VIP用户
   - 特点: 具有VIP权限，体验高级功能

5. **系统测试**: 13800138009 / Test123456!
   - 角色: 系统测试专用账号
   - 特点: 用于自动化测试和功能验证

## 📈 数据质量指标

- **数据完整性**: ✅ 所有核心表都包含测试数据
- **关联关系**: ✅ 表间外键关系正确建立
- **业务逻辑**: ✅ 点亮、关注、评论等业务流程完整
- **安全合规**: ✅ 密码加密、敏感信息保护
- **性能优化**: ✅ 包含足够数据量用于性能测试

## 🔄 数据更新机制

- **种子数据重置**: 使用 \`npm run seed:clean\` 清理数据
- **增量数据生成**: 使用 \`npm run seed:users\` 仅生成用户数据
- **数据验证**: 使用 \`npm run seed:validate\` 检查数据完整性
- **统计查询**: 使用 \`npm run seed:stats\` 获取最新统计

---

**报告生成时间**: ${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}  
**数据版本**: YGS v1.0.0 企业级种子数据  
**维护状态**: 活跃维护中
`;

  // 保存报告
  const reportFile = path.join(__dirname, '..', 'SEED_DATA_REPORT.md');
  fs.writeFileSync(reportFile, report, 'utf8');
  
  console.log(`✅ 详细报告已生成: ${reportFile}`);
  console.log(`📊 报告包含 ${Object.keys(stats.tables).length} 个表的详细分析`);
  console.log(`🔍 总数据量: ${getTotalRecords(stats)} 条记录`);
  
  return reportFile;
}

function getTotalRecords(stats) {
  return Object.values(stats.tables).reduce((total, table) => {
    return total + (table.count || 0);
  }, 0);
}

function getTableDisplayName(tableName) {
  const displayNames = {
    'users': '👥 用户表',
    'stories': '📖 故事表', 
    'characters': '🎭 人物表',
    'themes': '🎨 主题表',
    'comments': '💬 评论表',
    'comment_likes': '👍 评论点赞表',
    'bookmarks': '⭐ 收藏表',
    'notifications': '🔔 通知表',
    'timeline_events': '📅 时间线事件表',
    'shares': '🔗 分享表',
    'share_access_logs': '📈 分享访问日志表',
    'light_requests': '💡 点亮申请表',
    'character_lightings': '✨ 人物点亮表',
    'user_follows': '👫 用户关注表',
    'friend_groups': '📁 好友分组表'
  };
  
  return displayNames[tableName] || tableName;
}

function getStoryStatus(status) {
  const statusMap = {
    1: '草稿',
    2: '已发布',
    3: '已归档'
  };
  return statusMap[status] || '未知';
}

// 执行报告生成
if (require.main === module) {
  try {
    generateDetailedReport();
  } catch (error) {
    console.error('❌ 报告生成失败:', error.message);
    process.exit(1);
  }
}