import type { CanActivate, ExecutionContext } from "@nestjs/common";
import { Injectable, Logger, ForbiddenException } from "@nestjs/common";
import { Reflector } from "@nestjs/core";
import { EnhancedRedisService } from "../services/enhanced-redis.service";
import type Redis from "ioredis";
import type { Request } from "express";

export interface SecurityConfig {
  level: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";
  checkSQLInjection?: boolean;
  checkXSS?: boolean;
  rateLimitPerMinute?: number;
  rateLimitPerHour?: number;
  requireWhitelist?: boolean;
}

// 简化的装饰器定义
export const Security = Reflector.createDecorator<SecurityConfig>();

interface RiskAnalysis {
  score: number;
  reasons: string[];
  blocked: boolean;
}

interface IPInfo {
  requests: number;
  lastRequest: number;
  riskScore: number;
  blocked: boolean;
  blockReason?: string;
}

@Injectable()
export class SecurityGuard implements CanActivate {
  private readonly logger = new Logger(SecurityGuard.name);
  private redis: Redis;

  // 恶意User-Agent模式
  private readonly maliciousUserAgents = [
    /sqlmap/i,
    /nmap/i,
    /nikto/i,
    /dirbuster/i,
    /masscan/i,
    /zap/i,
    /burp/i,
    /wget/i,
    /curl.*bot/i,
  ];

  // SQL注入模式
  private readonly sqlInjectionPatterns = [
    /(\s|^)(union|select|insert|update|delete|drop|create|alter|exec|execute)\s/i,
    /(\s|^)(and|or)\s+\d+\s*=\s*\d+/i,
    /(\s|^)(and|or)\s+[\w\s]*\s*=\s*[\w\s]*/i,
    /'(\s|^)(union|select|insert|update|delete|drop)/i,
    /(\s|^)0x[0-9a-f]+/i,
    /(\s|^)char\(/i,
    /(\s|^)concat\(/i,
    /(\s|^)group_concat\(/i,
    /(\s|^)load_file\(/i,
    /(\s|^)into\s+outfile/i,
  ];

  // XSS模式
  private readonly xssPatterns = [
    /<script[\s\S]*?>[\s\S]*?<\/script>/i,
    /<iframe[\s\S]*?>[\s\S]*?<\/iframe>/i,
    /<object[\s\S]*?>[\s\S]*?<\/object>/i,
    /<embed[\s\S]*?>/i,
    /<applet[\s\S]*?>[\s\S]*?<\/applet>/i,
    /javascript:/i,
    /vbscript:/i,
    /onload\s*=/i,
    /onerror\s*=/i,
    /onclick\s*=/i,
    /onmouseover\s*=/i,
    /onfocus\s*=/i,
    /onblur\s*=/i,
  ];

  constructor(
    private reflector: Reflector,
    private enhancedRedisService: EnhancedRedisService,
  ) {
    this.redis = this.enhancedRedisService.getClient();
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const securityConfig = this.reflector.get(Security, context.getHandler());

    if (!securityConfig) {
      return true; // 如果没有安全配置，直接通过
    }

    const request = context.switchToHttp().getRequest<Request>();
    const clientIP = this.getClientIP(request);
    const userAgent = request.headers["user-agent"] || "";
    const url = request.url;
    const method = request.method;

    this.logger.debug(`安全检查: ${method} ${url} from ${clientIP}`);

    try {
      // 1. 检查IP黑名单
      const isBlacklisted = await this.isIPBlacklisted(clientIP);
      if (isBlacklisted) {
        this.logger.warn(`IP在黑名单中: ${clientIP}`);
        throw new ForbiddenException("访问被拒绝");
      }

      // 2. 检查白名单（如果需要）
      if (securityConfig.requireWhitelist) {
        const isWhitelisted = await this.isIPWhitelisted(clientIP);
        if (!isWhitelisted) {
          this.logger.warn(`IP不在白名单中: ${clientIP}`);
          throw new ForbiddenException("访问被拒绝");
        }
      }

      // 3. 检查恶意User-Agent
      if (this.isMaliciousUserAgent(userAgent)) {
        await this.blockIP(clientIP, "恶意User-Agent", 24 * 60 * 60);
        this.logger.warn(`检测到恶意User-Agent: ${clientIP} - ${userAgent}`);
        throw new ForbiddenException("访问被拒绝");
      }

      // 4. 速率限制检查
      const rateLimitCheck = await this.checkRateLimit(
        clientIP,
        securityConfig,
      );
      if (!rateLimitCheck.allowed) {
        this.logger.warn(
          `速率限制触发: ${clientIP} - ${rateLimitCheck.reason}`,
        );
        throw new ForbiddenException(rateLimitCheck.reason);
      }

      // 5. SQL注入检查
      if (securityConfig.checkSQLInjection !== false) {
        const sqlInjectionDetected = this.detectSQLInjection(request);
        if (sqlInjectionDetected) {
          await this.blockIP(clientIP, "SQL注入攻击", 24 * 60 * 60);
          this.logger.error(`SQL注入攻击检测: ${clientIP} - ${url}`);
          throw new ForbiddenException("请求被拒绝");
        }
      }

      // 6. XSS检查
      if (securityConfig.checkXSS !== false) {
        const xssDetected = this.detectXSS(request);
        if (xssDetected) {
          await this.blockIP(clientIP, "XSS攻击", 24 * 60 * 60);
          this.logger.error(`XSS攻击检测: ${clientIP} - ${url}`);
          throw new ForbiddenException("请求被拒绝");
        }
      }

      // 7. 风险分析
      const riskAnalysis = await this.analyzeRisk(
        clientIP,
        request,
        securityConfig,
      );
      if (riskAnalysis.blocked) {
        this.logger.warn(
          `风险分析阻止: ${clientIP} - 风险分数: ${riskAnalysis.score}, 原因: ${riskAnalysis.reasons.join(", ")}`,
        );
        throw new ForbiddenException("访问被暂时限制");
      }

      // 8. 更新访问统计
      await this.updateAccessStats(clientIP, method, url);

      return true;
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }

      this.logger.error(
        `安全检查错误: ${clientIP}`,
        error instanceof Error ? error.stack : String(error),
      );
      // 在错误情况下允许通过，避免影响正常用户
      return true;
    }
  }

  /**
   * 获取客户端真实IP
   */
  private getClientIP(request: Request): string {
    return (
      (request.headers["x-forwarded-for"] as string) ||
      (request.headers["x-real-ip"] as string) ||
      request.connection.remoteAddress ||
      request.socket.remoteAddress ||
      "127.0.0.1"
    )
      .split(",")[0]
      .trim();
  }

  /**
   * 检查IP是否在黑名单
   */
  private async isIPBlacklisted(ip: string): Promise<boolean> {
    const result = await this.redis.sismember("security:blacklist", ip);
    return result === 1;
  }

  /**
   * 检查IP是否在白名单
   */
  private async isIPWhitelisted(ip: string): Promise<boolean> {
    const result = await this.redis.sismember("security:whitelist", ip);
    return result === 1;
  }

  /**
   * 阻止IP
   */
  private async blockIP(
    ip: string,
    reason: string,
    durationSeconds: number,
  ): Promise<void> {
    const pipe = this.redis.pipeline();
    pipe.sadd("security:blacklist", ip);
    pipe.set(`security:block:${ip}`, reason, "EX", durationSeconds);
    pipe.set(`security:block_time:${ip}`, Date.now(), "EX", durationSeconds);
    await pipe.exec();

    this.logger.warn(
      `IP已被阻止: ${ip}, 原因: ${reason}, 持续时间: ${durationSeconds}秒`,
    );
  }

  /**
   * 检查恶意User-Agent
   */
  private isMaliciousUserAgent(userAgent: string): boolean {
    return this.maliciousUserAgents.some((pattern) => pattern.test(userAgent));
  }

  /**
   * 速率限制检查
   */
  private async checkRateLimit(
    ip: string,
    config: SecurityConfig,
  ): Promise<{ allowed: boolean; reason?: string }> {
    const now = Date.now();
    const minuteKey = `security:rate:${ip}:${Math.floor(now / 60000)}`;
    const hourKey = `security:rate:${ip}:${Math.floor(now / 3600000)}`;

    const pipe = this.redis.pipeline();
    pipe.incr(minuteKey);
    pipe.expire(minuteKey, 60);
    pipe.incr(hourKey);
    pipe.expire(hourKey, 3600);

    const results = await pipe.exec();
    if (!results) {
      throw new Error("Redis执行失败");
    }
    const minuteCount = (results[0]?.[1] as number) || 0;
    const hourCount = (results[2]?.[1] as number) || 0;

    const minuteLimit = config.rateLimitPerMinute || 60;
    const hourLimit = config.rateLimitPerHour || 1000;

    if (minuteCount > minuteLimit) {
      return { allowed: false, reason: "请求过于频繁，请稍后再试" };
    }

    if (hourCount > hourLimit) {
      return { allowed: false, reason: "您的请求次数已达上限，请稍后再试" };
    }

    return { allowed: true };
  }

  /**
   * SQL注入检测
   */
  private detectSQLInjection(request: Request): boolean {
    const checkString = (str: string): boolean => {
      return this.sqlInjectionPatterns.some((pattern) => pattern.test(str));
    };

    // 检查URL参数
    if (checkString(request.url)) {
      return true;
    }

    // 检查查询参数
    for (const [key, value] of Object.entries(request.query || {})) {
      if (checkString(key) || checkString(String(value))) {
        return true;
      }
    }

    // 检查请求体
    if (request.body) {
      const bodyStr = JSON.stringify(request.body);
      if (checkString(bodyStr)) {
        return true;
      }
    }

    return false;
  }

  /**
   * XSS检测
   */
  private detectXSS(request: Request): boolean {
    const checkString = (str: string): boolean => {
      return this.xssPatterns.some((pattern) => pattern.test(str));
    };

    // 检查URL参数
    if (checkString(request.url)) {
      return true;
    }

    // 检查查询参数
    for (const [key, value] of Object.entries(request.query || {})) {
      if (checkString(key) || checkString(String(value))) {
        return true;
      }
    }

    // 检查请求体
    if (request.body) {
      const bodyStr = JSON.stringify(request.body);
      if (checkString(bodyStr)) {
        return true;
      }
    }

    return false;
  }

  /**
   * 风险分析
   */
  private async analyzeRisk(
    ip: string,
    request: Request,
    config: SecurityConfig,
  ): Promise<RiskAnalysis> {
    const ipInfo = await this.getIPInfo(ip);
    let riskScore = 0;
    const reasons: string[] = [];

    // 基于请求频率的风险评估
    if (ipInfo.requests > 100) {
      riskScore += 10;
      reasons.push("高频请求");
    }

    // 基于历史风险分数
    riskScore += ipInfo.riskScore;

    // 基于安全级别调整
    const levelMultipliers = {
      LOW: 0.5,
      MEDIUM: 1.0,
      HIGH: 1.5,
      CRITICAL: 2.0,
    };
    riskScore *= levelMultipliers[config.level];

    // 风险阈值
    const riskThreshold = 50;
    const blocked = riskScore > riskThreshold;

    if (blocked) {
      await this.blockIP(ip, `风险分数过高: ${riskScore}`, 60 * 60); // 阻止1小时
    }

    return {
      score: riskScore,
      reasons,
      blocked,
    };
  }

  /**
   * 获取IP信息
   */
  private async getIPInfo(ip: string): Promise<IPInfo> {
    const pipe = this.redis.pipeline();
    pipe.get(`security:ip:${ip}:requests`);
    pipe.get(`security:ip:${ip}:last_request`);
    pipe.get(`security:ip:${ip}:risk_score`);
    pipe.get(`security:block:${ip}`);

    const results = await pipe.exec();
    if (!results) {
      throw new Error("Redis执行失败");
    }

    return {
      requests: parseInt((results[0]?.[1] as string) || "0"),
      lastRequest: parseInt((results[1]?.[1] as string) || "0"),
      riskScore: parseInt((results[2]?.[1] as string) || "0"),
      blocked: !!results[3]?.[1],
      blockReason: (results[3]?.[1] as string) || "",
    };
  }

  /**
   * 更新访问统计
   */
  private async updateAccessStats(
    ip: string,
    _method: string,
    _url: string,
  ): Promise<void> {
    const now = Date.now();
    const today = new Date().toDateString();

    const pipe = this.redis.pipeline();
    pipe.incr(`security:ip:${ip}:requests`);
    pipe.expire(`security:ip:${ip}:requests`, 24 * 60 * 60);
    pipe.set(`security:ip:${ip}:last_request`, now, "EX", 24 * 60 * 60);
    pipe.incr(`security:stats:requests:${today}`);
    pipe.expire(`security:stats:requests:${today}`, 7 * 24 * 60 * 60);

    await pipe.exec();
  }
}
