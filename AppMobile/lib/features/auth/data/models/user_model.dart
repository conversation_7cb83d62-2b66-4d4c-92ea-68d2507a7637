import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/user_entity.dart';

part 'user_model.freezed.dart';
part 'user_model.g.dart';

/// 用户数据模型
@freezed
class UserModel with _$UserModel {
  const UserModel._();
  
  const factory UserModel({
    required String id,
    @JsonKey(name: 'user_number') required String userNumber,
    String? phone,
    String? email,
    required String nickname,
    String? avatar,
    String? bio,
    @<PERSON>son<PERSON><PERSON>(name: 'is_vip') @Default(false) bool isVip,
    @Json<PERSON>ey(name: 'vip_expire_at') DateTime? vipExpireAt,
    @Json<PERSON>ey(name: 'ai_quota') @Default(5) int aiQuota,
    @Json<PERSON>ey(name: 'ai_quota_used') @Default(5) int aiQuotaUsed,
    @<PERSON>son<PERSON><PERSON>(name: 'created_at') required DateTime createdAt,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at') required DateTime updatedAt,
    
    // 统计信息
    @<PERSON><PERSON><PERSON><PERSON>(name: 'stories_count') @Default(0) int storiesCount,
    @Json<PERSON><PERSON>(name: 'characters_count') @Default(0) int charactersCount,
    @Json<PERSON>ey(name: 'lighted_count') @Default(0) int lightedCount,
    @JsonKey(name: 'followers_count') @Default(0) int followersCount,
    @JsonKey(name: 'following_count') @Default(0) int followingCount,
    
    // 设置信息
    @JsonKey(name: 'enable_notification') @Default(true) bool enableNotification,
    @JsonKey(name: 'enable_biometric') @Default(false) bool enableBiometric,
    @JsonKey(name: 'default_story_permission') @Default('public') String defaultStoryPermission,
  }) = _UserModel;

  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);

  /// 转换为实体
  UserEntity toEntity() => UserEntity(
    id: id,
    userNumber: userNumber,
    phone: phone,
    email: email,
    nickname: nickname,
    avatar: avatar,
    bio: bio,
    isVip: isVip,
    vipExpireAt: vipExpireAt,
    aiQuota: aiQuota,
    aiQuotaUsed: aiQuotaUsed,
    createdAt: createdAt,
    updatedAt: updatedAt,
    storiesCount: storiesCount,
    charactersCount: charactersCount,
    lightedCount: lightedCount,
    followersCount: followersCount,
    followingCount: followingCount,
    enableNotification: enableNotification,
    enableBiometric: enableBiometric,
    defaultStoryPermission: defaultStoryPermission,
  );

  /// 从实体创建
  factory UserModel.fromEntity(UserEntity entity) => UserModel(
    id: entity.id,
    userNumber: entity.userNumber,
    phone: entity.phone,
    email: entity.email,
    nickname: entity.nickname,
    avatar: entity.avatar,
    bio: entity.bio,
    isVip: entity.isVip,
    vipExpireAt: entity.vipExpireAt,
    aiQuota: entity.aiQuota,
    aiQuotaUsed: entity.aiQuotaUsed,
    createdAt: entity.createdAt,
    updatedAt: entity.updatedAt,
    storiesCount: entity.storiesCount,
    charactersCount: entity.charactersCount,
    lightedCount: entity.lightedCount,
    followersCount: entity.followersCount,
    followingCount: entity.followingCount,
    enableNotification: entity.enableNotification,
    enableBiometric: entity.enableBiometric,
    defaultStoryPermission: entity.defaultStoryPermission,
  );
}