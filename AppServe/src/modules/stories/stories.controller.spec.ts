import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { BadRequestException, ForbiddenException } from "@nestjs/common";
import { StoriesController } from "./stories.controller";
import { StoriesService } from "./stories.service";
import type { Story } from "./entities/story.entity";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import type {
  CreateStoryDto,
  UpdateStoryDto,
  StoryQueryDto,
  StoryResponseDto,
} from "./dto";
import type { AuthRequest } from "../../common/types/request.types";
import { PaginatedResponse } from "../../common/dto/paginated-response.dto";

describe("StoriesController", () => {
  let controller: StoriesController;
  let mockStoriesService: jest.Mocked<StoriesService>;

  // Mock数据
  const mockUser = { id: "user-1", username: "testuser", userNumber: "100001" };
  const mockAuthRequest = { user: mockUser } as AuthRequest;

  const mockStory = {
    id: "story-1",
    title: "测试故事",
    content: "这是一个测试故事的内容",
    status: 1,
    userId: "user-1",
    themeId: "theme-1",
    storyDate: new Date(),
    location: "测试地点",
    images: ["image1.jpg"],
    permissionLevel: "public" as const,
    allowComments: true,
    allowLikes: true,
    allowSharing: true,
    aiSafetyScore: 95.5,
    commentCount: 0,
    coverImageUrl: "",
    viewCount: 0,
    likeCount: 0,
    createdAt: new Date(),
    updatedAt: new Date(),
    // 关联关系 Mock
    user: mockUser,
    theme: {
      id: "theme-1",
      name: "测试主题",
      icon: "📚",
      color: "#FF5733",
    },
    chapters: [],
    characters: [],
    shares: [],
  };

  const mockStoryResponse: StoryResponseDto = {
    id: "story-1",
    title: "测试故事",
    content: "这是一个测试故事的内容",
    status: 1,
    storyDate: new Date(),
    location: "测试地点",
    images: ["image1.jpg"],
    permissionLevel: "public" as const,
    allowComments: true,
    allowLikes: true,
    allowSharing: true,
    commentCount: 0,
    coverImageUrl: "",
    viewCount: 0,
    likeCount: 0,
    createdAt: new Date(),
    updatedAt: new Date(),
    aiSafetyScore: 95.5,
    characters: [],
    user: {
      id: "user-1",
      nickname: "测试用户",
      avatar: "",
      userNumber: "100001",
    },
    theme: {
      id: "theme-1",
      name: "测试主题",
      icon: "📚",
      color: "#FF5733",
    },
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } as any;

  const mockPaginatedResponse: PaginatedResponse<StoryResponseDto> =
    new PaginatedResponse([mockStoryResponse], 1, 1, 10);

  beforeEach(async () => {
    const mockServiceMethods = {
      createStory: jest.fn(),
      updateStory: jest.fn(),
      deleteStory: jest.fn(),
      findById: jest.fn(),
      getUserStories: jest.fn(),
      getAccessibleStories: jest.fn(),
      getPublicStories: jest.fn(),
      validateStoryAccess: jest.fn(),
      incrementViewCount: jest.fn(),
      incrementLikeCount: jest.fn(),
      validateStoryContent: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [StoriesController],
      providers: [
        {
          provide: StoriesService,
          useValue: mockServiceMethods,
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({
        canActivate: jest.fn(() => true),
      })
      .compile();

    controller = module.get<StoriesController>(StoriesController);
    mockStoriesService = module.get(
      StoriesService,
    ) as jest.Mocked<StoriesService>;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("createStory", () => {
    it("应该成功创建故事", async () => {
      const createDto: CreateStoryDto = {
        title: "新故事",
        content: "故事内容",
        themeId: "theme-1",
        permissionLevel: "public",
      };

      mockStoriesService.createStory.mockResolvedValue(
        mockStory as unknown as Story,
      );

      const result = await controller.createStory(createDto, mockAuthRequest);

      expect(mockStoriesService.createStory).toHaveBeenCalledWith(
        "user-1",
        createDto,
      );
      expect(result.success).toBe(true);
      expect(result.message).toBe("故事创建成功");
      expect(result.statusCode).toBe(201);
      expect(result.data).toBeInstanceOf(Object);
    });

    it("应该处理创建故事时的错误", async () => {
      const createDto: CreateStoryDto = {
        title: "新故事",
        content: "故事内容",
        themeId: "theme-1",
        permissionLevel: "public",
      };

      mockStoriesService.createStory.mockRejectedValue(new Error("创建失败"));

      await expect(
        controller.createStory(createDto, mockAuthRequest),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe("updateStory", () => {
    it("应该成功更新故事", async () => {
      const updateDto: UpdateStoryDto = {
        title: "更新的标题",
        content: "更新的内容",
      };

      mockStoriesService.updateStory.mockResolvedValue(
        mockStory as unknown as Story,
      );

      const result = await controller.updateStory(
        "story-1",
        updateDto,
        mockAuthRequest,
      );

      expect(mockStoriesService.updateStory).toHaveBeenCalledWith(
        "story-1",
        updateDto,
        "user-1",
      );
      expect(result.success).toBe(true);
      expect(result.message).toBe("故事更新成功");
      expect(result.statusCode).toBe(200);
    });

    it("应该传递权限错误", async () => {
      const updateDto: UpdateStoryDto = {
        title: "更新的标题",
      };

      mockStoriesService.updateStory.mockRejectedValue(
        new ForbiddenException("无权限"),
      );

      await expect(
        controller.updateStory("story-1", updateDto, mockAuthRequest),
      ).rejects.toThrow(ForbiddenException);
    });

    it("应该处理其他更新错误", async () => {
      const updateDto: UpdateStoryDto = {
        title: "更新的标题",
      };

      mockStoriesService.updateStory.mockRejectedValue(new Error("更新失败"));

      await expect(
        controller.updateStory("story-1", updateDto, mockAuthRequest),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe("deleteStory", () => {
    it("应该成功删除故事", async () => {
      mockStoriesService.deleteStory.mockResolvedValue(undefined);

      await controller.deleteStory("story-1", mockAuthRequest);

      expect(mockStoriesService.deleteStory).toHaveBeenCalledWith(
        "story-1",
        "user-1",
      );
    });

    it("应该传递权限错误", async () => {
      mockStoriesService.deleteStory.mockRejectedValue(
        new ForbiddenException("无权限"),
      );

      await expect(
        controller.deleteStory("story-1", mockAuthRequest),
      ).rejects.toThrow(ForbiddenException);
    });

    it("应该处理其他删除错误", async () => {
      mockStoriesService.deleteStory.mockRejectedValue(new Error("删除失败"));

      await expect(
        controller.deleteStory("story-1", mockAuthRequest),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe("getStoryById", () => {
    it("应该成功获取故事详情", async () => {
      mockStoriesService.validateStoryAccess.mockResolvedValue(true);
      mockStoriesService.findById.mockResolvedValue(
        mockStory as unknown as Story,
      );
      mockStoriesService.incrementViewCount.mockResolvedValue(undefined);

      const result = await controller.getStoryById("story-1", mockAuthRequest);

      expect(mockStoriesService.validateStoryAccess).toHaveBeenCalledWith(
        "story-1",
        "user-1",
      );
      expect(mockStoriesService.findById).toHaveBeenCalledWith("story-1");
      expect(result.success).toBe(true);
      expect(result.message).toBe("获取故事成功");
    });

    it("应该在非作者访问时增加浏览量", async () => {
      const otherUserStory = {
        ...mockStory,
        userId: "other-user",
      } as unknown as Story;

      mockStoriesService.validateStoryAccess.mockResolvedValue(true);
      mockStoriesService.findById.mockResolvedValue(otherUserStory);
      mockStoriesService.incrementViewCount.mockResolvedValue(undefined);

      await controller.getStoryById("story-1", mockAuthRequest);

      expect(mockStoriesService.incrementViewCount).toHaveBeenCalledWith(
        "story-1",
      );
    });

    it("应该在作者访问时不增加浏览量", async () => {
      mockStoriesService.validateStoryAccess.mockResolvedValue(true);
      mockStoriesService.findById.mockResolvedValue(
        mockStory as unknown as Story,
      );

      await controller.getStoryById("story-1", mockAuthRequest);

      expect(mockStoriesService.incrementViewCount).not.toHaveBeenCalled();
    });

    it("应该在无访问权限时抛出错误", async () => {
      mockStoriesService.validateStoryAccess.mockResolvedValue(false);

      await expect(
        controller.getStoryById("story-1", mockAuthRequest),
      ).rejects.toThrow(ForbiddenException);
    });
  });

  describe("getStories", () => {
    it("应该获取用户自己的故事", async () => {
      const query: StoryQueryDto = { authorId: "user-1", page: 1, limit: 10 };

      mockStoriesService.getUserStories.mockResolvedValue(
        mockPaginatedResponse,
      );

      const result = await controller.getStories(query, mockAuthRequest);

      expect(mockStoriesService.getUserStories).toHaveBeenCalledWith(
        "user-1",
        query,
      );
      expect(result.success).toBe(true);
      expect(result.message).toBe("获取故事列表成功");
      expect(result.data).toBe(mockPaginatedResponse);
    });

    it("应该获取其他用户的可访问故事", async () => {
      const query: StoryQueryDto = {
        authorId: "other-user",
        page: 1,
        limit: 10,
      };

      mockStoriesService.getAccessibleStories.mockResolvedValue(
        mockPaginatedResponse,
      );

      await controller.getStories(query, mockAuthRequest);

      expect(mockStoriesService.getAccessibleStories).toHaveBeenCalledWith(
        query,
        "user-1",
      );
    });

    it("应该获取可访问的故事列表（无指定作者）", async () => {
      const query: StoryQueryDto = { page: 1, limit: 10 };

      mockStoriesService.getAccessibleStories.mockResolvedValue(
        mockPaginatedResponse,
      );

      await controller.getStories(query, mockAuthRequest);

      expect(mockStoriesService.getAccessibleStories).toHaveBeenCalledWith(
        query,
        "user-1",
      );
    });
  });

  describe("getPublicStories", () => {
    it("应该获取公开故事列表", async () => {
      const query: StoryQueryDto = { page: 1, limit: 10 };

      mockStoriesService.getPublicStories.mockResolvedValue(
        mockPaginatedResponse,
      );

      const result = await controller.getPublicStories(mockAuthRequest, query);

      expect(mockStoriesService.getPublicStories).toHaveBeenCalledWith(
        query,
        "user-1",
      );
      expect(result.success).toBe(true);
      expect(result.message).toBe("获取公开故事列表成功");
      expect(result.data).toBe(mockPaginatedResponse);
    });
  });

  describe("getMyStories", () => {
    it("应该获取我的故事列表", async () => {
      const query: StoryQueryDto = { page: 1, limit: 10 };

      mockStoriesService.getUserStories.mockResolvedValue(
        mockPaginatedResponse,
      );

      const result = await controller.getMyStories(query, mockAuthRequest);

      expect(mockStoriesService.getUserStories).toHaveBeenCalledWith(
        "user-1",
        query,
      );
      expect(result.success).toBe(true);
      expect(result.message).toBe("获取我的故事列表成功");
      expect(result.data).toBe(mockPaginatedResponse);
    });
  });

  describe("likeStory", () => {
    it("应该成功点赞故事", async () => {
      const allowLikesStory = {
        ...mockStory,
        allowLikes: true,
      } as unknown as Story;

      mockStoriesService.validateStoryAccess.mockResolvedValue(true);
      mockStoriesService.findById.mockResolvedValue(allowLikesStory);
      mockStoriesService.incrementLikeCount.mockResolvedValue(undefined);

      const result = await controller.likeStory(mockAuthRequest, "story-1");

      expect(mockStoriesService.validateStoryAccess).toHaveBeenCalledWith(
        "story-1",
        "user-1",
      );
      expect(mockStoriesService.findById).toHaveBeenCalledWith("story-1");
      expect(mockStoriesService.incrementLikeCount).toHaveBeenCalledWith(
        "story-1",
      );
      expect(result.success).toBe(true);
      expect(result.message).toBe("点赞成功");
    });

    it("应该在无访问权限时抛出错误", async () => {
      mockStoriesService.validateStoryAccess.mockResolvedValue(false);

      await expect(
        controller.likeStory(mockAuthRequest, "story-1"),
      ).rejects.toThrow(ForbiddenException);
    });

    it("应该在不允许点赞时抛出错误", async () => {
      const noLikesStory = {
        ...mockStory,
        allowLikes: false,
      } as unknown as Story;

      mockStoriesService.validateStoryAccess.mockResolvedValue(true);
      mockStoriesService.findById.mockResolvedValue(noLikesStory);

      await expect(
        controller.likeStory(mockAuthRequest, "story-1"),
      ).rejects.toThrow(ForbiddenException);
    });
  });

  describe("publishStory", () => {
    it("应该成功发布故事", async () => {
      const publishedStory = { ...mockStory, status: 2 } as unknown as Story;

      mockStoriesService.updateStory.mockResolvedValue(publishedStory);

      const result = await controller.publishStory("story-1", mockAuthRequest);

      expect(mockStoriesService.updateStory).toHaveBeenCalledWith(
        "story-1",
        { status: 2 },
        "user-1",
      );
      expect(result.success).toBe(true);
      expect(result.message).toBe("故事发布成功");
    });
  });

  describe("archiveStory", () => {
    it("应该成功归档故事", async () => {
      const archivedStory = { ...mockStory, status: 3 } as unknown as Story;

      mockStoriesService.updateStory.mockResolvedValue(archivedStory);

      const result = await controller.archiveStory("story-1", mockAuthRequest);

      expect(mockStoriesService.updateStory).toHaveBeenCalledWith(
        "story-1",
        { status: 3 },
        "user-1",
      );
      expect(result.success).toBe(true);
      expect(result.message).toBe("故事归档成功");
    });
  });

  describe("uploadStoryCover", () => {
    it("应该成功上传封面", async () => {
      const mockFile = {
        mimetype: "image/jpeg",
        toBuffer: jest.fn().mockResolvedValue(Buffer.alloc(1024)), // 1KB文件
      };

      mockStoriesService.updateStory.mockResolvedValue(
        mockStory as unknown as Story,
      );

      const result = await controller.uploadStoryCover(
        mockAuthRequest,
        "story-1",
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        mockFile as any,
      );

      expect(result.success).toBe(true);
      expect(result.message).toBe("封面上传成功");
      expect(result.data.coverUrl).toBeDefined();
    });

    it("应该在没有文件时抛出错误", async () => {
      await expect(
        controller.uploadStoryCover(
          mockAuthRequest,
          "story-1",
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          null as unknown as any,
        ),
      ).rejects.toThrow(BadRequestException);
    });

    it("应该在文件类型不支持时抛出错误", async () => {
      const mockFile = {
        mimetype: "text/plain",
        toBuffer: jest.fn().mockResolvedValue(Buffer.alloc(1024)),
      };

      await expect(
        controller.uploadStoryCover(
          mockAuthRequest,
          "story-1",
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          mockFile as any,
        ),
      ).rejects.toThrow(BadRequestException);
    });

    it("应该在文件过大时抛出错误", async () => {
      const mockFile = {
        mimetype: "image/jpeg",
        toBuffer: jest.fn().mockResolvedValue(Buffer.alloc(6 * 1024 * 1024)), // 6MB文件
      };

      await expect(
        controller.uploadStoryCover(
          mockAuthRequest,
          "story-1",
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          mockFile as any,
        ),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe("validateContent", () => {
    it("应该成功验证内容", async () => {
      const validationResult = {
        isValid: true,
        aiSafetyScore: 95.5,
        violations: [],
        suggestions: [],
      };

      mockStoriesService.validateStoryContent.mockResolvedValue(
        validationResult,
      );

      const result = await controller.validateContent(mockAuthRequest, {
        content: "测试内容",
      });

      expect(mockStoriesService.validateStoryContent).toHaveBeenCalledWith(
        "测试内容",
      );
      expect(result.success).toBe(true);
      expect(result.message).toBe("内容验证完成");
      expect(result.data).toBe(validationResult);
    });

    it("应该返回验证建议", async () => {
      const validationResult = {
        isValid: false,
        aiSafetyScore: 45.0,
        violations: ["内容过短"],
        suggestions: ["内容过短", "缺少关键信息"],
      };

      mockStoriesService.validateStoryContent.mockResolvedValue(
        validationResult,
      );

      const result = await controller.validateContent(mockAuthRequest, {
        content: "短内容",
      });

      expect(result.data.isValid).toBe(false);
      expect(result.data.suggestions).toEqual(["内容过短", "缺少关键信息"]);
    });
  });

  it("应该正确注入StoriesService", () => {
    expect(controller).toBeDefined();
    expect(mockStoriesService).toBeDefined();
  });
});
