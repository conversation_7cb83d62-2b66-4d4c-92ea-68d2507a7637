<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="jest tests" tests="48" failures="1" errors="0" time="0.977">
  <testsuite name="SocialController - 企业级单元测试" errors="0" failures="1" skipped="0" timestamp="2025-07-24T12:23:35" time="0.835" tests="48">
    <testcase classname="SocialController - 企业级单元测试 › followUser - POST /social/follow" name="应该成功关注用户" time="0.011">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › followUser - POST /social/follow" name="应该正确传递关注设置" time="0.005">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › followUser - POST /social/follow" name="应该正确处理服务层错误" time="0.016">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › followUser - POST /social/follow" name="应该正确处理重复关注错误" time="0.006">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › followUser - POST /social/follow" name="应该正确处理用户不存在错误" time="0.005">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › unfollowUser - DELETE /social/follow/:userId" name="应该成功取消关注" time="0.003">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › unfollowUser - DELETE /social/follow/:userId" name="应该正确处理关注关系不存在的错误" time="0.002">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › getFollowingList - GET /social/following/:userId" name="应该成功获取关注列表" time="0.006">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › getFollowingList - GET /social/following/:userId" name="应该正确传递查询参数" time="0.002">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › getFollowingList - GET /social/following/:userId" name="应该处理空结果" time="0.004">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › getFollowingList - GET /social/following/:userId" name="应该正确处理分页参数" time="0.002">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › getFollowersList - GET /social/followers/:userId" name="应该成功获取粉丝列表" time="0.003">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › getFollowersList - GET /social/followers/:userId" name="应该支持搜索功能" time="0.002">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › createFriendGroup - POST /social/groups" name="应该成功创建好友分组" time="0.001">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › createFriendGroup - POST /social/groups" name="应该正确处理最小参数创建" time="0.001">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › createFriendGroup - POST /social/groups" name="应该正确处理不同分组类型" time="0.002">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › createFriendGroup - POST /social/groups" name="应该正确处理分组名重复错误" time="0.001">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › getFriendGroups - GET /social/groups" name="应该成功获取分组列表" time="0.001">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › getFriendGroups - GET /social/groups" name="应该正确传递筛选条件" time="0.002">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › getFriendGroups - GET /social/groups" name="应该处理空分组列表" time="0.003">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › getFriendGroups - GET /social/groups" name="应该正确处理排序参数" time="0.002">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › getGroupMembers - GET /social/groups/:groupId/members" name="应该成功获取分组成员" time="0.001">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › getGroupMembers - GET /social/groups/:groupId/members" name="应该正确传递查询参数" time="0.003">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › getGroupMembers - GET /social/groups/:groupId/members" name="应该正确处理角色筛选" time="0.003">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › getGroupMembers - GET /social/groups/:groupId/members" name="应该正确处理分组不存在错误" time="0.001">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › getGroupMembers - GET /social/groups/:groupId/members" name="应该正确处理权限不足错误" time="0.001">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › addMembersToGroup - POST /social/groups/:groupId/members" name="应该成功添加成员到分组" time="0.001">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › addMembersToGroup - POST /social/groups/:groupId/members" name="应该正确处理部分添加成功的情况" time="0.001">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › addMembersToGroup - POST /social/groups/:groupId/members" name="应该正确处理没有新增成员的情况" time="0.006">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › addMembersToGroup - POST /social/groups/:groupId/members" name="应该正确处理分组不存在错误" time="0.001">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › addMembersToGroup - POST /social/groups/:groupId/members" name="应该正确处理权限不足错误" time="0.001">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › addMembersToGroup - POST /social/groups/:groupId/members" name="应该正确处理用户不存在错误" time="0.002">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › getSocialStats - GET /social/stats/:userId" name="应该成功获取社交统计数据" time="0.001">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › getSocialStats - GET /social/stats/:userId" name="应该正确返回完整的统计数据" time="0.001">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › getSocialStats - GET /social/stats/:userId" name="应该正确处理空统计数据" time="0.001">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › getSocialStats - GET /social/stats/:userId" name="应该正确返回热门分组信息" time="0.001">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › getSocialStats - GET /social/stats/:userId" name="应该正确返回互动用户信息" time="0.001">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › 错误处理和边界测试" name="应该正确处理服务层抛出的各种异常" time="0.004">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › 错误处理和边界测试" name="应该处理无效的UUID格式" time="0.001">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › 错误处理和边界测试" name="应该处理请求中缺少用户信息的情况" time="0.005">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › 错误处理和边界测试" name="应该处理空的初始成员列表" time="0.001">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › ApiResponse 格式验证" name="所有成功响应应该包含正确的 ApiResponse 格式" time="0.001">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › ApiResponse 格式验证" name="应该包含正确的HTTP状态码" time="0.001">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › 关注设置处理" name="应该正确处理不同的关注设置" time="0.001">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › 分组权限处理" name="应该正确处理不同的分组权限设置" time="0.001">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › 分页参数处理" name="应该正确处理默认分页参数" time="0.003">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: &quot;user-001&quot;, ObjectContaining {&quot;limit&quot;: undefined, &quot;page&quot;: undefined}, &quot;user-001&quot;
Received: &quot;user-001&quot;, {}, &quot;user-001&quot;

Number of calls: 1
    at Object.&lt;anonymous&gt; (/Users/<USER>/Desktop/YGS/ygs-v1.0.0/AppServe/src/modules/social/social.controller.spec.ts:1315:50)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › 分页参数处理" name="应该正确验证分页参数范围" time="0.003">
    </testcase>
    <testcase classname="SocialController - 企业级单元测试 › 分页参数处理" name="应该正确处理大页码" time="0.001">
    </testcase>
  </testsuite>
</testsuites>