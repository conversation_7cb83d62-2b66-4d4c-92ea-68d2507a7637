/**
 * 企业级社交关系种子数据
 *
 * 基于 YGS v1.0.0 社交系统设计
 * 涵盖关注、好友、分组等关系
 */

import { USER_ROLES } from "./enhanced-user.seeder";
import { FollowStatus } from "../../modules/social/entities/user-follow.entity";

export interface EnhancedFollowSeedData {
  followerUsername: string;
  followingUsername: string;
  status: FollowStatus;
  isSpecial: boolean;
  note?: string;
}

export interface EnhancedFriendGroupSeedData {
  ownerUsername: string;
  groupName: string;
  description?: string;
  members: string[]; // 用户名数组
}

export const ENHANCED_FOLLOW_SEED_DATA: EnhancedFollowSeedData[] = [
  // ========== 互相关注（好友关系） ==========
  {
    followerUsername: USER_ROLES.ZHANG_MINGYUAN,
    followingUsername: USER_ROLES.LI_XIAOHONG,
    status: FollowStatus.ACTIVE,
    isSpecial: true,
    note: "点亮了我故事中的李明",
  },
  {
    followerUsername: USER_ROLES.LI_XIAOHONG,
    followingUsername: USER_ROLES.ZHANG_MINGYUAN,
    status: FollowStatus.ACTIVE,
    isSpecial: true,
    note: "写出了感人的故事",
  },
  {
    followerUsername: USER_ROLES.ZHANG_MINGYUAN,
    followingUsername: USER_ROLES.WANG_JIANGUO,
    status: FollowStatus.ACTIVE,
    isSpecial: false,
  },
  {
    followerUsername: USER_ROLES.WANG_JIANGUO,
    followingUsername: USER_ROLES.ZHANG_MINGYUAN,
    status: FollowStatus.ACTIVE,
    isSpecial: false,
  },
  {
    followerUsername: USER_ROLES.LI_XIAOHONG,
    followingUsername: USER_ROLES.ZHOU_FAXIAN,
    status: FollowStatus.ACTIVE,
    isSpecial: true,
    note: "点亮达人，互相学习",
  },
  {
    followerUsername: USER_ROLES.ZHOU_FAXIAN,
    followingUsername: USER_ROLES.LI_XIAOHONG,
    status: FollowStatus.ACTIVE,
    isSpecial: true,
    note: "优秀的点亮者",
  },

  // ========== 单向关注 ==========
  {
    followerUsername: USER_ROLES.ZHAO_XIAOFANG,
    followingUsername: USER_ROLES.ZHANG_MINGYUAN,
    status: FollowStatus.ACTIVE,
    isSpecial: false,
    note: "喜欢他的故事",
  },
  {
    followerUsername: USER_ROLES.ZHAO_XIAOFANG,
    followingUsername: USER_ROLES.WANG_JIANGUO,
    status: FollowStatus.ACTIVE,
    isSpecial: false,
    note: "老师的故事很感人",
  },
  {
    followerUsername: USER_ROLES.LIU_XIAOXIN,
    followingUsername: USER_ROLES.ZHANG_MINGYUAN,
    status: FollowStatus.ACTIVE,
    isSpecial: false,
  },
  {
    followerUsername: USER_ROLES.LIU_XIAOXIN,
    followingUsername: USER_ROLES.LI_XIAOHONG,
    status: FollowStatus.ACTIVE,
    isSpecial: false,
  },
  {
    followerUsername: USER_ROLES.CHEN_VIP,
    followingUsername: USER_ROLES.ZHANG_MINGYUAN,
    status: FollowStatus.ACTIVE,
    isSpecial: true,
    note: "VIP特别关注",
  },
  {
    followerUsername: USER_ROLES.CHEN_VIP,
    followingUsername: USER_ROLES.WANG_JIANGUO,
    status: FollowStatus.ACTIVE,
    isSpecial: true,
    note: "VIP特别关注",
  },
  {
    followerUsername: USER_ROLES.CHEN_VIP,
    followingUsername: USER_ROLES.WU_SIMI,
    status: FollowStatus.ACTIVE,
    isSpecial: false,
  },

  // ========== 屏蔽关系 ==========
  {
    followerUsername: USER_ROLES.ZHANG_MINGYUAN,
    followingUsername: USER_ROLES.SUN_WENTI,
    status: FollowStatus.BLOCKED,
    isSpecial: false,
    note: "异常用户",
  },
  {
    followerUsername: USER_ROLES.WANG_JIANGUO,
    followingUsername: USER_ROLES.SUN_WENTI,
    status: FollowStatus.BLOCKED,
    isSpecial: false,
  },

  // ========== 静音关系 ==========
  {
    followerUsername: USER_ROLES.LI_XIAOHONG,
    followingUsername: USER_ROLES.SYSTEM_TEST,
    status: FollowStatus.MUTED,
    isSpecial: false,
    note: "系统测试账号",
  },
];

export const ENHANCED_FRIEND_GROUP_SEED_DATA: EnhancedFriendGroupSeedData[] = [
  // 张明远的分组
  {
    ownerUsername: USER_ROLES.ZHANG_MINGYUAN,
    groupName: "大学兄弟",
    description: "大学时期的室友和好友",
    members: [USER_ROLES.LI_XIAOHONG, USER_ROLES.ZHOU_FAXIAN],
  },
  {
    ownerUsername: USER_ROLES.ZHANG_MINGYUAN,
    groupName: "创业伙伴",
    description: "一起创业的朋友们",
    members: [USER_ROLES.CHEN_VIP],
  },
  {
    ownerUsername: USER_ROLES.ZHANG_MINGYUAN,
    groupName: "同行交流",
    description: "同样爱写故事的朋友",
    members: [USER_ROLES.WANG_JIANGUO, USER_ROLES.WU_SIMI],
  },

  // 王建国的分组
  {
    ownerUsername: USER_ROLES.WANG_JIANGUO,
    groupName: "教师同行",
    description: "教育界的朋友",
    members: [],
  },
  {
    ownerUsername: USER_ROLES.WANG_JIANGUO,
    groupName: "优秀学生",
    description: "曾经教过的优秀学生",
    members: [USER_ROLES.ZHOU_FAXIAN],
  },

  // 李小红的分组
  {
    ownerUsername: USER_ROLES.LI_XIAOHONG,
    groupName: "点亮好友",
    description: "通过点亮认识的朋友",
    members: [USER_ROLES.ZHANG_MINGYUAN, USER_ROLES.ZHOU_FAXIAN],
  },
  {
    ownerUsername: USER_ROLES.LI_XIAOHONG,
    groupName: "大学闺蜜",
    description: "大学时期的好姐妹",
    members: [],
  },

  // 吴私密的分组
  {
    ownerUsername: USER_ROLES.WU_SIMI,
    groupName: "家人",
    description: "最亲密的家人",
    members: [],
  },
  {
    ownerUsername: USER_ROLES.WU_SIMI,
    groupName: "密友",
    description: "可以分享秘密的朋友",
    members: [USER_ROLES.LI_XIAOHONG],
  },

  // VIP用户的分组
  {
    ownerUsername: USER_ROLES.CHEN_VIP,
    groupName: "优质创作者",
    description: "平台上的优秀创作者",
    members: [
      USER_ROLES.ZHANG_MINGYUAN,
      USER_ROLES.WANG_JIANGUO,
      USER_ROLES.WU_SIMI,
    ],
  },
  {
    ownerUsername: USER_ROLES.CHEN_VIP,
    groupName: "商务合作",
    description: "潜在的商务合作伙伴",
    members: [USER_ROLES.ZHANG_MINGYUAN],
  },
];

// 导出社交关系统计
export const SOCIAL_STATS = {
  totalFollows: ENHANCED_FOLLOW_SEED_DATA.length,
  activeFollows: ENHANCED_FOLLOW_SEED_DATA.filter(
    (f) => f.status === FollowStatus.ACTIVE,
  ).length,
  blockedFollows: ENHANCED_FOLLOW_SEED_DATA.filter(
    (f) => f.status === FollowStatus.BLOCKED,
  ).length,
  mutedFollows: ENHANCED_FOLLOW_SEED_DATA.filter(
    (f) => f.status === FollowStatus.MUTED,
  ).length,
  specialFollows: ENHANCED_FOLLOW_SEED_DATA.filter((f) => f.isSpecial).length,
  totalGroups: ENHANCED_FRIEND_GROUP_SEED_DATA.length,

  // 计算好友对数（互相关注）
  friendPairs: (() => {
    const pairs = new Set<string>();
    ENHANCED_FOLLOW_SEED_DATA.forEach((f1) => {
      if (f1.status === FollowStatus.ACTIVE) {
        const reverse = ENHANCED_FOLLOW_SEED_DATA.find(
          (f2) =>
            f2.followerUsername === f1.followingUsername &&
            f2.followingUsername === f1.followerUsername &&
            f2.status === FollowStatus.ACTIVE,
        );
        if (reverse) {
          const pair = [f1.followerUsername, f1.followingUsername]
            .sort()
            .join("-");
          pairs.add(pair);
        }
      }
    });
    return pairs.size;
  })(),
};
