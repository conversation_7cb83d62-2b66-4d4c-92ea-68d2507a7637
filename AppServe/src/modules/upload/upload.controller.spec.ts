import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { BadRequestException } from "@nestjs/common";
import { UploadController } from "./upload.controller";
import { UploadService, UploadType } from "./upload.service";
import type { UploadResult } from "./upload.service";
import type { AuthRequest } from "../../common/types/request.types";

// Mock Request interface for upload testing
interface MockUploadRequest extends AuthRequest {
  file: () => Promise<{
    buffer: Buffer;
    mimetype: string;
    originalname: string;
    size: number;
    filename?: string;
    encoding?: string;
    toBuffer?: () => Promise<Buffer>;
  }>;
}

describe("UploadController", () => {
  let controller: UploadController;
  let mockUploadService: jest.Mocked<UploadService>;

  // Mock数据
  const mockUser = { id: "user-1", username: "testuser", userNumber: "100001" };

  const mockUploadResult: UploadResult = {
    url: "https://ygs-storage.oss-cn-hangzhou.aliyuncs.com/temp/2025/07/17/uuid-test.jpg?signature=xxx",
    filename: "temp/2025/07/17/uuid-test.jpg",
    size: 1024000,
    mimeType: "image/jpeg",
  };

  const mockFileData = {
    buffer: Buffer.from("mock file content"),
    mimetype: "image/jpeg",
    originalname: "test.jpg",
    size: 1024000,
    filename: "test.jpg",
    encoding: "binary",
    toBuffer: jest.fn().mockResolvedValue(Buffer.from("mock file content")),
  };

  beforeEach(async () => {
    const mockServiceMethods = {
      uploadFile: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [UploadController],
      providers: [
        {
          provide: UploadService,
          useValue: mockServiceMethods,
        },
      ],
    }).compile();

    controller = module.get<UploadController>(UploadController);
    mockUploadService = module.get(UploadService) as jest.Mocked<UploadService>;

    // Mock console methods to avoid output during tests
    jest.spyOn(console, "log").mockImplementation(() => {});
    jest.spyOn(console, "error").mockImplementation(() => {});
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("uploadSingle", () => {
    it("应该成功上传文件", async () => {
      const mockRequest = {
        user: mockUser,
        file: jest.fn().mockResolvedValue(mockFileData),
      } as MockUploadRequest;

      mockUploadService.uploadFile.mockResolvedValue(mockUploadResult);

      const result = await controller.uploadSingle(mockRequest);

      expect(mockRequest.file).toHaveBeenCalled();
      expect(mockUploadService.uploadFile).toHaveBeenCalledWith(
        mockFileData.buffer,
        "test.jpg",
        "image/jpeg",
        UploadType.TEMP,
        "user-1",
      );
      expect(result.success).toBe(true);
      expect(result.message).toBe("文件上传成功");
      expect(result.data).toBe(mockUploadResult);
      expect(result.statusCode).toBe(200);
    });

    it("应该使用toBuffer方法获取文件数据", async () => {
      const fileDataWithToBuffer = {
        ...mockFileData,
        buffer: undefined as unknown as Buffer,
        toBuffer: jest.fn().mockResolvedValue(Buffer.from("toBuffer content")),
      };

      const mockRequest = {
        user: mockUser,
        file: jest.fn().mockResolvedValue(fileDataWithToBuffer),
      } as MockUploadRequest;

      mockUploadService.uploadFile.mockResolvedValue(mockUploadResult);

      await controller.uploadSingle(mockRequest);

      expect(fileDataWithToBuffer.toBuffer).toHaveBeenCalled();
      expect(mockUploadService.uploadFile).toHaveBeenCalledWith(
        Buffer.from("toBuffer content"),
        "test.jpg",
        "image/jpeg",
        UploadType.TEMP,
        "user-1",
      );
    });

    it("应该使用originalname当filename不存在时", async () => {
      const fileDataWithoutFilename = {
        ...mockFileData,
        filename: undefined,
      };

      const mockRequest = {
        user: mockUser,
        file: jest.fn().mockResolvedValue(fileDataWithoutFilename),
      } as MockUploadRequest;

      mockUploadService.uploadFile.mockResolvedValue(mockUploadResult);

      await controller.uploadSingle(mockRequest);

      expect(mockUploadService.uploadFile).toHaveBeenCalledWith(
        mockFileData.buffer,
        "test.jpg", // 使用originalname
        "image/jpeg",
        UploadType.TEMP,
        "user-1",
      );
    });

    it("应该处理匿名用户", async () => {
      const mockRequest = {
        user: undefined,
        file: jest.fn().mockResolvedValue(mockFileData),
      } as MockUploadRequest;

      mockUploadService.uploadFile.mockResolvedValue(mockUploadResult);

      await controller.uploadSingle(mockRequest);

      expect(mockUploadService.uploadFile).toHaveBeenCalledWith(
        mockFileData.buffer,
        "test.jpg",
        "image/jpeg",
        UploadType.TEMP,
        "anonymous",
      );
    });

    it("应该在没有文件时抛出错误", async () => {
      const mockRequest = {
        user: mockUser,
        file: jest.fn().mockResolvedValue(null),
      } as MockUploadRequest;

      await expect(controller.uploadSingle(mockRequest)).rejects.toThrow(
        BadRequestException,
      );
      await expect(controller.uploadSingle(mockRequest)).rejects.toThrow(
        "请选择要上传的文件",
      );
    });

    it("应该处理上传服务错误", async () => {
      const mockRequest = {
        user: mockUser,
        file: jest.fn().mockResolvedValue(mockFileData),
      } as MockUploadRequest;

      const error = new Error("OSS连接失败");
      mockUploadService.uploadFile.mockRejectedValue(error);

      await expect(controller.uploadSingle(mockRequest)).rejects.toThrow(
        BadRequestException,
      );
      await expect(controller.uploadSingle(mockRequest)).rejects.toThrow(
        "文件上传失败: OSS连接失败",
      );
    });

    it("应该传递BadRequestException不做修改", async () => {
      const mockRequest = {
        user: mockUser,
        file: jest.fn().mockResolvedValue(mockFileData),
      } as MockUploadRequest;

      const badRequestError = new BadRequestException("文件类型不支持");
      mockUploadService.uploadFile.mockRejectedValue(badRequestError);

      await expect(controller.uploadSingle(mockRequest)).rejects.toThrow(
        badRequestError,
      );
    });

    it("应该处理文件读取错误", async () => {
      const mockRequest = {
        user: mockUser,
        file: jest.fn().mockRejectedValue(new Error("文件读取失败")),
      } as MockUploadRequest;

      await expect(controller.uploadSingle(mockRequest)).rejects.toThrow(
        BadRequestException,
      );
      await expect(controller.uploadSingle(mockRequest)).rejects.toThrow(
        "文件上传失败: 文件读取失败",
      );
    });

    it("应该记录调试日志", async () => {
      const mockRequest = {
        user: mockUser,
        file: jest.fn().mockResolvedValue(mockFileData),
      } as MockUploadRequest;

      mockUploadService.uploadFile.mockResolvedValue(mockUploadResult);

      await controller.uploadSingle(mockRequest);

      expect(console.log).toHaveBeenCalledWith("开始处理文件上传...");
      expect(console.log).toHaveBeenCalledWith("文件信息:", {
        filename: "test.jpg",
        mimetype: "image/jpeg",
        encoding: "binary",
      });
      expect(console.log).toHaveBeenCalledWith(
        "文件缓冲区大小:",
        mockFileData.buffer.length,
      );
      expect(console.log).toHaveBeenCalledWith("用户ID:", "user-1");
    });

    it("应该处理未知编码", async () => {
      const fileDataWithoutEncoding = {
        ...mockFileData,
        encoding: undefined,
      };

      const mockRequest = {
        user: mockUser,
        file: jest.fn().mockResolvedValue(fileDataWithoutEncoding),
      } as MockUploadRequest;

      mockUploadService.uploadFile.mockResolvedValue(mockUploadResult);

      await controller.uploadSingle(mockRequest);

      expect(console.log).toHaveBeenCalledWith("文件信息:", {
        filename: "test.jpg",
        mimetype: "image/jpeg",
        encoding: "unknown",
      });
    });

    it("应该处理非Error类型的错误", async () => {
      const mockRequest = {
        user: mockUser,
        file: jest.fn().mockResolvedValue(mockFileData),
      } as MockUploadRequest;

      mockUploadService.uploadFile.mockRejectedValue("字符串错误");

      await expect(controller.uploadSingle(mockRequest)).rejects.toThrow(
        "文件上传失败: 字符串错误",
      );
    });
  });

  it("应该正确注入UploadService", () => {
    expect(controller).toBeDefined();
    expect(mockUploadService).toBeDefined();
  });
});
