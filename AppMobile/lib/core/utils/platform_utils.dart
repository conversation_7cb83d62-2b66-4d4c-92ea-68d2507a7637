import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 跨平台工具类 - 解决Chrome和真机间距差异问题
class PlatformUtils {
  /// 检测是否为Web环境
  static bool get isWeb => kIsWeb;
  
  /// 检测是否为移动端
  static bool get isMobile => !kIsWeb;
  
  /// 获取跨平台适配的高度
  /// 
  /// Web环境: 使用视口比例计算
  /// 移动端: 使用ScreenUtil计算
  static double getResponsiveHeight(BuildContext context, double designHeight) {
    if (isWeb) {
      // Web环境使用屏幕高度比例，避免SafeArea差异
      final screenHeight = MediaQuery.of(context).size.height;
      return screenHeight * (designHeight / 1624.0);
    } else {
      // 移动端使用ScreenUtil
      return designHeight.h;
    }
  }
  
  /// 获取跨平台适配的宽度
  static double getResponsiveWidth(BuildContext context, double designWidth) {
    if (isWeb) {
      final screenWidth = MediaQuery.of(context).size.width;
      return screenWidth * (designWidth / 750.0);
    } else {
      return designWidth.w;
    }
  }
  
  /// 获取跨平台适配的字体大小
  static double getResponsiveFontSize(BuildContext context, double designFontSize) {
    if (isWeb) {
      final screenWidth = MediaQuery.of(context).size.width;
      return (screenWidth / 750.0) * designFontSize;
    } else {
      return designFontSize.sp;
    }
  }
  
  /// 获取跨平台适配的圆角
  static double getResponsiveRadius(BuildContext context, double designRadius) {
    if (isWeb) {
      final screenWidth = MediaQuery.of(context).size.width;
      return (screenWidth / 750.0) * designRadius;
    } else {
      return designRadius.r;
    }
  }
}

/// 跨平台SafeArea组件
class PlatformSafeArea extends StatelessWidget {
  final Widget child;
  final bool top;
  final bool bottom;
  final bool left;
  final bool right;
  
  const PlatformSafeArea({
    super.key,
    required this.child,
    this.top = true,
    this.bottom = true,
    this.left = true,
    this.right = true,
  });

  @override
  Widget build(BuildContext context) {
    if (PlatformUtils.isWeb) {
      // Web环境使用固定间距，避免SafeArea计算差异
      return Padding(
        padding: EdgeInsets.only(
          top: top ? PlatformUtils.getResponsiveHeight(context, 20) : 0.0,
          bottom: bottom ? PlatformUtils.getResponsiveHeight(context, 20) : 0.0,
          left: left ? PlatformUtils.getResponsiveWidth(context, 0) : 0.0,
          right: right ? PlatformUtils.getResponsiveWidth(context, 0) : 0.0,
        ),
        child: child,
      );
    } else {
      // 移动端使用原生SafeArea
      return SafeArea(
        top: top,
        bottom: bottom,
        left: left,
        right: right,
        child: child,
      );
    }
  }
}

/// 跨平台间距工具
class PlatformSpacing {
  /// 获取搜索框到Tab的标准间距
  static double getSearchToTabSpacing(BuildContext context) {
    if (PlatformUtils.isWeb) {
      // Web环境使用固定的小间距
      return PlatformUtils.getResponsiveHeight(context, 12);
    } else {
      // 移动端使用设计稿标准间距
      return PlatformUtils.getResponsiveHeight(context, 8);
    }
  }
  
  /// 获取Tab之间的间距
  static double getTabInternalSpacing(BuildContext context) {
    if (PlatformUtils.isWeb) {
      return PlatformUtils.getResponsiveHeight(context, 8);
    } else {
      return PlatformUtils.getResponsiveHeight(context, 5);
    }
  }
  
  /// 获取搜索框的内部间距
  static EdgeInsets getSearchBarPadding(BuildContext context) {
    return EdgeInsets.fromLTRB(
      PlatformUtils.getResponsiveWidth(context, 36),
      PlatformUtils.getResponsiveHeight(context, 20),
      PlatformUtils.getResponsiveWidth(context, 36),
      getSearchToTabSpacing(context),
    );
  }
}