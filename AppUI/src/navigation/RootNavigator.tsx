/**
 * 有故事 APP - 根导航器
 * 
 * 功能特性：
 * - React Navigation 集成
 * - Stack 导航
 * - 类型安全导航
 * - 企业级架构设计
 */

import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

// 导入屏幕组件
import SplashScreen from '../screens/auth/SplashScreen';
import AuthScreen from '../screens/auth/AuthScreen';
import MainTabNavigator from '../screens/MainTabNavigator';

// ===== 导航类型定义 =====
export type RootStackParamList = {
  Splash: undefined;
  Auth: undefined;
  Main: undefined;
};

// 创建 Stack Navigator
const Stack = createNativeStackNavigator<RootStackParamList>();

/**
 * 根导航器组件
 * 
 * 负责：
 * - 应用级导航配置
 * - 屏幕路由管理
 * - 导航主题配置
 */
const RootNavigator: React.FC = () => {
  return (
    <NavigationContainer>
      <Stack.Navigator
        initialRouteName="Splash"
        screenOptions={{
          headerShown: false,
          headerTitleStyle: {
            fontWeight: '600',
          },
        }}
      >
        <Stack.Screen
          name="Splash"
          component={SplashScreen}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="Auth"
          component={AuthScreen}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="Main"
          component={MainTabNavigator}
          options={{
            headerShown: false,
          }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default RootNavigator; 