#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

echo "🚀 YGS 后端代码质量检查..."

# 获取暂存的后端文件
BACKEND_FILES=$(git diff --cached --name-only | grep "^AppServe/" || true)

# 如果没有后端文件变更，跳过检查
if [ -z "$BACKEND_FILES" ]; then
  echo "📝 没有后端代码变更，跳过质量检查"
  echo "✅ 提交检查完成"
  exit 0
fi

echo "📁 检测到后端文件变更: $(echo "$BACKEND_FILES" | wc -l | xargs) 个"

# 进入后端目录执行检查
cd AppServe

# 1. ESLint代码规范检查
echo "🔍 执行ESLint代码规范检查..."
npx lint-staged
if [ $? -ne 0 ]; then
  echo "❌ ESLint检查失败 - 请修复代码规范问题"
  exit 1
fi

# 2. TypeScript类型检查
echo "🔧 执行TypeScript类型检查..."
npm run type-check
if [ $? -ne 0 ]; then
  echo "❌ TypeScript类型检查失败 - 请修复类型错误"
  exit 1
fi

# 3. 可选：单元测试检查（如果需要）
# echo "🧪 执行单元测试..."
# npm run test:staged
# if [ $? -ne 0 ]; then
#   echo "❌ 单元测试失败"
#   exit 1
# fi

cd ..

echo "✅ 后端代码质量检查通过"
echo "🎉 提交检查完成！"