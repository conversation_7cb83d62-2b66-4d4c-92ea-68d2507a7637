/* eslint-disable @typescript-eslint/no-explicit-any */
import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import type { Repository } from "typeorm";
import { getRepositoryToken } from "@nestjs/typeorm";
import { NotFoundException } from "@nestjs/common";

import { HomepageService } from "./homepage.service";
import { User } from "../users/entities/user.entity";
import { Story } from "../stories/entities/story.entity";
import { Character } from "../characters/entities/character.entity";
import { Bookmark } from "../bookmarks/entities/bookmark.entity";
import { TimelineEvent } from "../timeline/entities/timeline-event.entity";
import { ReferenceCollection } from "../story-references/entities/reference-collection.entity";
import { UserFollow } from "../social/entities/user-follow.entity";
import { FriendGroup } from "../social/entities/friend-group.entity";
import { HomepageTheme, HomepageLayout } from "./dto";
import type {
  UpdateDisplaySettingsDto,
  UpdateHomepageSettingsDto,
} from "./dto";

describe("HomepageService - 企业级单元测试", () => {
  let service: HomepageService;
  let mockUserRepository: jest.Mocked<Repository<User>>;
  let mockStoryRepository: jest.Mocked<Repository<Story>>;
  let mockCharacterRepository: jest.Mocked<Repository<Character>>;
  let mockBookmarkRepository: jest.Mocked<Repository<Bookmark>>;
  let mockTimelineEventRepository: jest.Mocked<Repository<TimelineEvent>>;
  let mockReferenceCollectionRepository: jest.Mocked<
    Repository<ReferenceCollection>
  >;
  let mockUserFollowRepository: jest.Mocked<Repository<UserFollow>>;
  let mockFriendGroupRepository: jest.Mocked<Repository<FriendGroup>>;

  // Mock数据 - 使用具体类型
  const mockUser = {
    id: "test-user-id",
    username: "testuser",
    nickname: "测试用户",
    email: "<EMAIL>",
    avatarUrl: "https://example.com/avatar.jpg",
    birthDate: new Date("1990-01-01"),
    bio: "这是一个测试用户",
    userNumber: "YGS001",
    profileDisplaySettings: {
      showBirthday: true,
      showBio: true,
      showStatistics: true,
      showCharacters: true,
      showTimeline: true,
      showStories: true,
      showBookmarks: false,
      showFollowList: false,
      showReferenceCollections: true,
    },
    homepageSettings: {
      theme: HomepageTheme.AUTO,
      layout: HomepageLayout.MODERN,
      customBio: "个性化简介",
      backgroundImage: "https://example.com/bg.jpg",
      pinnedItems: ["story-1", "character-1"],
      socialLinks: [
        {
          platform: "github",
          url: "https://github.com/testuser",
          display: true,
        },
      ],
    },
    createdAt: new Date("2024-01-01"),
    lastLoginAt: new Date("2025-01-01"),
  };

  const mockStory = {
    id: "test-story-id",
    title: "测试故事",
    authorId: "test-user-id",
    authorName: "测试用户",
    summary: "测试故事摘要",
    publishedAt: new Date("2025-01-01"),
    likesCount: 10,
    viewCount: 100,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockCharacter = {
    id: "test-character-id",
    name: "测试人物",
    userId: "test-user-id",
    isLighted: true,
    category: "主角",
    likesCount: 5,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockTimelineEvent = {
    id: "test-event-id",
    userId: "test-user-id",
    title: "测试事件",
    isMilestone: true,
    createdAt: new Date(),
  };

  const mockBookmark = {
    id: "test-bookmark-id",
    userId: "test-user-id",
    targetType: "story",
    targetId: "test-story-id",
    createdAt: new Date(),
  };

  const mockReferenceCollection = {
    id: "test-collection-id",
    userId: "test-user-id",
    name: "测试引用集合",
    createdAt: new Date(),
  };

  beforeEach(async () => {
    // 创建Repository Mock对象
    const mockUserRepositoryMethods = {
      findOne: jest.fn(),
      save: jest.fn(),
      update: jest.fn(),
      createQueryBuilder: jest.fn().mockReturnValue({
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        getRawOne: jest.fn().mockResolvedValue({}),
      }),
    };

    const mockStoryRepositoryMethods = {
      find: jest.fn(),
      count: jest.fn(),
      createQueryBuilder: jest.fn().mockReturnValue({
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([]),
        select: jest.fn().mockReturnThis(),
        getRawOne: jest.fn().mockResolvedValue({ sum: 0 }),
      }),
    };

    const mockCharacterRepositoryMethods = {
      find: jest.fn(),
      count: jest.fn(),
      createQueryBuilder: jest.fn().mockReturnValue({
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([]),
      }),
    };

    const mockBookmarkRepositoryMethods = {
      find: jest.fn(),
      count: jest.fn(),
      createQueryBuilder: jest.fn().mockReturnValue({
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([]),
      }),
    };

    const mockTimelineEventRepositoryMethods = {
      find: jest.fn(),
      count: jest.fn(),
      createQueryBuilder: jest.fn().mockReturnValue({
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([]),
        select: jest.fn().mockReturnThis(),
        getRawOne: jest.fn().mockResolvedValue({}),
      }),
    };

    const mockReferenceCollectionRepositoryMethods = {
      find: jest.fn(),
      count: jest.fn(),
      createQueryBuilder: jest.fn().mockReturnValue({
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([]),
      }),
    };

    const mockUserFollowRepositoryMethods = {
      findOne: jest.fn(),
      count: jest.fn(),
      createQueryBuilder: jest.fn().mockReturnValue({
        where: jest.fn().mockReturnThis(),
        getCount: jest.fn().mockResolvedValue(0),
      }),
    };

    const mockFriendGroupRepositoryMethods = {
      findOne: jest.fn(),
      createQueryBuilder: jest.fn().mockReturnValue({
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(null),
      }),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        HomepageService,
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepositoryMethods,
        },
        {
          provide: getRepositoryToken(Story),
          useValue: mockStoryRepositoryMethods,
        },
        {
          provide: getRepositoryToken(Character),
          useValue: mockCharacterRepositoryMethods,
        },
        {
          provide: getRepositoryToken(Bookmark),
          useValue: mockBookmarkRepositoryMethods,
        },
        {
          provide: getRepositoryToken(TimelineEvent),
          useValue: mockTimelineEventRepositoryMethods,
        },
        {
          provide: getRepositoryToken(ReferenceCollection),
          useValue: mockReferenceCollectionRepositoryMethods,
        },
        {
          provide: getRepositoryToken(UserFollow),
          useValue: mockUserFollowRepositoryMethods,
        },
        {
          provide: getRepositoryToken(FriendGroup),
          useValue: mockFriendGroupRepositoryMethods,
        },
      ],
    }).compile();

    service = module.get<HomepageService>(HomepageService);
    mockUserRepository = module.get(getRepositoryToken(User));
    mockStoryRepository = module.get(getRepositoryToken(Story));
    mockCharacterRepository = module.get(getRepositoryToken(Character));
    mockBookmarkRepository = module.get(getRepositoryToken(Bookmark));
    mockTimelineEventRepository = module.get(getRepositoryToken(TimelineEvent));
    mockReferenceCollectionRepository = module.get(
      getRepositoryToken(ReferenceCollection),
    );
    mockUserFollowRepository = module.get(getRepositoryToken(UserFollow));
    mockFriendGroupRepository = module.get(getRepositoryToken(FriendGroup));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("getHomepageData - 获取个人主页完整数据", () => {
    it("应该成功获取个人主页数据", async () => {
      // Arrange
      const targetUserId = "test-user-id";
      const currentUserId = "current-user-id";

      mockUserRepository.findOne.mockResolvedValue(mockUser as any);

      // Mock统计查询
      const mockQueryBuilder = mockUserRepository.createQueryBuilder();
      (mockQueryBuilder.getRawOne as jest.Mock).mockResolvedValue({
        storiesCount: 5,
        charactersCount: 3,
        lightedCharactersCount: 2,
        bookmarksCount: 10,
        timelineEventsCount: 8,
        referenceCollectionsCount: 2,
        totalLikesReceived: 50,
        totalViewsReceived: 500,
      });

      mockStoryRepository.find.mockResolvedValue([mockStory] as any);
      mockCharacterRepository.find.mockResolvedValue([mockCharacter] as any);
      mockTimelineEventRepository.find.mockResolvedValue([
        mockTimelineEvent,
      ] as any);
      mockBookmarkRepository.find.mockResolvedValue([mockBookmark] as any);
      mockReferenceCollectionRepository.find.mockResolvedValue([
        mockReferenceCollection,
      ] as any);

      // Mock关系状态查询
      mockUserFollowRepository.findOne.mockResolvedValue(null);
      mockFriendGroupRepository.findOne.mockResolvedValue(null);

      // Act
      const result = await service.getHomepageData(targetUserId, currentUserId);

      // Assert
      expect(result).toBeDefined();
      expect(result.userId).toBe(mockUser.id);
      expect(result.nickname).toBe(mockUser.nickname);
      expect(result.username).toBe(mockUser.username);
      expect(result.avatarUrl).toBe(mockUser.avatarUrl);
      expect(result.canEdit).toBe(false); // currentUserId !== targetUserId
      expect(result.displaySettings).toEqual(mockUser.profileDisplaySettings);
      expect(result.statistics).toBeDefined();
      expect(result.content).toBeDefined();

      expect(mockUserRepository.findOne).toHaveBeenCalledWith({
        where: { id: targetUserId },
      });
    });

    it("应该在用户不存在时抛出NotFoundException", async () => {
      // Arrange
      const targetUserId = "non-existent-user";
      mockUserRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(service.getHomepageData(targetUserId)).rejects.toThrow(
        NotFoundException,
      );
      expect(mockUserRepository.findOne).toHaveBeenCalledWith({
        where: { id: targetUserId },
      });
    });

    it("应该正确处理隐私设置", async () => {
      // Arrange
      const targetUserId = "test-user-id";
      const mockUserWithPrivateSettings = {
        ...mockUser,
        profileDisplaySettings: {
          ...mockUser.profileDisplaySettings,
          showBirthday: false,
          showBio: false,
          showStatistics: false,
        },
      };

      mockUserRepository.findOne.mockResolvedValue(
        mockUserWithPrivateSettings as any,
      );

      const mockQueryBuilder = mockUserRepository.createQueryBuilder();
      (mockQueryBuilder.getRawOne as jest.Mock).mockResolvedValue({});

      // Act
      const result = await service.getHomepageData(targetUserId);

      // Assert
      expect(result.birthDate).toBeUndefined();
      expect(result.bio).toBeUndefined();
      expect(result.statistics).toEqual({
        storiesCount: 0,
        charactersCount: 0,
        lightedCharactersCount: 0,
        bookmarksCount: 0,
        followersCount: 0,
        followingCount: 0,
        timelineEventsCount: 0,
        referenceCollectionsCount: 0,
        totalLikesReceived: 0,
        totalViewsReceived: 0,
        memberSince: mockUser.createdAt,
        lastActiveAt: mockUser.lastLoginAt,
      });
    });

    it("应该正确判断编辑权限", async () => {
      // Arrange
      const targetUserId = "test-user-id";
      const currentUserId = "test-user-id"; // 相同用户ID

      mockUserRepository.findOne.mockResolvedValue(mockUser as any);

      const mockQueryBuilder = mockUserRepository.createQueryBuilder();
      (mockQueryBuilder.getRawOne as jest.Mock).mockResolvedValue({});

      // Act
      const result = await service.getHomepageData(targetUserId, currentUserId);

      // Assert
      expect(result.canEdit).toBe(true);
    });

    it("应该正确处理关注和好友关系", async () => {
      // Arrange
      const targetUserId = "test-user-id";
      const currentUserId = "current-user-id";

      mockUserRepository.findOne.mockResolvedValue(mockUser as any);

      const mockQueryBuilder = mockUserRepository.createQueryBuilder();
      (mockQueryBuilder.getRawOne as jest.Mock).mockResolvedValue({});

      // Mock关注关系
      mockUserFollowRepository.findOne.mockResolvedValue({
        id: "follow-id",
        followerId: currentUserId,
        followedId: targetUserId,
      } as any);

      // Mock好友关系
      const mockFriendQueryBuilder =
        mockFriendGroupRepository.createQueryBuilder();
      (mockFriendQueryBuilder.getOne as jest.Mock).mockResolvedValue({
        id: "friend-id",
      });

      // Act
      const result = await service.getHomepageData(targetUserId, currentUserId);

      // Assert
      expect(result.isFollowing).toBe(true);
      expect(result.isFriend).toBe(true);
    });
  });

  describe("updateDisplaySettings - 更新个人主页展示设置", () => {
    const updateDto: UpdateDisplaySettingsDto = {
      showBirthday: false,
      showBio: true,
      showStatistics: true,
      showCharacters: true,
      showTimeline: false,
      showStories: true,
      showBookmarks: true,
      showFollowList: false,
      showReferenceCollections: true,
    };

    it("应该成功更新展示设置", async () => {
      // Arrange
      const userId = "test-user-id";
      mockUserRepository.findOne.mockResolvedValue(mockUser as any);
      mockUserRepository.save.mockResolvedValue({
        ...mockUser,
        profileDisplaySettings: updateDto,
      } as any);

      // Act
      const result = await service.updateDisplaySettings(userId, updateDto);

      // Assert
      expect(result).toBeDefined();
      expect(result.message).toBe("展示设置更新成功");
      expect(mockUserRepository.findOne).toHaveBeenCalledWith({
        where: { id: userId },
      });
      expect(mockUserRepository.save).toHaveBeenCalled();
    });

    it("应该在用户不存在时抛出NotFoundException", async () => {
      // Arrange
      const userId = "non-existent-user";
      mockUserRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.updateDisplaySettings(userId, updateDto),
      ).rejects.toThrow(NotFoundException);
    });

    it("应该正确合并现有设置", async () => {
      // Arrange
      const userId = "test-user-id";
      const partialUpdateDto: UpdateDisplaySettingsDto = {
        showBirthday: false,
        showStatistics: true,
      };

      mockUserRepository.findOne.mockResolvedValue(mockUser as any);
      mockUserRepository.save.mockResolvedValue(mockUser as any);

      // Act
      await service.updateDisplaySettings(userId, partialUpdateDto);

      // Assert
      expect(mockUserRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          profileDisplaySettings: expect.objectContaining({
            showBirthday: false,
            showStatistics: true,
            // 其他设置应保持不变
            showBio: mockUser.profileDisplaySettings.showBio,
            showCharacters: mockUser.profileDisplaySettings.showCharacters,
          }),
        }),
      );
    });
  });

  describe("updateHomepageSettings - 更新个人主页高级设置", () => {
    const updateDto: UpdateHomepageSettingsDto = {
      theme: HomepageTheme.DARK,
      layout: HomepageLayout.MINIMAL,
      customBio: "新的个性化简介",
      backgroundImage: "https://example.com/new-bg.jpg",
      pinnedItems: ["story-2", "character-2"],
      socialLinks: [
        {
          platform: "twitter",
          url: "https://twitter.com/testuser",
          display: true,
        },
      ],
      showBirthday: true,
      showBio: false,
    };

    it("应该成功更新高级设置", async () => {
      // Arrange
      const userId = "test-user-id";
      mockUserRepository.findOne.mockResolvedValue(mockUser as any);
      mockUserRepository.save.mockResolvedValue(mockUser as any);

      // Act
      const result = await service.updateHomepageSettings(userId, updateDto);

      // Assert
      expect(result).toBeDefined();
      expect(result.message).toBe("主页设置更新成功");
      expect(mockUserRepository.findOne).toHaveBeenCalledWith({
        where: { id: userId },
      });
      expect(mockUserRepository.save).toHaveBeenCalled();
    });

    it("应该在用户不存在时抛出NotFoundException", async () => {
      // Arrange
      const userId = "non-existent-user";
      mockUserRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.updateHomepageSettings(userId, updateDto),
      ).rejects.toThrow(NotFoundException);
    });

    it("应该同时更新展示设置和高级设置", async () => {
      // Arrange
      const userId = "test-user-id";
      mockUserRepository.findOne.mockResolvedValue(mockUser as any);
      mockUserRepository.save.mockResolvedValue(mockUser as any);

      // Act
      await service.updateHomepageSettings(userId, updateDto);

      // Assert
      expect(mockUserRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          profileDisplaySettings: expect.objectContaining({
            showBirthday: updateDto.showBirthday,
            showBio: updateDto.showBio,
          }),
          homepageSettings: expect.objectContaining({
            theme: updateDto.theme,
            layout: updateDto.layout,
            customBio: updateDto.customBio,
            backgroundImage: updateDto.backgroundImage,
            pinnedItems: updateDto.pinnedItems,
            socialLinks: updateDto.socialLinks,
          }),
        }),
      );
    });

    it("应该正确处理部分更新", async () => {
      // Arrange
      const userId = "test-user-id";
      const partialUpdateDto: UpdateHomepageSettingsDto = {
        theme: HomepageTheme.LIGHT,
        customBio: "部分更新简介",
      };

      mockUserRepository.findOne.mockResolvedValue(mockUser as any);
      mockUserRepository.save.mockResolvedValue(mockUser as any);

      // Act
      await service.updateHomepageSettings(userId, partialUpdateDto);

      // Assert
      expect(mockUserRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          homepageSettings: expect.objectContaining({
            theme: partialUpdateDto.theme,
            customBio: partialUpdateDto.customBio,
            // 其他设置应保持原值
            layout: mockUser.homepageSettings.layout,
            backgroundImage: mockUser.homepageSettings.backgroundImage,
          }),
        }),
      );
    });
  });

  describe("getPersonalStats - 获取个人统计数据", () => {
    it("应该成功获取完整的个人统计数据", async () => {
      // Arrange
      const userId = "test-user-id";
      mockUserRepository.findOne.mockResolvedValue(mockUser as any);

      // Mock各种统计查询
      mockStoryRepository.count.mockResolvedValue(5);
      mockCharacterRepository.count.mockResolvedValue(3);
      mockTimelineEventRepository.count.mockResolvedValue(8);
      mockBookmarkRepository.count.mockResolvedValue(12);
      mockReferenceCollectionRepository.count.mockResolvedValue(2);

      const mockFollowersQueryBuilder =
        mockUserFollowRepository.createQueryBuilder();
      const mockFollowingQueryBuilder =
        mockUserFollowRepository.createQueryBuilder();
      (mockFollowersQueryBuilder.getCount as jest.Mock).mockResolvedValue(25);
      (mockFollowingQueryBuilder.getCount as jest.Mock).mockResolvedValue(30);

      // Mock点赞数统计
      const mockStoryLikesQueryBuilder =
        mockStoryRepository.createQueryBuilder();
      (mockStoryLikesQueryBuilder.getRawOne as jest.Mock).mockResolvedValue({
        sum: 100,
      });

      const mockCharacterLikesQueryBuilder =
        mockCharacterRepository.createQueryBuilder();
      (mockCharacterLikesQueryBuilder.getRawOne as jest.Mock).mockResolvedValue(
        { sum: 50 },
      );

      // Mock浏览数统计
      const mockStoryViewsQueryBuilder =
        mockStoryRepository.createQueryBuilder();
      (mockStoryViewsQueryBuilder.getRawOne as jest.Mock).mockResolvedValue({
        sum: 1000,
      });

      // Act
      const result = await service.getPersonalStats(userId);

      // Assert
      expect(result).toBeDefined();
      expect(result.userId).toBe(userId);
      expect(result.basicStats.storiesCount).toBe(5);
      expect(result.basicStats.charactersCount).toBe(3);
      expect(result.basicStats.timelineEventsCount).toBe(8);
      expect(result.basicStats.bookmarksCount).toBe(12);
      expect(result.basicStats.referenceCollectionsCount).toBe(2);
      expect(result.socialStats.followersCount).toBe(25);
      expect(result.socialStats.followingCount).toBe(30);
      expect(result.interactionStats.totalLikesReceived).toBe(150); // 100 + 50
      expect(result.interactionStats.totalViewsReceived).toBe(1000);
      expect(result.accountInfo.memberSince).toBe(mockUser.createdAt);
      expect(result.accountInfo.lastActiveAt).toBe(mockUser.lastLoginAt);
    });

    it("应该在用户不存在时抛出NotFoundException", async () => {
      // Arrange
      const userId = "non-existent-user";
      mockUserRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(service.getPersonalStats(userId)).rejects.toThrow(
        NotFoundException,
      );
    });

    it("应该正确处理统计数据为零的情况", async () => {
      // Arrange
      const userId = "test-user-id";
      mockUserRepository.findOne.mockResolvedValue(mockUser as any);

      // Mock所有统计为0
      mockStoryRepository.count.mockResolvedValue(0);
      mockCharacterRepository.count.mockResolvedValue(0);
      mockTimelineEventRepository.count.mockResolvedValue(0);
      mockBookmarkRepository.count.mockResolvedValue(0);
      mockReferenceCollectionRepository.count.mockResolvedValue(0);

      const mockQueryBuilder = mockUserFollowRepository.createQueryBuilder();
      (mockQueryBuilder.getCount as jest.Mock).mockResolvedValue(0);

      const mockLikesQueryBuilder = mockStoryRepository.createQueryBuilder();
      (mockLikesQueryBuilder.getRawOne as jest.Mock).mockResolvedValue({
        sum: null,
      });

      const mockCharacterLikesQueryBuilder =
        mockCharacterRepository.createQueryBuilder();
      (mockCharacterLikesQueryBuilder.getRawOne as jest.Mock).mockResolvedValue(
        { sum: null },
      );

      const mockViewsQueryBuilder = mockStoryRepository.createQueryBuilder();
      (mockViewsQueryBuilder.getRawOne as jest.Mock).mockResolvedValue({
        sum: null,
      });

      // Act
      const result = await service.getPersonalStats(userId);

      // Assert
      expect(result.basicStats.storiesCount).toBe(0);
      expect(result.basicStats.charactersCount).toBe(0);
      expect(result.basicStats.timelineEventsCount).toBe(0);
      expect(result.basicStats.bookmarksCount).toBe(0);
      expect(result.basicStats.referenceCollectionsCount).toBe(0);
      expect(result.socialStats.followersCount).toBe(0);
      expect(result.socialStats.followingCount).toBe(0);
      expect(result.interactionStats.totalLikesReceived).toBe(0);
      expect(result.interactionStats.totalViewsReceived).toBe(0);
    });

    it("应该正确计算点亮人物数量", async () => {
      // Arrange
      const userId = "test-user-id";
      mockUserRepository.findOne.mockResolvedValue(mockUser as any);

      // Mock基础统计
      mockStoryRepository.count.mockResolvedValue(0);
      mockCharacterRepository.count.mockResolvedValue(5);
      mockTimelineEventRepository.count.mockResolvedValue(0);
      mockBookmarkRepository.count.mockResolvedValue(0);
      mockReferenceCollectionRepository.count.mockResolvedValue(0);

      const mockQueryBuilder = mockUserFollowRepository.createQueryBuilder();
      (mockQueryBuilder.getCount as jest.Mock).mockResolvedValue(0);

      // Mock点亮人物统计
      const mockLightedCharactersQueryBuilder =
        mockCharacterRepository.createQueryBuilder();
      (
        mockLightedCharactersQueryBuilder.getCount as jest.Mock
      ).mockResolvedValue(3);

      // Act
      const result = await service.getPersonalStats(userId);

      // Assert
      expect(result.basicStats.charactersCount).toBe(5);
      expect(result.basicStats.lightedCharactersCount).toBe(3);
    });
  });

  describe("边界条件和错误处理", () => {
    it("应该正确处理空的展示设置", async () => {
      // Arrange
      const userId = "test-user-id";
      const emptyDto: UpdateDisplaySettingsDto = {};

      mockUserRepository.findOne.mockResolvedValue(mockUser as any);
      mockUserRepository.save.mockResolvedValue(mockUser as any);

      // Act
      const result = await service.updateDisplaySettings(userId, emptyDto);

      // Assert
      expect(result.message).toBe("展示设置更新成功");
      expect(mockUserRepository.save).toHaveBeenCalled();
    });

    it("应该正确处理空的高级设置", async () => {
      // Arrange
      const userId = "test-user-id";
      const emptyDto: UpdateHomepageSettingsDto = {};

      mockUserRepository.findOne.mockResolvedValue(mockUser as any);
      mockUserRepository.save.mockResolvedValue(mockUser as any);

      // Act
      const result = await service.updateHomepageSettings(userId, emptyDto);

      // Assert
      expect(result.message).toBe("主页设置更新成功");
      expect(mockUserRepository.save).toHaveBeenCalled();
    });

    it("应该处理数据库查询异常", async () => {
      // Arrange
      const userId = "test-user-id";
      mockUserRepository.findOne.mockRejectedValue(new Error("数据库连接错误"));

      // Act & Assert
      await expect(service.getHomepageData(userId)).rejects.toThrow(
        "数据库连接错误",
      );
    });

    it("应该正确处理没有高级设置的用户", async () => {
      // Arrange
      const userWithoutAdvancedSettings = {
        ...mockUser,
        homepageSettings: null,
      };

      mockUserRepository.findOne.mockResolvedValue(
        userWithoutAdvancedSettings as any,
      );

      const mockQueryBuilder = mockUserRepository.createQueryBuilder();
      (mockQueryBuilder.getRawOne as jest.Mock).mockResolvedValue({});

      // Act
      const result = await service.getHomepageData("test-user-id");

      // Assert
      expect(result.advancedSettings).toBeDefined();
      expect(result.advancedSettings.theme).toBe(HomepageTheme.AUTO);
      expect(result.advancedSettings.layout).toBe(HomepageLayout.MODERN);
    });
  });

  describe("性能和并发测试", () => {
    it("应该能并发处理多个个人主页数据请求", async () => {
      // Arrange
      const userIds = ["user-1", "user-2", "user-3"];
      mockUserRepository.findOne.mockImplementation((options: any) => {
        const userId = options.where.id;
        return Promise.resolve({
          ...mockUser,
          id: userId,
          nickname: `测试用户${userId}`,
        } as any);
      });

      const mockQueryBuilder = mockUserRepository.createQueryBuilder();
      (mockQueryBuilder.getRawOne as jest.Mock).mockResolvedValue({});

      // Act
      const promises = userIds.map((userId) => service.getHomepageData(userId));
      const results = await Promise.all(promises);

      // Assert
      expect(results).toHaveLength(3);
      results.forEach((result, index) => {
        expect(result.userId).toBe(userIds[index]);
        expect(result.nickname).toBe(`测试用户${userIds[index]}`);
      });
    });

    it("应该能处理大量统计数据", async () => {
      // Arrange
      const userId = "test-user-id";
      mockUserRepository.findOne.mockResolvedValue(mockUser as any);

      // Mock大量数据
      mockStoryRepository.count.mockResolvedValue(10000);
      mockCharacterRepository.count.mockResolvedValue(5000);
      mockTimelineEventRepository.count.mockResolvedValue(50000);

      const mockQueryBuilder = mockUserFollowRepository.createQueryBuilder();
      (mockQueryBuilder.getCount as jest.Mock).mockResolvedValue(100000);

      // Act
      const result = await service.getPersonalStats(userId);

      // Assert
      expect(result.basicStats.storiesCount).toBe(10000);
      expect(result.basicStats.charactersCount).toBe(5000);
      expect(result.basicStats.timelineEventsCount).toBe(50000);
      expect(result.socialStats.followersCount).toBe(100000);
      expect(result.socialStats.followingCount).toBe(100000);
    });
  });
});
