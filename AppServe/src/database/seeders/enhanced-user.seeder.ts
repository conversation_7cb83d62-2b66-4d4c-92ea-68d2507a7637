/**
 * 企业级用户种子数据
 *
 * 基于 YGS v1.0.0 完整业务逻辑设计
 * 涵盖多种用户角色和使用场景
 */

// 用户种子数据接口定义

export interface EnhancedUserSeedData {
  phone: string;
  email: string;
  password: string; // 明文密码，种子服务会hash
  userNumber: string;
  nickname: string;
  username: string;
  avatarUrl: string;
  birthDate: string;
  bio: string;
  profileDisplaySettings: {
    showBirthday: boolean;
    showBio: boolean;
    showStatistics: boolean;
    showCharacters: boolean;
    showTimeline: boolean;
    showStories: boolean;
    showBookmarks: boolean;
    showFollowList: boolean;
    showReferenceCollections: boolean;
  };
  aiQuotaRemaining: number;
  isPhoneVerified: boolean;
  isEmailVerified: boolean;
  isVipUser: boolean;
  anomalyStatus: "normal" | "warning" | "restricted" | "suspended";
  role: "creator" | "lighter" | "reader" | "tester"; // 业务角色标记
}

export const ENHANCED_USER_SEED_DATA: EnhancedUserSeedData[] = [
  // 1. 活跃创作者 - 张明远
  {
    phone: "13800138001",
    email: "<EMAIL>",
    password: "Test123456!",
    userNumber: "100001",
    nickname: "明远说",
    username: "story_creator_zhang",
    avatarUrl:
      "https://ygs-oss-dev.oss-cn-hangzhou.aliyuncs.com/avatars/zhang_mingyuan.jpg",
    birthDate: "1990-05-15",
    bio: "用文字记录生活，用故事温暖人心。已创作50+故事，记录了身边100+位朋友的人生片段。",
    profileDisplaySettings: {
      showBirthday: false,
      showBio: true,
      showStatistics: true,
      showCharacters: true,
      showTimeline: true,
      showStories: true,
      showBookmarks: false,
      showFollowList: false,
      showReferenceCollections: true,
    },
    aiQuotaRemaining: 5,
    isPhoneVerified: true,
    isEmailVerified: true,
    isVipUser: false,
    anomalyStatus: "normal",
    role: "creator",
  },

  // 2. 活跃点亮者 - 李小红
  {
    phone: "13800138002",
    email: "<EMAIL>",
    password: "Test123456!",
    userNumber: "100002",
    nickname: "小红姐姐",
    username: "character_lighter_li",
    avatarUrl:
      "https://ygs-oss-dev.oss-cn-hangzhou.aliyuncs.com/avatars/li_xiaohong.jpg",
    birthDate: "1992-08-20",
    bio: "在别人的故事里找到了自己。已成功点亮15个人物，每个都是珍贵的回忆。",
    profileDisplaySettings: {
      showBirthday: true,
      showBio: true,
      showStatistics: true,
      showCharacters: true,
      showTimeline: true,
      showStories: true,
      showBookmarks: true,
      showFollowList: true,
      showReferenceCollections: true,
    },
    aiQuotaRemaining: 3,
    isPhoneVerified: true,
    isEmailVerified: false,
    isVipUser: false,
    anomalyStatus: "normal",
    role: "lighter",
  },

  // 3. 内容创作者 - 王建国
  {
    phone: "13800138003",
    email: "<EMAIL>",
    password: "Test123456!",
    userNumber: "100003",
    nickname: "老王讲故事",
    username: "story_creator_wang",
    avatarUrl:
      "https://ygs-oss-dev.oss-cn-hangzhou.aliyuncs.com/avatars/wang_jianguo.jpg",
    birthDate: "1985-03-10",
    bio: "退休教师，喜欢记录学生们的成长故事。每个孩子都是一个独特的世界。",
    profileDisplaySettings: {
      showBirthday: false,
      showBio: true,
      showStatistics: true,
      showCharacters: true,
      showTimeline: false,
      showStories: true,
      showBookmarks: false,
      showFollowList: false,
      showReferenceCollections: true,
    },
    aiQuotaRemaining: 0, // 今日配额已用完
    isPhoneVerified: true,
    isEmailVerified: true,
    isVipUser: false,
    anomalyStatus: "normal",
    role: "creator",
  },

  // 4. 普通读者 - 赵小芳
  {
    phone: "13800138004",
    email: "<EMAIL>",
    password: "Test123456!",
    userNumber: "100004",
    nickname: "芳芳爱读书",
    username: "regular_reader_zhao",
    avatarUrl:
      "https://ygs-oss-dev.oss-cn-hangzhou.aliyuncs.com/avatars/zhao_xiaofang.jpg",
    birthDate: "1995-11-28",
    bio: "喜欢看故事的普通人，偶尔也会写写自己的小日常。",
    profileDisplaySettings: {
      showBirthday: true,
      showBio: true,
      showStatistics: false,
      showCharacters: false,
      showTimeline: true,
      showStories: true,
      showBookmarks: true,
      showFollowList: false,
      showReferenceCollections: false,
    },
    aiQuotaRemaining: 5,
    isPhoneVerified: true,
    isEmailVerified: false,
    isVipUser: false,
    anomalyStatus: "normal",
    role: "reader",
  },

  // 5. VIP测试用户 - 陈总
  {
    phone: "16675158665", // 真实短信测试号
    email: "<EMAIL>",
    password: "Test123456!",
    userNumber: "188888",
    nickname: "陈总",
    username: "vip_tester_chen",
    avatarUrl:
      "https://ygs-oss-dev.oss-cn-hangzhou.aliyuncs.com/avatars/chen_vip.jpg",
    birthDate: "1980-01-01",
    bio: "VIP用户，体验所有高级功能。",
    profileDisplaySettings: {
      showBirthday: false,
      showBio: false,
      showStatistics: true,
      showCharacters: true,
      showTimeline: true,
      showStories: true,
      showBookmarks: true,
      showFollowList: true,
      showReferenceCollections: true,
    },
    aiQuotaRemaining: 200, // VIP配额
    isPhoneVerified: true,
    isEmailVerified: true,
    isVipUser: true,
    anomalyStatus: "normal",
    role: "tester",
  },

  // 6. 新注册用户 - 刘小新
  {
    phone: "13800138005",
    email: null,
    password: "Test123456!",
    userNumber: "100005",
    nickname: "新用户小新",
    username: "new_user_liu",
    avatarUrl: null,
    birthDate: null,
    bio: null,
    profileDisplaySettings: {
      showBirthday: false,
      showBio: true,
      showStatistics: true,
      showCharacters: true,
      showTimeline: true,
      showStories: true,
      showBookmarks: false,
      showFollowList: false,
      showReferenceCollections: true,
    },
    aiQuotaRemaining: 5,
    isPhoneVerified: false,
    isEmailVerified: false,
    isVipUser: false,
    anomalyStatus: "normal",
    role: "reader",
  },

  // 7. 异常警告用户 - 孙问题
  {
    phone: "13800138006",
    email: "<EMAIL>",
    password: "Test123456!",
    userNumber: "100006",
    nickname: "问题用户",
    username: "warning_user_sun",
    avatarUrl:
      "https://ygs-oss-dev.oss-cn-hangzhou.aliyuncs.com/avatars/sun_warning.jpg",
    birthDate: "2000-06-06",
    bio: "用于测试异常用户处理流程。",
    profileDisplaySettings: {
      showBirthday: false,
      showBio: false,
      showStatistics: false,
      showCharacters: false,
      showTimeline: false,
      showStories: true,
      showBookmarks: false,
      showFollowList: false,
      showReferenceCollections: false,
    },
    aiQuotaRemaining: 1,
    isPhoneVerified: true,
    isEmailVerified: false,
    isVipUser: false,
    anomalyStatus: "warning",
    role: "tester",
  },

  // 8. 点亮达人 - 周发现
  {
    phone: "13800138007",
    email: "<EMAIL>",
    password: "Test123456!",
    userNumber: "100007",
    nickname: "发现自己",
    username: "lighter_master_zhou",
    avatarUrl:
      "https://ygs-oss-dev.oss-cn-hangzhou.aliyuncs.com/avatars/zhou_faxian.jpg",
    birthDate: "1993-09-15",
    bio: "专注于在故事中发现自己和朋友们的身影。已点亮30+人物，每个都是一段珍贵回忆。",
    profileDisplaySettings: {
      showBirthday: false,
      showBio: true,
      showStatistics: true,
      showCharacters: true,
      showTimeline: true,
      showStories: false,
      showBookmarks: false,
      showFollowList: true,
      showReferenceCollections: true,
    },
    aiQuotaRemaining: 2,
    isPhoneVerified: true,
    isEmailVerified: true,
    isVipUser: false,
    anomalyStatus: "normal",
    role: "lighter",
  },

  // 9. 私密创作者 - 吴私密
  {
    phone: "13800138008",
    email: "<EMAIL>",
    password: "Test123456!",
    userNumber: "100008",
    nickname: "私密日记",
    username: "private_creator_wu",
    avatarUrl:
      "https://ygs-oss-dev.oss-cn-hangzhou.aliyuncs.com/avatars/wu_simi.jpg",
    birthDate: "1988-12-24",
    bio: "只为自己和最亲密的朋友记录生活。",
    profileDisplaySettings: {
      showBirthday: false,
      showBio: false,
      showStatistics: false,
      showCharacters: false,
      showTimeline: false,
      showStories: false,
      showBookmarks: false,
      showFollowList: false,
      showReferenceCollections: false,
    },
    aiQuotaRemaining: 5,
    isPhoneVerified: true,
    isEmailVerified: true,
    isVipUser: false,
    anomalyStatus: "normal",
    role: "creator",
  },

  // 10. 系统测试账号
  {
    phone: "13800138009",
    email: "<EMAIL>",
    password: "Test123456!",
    userNumber: "199999",
    nickname: "系统测试",
    username: "system_tester",
    avatarUrl: null,
    birthDate: null,
    bio: "系统测试专用账号，请勿删除。",
    profileDisplaySettings: {
      showBirthday: true,
      showBio: true,
      showStatistics: true,
      showCharacters: true,
      showTimeline: true,
      showStories: true,
      showBookmarks: true,
      showFollowList: true,
      showReferenceCollections: true,
    },
    aiQuotaRemaining: 999, // 测试账号不限配额
    isPhoneVerified: true,
    isEmailVerified: true,
    isVipUser: true,
    anomalyStatus: "normal",
    role: "tester",
  },
];

// 导出用户角色映射，方便其他种子数据引用
export const USER_ROLES = {
  ZHANG_MINGYUAN: "story_creator_zhang", // 活跃创作者
  LI_XIAOHONG: "character_lighter_li", // 活跃点亮者
  WANG_JIANGUO: "story_creator_wang", // 内容创作者
  ZHAO_XIAOFANG: "regular_reader_zhao", // 普通读者
  CHEN_VIP: "vip_tester_chen", // VIP测试
  LIU_XIAOXIN: "new_user_liu", // 新用户
  SUN_WENTI: "warning_user_sun", // 异常用户
  ZHOU_FAXIAN: "lighter_master_zhou", // 点亮达人
  WU_SIMI: "private_creator_wu", // 私密创作者
  SYSTEM_TEST: "system_tester", // 系统测试
};
