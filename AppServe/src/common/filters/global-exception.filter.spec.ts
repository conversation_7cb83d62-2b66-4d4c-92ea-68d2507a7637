import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import type { ArgumentsHost } from "@nestjs/common";
import {
  HttpException,
  HttpStatus,
  BadRequestException,
  Logger,
} from "@nestjs/common";
import { QueryFailedError, EntityNotFoundError } from "typeorm";
import { ValidationError } from "class-validator";
import { GlobalExceptionFilter } from "./global-exception.filter";
import type { FastifyRequest, FastifyReply } from "fastify";

describe("GlobalExceptionFilter", () => {
  let filter: GlobalExceptionFilter;
  let mockRequest: Partial<FastifyRequest>;
  let mockResponse: Partial<FastifyReply>;
  let mockArgumentsHost: ArgumentsHost;
  let mockLogger: {
    error: jest.SpyInstance;
    warn: jest.SpyInstance;
    log: jest.SpyInstance;
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [GlobalExceptionFilter],
    }).compile();

    filter = module.get<GlobalExceptionFilter>(GlobalExceptionFilter);

    // Mock Request
    mockRequest = {
      url: "/api/test",
      method: "GET",
      ip: "127.0.0.1",
      headers: {
        "user-agent": "Mozilla/5.0 (Test Browser)",
        "x-request-id": "test-request-123",
      },
      body: { test: "data" },
      query: { page: "1" },
      params: { id: "123" },
    };

    // Mock Response
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      send: jest.fn(),
    };

    // Mock ArgumentsHost
    mockArgumentsHost = {
      switchToHttp: jest.fn().mockReturnValue({
        getRequest: jest.fn().mockReturnValue(mockRequest),
        getResponse: jest.fn().mockReturnValue(mockResponse),
      }),
    } as unknown as ArgumentsHost;

    // Mock Logger
    mockLogger = {
      error: jest.spyOn(Logger.prototype, "error").mockImplementation(() => {}),
      warn: jest.spyOn(Logger.prototype, "warn").mockImplementation(() => {}),
      log: jest.spyOn(Logger.prototype, "log").mockImplementation(() => {}),
    };

    // Mock console.warn
    jest.spyOn(console, "warn").mockImplementation(() => {});
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe("catch", () => {
    it("应该处理HttpException", () => {
      const exception = new BadRequestException("验证失败");

      filter.catch(exception, mockArgumentsHost);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.send).toHaveBeenCalledWith({
        code: 400,
        message: "验证失败",
        error: "Bad Request",
        details: undefined,
        timestamp: expect.any(String),
        path: "/api/test",
        requestId: "test-request-123",
      });
      expect(mockLogger.log).toHaveBeenCalled(); // LOW severity
    });

    it("应该处理带有复杂响应的HttpException", () => {
      const complexResponse = {
        message: "验证失败",
        error: "VALIDATION_ERROR",
        details: { field: "name", issue: "required" },
      };
      const exception = new BadRequestException(complexResponse);

      filter.catch(exception, mockArgumentsHost);

      expect(mockResponse.send).toHaveBeenCalledWith({
        code: 400,
        message: "验证失败",
        error: "VALIDATION_ERROR",
        details: { field: "name", issue: "required" },
        timestamp: expect.any(String),
        path: "/api/test",
        requestId: "test-request-123",
      });
    });

    it("应该处理内部服务器错误", () => {
      const exception = new HttpException(
        "Internal Error",
        HttpStatus.INTERNAL_SERVER_ERROR,
      );

      filter.catch(exception, mockArgumentsHost);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockLogger.error).toHaveBeenCalled(); // HIGH severity
    });

    it("应该处理QueryFailedError", () => {
      const dbError = new QueryFailedError(
        "SELECT * FROM users",
        [],
        new Error("Database error"),
      );
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (dbError as any).constraint = "unique_email";
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (dbError as any).table = "users";
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (dbError as any).column = "email";

      filter.catch(dbError, mockArgumentsHost);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.send).toHaveBeenCalledWith({
        code: 400,
        message: "Database operation failed",
        error: "DATABASE_ERROR",
        details: {
          constraint: "unique_email",
          table: "users",
          column: "email",
        },
        timestamp: expect.any(String),
        path: "/api/test",
        requestId: "test-request-123",
      });
      expect(mockLogger.warn).toHaveBeenCalled(); // MEDIUM severity
    });

    it("应该处理EntityNotFoundError", () => {
      const exception = new EntityNotFoundError("User", { id: "123" });

      filter.catch(exception, mockArgumentsHost);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.send).toHaveBeenCalledWith({
        code: 404,
        message: "Resource not found",
        error: "ENTITY_NOT_FOUND",
        details: undefined,
        timestamp: expect.any(String),
        path: "/api/test",
        requestId: "test-request-123",
      });
      expect(mockLogger.log).toHaveBeenCalled(); // LOW severity
    });

    it("应该处理ValidationError", () => {
      const validationError = new ValidationError();
      validationError.property = "email";
      validationError.value = "invalid-email";
      validationError.constraints = { isEmail: "email must be a valid email" };

      filter.catch(validationError, mockArgumentsHost);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.send).toHaveBeenCalledWith({
        code: 400,
        message: "Validation failed",
        error: "VALIDATION_ERROR",
        details: {
          violations: [
            {
              property: "email",
              value: "invalid-email",
              constraints: { isEmail: "email must be a valid email" },
            },
          ],
        },
        timestamp: expect.any(String),
        path: "/api/test",
        requestId: "test-request-123",
      });
    });

    it("应该处理普通Error", () => {
      const exception = new Error("系统错误");

      filter.catch(exception, mockArgumentsHost);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockLogger.error).toHaveBeenCalled(); // HIGH severity
    });

    it("应该在生产环境下隐藏错误消息", () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = "production";

      const exception = new Error("敏感系统信息");

      filter.catch(exception, mockArgumentsHost);

      expect(mockResponse.send).toHaveBeenCalledWith({
        code: 500,
        message: "Internal server error",
        error: "SYSTEM_ERROR",
        details: undefined,
        timestamp: expect.any(String),
        path: "/api/test",
        requestId: "test-request-123",
      });

      process.env.NODE_ENV = originalEnv;
    });

    it("应该在开发环境下显示详细错误信息", () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = "development";

      const exception = new Error("详细错误信息");
      exception.stack = "Error: 详细错误信息\n    at test";

      filter.catch(exception, mockArgumentsHost);

      expect(mockResponse.send).toHaveBeenCalledWith(
        expect.objectContaining({
          message: "详细错误信息",
          stack: "Error: 详细错误信息\n    at test",
          context: expect.any(Object),
        }),
      );

      process.env.NODE_ENV = originalEnv;
    });

    it("应该生成请求ID", () => {
      // 移除请求头中的request-id
      delete mockRequest.headers!["x-request-id"];

      const exception = new BadRequestException("测试");

      filter.catch(exception, mockArgumentsHost);

      expect(mockResponse.send).toHaveBeenCalledWith(
        expect.objectContaining({
          requestId: expect.stringMatching(/^req_\d+_[a-z0-9]+$/),
        }),
      );
    });

    it("应该清理敏感字段", () => {
      mockRequest.body = {
        username: "test",
        password: "secret123",
        token: "jwt-token",
        email: "<EMAIL>",
      };

      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = "development";

      const exception = new BadRequestException("测试");

      filter.catch(exception, mockArgumentsHost);

      const call = mockResponse.send as jest.Mock;
      const response = call.mock.calls[0][0];

      expect(response.context.body).toEqual({
        username: "test",
        password: "***REDACTED***",
        token: "***REDACTED***",
        email: "<EMAIL>",
      });

      process.env.NODE_ENV = originalEnv;
    });

    it("应该处理带用户信息的请求", () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (mockRequest as any).user = { id: "user-123" };

      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = "development";

      const exception = new BadRequestException("测试");

      filter.catch(exception, mockArgumentsHost);

      const call = mockResponse.send as jest.Mock;
      const response = call.mock.calls[0][0];

      expect(response.context.userId).toBe("user-123");

      process.env.NODE_ENV = originalEnv;
    });

    it("应该发送高严重级别告警", async () => {
      const exception = new Error("严重系统错误");

      filter.catch(exception, mockArgumentsHost);

      // 由于告警是异步的，我们需要等待一下
      await new Promise((resolve) => setTimeout(resolve, 0));

      expect(console.warn).toHaveBeenCalledWith(
        "🚨 High Severity Error Alert:",
        expect.objectContaining({
          service: "story-app-backend",
          severity: "high",
          title: "SYSTEM_ERROR - 严重系统错误",
        }),
      );
    });

    it("应该处理非对象类型的请求体", () => {
      mockRequest.body = "string body";

      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = "development";

      const exception = new BadRequestException("测试");

      filter.catch(exception, mockArgumentsHost);

      const call = mockResponse.send as jest.Mock;
      const response = call.mock.calls[0][0];

      expect(response.context.body).toEqual({ value: "string body" });

      process.env.NODE_ENV = originalEnv;
    });

    it("应该处理null请求体", () => {
      mockRequest.body = null;

      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = "development";

      const exception = new BadRequestException("测试");

      filter.catch(exception, mockArgumentsHost);

      const call = mockResponse.send as jest.Mock;
      const response = call.mock.calls[0][0];

      expect(response.context.body).toBeUndefined();

      process.env.NODE_ENV = originalEnv;
    });

    it("应该根据严重级别记录不同级别的日志", () => {
      // 测试LOW级别 - BadRequest
      filter.catch(new BadRequestException("测试"), mockArgumentsHost);
      expect(mockLogger.log).toHaveBeenCalled();

      jest.clearAllMocks();

      // 测试MEDIUM级别 - Database Error
      const dbError = new QueryFailedError("SELECT", [], new Error("DB Error"));
      filter.catch(dbError, mockArgumentsHost);
      expect(mockLogger.warn).toHaveBeenCalled();

      jest.clearAllMocks();

      // 测试HIGH级别 - Internal Server Error
      filter.catch(new HttpException("Error", 500), mockArgumentsHost);
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  it("应该正确实例化", () => {
    expect(filter).toBeDefined();
    expect(filter).toBeInstanceOf(GlobalExceptionFilter);
  });
});
