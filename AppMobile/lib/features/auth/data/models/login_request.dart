import 'package:freezed_annotation/freezed_annotation.dart';

part 'login_request.freezed.dart';
part 'login_request.g.dart';

/// 登录请求模型
@freezed
class LoginRequest with _$LoginRequest {
  const factory LoginRequest.phone({
    required String phone,
    required String code,
    @Json<PERSON>ey(name: 'device_id') String? deviceId,
    @Json<PERSON>ey(name: 'device_name') String? deviceName,
  }) = PhoneLoginRequest;

  const factory LoginRequest.email({
    required String email,
    required String password,
    @Json<PERSON>ey(name: 'device_id') String? deviceId,
    @JsonKey(name: 'device_name') String? deviceName,
  }) = EmailLoginRequest;

  factory LoginRequest.fromJson(Map<String, dynamic> json) =>
      _$LoginRequestFromJson(json);
}