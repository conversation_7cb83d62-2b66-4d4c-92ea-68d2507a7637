import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { AuthController } from "./auth.controller";
import { AuthService } from "./auth.service";
import { JwtAuthGuard } from "./guards/jwt-auth.guard";
import type { PhoneLoginDto, LoginDto } from "./dto/login.dto";
import type {
  RegisterWithEmailDto,
  SetEmailPasswordDto,
  RefreshTokenDto,
} from "./dto/auth.dto";

describe("AuthController", () => {
  let controller: AuthController;
  let authService: AuthService;

  // Mock 数据
  const mockUser = {
    id: "test-user-id",
    phone: "13800138000",
    email: "<EMAIL>",
    username: "testuser",
    avatarUrl: null,
    passwordHash: "hashed_password",
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockLoginResult = {
    user: mockUser,
    accessToken: "mock_access_token",
    refreshToken: "mock_refresh_token",
    isFirstLogin: false,
  };

  const mockRegisterResult = {
    user: mockUser,
    accessToken: "mock_access_token",
    refreshToken: "mock_refresh_token",
  };

  const mockTokenResult = {
    accessToken: "new_access_token",
    refreshToken: "new_refresh_token",
  };

  const mockRequest = {
    user: {
      id: "test-user-id",
      phone: "13800138000",
      email: "<EMAIL>",
      username: "testuser",
      avatarUrl: null,
      birthDate: null,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    ip: "***********",
    headers: {
      "user-agent": "Mozilla/5.0...",
    },
  };

  beforeEach(async () => {
    const mockAuthService = {
      sendPhoneVerificationCode: jest.fn(),
      loginWithPhone: jest.fn(),
      registerWithEmail: jest.fn(),
      loginWithEmail: jest.fn(),
      setEmailAndPassword: jest.fn(),
      refreshToken: jest.fn(),
      validateToken: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthController],
      providers: [
        {
          provide: AuthService,
          useValue: mockAuthService,
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({
        canActivate: jest.fn().mockReturnValue(true),
      })
      .compile();

    controller = module.get<AuthController>(AuthController);
    authService = module.get<AuthService>(AuthService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("sendVerificationCode", () => {
    it("should send SMS verification code successfully", async () => {
      const phone = "13800138000";

      (authService.sendPhoneVerificationCode as jest.Mock).mockResolvedValue({
        success: true,
        message: "验证码发送成功",
      });

      const result = await controller.sendVerificationCode({ phone });

      expect(result).toEqual({
        success: true,
        message: "验证码发送成功",
      });
      expect(authService.sendPhoneVerificationCode).toHaveBeenCalledWith(phone);
    });

    it("should handle SMS sending service errors", async () => {
      const phone = "13800138000";

      (authService.sendPhoneVerificationCode as jest.Mock).mockRejectedValue(
        new Error("SMS service error"),
      );

      await expect(controller.sendVerificationCode({ phone })).rejects.toThrow(
        "SMS service error",
      );
    });

    it("should handle invalid phone number format", async () => {
      const phone = "invalid";

      (authService.sendPhoneVerificationCode as jest.Mock).mockRejectedValue(
        new Error("手机号格式不正确"),
      );

      await expect(controller.sendVerificationCode({ phone })).rejects.toThrow(
        "手机号格式不正确",
      );
    });
  });

  describe("loginWithPhone", () => {
    it("should login with phone successfully for existing user", async () => {
      const loginDto: PhoneLoginDto = {
        phone: "13800138000",
        code: "123456",
      };

      (authService.loginWithPhone as jest.Mock).mockResolvedValue(
        mockLoginResult,
      );

      const result = await controller.loginWithPhone(loginDto, mockRequest);

      expect(result).toEqual({
        success: true,
        message: "登录成功",
        data: {
          user: {
            id: mockUser.id,
            phone: mockUser.phone,
            email: mockUser.email,
            username: mockUser.username,
            avatarUrl: mockUser.avatarUrl,
            isFirstLogin: false,
          },
          tokens: {
            accessToken: mockLoginResult.accessToken,
            refreshToken: mockLoginResult.refreshToken,
          },
        },
      });
      expect(authService.loginWithPhone).toHaveBeenCalledWith(
        loginDto.phone,
        loginDto.code,
        mockRequest,
      );
    });

    it("should login with phone successfully for new user", async () => {
      const loginDto: PhoneLoginDto = {
        phone: "13800138000",
        code: "123456",
      };

      const firstLoginResult = {
        ...mockLoginResult,
        isFirstLogin: true,
      };

      (authService.loginWithPhone as jest.Mock).mockResolvedValue(
        firstLoginResult,
      );

      const result = await controller.loginWithPhone(loginDto, mockRequest);

      expect(result).toEqual({
        success: true,
        message: "注册并登录成功",
        data: {
          user: {
            id: mockUser.id,
            phone: mockUser.phone,
            email: mockUser.email,
            username: mockUser.username,
            avatarUrl: mockUser.avatarUrl,
            isFirstLogin: true,
          },
          tokens: {
            accessToken: firstLoginResult.accessToken,
            refreshToken: firstLoginResult.refreshToken,
          },
        },
      });
    });

    it("should handle phone login service errors", async () => {
      const loginDto: PhoneLoginDto = {
        phone: "13800138000",
        code: "123456",
      };

      (authService.loginWithPhone as jest.Mock).mockRejectedValue(
        new Error("Invalid verification code"),
      );

      await expect(
        controller.loginWithPhone(loginDto, mockRequest),
      ).rejects.toThrow("Invalid verification code");
    });
  });

  describe("registerWithEmail", () => {
    it("should register with email successfully", async () => {
      const registerDto: RegisterWithEmailDto = {
        email: "<EMAIL>",
        password: "password123",
        username: "testuser",
      };

      (authService.registerWithEmail as jest.Mock).mockResolvedValue(
        mockRegisterResult,
      );

      const result = await controller.registerWithEmail(registerDto);

      expect(result).toEqual({
        success: true,
        message: "注册成功",
        data: {
          user: {
            id: mockUser.id,
            phone: mockUser.phone,
            email: mockUser.email,
            username: mockUser.username,
            avatarUrl: mockUser.avatarUrl,
          },
          tokens: {
            accessToken: mockRegisterResult.accessToken,
            refreshToken: mockRegisterResult.refreshToken,
          },
        },
      });
      expect(authService.registerWithEmail).toHaveBeenCalledWith(
        registerDto.email,
        registerDto.password,
        registerDto.username,
      );
    });

    it("should handle email registration service errors", async () => {
      const registerDto: RegisterWithEmailDto = {
        email: "<EMAIL>",
        password: "password123",
        username: "testuser",
      };

      (authService.registerWithEmail as jest.Mock).mockRejectedValue(
        new Error("Email already exists"),
      );

      await expect(controller.registerWithEmail(registerDto)).rejects.toThrow(
        "Email already exists",
      );
    });
  });

  describe("loginWithEmail", () => {
    it("should login with email successfully", async () => {
      const loginDto: LoginDto = {
        email: "<EMAIL>",
        password: "password123",
      };

      (authService.loginWithEmail as jest.Mock).mockResolvedValue(
        mockLoginResult,
      );

      const result = await controller.loginWithEmail(loginDto);

      expect(result).toEqual({
        success: true,
        message: "登录成功",
        data: {
          user: {
            id: mockUser.id,
            phone: mockUser.phone,
            email: mockUser.email,
            username: mockUser.username,
            avatarUrl: mockUser.avatarUrl,
          },
          tokens: {
            accessToken: mockLoginResult.accessToken,
            refreshToken: mockLoginResult.refreshToken,
          },
        },
      });
      expect(authService.loginWithEmail).toHaveBeenCalledWith(
        loginDto.email,
        loginDto.password,
      );
    });

    it("should handle email login service errors", async () => {
      const loginDto: LoginDto = {
        email: "<EMAIL>",
        password: "wrongpassword",
      };

      (authService.loginWithEmail as jest.Mock).mockRejectedValue(
        new Error("Invalid credentials"),
      );

      await expect(controller.loginWithEmail(loginDto)).rejects.toThrow(
        "Invalid credentials",
      );
    });
  });

  describe("setEmailAndPassword", () => {
    it("should set email and password successfully", async () => {
      const setEmailPasswordDto: SetEmailPasswordDto = {
        email: "<EMAIL>",
        password: "newpassword123",
      };

      const mockUpdatedUser = {
        ...mockUser,
        email: "<EMAIL>",
      };

      (authService.setEmailAndPassword as jest.Mock).mockResolvedValue(
        mockUpdatedUser,
      );

      const result = await controller.setEmailAndPassword(
        mockRequest,
        setEmailPasswordDto,
      );

      expect(result).toEqual({
        success: true,
        message: "邮箱和密码设置成功",
        data: {
          user: {
            id: mockUpdatedUser.id,
            phone: mockUpdatedUser.phone,
            email: mockUpdatedUser.email,
            username: mockUpdatedUser.username,
            avatarUrl: mockUpdatedUser.avatarUrl,
          },
        },
      });
      expect(authService.setEmailAndPassword).toHaveBeenCalledWith(
        mockRequest.user.id,
        setEmailPasswordDto.email,
        setEmailPasswordDto.password,
      );
    });

    it("should handle set email and password service errors", async () => {
      const setEmailPasswordDto: SetEmailPasswordDto = {
        email: "<EMAIL>",
        password: "newpassword123",
      };

      (authService.setEmailAndPassword as jest.Mock).mockRejectedValue(
        new Error("Email already in use"),
      );

      await expect(
        controller.setEmailAndPassword(mockRequest, setEmailPasswordDto),
      ).rejects.toThrow("Email already in use");
    });
  });

  describe("refreshToken", () => {
    it("should refresh token successfully", async () => {
      const refreshTokenDto: RefreshTokenDto = {
        refreshToken: "valid_refresh_token",
      };

      (authService.refreshToken as jest.Mock).mockResolvedValue(
        mockTokenResult,
      );

      const result = await controller.refreshToken(refreshTokenDto);

      expect(result).toEqual({
        success: true,
        data: { tokens: mockTokenResult },
        message: "令牌刷新成功",
      });
      expect(authService.refreshToken).toHaveBeenCalledWith(
        refreshTokenDto.refreshToken,
      );
    });

    it("should handle refresh token service errors", async () => {
      const refreshTokenDto: RefreshTokenDto = {
        refreshToken: "invalid_refresh_token",
      };

      (authService.refreshToken as jest.Mock).mockRejectedValue(
        new Error("Invalid refresh token"),
      );

      await expect(controller.refreshToken(refreshTokenDto)).rejects.toThrow(
        "Invalid refresh token",
      );
    });

    it("should handle expired refresh token", async () => {
      const refreshTokenDto: RefreshTokenDto = {
        refreshToken: "expired_refresh_token",
      };

      (authService.refreshToken as jest.Mock).mockRejectedValue(
        new Error("Token expired"),
      );

      await expect(controller.refreshToken(refreshTokenDto)).rejects.toThrow(
        "Token expired",
      );
    });
  });

  describe("getProfile", () => {
    it("should get user profile successfully", async () => {
      const result = await controller.getProfile(mockRequest);

      expect(result).toEqual({
        success: true,
        message: "获取用户信息成功",
        data: {
          user: {
            id: mockRequest.user.id,
            phone: mockRequest.user.phone,
            email: mockRequest.user.email,
            username: mockRequest.user.username,
            avatarUrl: mockRequest.user.avatarUrl,
            birthDate: mockRequest.user.birthDate,
            isActive: mockRequest.user.isActive,
            createdAt: mockRequest.user.createdAt,
            updatedAt: mockRequest.user.updatedAt,
          },
        },
      });
    });

    it("should handle missing user data gracefully", async () => {
      const requestWithPartialUser = {
        user: {
          id: "test-user-id",
          // Missing other properties
        },
      };

      const result = await controller.getProfile(requestWithPartialUser);

      expect(result.success).toBe(true);
      expect(result.data.user.id).toBe("test-user-id");
      expect(result.data.user.phone).toBeUndefined();
    });
  });

  describe("logout", () => {
    it("should logout successfully", async () => {
      const result = await controller.logout();

      expect(result).toEqual({
        success: true,
        message: "登出成功",
      });
    });
  });

  describe("error handling", () => {
    it("should propagate service errors correctly", async () => {
      const phone = "invalid-phone";

      (authService.sendPhoneVerificationCode as jest.Mock).mockRejectedValue(
        new Error("手机号格式不正确"),
      );

      await expect(controller.sendVerificationCode({ phone })).rejects.toThrow(
        "手机号格式不正确",
      );
    });

    it("should handle undefined request objects", async () => {
      const phone = "13800138000";

      (authService.sendPhoneVerificationCode as jest.Mock).mockResolvedValue({
        success: true,
        message: "验证码发送成功",
      });

      // Should not throw even with undefined request
      const result = await controller.sendVerificationCode({ phone });

      expect(result.success).toBe(true);
      expect(authService.sendPhoneVerificationCode).toHaveBeenCalledWith(phone);
    });
  });

  describe("data transformation", () => {
    it("should transform user data consistently across endpoints", async () => {
      const mockUserWithNullValues = {
        ...mockUser,
        phone: null,
        avatarUrl: null,
      };

      const resultWithNulls = {
        ...mockLoginResult,
        user: mockUserWithNullValues,
      };

      (authService.loginWithPhone as jest.Mock).mockResolvedValue(
        resultWithNulls,
      );

      const loginDto: PhoneLoginDto = {
        phone: "13800138000",
        code: "123456",
      };

      const result = await controller.loginWithPhone(loginDto, mockRequest);

      expect(result.data.user.phone).toBeNull();
      expect(result.data.user.avatarUrl).toBeNull();
      expect(result.data.user.id).toBe(mockUser.id);
    });

    it("should include isFirstLogin in phone login response", async () => {
      const firstTimeLoginResult = {
        ...mockLoginResult,
        isFirstLogin: true,
      };

      (authService.loginWithPhone as jest.Mock).mockResolvedValue(
        firstTimeLoginResult,
      );

      const loginDto: PhoneLoginDto = {
        phone: "13800138000",
        code: "123456",
      };

      const result = await controller.loginWithPhone(loginDto, mockRequest);

      expect(result.data.user.isFirstLogin).toBe(true);
      expect(result.message).toBe("注册并登录成功");
    });

    it("should not include isFirstLogin in email login response", async () => {
      (authService.loginWithEmail as jest.Mock).mockResolvedValue(
        mockLoginResult,
      );

      const loginDto: LoginDto = {
        email: "<EMAIL>",
        password: "password123",
      };

      const result = await controller.loginWithEmail(loginDto);

      expect(result.data.user).not.toHaveProperty("isFirstLogin");
    });
  });

  describe("Integration Tests", () => {
    it("should handle complete user journey: phone registration -> profile -> logout", async () => {
      // 1. 发送验证码
      (authService.sendPhoneVerificationCode as jest.Mock).mockResolvedValue({
        success: true,
        message: "验证码发送成功",
      });

      const smsResult = await controller.sendVerificationCode({
        phone: "13800138000",
      });
      expect(smsResult.success).toBe(true);

      // 2. 手机号注册登录
      const firstLoginResult = {
        ...mockLoginResult,
        isFirstLogin: true,
      };

      (authService.loginWithPhone as jest.Mock).mockResolvedValue(
        firstLoginResult,
      );

      const loginResult = await controller.loginWithPhone(
        { phone: "13800138000", code: "123456" },
        mockRequest,
      );
      expect(loginResult.message).toBe("注册并登录成功");

      // 3. 获取用户信息
      const profileResult = await controller.getProfile(mockRequest);
      expect(profileResult.success).toBe(true);

      // 4. 登出
      const logoutResult = await controller.logout();
      expect(logoutResult.success).toBe(true);
    });

    it("should handle email user journey: register -> login -> set password -> refresh", async () => {
      // 1. 邮箱注册
      (authService.registerWithEmail as jest.Mock).mockResolvedValue(
        mockRegisterResult,
      );

      const registerResult = await controller.registerWithEmail({
        email: "<EMAIL>",
        password: "password123",
        username: "testuser",
      });
      expect(registerResult.success).toBe(true);

      // 2. 邮箱登录
      (authService.loginWithEmail as jest.Mock).mockResolvedValue(
        mockLoginResult,
      );

      const loginResult = await controller.loginWithEmail({
        email: "<EMAIL>",
        password: "password123",
      });
      expect(loginResult.success).toBe(true);

      // 3. 设置新邮箱密码
      const updatedUser = { ...mockUser, email: "<EMAIL>" };
      (authService.setEmailAndPassword as jest.Mock).mockResolvedValue(
        updatedUser,
      );

      const setResult = await controller.setEmailAndPassword(mockRequest, {
        email: "<EMAIL>",
        password: "newpassword123",
      });
      expect(setResult.success).toBe(true);

      // 4. 刷新令牌
      (authService.refreshToken as jest.Mock).mockResolvedValue(
        mockTokenResult,
      );

      const refreshResult = await controller.refreshToken({
        refreshToken: "refresh_token",
      });
      expect(refreshResult.success).toBe(true);
    });
  });

  describe("Edge Cases and Error Scenarios", () => {
    it("should handle concurrent login attempts", async () => {
      const loginDto: PhoneLoginDto = {
        phone: "13800138000",
        code: "123456",
      };

      (authService.loginWithPhone as jest.Mock).mockResolvedValue(
        mockLoginResult,
      );

      // 并发登录请求
      const promises = [
        controller.loginWithPhone(loginDto, mockRequest),
        controller.loginWithPhone(loginDto, mockRequest),
        controller.loginWithPhone(loginDto, mockRequest),
      ];

      const results = await Promise.all(promises);

      results.forEach((result) => {
        expect(result.success).toBe(true);
        expect(result.message).toBe("登录成功");
      });
    });

    it("should handle service timeouts gracefully", async () => {
      const timeoutError = new Error("Request timeout");
      timeoutError.name = "TimeoutError";

      (authService.sendPhoneVerificationCode as jest.Mock).mockRejectedValue(
        timeoutError,
      );

      await expect(
        controller.sendVerificationCode({ phone: "13800138000" }),
      ).rejects.toThrow("Request timeout");
    });

    it("should handle malformed request data", async () => {
      const malformedLoginDto = {
        phone: "",
        code: "",
      };

      (authService.loginWithPhone as jest.Mock).mockRejectedValue(
        new Error("Invalid input data"),
      );

      await expect(
        controller.loginWithPhone(malformedLoginDto, mockRequest),
      ).rejects.toThrow("Invalid input data");
    });

    it("should handle very long input strings", async () => {
      const longEmail = "a".repeat(1000) + "@example.com";
      const longPassword = "p".repeat(1000);

      (authService.registerWithEmail as jest.Mock).mockRejectedValue(
        new Error("Input too long"),
      );

      await expect(
        controller.registerWithEmail({
          email: longEmail,
          password: longPassword,
          username: "test",
        }),
      ).rejects.toThrow("Input too long");
    });

    it("should handle special characters in input", async () => {
      const specialUsername = "用户@#$%^&*()_+-=[]{}|;':\",./<>?";

      (authService.registerWithEmail as jest.Mock).mockResolvedValue({
        ...mockRegisterResult,
        user: { ...mockUser, username: specialUsername },
      });

      const result = await controller.registerWithEmail({
        email: "<EMAIL>",
        password: "password123",
        username: specialUsername,
      });

      expect(result.success).toBe(true);
      expect(result.data.user.username).toBe(specialUsername);
    });

    it("should handle null and undefined values in user data", async () => {
      const userWithNulls = {
        ...mockUser,
        phone: null,
        email: null,
        avatarUrl: null,
        birthDate: null,
      };

      const mockRequestWithNulls = {
        ...mockRequest,
        user: userWithNulls,
      };

      const result = await controller.getProfile(mockRequestWithNulls);

      expect(result.success).toBe(true);
      expect(result.data.user.phone).toBeNull();
      expect(result.data.user.email).toBeNull();
    });
  });

  describe("Security and Validation", () => {
    it("should handle SQL injection attempts in inputs", async () => {
      const sqlInjectionEmail = "'; DROP TABLE users; --";

      (authService.registerWithEmail as jest.Mock).mockRejectedValue(
        new Error("Invalid email format"),
      );

      await expect(
        controller.registerWithEmail({
          email: sqlInjectionEmail,
          password: "password123",
          username: "test",
        }),
      ).rejects.toThrow("Invalid email format");
    });

    it("should handle XSS attempts in inputs", async () => {
      const xssUsername = "<script>alert('xss')</script>";

      (authService.registerWithEmail as jest.Mock).mockRejectedValue(
        new Error("Invalid username format"),
      );

      await expect(
        controller.registerWithEmail({
          email: "<EMAIL>",
          password: "password123",
          username: xssUsername,
        }),
      ).rejects.toThrow("Invalid username format");
    });

    it("should handle token manipulation attempts", async () => {
      const maliciousToken =
        "eyJhbGciOiJub25lIiwidHlwIjoiSldUIn0.malicious.payload";

      (authService.refreshToken as jest.Mock).mockRejectedValue(
        new Error("Invalid token signature"),
      );

      await expect(
        controller.refreshToken({ refreshToken: maliciousToken }),
      ).rejects.toThrow("Invalid token signature");
    });
  });

  it("should be defined", () => {
    expect(controller).toBeDefined();
  });
});
