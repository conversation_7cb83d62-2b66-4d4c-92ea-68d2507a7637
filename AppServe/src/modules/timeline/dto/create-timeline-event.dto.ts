import {
  IsUUID,
  IsString,
  IsEnum,
  IsOptional,
  IsArray,
  IsBoolean,
  IsDateString,
  MaxLength,
  <PERSON><PERSON>eng<PERSON>,
} from "class-validator";
import { Type } from "class-transformer";
import {
  EventType,
  EventVisibility,
  EventImportance,
} from "../entities/timeline-event.entity";

/**
 * 创建时间线事件DTO
 */
export class CreateTimelineEventDto {
  @IsString({
    message: "事件标题必须是字符串",
  })
  @MinLength(1, {
    message: "事件标题不能为空",
  })
  @MaxLength(200, {
    message: "事件标题不能超过200个字符",
  })
  title: string;

  @IsOptional()
  @IsString({
    message: "事件描述必须是字符串",
  })
  @MaxLength(2000, {
    message: "事件描述不能超过2000个字符",
  })
  description?: string;

  @IsEnum(EventType, {
    message: "事件类型必须是有效的选项",
  })
  eventType: EventType;

  @IsOptional()
  @IsEnum(EventVisibility, {
    message: "可见性设置必须是有效的选项",
  })
  visibility?: EventVisibility = EventVisibility.PRIVATE;

  @IsOptional()
  @IsEnum(EventImportance, {
    message: "重要程度必须是有效的选项",
  })
  importance?: EventImportance = EventImportance.MEDIUM;

  @IsDateString(
    {},
    {
      message: "事件发生日期必须是有效的日期格式",
    },
  )
  eventDate: string;

  @IsOptional()
  @IsDateString(
    {},
    {
      message: "事件结束日期必须是有效的日期格式",
    },
  )
  endDate?: string;

  @IsOptional()
  @IsString({
    message: "地点必须是字符串",
  })
  @MaxLength(200, {
    message: "地点不能超过200个字符",
  })
  location?: string;

  @IsOptional()
  @IsArray({
    message: "相关人员必须是数组",
  })
  @IsString({
    each: true,
    message: "每个相关人员必须是字符串",
  })
  @MaxLength(50, {
    each: true,
    message: "每个相关人员名称不能超过50个字符",
  })
  relatedPeople?: string[];

  @IsOptional()
  @IsArray({
    message: "事件标签必须是数组",
  })
  @IsString({
    each: true,
    message: "每个标签必须是字符串",
  })
  @MaxLength(30, {
    each: true,
    message: "每个标签不能超过30个字符",
  })
  tags?: string[];

  @IsOptional()
  @IsUUID("4", {
    message: "关联的故事ID必须是有效的UUID格式",
  })
  relatedStoryId?: string;

  @IsOptional()
  @IsArray({
    message: "图片URL列表必须是数组",
  })
  @IsString({
    each: true,
    message: "每个图片URL必须是字符串",
  })
  images?: string[];

  @IsOptional()
  @IsArray({
    message: "附件URL列表必须是数组",
  })
  @IsString({
    each: true,
    message: "每个附件URL必须是字符串",
  })
  attachments?: string[];

  @IsOptional()
  @IsString({
    message: "事件颜色标记必须是字符串",
  })
  @MaxLength(50, {
    message: "事件颜色标记不能超过50个字符",
  })
  color?: string;

  @IsOptional()
  @IsString({
    message: "事件图标必须是字符串",
  })
  @MaxLength(50, {
    message: "事件图标不能超过50个字符",
  })
  icon?: string;

  @IsOptional()
  @IsBoolean({
    message: "是否为里程碑事件必须是布尔值",
  })
  isMilestone?: boolean = false;

  @IsOptional()
  @IsBoolean({
    message: "是否置顶显示必须是布尔值",
  })
  isPinned?: boolean = false;

  @IsOptional()
  @Type(() => Object)
  customData?: {
    category?: string;
    subCategory?: string;
    duration?: string;
    outcome?: string;
    impact?: string;
    lessons?: string[];
    linkedEvents?: string[];
  };

  @IsOptional()
  @Type(() => Object)
  metadata?: Record<string, unknown>;
}
