import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../core/themes/app_theme.dart';
import 'routes/app_router.dart';

/// YGS应用主Widget
class YgsApp extends ConsumerWidget {
  const YgsApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final router = ref.watch(appRouterProvider);
    
    // 使用ScreenUtilInit包装应用，配置750px设计稿
    return ScreenUtilInit(
      // 设计稿尺寸 - 基于实际设计稿 (750px * 1624px)
      designSize: const Size(750, 1624),
      // 最小文字适配
      minTextAdapt: true,
      // 分屏支持
      splitScreenMode: true,
      // 禁用字体跟随系统缩放，确保跨平台一致性
      useInheritedMediaQuery: true,
      builder: (context, child) {
        return MaterialApp.router(
          title: 'YGS - 有故事',
          debugShowCheckedModeBanner: false,
          
          // 路由配置
          routerConfig: router,
          
          // 主题配置
          theme: AppTheme.lightTheme,
          
          // 国际化配置
          localizationsDelegates: const [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [
            Locale('zh', 'CN'),
            Locale('en', 'US'),
          ],
          locale: const Locale('zh', 'CN'),
          
          // 构建器 - 禁用系统字体缩放，确保跨平台字体一致性
          builder: (context, child) {
            return MediaQuery(
              // 固定文字缩放因子为1.0，避免系统设置和平台差异影响
              data: MediaQuery.of(context).copyWith(textScaler: const TextScaler.linear(1.0)),
              child: child ?? const SizedBox.shrink(),
            );
          },
        );
      },
    );
  }
}