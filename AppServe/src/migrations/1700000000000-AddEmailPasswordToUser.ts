import type { MigrationInterface, QueryRunner } from "typeorm";
import { TableColumn } from "typeorm";

export class AddEmailPasswordToUser1700000000000 implements MigrationInterface {
  name = "AddEmailPasswordToUser1700000000000";

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 添加email字段
    await queryRunner.addColumn(
      "users",
      new TableColumn({
        name: "email",
        type: "varchar",
        isNullable: true,
        isUnique: true,
        comment: "邮箱",
      }),
    );

    // 添加passwordHash字段
    await queryRunner.addColumn(
      "users",
      new TableColumn({
        name: "passwordHash",
        type: "varchar",
        isNullable: true,
        comment: "密码hash",
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 删除passwordHash字段
    await queryRunner.dropColumn("users", "passwordHash");

    // 删除email字段
    await queryRunner.dropColumn("users", "email");
  }
}
