import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../tokens/ygs_colors.dart';

/// YGS设计系统 - 全局骨架图组件系统
/// 基于企业级标准和Material Design规范实现
/// 支持全局复用和性能优化
class YgsSkeleton {
  // 私有构造函数，防止实例化
  YgsSkeleton._();

  /// 基础骨架图颜色配置 - 符合YGS设计风格（淡化版本）
  static const Color _baseColor = Color(0xFFF5F5F5);      // 极浅灰底色
  static const Color _highlightColor = Color(0xFFFAFAFA);  // 高亮色
  static const Color _shimmerBase = Color(0xFFF0F0F0);     // Shimmer基础色（淡化）
  static const Color _shimmerHighlight = Color(0xFFF8F8F8); // Shimmer高亮色（淡化）

  /// Material Design标准动画参数
  static const Duration _animationDuration = Duration(milliseconds: 1500);
  static const Curve _animationCurve = Curves.easeInOut;
}

/// 基础骨架图容器组件
/// 提供统一的骨架图外观和动画效果
class YgsSkeletonContainer extends StatefulWidget {
  const YgsSkeletonContainer({
    required this.width,
    required this.height,
    this.borderRadius,
    this.margin,
    this.child,
    super.key,
  });

  final double width;
  final double height;
  final BorderRadius? borderRadius;
  final EdgeInsetsGeometry? margin;
  final Widget? child;

  @override
  State<YgsSkeletonContainer> createState() => _YgsSkeletonContainerState();
}

class _YgsSkeletonContainerState extends State<YgsSkeletonContainer>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: YgsSkeleton._animationDuration,
      vsync: this,
    );

    _animation = Tween<double>(
      begin: -1.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: YgsSkeleton._animationCurve,
    ));

    _animationController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width,
      height: widget.height,
      margin: widget.margin,
      decoration: BoxDecoration(
        borderRadius: widget.borderRadius ?? BorderRadius.circular(8.r),
      ),
      child: AnimatedBuilder(
        animation: _animation,
        builder: (context, child) {
          return Container(
            decoration: BoxDecoration(
              borderRadius: widget.borderRadius ?? BorderRadius.circular(8.r),
              gradient: LinearGradient(
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
                colors: const [
                  YgsSkeleton._shimmerBase,
                  YgsSkeleton._shimmerHighlight,
                  YgsSkeleton._shimmerBase,
                ],
                stops: [
                  (_animation.value - 1.0).clamp(0.0, 1.0),
                  _animation.value.clamp(0.0, 1.0),
                  (_animation.value + 1.0).clamp(0.0, 1.0),
                ],
              ),
            ),
            child: widget.child,
          );
        },
      ),
    );
  }
}

/// 骨架图文本行组件
class YgsSkeletonText extends StatelessWidget {
  const YgsSkeletonText({
    this.width,
    this.height,
    this.margin,
    super.key,
  });

  final double? width;
  final double? height;
  final EdgeInsetsGeometry? margin;

  @override
  Widget build(BuildContext context) {
    return YgsSkeletonContainer(
      width: width ?? double.infinity,
      height: height ?? 20.h,
      margin: margin,
      borderRadius: BorderRadius.circular(4.r),
    );
  }
}

/// 骨架图圆形头像组件
class YgsSkeletonAvatar extends StatelessWidget {
  const YgsSkeletonAvatar({
    required this.size,
    this.margin,
    super.key,
  });

  final double size;
  final EdgeInsetsGeometry? margin;

  @override
  Widget build(BuildContext context) {
    return YgsSkeletonContainer(
      width: size,
      height: size,
      margin: margin,
      borderRadius: BorderRadius.circular(size / 2),
    );
  }
}

/// 骨架图矩形图片组件
class YgsSkeletonImage extends StatelessWidget {
  const YgsSkeletonImage({
    required this.width,
    required this.height,
    this.borderRadius,
    this.margin,
    super.key,
  });

  final double width;
  final double height;
  final BorderRadius? borderRadius;
  final EdgeInsetsGeometry? margin;

  @override
  Widget build(BuildContext context) {
    return YgsSkeletonContainer(
      width: width,
      height: height,
      margin: margin,
      borderRadius: borderRadius ?? BorderRadius.circular(8.r),
      child: Center(
        child: Icon(
          Icons.image_outlined,
          size: (width * 0.2).clamp(16.0, 32.0),
          color: YgsSkeleton._baseColor.withOpacity(0.3),
        ),
      ),
    );
  }
}

/// 故事卡片骨架图组件
/// 严格匹配实际故事卡片的布局结构
class YgsStoryCardSkeleton extends StatelessWidget {
  const YgsStoryCardSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 690.w,
      margin: EdgeInsets.only(bottom: 21.h),
      decoration: BoxDecoration(
        color: YgsColors.backgroundCard,
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Padding(
        padding: EdgeInsets.fromLTRB(44.w, 32.w, 44.w, 32.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 作者信息骨架
            _buildAuthorSkeleton(),
            
            SizedBox(height: 38.h),
            
            // 故事标题骨架
            _buildTitleSkeleton(),
            
            SizedBox(height: 18.h),
            
            // 故事内容骨架
            _buildContentSkeleton(),
            
            SizedBox(height: 45.h),
            
            // 故事图片骨架
            _buildImagesSkeleton(),
            
            SizedBox(height: 34.h),
            
            // 互动头像骨架
            _buildInteractionAvatarsSkeleton(),
            
            SizedBox(height: 16.h),
            
            // 底部信息骨架
            _buildBottomInfoSkeleton(),
          ],
        ),
      ),
    );
  }

  /// 作者信息骨架
  Widget _buildAuthorSkeleton() {
    return Row(
      children: [
        // 作者头像骨架
        YgsSkeletonAvatar(size: 92.w),
        
        SizedBox(width: 10.w),
        
        // 作者信息骨架
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 作者名字
              YgsSkeletonText(
                width: 120.w,
                height: 24.h,
              ),
              
              SizedBox(height: 8.h),
              
              // 分享信息
              Row(
                children: [
                  YgsSkeletonText(
                    width: 80.w,
                    height: 18.h,
                  ),
                  
                  SizedBox(width: 7.w),
                  
                  YgsSkeletonText(
                    width: 60.w,
                    height: 18.h,
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 标题骨架
  Widget _buildTitleSkeleton() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        YgsSkeletonText(
          width: double.infinity,
          height: 24.h,
        ),
        SizedBox(height: 8.h),
        YgsSkeletonText(
          width: 200.w,
          height: 24.h,
        ),
      ],
    );
  }

  /// 内容骨架
  Widget _buildContentSkeleton() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        YgsSkeletonText(
          width: double.infinity,
          height: 20.h,
        ),
        SizedBox(height: 6.h),
        YgsSkeletonText(
          width: double.infinity,
          height: 20.h,
        ),
        SizedBox(height: 6.h),
        YgsSkeletonText(
          width: 300.w,
          height: 20.h,
        ),
      ],
    );
  }

  /// 图片骨架
  Widget _buildImagesSkeleton() {
    return Row(
      children: [
        YgsSkeletonImage(
          width: 160.w,
          height: 160.w,
          borderRadius: BorderRadius.circular(8.r),
        ),
        SizedBox(width: 20.w),
        YgsSkeletonImage(
          width: 160.w,
          height: 160.w,
          borderRadius: BorderRadius.circular(8.r),
        ),
        SizedBox(width: 20.w),
        YgsSkeletonImage(
          width: 160.w,
          height: 160.w,
          borderRadius: BorderRadius.circular(8.r),
        ),
      ],
    );
  }

  /// 互动头像骨架
  Widget _buildInteractionAvatarsSkeleton() {
    return Row(
      children: [
        YgsSkeletonAvatar(size: 48.w),
        SizedBox(width: 40.w),
        YgsSkeletonAvatar(size: 48.w),
        SizedBox(width: 40.w),
        YgsSkeletonAvatar(size: 48.w),
      ],
    );
  }

  /// 底部信息骨架
  Widget _buildBottomInfoSkeleton() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // 日期和地点
        YgsSkeletonText(
          width: 150.w,
          height: 16.h,
        ),
        
        // 点赞区域
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            YgsSkeletonContainer(
              width: 32.w,
              height: 32.w,
              borderRadius: BorderRadius.circular(16.w),
            ),
            
            SizedBox(width: 8.w),
            
            YgsSkeletonText(
              width: 30.w,
              height: 16.h,
            ),
          ],
        ),
      ],
    );
  }

}

/// 扁平化三点加载动画组件
/// 符合YGS设计风格的极简加载指示器
class YgsThreeDotsLoading extends StatefulWidget {
  const YgsThreeDotsLoading({
    this.size = 8.0,
    this.color,
    super.key,
  });

  final double size;
  final Color? color;

  @override
  State<YgsThreeDotsLoading> createState() => _YgsThreeDotsLoadingState();
}

class _YgsThreeDotsLoadingState extends State<YgsThreeDotsLoading>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    
    // 创建3个独立的动画控制器
    _controllers = List.generate(3, (index) {
      return AnimationController(
        duration: const Duration(milliseconds: 600),
        vsync: this,
      );
    });

    // 创建3个动画，每个都有不同的延迟
    _animations = _controllers.map((controller) {
      return Tween<double>(
        begin: 0.4, // 最小透明度
        end: 1.0,   // 最大透明度
      ).animate(CurvedAnimation(
        parent: controller,
        curve: Curves.easeInOut,
      ));
    }).toList();

    // 启动动画，每个点有不同的延迟
    _startAnimations();
  }

  void _startAnimations() async {
    while (mounted) {
      for (int i = 0; i < _controllers.length; i++) {
        if (mounted) {
          _controllers[i].forward();
        }
        await Future.delayed(const Duration(milliseconds: 200));
      }
      
      // 等待所有动画完成
      await Future.delayed(const Duration(milliseconds: 400));
      
      // 重置所有动画
      for (final controller in _controllers) {
        if (mounted) {
          controller.reset();
        }
      }
      
      // 循环间隔
      await Future.delayed(const Duration(milliseconds: 300));
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final dotColor = widget.color ?? YgsColors.textSecondary;
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(3, (index) {
        return AnimatedBuilder(
          animation: _animations[index],
          builder: (context, child) {
            return Container(
              width: widget.size.w,
              height: widget.size.w,
              margin: EdgeInsets.symmetric(horizontal: 2.w),
              decoration: BoxDecoration(
                color: dotColor.withOpacity(_animations[index].value),
                shape: BoxShape.circle,
              ),
            );
          },
        );
      }),
    );
  }
}

/// 骨架图列表组件
/// 用于首页下拉刷新时显示2张故事卡片骨架图
class YgsSkeletonList extends StatelessWidget {
  const YgsSkeletonList({
    this.itemCount = 2,
    super.key,
  });

  final int itemCount;

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: EdgeInsets.fromLTRB(30.w, 24.h, 30.w, 168.h + 40.h),
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: itemCount,
      itemBuilder: (context, index) {
        return const YgsStoryCardSkeleton();
      },
    );
  }
}