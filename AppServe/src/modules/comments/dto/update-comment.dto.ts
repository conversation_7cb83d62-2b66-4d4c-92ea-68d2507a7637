import { IsString, IsOptional, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "class-validator";

/**
 * 更新评论DTO
 */
export class UpdateCommentDto {
  @IsString({
    message: "评论内容必须是字符串",
  })
  @MinLength(1, {
    message: "评论内容不能为空",
  })
  @MaxLength(2000, {
    message: "评论内容不能超过2000个字符",
  })
  content: string;

  @IsOptional()
  metadata?: Record<string, unknown>;
}
