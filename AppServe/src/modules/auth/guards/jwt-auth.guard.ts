import type { CanActivate, ExecutionContext } from "@nestjs/common";
import { Injectable, UnauthorizedException } from "@nestjs/common";
import type { FastifyRequest } from "fastify";
import { AuthService } from "../auth.service";
import type { AuthenticatedRequest, UserPayload } from "../../../common/types";

@Injectable()
export class JwtAuthGuard implements CanActivate {
  constructor(private readonly authService: AuthService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<AuthenticatedRequest>();
    const token = this.extractTokenFromHeader(request);

    if (!token) {
      throw new UnauthorizedException("访问令牌不存在");
    }

    try {
      const user = await this.authService.validateToken(token);
      // 将User实体转换为UserPayload格式
      const userPayload: UserPayload = {
        id: user.id,
        username: user.username || user.nickname || "unknown",
        nickname: user.nickname,
        email: user.email,
        phone: user.phone,
        isVerified: user.isActive ?? false, // 使用isActive作为验证状态
        permissions: [], // 默认空权限列表
        role: "user", // 默认用户角色
      };
      request.user = userPayload;
      return true;
    } catch (error) {
      throw new UnauthorizedException("无效的访问令牌");
    }
  }

  private extractTokenFromHeader(request: FastifyRequest): string | undefined {
    const [type, token] = request.headers.authorization?.split(" ") ?? [];
    return type === "Bearer" ? token : undefined;
  }
}
