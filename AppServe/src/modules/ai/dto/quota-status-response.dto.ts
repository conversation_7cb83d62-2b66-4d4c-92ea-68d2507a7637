import { ApiProperty } from "@nestjs/swagger";

/**
 * 配额状态响应DTO
 * 用于返回用户AI功能配额使用情况
 */
export class QuotaStatusResponseDto {
  @ApiProperty({
    description: "剩余配额数量",
    example: 2,
    minimum: 0,
    maximum: 3,
  })
  remainingQuota: number;

  @ApiProperty({
    description: "每日总配额",
    example: 3,
  })
  totalDailyQuota: number;

  @ApiProperty({
    description: "已使用配额",
    example: 1,
    minimum: 0,
    maximum: 3,
  })
  usedQuota: number;

  @ApiProperty({
    description: "配额重置时间",
    example: "2025-07-09T00:00:00.000Z",
    type: Date,
  })
  resetTime: Date;
}
