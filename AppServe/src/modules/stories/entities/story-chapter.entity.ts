import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from "typeorm";
import { Story } from "./story.entity";

@Entity("story_chapters")
export class StoryChapter {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ name: "story_id" })
  storyId: string;

  @Column({ length: 255, nullable: true, comment: "章节标题" })
  title: string;

  @Column({ type: "text", comment: "章节内容" })
  content: string;

  @Column({ name: "chapter_order", comment: "章节排序" })
  chapterOrder: number;

  @Column({
    name: "image_urls",
    type: "text",
    array: true,
    nullable: true,
    comment: "章节图片URLs",
  })
  imageUrls: string[];

  @CreateDateColumn({ name: "created_at", comment: "创建时间" })
  createdAt: Date;

  // 关联关系
  @ManyToOne(() => Story, (story) => story.chapters, { onDelete: "CASCADE" })
  @JoinColumn({ name: "story_id" })
  story: Story;
}
