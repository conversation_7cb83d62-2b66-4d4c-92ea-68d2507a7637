import { Module, forwardRef } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { StoriesService } from "./stories.service";
import { StoriesController } from "./stories.controller";
import { StoryCharactersController } from "./controllers/story-characters.controller";
import { StoryPermissionService } from "./services/story-permission.service";
import {
  StoryAccessGuard,
  StoryEditGuard,
  StoryActionGuard,
} from "./guards/story-access.guard";
import { Story } from "./entities/story.entity";
import { StoryChapter } from "./entities/story-chapter.entity";
import { StoryShare } from "./entities/story-share.entity";
import { Theme } from "./entities/theme.entity";
import { Character } from "../characters/entities/character.entity";
import { CharacterLighting } from "../characters/entities/character-lighting.entity";
import { User } from "../users/entities/user.entity";
import { UserRelationship } from "../users/entities/user-relationship.entity";
import { CharactersModule } from "../characters/characters.module";
import { AuthModule } from "../auth/auth.module";
import { LightingModule } from "../lighting/lighting.module";

/**
 * 故事模块 - v1.0.0
 * 负责故事创建、管理、权限控制和内容安全检测
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      Story,
      StoryChapter,
      StoryShare,
      Theme,
      Character,
      CharacterLighting,
      User,
      UserRelationship,
    ]),
    forwardRef(() => CharactersModule),
    forwardRef(() => AuthModule), // 修复JwtAuthGuard依赖问题
    forwardRef(() => LightingModule), // 支持新故事通知功能
  ],
  controllers: [StoriesController, StoryCharactersController],
  providers: [
    StoriesService,
    StoryPermissionService,
    StoryAccessGuard,
    StoryEditGuard,
    StoryActionGuard,
  ],
  exports: [StoriesService, StoryPermissionService],
})
export class StoriesModule {}
