import {
  <PERSON>Optional,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
} from "class-validator";
import { Transform } from "class-transformer";

/**
 * 故事查询DTO
 * 用于故事列表查询的参数验证
 */
export class StoryQueryDto {
  @IsOptional()
  @IsInt()
  @Min(1)
  @Transform(({ value }) => parseInt(value))
  page?: number = 1;

  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(100)
  @Transform(({ value }) => parseInt(value))
  limit?: number = 10;

  @IsOptional()
  @IsUUID()
  authorId?: string;

  @IsOptional()
  @IsUUID()
  themeId?: string;

  @IsOptional()
  @IsEnum(["private", "friends", "public", "characters_only"])
  permissionLevel?: string;

  @IsOptional()
  @IsEnum([1, 2, 3])
  @Transform(({ value }) => parseInt(value))
  status?: number;

  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsEnum(["newest", "oldest", "most_liked", "most_viewed"])
  sortBy?: string = "newest";
}

/**
 * 内容验证结果
 */
export class ContentValidationResult {
  isValid: boolean;
  aiSafetyScore: number;
  violations: string[];
  suggestions: string[];
}
