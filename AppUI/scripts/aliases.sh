#!/bin/bash

# ===========================================
# 有故事 APP - 开发命令别名配置
# ===========================================

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 获取项目根目录
PROJECT_ROOT=$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)

echo -e "${BLUE}📝 有故事 APP 开发命令别名配置${NC}"
echo ""
echo -e "${YELLOW}# 有故事 APP 开发别名${NC}"
echo "alias ygs-start='cd $PROJECT_ROOT && ./scripts/dev-start.sh'"
echo "alias ygs-check='cd $PROJECT_ROOT && ./scripts/check-network.sh'"
echo "alias ygs-setup='cd $PROJECT_ROOT && ./scripts/setup-android-network.sh'"
echo "alias ygs-clean='cd $PROJECT_ROOT && ./scripts/clean-build.sh'"
echo "alias ygs-android-fast='cd $PROJECT_ROOT && ./scripts/fast-android.sh'"
echo "alias ygs-android='cd $PROJECT_ROOT && npx react-native run-android'"
echo "alias ygs-ios='cd $PROJECT_ROOT && npx react-native run-ios'"
echo "alias ygs-metro='cd $PROJECT_ROOT && npx react-native start'"
echo "alias ygs-backend='cd $PROJECT_ROOT/../AppServe && docker-compose up'"
echo ""
echo -e "${BLUE}🔧 快速添加到 ~/.zshrc 的命令：${NC}"
echo 'echo "# 有故事 APP 开发别名" >> ~/.zshrc'
echo "echo \"alias ygs-start='cd $PROJECT_ROOT && ./scripts/dev-start.sh'\" >> ~/.zshrc"
echo "echo \"alias ygs-check='cd $PROJECT_ROOT && ./scripts/check-network.sh'\" >> ~/.zshrc"
echo "echo \"alias ygs-setup='cd $PROJECT_ROOT && ./scripts/setup-android-network.sh'\" >> ~/.zshrc"
echo "echo \"alias ygs-clean='cd $PROJECT_ROOT && ./scripts/clean-build.sh'\" >> ~/.zshrc"
echo "echo \"alias ygs-android-fast='cd $PROJECT_ROOT && ./scripts/fast-android.sh'\" >> ~/.zshrc"
echo "echo \"alias ygs-android='cd $PROJECT_ROOT && npx react-native run-android'\" >> ~/.zshrc"
echo "echo \"alias ygs-ios='cd $PROJECT_ROOT && npx react-native run-ios'\" >> ~/.zshrc"
echo "echo \"alias ygs-metro='cd $PROJECT_ROOT && npx react-native start'\" >> ~/.zshrc"
echo "echo \"alias ygs-backend='cd $PROJECT_ROOT/../AppServe && docker-compose up'\" >> ~/.zshrc"
echo 'source ~/.zshrc'
echo ""
echo -e "${GREEN}💡 添加别名后，就可以在任何目录使用 ygs-start、ygs-check 等命令了！${NC}" 