/**
 * 企业级图像处理服务集成测试
 * 测试ImageService和SecureImageService的完整业务流程
 * 包括权限控制、批量处理、缓存机制、性能优化等
 */

import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { ConfigService } from "@nestjs/config";
import { NotFoundException } from "@nestjs/common";
import { ImageService, ImageType } from "./image.service";
import { SecureImageService, ImageAccessLevel } from "./secure-image.service";
import { EnhancedRedisService } from "../../common/services/enhanced-redis.service";
import type { ImageAccessRequest } from "./image.service";
import type { SecureImageRequest } from "./secure-image.service";

// Mock OSS模块 - 企业级Mock策略
const mockOssInstance = {
  head: jest.fn(),
  get: jest.fn(),
  signatureUrl: jest.fn(),
};

jest.mock("ali-oss", () => {
  return jest.fn().mockImplementation(() => mockOssInstance);
});

describe("企业级图像处理服务集成测试", () => {
  let imageService: ImageService;
  let secureImageService: SecureImageService;

  // 企业级测试数据集
  const testData = {
    images: {
      avatar: "ygs-app/avatars/user123/avatar.jpg",
      storyCover: "ygs-app/stories/story456/cover.jpg",
    },
    users: {
      owner: "user-123",
      viewer: "user-456",
    },
    mockOSSConfig: {
      region: "oss-cn-hangzhou",
      bucket: "test-bucket",
      cdnDomain: "https://cdn.example.com",
      baseDir: "ygs/",
    },
  };

  beforeEach(async () => {
    jest.clearAllMocks();

    mockOssInstance.head.mockClear();
    mockOssInstance.get.mockClear();
    mockOssInstance.signatureUrl.mockClear();

    const mockConfigService = {
      get: jest.fn((key: string) => {
        if (key === "oss") {
          return testData.mockOSSConfig;
        }
        return testData.mockOSSConfig[
          key as keyof typeof testData.mockOSSConfig
        ];
      }),
    };

    const mockRedisService = {
      setCache: jest.fn().mockResolvedValue(true),
      getCache: jest.fn().mockResolvedValue(null),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ImageService,
        SecureImageService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: EnhancedRedisService,
          useValue: mockRedisService,
        },
      ],
    }).compile();

    imageService = module.get<ImageService>(ImageService);
    secureImageService = module.get<SecureImageService>(SecureImageService);
  });

  describe("🏢 企业级图像权限控制测试", () => {
    beforeEach(() => {
      mockOssInstance.head.mockResolvedValue({
        res: { status: 200 },
        size: 1024,
        lastModified: new Date(),
      });
    });

    it("should handle complete image access workflow", async () => {
      const accessRequests: ImageAccessRequest[] = [
        {
          imagePath: testData.images.avatar,
          imageType: ImageType.AVATAR,
          userId: testData.users.viewer,
        },
        {
          imagePath: testData.images.storyCover,
          imageType: ImageType.STORY_COVER,
          userId: testData.users.viewer,
          storyId: "story456",
        },
      ];

      const results = await Promise.all(
        accessRequests.map((request) => imageService.checkImageAccess(request)),
      );

      expect(results).toEqual([true, true]);
      expect(mockOssInstance.head).toHaveBeenCalledTimes(2);
    });

    it("should handle missing image scenarios", async () => {
      const notFoundError = new Error("NoSuchKey") as Error & { code: string };
      notFoundError.code = "NoSuchKey";
      mockOssInstance.head.mockRejectedValue(notFoundError);

      const request: ImageAccessRequest = {
        imagePath: "nonexistent/image.jpg",
        imageType: ImageType.AVATAR,
        userId: testData.users.viewer,
      };

      await expect(imageService.checkImageAccess(request)).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe("🔐 企业级安全图像URL生成测试", () => {
    beforeEach(() => {
      mockOssInstance.head.mockResolvedValue({
        res: { status: 200 },
      });
    });

    it("should handle secure URL workflow", async () => {
      const mockSignedUrl = "https://signed-url.example.com/image.jpg";
      mockOssInstance.signatureUrl.mockReturnValue(mockSignedUrl);

      const secureRequests: SecureImageRequest[] = [
        {
          imagePath: "public/image.jpg",
          accessLevel: ImageAccessLevel.PUBLIC,
          userId: testData.users.viewer,
        },
        {
          imagePath: "private/image.jpg",
          accessLevel: ImageAccessLevel.PRIVATE,
          userId: testData.users.owner,
          expiresIn: 3600,
        },
      ];

      const results = await Promise.all(
        secureRequests.map((request) =>
          secureImageService.getSecureImageUrl(request),
        ),
      );

      expect(results[0]).toEqual({
        url: "https://cdn.example.com/ygs/public/image.jpg",
        accessLevel: ImageAccessLevel.PUBLIC,
        cacheable: true,
      });

      expect(results[1]).toEqual({
        url: mockSignedUrl,
        accessLevel: ImageAccessLevel.PRIVATE,
        expiresAt: expect.any(Date),
        cacheable: false,
      });
    });
  });

  describe("📊 企业级图像流处理测试", () => {
    it("should handle image stream retrieval", async () => {
      const mockBuffer = Buffer.from("test image data");
      mockOssInstance.get.mockResolvedValue({
        content: mockBuffer,
        res: { status: 200 },
      });

      const result = await imageService.getImageStream(testData.images.avatar);

      expect(result).toEqual(mockBuffer);
      expect(mockOssInstance.get).toHaveBeenCalledWith(testData.images.avatar);
    });
  });

  describe("🔍 企业级图像路径解析测试", () => {
    it("should parse image paths correctly", () => {
      const testCases = [
        {
          path: testData.images.avatar,
          expected: { type: ImageType.AVATAR },
        },
        {
          path: testData.images.storyCover,
          expected: { type: ImageType.STORY_COVER, resourceId: "story456" },
        },
      ];

      testCases.forEach(({ path, expected }) => {
        const result = imageService.parseImagePath(path);
        expect(result).toEqual(expected);
      });
    });
  });

  it("should be defined", () => {
    expect(imageService).toBeDefined();
    expect(secureImageService).toBeDefined();
  });
});
