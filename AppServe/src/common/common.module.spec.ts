/**
 * CommonModule 单元测试
 * 测试公共模块的依赖注入和模块配置
 */

import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { CACHE_MANAGER } from "@nestjs/cache-manager";
import type { Cache } from "@nestjs/cache-manager";
import { ConfigService } from "@nestjs/config";
import { EnhancedRedisService } from "./services/enhanced-redis.service";
import { DeviceFingerprintService } from "./services/device-fingerprint.service";
import { VerificationSecurityService } from "./services/verification-security.service";

describe("CommonModule", () => {
  let module: TestingModule;

  // 企业级Mock配置 - 完整的Cache接口实现
  const mockCacheManager = {
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
    reset: jest.fn(),
    wrap: jest.fn(),
    // 添加缺失的顶层方法
    mget: jest.fn(),
    mset: jest.fn(),
    mdel: jest.fn(),
    ttl: jest.fn(),
    keys: jest.fn(),
    clear: jest.fn(),
    on: jest.fn(),
    off: jest.fn(),
    disconnect: jest.fn(),
    store: {
      name: "memory",
      isCacheable: jest.fn(),
      get: jest.fn(),
      set: jest.fn(),
      del: jest.fn(),
      reset: jest.fn(),
      mget: jest.fn(),
      mset: jest.fn(),
      mdel: jest.fn(),
      keys: jest.fn(),
      ttl: jest.fn(),
    },
  } as unknown as jest.Mocked<Cache>;

  // Mock ConfigService
  const mockConfigService = {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    get: jest.fn((key: string, defaultValue?: any) => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const config: Record<string, any> = {
        REDIS_HOST: "localhost",
        REDIS_PORT: 6379,
        REDIS_PASSWORD: undefined,
        REDIS_DB: 0,
      };
      return config[key] ?? defaultValue;
    }),
  };

  // Mock EnhancedRedisService
  const mockRedisClient = {
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
    exists: jest.fn(),
    ttl: jest.fn(),
    expire: jest.fn(),
    incr: jest.fn(),
    decr: jest.fn(),
    keys: jest.fn(),
    multi: jest.fn().mockReturnValue({
      exec: jest.fn().mockResolvedValue([]),
    }),
    pipeline: jest.fn().mockReturnValue({
      exec: jest.fn().mockResolvedValue([]),
    }),
  };

  const mockEnhancedRedisService = {
    setCache: jest.fn(),
    getCache: jest.fn(),
    delCache: jest.fn(),
    getClient: jest.fn().mockReturnValue(mockRedisClient),
    onModuleInit: jest.fn(),
    onModuleDestroy: jest.fn(),
  };

  beforeEach(async () => {
    module = await Test.createTestingModule({
      providers: [
        // Mock服务以避免Redis连接问题
        {
          provide: EnhancedRedisService,
          useValue: mockEnhancedRedisService,
        },
        DeviceFingerprintService,
        VerificationSecurityService,
        {
          provide: CACHE_MANAGER,
          useValue: mockCacheManager,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();
  });

  afterEach(async () => {
    if (module) {
      await module.close();
    }
  });

  it("should be defined", () => {
    expect(module).toBeDefined();
  });

  describe("服务提供者", () => {
    it("should provide EnhancedRedisService", () => {
      const enhancedRedisService =
        module.get<EnhancedRedisService>(EnhancedRedisService);
      expect(enhancedRedisService).toBeDefined();
      expect(enhancedRedisService).toBe(mockEnhancedRedisService);
    });

    it("should provide DeviceFingerprintService", () => {
      const deviceFingerprintService = module.get<DeviceFingerprintService>(
        DeviceFingerprintService,
      );
      expect(deviceFingerprintService).toBeDefined();
      expect(deviceFingerprintService).toBeInstanceOf(DeviceFingerprintService);
    });

    it("should provide VerificationSecurityService", () => {
      const verificationSecurityService =
        module.get<VerificationSecurityService>(VerificationSecurityService);
      expect(verificationSecurityService).toBeDefined();
      expect(verificationSecurityService).toBeInstanceOf(
        VerificationSecurityService,
      );
    });
  });

  describe("依赖注入", () => {
    it("should inject CACHE_MANAGER", () => {
      const cacheManager = module.get(CACHE_MANAGER);
      expect(cacheManager).toBeDefined();
      expect(cacheManager).toBe(mockCacheManager);
    });
  });

  describe("模块配置", () => {
    it("should be global module", () => {
      // CommonModule应该是全局模块，可以被其他模块使用
      expect(module).toBeDefined();
    });

    it("should export EnhancedRedisService for other modules", () => {
      const enhancedRedisService =
        module.get<EnhancedRedisService>(EnhancedRedisService);
      expect(enhancedRedisService).toBeDefined();
    });

    it("should export DeviceFingerprintService for other modules", () => {
      const deviceFingerprintService = module.get<DeviceFingerprintService>(
        DeviceFingerprintService,
      );
      expect(deviceFingerprintService).toBeDefined();
    });

    it("should export VerificationSecurityService for other modules", () => {
      const verificationSecurityService =
        module.get<VerificationSecurityService>(VerificationSecurityService);
      expect(verificationSecurityService).toBeDefined();
    });
  });

  describe("公共服务功能", () => {
    it("should provide Redis functionality", async () => {
      const enhancedRedisService =
        module.get<EnhancedRedisService>(EnhancedRedisService);

      // 验证Redis服务具有基本功能
      expect(enhancedRedisService).toBeDefined();
      expect(typeof enhancedRedisService.setCache).toBe("function");
      expect(typeof enhancedRedisService.getCache).toBe("function");
      expect(typeof enhancedRedisService.delCache).toBe("function");
    });

    it("should provide device fingerprint functionality", async () => {
      const deviceFingerprintService = module.get<DeviceFingerprintService>(
        DeviceFingerprintService,
      );

      // 验证设备指纹服务具有指纹生成功能
      expect(deviceFingerprintService).toBeDefined();
      expect(typeof deviceFingerprintService.generateDeviceFingerprint).toBe(
        "function",
      );
    });

    it("should provide verification security functionality", async () => {
      const verificationSecurityService =
        module.get<VerificationSecurityService>(VerificationSecurityService);

      // 验证验证安全服务具有安全检查功能
      expect(verificationSecurityService).toBeDefined();
      expect(typeof verificationSecurityService.checkVerificationAttempt).toBe(
        "function",
      );
    });
  });

  describe("服务间依赖", () => {
    it("should inject required dependencies into services", () => {
      // 验证所有服务都能正确注入依赖
      const enhancedRedisService =
        module.get<EnhancedRedisService>(EnhancedRedisService);
      const deviceFingerprintService = module.get<DeviceFingerprintService>(
        DeviceFingerprintService,
      );
      const verificationSecurityService =
        module.get<VerificationSecurityService>(VerificationSecurityService);

      expect(enhancedRedisService).toBeDefined();
      expect(deviceFingerprintService).toBeDefined();
      expect(verificationSecurityService).toBeDefined();
    });
  });
});
