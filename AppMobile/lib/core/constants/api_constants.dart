/// API端点常量
class ApiConstants {
  ApiConstants._();
  
  // 认证相关
  static const String loginPhone = '/auth/login/phone';
  static const String loginEmail = '/auth/login/email';
  static const String refreshToken = '/auth/refresh';
  static const String logout = '/auth/logout';
  static const String sendSmsCode = '/auth/send-sms';
  static const String profile = '/auth/profile';
  
  // 用户相关
  static const String userProfile = '/users/profile';
  static const String updateProfile = '/users/profile';
  static const String uploadAvatar = '/users/avatar';
  static const String changePassword = '/users/password';
  static const String bindPhone = '/users/phone/bind';
  
  // 故事相关
  static const String stories = '/stories';
  static const String storyDetail = '/stories/{id}';
  static const String createStory = '/stories';
  static const String updateStory = '/stories/{id}';
  static const String deleteStory = '/stories/{id}';
  static const String publishStory = '/stories/{id}/publish';
  static const String unpublishStory = '/stories/{id}/unpublish';
  
  // 人物相关
  static const String characters = '/characters';
  static const String characterDetail = '/characters/{id}';
  static const String createCharacter = '/characters';
  static const String updateCharacter = '/characters/{id}';
  static const String deleteCharacter = '/characters/{id}';
  
  // 点亮系统
  static const String lightingRequests = '/lighting/requests';
  static const String createLightingRequest = '/lighting/requests';
  static const String approveLightingRequest = '/lighting/requests/{id}/approve';
  static const String rejectLightingRequest = '/lighting/requests/{id}/reject';
  static const String lightingHistory = '/lighting/history';
  static const String lightingPermissions = '/lighting/permissions';
  
  // 社交功能
  static const String follow = '/social/follow';
  static const String unfollow = '/social/unfollow';
  static const String followers = '/social/followers';
  static const String following = '/social/following';
  static const String blockUser = '/social/block';
  static const String unblockUser = '/social/unblock';
  
  // 互动功能
  static const String likeStory = '/interactions/stories/{id}/like';
  static const String unlikeStory = '/interactions/stories/{id}/unlike';
  static const String commentStory = '/interactions/stories/{id}/comments';
  static const String deleteComment = '/interactions/comments/{id}';
  static const String collectStory = '/interactions/stories/{id}/collect';
  static const String uncollectStory = '/interactions/stories/{id}/uncollect';
  static const String shareStory = '/interactions/stories/{id}/share';
  static const String reportContent = '/interactions/report';
  
  // 通知相关
  static const String notifications = '/notifications';
  static const String markNotificationRead = '/notifications/{id}/read';
  static const String markAllNotificationsRead = '/notifications/read-all';
  static const String notificationSettings = '/notifications/settings';
  
  // 搜索相关
  static const String searchStories = '/search/stories';
  static const String searchUsers = '/search/users';
  static const String searchCharacters = '/search/characters';
  static const String searchSuggestions = '/search/suggestions';
  
  // 统计相关
  static const String userStatistics = '/statistics/user';
  static const String storyStatistics = '/statistics/story/{id}';
  static const String overallStatistics = '/statistics/overall';
}