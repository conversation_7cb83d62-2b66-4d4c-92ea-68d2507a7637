import { IsUUID, IsOptional, IsBoolean, ValidateNested } from "class-validator";
import { Type } from "class-transformer";

class FollowSettingsDto {
  @IsOptional()
  @IsBoolean({
    message: "发布通知设置必须是布尔值",
  })
  notifyOnPost?: boolean = true;

  @IsOptional()
  @IsBoolean({
    message: "评论通知设置必须是布尔值",
  })
  notifyOnComment?: boolean = false;

  @IsOptional()
  @IsBoolean({
    message: "时间线显示设置必须是布尔值",
  })
  showInTimeline?: boolean = true;
}

/**
 * 关注用户DTO
 */
export class FollowUserDto {
  @IsUUID("4", {
    message: "用户ID必须是有效的UUID格式",
  })
  userId: string;

  @IsOptional()
  @IsBoolean({
    message: "特别关注设置必须是布尔值",
  })
  isSpecial?: boolean = false;

  @IsOptional()
  @ValidateNested()
  @Type(() => FollowSettingsDto)
  settings?: FollowSettingsDto;

  @IsOptional()
  @Type(() => Object)
  metadata?: Record<string, unknown>;
}
