-- 为users表添加异常状态管理字段
-- 执行时间: 2025-07-13

-- 1. 添加异常状态字段
ALTER TABLE "users" 
ADD COLUMN IF NOT EXISTS "anomaly_status" varchar(20) NOT NULL DEFAULT 'normal';

COMMENT ON COLUMN "users"."anomaly_status" IS '用户异常状态：normal-正常，warning-警告，restricted-受限，suspended-暂停';

-- 2. 添加异常警告计数
ALTER TABLE "users" 
ADD COLUMN IF NOT EXISTS "anomaly_warning_count" integer NOT NULL DEFAULT 0;

COMMENT ON COLUMN "users"."anomaly_warning_count" IS '异常警告累计次数';

-- 3. 添加异常限制计数
ALTER TABLE "users" 
ADD COLUMN IF NOT EXISTS "anomaly_restriction_count" integer NOT NULL DEFAULT 0;

COMMENT ON COLUMN "users"."anomaly_restriction_count" IS '异常限制累计次数';

-- 4. 添加点亮申请限制到期时间
ALTER TABLE "users" 
ADD COLUMN IF NOT EXISTS "lighting_restricted_until" TIMESTAMP NULL;

COMMENT ON COLUMN "users"."lighting_restricted_until" IS '点亮申请限制到期时间';

-- 5. 添加最后一次无效点亮申请时间
ALTER TABLE "users" 
ADD COLUMN IF NOT EXISTS "last_invalid_lighting_attempt" TIMESTAMP NULL;

COMMENT ON COLUMN "users"."last_invalid_lighting_attempt" IS '最后一次无效点亮申请时间';

-- 6. 添加今日点亮申请次数
ALTER TABLE "users" 
ADD COLUMN IF NOT EXISTS "daily_lighting_attempts" integer NOT NULL DEFAULT 0;

COMMENT ON COLUMN "users"."daily_lighting_attempts" IS '今日点亮申请次数';

-- 7. 添加点亮申请计数重置日期
ALTER TABLE "users" 
ADD COLUMN IF NOT EXISTS "lighting_attempt_reset_date" date NOT NULL DEFAULT CURRENT_DATE;

COMMENT ON COLUMN "users"."lighting_attempt_reset_date" IS '点亮申请计数重置日期';

-- 8. 修改默认值
ALTER TABLE "users" 
ALTER COLUMN "profile_display_settings" 
SET DEFAULT '{"showBirthday":false,"showBio":true,"showStatistics":true,"showCharacters":true,"showTimeline":true}';