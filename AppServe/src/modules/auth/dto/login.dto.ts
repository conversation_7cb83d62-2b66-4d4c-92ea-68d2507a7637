import { IsString, IsEmail, Length, Matches } from "class-validator";
import { ApiProperty } from "@nestjs/swagger";

/**
 * 登录DTO基类
 */
export class LoginDto {
  @ApiProperty({ description: "邮箱", example: "<EMAIL>" })
  @IsEmail({}, { message: "邮箱格式不正确" })
  email: string;

  @ApiProperty({ description: "密码", example: "Password123" })
  @IsString()
  @Length(8, 50, { message: "密码长度必须在8-50个字符之间" })
  password: string;
}

/**
 * 手机号登录DTO
 */
export class PhoneLoginDto {
  @ApiProperty({ description: "手机号", example: "13800138000" })
  @IsString()
  @Length(11, 11, { message: "手机号必须为11位" })
  @Matches(/^1[3-9]\d{9}$/, { message: "手机号格式不正确" })
  phone: string;

  @ApiProperty({ description: "短信验证码", example: "1234" })
  @IsString()
  @Length(4, 4, { message: "验证码必须为4位" })
  code: string;
}

/**
 * 邮箱登录DTO
 */
export class EmailLoginDto {
  @ApiProperty({ description: "邮箱", example: "<EMAIL>" })
  @IsEmail({}, { message: "邮箱格式不正确" })
  email: string;

  @ApiProperty({ description: "密码", example: "Password123" })
  @IsString()
  @Length(8, 50, { message: "密码长度必须在8-50个字符之间" })
  password: string;
}
