import {
  IsEnum,
  IsString,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "class-validator";
import { Type } from "class-transformer";
import { ReportType, ReportReason } from "../entities/report.entity";

/**
 * 创建举报DTO
 */
export class CreateReportDto {
  @IsEnum(ReportType, {
    message: "举报类型必须是 story、comment 或 user",
  })
  @Type(() => String)
  reportType: ReportType;

  @IsUUID("4", {
    message: "目标ID必须是有效的UUID格式",
  })
  targetId: string;

  @IsEnum(ReportReason, {
    message: "举报原因必须是有效的选项",
  })
  @Type(() => String)
  reason: ReportReason;

  @IsOptional()
  @IsString({
    message: "详细说明必须是字符串",
  })
  @MinLength(10, {
    message: "详细说明至少需要10个字符",
  })
  @MaxLength(1000, {
    message: "详细说明不能超过1000个字符",
  })
  description?: string;

  @IsOptional()
  @Type(() => Object)
  metadata?: Record<string, unknown>;
}
