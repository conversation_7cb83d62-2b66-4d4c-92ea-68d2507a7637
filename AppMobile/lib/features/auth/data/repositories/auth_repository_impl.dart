import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import '../../../../core/errors/exceptions.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/network/dio_client.dart';
import '../../domain/entities/auth_token.dart';
import '../../domain/entities/user_entity.dart';
import '../../domain/repositories/auth_repository.dart';
import '../datasources/auth_local_datasource.dart';
import '../datasources/auth_remote_datasource.dart';
import '../models/user_model.dart';
import '../models/auth_token_model.dart';

/// 认证仓库实现
class AuthRepositoryImpl implements AuthRepository {
  final AuthRemoteDataSource remoteDataSource;
  final AuthLocalDataSource localDataSource;

  AuthRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
  });

  @override
  Future<Either<Failure, void>> sendSmsCode(String phone) async {
    try {
      await remoteDataSource.sendSmsCode(phone);
      return const Right(null);
    } on DioException catch (e) {
      return Left(_handleDioError(e));
    } on AppException catch (e) {
      return Left(_handleAppException(e));
    } catch (e) {
      return Left(Failure.unknown(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, (UserEntity, AuthToken)>> loginWithPhone({
    required String phone,
    required String code,
  }) async {
    try {
      final (userModel, tokenModel) = await remoteDataSource.loginWithPhone(
        phone,
        code,
      );
      
      final user = userModel.toEntity();
      final token = tokenModel.toEntity();
      
      return Right((user, token));
    } on DioException catch (e) {
      return Left(_handleDioError(e));
    } on AppException catch (e) {
      return Left(_handleAppException(e));
    } catch (e) {
      return Left(Failure.unknown(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, (UserEntity, AuthToken)>> loginWithEmail({
    required String email,
    required String password,
  }) async {
    try {
      final (userModel, tokenModel) = await remoteDataSource.loginWithEmail(
        email,
        password,
      );
      
      final user = userModel.toEntity();
      final token = tokenModel.toEntity();
      
      return Right((user, token));
    } on DioException catch (e) {
      return Left(_handleDioError(e));
    } on AppException catch (e) {
      return Left(_handleAppException(e));
    } catch (e) {
      return Left(Failure.unknown(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, AuthToken>> refreshToken(String refreshToken) async {
    try {
      final tokenModel = await remoteDataSource.refreshToken(refreshToken);
      final token = tokenModel.toEntity();
      
      // 保存新的token
      await localDataSource.saveToken(tokenModel);
      
      return Right(token);
    } on DioException catch (e) {
      return Left(_handleDioError(e));
    } on AppException catch (e) {
      return Left(_handleAppException(e));
    } catch (e) {
      return Left(Failure.unknown(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> logout() async {
    try {
      // 调用远程登出接口
      await remoteDataSource.logout();
      // 清除本地数据
      await localDataSource.clearAuthData();
      return const Right(null);
    } on DioException catch (e) {
      // 即使远程登出失败，也要清除本地数据
      await localDataSource.clearAuthData();
      return Left(_handleDioError(e));
    } catch (e) {
      await localDataSource.clearAuthData();
      return Left(Failure.unknown(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, UserEntity?>> getCurrentUser() async {
    try {
      // 先尝试从本地获取
      final localUser = await localDataSource.getUser();
      if (localUser != null) {
        return Right(localUser.toEntity());
      }
      
      // 本地没有则从远程获取
      final remoteUser = await remoteDataSource.getUserProfile();
      await localDataSource.saveUser(remoteUser);
      
      return Right(remoteUser.toEntity());
    } on DioException catch (e) {
      return Left(_handleDioError(e));
    } on AppException catch (e) {
      return Left(_handleAppException(e));
    } catch (e) {
      return Left(Failure.unknown(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, UserEntity>> updateUserInfo({
    String? nickname,
    String? bio,
    String? avatar,
  }) async {
    try {
      final userModel = await remoteDataSource.updateUserProfile(
        nickname: nickname,
        bio: bio,
        avatar: avatar,
      );
      
      // 更新本地缓存
      await localDataSource.saveUser(userModel);
      
      return Right(userModel.toEntity());
    } on DioException catch (e) {
      return Left(_handleDioError(e));
    } on AppException catch (e) {
      return Left(_handleAppException(e));
    } catch (e) {
      return Left(Failure.unknown(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> changePassword({
    required String oldPassword,
    required String newPassword,
  }) async {
    try {
      await remoteDataSource.changePassword(oldPassword, newPassword);
      return const Right(null);
    } on DioException catch (e) {
      return Left(_handleDioError(e));
    } on AppException catch (e) {
      return Left(_handleAppException(e));
    } catch (e) {
      return Left(Failure.unknown(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, UserEntity>> bindPhone({
    required String phone,
    required String code,
  }) async {
    try {
      final userModel = await remoteDataSource.bindPhone(phone, code);
      
      // 更新本地缓存
      await localDataSource.saveUser(userModel);
      
      return Right(userModel.toEntity());
    } on DioException catch (e) {
      return Left(_handleDioError(e));
    } on AppException catch (e) {
      return Left(_handleAppException(e));
    } catch (e) {
      return Left(Failure.unknown(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, UserEntity>> bindEmail({
    required String email,
    required String password,
  }) async {
    try {
      final userModel = await remoteDataSource.bindEmail(email, password);
      
      // 更新本地缓存
      await localDataSource.saveUser(userModel);
      
      return Right(userModel.toEntity());
    } on DioException catch (e) {
      return Left(_handleDioError(e));
    } on AppException catch (e) {
      return Left(_handleAppException(e));
    } catch (e) {
      return Left(Failure.unknown(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> saveAuthData({
    required UserEntity user,
    required AuthToken token,
  }) async {
    try {
      // 保存用户信息
      final userModel = UserModel.fromEntity(user);
      await localDataSource.saveUser(userModel);
      
      // 保存token
      final tokenModel = AuthTokenModel.fromEntity(token);
      await localDataSource.saveToken(tokenModel);
      
      return const Right(null);
    } catch (e) {
      return Left(Failure.cache(message: '保存认证信息失败'));
    }
  }

  @override
  Future<Either<Failure, void>> clearAuthData() async {
    try {
      await localDataSource.clearAuthData();
      return const Right(null);
    } catch (e) {
      return Left(Failure.cache(message: '清除认证信息失败'));
    }
  }

  @override
  Future<Either<Failure, AuthToken?>> getSavedToken() async {
    try {
      final tokenModel = await localDataSource.getToken();
      return Right(tokenModel?.toEntity());
    } catch (e) {
      return Left(Failure.cache(message: '获取保存的令牌失败'));
    }
  }

  @override
  Future<Either<Failure, bool>> isLoggedIn() async {
    try {
      final isLoggedIn = await localDataSource.isLoggedIn();
      return Right(isLoggedIn);
    } catch (e) {
      return const Right(false);
    }
  }

  /// 处理Dio错误
  Failure _handleDioError(DioException error) {
    final appException = DioClient.handleDioError(error);
    return _handleAppException(appException);
  }

  /// 处理应用异常
  Failure _handleAppException(AppException exception) {
    switch (exception.runtimeType) {
      case NetworkException:
        return Failure.network(
          message: exception.message,
          code: exception.code,
          data: exception.data,
        );
      case ServerException:
        return Failure.server(
          message: exception.message,
          code: exception.code,
          statusCode: (exception as ServerException).statusCode,
          data: exception.data,
        );
      case AuthException:
        return Failure.auth(
          message: exception.message,
          code: exception.code,
          data: exception.data,
        );
      case ValidationException:
        return Failure.validation(
          message: exception.message,
          code: exception.code,
          errors: (exception as ValidationException).errors,
          data: exception.data,
        );
      case PermissionException:
        return Failure.permission(
          message: exception.message,
          code: exception.code,
          data: exception.data,
        );
      case BusinessException:
        return Failure.business(
          message: exception.message,
          code: exception.code,
          data: exception.data,
        );
      case CacheException:
        return Failure.cache(
          message: exception.message,
          code: exception.code,
          data: exception.data,
        );
      default:
        return Failure.unknown(
          message: exception.message,
          code: exception.code,
          data: exception.data,
        );
    }
  }
}