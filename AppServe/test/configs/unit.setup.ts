/**
 * 企业级单元测试环境配置
 * 完全隔离的纯粹单元测试环境
 * 零外部依赖，100% Mock
 *
 * @version 1.0.0
 * <AUTHOR>
 * @date 2025-07-20
 */

/* eslint-disable @typescript-eslint/no-explicit-any */

import "reflect-metadata";

// ===========================================
// 全局测试环境设置
// ===========================================

console.log("🏗️  初始化企业级单元测试环境...");

// 强制设置测试环境变量
process.env.NODE_ENV = "unit-test";
process.env.LOG_LEVEL = "error"; // 最小化日志输出

// ===========================================
// 企业级Mock全局服务
// ===========================================

// Mock数据库连接 - 单元测试绝不连接真实数据库
jest.mock("typeorm", () => {
  const actual = jest.requireActual("typeorm");
  return {
    ...actual,
    createConnection: jest.fn().mockResolvedValue({
      close: jest.fn(),
      isConnected: true,
    }),
    getConnection: jest.fn().mockReturnValue({
      close: jest.fn(),
      isConnected: true,
      manager: {
        save: jest.fn(),
        find: jest.fn(),
        findOne: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
      },
    }),
    DataSource: jest.fn().mockImplementation(() => ({
      initialize: jest.fn().mockResolvedValue(undefined),
      destroy: jest.fn().mockResolvedValue(undefined),
      isInitialized: true,
      manager: {
        save: jest.fn(),
        find: jest.fn(),
        findOne: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
      },
    })),
  };
});

// Mock Redis连接 - 单元测试使用内存Mock
jest.mock("ioredis", () => ({
  Redis: jest.fn().mockImplementation(() => ({
    get: jest.fn().mockResolvedValue(null),
    set: jest.fn().mockResolvedValue("OK"),
    del: jest.fn().mockResolvedValue(1),
    exists: jest.fn().mockResolvedValue(0),
    expire: jest.fn().mockResolvedValue(1),
    ttl: jest.fn().mockResolvedValue(-1),
    incr: jest.fn().mockResolvedValue(1),
    decr: jest.fn().mockResolvedValue(0),
    hget: jest.fn().mockResolvedValue(null),
    hset: jest.fn().mockResolvedValue(1),
    hdel: jest.fn().mockResolvedValue(1),
    hgetall: jest.fn().mockResolvedValue({}),
    zadd: jest.fn().mockResolvedValue(1),
    zrange: jest.fn().mockResolvedValue([]),
    zrem: jest.fn().mockResolvedValue(1),
    pipeline: jest.fn().mockReturnValue({
      get: jest.fn().mockReturnThis(),
      set: jest.fn().mockReturnThis(),
      exec: jest.fn().mockResolvedValue([]),
    }),
    multi: jest.fn().mockReturnValue({
      get: jest.fn().mockReturnThis(),
      set: jest.fn().mockReturnThis(),
      exec: jest.fn().mockResolvedValue([]),
    }),
    disconnect: jest.fn(),
    quit: jest.fn().mockResolvedValue("OK"),
    status: "ready",
  })),
}));

// Mock NestJS Cache - 使用内存Mock
jest.mock("@nestjs/cache-manager", () => ({
  CACHE_MANAGER: "CACHE_MANAGER_TOKEN",
  CacheModule: {
    register: jest.fn().mockReturnValue({
      module: class MockCacheModule {},
    }),
    registerAsync: jest.fn().mockReturnValue({
      module: class MockCacheModule {},
      providers: [],
      exports: [],
    }),
  },
}));

// Mock 阿里云OSS服务 - 支持 namespace 导入
jest.mock("ali-oss", () => {
  // 创建构造函数Mock - 每次调用都返回新的Mock实例
  const MockOSSConstructor = jest.fn().mockImplementation(() => ({
    put: jest.fn().mockResolvedValue({
      name: "test-file.jpg",
      url: "https://mock-oss-url.com/test-file.jpg",
      res: { status: 200 },
    }),
    get: jest.fn().mockResolvedValue({
      content: Buffer.from("mock-content"),
    }),
    delete: jest.fn().mockResolvedValue({
      res: { status: 204 },
    }),
    head: jest.fn().mockResolvedValue({
      meta: {},
      res: { headers: {} },
    }),
    signatureUrl: jest.fn().mockReturnValue("https://mock-signature-url.com"),
  }));

  // 返回 namespace，直接将构造函数作为模块导出
  // 这样 import * as OSS from "ali-oss" 时，OSS 就是这个构造函数
  return MockOSSConstructor;
});

// Mock 阿里云短信 - 单元测试使用Mock
jest.mock("@alicloud/dysmsapi20170525", () => ({
  __esModule: true,
  default: jest.fn().mockImplementation(() => ({
    sendSms: jest.fn().mockResolvedValue({
      body: {
        code: "OK",
        message: "OK",
        bizId: "mock-biz-id",
      },
    }),
  })),
}));

// ===========================================
// 企业级全局测试配置
// ===========================================

// Jest全局配置
beforeEach(() => {
  // 清理所有Mock状态
  jest.clearAllMocks();
});

afterEach(() => {
  // 重置所有Mock
  jest.resetAllMocks();
});

// 全局错误处理
process.on("unhandledRejection", (reason, _promise) => {
  console.error("单元测试中发现未处理的Promise拒绝:", reason);
  // 单元测试中的未处理Promise是严重问题
  process.exit(1);
});

// 全局超时设置
jest.setTimeout(5000); // 单元测试应该很快完成

// ===========================================
// 企业级Mock工厂函数
// ===========================================

/**
 * 创建企业级Repository Mock
 */
const createMockRepository = <mockT = unknown>() => {
  // 创建完整的QueryBuilder Mock
  const mockQueryBuilder = {
    select: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    orWhere: jest.fn().mockReturnThis(),
    innerJoin: jest.fn().mockReturnThis(),
    leftJoin: jest.fn().mockReturnThis(),
    rightJoin: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    addOrderBy: jest.fn().mockReturnThis(),
    groupBy: jest.fn().mockReturnThis(),
    having: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    offset: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    take: jest.fn().mockReturnThis(),
    getOne: jest.fn().mockResolvedValue(null),
    getMany: jest.fn().mockResolvedValue([]),
    getRawOne: jest.fn().mockResolvedValue(null),
    getRawMany: jest.fn().mockResolvedValue([]),
    getCount: jest.fn().mockResolvedValue(0),
    execute: jest.fn().mockResolvedValue({ affected: 0 }),
    update: jest.fn().mockReturnThis(),
    set: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    into: jest.fn().mockReturnThis(),
    values: jest.fn().mockReturnThis(),
  };

  return {
    find: jest.fn() as jest.MockedFunction<any>,
    findOne: jest.fn() as jest.MockedFunction<any>,
    findOneBy: jest.fn() as jest.MockedFunction<any>,
    findAndCount: jest.fn() as jest.MockedFunction<any>,
    save: jest.fn() as jest.MockedFunction<any>,
    update: jest.fn() as jest.MockedFunction<any>,
    delete: jest.fn() as jest.MockedFunction<any>,
    remove: jest.fn() as jest.MockedFunction<any>,
    create: jest.fn() as jest.MockedFunction<any>,
    createQueryBuilder: jest.fn().mockReturnValue(mockQueryBuilder),
    count: jest.fn() as jest.MockedFunction<any>,
    exists: jest.fn() as jest.MockedFunction<any>,
    clear: jest.fn() as jest.MockedFunction<any>,
    increment: jest.fn() as jest.MockedFunction<any>,
    decrement: jest.fn() as jest.MockedFunction<any>,
  };
};

/**
 * 创建企业级Service Mock
 */
const createMockService = <T = unknown>(methods: string[]) => {
  const mockService: Record<string, jest.Mock> = {};
  methods.forEach((method) => {
    mockService[method] = jest.fn();
  });
  return mockService as jest.Mocked<T>;
};

/**
 * 创建企业级Logger Mock
 */
const createMockLogger = () => ({
  log: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
  verbose: jest.fn(),
});

/**
 * 创建企业级DataSource Mock
 */
const createMockDataSource = () => ({
  initialize: jest.fn().mockResolvedValue(undefined),
  destroy: jest.fn().mockResolvedValue(undefined),
  isInitialized: true,
  getRepository: jest.fn().mockImplementation(() => createMockRepository()),
  manager: {
    save: jest.fn(),
    find: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    create: jest.fn(),
    transaction: jest.fn().mockImplementation((fn) =>
      fn({
        save: jest.fn(),
        find: jest.fn(),
        findOne: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
      }),
    ),
  },
  // 添加顶级 transaction 方法
  transaction: jest.fn().mockImplementation((fn) => {
    const mockEntityManager = {
      save: jest.fn(),
      find: jest.fn(),
      findOne: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      create: jest.fn(),
      getRepository: jest.fn().mockImplementation(() => createMockRepository()),
    };
    return fn(mockEntityManager);
  }),
  createQueryRunner: jest.fn().mockReturnValue({
    connect: jest.fn(),
    startTransaction: jest.fn(),
    commitTransaction: jest.fn(),
    rollbackTransaction: jest.fn(),
    release: jest.fn(),
    manager: {
      save: jest.fn(),
      find: jest.fn(),
      findOne: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      create: jest.fn().mockImplementation((EntityClass, data) => ({
        ...data,
        id: "mock-id",
      })),
      getRepository: jest.fn().mockImplementation(() => createMockRepository()),
    },
  }),
});

// 将Mock工厂函数添加到全局对象
(global as Record<string, unknown>).createEnterpriseRepository =
  createMockRepository;
(global as Record<string, unknown>).createMockRepository = createMockRepository;
(global as Record<string, unknown>).createMockService = createMockService;
(global as Record<string, unknown>).createEnterpriseService = createMockService; // 企业级服务Mock
(global as Record<string, unknown>).createMockLogger = createMockLogger;
(global as Record<string, unknown>).createEnterpriseDataSource =
  createMockDataSource;
(global as Record<string, unknown>).createMockDataSource = createMockDataSource;

// 导出供TypeScript使用
export {
  createMockRepository,
  createMockService,
  createMockLogger,
  createMockDataSource,
};

console.log("✅ 企业级单元测试环境初始化完成");
console.log("📊 环境特性: 零外部依赖，100% Mock，企业级隔离");
