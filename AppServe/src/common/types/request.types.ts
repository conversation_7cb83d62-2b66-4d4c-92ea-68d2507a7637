/**
 * 请求相关的共享类型定义
 * 提供类型安全的请求处理接口
 */

import type { FastifyRequest } from "fastify";
import type { MultipartFile } from "@fastify/multipart";

/**
 * 用户载荷接口
 * JWT令牌中包含的用户信息
 */
export interface UserPayload {
  /** 用户ID */
  id: string;
  /** 用户名 */
  username: string;
  /** 昵称 */
  nickname?: string;
  /** 邮箱 */
  email?: string;
  /** 手机号 */
  phone?: string;
  /** 是否已验证 */
  isVerified: boolean;
  /** 权限列表 */
  permissions?: string[];
  /** 用户角色 */
  role?: string;
  /** 令牌颁发时间 */
  iat?: number;
  /** 令牌过期时间 */
  exp?: number;
  /** 支持任意字符串键的索引签名 */
  [key: string]: unknown;
}

/**
 * 认证后的请求接口
 * 包含用户信息的请求对象
 */
export interface AuthenticatedRequest extends FastifyRequest {
  user: UserPayload & { [key: string]: unknown };
}

/**
 * 简单的认证请求类型（用于@Request()装饰器）
 */
export type AuthRequest = { user: { id: string; [key: string]: unknown } };

/**
 * 请求体类型定义
 * 用于错误日志记录和调试
 */
export interface RequestBody {
  [key: string]: unknown;
}

/**
 * 查询参数类型定义
 */
export interface QueryParams {
  [key: string]: string | string[] | undefined;
}

/**
 * 路由参数类型定义
 */
export interface RouteParams {
  [key: string]: string | undefined;
}

/**
 * 分页查询参数接口
 */
export interface PaginationQuery {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "ASC" | "DESC";
}

/**
 * 分页响应接口
 */
export interface PaginatedResponse<T> {
  data: T[];
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

/**
 * 文件上传请求接口
 */
export interface FileUploadRequest extends FastifyRequest {
  file: () => Promise<MultipartFile>;
}

/**
 * API响应元数据接口
 */
export interface ResponseMetadata {
  timestamp: string;
  path: string;
  method: string;
  statusCode: number;
  executionTime?: number;
}

/**
 * 设备信息接口
 * 用于设备指纹识别和会话管理
 */
export interface DeviceInfo {
  /** 用户代理字符串 */
  userAgent?: string;
  /** 设备平台 */
  platform?: string;
  /** 应用版本 */
  version?: string;
  /** IP地址 */
  ip?: string;
  /** 设备指纹 */
  fingerprint?: string;
  /** 连接信息 */
  connection?: {
    remoteAddress?: string;
    remotePort?: number;
  };
}
