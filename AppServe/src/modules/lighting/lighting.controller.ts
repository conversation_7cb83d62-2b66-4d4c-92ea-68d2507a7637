import {
  Controller,
  Post,
  Get,
  Put,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
} from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from "@nestjs/swagger";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { LightingService } from "./lighting.service";
import {
  SubmitLightRequestDto,
  LightRequestResponseDto,
  LightingStatusResponseDto,
} from "./dto";
import { LightingValidationResultDto } from "./dto/lighting-validation-result.dto";
import type { ApiResponse as ApiResponseDto } from "../../common/dto/api-response.dto";
import { AuthRequest as AuthRequestType } from "../../common/types/request.types";

@ApiTags("人物点亮")
@Controller("lighting")
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class LightingController {
  constructor(private readonly lightingService: LightingService) {}

  @Post("stories/:storyId/characters/:characterId/light-request")
  @ApiOperation({ summary: "提交人物点亮申请" })
  @ApiParam({ name: "storyId", description: "故事ID" })
  @ApiParam({ name: "characterId", description: "人物ID" })
  @ApiResponse({
    status: 201,
    description: "申请提交成功",
    type: LightRequestResponseDto,
  })
  @ApiResponse({ status: 400, description: "申请参数无效或违反业务规则" })
  @ApiResponse({ status: 409, description: "申请冲突（重复申请或已点亮）" })
  async submitLightRequest(
    @Request() req: AuthRequestType,
    @Param("storyId") storyId: string,
    @Param("characterId") characterId: string,
    @Body() submitLightRequestDto: SubmitLightRequestDto,
  ): Promise<ApiResponseDto<LightRequestResponseDto>> {
    const result = await this.lightingService.submitLightRequest(
      storyId,
      characterId,
      req.user.id,
      submitLightRequestDto,
    );

    return {
      success: true,
      message: "点亮申请提交成功",
      data: result,
      statusCode: 201,
      timestamp: new Date().toISOString(),
    };
  }

  @Get("light-requests")
  @ApiOperation({ summary: "获取待处理的点亮申请（作为故事作者）" })
  @ApiResponse({
    status: 200,
    description: "获取成功",
    type: [LightRequestResponseDto],
  })
  async getPendingRequests(
    @Request() req: AuthRequestType,
  ): Promise<ApiResponseDto<LightRequestResponseDto[]>> {
    const result = await this.lightingService.getPendingRequests(req.user.id);

    return {
      success: true,
      message: "获取待处理申请成功",
      data: result,
      statusCode: 200,
      timestamp: new Date().toISOString(),
    };
  }

  @Get("my-requests")
  @ApiOperation({ summary: "获取我的点亮申请记录（作为申请者）" })
  @ApiResponse({
    status: 200,
    description: "获取成功",
    type: [LightRequestResponseDto],
  })
  async getUserRequests(
    @Request() req: AuthRequestType,
  ): Promise<ApiResponseDto<LightRequestResponseDto[]>> {
    const result = await this.lightingService.getUserRequests(req.user.id);

    return {
      success: true,
      message: "获取申请记录成功",
      data: result,
      statusCode: 200,
      timestamp: new Date().toISOString(),
    };
  }

  @Get("characters/:characterId/lighting-status")
  @ApiOperation({ summary: "查询人物点亮状态" })
  @ApiParam({ name: "characterId", description: "人物ID" })
  @ApiQuery({
    name: "includeUserStatus",
    required: false,
    description: "是否包含当前用户的申请状态",
    type: Boolean,
  })
  @ApiResponse({
    status: 200,
    description: "查询成功",
    type: LightingStatusResponseDto,
  })
  async getLightingStatus(
    @Request() req: AuthRequestType,
    @Param("characterId") characterId: string,
    @Query("includeUserStatus") includeUserStatus?: boolean,
  ): Promise<ApiResponseDto<LightingStatusResponseDto>> {
    const currentUserId = includeUserStatus ? req.user?.id : undefined;
    const result = await this.lightingService.getLightingStatus(
      characterId,
      currentUserId,
    );

    return {
      success: true,
      message: "查询点亮状态成功",
      data: result,
      statusCode: 200,
      timestamp: new Date().toISOString(),
    };
  }

  @Get("characters/:characterId/can-apply")
  @ApiOperation({ summary: "检查是否可以申请点亮" })
  @ApiParam({ name: "characterId", description: "人物ID" })
  @ApiResponse({
    status: 200,
    description: "检查完成",
    schema: {
      type: "object",
      properties: {
        success: { type: "boolean" },
        message: { type: "string" },
        data: {
          type: "object",
          properties: {
            canApply: { type: "boolean" },
            reason: { type: "string" },
          },
        },
      },
    },
  })
  async checkCanApplyLighting(
    @Request() req: AuthRequestType,
    @Param("characterId") characterId: string,
  ): Promise<ApiResponseDto<{ canApply: boolean; reason?: string }>> {
    const status = await this.lightingService.getLightingStatus(
      characterId,
      req.user.id,
    );

    return {
      success: true,
      message: "检查完成",
      data: {
        canApply: status.canApplyLighting,
        reason: status.cannotApplyReason,
      },
      statusCode: 200,
      timestamp: new Date().toISOString(),
    };
  }

  @Get("stories/:storyId/characters/:characterId/validate-lighting")
  @ApiOperation({ summary: "验证点亮申请条件（详细版）" })
  @ApiParam({ name: "storyId", description: "故事ID" })
  @ApiParam({ name: "characterId", description: "人物ID" })
  @ApiResponse({
    status: 200,
    description: "验证完成",
    type: LightingValidationResultDto,
  })
  async validateLightingRequest(
    @Request() req: AuthRequestType,
    @Param("storyId") storyId: string,
    @Param("characterId") characterId: string,
  ): Promise<ApiResponseDto<LightingValidationResultDto>> {
    const result = await this.lightingService.validateLightingRequestDetailed(
      req.user.id,
      storyId,
      characterId,
    );

    return {
      success: true,
      message: "验证完成",
      data: result,
      statusCode: 200,
      timestamp: new Date().toISOString(),
    };
  }

  @Put("light-requests/:requestId/confirm")
  @ApiOperation({ summary: "确认点亮申请" })
  @ApiParam({ name: "requestId", description: "申请ID" })
  @ApiResponse({
    status: 200,
    description: "确认成功",
    schema: {
      type: "object",
      properties: {
        success: { type: "boolean" },
        message: { type: "string" },
      },
    },
  })
  @ApiResponse({ status: 403, description: "权限不足" })
  @ApiResponse({ status: 404, description: "申请不存在" })
  @ApiResponse({ status: 409, description: "申请冲突（已处理或已点亮）" })
  async confirmLightRequest(
    @Request() req: AuthRequestType,
    @Param("requestId") requestId: string,
  ): Promise<ApiResponseDto<Record<string, never>>> {
    await this.lightingService.confirmLightRequest(requestId, req.user.id);

    return {
      success: true,
      message: "点亮申请确认成功",
      data: {},
      statusCode: 200,
      timestamp: new Date().toISOString(),
    };
  }

  @Put("light-requests/:requestId/reject")
  @ApiOperation({ summary: "拒绝点亮申请" })
  @ApiParam({ name: "requestId", description: "申请ID" })
  @ApiResponse({
    status: 200,
    description: "拒绝成功",
    schema: {
      type: "object",
      properties: {
        success: { type: "boolean" },
        message: { type: "string" },
      },
    },
  })
  @ApiResponse({ status: 403, description: "权限不足" })
  @ApiResponse({ status: 404, description: "申请不存在" })
  async rejectLightRequest(
    @Request() req: AuthRequestType,
    @Param("requestId") requestId: string,
    @Body() body?: { reason?: string },
  ): Promise<ApiResponseDto<Record<string, never>>> {
    await this.lightingService.rejectLightRequest(
      requestId,
      req.user.id,
      body?.reason,
    );

    return {
      success: true,
      message: "点亮申请已拒绝",
      data: {},
      statusCode: 200,
      timestamp: new Date().toISOString(),
    };
  }

  @Get("users/:userId/lighted-characters")
  @ApiOperation({ summary: "获取用户点亮的人物集" })
  @ApiParam({ name: "userId", description: "用户ID" })
  @ApiResponse({
    status: 200,
    description: "获取成功",
    schema: {
      type: "object",
      properties: {
        success: { type: "boolean" },
        message: { type: "string" },
        data: { type: "array" },
      },
    },
  })
  async getUserLightedCharacters(
    @Param("userId") userId: string,
  ): Promise<ApiResponseDto<unknown[]>> {
    const result = await this.lightingService.getUserLightedCharacters(userId);

    return {
      success: true,
      message: "获取点亮集成功",
      data: result,
      statusCode: 200,
      timestamp: new Date().toISOString(),
    };
  }

  @Get("characters/:characterId/lightings")
  @ApiOperation({ summary: "获取人物的点亮记录" })
  @ApiParam({ name: "characterId", description: "人物ID" })
  @ApiResponse({
    status: 200,
    description: "获取成功",
    schema: {
      type: "object",
      properties: {
        success: { type: "boolean" },
        message: { type: "string" },
        data: { type: "array" },
      },
    },
  })
  async getCharacterLightings(
    @Param("characterId") characterId: string,
  ): Promise<ApiResponseDto<unknown[]>> {
    const result =
      await this.lightingService.getCharacterLightings(characterId);

    return {
      success: true,
      message: "获取人物点亮记录成功",
      data: result,
      statusCode: 200,
      timestamp: new Date().toISOString(),
    };
  }

  @Get("users/:userId/lighting-history")
  @ApiOperation({ summary: "获取用户的点亮历史" })
  @ApiParam({ name: "userId", description: "用户ID" })
  @ApiResponse({
    status: 200,
    description: "获取成功",
    schema: {
      type: "object",
      properties: {
        success: { type: "boolean" },
        message: { type: "string" },
        data: {
          type: "object",
          properties: {
            lightedByUser: { type: "array" },
            lightedByOthers: { type: "array" },
          },
        },
      },
    },
  })
  async getLightingHistory(
    @Param("userId") userId: string,
  ): Promise<ApiResponseDto<unknown>> {
    const result = await this.lightingService.getLightingHistory(userId);

    return {
      success: true,
      message: "获取点亮历史成功",
      data: result,
      statusCode: 200,
      timestamp: new Date().toISOString(),
    };
  }

  @Get("users/:userId/lighting-stats")
  @ApiOperation({ summary: "获取用户点亮统计数据" })
  @ApiParam({ name: "userId", description: "用户ID" })
  @ApiResponse({
    status: 200,
    description: "获取成功",
    schema: {
      type: "object",
      properties: {
        success: { type: "boolean" },
        message: { type: "string" },
        data: {
          type: "object",
          properties: {
            totalLighted: { type: "number" },
            totalReceived: { type: "number" },
            pendingRequests: { type: "number" },
            thisMonthLighted: { type: "number" },
          },
        },
      },
    },
  })
  async getUserLightingStats(
    @Param("userId") userId: string,
  ): Promise<ApiResponseDto<unknown>> {
    const result = await this.lightingService.getUserLightingStats(userId);

    return {
      success: true,
      message: "获取统计数据成功",
      data: result,
      statusCode: 200,
      timestamp: new Date().toISOString(),
    };
  }

  @Post("maintenance/cleanup-expired")
  @ApiOperation({ summary: "清理过期申请（管理员功能）" })
  @ApiResponse({
    status: 200,
    description: "清理完成",
    schema: {
      type: "object",
      properties: {
        success: { type: "boolean" },
        message: { type: "string" },
        data: {
          type: "object",
          properties: {
            cleaned: { type: "number" },
          },
        },
      },
    },
  })
  async cleanupExpiredRequests(): Promise<ApiResponseDto<{ cleaned: number }>> {
    const result = await this.lightingService.cleanupExpiredRequests();

    return {
      success: true,
      message: `已清理 ${result.cleaned} 个过期申请`,
      data: result,
      statusCode: 200,
      timestamp: new Date().toISOString(),
    };
  }

  @Post("maintenance/sync-status")
  @ApiOperation({ summary: "同步点亮状态（管理员功能）" })
  @ApiResponse({
    status: 200,
    description: "同步完成",
    schema: {
      type: "object",
      properties: {
        success: { type: "boolean" },
        message: { type: "string" },
        data: {
          type: "object",
          properties: {
            synced: { type: "number" },
          },
        },
      },
    },
  })
  async syncLightingStatus(): Promise<ApiResponseDto<{ synced: number }>> {
    const result = await this.lightingService.syncLightingStatus();

    return {
      success: true,
      message: `已同步 ${result.synced} 个人物状态`,
      data: result,
      statusCode: 200,
      timestamp: new Date().toISOString(),
    };
  }
}
