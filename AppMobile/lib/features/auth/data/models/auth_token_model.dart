import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/auth_token.dart';

part 'auth_token_model.freezed.dart';
part 'auth_token_model.g.dart';

/// 认证令牌数据模型
@freezed
class AuthTokenModel with _$AuthTokenModel {
  const AuthTokenModel._();
  
  const factory AuthTokenModel({
    @JsonKey(name: 'access_token') required String accessToken,
    @<PERSON><PERSON><PERSON>ey(name: 'refresh_token') required String refreshToken,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'expires_at') required DateTime expiresAt,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'token_type') @Default('Bearer') String tokenType,
  }) = _AuthTokenModel;

  factory AuthTokenModel.fromJson(Map<String, dynamic> json) =>
      _$AuthTokenModelFromJson(json);

  /// 转换为实体
  AuthToken toEntity() => AuthToken(
    accessToken: accessToken,
    refreshToken: refreshToken,
    expiresAt: expiresAt,
    tokenType: tokenType,
  );

  /// 从实体创建
  factory AuthTokenModel.fromEntity(AuthToken entity) => AuthTokenModel(
    accessToken: entity.accessToken,
    refreshToken: entity.refreshToken,
    expiresAt: entity.expiresAt,
    tokenType: entity.tokenType,
  );
}