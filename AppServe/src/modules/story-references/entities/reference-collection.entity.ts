import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  OneToMany,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from "typeorm";
import { User } from "../../users/entities/user.entity";
import { StoryReference } from "./story-reference.entity";

export enum CollectionType {
  GENERAL = "general",
  THEMATIC = "thematic", // 主题性收集
  RESEARCH = "research", // 研究用途
  INSPIRATION = "inspiration", // 灵感收集
  RECOMMENDATION = "recommendation", // 推荐集合
  CUSTOM = "custom",
}

export enum CollectionStatus {
  ACTIVE = "active",
  ARCHIVED = "archived",
  DELETED = "deleted",
}

export enum CollectionVisibility {
  PUBLIC = "public",
  FRIENDS_ONLY = "friends_only",
  PRIVATE = "private",
}

/**
 * 引用集合实体
 * 用户创建的故事引用集合
 */
@Entity("reference_collections")
@Index(["ownerId", "status"])
@Index(["collectionType", "visibility"])
@Index(["createdAt"])
export class ReferenceCollection {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({
    type: "varchar",
    length: 100,
    comment: "集合名称",
  })
  name: string;

  @Column({
    type: "text",
    nullable: true,
    comment: "集合描述",
  })
  description: string;

  @Column({
    type: "uuid",
    comment: "集合所有者用户ID",
  })
  ownerId: string;

  @Column({
    type: "enum",
    enum: CollectionType,
    default: CollectionType.GENERAL,
    comment: "集合类型",
  })
  collectionType: CollectionType;

  @Column({
    type: "enum",
    enum: CollectionStatus,
    default: CollectionStatus.ACTIVE,
    comment: "集合状态",
  })
  status: CollectionStatus;

  @Column({
    type: "enum",
    enum: CollectionVisibility,
    default: CollectionVisibility.PRIVATE,
    comment: "可见性设置",
  })
  visibility: CollectionVisibility;

  @Column({
    type: "varchar",
    length: 500,
    nullable: true,
    comment: "封面图片URL",
  })
  coverImage: string;

  @Column({
    type: "varchar",
    length: 50,
    nullable: true,
    comment: "颜色主题",
  })
  colorTheme: string;

  @Column({
    type: "simple-array",
    nullable: true,
    comment: "集合标签",
  })
  tags: string[];

  @Column({
    type: "int",
    default: 0,
    comment: "引用数量",
  })
  referenceCount: number;

  @Column({
    type: "int",
    default: 0,
    comment: "关注者数量",
  })
  followersCount: number;

  @Column({
    type: "int",
    default: 0,
    comment: "点赞数",
  })
  likesCount: number;

  @Column({
    type: "int",
    default: 0,
    comment: "排序权重",
  })
  sortOrder: number;

  @Column({
    type: "boolean",
    default: false,
    comment: "是否为精选集合",
  })
  isFeatured: boolean;

  @Column({
    type: "boolean",
    default: true,
    comment: "是否允许协作编辑",
  })
  allowCollaboration: boolean;

  @Column({
    type: "jsonb",
    nullable: true,
    comment: "集合设置",
  })
  settings: {
    autoAddSimilar?: boolean;
    notifyOnNewReference?: boolean;
    allowPublicContribution?: boolean;
    moderationRequired?: boolean;
  };

  @Column({
    type: "timestamp with time zone",
    nullable: true,
    comment: "最后更新内容时间",
  })
  lastContentUpdate: Date;

  @Column({
    type: "jsonb",
    nullable: true,
    comment: "统计信息",
  })
  statistics: {
    totalViews?: number;
    monthlyViews?: number;
    totalShares?: number;
    averageRating?: number;
    topTags?: string[];
  };

  @Column({
    type: "jsonb",
    nullable: true,
    comment: "扩展字段",
  })
  metadata: Record<string, unknown>;

  @CreateDateColumn({
    type: "timestamp with time zone",
    comment: "创建时间",
  })
  createdAt: Date;

  @UpdateDateColumn({
    type: "timestamp with time zone",
    comment: "更新时间",
  })
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => User, { eager: false })
  @JoinColumn({ name: "ownerId" })
  owner: User;

  @OneToMany(() => StoryReference, (reference) => reference.collection)
  references: StoryReference[];
}
