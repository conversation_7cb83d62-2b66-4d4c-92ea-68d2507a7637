/**
 * 有故事 APP - API 服务配置
 * 
 * 功能特性：
 * - Axios 实例配置
 * - 请求/响应拦截器
 * - 错误处理
 * - Token 管理
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { getNetworkConfig, networkDebugger } from '../config/network';

// ===== 网络配置 =====
const networkConfig = getNetworkConfig();
const TOKEN_KEY = 'access_token';
const REFRESH_TOKEN_KEY = 'refresh_token';

// ===== API 响应类型 =====
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  timestamp: number;
}

// ===== API 错误类型 =====
export interface ApiError {
  code: number;
  message: string;
  details?: any;
}

/**
 * 创建 Axios 实例
 */
const createApiInstance = (): AxiosInstance => {
  // 在开发环境打印网络配置信息
  if (__DEV__) {
    networkDebugger.printConfig();
  }

  const instance = axios.create({
    baseURL: networkConfig.baseURL,
    timeout: networkConfig.timeout,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // 请求拦截器 - 添加 Token
  instance.interceptors.request.use(
    async (config: any) => {
      try {
        const token = await AsyncStorage.getItem(TOKEN_KEY);
        if (token && config.headers) {
          config.headers.Authorization = `Bearer ${token}`;
        }
      } catch (error) {
        console.warn('获取 Token 失败:', error);
      }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  // 响应拦截器 - 处理错误和 Token 刷新
  instance.interceptors.response.use(
    (response: AxiosResponse<ApiResponse>) => {
      return response;
    },
    async (error) => {
      const originalRequest = error.config;

      // Token 过期处理
      if (error.response?.status === 401 && !originalRequest._retry) {
        originalRequest._retry = true;
        
        try {
          const refreshToken = await AsyncStorage.getItem(REFRESH_TOKEN_KEY);
          if (refreshToken) {
            // 刷新 Token
            const response = await instance.post('/auth/refresh', {
              refreshToken,
            });
            
            const { accessToken, refreshToken: newRefreshToken } = response.data.data;
            
            // 保存新 Token
            await AsyncStorage.setItem(TOKEN_KEY, accessToken);
            await AsyncStorage.setItem(REFRESH_TOKEN_KEY, newRefreshToken);
            
            // 重新发送原始请求
            originalRequest.headers.Authorization = `Bearer ${accessToken}`;
            return instance(originalRequest);
          }
        } catch (refreshError) {
          // 刷新失败，清除所有 Token
          await AsyncStorage.multiRemove([TOKEN_KEY, REFRESH_TOKEN_KEY]);
          // 导航到登录页面
          // 这里可以通过事件或导航服务处理
        }
      }

      return Promise.reject(error);
    }
  );

  return instance;
};

// ===== API 实例 =====
export const api = createApiInstance();

// ===== Token 管理工具 =====
export const tokenManager = {
  async getToken(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(TOKEN_KEY);
    } catch (error) {
      console.error('获取 Token 失败:', error);
      return null;
    }
  },

  async setTokens(accessToken: string, refreshToken: string): Promise<void> {
    try {
      await AsyncStorage.multiSet([
        [TOKEN_KEY, accessToken],
        [REFRESH_TOKEN_KEY, refreshToken],
      ]);
    } catch (error) {
      console.error('保存 Token 失败:', error);
    }
  },

  async clearTokens(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([TOKEN_KEY, REFRESH_TOKEN_KEY]);
    } catch (error) {
      console.error('清除 Token 失败:', error);
    }
  },

  async isAuthenticated(): Promise<boolean> {
    const token = await this.getToken();
    return !!token;
  },
}; 