import '../../domain/entities/story_entity.dart';

/// 故事本地数据源 - 负责本地缓存
class StoryLocalDataSource {
  // 缓存存储
  final Map<String, List<StoryEntity>> _cache = {};
  final Map<String, DateTime> _cacheTimestamps = {};

  /// 获取缓存的故事列表
  Future<List<StoryEntity>> getCachedStories({
    String? category,
    int limit = 10,
  }) async {
    final cacheKey = category ?? 'all';
    final timestamp = _cacheTimestamps[cacheKey];
    
    // 检查缓存是否过期（5分钟）
    if (timestamp != null && 
        DateTime.now().difference(timestamp).inMinutes < 5) {
      final cached = _cache[cacheKey] ?? [];
      return cached.take(limit).toList();
    }
    
    return [];
  }

  /// 缓存故事列表
  Future<void> cacheStories(
    List<StoryEntity> stories, {
    String? category,
  }) async {
    final cacheKey = category ?? 'all';
    _cache[cacheKey] = stories;
    _cacheTimestamps[cacheKey] = DateTime.now();
  }

  /// 更新故事点赞状态
  Future<void> updateStoryLikeStatus(String storyId, bool isLiked) async {
    for (final cacheKey in _cache.keys) {
      final stories = _cache[cacheKey];
      if (stories != null) {
        final index = stories.indexWhere((story) => story.id == storyId);
        if (index >= 0) {
          final story = stories[index];
          final updatedStory = story.copyWith(
            isLiked: isLiked,
            likeCount: isLiked 
                ? story.likeCount + 1 
                : (story.likeCount > 0 ? story.likeCount - 1 : 0),
          );
          stories[index] = updatedStory;
        }
      }
    }
  }

  /// 清除缓存
  Future<void> clearCache() async {
    _cache.clear();
    _cacheTimestamps.clear();
  }
}