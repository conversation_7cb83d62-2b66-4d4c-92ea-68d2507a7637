# YGS (有故事) AI 开发助手规范

> **强制执行**: 所有开发规范必须无条件遵守  
> **最后更新**: 2025-08-01

## 🎯 核心开发约束

### 📐 设计稿严格遵循 **[关键]**
- **设计基准**: 750px × 1624px (Figma设计稿标准尺寸)
- **响应式适配**: 必须使用flutter_screenutil，所有尺寸使用`.w`、`.h`、`.sp`、`.r`
- **像素完美**: 代码实现必须与Figma设计稿1:1像素对应
- **Figma链接**: 有人类提供
- **设计令牌**: 严格使用YGS设计系统颜色、间距、字体规范

### 🛠️ 技术栈强制标准
- **前端**: Flutter 3.32 + Dart 3.5
- **状态管理**: Clean Architecture + Riverpod  
- **响应式**: flutter_screenutil (基于750×1624)
- **后端**: NestJS 11 + Fastify 5 + TypeScript
- **数据库**: 阿里云 RDS PostgreSQL (禁止本地数据库)

### 🌏 语言和交互规范
- **Claude响应**: 使用中文
- **代码注释**: 中文注释，英文变量名
- **错误处理**: 提供中文说明和解决方案

## ⚡ Flutter开发强制规则

### 🔥 Flutter热重载协作
**❌ 绝对禁止**: AI执行`flutter run`命令

**严格分工**:
- **人类负责**: 手动启动Flutter服务
- **AI负责**: 专注代码开发，告知修改了哪些文件

**标准提示格式**:
```
✏️ 修改了: 具体文件路径
🔄 建议: 按 r 键热重载查看效果
```

### 🎨 YGS设计系统强制使用
**❌ 零容忍**: 禁止硬编码颜色、尺寸、字体

**强制使用**:
```dart
// ✅ 正确使用设计系统
YgsColors.textPrimary        // #151523
YgsColors.textSecondary      // #868691  
YgsSpacing.lg               // 24.w
YgsTypography.bodyMedium    // 响应式字体
```

### 📱 响应式尺寸强制规范
**❌ 硬编码禁令**: 严禁使用固定像素值

**强制使用**:
```dart
// ✅ 正确
width: 345.w     // 基于750px设计稿
height: 48.h     // 基于1624px设计稿  
fontSize: 16.sp  // 响应式字体
borderRadius: 12.r // 响应式圆角

// ❌ 错误
width: 345       // 固定像素值
```

### 🎯 设计驱动开发强制规范
**❌ 零容忍原则**: 严禁偏离Figma设计稿，任何"差不多"都是不可接受的

**核心开发思维转变**:
```
❌ 传统思维: 看设计稿 → 大概估算 → 用Flutter组件 → 微调到"差不多"
✅ 设计思维: Figma设计稿 → MCP精确参数 → 绝对定位实现 → 像素级完美
```

**强制执行流程**:
1. **MCP工具优先**: 每个UI组件开发前必须使用MCP获取精确参数
2. **参数严格执行**: Figma的CSS类名和百分比数值必须原样实现
3. **绝对定位思维**: 优先使用Stack + Positioned而非Row/Column估算
4. **像素级验证**: 完成后立即截图对比Figma原稿，0差异才算完成

**禁止行为**:
- ❌ 凭经验"大概估算"尺寸和位置
- ❌ 使用"看起来差不多"的数值
- ❌ 用Flutter习惯替代Figma精确定位
- ❌ 跳过MCP工具直接开发UI组件
- ❌ 手动绘制系统自带组件（如Home指示器、状态栏等）

**标准代码模式**:
```dart
// ✅ 正确：严格按照Figma参数实现
Positioned(
  bottom: 168.h * 0.4643,  // 严格按照bottom-[46.43%]
  top: 168.h * 0.1548,     // 严格按照top-[15.48%]
  child: SizedBox(
    width: 52.w,           // 从设计稿区域计算的精确尺寸
    height: 52.w,
    child: Image.asset(...),
  ),
)

// ❌ 错误：凭经验估算
Column(
  children: [
    Icon(size: 32.w),      // 随意猜测的尺寸
    SizedBox(height: 4.h), // 随意设置的间距
    Text(...),
  ],
)
```

### 📱 系统组件识别规范 **[关键经验]**
**❌ 绝对禁止**: 手动绘制系统自带组件，避免重复显示

**系统自带组件清单**:
- **Home指示器**: iPhone X系列底部黑色横线，系统自动显示
- **状态栏**: 时间、电池、信号等，系统控制
- **安全区域**: 刘海屏、圆角等区域，使用SafeArea处理
- **导航栏**: 系统返回按钮等原生导航

**正确处理方式**:
```dart
// ✅ 正确：忽略Figma中的系统组件预览
// Figma设计稿中的Home指示器仅为预览效果
// 实际开发中系统会自动显示，无需手动绘制

// ❌ 错误：手动绘制系统组件
Container(
  height: 10.h,
  color: Colors.black, // 会与系统Home指示器重复显示
)
```

### ⌨️ 键盘适配行业标准 **[关键经验]**
**❌ 严禁违背**: 键盘适配必须遵循行业标准做法，禁止自创动画方案

**行业标准唯一正确做法**:
```dart
// ✅ 行业标准：直接跟随系统键盘动画
Transform.translate(
  offset: Offset(0, -MediaQuery.of(context).viewInsets.bottom * 系数),
  child: 页面内容,
)
```

**强制约束**:
- **同步原则**: 页面动画必须与系统键盘动画完全同步
- **禁止自定义**: 严禁使用AnimatedContainer、TweenAnimationBuilder等自定义动画
- **实时跟随**: 直接使用MediaQuery.viewInsets.bottom实时计算偏移
- **系数调整**: 通过乘法系数(如0.8)微调上移距离，确保内容可见

**错误做法**:
```dart
// ❌ 错误：自定义动画时长导致不同步
AnimatedContainer(
  duration: Duration(milliseconds: 250),
  transform: Matrix4.translationValues(0, offset, 0),
)

// ❌ 错误：使用TweenAnimationBuilder造成延迟
TweenAnimationBuilder<double>(
  duration: Duration(milliseconds: 200),
  tween: Tween(begin: 0, end: offset),
)
```

**经验教训**:
- 系统键盘有自己的动画节奏，任何自定义动画都会造成不同步
- 微信、支付宝等主流APP都使用Transform.translate直接跟随方案
- 这是移动开发的基础标准，必须无条件遵循，不可自创方案

### 🎯 Flutter布局约束系统规范 **[关键经验]**
**❌ 绝对禁止**: 使用负值边距、Transform hack等违反Flutter约束系统的布局方式

**核心问题**:
- **负值布局**: 使用负值top、left等定位会导致内容超出容器边界
- **Transform hack**: 使用Transform.translate临时修补布局问题
- **约束违反**: 违反Flutter父子组件的约束传递机制

**正确布局原则**:
```dart
// ✅ 正确：使用标准Padding控制位置
Padding(
  padding: EdgeInsets.only(
    left: 40.w,
    right: 40.w,
    top: 8.h,     // 使用正值
    bottom: 45.h, // 通过增大bottom让内容上移
  ),
  child: Row(children: widgets),
)

// ❌ 错误：使用负值定位
Positioned(
  top: -8.h,    // 负值会导致溢出
  child: widget,
)

// ❌ 错误：使用Transform hack
Transform.translate(
  offset: Offset(0, -10.h), // 临时性修补措施
  child: widget,
)
```

**强制执行原则**:
- **约束驱动**: 所有布局必须遵循Flutter的约束系统
- **正值原则**: 禁止使用负值边距和定位
- **标准组件**: 优先使用Padding、Container、Column、Row等标准布局组件
- **企业级思维**: 建立可维护的布局系统，避免临时性hack方案

### 🎯 交互焦点管理规范 **[关键经验]**
**❌ 绝对禁止**: 手势事件拦截导致输入框无法获取焦点

**核心问题**:
- **外层GestureDetector拦截**: 包裹页面的手势检测器会拦截TextField点击事件
- **unfocus()执行时机**: 立即执行unfocus()会阻止TextField获取焦点
- **HitTestBehavior配置**: opaque行为会完全拦截子组件事件

**正确处理方式**:
```dart
// ✅ 正确：使用translucent行为，允许子组件优先响应
GestureDetector(
  behavior: HitTestBehavior.translucent, // 关键：允许事件穿透
  onTap: () {
    // 延迟执行，让TextField有机会获取焦点
    Future.microtask(() {
      FocusScope.of(context).unfocus();
    });
  },
  child: // 页面内容，包含TextField
)

// ✅ 正确：输入区域主动请求焦点
GestureDetector(
  onTap: () => _focusNode.requestFocus(), // 直接请求焦点
  child: Container(
    child: TextField(focusNode: _focusNode),
  ),
)
```

**禁止做法**:
```dart
// ❌ 错误：opaque行为完全拦截子组件事件
GestureDetector(
  behavior: HitTestBehavior.opaque,
  onTap: () => FocusScope.of(context).unfocus(), // 立即取消焦点
  child: TextField(...), // 无法获取焦点
)
```

**强制原则**:
- **事件穿透**: 使用translucent让子组件优先处理点击
- **时序控制**: 使用Future.microtask()确保正确的执行顺序
- **精确控制**: 输入区域单独处理焦点获取逻辑
- **双重保障**: 既保证输入框可点击，又保证空白区域收起键盘

### 📱 移动端交互最佳实践 **[行业标准]**
**❌ 零容忍**: 任何交互组件必须符合移动端最佳实践标准

**点击目标最小尺寸**:
- **iOS标准**: 最小44pt × 44pt (约88px × 88px @2x)
- **Android标准**: 最小48dp × 48dp (约96px × 96px @xxhdpi)
- **YGS标准**: 统一使用最小120.w × 80.h的点击区域

**强制实现要点**:
```dart
// ✅ 正确：扩大点击区域的标准做法
GestureDetector(
  behavior: HitTestBehavior.opaque, // 扩大到整个容器
  child: Container(
    height: 80.h, // 足够的点击高度
    padding: EdgeInsets.symmetric(vertical: 20.h), // 增加上下点击范围
    child: 交互元素,
  ),
)

// ❌ 错误：只有文字/图标可点击
GestureDetector(
  child: Text('按钮'), // 点击区域过小
)
```

**通用交互规范**:
- **Tab导航**: 最小80.h高度 + 20.h上下内边距
- **按钮**: 最小88.w × 88.h点击区域
- **列表项**: 最小120.h高度
- **输入框**: 最小80.h高度，左右内边距≥24.w

### 🏆 行业高标准强制原则
**❌ 零容忍底线**: 任何功能实现必须达到行业顶级标准

**强制执行标准**:
- **企业级完整性**: 每个功能必须包含完整的错误处理、资源预加载、异常恢复机制
- **用户体验优先**: 严格遵循行业UX标准，如启动页3-5秒展示、动画流畅性
- **移动端交互**: 所有点击目标必须符合44pt/48dp最小尺寸标准
- **代码健壮性**: 实现防崩溃机制、优雅降级、资源管理、内存清理
- **性能基准**: 达到行业基准性能指标，不允许因开发便利而牺牲质量
- **设计还原度**: 必须达到像素级完美，与Figma设计稿0差异

## 🔧 后端开发规范 (NestJS)

### 🏗️ NestJS 开发约束
- **框架版本**: NestJS 11 + Fastify 5，严格版本控制
- **架构模式**: 模块化架构，单一职责原则
- **依赖注入**: 充分利用NestJS依赖注入系统
- **装饰器**: 规范使用Controller、Service、Repository装饰器

### 🔐 数据库操作规范
- **ORM框架**: TypeORM，统一数据访问层
- **连接管理**: 必须使用阿里云RDS PostgreSQL
- **查询优化**: 合理使用索引、连接池、查询优化
- **事务管理**: 关键操作必须包含事务处理

### 🔒 TypeScript 严格规范
```typescript
// ✅ 正确的后端代码规范
@Injectable()
export class UserService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  // 中文注释说明方法功能
  async findById(id: string): Promise<ServiceResult<User>> {
    try {
      const user = await this.userRepository.findOne({ where: { id } });
      if (!user) {
        throw new NotFoundException('用户不存在');
      }
      return { success: true, data: user };
    } catch (error) {
      // 中文错误处理
      return { success: false, error: '查询用户失败' };
    }
  }
}
```

### 🧪 后端测试规范
- **单元测试**: Service、Controller、Utils单元测试
- **集成测试**: 模块间协作、数据库集成测试
- **E2E测试**: 完整业务流程端到端测试
- **测试覆盖率**: ≥80%

## 🛡️ 全局公共规范

### ⏰ 时间标准规范
- **时区**: 北京时间 (UTC+8)
- **时间来源**: 使用环境变量中的 "Today's date" 作为基准
- **格式标准**: YYYY-MM-DD (如: 2025-07-28)

### ⚡ 命令执行时间管理
**❌ 严禁长时间等待**: 各类命令执行不得超过合理时间

| 操作类型 | 最大等待时间 | 超时处理 |
|---------|-------------|----------|
| 服务器启动 | 10秒 | 立即继续后续操作 |
| 数据库连接 | 15秒 | 立即分析问题原因 |
| 测试执行 | 60秒 | 停止并分析问题 |

### 📋 产品文档对照规范
**❌ 绝对禁止**: 脱离产品文档进行功能开发

**强制流程**:
1. **开发前必读**: 先阅读对应的产品文档
2. **逐条对照**: 对照产品文档的功能需求和业务逻辑
3. **疑问立即停止**: 文档不清楚时立即停止开发

**关键文档**:
- `产品文档/v1.0.0/` - 功能需求基准
- `对接文档/API接口文档.md` - 134个接口技术规范

### 📱 跨平台布局一致性规范 **[关键经验]**
**❌ 绝对禁止**: 使用Transform、负边距等hack方法解决跨平台间距差异

**核心问题根源**:
- **SafeArea平台差异**: Web环境MediaQuery.padding通常为0，移动端有实际状态栏高度
- **flutter_screenutil缺陷**: 在Web和移动端计算机制存在偏差，导致尺寸异常
- **设备特性差异**: 刘海屏vs普通屏幕的SafeArea空间差异

**企业级解决方案**:
```dart
// ✅ 正确：企业级自适应间距系统
class AdaptiveSpacing {
  static double getSearchToTabSpacing(BuildContext context) {
    final deviceType = getDeviceType(context);
    final isWeb = kIsWeb;
    
    double multiplier = 1.0;
    
    // Web端特殊处理：补偿SafeArea差异
    if (isWeb) {
      multiplier *= 1.3; // Web端SafeArea不生效，需要额外间距
    }
    
    // 移动端特殊处理：考虑实际SafeArea影响
    if (!isWeb) {
      final mediaQuery = MediaQuery.of(context);
      final hasNotch = mediaQuery.padding.top > 24; // 检测刘海屏
      
      if (hasNotch) {
        multiplier *= 0.6; // 刘海屏设备大幅减少间距
      } else {
        multiplier *= 0.8; // 普通设备适度减少间距
      }
    }
    
    return 8.0 * multiplier; // 基础间距 * 平台系数
  }
}
```

**技术架构原则**:
1. **避免flutter_screenutil**: 使用LayoutBuilder + MediaQuery.sizeOf()
2. **智能设备检测**: 基于MediaQuery.padding.top检测刘海屏
3. **跨平台SafeArea**: Web用Padding模拟，移动端用原生SafeArea
4. **约束驱动设计**: 基于可用空间而非设备类型设计布局

**禁止做法**:
```dart
// ❌ 错误：使用Transform hack方法
Transform.translate(
  offset: Offset(0, -10.h), // 临时性修补措施
  child: TabBar(),
)

// ❌ 错误：使用负边距
padding: EdgeInsets.only(bottom: -10.h), // 违反Flutter约束系统

// ❌ 错误：硬编码平台差异
if (kIsWeb) { return 8.h; } else { return 4.h; } // 缺乏设备特性考虑
```

**标准文件结构**:
- `/lib/core/responsive/adaptive_spacing.dart` - 企业级响应式间距系统
- 包含DeviceType枚举、SpacingContext上下文、AdaptiveSafeArea组件

**实施要求**:
- 所有跨平台布局问题必须使用AdaptiveSpacing系统解决
- 禁止使用Transform、负边距等临时性修补措施
- 必须考虑Web、普通移动设备、刘海屏设备三种场景
- 新增响应式组件必须基于LayoutBuilder构建

### 🎯 企业级自适应布局强制思维 **[核心原则]**
**❌ 绝对禁止**: 基于特定设备进行布局和样式开发，必须站在自动适配所有设备的企业级要求开发

**核心问题根源**:
- **设备思维误区**: 前端开发陷入"针对设备调试"的错误思维模式
- **硬编码适配**: 使用固定像素值或设备特定的调试方法
- **临时性方案**: 通过调试代码（如红线参考线）解决对齐问题，而非建立自适应系统

**企业级自适应布局思维转变**:
```
❌ 设备调试思维: 在iPhone上看效果 → 在Android上调试 → 加调试线对齐 → 各设备分别处理
✅ 企业级思维: 建立自适应算法 → 基于可用空间计算 → 所有设备自动完美适配 → 零调试代码
```

**强制执行原则**:
1. **算法驱动布局**: 使用TextPainter、LayoutBuilder等API精确计算，而非目测调试
2. **比例化设计**: 基于屏幕比例和可用空间进行布局，而非固定像素值
3. **零调试代码**: 任何红线、打印调试、硬编码对齐都是不可接受的临时方案
4. **约束传播**: 充分利用Flutter的约束系统，让子组件自动适应父容器
5. **测量优先**: 所有文字、图标、组件都必须先测量实际尺寸再计算位置

**正确的企业级对齐方案**:
```dart
// ✅ 正确：企业级自适应对齐算法
double _calculateTimeTabPosition() {
  // 1. 动态测量文字实际宽度
  final textPainter = TextPainter(
    text: TextSpan(text: '时间', style: currentTextStyle),
    textDirection: TextDirection.ltr,
  );
  textPainter.layout();
  
  // 2. 基于可用空间计算时间轴位置（而非硬编码114.w）
  final availableWidth = MediaQuery.sizeOf(context).width;
  final timeAxisPosition = availableWidth * 0.152; // 时间轴相对位置
  
  // 3. 算法计算精确对齐位置
  return timeAxisPosition - (textPainter.size.width / 2);
}
```

**禁止的设备调试思维**:
```dart
// ❌ 错误：硬编码设备特定数值
final calculatedPosition = 114.w - (textWidth / 2); // 基于750px设计稿硬编码

// ❌ 错误：调试代码辅助对齐
Positioned(
  left: 114.w - 0.5, // 调试参考线
  child: Container(width: 1, color: Colors.red),
)

// ❌ 错误：打印调试信息
print('目标时间轴位置: 114.w = ${114.w}'); // 临时调试方案
```

**企业级布局开发标准流程**:
1. **分析需求**: 理解对齐的业务逻辑和视觉要求
2. **设计算法**: 基于比例、约束、测量建立自适应算法
3. **实现代码**: 使用Flutter标准API实现算法，无硬编码
4. **全设备验证**: 在不同尺寸设备上验证自动适配效果
5. **零调试代码**: 确保生产代码中无任何调试辅助元素

**强制禁止行为**:
- ❌ 使用红线、参考线等视觉调试辅助
- ❌ 在生产代码中保留print调试信息
- ❌ 基于特定设备尺寸进行硬编码
- ❌ 使用"看起来差不多"的经验数值
- ❌ 为了快速出效果而采用临时性对齐方案

**质量标准**:
- 所有设备自动完美对齐，无需手动调试
- 代码中零调试辅助元素，纯净的生产级代码
- 新设备自动适配，无需额外开发工作
- 布局算法经得起任何屏幕尺寸和分辨率考验

### 🔍 代码审查标准
- **设计模式**: 采用标准设计模式
- **性能优化**: 关注性能热点和优化
- **安全防护**: 数据验证、权限控制、防护机制
- **可维护性**: 代码结构清晰，模块边界明确

### 📝 文档同步规范
- **代码注释**: 关键逻辑必须有中文注释
- **API文档**: 接口变更同步更新文档
- **README维护**: 遵循各目录README的联动机制
- **技术文档**: 架构变更同步更新技术文档

## 🚨 代码质量零容忍标准

### 📊 编译要求
- **Flutter**: 0错误 0警告
- **TypeScript**: 0编译错误  
- **测试覆盖率**: ≥80%

### 🔍 开发流程
1. **理解需求** → 对照产品文档和Figma设计稿
2. **使用设计系统** → 严格使用YGS组件和令牌
3. **响应式适配** → 确保750×1624基准下的完美适配
4. **质量检查** → 0错误状态交付

## ⚡ AI开发助手执行约束

### ❌ 严禁行为
- 为了进度而降低代码质量
- 跳过测试编写或忽略测试失败
- 使用临时解决方案绕过技术问题  
- 在业务代码中使用any类型（测试文件除外）
- 硬编码配置信息或魔法数字

### ✅ 强制执行行为
- 每次代码生成前回顾相关规范
- 代码生成后立即执行质量检查
- 发现问题立即修复，确保0错误状态
- 同步更新相关文档和注释

### 🔄 开发流程约束
1. **理解需求**: 充分理解业务需求和技术要求
2. **设计方案**: 考虑架构、安全、性能、扩展性
3. **编写代码**: 遵循技术栈规范和质量标准
4. **测试验证**: 编写完整测试，确保覆盖率达标
5. **文档同步**: 更新相关文档，保持一致性
6. **质量检查**: 执行所有质量检查，确保0错误

## 🚨 违规处理

### 轻微违规 (代码规范)
- 立即修复并继续

### 严重违规 (质量标准、架构原则、设计稿偏离)
- 立即停止当前工作
- 重新review需求和规范
- 完全符合标准后方可继续

### 持续违规
- 深入review全局开发规范
- 强化质量检查流程
- 建立更严格的自检机制

---

**规范承诺**: 每一行代码都必须达到企业级标准，为项目长期成功提供坚实保障

**执行机构**: YGS开发团队 & AI开发助手  
**监督机制**: 代码审查 + 自动化检查 + 文档联动验证