#!/bin/bash

# Flutter启动脚本
echo "🚀 正在启动YGS Flutter应用..."

# 切换到项目目录
cd "$(dirname "$0")"

# 设置Flutter官方源（临时）
export FLUTTER_STORAGE_BASE_URL="https://storage.googleapis.com"
export PUB_HOSTED_URL="https://pub.dev"

# 检查可用设备
echo "📱 检查可用设备..."
flutter devices

# 获取依赖（如果需要）
echo "📦 获取项目依赖..."
flutter pub get --no-example

# 运行应用
echo "🎯 启动应用..."
flutter run

# 如果无法自动选择设备，提供选项
if [ $? -ne 0 ]; then
    echo "❌ 启动失败，请手动选择设备："
    echo "1. iOS模拟器: flutter run -d iphone"
    echo "2. Android模拟器: flutter run -d android"
    echo "3. Chrome浏览器: flutter run -d chrome"
    echo "4. macOS桌面: flutter run -d macos"
fi