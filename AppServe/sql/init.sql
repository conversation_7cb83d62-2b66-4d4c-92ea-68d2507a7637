-- 有故事APP数据库初始化脚本
-- 此文件会在PostgreSQL容器首次启动时自动执行

-- 创建开发数据库（如果不存在）
CREATE DATABASE story_app_dev OWNER postgres;

-- 创建测试数据库（用于测试）
CREATE DATABASE story_app_test OWNER postgres;

-- 设置数据库编码
ALTER DATABASE story_app_dev SET timezone TO 'Asia/Shanghai';
ALTER DATABASE story_app_test SET timezone TO 'Asia/Shanghai';

-- 切换到开发数据库
\c story_app_dev;

-- 创建UUID扩展（TypeORM可能需要）
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 输出初始化完成信息
SELECT 'Database initialization completed!' as status; 