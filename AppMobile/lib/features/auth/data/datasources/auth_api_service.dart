import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/network/api_result.dart';
import '../../../../core/network/base_api_service.dart';
import '../../../../core/network/dio_client.dart';
import '../models/auth_request_models.dart';
import '../models/auth_response_models.dart';

/// 认证API服务Provider
final authApiServiceProvider = Provider<AuthApiService>((ref) {
  return AuthApiService(dio: ref.watch(dioProvider));
});

/// 认证API服务实现
class AuthApiService extends BaseApiService {
  AuthApiService({super.dio});

  /// 发送短信验证码
  Future<ApiResult<void>> sendSmsCode(SendSmsRequest request) async {
    return post<void>(
      '/auth/send-sms',
      data: request.toJson(),
    );
  }

  /// 手机号登录/注册
  Future<ApiResult<AuthResponse>> phoneLogin(PhoneLoginRequest request) async {
    return post<AuthResponse>(
      '/auth/login/phone',
      data: request.toJson(),
      parser: (data) => AuthResponse.fromJson(data),
    );
  }

  /// 邮箱密码注册
  Future<ApiResult<AuthResponse>> emailRegister(EmailRegisterRequest request) async {
    return post<AuthResponse>(
      '/auth/register/email',
      data: request.toJson(),
      parser: (data) => AuthResponse.fromJson(data),
    );
  }

  /// 邮箱密码登录
  Future<ApiResult<AuthResponse>> emailLogin(EmailLoginRequest request) async {
    return post<AuthResponse>(
      '/auth/login/email',
      data: request.toJson(),
      parser: (data) => AuthResponse.fromJson(data),
    );
  }

  /// 设置邮箱密码
  Future<ApiResult<void>> setEmailPassword(SetEmailPasswordRequest request) async {
    return post<void>(
      '/auth/set-email-password',
      data: request.toJson(),
    );
  }

  /// 刷新访问令牌
  Future<ApiResult<RefreshTokenResponse>> refreshToken(String refreshToken) async {
    return post<RefreshTokenResponse>(
      '/auth/refresh',
      data: {'refreshToken': refreshToken},
      parser: (data) => RefreshTokenResponse.fromJson(data),
    );
  }

  /// 获取用户信息
  Future<ApiResult<User>> getUserProfile() async {
    return get<User>(
      '/auth/profile',
      parser: (data) => User.fromJson(data),
    );
  }

  /// 用户登出
  Future<ApiResult<void>> logout() async {
    return post<void>('/auth/logout');
  }
}