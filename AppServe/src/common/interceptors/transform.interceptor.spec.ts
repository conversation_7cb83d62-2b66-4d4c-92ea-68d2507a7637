import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import type { ExecutionContext, CallHandler } from "@nestjs/common";
import { TransformInterceptor } from "./transform.interceptor";
import { of } from "rxjs";

describe("TransformInterceptor", () => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let interceptor: TransformInterceptor<any>;

  const mockExecutionContext = {} as ExecutionContext;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [TransformInterceptor],
    }).compile();

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    interceptor = module.get<TransformInterceptor<any>>(TransformInterceptor);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("intercept", () => {
    it("应该保持已有标准格式（包含code）的响应", (done) => {
      const standardResponse = {
        code: 200,
        message: "success",
        data: { id: 1, name: "test" },
      };

      const mockCallHandler: CallHandler = {
        handle: jest.fn().mockReturnValue(of(standardResponse)),
      };

      const result$ = interceptor.intercept(
        mockExecutionContext,
        mockCallHandler,
      );

      result$.subscribe({
        next: (data) => {
          expect(data).toEqual({
            ...standardResponse,
            timestamp: expect.any(Number),
          });
          expect(data.code).toBe(200);
          expect(data.message).toBe("success");
          expect(data.data).toEqual({ id: 1, name: "test" });
          expect(data.timestamp).toBeDefined();
          done();
        },
      });
    });

    it("应该保持自定义格式（包含success）的响应", (done) => {
      const customResponse = {
        success: true,
        message: "操作成功",
        data: { id: 1, name: "test" },
        statusCode: 200,
      };

      const mockCallHandler: CallHandler = {
        handle: jest.fn().mockReturnValue(of(customResponse)),
      };

      const result$ = interceptor.intercept(
        mockExecutionContext,
        mockCallHandler,
      );

      result$.subscribe({
        next: (data) => {
          expect(data).toEqual({
            ...customResponse,
            timestamp: expect.any(Number),
          });
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          expect((data as any).success).toBe(true);
          expect(data.message).toBe("操作成功");
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          expect((data as any).statusCode).toBe(200);
          expect(data.timestamp).toBeDefined();
          done();
        },
      });
    });

    it("应该包装普通对象响应", (done) => {
      const plainResponse = { id: 1, name: "test", email: "<EMAIL>" };

      const mockCallHandler: CallHandler = {
        handle: jest.fn().mockReturnValue(of(plainResponse)),
      };

      const result$ = interceptor.intercept(
        mockExecutionContext,
        mockCallHandler,
      );

      result$.subscribe({
        next: (data) => {
          expect(data).toEqual({
            code: 200,
            message: "success",
            data: plainResponse,
            timestamp: expect.any(Number),
          });
          expect(data.code).toBe(200);
          expect(data.message).toBe("success");
          expect(data.data).toEqual(plainResponse);
          expect(data.timestamp).toBeDefined();
          done();
        },
      });
    });

    it("应该包装字符串响应", (done) => {
      const stringResponse = "操作成功";

      const mockCallHandler: CallHandler = {
        handle: jest.fn().mockReturnValue(of(stringResponse)),
      };

      const result$ = interceptor.intercept(
        mockExecutionContext,
        mockCallHandler,
      );

      result$.subscribe({
        next: (data) => {
          expect(data).toEqual({
            code: 200,
            message: "success",
            data: stringResponse,
            timestamp: expect.any(Number),
          });
          expect(data.data).toBe(stringResponse);
          done();
        },
      });
    });

    it("应该包装数字响应", (done) => {
      const numberResponse = 42;

      const mockCallHandler: CallHandler = {
        handle: jest.fn().mockReturnValue(of(numberResponse)),
      };

      const result$ = interceptor.intercept(
        mockExecutionContext,
        mockCallHandler,
      );

      result$.subscribe({
        next: (data) => {
          expect(data).toEqual({
            code: 200,
            message: "success",
            data: numberResponse,
            timestamp: expect.any(Number),
          });
          expect(data.data).toBe(numberResponse);
          done();
        },
      });
    });

    it("应该包装数组响应", (done) => {
      const arrayResponse = [
        { id: 1, name: "test1" },
        { id: 2, name: "test2" },
      ];

      const mockCallHandler: CallHandler = {
        handle: jest.fn().mockReturnValue(of(arrayResponse)),
      };

      const result$ = interceptor.intercept(
        mockExecutionContext,
        mockCallHandler,
      );

      result$.subscribe({
        next: (data) => {
          expect(data).toEqual({
            code: 200,
            message: "success",
            data: arrayResponse,
            timestamp: expect.any(Number),
          });
          expect(data.data).toEqual(arrayResponse);
          done();
        },
      });
    });

    it("应该包装null响应", (done) => {
      const nullResponse = null;

      const mockCallHandler: CallHandler = {
        handle: jest.fn().mockReturnValue(of(nullResponse)),
      };

      const result$ = interceptor.intercept(
        mockExecutionContext,
        mockCallHandler,
      );

      result$.subscribe({
        next: (data) => {
          expect(data).toEqual({
            code: 200,
            message: "success",
            data: null,
            timestamp: expect.any(Number),
          });
          expect(data.data).toBeNull();
          done();
        },
      });
    });

    it("应该包装undefined响应", (done) => {
      const undefinedResponse = undefined;

      const mockCallHandler: CallHandler = {
        handle: jest.fn().mockReturnValue(of(undefinedResponse)),
      };

      const result$ = interceptor.intercept(
        mockExecutionContext,
        mockCallHandler,
      );

      result$.subscribe({
        next: (data) => {
          expect(data).toEqual({
            code: 200,
            message: "success",
            data: undefined,
            timestamp: expect.any(Number),
          });
          expect(data.data).toBeUndefined();
          done();
        },
      });
    });

    it("应该处理布尔值响应", (done) => {
      const booleanResponse = true;

      const mockCallHandler: CallHandler = {
        handle: jest.fn().mockReturnValue(of(booleanResponse)),
      };

      const result$ = interceptor.intercept(
        mockExecutionContext,
        mockCallHandler,
      );

      result$.subscribe({
        next: (data) => {
          expect(data).toEqual({
            code: 200,
            message: "success",
            data: true,
            timestamp: expect.any(Number),
          });
          expect(data.data).toBe(true);
          done();
        },
      });
    });

    it("应该处理空对象响应", (done) => {
      const emptyObjectResponse = {};

      const mockCallHandler: CallHandler = {
        handle: jest.fn().mockReturnValue(of(emptyObjectResponse)),
      };

      const result$ = interceptor.intercept(
        mockExecutionContext,
        mockCallHandler,
      );

      result$.subscribe({
        next: (data) => {
          expect(data).toEqual({
            code: 200,
            message: "success",
            data: {},
            timestamp: expect.any(Number),
          });
          expect(data.data).toEqual({});
          done();
        },
      });
    });

    it("应该处理错误状态码的标准格式响应", (done) => {
      const errorResponse = {
        code: 400,
        message: "Bad Request",
        data: null,
      };

      const mockCallHandler: CallHandler = {
        handle: jest.fn().mockReturnValue(of(errorResponse)),
      };

      const result$ = interceptor.intercept(
        mockExecutionContext,
        mockCallHandler,
      );

      result$.subscribe({
        next: (data) => {
          expect(data).toEqual({
            ...errorResponse,
            timestamp: expect.any(Number),
          });
          expect(data.code).toBe(400);
          expect(data.message).toBe("Bad Request");
          done();
        },
      });
    });

    it("应该保持自定义错误格式响应", (done) => {
      const customErrorResponse = {
        success: false,
        message: "操作失败",
        error: "详细错误信息",
        statusCode: 400,
      };

      const mockCallHandler: CallHandler = {
        handle: jest.fn().mockReturnValue(of(customErrorResponse)),
      };

      const result$ = interceptor.intercept(
        mockExecutionContext,
        mockCallHandler,
      );

      result$.subscribe({
        next: (data) => {
          expect(data).toEqual({
            ...customErrorResponse,
            timestamp: expect.any(Number),
          });
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          expect((data as any).success).toBe(false);
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          expect((data as any).statusCode).toBe(400);
          done();
        },
      });
    });

    it("应该添加正确的时间戳", (done) => {
      const now = Date.now();
      jest.spyOn(Date, "now").mockReturnValue(now);

      const response = { id: 1, name: "test" };
      const mockCallHandler: CallHandler = {
        handle: jest.fn().mockReturnValue(of(response)),
      };

      const result$ = interceptor.intercept(
        mockExecutionContext,
        mockCallHandler,
      );

      result$.subscribe({
        next: (data) => {
          expect(data.timestamp).toBe(now);
          done();
        },
      });
    });
  });

  it("应该正确实例化", () => {
    expect(interceptor).toBeDefined();
    expect(interceptor).toBeInstanceOf(TransformInterceptor);
  });
});
