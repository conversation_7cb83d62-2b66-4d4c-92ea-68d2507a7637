/**
 * 环境变量工具测试 - 企业级工具函数验证
 * 测试类型安全的环境变量解析函数
 */

import {
  parseIntEnv,
  parseFloatEnv,
  parseBooleanEnv,
  getStringEnv,
  getRequiredEnv,
  getRequiredIntEnv,
} from "./env.utils";

describe("EnvUtils - 企业级工具函数测试", () => {
  describe("parseIntEnv", () => {
    it("should return default value when env var is undefined", () => {
      const result = parseIntEnv(undefined, 100);
      expect(result).toBe(100);
    });

    it("should return default value when env var is empty string", () => {
      const result = parseIntEnv("", 100);
      expect(result).toBe(100);
    });

    it("should parse valid integer string", () => {
      const result = parseIntEnv("42", 100);
      expect(result).toBe(42);
    });

    it("should return default value for invalid integer string", () => {
      const result = parseIntEnv("not-a-number", 100);
      expect(result).toBe(100);
    });

    it("should parse negative integers", () => {
      const result = parseIntEnv("-42", 100);
      expect(result).toBe(-42);
    });

    it("should handle zero value", () => {
      const result = parseIntEnv("0", 100);
      expect(result).toBe(0);
    });
  });

  describe("parseFloatEnv", () => {
    it("should return default value when env var is undefined", () => {
      const result = parseFloatEnv(undefined, 3.14);
      expect(result).toBe(3.14);
    });

    it("should return default value when env var is empty string", () => {
      const result = parseFloatEnv("", 3.14);
      expect(result).toBe(3.14);
    });

    it("should parse valid float string", () => {
      const result = parseFloatEnv("3.14159", 0);
      expect(result).toBe(3.14159);
    });

    it("should return default value for invalid float string", () => {
      const result = parseFloatEnv("not-a-number", 3.14);
      expect(result).toBe(3.14);
    });

    it("should parse integer as float", () => {
      const result = parseFloatEnv("42", 0);
      expect(result).toBe(42.0);
    });

    it("should handle negative floats", () => {
      const result = parseFloatEnv("-3.14", 0);
      expect(result).toBe(-3.14);
    });
  });

  describe("parseBooleanEnv", () => {
    it("should return default value when env var is undefined", () => {
      const result = parseBooleanEnv(undefined, false);
      expect(result).toBe(false);
    });

    it("should return default value when env var is empty string", () => {
      const result = parseBooleanEnv("", true);
      expect(result).toBe(true);
    });

    it('should return true for "true" string', () => {
      const result = parseBooleanEnv("true", false);
      expect(result).toBe(true);
    });

    it('should return true for "TRUE" string (case insensitive)', () => {
      const result = parseBooleanEnv("TRUE", false);
      expect(result).toBe(true);
    });

    it('should return true for "1" string', () => {
      const result = parseBooleanEnv("1", false);
      expect(result).toBe(true);
    });

    it('should return false for "false" string', () => {
      const result = parseBooleanEnv("false", true);
      expect(result).toBe(false);
    });

    it('should return false for "0" string', () => {
      const result = parseBooleanEnv("0", true);
      expect(result).toBe(false);
    });

    it("should return false for any other string", () => {
      const result = parseBooleanEnv("random", true);
      expect(result).toBe(false);
    });
  });

  describe("getStringEnv", () => {
    it("should return default value when env var is undefined", () => {
      const result = getStringEnv(undefined, "default");
      expect(result).toBe("default");
    });

    it("should return default value when env var is empty string", () => {
      const result = getStringEnv("", "default");
      expect(result).toBe("default");
    });

    it("should return env var value when provided", () => {
      const result = getStringEnv("test-value", "default");
      expect(result).toBe("test-value");
    });

    it("should handle whitespace values", () => {
      const result = getStringEnv("   ", "default");
      expect(result).toBe("   ");
    });
  });

  describe("getRequiredEnv", () => {
    let originalEnv: NodeJS.ProcessEnv;

    beforeAll(() => {
      originalEnv = { ...process.env };
    });

    afterEach(() => {
      process.env = { ...originalEnv };
    });

    it("should return env var value when it exists", () => {
      process.env.TEST_VAR = "test-value";
      const result = getRequiredEnv("TEST_VAR");
      expect(result).toBe("test-value");
    });

    it("should throw error when env var does not exist", () => {
      delete process.env.TEST_VAR;
      expect(() => getRequiredEnv("TEST_VAR")).toThrow(
        "Required environment variable TEST_VAR is not set",
      );
    });

    it("should throw error when env var is empty string", () => {
      process.env.TEST_VAR = "";
      expect(() => getRequiredEnv("TEST_VAR")).toThrow(
        "Required environment variable TEST_VAR is not set",
      );
    });
  });

  describe("getRequiredIntEnv", () => {
    let originalEnv: NodeJS.ProcessEnv;

    beforeAll(() => {
      originalEnv = { ...process.env };
    });

    afterEach(() => {
      process.env = { ...originalEnv };
    });

    it("should return parsed integer when env var is valid", () => {
      process.env.TEST_INT_VAR = "42";
      const result = getRequiredIntEnv("TEST_INT_VAR");
      expect(result).toBe(42);
    });

    it("should throw error when env var does not exist", () => {
      delete process.env.TEST_INT_VAR;
      expect(() => getRequiredIntEnv("TEST_INT_VAR")).toThrow(
        "Required environment variable TEST_INT_VAR is not set",
      );
    });

    it("should throw error when env var is not a valid integer", () => {
      process.env.TEST_INT_VAR = "not-a-number";
      expect(() => getRequiredIntEnv("TEST_INT_VAR")).toThrow(
        "Environment variable TEST_INT_VAR must be a valid integer",
      );
    });

    it("should handle negative integers", () => {
      process.env.TEST_INT_VAR = "-42";
      const result = getRequiredIntEnv("TEST_INT_VAR");
      expect(result).toBe(-42);
    });

    it("should handle zero value", () => {
      process.env.TEST_INT_VAR = "0";
      const result = getRequiredIntEnv("TEST_INT_VAR");
      expect(result).toBe(0);
    });
  });

  describe("类型安全验证", () => {
    it("parseIntEnv should always return number", () => {
      const result1 = parseIntEnv("42", 0);
      const result2 = parseIntEnv(undefined, 100);

      expect(typeof result1).toBe("number");
      expect(typeof result2).toBe("number");
    });

    it("parseFloatEnv should always return number", () => {
      const result1 = parseFloatEnv("3.14", 0);
      const result2 = parseFloatEnv(undefined, 2.71);

      expect(typeof result1).toBe("number");
      expect(typeof result2).toBe("number");
    });

    it("parseBooleanEnv should always return boolean", () => {
      const result1 = parseBooleanEnv("true", false);
      const result2 = parseBooleanEnv(undefined, true);

      expect(typeof result1).toBe("boolean");
      expect(typeof result2).toBe("boolean");
    });

    it("getStringEnv should always return string", () => {
      const result1 = getStringEnv("test", "default");
      const result2 = getStringEnv(undefined, "default");

      expect(typeof result1).toBe("string");
      expect(typeof result2).toBe("string");
    });
  });
});
