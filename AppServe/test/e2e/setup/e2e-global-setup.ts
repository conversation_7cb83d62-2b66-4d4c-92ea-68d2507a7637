/**
 * 企业级E2E测试全局设置
 *
 * 职责:
 * 1. 完整应用环境初始化
 * 2. 测试服务器启动
 * 3. 真实HTTP请求环境配置
 * 4. 企业级性能基准设置
 */

import * as dotenv from "dotenv";

export default async function setup(): Promise<void> {
  console.log("🚀 企业级E2E测试环境初始化开始...");

  // 1. 加载E2E测试环境变量
  dotenv.config({ path: ".env.e2e.test" });

  // 2. 确保使用E2E测试数据库（企业级云端RDS）
  const testDbName = process.env.DB_NAME || "ygs-e2e-test";
  if (!testDbName.includes("e2e") && !testDbName.includes("test")) {
    throw new Error("❌ 安全检查失败: 必须使用E2E测试数据库");
  }

  // 3. 验证云端RDS连接配置
  const dbHost = process.env.DB_HOST;
  if (!dbHost || !dbHost.includes("rds.aliyuncs.com")) {
    throw new Error("❌ 企业级验证失败: E2E测试必须使用阿里云RDS数据库");
  }

  console.log(`📊 使用E2E测试数据库: ${testDbName}`);
  console.log(`🌐 连接阿里云RDS: ${dbHost}`);

  try {
    // 4. 设置E2E测试环境变量
    process.env.NODE_ENV = "e2e-test";
    process.env.DB_NAME = testDbName;
    process.env.REDIS_PREFIX = "e2e-test:";
    process.env.TEST_PORT = process.env.TEST_PORT || "3001";

    // 4. 性能基准配置
    process.env.E2E_RESPONSE_TIMEOUT = "5000"; // 5秒API响应超时
    process.env.E2E_APP_STARTUP_TIMEOUT = "30000"; // 30秒应用启动超时

    console.log("✅ 企业级E2E测试环境初始化完成");
    console.log("🎯 测试环境特性: 阿里云RDS + 完整应用实例 + 真实HTTP请求验证");
    console.log(`📡 测试服务器端口: ${process.env.TEST_PORT}`);
    console.log("🌐 云端一致性: 与生产环境完全相同的阿里云基础设施");
  } catch (error) {
    console.error("❌ E2E测试环境初始化失败:", (error as Error).message);
    throw error;
  }
}
