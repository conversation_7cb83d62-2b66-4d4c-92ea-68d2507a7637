import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from "typeorm";
import { User } from "../../users/entities/user.entity";
import { Story } from "./story.entity";
import { Character } from "../../characters/entities/character.entity";

export enum ShareType {
  LINK = "link",
  POSTER = "poster",
  EXCERPT = "excerpt",
}

@Entity("story_shares")
export class StoryShare {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ name: "story_id" })
  storyId: string;

  @Column({ name: "sharer_id" })
  sharerId: string;

  @Column({ name: "target_character_id", nullable: true })
  targetCharacterId: string;

  @Column({
    name: "share_type",
    type: "varchar",
    length: 20,
    comment: "分享类型：link, poster, excerpt",
  })
  shareType: ShareType;

  @Column({
    name: "share_content",
    type: "text",
    nullable: true,
    comment: "分享的具体内容或链接",
  })
  shareContent: string;

  @Column({
    name: "share_token",
    length: 64,
    nullable: true,
    comment: "分享访问令牌",
  })
  shareToken: string;

  @Column({ name: "view_count", default: 0, comment: "查看次数" })
  viewCount: number;

  @Column({ name: "expires_at", nullable: true, comment: "过期时间" })
  expiresAt: Date;

  @CreateDateColumn({ name: "created_at", comment: "创建时间" })
  createdAt: Date;

  // 关联关系
  @ManyToOne(() => Story, (story) => story.shares, { onDelete: "CASCADE" })
  @JoinColumn({ name: "story_id" })
  story: Story;

  @ManyToOne(() => User, (user) => user.shares, { onDelete: "CASCADE" })
  @JoinColumn({ name: "sharer_id" })
  sharer: User;

  @ManyToOne(() => Character, { nullable: true })
  @JoinColumn({ name: "target_character_id" })
  targetCharacter: Character;
}
