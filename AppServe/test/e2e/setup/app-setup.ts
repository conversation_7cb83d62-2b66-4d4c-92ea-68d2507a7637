/**
 * 企业级E2E测试应用设置
 *
 * 职责:
 * 1. NestJS应用实例创建和配置
 * 2. E2E测试专用Mock配置
 * 3. 全局拦截器和守卫配置
 * 4. 测试应用生命周期管理
 */

import type { INestApplication } from "@nestjs/common";
import { Test } from "@nestjs/testing";
import { AppModule } from "../../../src/app.module";
import * as request from "supertest";

console.log("🔧 企业级E2E测试应用设置初始化...");

let app: INestApplication;

beforeAll(async () => {
  console.log("🏗️ 创建E2E测试应用实例...");

  try {
    // 创建测试模块
    const moduleFixture = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    // 创建应用实例
    app = moduleFixture.createNestApplication();

    // 设置全局前缀
    app.setGlobalPrefix("api/v1");

    // 启动应用
    await app.init();

    console.log("✅ E2E测试应用启动成功");
  } catch (error) {
    console.error("❌ E2E测试应用启动失败:", (error as Error).message);
    throw error;
  }
});

afterAll(async () => {
  if (app) {
    await app.close();
    console.log("✅ E2E测试应用已关闭");
  }
});

// 导出应用实例供测试使用
export const getTestApp = (): INestApplication => {
  if (!app) {
    throw new Error("E2E测试应用未初始化");
  }
  return app;
};

// 导出请求工具
export const testRequest = () => {
  return request(getTestApp().getHttpServer());
};
