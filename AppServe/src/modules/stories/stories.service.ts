import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
  Inject,
  forwardRef,
  Optional,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import type { SelectQueryBuilder } from "typeorm";
import { Repository, In } from "typeorm";
import { Story, StoryStatus } from "./entities/story.entity";
import { Character } from "../characters/entities/character.entity";
import { User } from "../users/entities/user.entity";
import { Theme } from "./entities/theme.entity";
import type {
  CreateStoryDto,
  UpdateStoryDto,
  StoryQueryDto,
  ContentValidationResult,
} from "./dto";
import { StoryResponseDto } from "./dto";
import { PaginatedResponse } from "../../common/dto/paginated-response.dto";
import { LightingService } from "../lighting/lighting.service";
// 暂时移除未使用的导入
// import { ServiceResult } from "../../common/types";

/**
 * 故事服务类
 * 负责故事的创建、管理、权限控制和内容安全检测
 */
@Injectable()
export class StoriesService {
  constructor(
    @InjectRepository(Story)
    private readonly storyRepository: Repository<Story>,
    @InjectRepository(Character)
    private readonly characterRepository: Repository<Character>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Theme)
    private readonly themeRepository: Repository<Theme>,
    @Optional()
    @Inject(forwardRef(() => LightingService))
    private readonly lightingService?: LightingService, // 可选依赖，避免循环依赖
  ) {}

  /**
   * 创建故事
   * @param authorId 作者ID
   * @param createData 创建数据
   * @returns 创建的故事
   */
  async createStory(
    authorId: string,
    createData: CreateStoryDto,
  ): Promise<Story> {
    // 验证作者存在
    const author = await this.userRepository.findOne({
      where: { id: authorId },
    });
    if (!author) {
      throw new NotFoundException("作者不存在");
    }

    // 验证主题存在（如果提供了主题ID）
    if (createData.themeId) {
      const theme = await this.themeRepository.findOne({
        where: { id: createData.themeId, isActive: true },
      });
      if (!theme) {
        throw new NotFoundException("主题不存在或已停用");
      }
    }

    // 内容安全检测
    const contentValidation = await this.validateStoryContent(
      createData.content || "",
    );
    if (!contentValidation.isValid) {
      throw new BadRequestException(
        `内容安全检测失败: ${contentValidation.violations.join(", ")}`,
      );
    }

    // 创建故事
    const { storyDate, ...restCreateData } = createData;
    const storyData: Partial<Story> = {
      ...restCreateData,
      userId: authorId,
      status: StoryStatus.DRAFT,
      aiSafetyScore: contentValidation.aiSafetyScore,
    };

    if (storyDate) {
      storyData.storyDate = new Date(storyDate);
    }

    const story = this.storyRepository.create(storyData);

    const savedStories = await this.storyRepository.save(story);
    const savedStory = Array.isArray(savedStories)
      ? savedStories[0]
      : savedStories;

    // 处理人物关联
    if (createData.characterIds && createData.characterIds.length > 0) {
      await this.associateCharactersWithStory(
        savedStory.id,
        createData.characterIds,
        authorId,
      );
    }

    return this.findById(savedStory.id);
  }

  /**
   * 更新故事
   * @param storyId 故事ID
   * @param updateData 更新数据
   * @param userId 当前用户ID
   * @returns 更新后的故事
   */
  async updateStory(
    storyId: string,
    updateData: UpdateStoryDto,
    userId: string,
  ): Promise<Story> {
    const story = await this.storyRepository.findOne({
      where: { id: storyId },
      relations: ["user"],
    });

    if (!story) {
      throw new NotFoundException("故事不存在");
    }

    // 权限验证：只有作者可以编辑
    if (story.userId !== userId) {
      throw new ForbiddenException("无权编辑此故事");
    }

    // 验证主题存在（如果提供了主题ID）
    if (updateData.themeId) {
      const theme = await this.themeRepository.findOne({
        where: { id: updateData.themeId, isActive: true },
      });
      if (!theme) {
        throw new NotFoundException("主题不存在或已停用");
      }
    }

    // 内容安全检测（如果更新了内容）
    if (updateData.content) {
      const contentValidation = await this.validateStoryContent(
        updateData.content,
      );
      if (!contentValidation.isValid) {
        throw new BadRequestException(
          `内容安全检测失败: ${contentValidation.violations.join(", ")}`,
        );
      }
      (updateData as UpdateStoryDto & { aiSafetyScore: number }).aiSafetyScore =
        contentValidation.aiSafetyScore;
    }

    // 创建更新载荷，处理日期类型转换
    const updatePayload: Partial<Story> = {};

    // 复制所有字段，除了storyDate
    Object.keys(updateData).forEach((key) => {
      if (key !== "storyDate") {
        (updatePayload as Record<string, unknown>)[key] = (
          updateData as Record<string, unknown>
        )[key];
      }
    });

    // 单独处理storyDate的类型转换
    if (updateData.storyDate) {
      updatePayload.storyDate = new Date(updateData.storyDate);
    }

    // 更新故事
    await this.storyRepository.update(storyId, updatePayload);

    // 处理人物关联更新
    if (updateData.characterIds !== undefined) {
      await this.updateStoryCharacters(
        storyId,
        updateData.characterIds,
        userId,
      );
    }

    return this.findById(storyId);
  }

  /**
   * 删除故事
   * @param storyId 故事ID
   * @param userId 当前用户ID
   */
  async deleteStory(storyId: string, userId: string): Promise<void> {
    const story = await this.storyRepository.findOne({
      where: { id: storyId },
      relations: ["user"],
    });

    if (!story) {
      throw new NotFoundException("故事不存在");
    }

    // 权限验证：只有作者可以删除
    if (story.userId !== userId) {
      throw new ForbiddenException("无权删除此故事");
    }

    await this.storyRepository.remove(story);
  }

  /**
   * 发布故事
   * @param storyId 故事ID
   * @param userId 当前用户ID
   * @returns 发布后的故事
   */
  async publishStory(storyId: string, userId: string): Promise<Story> {
    const story = await this.storyRepository.findOne({
      where: { id: storyId },
      relations: ["user", "characters"],
    });

    if (!story) {
      throw new NotFoundException("故事不存在");
    }

    // 权限验证：只有作者可以发布
    if (story.userId !== userId) {
      throw new ForbiddenException("无权发布此故事");
    }

    // 验证故事状态
    if (story.status === StoryStatus.PUBLISHED) {
      throw new BadRequestException("故事已发布");
    }

    // 内容安全检测
    if (story.content) {
      const contentValidation = await this.validateStoryContent(story.content);
      if (!contentValidation.isValid) {
        throw new BadRequestException(
          `内容安全检测失败: ${contentValidation.violations.join(", ")}`,
        );
      }
    }

    // 更新故事状态
    await this.storyRepository.update(storyId, {
      status: StoryStatus.PUBLISHED,
    });

    // 🔥 触发已点亮人物的新故事通知
    if (
      this.lightingService &&
      story.characters &&
      story.characters.length > 0
    ) {
      try {
        await this.lightingService.handleNewStoryPublished(storyId, userId);
      } catch (error) {
        // 通知失败不影响发布流程，只记录日志
        console.error("Failed to send new story notifications:", error);
      }
    }

    return this.findById(storyId);
  }

  /**
   * 根据ID查找故事
   * @param storyId 故事ID
   * @returns 故事详情
   */
  async findById(storyId: string): Promise<Story> {
    const story = await this.storyRepository.findOne({
      where: { id: storyId },
      relations: ["user", "theme", "characters"],
    });

    if (!story) {
      throw new NotFoundException("故事不存在");
    }

    return story;
  }

  /**
   * 验证故事访问权限
   * @param storyId 故事ID
   * @param userId 当前用户ID
   * @returns 是否有访问权限
   */
  async validateStoryAccess(storyId: string, userId: string): Promise<boolean> {
    const story = await this.storyRepository.findOne({
      where: { id: storyId },
      relations: ["user", "characters"],
    });

    if (!story) {
      throw new NotFoundException("故事不存在");
    }

    // 作者始终有访问权限
    if (story.userId === userId) {
      return true;
    }

    // 根据权限级别验证
    switch (story.permissionLevel) {
      case "private":
        return false;

      case "public":
        return true;

      case "friends":
        // 需要查询朋友关系
        return await this.isFriend(story.userId, userId);

      case "characters_only":
        // 需要查询是否点亮了故事中的人物
        return await this.hasLightedCharacterInStory(storyId, userId);

      default:
        return false;
    }
  }

  /**
   * 获取用户故事列表
   * @param userId 用户ID
   * @param query 查询参数
   * @returns 分页的故事列表
   */
  async getUserStories(
    userId: string,
    query: StoryQueryDto,
  ): Promise<PaginatedResponse<StoryResponseDto>> {
    const queryBuilder = this.createStoryQueryBuilder(query);

    queryBuilder.where("story.userId = :userId", { userId });

    return this.executeStoryQuery(queryBuilder, query);
  }

  /**
   * 获取公开故事列表
   * @param query 查询参数
   * @param currentUserId 当前用户ID
   * @returns 分页的故事列表
   */
  async getPublicStories(
    query: StoryQueryDto,
    _currentUserId?: string,
  ): Promise<PaginatedResponse<StoryResponseDto>> {
    const queryBuilder = this.createStoryQueryBuilder(query);

    queryBuilder.where("story.permissionLevel = :permissionLevel", {
      permissionLevel: "public",
    });
    queryBuilder.andWhere("story.status = :status", {
      status: StoryStatus.PUBLISHED,
    });

    return this.executeStoryQuery(queryBuilder, query);
  }

  /**
   * 获取可访问的故事列表
   * @param query 查询参数
   * @param currentUserId 当前用户ID
   * @returns 分页的故事列表
   */
  async getAccessibleStories(
    query: StoryQueryDto,
    currentUserId: string,
  ): Promise<PaginatedResponse<StoryResponseDto>> {
    const queryBuilder = this.createStoryQueryBuilder(query);

    // 构建权限查询条件
    queryBuilder.where("story.status = :status", {
      status: StoryStatus.PUBLISHED,
    });
    queryBuilder.andWhere(
      "(story.permissionLevel = :public OR story.userId = :currentUserId)",
      {
        public: "public",
        currentUserId,
      },
    );

    return this.executeStoryQuery(queryBuilder, query);
  }

  /**
   * 内容安全检测
   * @param content 故事内容
   * @returns 检测结果
   */
  async validateStoryContent(
    content: string,
  ): Promise<ContentValidationResult> {
    // 简单的内容安全检测实现
    // 实际项目中应集成专业的内容安全检测服务
    const violations: string[] = [];
    // const suggestions: string[] = []; // 暂时未使用

    // 敏感词检测
    const sensitiveWords = ["敏感词1", "敏感词2"]; // 实际应从配置或数据库获取
    for (const word of sensitiveWords) {
      if (content.includes(word)) {
        violations.push(`包含敏感词: ${word}`);
      }
    }

    // 长度检测
    if (content.length > 10000) {
      violations.push("内容过长");
    }

    // 计算安全评分
    const aiSafetyScore =
      violations.length === 0
        ? 0.95
        : Math.max(0.1, 0.95 - violations.length * 0.2);

    return {
      isValid: violations.length === 0,
      aiSafetyScore,
      violations,
      suggestions: violations.length > 0 ? ["请修改相关内容"] : [],
    };
  }

  /**
   * 增加故事浏览量
   * @param storyId 故事ID
   */
  async incrementViewCount(storyId: string): Promise<void> {
    await this.storyRepository.increment({ id: storyId }, "viewCount", 1);
  }

  /**
   * 增加故事点赞量
   * @param storyId 故事ID
   */
  async incrementLikeCount(storyId: string): Promise<void> {
    await this.storyRepository.increment({ id: storyId }, "likeCount", 1);
  }

  /**
   * 创建故事查询构建器
   * @param query 查询参数
   * @returns 查询构建器
   */
  private createStoryQueryBuilder(
    query: StoryQueryDto,
  ): SelectQueryBuilder<Story> {
    const queryBuilder = this.storyRepository.createQueryBuilder("story");

    // 关联查询
    queryBuilder.leftJoinAndSelect("story.user", "user");
    queryBuilder.leftJoinAndSelect("story.theme", "theme");
    queryBuilder.leftJoinAndSelect("story.characters", "characters");

    // 搜索条件
    if (query.search) {
      queryBuilder.andWhere(
        "(story.title ILIKE :search OR story.content ILIKE :search)",
        {
          search: `%${query.search}%`,
        },
      );
    }

    // 过滤条件
    if (query.authorId) {
      queryBuilder.andWhere("story.userId = :authorId", {
        authorId: query.authorId,
      });
    }

    if (query.themeId) {
      queryBuilder.andWhere("story.themeId = :themeId", {
        themeId: query.themeId,
      });
    }

    if (query.permissionLevel) {
      queryBuilder.andWhere("story.permissionLevel = :permissionLevel", {
        permissionLevel: query.permissionLevel,
      });
    }

    if (query.status) {
      queryBuilder.andWhere("story.status = :status", { status: query.status });
    }

    // 排序
    switch (query.sortBy) {
      case "newest":
        queryBuilder.orderBy("story.createdAt", "DESC");
        break;
      case "oldest":
        queryBuilder.orderBy("story.createdAt", "ASC");
        break;
      case "most_liked":
        queryBuilder.orderBy("story.likeCount", "DESC");
        break;
      case "most_viewed":
        queryBuilder.orderBy("story.viewCount", "DESC");
        break;
      default:
        queryBuilder.orderBy("story.createdAt", "DESC");
    }

    return queryBuilder;
  }

  /**
   * 执行故事查询
   * @param queryBuilder 查询构建器
   * @param query 查询参数
   * @returns 分页结果
   */
  private async executeStoryQuery(
    queryBuilder: SelectQueryBuilder<Story>,
    query: StoryQueryDto,
  ): Promise<PaginatedResponse<StoryResponseDto>> {
    const page = query.page || 1;
    const limit = query.limit || 10;
    const skip = (page - 1) * limit;

    const [stories, total] = await queryBuilder
      .skip(skip)
      .take(limit)
      .getManyAndCount();

    const items = stories.map((story) => new StoryResponseDto(story));

    return new PaginatedResponse(items, total, page, limit);
  }

  /**
   * 关联人物与故事
   * @param storyId 故事ID
   * @param characterIds 人物ID数组
   * @param userId 用户ID
   */
  private async associateCharactersWithStory(
    storyId: string,
    characterIds: string[],
    userId: string,
  ): Promise<void> {
    // 验证人物存在且属于当前用户
    const characters = await this.characterRepository.find({
      where: {
        id: In(characterIds),
        creatorId: userId,
      },
    });

    if (characters.length !== characterIds.length) {
      throw new BadRequestException("部分人物不存在或无权访问");
    }

    // 建立关联
    await this.storyRepository
      .createQueryBuilder()
      .relation(Story, "characters")
      .of(storyId)
      .add(characterIds);
  }

  /**
   * 更新故事人物关联
   * @param storyId 故事ID
   * @param characterIds 新的人物ID数组
   * @param userId 用户ID
   */
  private async updateStoryCharacters(
    storyId: string,
    characterIds: string[],
    userId: string,
  ): Promise<void> {
    // 先移除现有关联
    await this.storyRepository
      .createQueryBuilder()
      .relation(Story, "characters")
      .of(storyId)
      .remove(await this.getStoryCharacterIds(storyId));

    // 添加新关联
    if (characterIds.length > 0) {
      await this.associateCharactersWithStory(storyId, characterIds, userId);
    }
  }

  /**
   * 获取故事关联的人物ID
   * @param storyId 故事ID
   * @returns 人物ID数组
   */
  private async getStoryCharacterIds(storyId: string): Promise<string[]> {
    const result = await this.storyRepository
      .createQueryBuilder("story")
      .leftJoin("story.characters", "character")
      .where("story.id = :storyId", { storyId })
      .select("character.id")
      .getRawMany();

    return result.map((item) => item.character_id);
  }

  /**
   * 检查是否为朋友关系
   * @param userId1 用户1ID
   * @param userId2 用户2ID
   * @returns 是否为朋友
   */
  private async isFriend(_userId1: string, _userId2: string): Promise<boolean> {
    // 查询用户关系表，判断是否存在好友关系
    // 由于UserRelationship表尚未实现，这里先返回false
    // TODO: 实现UserRelationship表后，查询双向好友关系
    // const relationship = await this.userRelationshipRepository.findOne({
    //   where: [
    //     { userId: userId1, friendId: userId2, status: 'accepted' },
    //     { userId: userId2, friendId: userId1, status: 'accepted' }
    //   ]
    // });
    // return !!relationship;

    // 暂时返回false，等待用户关系功能实现
    return false;
  }

  /**
   * 检查用户是否点亮了故事中的人物
   * @param storyId 故事ID
   * @param userId 用户ID
   * @returns 是否点亮了人物
   */
  private async hasLightedCharacterInStory(
    storyId: string,
    userId: string,
  ): Promise<boolean> {
    // 获取故事中的所有人物
    const story = await this.storyRepository.findOne({
      where: { id: storyId },
      relations: ["characters"],
    });

    if (!story?.characters || story.characters.length === 0) {
      return false;
    }

    // 获取人物ID列表
    const characterIds = story.characters.map((char) => char.id);

    // 查询用户是否点亮了这些人物中的任意一个
    const lightingCount = await this.characterRepository
      .createQueryBuilder("character")
      .innerJoin("character_lighting", "cl", "cl.character_id = character.id")
      .where("character.id IN (:...characterIds)", { characterIds })
      .andWhere("cl.lighter_id = :userId", { userId })
      .andWhere("cl.is_active = true")
      .getCount();

    return lightingCount > 0;
  }
}
