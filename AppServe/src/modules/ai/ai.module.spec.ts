/**
 * AIModule 单元测试
 * 测试AI模块的依赖注入和模块配置
 */

import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import { CACHE_MANAGER } from "@nestjs/cache-manager";
import { ConfigService } from "@nestjs/config";
import { AiController } from "./ai.controller";
import { AiService } from "./ai.service";
import { AiUsageLog } from "./entities/ai-usage-log.entity";
import { User } from "../users/entities/user.entity";

// 公共服务Mock
import { EnhancedRedisService } from "../../common/services/enhanced-redis.service";
import { MockAIProvider } from "./providers/mock-ai.provider";

// 认证相关Mock
import { AuthService } from "../auth/auth.service";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";

describe("AIModule - 企业级单元测试", () => {
  let module: TestingModule;

  const mockRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  };

  const mockCacheManager = {
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
    reset: jest.fn(),
  };

  // Mock 配置服务
  const mockConfigService = {
    get: jest.fn((key: string) => {
      const config: Record<string, string> = {
        REDIS_HOST: "localhost",
        REDIS_PORT: "6379",
        AI_API_KEY: "test-api-key",
        AI_API_URL: "https://test-api.com",
      };
      return config[key];
    }),
  };

  // Mock EnhancedRedisService
  const mockEnhancedRedisService = {
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
    exists: jest.fn(),
    increment: jest.fn(),
    decrement: jest.fn(),
    expire: jest.fn(),
    getClient: jest.fn().mockReturnValue({}),
    pipeline: jest.fn().mockReturnValue({
      exec: jest.fn().mockResolvedValue([]),
    }),
  };

  // Mock MockAIProvider
  const mockAIProvider = {
    generateTitle: jest.fn().mockResolvedValue("AI生成的标题"),
    generateContent: jest.fn().mockResolvedValue("AI生成的内容"),
    optimizeStory: jest.fn().mockResolvedValue("优化后的故事"),
    generateSummary: jest.fn().mockResolvedValue("故事摘要"),
    analyzeEmotion: jest.fn().mockResolvedValue("情感分析结果"),
  };

  // Mock AuthService
  const mockAuthService = {
    validateUser: jest.fn(),
    login: jest.fn(),
    register: jest.fn(),
    refreshToken: jest.fn(),
  };

  // Mock JwtAuthGuard
  const mockJwtAuthGuard = {
    canActivate: jest.fn().mockReturnValue(true),
  };

  beforeEach(async () => {
    module = await Test.createTestingModule({
      controllers: [AiController],
      providers: [
        AiService,
        // Mock 提供者
        { provide: getRepositoryToken(AiUsageLog), useValue: mockRepository },
        { provide: getRepositoryToken(User), useValue: mockRepository },
        { provide: CACHE_MANAGER, useValue: mockCacheManager },
        { provide: ConfigService, useValue: mockConfigService },
        { provide: EnhancedRedisService, useValue: mockEnhancedRedisService },
        { provide: MockAIProvider, useValue: mockAIProvider },
        { provide: AuthService, useValue: mockAuthService },
        { provide: JwtAuthGuard, useValue: mockJwtAuthGuard },
      ],
    }).compile();
  });

  afterEach(async () => {
    if (module) {
      await module.close();
    }
  });

  it("应该成功创建模块实例", () => {
    expect(module).toBeDefined();
  });

  describe("服务提供者", () => {
    it("should provide AiService", () => {
      const aiService = module.get<AiService>(AiService);
      expect(aiService).toBeDefined();
      expect(aiService).toBeInstanceOf(AiService);
    });
  });

  describe("控制器", () => {
    it("should provide AiController", () => {
      const aiController = module.get<AiController>(AiController);
      expect(aiController).toBeDefined();
      expect(aiController).toBeInstanceOf(AiController);
    });
  });

  describe("依赖注入", () => {
    it("should inject AiUsageLog repository", () => {
      const aiUsageLogRepository = module.get(getRepositoryToken(AiUsageLog));
      expect(aiUsageLogRepository).toBeDefined();
      expect(aiUsageLogRepository).toBe(mockRepository);
    });

    it("should inject CACHE_MANAGER", () => {
      const cacheManager = module.get(CACHE_MANAGER);
      expect(cacheManager).toBeDefined();
      expect(cacheManager).toBe(mockCacheManager);
    });
  });

  describe("模块配置", () => {
    it("should export AiService for other modules", () => {
      const aiService = module.get<AiService>(AiService);
      expect(aiService).toBeDefined();
    });
  });
});
