/**
 * API响应DTO
 * 用于API返回数据的统一格式
 */
export class ApiResponse<T = unknown> {
  success: boolean;
  message: string;
  data: T;
  statusCode: number;
  timestamp: string;

  constructor(success: boolean, message: string, data: T, statusCode: number) {
    this.success = success;
    this.message = message;
    this.data = data;
    this.statusCode = statusCode;
    this.timestamp = new Date().toISOString();
  }

  /**
   * 创建成功响应
   */
  static success<T>(
    data: T,
    message = "操作成功",
    statusCode = 200,
  ): ApiResponse<T> {
    return new ApiResponse(true, message, data, statusCode);
  }

  /**
   * 创建错误响应
   */
  static error<T = null>(
    message: string,
    statusCode = 500,
    data: T = null as T,
  ): ApiResponse<T> {
    return new ApiResponse(false, message, data, statusCode);
  }

  /**
   * 创建分页响应
   */
  static paginated<T>(
    data: T[],
    page: number,
    limit: number,
    total: number,
    message = "查询成功",
  ): ApiResponse<{
    data: T[];
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  }> {
    return new ApiResponse(
      true,
      message,
      {
        data,
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
      200,
    );
  }
}

// 类型别名用于向后兼容
export type ApiResponseDto<T = unknown> = ApiResponse<T>;
