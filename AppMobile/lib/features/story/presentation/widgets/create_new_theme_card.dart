import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../design_system/tokens/ygs_colors.dart';
import '../../../../design_system/tokens/ygs_typography.dart';

/// 创建新主题卡片 - 与主题卡片相同尺寸的创建入口
/// 设计风格：简洁现代，突出创建功能，符合YGS设计系统
class CreateNewThemeCard extends StatefulWidget {
  final VoidCallback? onTap;

  const CreateNewThemeCard({
    super.key,
    this.onTap,
  });

  @override
  State<CreateNewThemeCard> createState() => _CreateNewThemeCardState();
}

class _CreateNewThemeCardState extends State<CreateNewThemeCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    setState(() {
      _isPressed = true;
    });
    _animationController.forward();
  }

  void _handleTapUp(TapUpDetails details) {
    setState(() {
      _isPressed = false;
    });
    _animationController.reverse();
  }

  void _handleTapCancel() {
    setState(() {
      _isPressed = false;
    });
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      onTapDown: _handleTapDown,
      onTapUp: _handleTapUp,
      onTapCancel: _handleTapCancel,
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              width: 345.w,
              height: 240.h,
              decoration: BoxDecoration(
                // 浅色渐变背景
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    YgsColors.backgroundSecondary,
                    YgsColors.backgroundCard,
                  ],
                ),
                borderRadius: BorderRadius.circular(16.r),
                // 虚线边框
                border: Border.all(
                  color: YgsColors.borderPrimary,
                  width: 2.w,
                  strokeAlign: BorderSide.strokeAlignInside,
                ),
              ),
              child: CustomPaint(
                painter: DashedBorderPainter(
                  color: YgsColors.borderPrimary,
                  strokeWidth: 2.w,
                  dashLength: 8.w,
                  dashSpace: 6.w,
                  borderRadius: 16.r,
                ),
                child: Container(
                  padding: EdgeInsets.all(24.w),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // 加号图标
                      Container(
                        width: 80.w,
                        height: 80.w,
                        decoration: BoxDecoration(
                          color: YgsColors.backgroundCard,
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: YgsColors.borderPrimary,
                            width: 1.w,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: YgsColors.borderPrimary.withValues(alpha: 0.3),
                              blurRadius: 8.r,
                              offset: Offset(0, 4.h),
                            ),
                          ],
                        ),
                        child: Icon(
                          Icons.add,
                          size: 40.w,
                          color: YgsColors.textSecondary,
                        ),
                      ),
                      
                      SizedBox(height: 24.h),
                      
                      // 创建新主题文字
                      Text(
                        '创建新主题',
                        style: YgsTypography.headlineSmall.copyWith(
                          fontSize: 28.sp,
                          fontWeight: FontWeight.w500,
                          color: YgsColors.textSecondary,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      
                      SizedBox(height: 8.h),
                      
                      // 提示文字
                      Text(
                        '点击开始创建',
                        style: YgsTypography.bodySmall.copyWith(
                          fontSize: 16.sp,
                          color: YgsColors.textTertiary,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

/// 虚线边框绘制器
class DashedBorderPainter extends CustomPainter {
  final Color color;
  final double strokeWidth;
  final double dashLength;
  final double dashSpace;
  final double borderRadius;

  DashedBorderPainter({
    required this.color,
    required this.strokeWidth,
    required this.dashLength,
    required this.dashSpace,
    required this.borderRadius,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    final path = Path()
      ..addRRect(RRect.fromRectAndRadius(
        Rect.fromLTWH(strokeWidth / 2, strokeWidth / 2, 
                     size.width - strokeWidth, size.height - strokeWidth),
        Radius.circular(borderRadius),
      ));

    _drawDashedPath(canvas, path, paint);
  }

  void _drawDashedPath(Canvas canvas, Path path, Paint paint) {
    final pathMetrics = path.computeMetrics();
    for (final pathMetric in pathMetrics) {
      double distance = 0.0;
      while (distance < pathMetric.length) {
        final nextDistance = distance + dashLength;
        final extractPath = pathMetric.extractPath(
          distance,
          nextDistance > pathMetric.length ? pathMetric.length : nextDistance,
        );
        canvas.drawPath(extractPath, paint);
        distance = nextDistance + dashSpace;
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
