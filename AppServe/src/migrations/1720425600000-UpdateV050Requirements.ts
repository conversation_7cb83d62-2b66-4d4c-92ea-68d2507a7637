import type { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateV050Requirements1720425600000 implements MigrationInterface {
  name = "UpdateV050Requirements1720425600000";

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 1. 为Users表添加v1.0.0必需字段
    await queryRunner.query(`
      ALTER TABLE "users" 
      ADD COLUMN "user_number" varchar(6) UNIQUE,
      ADD COLUMN "nickname" varchar(50) NOT NULL DEFAULT '',
      ADD COLUMN "bio" text,
      ADD COLUMN "ai_quota_remaining" integer DEFAULT 3,
      ADD COLUMN "ai_quota_reset_date" date DEFAULT CURRENT_DATE,
      ADD COLUMN "profile_display_settings" jsonb DEFAULT '{"showBirthday":false,"showBio":true,"showStatistics":true,"showCharacters":true,"showTimeline":true}',
      ADD COLUMN "status" varchar(20) DEFAULT 'active'
    `);

    // 添加字段注释
    await queryRunner.query(`
      COMMENT ON COLUMN "users"."user_number" IS '6位有故事号';
      COMMENT ON COLUMN "users"."nickname" IS '用户昵称';
      COMMENT ON COLUMN "users"."bio" IS '个人简介';
      COMMENT ON COLUMN "users"."ai_quota_remaining" IS 'AI每日剩余配额';
      COMMENT ON COLUMN "users"."ai_quota_reset_date" IS 'AI配额重置日期';
      COMMENT ON COLUMN "users"."profile_display_settings" IS '个人主页展示设置';
      COMMENT ON COLUMN "users"."status" IS '用户状态';
    `);

    // 2. 创建刷新令牌表
    await queryRunner.query(`
      CREATE TABLE "refresh_tokens" (
        "id" uuid NOT NULL DEFAULT gen_random_uuid(),
        "user_id" uuid REFERENCES users(id) ON DELETE CASCADE,
        "token_hash" varchar(255) NOT NULL,
        "device_fingerprint" varchar(255),
        "device_info" jsonb,
        "expires_at" timestamp NOT NULL,
        "created_at" timestamp DEFAULT CURRENT_TIMESTAMP,
        "last_used_at" timestamp DEFAULT CURRENT_TIMESTAMP,
        "is_active" boolean DEFAULT true,
        CONSTRAINT "PK_refresh_tokens_id" PRIMARY KEY ("id")
      );
    `);

    // 3. 创建主题表
    await queryRunner.query(`
      CREATE TABLE "themes" (
        "id" uuid NOT NULL DEFAULT gen_random_uuid(),
        "name" varchar(50) NOT NULL,
        "description" text,
        "icon" varchar(10),
        "color" varchar(7),
        "sort_order" integer DEFAULT 0,
        "is_active" boolean DEFAULT true,
        "created_at" timestamp DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT "PK_themes_id" PRIMARY KEY ("id")
      );
    `);

    // 插入默认主题数据
    await queryRunner.query(`
      INSERT INTO "themes" ("name", "description", "icon", "color", "sort_order") VALUES
      ('童年回忆', '记录美好的童年时光', '🧸', '#FF6B6B', 1),
      ('青春岁月', '青春年华的点点滴滴', '🌟', '#4ECDC4', 2),
      ('家庭生活', '温馨的家庭故事', '🏠', '#45B7D1', 3),
      ('友情时光', '珍贵的友谊时刻', '👥', '#96CEB4', 4),
      ('爱情故事', '浪漫的爱情经历', '💕', '#FFEAA7', 5),
      ('工作经历', '职场生涯的记录', '💼', '#DDA0DD', 6),
      ('旅行见闻', '旅途中的美好回忆', '✈️', '#FFB347', 7),
      ('成长感悟', '人生成长的感悟', '🌱', '#98D8C8', 8);
    `);

    // 4. 为Stories表添加v0.5.0字段
    await queryRunner.query(`
      ALTER TABLE "stories"
      ADD COLUMN "theme_id" uuid REFERENCES themes(id),
      ADD COLUMN "story_date" date,
      ADD COLUMN "location" varchar(255),
      ADD COLUMN "images" jsonb,
      ADD COLUMN "permission_level" varchar(20) DEFAULT 'public',
      ADD COLUMN "allow_comments" boolean DEFAULT true,
      ADD COLUMN "allow_likes" boolean DEFAULT true,
      ADD COLUMN "allow_sharing" boolean DEFAULT true,
      ADD COLUMN "ai_safety_score" decimal(3,2),
      ADD COLUMN "comment_count" integer DEFAULT 0
    `);

    // 删除旧的privacy_level字段，使用新的permission_level
    await queryRunner.query(
      `ALTER TABLE "stories" DROP COLUMN "privacy_level"`,
    );

    // 添加字段注释
    await queryRunner.query(`
      COMMENT ON COLUMN "stories"."theme_id" IS '故事主题ID';
      COMMENT ON COLUMN "stories"."story_date" IS '故事发生日期';
      COMMENT ON COLUMN "stories"."location" IS '故事发生地点';
      COMMENT ON COLUMN "stories"."images" IS '故事图片数组';
      COMMENT ON COLUMN "stories"."permission_level" IS '权限级别：private/friends/public/characters_only';
      COMMENT ON COLUMN "stories"."allow_comments" IS '是否允许评论';
      COMMENT ON COLUMN "stories"."allow_likes" IS '是否允许点赞';
      COMMENT ON COLUMN "stories"."allow_sharing" IS '是否允许分享';
      COMMENT ON COLUMN "stories"."ai_safety_score" IS 'AI安全检测评分';
      COMMENT ON COLUMN "stories"."comment_count" IS '评论数量';
    `);

    // 5. 为Characters表添加v0.5.0字段
    await queryRunner.query(`
      ALTER TABLE "characters"
      ADD COLUMN "gender" varchar(10),
      ADD COLUMN "relationship" varchar(50),
      ADD COLUMN "custom_relationship" varchar(100),
      ADD COLUMN "is_lighted" boolean DEFAULT false,
      ADD COLUMN "lighting_count" integer DEFAULT 0,
      ADD COLUMN "updated_at" timestamp DEFAULT CURRENT_TIMESTAMP
    `);

    // 删除不需要的字段
    await queryRunner.query(`
      ALTER TABLE "characters" 
      DROP COLUMN IF EXISTS "personality_traits",
      DROP COLUMN IF EXISTS "physical_description",
      DROP COLUMN IF EXISTS "relation_type"
    `);

    // 添加同名唯一约束
    await queryRunner.query(`
      ALTER TABLE "characters" 
      ADD CONSTRAINT "UQ_characters_creator_name" UNIQUE ("creator_id", "name")
    `);

    // 添加字段注释
    await queryRunner.query(`
      COMMENT ON COLUMN "characters"."gender" IS '性别';
      COMMENT ON COLUMN "characters"."relationship" IS '关系类型';
      COMMENT ON COLUMN "characters"."custom_relationship" IS '自定义关系';
      COMMENT ON COLUMN "characters"."is_lighted" IS '是否已点亮';
      COMMENT ON COLUMN "characters"."lighting_count" IS '被点亮次数';
      COMMENT ON COLUMN "characters"."updated_at" IS '更新时间';
    `);

    // 6. 创建点亮申请表 (核心功能)
    await queryRunner.query(`
      CREATE TABLE "light_requests" (
        "id" uuid NOT NULL DEFAULT gen_random_uuid(),
        "story_id" uuid REFERENCES stories(id) ON DELETE CASCADE,
        "character_id" uuid REFERENCES characters(id) ON DELETE CASCADE,
        "requester_id" uuid REFERENCES users(id) ON DELETE CASCADE,
        "story_author_id" uuid REFERENCES users(id) ON DELETE CASCADE,
        "phone_verification" varchar(20) NOT NULL,
        "message" text,
        "status" varchar(20) DEFAULT 'pending',
        "expires_at" timestamp NOT NULL,
        "created_at" timestamp DEFAULT CURRENT_TIMESTAMP,
        "processed_at" timestamp,
        CONSTRAINT "PK_light_requests_id" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_light_requests_story_character_requester" UNIQUE ("story_id", "character_id", "requester_id")
      );
    `);

    // 添加字段注释
    await queryRunner.query(`
      COMMENT ON COLUMN "light_requests"."phone_verification" IS '脱敏手机号验证';
      COMMENT ON COLUMN "light_requests"."message" IS '申请留言';
      COMMENT ON COLUMN "light_requests"."status" IS '状态：pending/confirmed/rejected/expired';
      COMMENT ON COLUMN "light_requests"."expires_at" IS '申请过期时间';
      COMMENT ON COLUMN "light_requests"."processed_at" IS '处理时间';
    `);

    // 7. 更新character_lightings表结构
    await queryRunner.query(`
      ALTER TABLE "character_lightings"
      ADD COLUMN "story_id" uuid REFERENCES stories(id) ON DELETE CASCADE,
      ADD COLUMN "request_id" uuid REFERENCES light_requests(id),
      DROP COLUMN IF EXISTS "lighting_message"
    `);

    // 添加唯一约束确保一个人物在一个故事中只能被一个用户点亮
    await queryRunner.query(`
      ALTER TABLE "character_lightings"
      ADD CONSTRAINT "UQ_character_lightings_character_story_lighter" UNIQUE ("character_id", "story_id", "lighter_user_id")
    `);

    // 8. 创建AI使用记录表
    await queryRunner.query(`
      CREATE TABLE "ai_usage_logs" (
        "id" uuid NOT NULL DEFAULT gen_random_uuid(),
        "user_id" uuid REFERENCES users(id) ON DELETE CASCADE,
        "feature_type" varchar(30) NOT NULL,
        "prompt_tokens" integer,
        "completion_tokens" integer,
        "total_tokens" integer,
        "cost_estimate" decimal(10,4),
        "success" boolean DEFAULT true,
        "created_at" timestamp DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT "PK_ai_usage_logs_id" PRIMARY KEY ("id")
      );
    `);

    // 9. 创建重要索引
    await queryRunner.query(
      `CREATE INDEX "idx_users_user_number" ON "users" ("user_number")`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_users_phone" ON "users" ("phone")`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_users_email" ON "users" ("email")`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_stories_theme_permission" ON "stories" ("theme_id", "permission_level")`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_stories_author_status_date" ON "stories" ("user_id", "status", "story_date" DESC)`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_light_requests_story_status" ON "light_requests" ("story_id", "status")`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_light_requests_requester_status" ON "light_requests" ("requester_id", "status")`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_light_requests_author_pending" ON "light_requests" ("story_author_id", "status") WHERE status = 'pending'`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_character_lightings_story_character" ON "character_lightings" ("story_id", "character_id")`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_ai_usage_user_date" ON "ai_usage_logs" ("user_id", "created_at" DESC)`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 删除索引
    await queryRunner.query(`DROP INDEX IF EXISTS "idx_ai_usage_user_date"`);
    await queryRunner.query(
      `DROP INDEX IF EXISTS "idx_character_lightings_story_character"`,
    );
    await queryRunner.query(
      `DROP INDEX IF EXISTS "idx_light_requests_author_pending"`,
    );
    await queryRunner.query(
      `DROP INDEX IF EXISTS "idx_light_requests_requester_status"`,
    );
    await queryRunner.query(
      `DROP INDEX IF EXISTS "idx_light_requests_story_status"`,
    );
    await queryRunner.query(
      `DROP INDEX IF EXISTS "idx_stories_author_status_date"`,
    );
    await queryRunner.query(
      `DROP INDEX IF EXISTS "idx_stories_theme_permission"`,
    );
    await queryRunner.query(`DROP INDEX IF EXISTS "idx_users_email"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "idx_users_phone"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "idx_users_user_number"`);

    // 删除表
    await queryRunner.query(`DROP TABLE IF EXISTS "ai_usage_logs"`);
    await queryRunner.query(`DROP TABLE IF EXISTS "light_requests"`);
    await queryRunner.query(`DROP TABLE IF EXISTS "themes"`);
    await queryRunner.query(`DROP TABLE IF EXISTS "refresh_tokens"`);

    // 还原表结构
    await queryRunner.query(`
      ALTER TABLE "users" 
      DROP COLUMN IF EXISTS "status",
      DROP COLUMN IF EXISTS "profile_display_settings",
      DROP COLUMN IF EXISTS "ai_quota_reset_date",
      DROP COLUMN IF EXISTS "ai_quota_remaining",
      DROP COLUMN IF EXISTS "bio",
      DROP COLUMN IF EXISTS "nickname",
      DROP COLUMN IF EXISTS "user_number"
    `);

    await queryRunner.query(`
      ALTER TABLE "stories"
      ADD COLUMN "privacy_level" integer NOT NULL DEFAULT '1',
      DROP COLUMN IF EXISTS "comment_count",
      DROP COLUMN IF EXISTS "ai_safety_score",
      DROP COLUMN IF EXISTS "allow_sharing",
      DROP COLUMN IF EXISTS "allow_likes",
      DROP COLUMN IF EXISTS "allow_comments",
      DROP COLUMN IF EXISTS "permission_level",
      DROP COLUMN IF EXISTS "images",
      DROP COLUMN IF EXISTS "location",
      DROP COLUMN IF EXISTS "story_date",
      DROP COLUMN IF EXISTS "theme_id"
    `);

    await queryRunner.query(`
      ALTER TABLE "characters"
      DROP CONSTRAINT IF EXISTS "UQ_characters_creator_name",
      DROP COLUMN IF EXISTS "updated_at",
      DROP COLUMN IF EXISTS "lighting_count",
      DROP COLUMN IF EXISTS "is_lighted",
      DROP COLUMN IF EXISTS "custom_relationship",
      DROP COLUMN IF EXISTS "relationship",
      DROP COLUMN IF EXISTS "gender"
    `);

    await queryRunner.query(`
      ALTER TABLE "character_lightings"
      DROP CONSTRAINT IF EXISTS "UQ_character_lightings_character_story_lighter",
      DROP COLUMN IF EXISTS "request_id",
      DROP COLUMN IF EXISTS "story_id"
    `);
  }
}
