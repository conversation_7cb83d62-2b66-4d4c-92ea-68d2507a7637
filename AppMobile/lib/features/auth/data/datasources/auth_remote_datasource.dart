import 'package:dio/dio.dart';
import '../../../../core/constants/api_constants.dart';
import '../../../../core/network/api_result.dart';
import '../models/user_model.dart';
import '../models/auth_token_model.dart';
import '../models/login_request.dart';

/// 认证远程数据源接口
abstract class AuthRemoteDataSource {
  Future<void> sendSmsCode(String phone);
  Future<(UserModel, AuthTokenModel)> loginWithPhone(String phone, String code);
  Future<(UserModel, AuthTokenModel)> loginWithEmail(String email, String password);
  Future<AuthTokenModel> refreshToken(String refreshToken);
  Future<void> logout();
  Future<UserModel> getUserProfile();
  Future<UserModel> updateUserProfile({
    String? nickname,
    String? bio,
    String? avatar,
  });
  Future<void> changePassword(String oldPassword, String newPassword);
  Future<UserModel> bindPhone(String phone, String code);
  Future<UserModel> bindEmail(String email, String password);
}

/// 认证远程数据源实现
class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  final Dio dio;

  AuthRemoteDataSourceImpl({required this.dio});

  @override
  Future<void> sendSmsCode(String phone) async {
    await dio.post(
      ApiConstants.sendSmsCode,
      data: {'phone': phone},
    );
  }

  @override
  Future<(UserModel, AuthTokenModel)> loginWithPhone(
    String phone,
    String code,
  ) async {
    final response = await dio.post(
      ApiConstants.loginPhone,
      data: {
        'phone': phone,
        'code': code,
      },
    );

    final result = ApiResult<Map<String, dynamic>>.fromJson(
      response.data,
      dataParser: (json) => json as Map<String, dynamic>,
    );

    final data = result.getDataOrThrow();
    final user = UserModel.fromJson(data['user']);
    final token = AuthTokenModel.fromJson(data['token']);

    return (user, token);
  }

  @override
  Future<(UserModel, AuthTokenModel)> loginWithEmail(
    String email,
    String password,
  ) async {
    final response = await dio.post(
      ApiConstants.loginEmail,
      data: {
        'email': email,
        'password': password,
      },
    );

    final result = ApiResult<Map<String, dynamic>>.fromJson(
      response.data,
      dataParser: (json) => json as Map<String, dynamic>,
    );

    final data = result.getDataOrThrow();
    final user = UserModel.fromJson(data['user']);
    final token = AuthTokenModel.fromJson(data['token']);

    return (user, token);
  }

  @override
  Future<AuthTokenModel> refreshToken(String refreshToken) async {
    final response = await dio.post(
      ApiConstants.refreshToken,
      data: {'refreshToken': refreshToken},
    );

    final result = ApiResult<Map<String, dynamic>>.fromJson(
      response.data,
      dataParser: (json) => json as Map<String, dynamic>,
    );

    final data = result.getDataOrThrow();
    return AuthTokenModel.fromJson(data['token']);
  }

  @override
  Future<void> logout() async {
    await dio.post(ApiConstants.logout);
  }

  @override
  Future<UserModel> getUserProfile() async {
    final response = await dio.get(ApiConstants.profile);

    final result = ApiResult<UserModel>.fromJson(
      response.data,
      dataParser: (json) => UserModel.fromJson(json as Map<String, dynamic>),
    );

    return result.getDataOrThrow();
  }

  @override
  Future<UserModel> updateUserProfile({
    String? nickname,
    String? bio,
    String? avatar,
  }) async {
    final data = <String, dynamic>{};
    if (nickname != null) data['nickname'] = nickname;
    if (bio != null) data['bio'] = bio;
    if (avatar != null) data['avatar'] = avatar;

    final response = await dio.put(
      ApiConstants.updateProfile,
      data: data,
    );

    final result = ApiResult<UserModel>.fromJson(
      response.data,
      dataParser: (json) => UserModel.fromJson(json as Map<String, dynamic>),
    );

    return result.getDataOrThrow();
  }

  @override
  Future<void> changePassword(String oldPassword, String newPassword) async {
    await dio.put(
      ApiConstants.changePassword,
      data: {
        'oldPassword': oldPassword,
        'newPassword': newPassword,
      },
    );
  }

  @override
  Future<UserModel> bindPhone(String phone, String code) async {
    final response = await dio.post(
      ApiConstants.bindPhone,
      data: {
        'phone': phone,
        'code': code,
      },
    );

    final result = ApiResult<UserModel>.fromJson(
      response.data,
      dataParser: (json) => UserModel.fromJson(json as Map<String, dynamic>),
    );

    return result.getDataOrThrow();
  }

  @override
  Future<UserModel> bindEmail(String email, String password) async {
    final response = await dio.post(
      '/users/email/bind',
      data: {
        'email': email,
        'password': password,
      },
    );

    final result = ApiResult<UserModel>.fromJson(
      response.data,
      dataParser: (json) => UserModel.fromJson(json as Map<String, dynamic>),
    );

    return result.getDataOrThrow();
  }
}