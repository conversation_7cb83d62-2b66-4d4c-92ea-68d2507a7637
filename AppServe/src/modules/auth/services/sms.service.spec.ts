import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { ConfigService } from "@nestjs/config";
import { BadRequestException } from "@nestjs/common";
import { SmsService } from "./sms.service";
import { EnhancedRedisService } from "../../../common/services/enhanced-redis.service";
import { DeviceFingerprintService } from "../../../common/services/device-fingerprint.service";
import { VerificationSecurityService } from "../../../common/services/verification-security.service";
import { AliyunSmsService } from "./aliyun-sms.service";
import type { Request } from "express";

// Mock Redis客户端类型
interface MockRedisClient {
  get: jest.Mock;
  set: jest.Mock;
  del: jest.Mock;
  pipeline: jest.Mock;
  sismember: jest.Mock;
  incr: jest.Mock;
  expire: jest.Mock;
  lpush: jest.Mock;
  ltrim: jest.Mock;
  lrange: jest.Mock;
}

// Mock配置类型
interface MockConfig {
  [key: string]: unknown;
}

describe("SmsService", () => {
  let service: SmsService;
  let configService: ConfigService;
  let mockEnhancedRedisService: { getClient: jest.Mock };
  let deviceFingerprintService: DeviceFingerprintService;
  let verificationSecurityService: VerificationSecurityService;
  let aliyunSmsService: AliyunSmsService;
  let mockRedisClient: MockRedisClient;

  // Mock数据
  const mockPhoneNumber = "13800138000";
  const mockCode = "1234";
  const mockDeviceInfo = {
    ip: "***********",
    fingerprint: "test-fingerprint",
    userAgent: "Mozilla/5.0...",
    platform: "desktop",
    timestamp: Date.now(),
  };

  const mockRequest = {
    ip: "***********",
    headers: {
      "user-agent": "Mozilla/5.0...",
    },
    connection: {
      remoteAddress: "***********",
    },
  } as Request;

  beforeEach(async () => {
    // 创建Redis客户端mock
    mockRedisClient = {
      get: jest.fn(),
      set: jest.fn(),
      del: jest.fn(),
      pipeline: jest.fn(),
      sismember: jest.fn(),
      incr: jest.fn(),
      expire: jest.fn(),
      lpush: jest.fn(),
      ltrim: jest.fn(),
      lrange: jest.fn(),
    };

    // 创建pipeline mock
    const mockPipeline = {
      get: jest.fn().mockReturnThis(),
      set: jest.fn().mockReturnThis(),
      incr: jest.fn().mockReturnThis(),
      expire: jest.fn().mockReturnThis(),
      del: jest.fn().mockReturnThis(),
      lpush: jest.fn().mockReturnThis(),
      ltrim: jest.fn().mockReturnThis(),
      lrange: jest.fn().mockReturnThis(),
      exec: jest.fn(),
    };

    mockRedisClient.pipeline.mockReturnValue(mockPipeline);

    // 创建服务mocks
    const mockConfigService = {
      get: jest.fn((key: string) => {
        const config: MockConfig = {
          NODE_ENV: "test",
          SMS_MOCK_CODE: "123456",
        };
        return config[key];
      }),
    };

    const mockEnhancedRedisService = {
      getClient: jest.fn().mockReturnValue(mockRedisClient),
    };

    const mockDeviceFingerprintService = {
      generateDeviceFingerprint: jest.fn(),
      recordSmsDevice: jest.fn(),
      validateSmsDevice: jest.fn(),
    };

    const mockVerificationSecurityService = {
      checkVerificationAttempt: jest.fn(),
      recordVerificationAttempt: jest.fn(),
    };

    const mockAliyunSmsService = {
      sendVerificationCode: jest.fn(),
      getVipTestPhones: jest.fn(),
      getCurrentSmsMode: jest.fn(),
      addVipTestPhone: jest.fn(),
      removeVipTestPhone: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SmsService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: EnhancedRedisService,
          useValue: mockEnhancedRedisService,
        },
        {
          provide: DeviceFingerprintService,
          useValue: mockDeviceFingerprintService,
        },
        {
          provide: VerificationSecurityService,
          useValue: mockVerificationSecurityService,
        },
        {
          provide: AliyunSmsService,
          useValue: mockAliyunSmsService,
        },
      ],
    }).compile();

    service = module.get<SmsService>(SmsService);
    configService = module.get<ConfigService>(ConfigService);
    // mockEnhancedRedisService 保持使用Mock对象
    deviceFingerprintService = module.get<DeviceFingerprintService>(
      DeviceFingerprintService,
    );
    verificationSecurityService = module.get<VerificationSecurityService>(
      VerificationSecurityService,
    );
    aliyunSmsService = module.get<AliyunSmsService>(AliyunSmsService);

    // 设置默认mock行为
    (aliyunSmsService.getVipTestPhones as jest.Mock).mockReturnValue([]);
    (aliyunSmsService.sendVerificationCode as jest.Mock).mockResolvedValue({
      success: true,
      message: "验证码发送成功",
    });
    (aliyunSmsService.getCurrentSmsMode as jest.Mock).mockReturnValue({
      mode: "hybrid",
      environment: "test",
      vipPhones: [],
      clientStatus: "ready",
    });
  });

  afterEach(() => {
    jest.clearAllMocks();

    // 重新设置默认mock行为
    (aliyunSmsService.getVipTestPhones as jest.Mock).mockReturnValue([]);
    (aliyunSmsService.sendVerificationCode as jest.Mock).mockResolvedValue({
      success: true,
      message: "验证码发送成功",
    });
    (aliyunSmsService.getCurrentSmsMode as jest.Mock).mockReturnValue({
      mode: "hybrid",
      environment: "test",
      vipPhones: [],
      clientStatus: "ready",
    });
  });

  describe("sendVerificationCode", () => {
    it("should send verification code successfully", async () => {
      // 设置mocks
      (
        deviceFingerprintService.generateDeviceFingerprint as jest.Mock
      ).mockReturnValue(mockDeviceInfo);
      (
        verificationSecurityService.checkVerificationAttempt as jest.Mock
      ).mockResolvedValue({
        allowed: true,
      });
      (mockRedisClient.sismember as jest.Mock).mockResolvedValue(0); // 不在黑名单
      (aliyunSmsService.getVipTestPhones as jest.Mock).mockReturnValue([]);
      (aliyunSmsService.sendVerificationCode as jest.Mock).mockResolvedValue({
        success: true,
        message: "验证码发送成功",
      });

      // 模拟Redis pipeline执行结果 - 没有发送限制
      const mockPipeline = mockRedisClient.pipeline();
      mockPipeline.exec.mockResolvedValue([
        [null, null], // sms:last_sent
        [null, "0"], // sms:daily
        [null, "0"], // sms:hourly
        [null, "0"], // sms:minute
        [null, "0"], // sms:failures
        [null, null], // sms:blocked
      ]);

      const result = await service.sendVerificationCode(
        mockPhoneNumber,
        mockRequest,
      );

      expect(result).toEqual({
        success: true,
        message: "验证码发送成功",
      });
      expect(
        deviceFingerprintService.generateDeviceFingerprint,
      ).toHaveBeenCalledWith(mockRequest);
      expect(
        verificationSecurityService.checkVerificationAttempt,
      ).toHaveBeenCalled();
      expect(mockRedisClient.sismember).toHaveBeenCalledWith(
        "sms:blacklist",
        mockPhoneNumber,
      );
      expect(aliyunSmsService.sendVerificationCode).toHaveBeenCalledWith(
        mockPhoneNumber,
        "1234",
      );
    });

    it("should throw BadRequestException for invalid phone number", async () => {
      const invalidPhone = "123";

      await expect(
        service.sendVerificationCode(invalidPhone, mockRequest),
      ).rejects.toThrow(BadRequestException);
      expect(
        deviceFingerprintService.generateDeviceFingerprint,
      ).not.toHaveBeenCalled();
    });

    it("should throw BadRequestException for security check failure", async () => {
      (
        deviceFingerprintService.generateDeviceFingerprint as jest.Mock
      ).mockReturnValue(mockDeviceInfo);
      (
        verificationSecurityService.checkVerificationAttempt as jest.Mock
      ).mockResolvedValue({
        allowed: false,
        reason: "安全检查失败",
      });

      await expect(
        service.sendVerificationCode(mockPhoneNumber, mockRequest),
      ).rejects.toThrow(BadRequestException);
      expect(
        verificationSecurityService.checkVerificationAttempt,
      ).toHaveBeenCalled();
    });

    it("should throw BadRequestException for blacklisted phone", async () => {
      (
        deviceFingerprintService.generateDeviceFingerprint as jest.Mock
      ).mockReturnValue(mockDeviceInfo);
      (
        verificationSecurityService.checkVerificationAttempt as jest.Mock
      ).mockResolvedValue({
        allowed: true,
      });
      (mockRedisClient.sismember as jest.Mock).mockResolvedValue(1); // 在黑名单

      await expect(
        service.sendVerificationCode(mockPhoneNumber, mockRequest),
      ).rejects.toThrow(BadRequestException);
      expect(mockRedisClient.sismember).toHaveBeenCalledWith(
        "sms:blacklist",
        mockPhoneNumber,
      );
    });

    it("should throw BadRequestException for hourly sending limits exceeded", async () => {
      // 模拟非测试环境
      (configService.get as jest.Mock).mockImplementation((key: string) => {
        if (key === "NODE_ENV") return "production";
        return key === "SMS_MOCK_CODE" ? "123456" : undefined;
      });

      (
        deviceFingerprintService.generateDeviceFingerprint as jest.Mock
      ).mockReturnValue(mockDeviceInfo);
      (
        verificationSecurityService.checkVerificationAttempt as jest.Mock
      ).mockResolvedValue({
        allowed: true,
      });
      (mockRedisClient.sismember as jest.Mock).mockResolvedValue(0);

      // 模拟达到小时发送限制（5次/小时）
      const mockPipeline = mockRedisClient.pipeline();
      mockPipeline.exec.mockResolvedValue([
        [null, null], // sms:last_sent
        [null, "0"], // sms:daily
        [null, "5"], // sms:hourly - 已达到小时限制
        [null, "0"], // sms:minute
        [null, "0"], // sms:failures
        [null, null], // sms:blocked
      ]);

      await expect(
        service.sendVerificationCode(mockPhoneNumber, mockRequest),
      ).rejects.toThrow(BadRequestException);

      // 恢复测试环境配置
      (configService.get as jest.Mock).mockImplementation((key: string) => {
        const config: MockConfig = {
          NODE_ENV: "test",
          SMS_MOCK_CODE: "123456",
        };
        return config[key];
      });
    });

    it("should throw BadRequestException for time interval limits (lastSent)", async () => {
      // 模拟非测试环境
      (configService.get as jest.Mock).mockImplementation((key: string) => {
        if (key === "NODE_ENV") return "production";
        return key === "SMS_MOCK_CODE" ? "123456" : undefined;
      });

      (
        deviceFingerprintService.generateDeviceFingerprint as jest.Mock
      ).mockReturnValue(mockDeviceInfo);
      (
        verificationSecurityService.checkVerificationAttempt as jest.Mock
      ).mockResolvedValue({
        allowed: true,
      });
      (mockRedisClient.sismember as jest.Mock).mockResolvedValue(0);

      // 模拟最近发送过（60秒内）
      const recentTimestamp = (Date.now() - 30 * 1000).toString(); // 30秒前发送
      const mockPipeline = mockRedisClient.pipeline();
      mockPipeline.exec.mockResolvedValue([
        [null, recentTimestamp], // sms:last_sent - 30秒前发送过
        [null, "0"], // sms:daily
        [null, "0"], // sms:hourly
        [null, "0"], // sms:minute
        [null, "0"], // sms:failures
        [null, null], // sms:blocked
      ]);

      await expect(
        service.sendVerificationCode(mockPhoneNumber, mockRequest),
      ).rejects.toThrow(BadRequestException);

      // 恢复测试环境配置
      (configService.get as jest.Mock).mockImplementation((key: string) => {
        const config: MockConfig = {
          NODE_ENV: "test",
          SMS_MOCK_CODE: "123456",
        };
        return config[key];
      });
    });

    it("should work without request object", async () => {
      (mockRedisClient.sismember as jest.Mock).mockResolvedValue(0);
      (aliyunSmsService.getVipTestPhones as jest.Mock).mockReturnValue([]);
      (aliyunSmsService.sendVerificationCode as jest.Mock).mockResolvedValue({
        success: true,
        message: "验证码发送成功",
      });

      const mockPipeline = mockRedisClient.pipeline();
      mockPipeline.exec.mockResolvedValue([
        [null, null], // sms:last_sent
        [null, "0"], // sms:daily
        [null, "0"], // sms:hourly
        [null, "0"], // sms:minute
        [null, "0"], // sms:failures
        [null, null], // sms:blocked
      ]);

      const result = await service.sendVerificationCode(mockPhoneNumber);

      expect(result).toEqual({
        success: true,
        message: "验证码发送成功",
      });
      expect(
        deviceFingerprintService.generateDeviceFingerprint,
      ).not.toHaveBeenCalled();
      expect(
        verificationSecurityService.checkVerificationAttempt,
      ).not.toHaveBeenCalled();
    });

    it("should handle SMS sending failure", async () => {
      (
        deviceFingerprintService.generateDeviceFingerprint as jest.Mock
      ).mockReturnValue(mockDeviceInfo);
      (
        verificationSecurityService.checkVerificationAttempt as jest.Mock
      ).mockResolvedValue({
        allowed: true,
      });
      (mockRedisClient.sismember as jest.Mock).mockResolvedValue(0);
      (aliyunSmsService.getVipTestPhones as jest.Mock).mockReturnValue([]);
      (aliyunSmsService.sendVerificationCode as jest.Mock).mockResolvedValue({
        success: false,
        message: "短信发送失败",
      });

      const mockPipeline = mockRedisClient.pipeline();
      mockPipeline.exec.mockResolvedValue([
        [null, null],
        [null, "0"],
        [null, "0"],
        [null, "0"],
        [null, "0"],
        [null, null],
      ]);

      await expect(
        service.sendVerificationCode(mockPhoneNumber, mockRequest),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe("verifyCode", () => {
    it("should verify code successfully", async () => {
      (
        deviceFingerprintService.generateDeviceFingerprint as jest.Mock
      ).mockReturnValue(mockDeviceInfo);
      (
        deviceFingerprintService.validateSmsDevice as jest.Mock
      ).mockResolvedValue(true);
      (mockRedisClient.get as jest.Mock).mockResolvedValue(mockCode);

      const result = await service.verifyCode(
        mockPhoneNumber,
        mockCode,
        mockRequest,
      );

      expect(result).toBe(true);
      expect(
        deviceFingerprintService.generateDeviceFingerprint,
      ).toHaveBeenCalledWith(mockRequest);
      expect(deviceFingerprintService.validateSmsDevice).toHaveBeenCalledWith(
        mockPhoneNumber,
        mockDeviceInfo,
      );
      expect(mockRedisClient.get).toHaveBeenCalledWith(
        `sms:code:${mockPhoneNumber}`,
      );
      expect(mockRedisClient.del).toHaveBeenCalledWith(
        `sms:code:${mockPhoneNumber}`,
      );
      expect(
        verificationSecurityService.recordVerificationAttempt,
      ).toHaveBeenCalledWith({
        phone: mockPhoneNumber,
        code: mockCode,
        timestamp: expect.any(Number),
        ip: mockDeviceInfo.ip,
        deviceFingerprint: mockDeviceInfo.fingerprint,
        success: true,
      });
    });

    it("should return false for wrong code", async () => {
      (
        deviceFingerprintService.generateDeviceFingerprint as jest.Mock
      ).mockReturnValue(mockDeviceInfo);
      (
        deviceFingerprintService.validateSmsDevice as jest.Mock
      ).mockResolvedValue(true);
      (mockRedisClient.get as jest.Mock).mockResolvedValue("5678"); // 不同的验证码

      const result = await service.verifyCode(
        mockPhoneNumber,
        mockCode,
        mockRequest,
      );

      expect(result).toBe(false);
      expect(mockRedisClient.del).not.toHaveBeenCalled();
      expect(
        verificationSecurityService.recordVerificationAttempt,
      ).toHaveBeenCalledWith({
        phone: mockPhoneNumber,
        code: mockCode,
        timestamp: expect.any(Number),
        ip: mockDeviceInfo.ip,
        deviceFingerprint: mockDeviceInfo.fingerprint,
        success: false,
      });
    });

    it("should return false for expired code", async () => {
      (
        deviceFingerprintService.generateDeviceFingerprint as jest.Mock
      ).mockReturnValue(mockDeviceInfo);
      (
        deviceFingerprintService.validateSmsDevice as jest.Mock
      ).mockResolvedValue(true);
      (mockRedisClient.get as jest.Mock).mockResolvedValue(null); // 验证码已过期

      const result = await service.verifyCode(
        mockPhoneNumber,
        mockCode,
        mockRequest,
      );

      expect(result).toBe(false);
      expect(
        verificationSecurityService.recordVerificationAttempt,
      ).toHaveBeenCalledWith({
        phone: mockPhoneNumber,
        code: mockCode,
        timestamp: expect.any(Number),
        ip: mockDeviceInfo.ip,
        deviceFingerprint: mockDeviceInfo.fingerprint,
        success: false,
      });
    });

    it("should work without request object", async () => {
      (mockRedisClient.get as jest.Mock).mockResolvedValue(mockCode);

      const result = await service.verifyCode(mockPhoneNumber, mockCode);

      expect(result).toBe(true);
      expect(
        deviceFingerprintService.generateDeviceFingerprint,
      ).not.toHaveBeenCalled();
      expect(
        verificationSecurityService.recordVerificationAttempt,
      ).not.toHaveBeenCalled();
    });

    it("should handle device fingerprint validation failure", async () => {
      (
        deviceFingerprintService.generateDeviceFingerprint as jest.Mock
      ).mockReturnValue(mockDeviceInfo);
      (
        deviceFingerprintService.validateSmsDevice as jest.Mock
      ).mockResolvedValue(false);
      (mockRedisClient.get as jest.Mock).mockResolvedValue(mockCode);

      const result = await service.verifyCode(
        mockPhoneNumber,
        mockCode,
        mockRequest,
      );

      expect(result).toBe(true); // 设备不一致时记录但不直接拒绝
      expect(
        verificationSecurityService.recordVerificationAttempt,
      ).toHaveBeenCalledWith({
        phone: mockPhoneNumber,
        code: mockCode,
        timestamp: expect.any(Number),
        ip: mockDeviceInfo.ip,
        deviceFingerprint: mockDeviceInfo.fingerprint,
        success: false,
      });
    });

    it("should handle verification error", async () => {
      (
        deviceFingerprintService.generateDeviceFingerprint as jest.Mock
      ).mockReturnValue(mockDeviceInfo);
      (
        deviceFingerprintService.validateSmsDevice as jest.Mock
      ).mockResolvedValue(true);
      (mockRedisClient.get as jest.Mock).mockRejectedValue(
        new Error("Redis error"),
      );

      const result = await service.verifyCode(
        mockPhoneNumber,
        mockCode,
        mockRequest,
      );

      expect(result).toBe(false);
      expect(
        verificationSecurityService.recordVerificationAttempt,
      ).toHaveBeenCalledWith({
        phone: mockPhoneNumber,
        code: mockCode,
        timestamp: expect.any(Number),
        ip: mockDeviceInfo.ip,
        deviceFingerprint: mockDeviceInfo.fingerprint,
        success: false,
      });
    });
  });

  describe("getStatistics", () => {
    it("should return SMS statistics", async () => {
      const mockPipeline = mockRedisClient.pipeline();
      mockPipeline.exec.mockResolvedValue([
        [null, "100"], // total_sent
        [null, "90"], // total_success
        [null, "10"], // total_failed
        [null, ["1000", "1200", "800"]], // response_times
      ]);

      const result = await service.getStatistics();

      expect(result).toEqual({
        totalSent: 100,
        totalFailed: 10,
        dailySuccessRate: 90,
        averageResponseTime: 1000,
        lastAnalysisTime: expect.any(Number),
      });
    });

    it("should handle empty statistics", async () => {
      const mockPipeline = mockRedisClient.pipeline();
      mockPipeline.exec.mockResolvedValue([
        [null, null],
        [null, null],
        [null, null],
        [null, []],
      ]);

      const result = await service.getStatistics();

      expect(result).toEqual({
        totalSent: 0,
        totalFailed: 0,
        dailySuccessRate: 0,
        averageResponseTime: 0,
        lastAnalysisTime: expect.any(Number),
      });
    });

    it("should handle pipeline execution failure", async () => {
      const mockPipeline = mockRedisClient.pipeline();
      mockPipeline.exec.mockResolvedValue(null);

      await expect(service.getStatistics()).rejects.toThrow(
        "Redis pipeline execution failed",
      );
    });
  });

  describe("getSmsServiceStatus", () => {
    it("should return SMS service status", async () => {
      const mockStatus = {
        mode: "hybrid",
        environment: "development",
        vipPhones: ["13800138000"],
        clientStatus: "ready",
      };

      (aliyunSmsService.getCurrentSmsMode as jest.Mock).mockReturnValue(
        mockStatus,
      );

      const result = await service.getSmsServiceStatus();

      expect(result).toEqual({
        ...mockStatus,
        limitInfo: undefined,
      });
      expect(aliyunSmsService.getCurrentSmsMode).toHaveBeenCalled();
    });
  });

  describe("VIP phone management", () => {
    it("should add VIP test phone", () => {
      const testPhone = "13800138001";

      service.addVipTestPhone(testPhone);

      expect(aliyunSmsService.addVipTestPhone).toHaveBeenCalledWith(testPhone);
    });

    it("should remove VIP test phone", () => {
      const testPhone = "13800138001";

      service.removeVipTestPhone(testPhone);

      expect(aliyunSmsService.removeVipTestPhone).toHaveBeenCalledWith(
        testPhone,
      );
    });
  });

  describe("private methods integration", () => {
    it("should handle test environment correctly", async () => {
      // 设置测试环境
      (configService.get as jest.Mock).mockImplementation((key: string) => {
        if (key === "NODE_ENV") return "test";
        if (key === "SMS_MOCK_CODE") return "123456";
        return undefined;
      });

      (
        deviceFingerprintService.generateDeviceFingerprint as jest.Mock
      ).mockReturnValue(mockDeviceInfo);
      (
        verificationSecurityService.checkVerificationAttempt as jest.Mock
      ).mockResolvedValue({
        allowed: true,
      });
      (mockRedisClient.sismember as jest.Mock).mockResolvedValue(0);
      (aliyunSmsService.getVipTestPhones as jest.Mock).mockReturnValue([]);
      (aliyunSmsService.sendVerificationCode as jest.Mock).mockResolvedValue({
        success: true,
        message: "验证码发送成功",
      });

      // 在测试环境下，发送限制检查应该被跳过
      const result = await service.sendVerificationCode(
        mockPhoneNumber,
        mockRequest,
      );

      expect(result).toEqual({
        success: true,
        message: "验证码发送成功",
      });
      // 在测试环境下应该跳过发送限制检查
      expect(mockRedisClient.pipeline).toHaveBeenCalled();
    });

    it("should handle VIP phone correctly", async () => {
      const vipPhone = "13800138888";

      (
        deviceFingerprintService.generateDeviceFingerprint as jest.Mock
      ).mockReturnValue(mockDeviceInfo);
      (
        verificationSecurityService.checkVerificationAttempt as jest.Mock
      ).mockResolvedValue({
        allowed: true,
      });
      (mockRedisClient.sismember as jest.Mock).mockResolvedValue(0);
      (aliyunSmsService.getVipTestPhones as jest.Mock).mockReturnValue([
        vipPhone,
      ]);
      (aliyunSmsService.sendVerificationCode as jest.Mock).mockResolvedValue({
        success: true,
        message: "验证码发送成功",
      });

      const mockPipeline = mockRedisClient.pipeline();
      mockPipeline.exec.mockResolvedValue([
        [null, null],
        [null, "0"],
        [null, "0"],
        [null, "0"],
        [null, "0"],
        [null, null],
      ]);

      const result = await service.sendVerificationCode(vipPhone, mockRequest);

      expect(result).toEqual({
        success: true,
        message: "验证码发送成功",
      });
      expect(aliyunSmsService.getVipTestPhones).toHaveBeenCalled();
    });
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  describe("分支覆盖率增强测试", () => {
    describe("sendVerificationCode - 错误处理分支", () => {
      it("should handle device fingerprint service error", async () => {
        // 模拟设备指纹服务错误
        (
          deviceFingerprintService.generateDeviceFingerprint as jest.Mock
        ).mockImplementation(() => {
          throw new Error("Device fingerprint error");
        });

        await expect(
          service.sendVerificationCode(mockPhoneNumber, mockRequest),
        ).rejects.toThrow(BadRequestException);
      });

      it("should handle verification security check failure", async () => {
        (
          deviceFingerprintService.generateDeviceFingerprint as jest.Mock
        ).mockReturnValue(mockDeviceInfo);
        (
          verificationSecurityService.checkVerificationAttempt as jest.Mock
        ).mockResolvedValue({
          allowed: false,
          reason: "Too many attempts",
        });

        await expect(
          service.sendVerificationCode(mockPhoneNumber, mockRequest),
        ).rejects.toThrow(BadRequestException);
      });

      it("should handle blacklisted phone number", async () => {
        (
          deviceFingerprintService.generateDeviceFingerprint as jest.Mock
        ).mockReturnValue(mockDeviceInfo);
        (
          verificationSecurityService.checkVerificationAttempt as jest.Mock
        ).mockResolvedValue({
          allowed: true,
        });
        (mockRedisClient.sismember as jest.Mock).mockResolvedValue(1); // 在黑名单中

        await expect(
          service.sendVerificationCode(mockPhoneNumber, mockRequest),
        ).rejects.toThrow(BadRequestException);
      });

      it("should handle rate limit exceeded - daily limit", async () => {
        (
          deviceFingerprintService.generateDeviceFingerprint as jest.Mock
        ).mockReturnValue(mockDeviceInfo);
        (
          verificationSecurityService.checkVerificationAttempt as jest.Mock
        ).mockResolvedValue({
          allowed: true,
        });
        (mockRedisClient.sismember as jest.Mock).mockResolvedValue(0);

        const mockPipeline = mockRedisClient.pipeline();
        mockPipeline.exec.mockResolvedValue([
          [null, null],
          [null, "100"], // 超过日限制 (假设限制为50)
          [null, "0"],
          [null, "0"],
          [null, "0"],
          [null, null],
        ]);

        await expect(
          service.sendVerificationCode(mockPhoneNumber, mockRequest),
        ).rejects.toThrow(BadRequestException);
      });

      it("should handle rate limit exceeded - hourly limit", async () => {
        (
          deviceFingerprintService.generateDeviceFingerprint as jest.Mock
        ).mockReturnValue(mockDeviceInfo);
        (
          verificationSecurityService.checkVerificationAttempt as jest.Mock
        ).mockResolvedValue({
          allowed: true,
        });
        (mockRedisClient.sismember as jest.Mock).mockResolvedValue(0);

        const mockPipeline = mockRedisClient.pipeline();
        mockPipeline.exec.mockResolvedValue([
          [null, null],
          [null, "5"],
          [null, "20"], // 超过小时限制 (假设限制为10)
          [null, "0"],
          [null, "0"],
          [null, null],
        ]);

        await expect(
          service.sendVerificationCode(mockPhoneNumber, mockRequest),
        ).rejects.toThrow(BadRequestException);
      });

      it("should handle rate limit exceeded - minute limit", async () => {
        (
          deviceFingerprintService.generateDeviceFingerprint as jest.Mock
        ).mockReturnValue(mockDeviceInfo);
        (
          verificationSecurityService.checkVerificationAttempt as jest.Mock
        ).mockResolvedValue({
          allowed: true,
        });
        (mockRedisClient.sismember as jest.Mock).mockResolvedValue(0);

        const mockPipeline = mockRedisClient.pipeline();
        mockPipeline.exec.mockResolvedValue([
          [null, null],
          [null, "5"],
          [null, "3"],
          [null, "5"], // 超过分钟限制 (假设限制为2)
          [null, "0"],
          [null, null],
        ]);

        await expect(
          service.sendVerificationCode(mockPhoneNumber, mockRequest),
        ).rejects.toThrow(BadRequestException);
      });

      it("should handle blocked phone due to too many failures", async () => {
        (
          deviceFingerprintService.generateDeviceFingerprint as jest.Mock
        ).mockReturnValue(mockDeviceInfo);
        (
          verificationSecurityService.checkVerificationAttempt as jest.Mock
        ).mockResolvedValue({
          allowed: true,
        });
        (mockRedisClient.sismember as jest.Mock).mockResolvedValue(0);

        const mockPipeline = mockRedisClient.pipeline();
        mockPipeline.exec.mockResolvedValue([
          [null, null],
          [null, "5"],
          [null, "3"],
          [null, "1"],
          [null, "10"], // 失败次数过多 (假设限制为5)
          [null, "1"], // 已被阻止
        ]);

        await expect(
          service.sendVerificationCode(mockPhoneNumber, mockRequest),
        ).rejects.toThrow(BadRequestException);
      });

      it("should handle aliyun SMS service error", async () => {
        (
          deviceFingerprintService.generateDeviceFingerprint as jest.Mock
        ).mockReturnValue(mockDeviceInfo);
        (
          verificationSecurityService.checkVerificationAttempt as jest.Mock
        ).mockResolvedValue({
          allowed: true,
        });
        (mockRedisClient.sismember as jest.Mock).mockResolvedValue(0);
        (aliyunSmsService.getVipTestPhones as jest.Mock).mockReturnValue([]);
        (aliyunSmsService.sendVerificationCode as jest.Mock).mockRejectedValue(
          new Error("Aliyun service error"),
        );

        const mockPipeline = mockRedisClient.pipeline();
        mockPipeline.exec.mockResolvedValue([
          [null, null],
          [null, "0"],
          [null, "0"],
          [null, "0"],
          [null, "0"],
          [null, null],
        ]);

        await expect(
          service.sendVerificationCode(mockPhoneNumber, mockRequest),
        ).rejects.toThrow(BadRequestException);
      });
    });

    describe("verifyCode - 分支覆盖", () => {
      it("should verify code without device info", async () => {
        // 不提供request参数的情况
        (aliyunSmsService.verifyCode as jest.Mock).mockResolvedValue(true);

        const result = await service.verifyCode(mockPhoneNumber, "123456");

        expect(result).toBe(true);
        expect(
          deviceFingerprintService.generateDeviceFingerprint,
        ).not.toHaveBeenCalled();
      });

      it("should handle verification failure", async () => {
        (
          deviceFingerprintService.generateDeviceFingerprint as jest.Mock
        ).mockReturnValue(mockDeviceInfo);
        (aliyunSmsService.verifyCode as jest.Mock).mockResolvedValue(false);

        const result = await service.verifyCode(
          mockPhoneNumber,
          "wrong-code",
          mockRequest,
        );

        expect(result).toBe(false);
      });

      it("should handle verification service error", async () => {
        (
          deviceFingerprintService.generateDeviceFingerprint as jest.Mock
        ).mockReturnValue(mockDeviceInfo);
        (aliyunSmsService.verifyCode as jest.Mock).mockRejectedValue(
          new Error("Verification service error"),
        );

        await expect(
          service.verifyCode(mockPhoneNumber, "123456", mockRequest),
        ).rejects.toThrow();
      });

      it("should handle device fingerprint error during verification", async () => {
        (
          deviceFingerprintService.generateDeviceFingerprint as jest.Mock
        ).mockImplementation(() => {
          throw new Error("Device fingerprint error");
        });

        // 应该继续验证，即使设备指纹生成失败
        (aliyunSmsService.verifyCode as jest.Mock).mockResolvedValue(true);

        const result = await service.verifyCode(
          mockPhoneNumber,
          "123456",
          mockRequest,
        );

        expect(result).toBe(true);
      });
    });

    describe("updateSendingLimits - 分支覆盖", () => {
      it("should handle pipeline execution error", async () => {
        const mockPipeline = mockRedisClient.pipeline();
        mockPipeline.exec.mockRejectedValue(new Error("Pipeline error"));

        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        await expect(
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          (service as any).updateSendingLimits(mockPhoneNumber, true),
        ).rejects.toThrow();
      });

      it("should handle success and failure statistics separately", async () => {
        const mockPipeline = mockRedisClient.pipeline();
        mockPipeline.exec.mockResolvedValue([]);

        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        await (service as any).updateSendingLimits(mockPhoneNumber, true);
        expect(mockPipeline.incr).toHaveBeenCalled();

        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        await (service as any).updateSendingLimits(mockPhoneNumber, false);
        expect(mockPipeline.incr).toHaveBeenCalled();
      });
    });

    describe("边界条件和异常处理", () => {
      it("should handle Redis connection failure", async () => {
        (mockRedisClient.sismember as jest.Mock).mockRejectedValue(
          new Error("Redis connection failed"),
        );

        await expect(
          service.sendVerificationCode(mockPhoneNumber, mockRequest),
        ).rejects.toThrow(BadRequestException);
      });

      it("should handle malformed pipeline results", async () => {
        (
          deviceFingerprintService.generateDeviceFingerprint as jest.Mock
        ).mockReturnValue(mockDeviceInfo);
        (
          verificationSecurityService.checkVerificationAttempt as jest.Mock
        ).mockResolvedValue({
          allowed: true,
        });
        (mockRedisClient.sismember as jest.Mock).mockResolvedValue(0);

        const mockPipeline = mockRedisClient.pipeline();
        // 返回格式错误的结果
        mockPipeline.exec.mockResolvedValue([
          [null, null],
          [new Error("Parse error"), null], // 错误情况
          [null, "0"],
          [null, "0"],
          [null, "0"],
          [null, null],
        ]);

        await expect(
          service.sendVerificationCode(mockPhoneNumber, mockRequest),
        ).rejects.toThrow(BadRequestException);
      });

      it("should handle configuration service errors", async () => {
        (configService.get as jest.Mock).mockImplementation(() => {
          throw new Error("Config service error");
        });

        await expect(
          service.sendVerificationCode(mockPhoneNumber, mockRequest),
        ).rejects.toThrow(BadRequestException);
      });
    });
  });
});
