import { registerAs } from "@nestjs/config";
import {
  IsString,
  IsNumber,
  IsBoolean,
  IsOptional,
  IsIn,
  validateSync,
} from "class-validator";
import { Transform, Type, plainToClass } from "class-transformer";

export class AppConfiguration {
  // 应用基础配置
  @IsString()
  NODE_ENV: string = "development";

  @IsNumber()
  @Type(() => Number)
  PORT: number = 3000;

  @IsString()
  @IsOptional()
  FRONTEND_URL?: string;

  @IsString()
  @IsOptional()
  INSTANCE_ID?: string;

  @IsString()
  @IsOptional()
  VERSION?: string;

  // 数据库配置
  @IsString()
  DB_HOST: string = "localhost";

  @IsNumber()
  @Type(() => Number)
  DB_PORT: number = 5432;

  @IsString()
  DB_USERNAME: string = "postgres";

  @IsString()
  DB_PASSWORD: string = "password";

  @IsString()
  DB_NAME: string = "story_app";

  @IsBoolean()
  @Transform(({ value }) => value === "true")
  AUTO_SYNC: boolean = false;

  @IsBoolean()
  @Transform(({ value }) => value === "true")
  @IsOptional()
  DB_SSL?: boolean = false;

  // Redis配置
  @IsString()
  REDIS_HOST: string = "localhost";

  @IsNumber()
  @Type(() => Number)
  REDIS_PORT: number = 6379;

  @IsString()
  @IsOptional()
  REDIS_PASSWORD?: string;

  @IsNumber()
  @Type(() => Number)
  REDIS_DB: number = 0;

  @IsString()
  @IsOptional()
  REDIS_KEY_PREFIX?: string = "story-app:";

  @IsNumber()
  @Type(() => Number)
  REDIS_TTL: number = 3600;

  @IsBoolean()
  @Transform(({ value }) => value === "true")
  REDIS_CLUSTER_ENABLED: boolean = false;

  // JWT配置
  @IsString()
  JWT_SECRET: string = "your-secret-key";

  @IsString()
  JWT_EXPIRES_IN: string = "7d";

  @IsString()
  JWT_REFRESH_SECRET: string = "your-refresh-secret";

  @IsString()
  JWT_REFRESH_EXPIRES_IN: string = "30d";

  @IsNumber()
  @Type(() => Number)
  BCRYPT_ROUNDS: number = 12;

  // 短信服务配置
  @IsIn(["aliyun", "tencent", "huawei"])
  @IsOptional()
  SMS_PROVIDER?: string = "aliyun";

  @IsString()
  @IsOptional()
  SMS_ACCESS_KEY_ID?: string;

  @IsString()
  @IsOptional()
  SMS_ACCESS_KEY_SECRET?: string;

  @IsString()
  @IsOptional()
  SMS_SIGN_NAME?: string;

  @IsString()
  @IsOptional()
  SMS_TEMPLATE_CODE?: string;

  @IsBoolean()
  @Transform(({ value }) => value === "true")
  SMS_MOCK_MODE: boolean = true;

  @IsString()
  @IsOptional()
  SMS_MOCK_CODE?: string = "123456";

  @IsNumber()
  @Type(() => Number)
  VERIFICATION_CODE_EXPIRATION: number = 300;

  // OpenAI配置
  @IsString()
  @IsOptional()
  OPENAI_API_KEY?: string;

  @IsString()
  OPENAI_BASE_URL: string = "https://api.openai.com/v1";

  @IsString()
  OPENAI_MODEL: string = "gpt-3.5-turbo";

  @IsNumber()
  @Type(() => Number)
  OPENAI_MAX_TOKENS: number = 1000;

  @IsNumber()
  @Type(() => Number)
  OPENAI_TEMPERATURE: number = 0.7;

  @IsNumber()
  @Type(() => Number)
  AI_REQUEST_TIMEOUT: number = 30000;

  @IsNumber()
  @Type(() => Number)
  AI_MAX_REQUESTS_PER_USER: number = 50;

  @IsNumber()
  @Type(() => Number)
  AI_MAX_REQUESTS_WINDOW: number = 3600;

  // 限流配置
  @IsNumber()
  @Type(() => Number)
  THROTTLE_TTL: number = 60;

  @IsNumber()
  @Type(() => Number)
  THROTTLE_LIMIT: number = 10;

  // 日志配置
  @IsIn(["error", "warn", "info", "debug", "verbose"])
  LOG_LEVEL: string = "info";

  @IsBoolean()
  @Transform(({ value }) => value !== "false")
  LOG_CONSOLE: boolean = true;

  @IsBoolean()
  @Transform(({ value }) => value === "true")
  LOG_FILE: boolean = false;

  @IsString()
  LOG_DIRECTORY: string = "./logs";

  @IsString()
  LOG_MAX_FILES: string = "7d";

  @IsString()
  LOG_MAX_SIZE: string = "100m";

  // 性能配置
  @IsNumber()
  @Type(() => Number)
  @IsOptional()
  MAX_REQUEST_SIZE?: number = 10485760; // 10MB

  @IsNumber()
  @Type(() => Number)
  @IsOptional()
  REQUEST_TIMEOUT?: number = 30000; // 30秒

  @IsNumber()
  @Type(() => Number)
  @IsOptional()
  KEEP_ALIVE_TIMEOUT?: number = 5000; // 5秒

  // 安全配置
  @IsBoolean()
  @Transform(({ value }) => value === "true")
  @IsOptional()
  ENABLE_HELMET?: boolean = true;

  @IsBoolean()
  @Transform(({ value }) => value === "true")
  @IsOptional()
  ENABLE_COMPRESSION?: boolean = true;

  @IsString()
  @IsOptional()
  CORS_ORIGIN?: string;

  // 监控配置
  @IsBoolean()
  @Transform(({ value }) => value === "true")
  @IsOptional()
  ENABLE_METRICS?: boolean = false;

  @IsString()
  @IsOptional()
  METRICS_ENDPOINT?: string = "/metrics";

  @IsBoolean()
  @Transform(({ value }) => value === "true")
  @IsOptional()
  ENABLE_TRACING?: boolean = false;
}

export function validateConfig(
  config: Record<string, unknown>,
): AppConfiguration {
  const validatedConfig = plainToClass(AppConfiguration, config, {
    enableImplicitConversion: true,
  });

  const errors = validateSync(validatedConfig, {
    skipMissingProperties: false,
  });

  if (errors.length > 0) {
    const errorMessages = errors.map((error) => {
      const constraints = Object.values(error.constraints || {});
      return `${error.property}: ${constraints.join(", ")}`;
    });

    throw new Error(
      `Configuration validation failed:\n${errorMessages.join("\n")}`,
    );
  }

  return validatedConfig;
}

export const appConfig = registerAs("app", () => {
  const config = validateConfig(process.env);

  // 检查关键配置
  validateCriticalConfig(config);

  return config;
});

function validateCriticalConfig(config: AppConfiguration): void {
  const criticalErrors: string[] = [];

  // 生产环境必须配置的项目
  if (config.NODE_ENV === "production") {
    if (!config.JWT_SECRET || config.JWT_SECRET.length < 32) {
      criticalErrors.push(
        "JWT_SECRET must be at least 32 characters in production",
      );
    }

    if (!config.JWT_REFRESH_SECRET || config.JWT_REFRESH_SECRET.length < 32) {
      criticalErrors.push(
        "JWT_REFRESH_SECRET must be at least 32 characters in production",
      );
    }

    if (!config.DB_PASSWORD || config.DB_PASSWORD.length < 8) {
      criticalErrors.push(
        "DB_PASSWORD must be at least 8 characters in production",
      );
    }

    if (config.AUTO_SYNC) {
      criticalErrors.push("AUTO_SYNC must be false in production");
    }

    if (config.SMS_MOCK_MODE && !config.SMS_ACCESS_KEY_ID) {
      criticalErrors.push(
        "SMS service must be properly configured in production",
      );
    }
  }

  // 开发环境警告
  if (config.NODE_ENV === "development") {
    if (config.JWT_SECRET === "story-app-secret-key-change-in-production") {
      console.warn("⚠️  Warning: Using default JWT_SECRET in development");
    }

    if (config.SMS_MOCK_MODE) {
      console.warn("⚠️  Warning: SMS is in mock mode");
    }
  }

  if (criticalErrors.length > 0) {
    throw new Error(
      `Critical configuration errors:\n${criticalErrors.join("\n")}`,
    );
  }
}

// 配置工具函数
export class ConfigUtils {
  static isDevelopment(): boolean {
    return process.env.NODE_ENV === "development";
  }

  static isProduction(): boolean {
    return process.env.NODE_ENV === "production";
  }

  static isTest(): boolean {
    return process.env.NODE_ENV === "test";
  }

  static getServiceVersion(): string {
    return process.env.npm_package_version || "1.0.0";
  }

  static getInstanceId(): string {
    return (
      process.env.INSTANCE_ID ||
      process.env.HOSTNAME ||
      `instance-${Date.now()}`
    );
  }

  static maskSensitiveValue(value: string): string {
    if (!value || value.length <= 4) {
      return "***";
    }
    return (
      value.substring(0, 2) +
      "*".repeat(value.length - 4) +
      value.substring(value.length - 2)
    );
  }

  static logSafeConfig(config: AppConfiguration): Record<string, unknown> {
    const safeConfig = { ...config };

    // 隐藏敏感信息
    const sensitiveFields = [
      "DB_PASSWORD",
      "JWT_SECRET",
      "JWT_REFRESH_SECRET",
      "SMS_ACCESS_KEY_SECRET",
      "OPENAI_API_KEY",
      "REDIS_PASSWORD",
    ];

    sensitiveFields.forEach((field) => {
      if ((safeConfig as Record<string, unknown>)[field]) {
        (safeConfig as Record<string, unknown>)[field] =
          this.maskSensitiveValue(
            (safeConfig as Record<string, unknown>)[field] as string,
          );
      }
    });

    return safeConfig;
  }
}
