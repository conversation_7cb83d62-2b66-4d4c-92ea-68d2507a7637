name: ygs_app
description: YGS - 基于真实社交关系的故事分享平台
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.5.0 <4.0.0'
  flutter: '>=3.32.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # 状态管理
  flutter_riverpod: ^2.5.0
  riverpod_annotation: ^2.3.0
  
  # 路由导航
  go_router: ^14.0.0
  
  # 网络请求
  dio: ^5.4.0
  retrofit: ^4.1.0
  pretty_dio_logger: ^1.3.1
  
  # 本地存储
  isar: ^3.1.0
  isar_flutter_libs: ^3.1.0
  flutter_secure_storage: ^9.2.0
  shared_preferences: ^2.2.0
  
  # 依赖注入
  get_it: ^7.6.0
  injectable: ^2.4.0
  
  # 函数式编程
  dartz: ^0.10.1
  
  # 数据类生成
  freezed_annotation: ^2.4.0
  json_annotation: ^4.9.0
  
  # UI组件
  cupertino_icons: ^1.0.6
  flutter_svg: ^2.0.10
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0
  keyboard_actions: ^4.1.0
  flutter_animate: ^4.5.0
  flutter_screenutil: ^5.9.0
  
  # 图片选择和处理（暂时禁用）
  # image_picker: ^1.0.0
  # image_cropper: ^5.0.0
  
  # 权限管理（暂时禁用）
  # permission_handler: ^11.0.0
  
  # 生物识别（暂时禁用）
  # local_auth: ^2.2.0
  
  # 推送通知（暂时禁用）
  # firebase_core: ^2.24.0
  # firebase_messaging: ^14.7.0
  # flutter_local_notifications: ^17.0.0
  
  # 性能监控（暂时禁用）
  # firebase_performance: ^0.9.3
  # firebase_crashlytics: ^3.4.0
  # firebase_analytics: ^10.8.0
  
  # 工具类
  intl: ^0.20.2
  uuid: ^4.3.0
  crypto: ^3.0.0
  collection: ^1.18.0
  
  # WebSocket
  web_socket_channel: ^2.4.0
  
  # 日志
  logger: ^2.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0
  
  # 代码生成
  build_runner: ^2.4.0
  freezed: ^2.4.0
  json_serializable: ^6.7.0
  retrofit_generator: ^8.0.0
  injectable_generator: ^2.4.0
  riverpod_generator: ^2.3.0
  
  # 测试
  mockito: ^5.4.0
  build_verify: ^3.1.0
  
  # 开发工具
  flutter_launcher_icons: ^0.13.0
  flutter_native_splash: ^2.3.0

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/images/splash/
    - assets/images/auth/
    - assets/images/test/
    - assets/images/avatars/
    - assets/images/story_covers/
    - assets/images/theme_modules/
    - assets/icons/
    - assets/icons/tab/
    - assets/icons/basic/
    - assets/icons/themes/
    - assets/fonts/
    
  # fonts:
    # - family: PingFang
    #   fonts:
    #     - asset: assets/fonts/PingFangSC-Regular.ttf
    #       weight: 400
    #     - asset: assets/fonts/PingFangSC-Medium.ttf
    #       weight: 500
    #     - asset: assets/fonts/PingFangSC-Semibold.ttf
    #       weight: 600

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icons/app_icon.png"
  adaptive_icon_background: "#FFFFFF"
  adaptive_icon_foreground: "assets/icons/app_icon_foreground.png"
  remove_alpha_ios: true

flutter_native_splash:
  color: "#FFFFFF"
  # image: assets/images/splash.png
  android: true
  ios: true