import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../tokens/ygs_colors.dart';

/// YGS自定义下拉刷新指示器
/// 完全自定义实现，不使用系统RefreshIndicator
class YgsCustomRefreshIndicator extends ConsumerStatefulWidget {
  const YgsCustomRefreshIndicator({
    required this.child,
    required this.onRefresh,
    super.key,
  });

  final Widget child;
  final Future<void> Function() onRefresh;

  @override
  ConsumerState<YgsCustomRefreshIndicator> createState() => _YgsCustomRefreshIndicatorState();
}

class _YgsCustomRefreshIndicatorState extends ConsumerState<YgsCustomRefreshIndicator>
    with TickerProviderStateMixin {
  bool _isRefreshing = false;
  double _pullDistance = 0.0;
  bool _canRefresh = false;
  final double _refreshThreshold = 120.h; // 增大触发阈值，需要更大幅度下拉才触发
  bool _isDragging = false; // 添加拖拽状态跟踪
  
  // 动画控制器，用于平滑的状态转换
  late AnimationController _bounceController;
  late Animation<double> _bounceAnimation;
  
  // 快速回弹动画控制器 - 用于未达到阈值时的快速回弹
  late AnimationController _quickBounceController;
  late Animation<double> _quickBounceAnimation;

  @override
  void initState() {
    super.initState();
    
    // 弹性回弹控制器 - 用于达到阈值后的弹性回弹
    _bounceController = AnimationController(
      duration: const Duration(milliseconds: 380), // 适中的时长，留给弹性动画时间
      vsync: this,
    );
    
    _bounceAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _bounceController,
      curve: const ElasticOutCurve(0.8), // 自定义弹性，0.8的周期更自然
    ));
    
    // 快速回弹控制器 - 用于未达到阈值时的快速回弹
    _quickBounceController = AnimationController(
      duration: const Duration(milliseconds: 180), // 快速回弹
      vsync: this,
    );
    
    _quickBounceAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _quickBounceController,
      curve: Curves.easeOutCubic, // 快速但平滑的回弹
    ));
  }

  @override
  void dispose() {
    _bounceController.dispose();
    _quickBounceController.dispose();
    super.dispose();
  }

  Future<void> _handleRefresh() async {
    if (_isRefreshing) return;
    
    setState(() {
      _isRefreshing = true;
      _canRefresh = false;
      _isDragging = false;
      // 触发刷新时，保持当前_pullDistance不变，页面悬浮等待数据
    });

    try {
      await widget.onRefresh();
    } finally {
      if (mounted) {
        // 先设置刷新完成状态，再开始回弹动画
        setState(() {
          _isRefreshing = false;
        });
        
        // 极短延迟，让UI更新后立即开始弹性回弹
        await Future.delayed(const Duration(milliseconds: 16)); // 一帧的时间
        
        if (mounted) {
          // 开始平滑回弹动画
          await _animateToZero();
        }
      }
    }
  }

  /// 弹性回弹到零位置 - 用于达到阈值后的刷新完成
  Future<void> _animateToZero() async {
    if (_pullDistance <= 0) return;
    
    final startDistance = _pullDistance;
    _bounceController.reset();
    
    // 创建监听器函数以便后续移除
    void animationListener() {
      if (mounted) {
        final currentDistance = startDistance * (1 - _bounceController.value);
        setState(() {
          _pullDistance = currentDistance;
          _canRefresh = currentDistance >= _refreshThreshold;
        });
      }
    }
    
    // 添加监听器
    _bounceController.addListener(animationListener);
    
    try {
      await _bounceController.forward();
    } finally {
      // 清理监听器
      _bounceController.removeListener(animationListener);
      
      if (mounted) {
        setState(() {
          _pullDistance = 0.0;
          _canRefresh = false;
        });
      }
    }
  }

  /// 快速回弹到零位置 - 用于未达到阈值时的快速回弹
  Future<void> _quickAnimateToZero() async {
    if (_pullDistance <= 0) return;
    
    final startDistance = _pullDistance;
    _quickBounceController.reset();
    
    // 创建监听器函数以便后续移除
    void quickAnimationListener() {
      if (mounted) {
        final currentDistance = startDistance * (1 - _quickBounceController.value);
        setState(() {
          _pullDistance = currentDistance;
          _canRefresh = currentDistance >= _refreshThreshold;
        });
      }
    }
    
    // 添加监听器
    _quickBounceController.addListener(quickAnimationListener);
    
    try {
      await _quickBounceController.forward();
    } finally {
      // 清理监听器
      _quickBounceController.removeListener(quickAnimationListener);
      
      if (mounted) {
        setState(() {
          _pullDistance = 0.0;
          _canRefresh = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return NotificationListener<ScrollNotification>(
      onNotification: (notification) {
        // 遵循iOS HIG：只在真正的用户拖拽时响应
        if (notification is ScrollStartNotification) {
          _isDragging = true;
        } else if (notification is ScrollUpdateNotification) {
          // 只在列表顶部且有拖拽手势时响应
          if (notification.metrics.pixels <= 0 && 
              notification.dragDetails != null && 
              _isDragging && 
              !_isRefreshing) {
            final delta = notification.dragDetails!.delta.dy;
            if (delta > 0) {
              setState(() {
                // 优化阻尼曲线：达到阈值前流畅，超过后阻尼增强
                final damping = _pullDistance > _refreshThreshold ? 0.12 : 0.35;
                _pullDistance = (_pullDistance + delta * damping).clamp(0.0, _refreshThreshold * 1.5);
                _canRefresh = _pullDistance >= _refreshThreshold;
              });
            }
          }
        } else if (notification is ScrollEndNotification) {
          if (_isDragging && !_isRefreshing) {
            if (_canRefresh && _pullDistance >= _refreshThreshold) {
              // 情况1：达到阈值，触发刷新 - 页面悬浮等待数据
              _handleRefresh();
            } else {
              // 情况2：未达到阈值，快速回弹消失
              _quickAnimateToZero();
            }
          }
          _isDragging = false;
        }
        return false;
      },
      child: Stack(
        children: [
          // 关键：使用Transform.translate让整个内容跟随下拉距离移动
          Transform.translate(
            offset: Offset(0, _pullDistance),
            child: widget.child,
          ),
          
          // 三个点指示器 - 始终跟随_pullDistance，确保平滑移动
          if (_pullDistance > 0 || _isRefreshing)
            Positioned(
              // 优化位置：让三个点在空白区域居中显示
              top: (_pullDistance * 0.6).clamp(20.h, 80.h),
              left: 0,
              right: 0,
              child: Center(
                child: AnimatedOpacity(
                  duration: const Duration(milliseconds: 150),
                  opacity: _isRefreshing 
                      ? 1.0 // 刷新时完全不透明，清晰显示
                      : (_pullDistance / _refreshThreshold).clamp(0.2, 1.0), // 下拉时从20%开始显示，更早出现
                  child: _isRefreshing 
                      ? const _YgsThreeDotsSpinner() // 刷新时显示动画
                      : const _YgsStaticThreeDots(), // 下拉时显示静态三个点
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// 静态三个点指示器 - 下拉时显示，无动画
class _YgsStaticThreeDots extends StatelessWidget {
  const _YgsStaticThreeDots();

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildDot(),
        SizedBox(width: 8.w),
        _buildDot(),
        SizedBox(width: 8.w),
        _buildDot(),
      ],
    );
  }

  Widget _buildDot() {
    return Container(
      width: 8.w,
      height: 8.w,
      decoration: BoxDecoration(
        color: YgsColors.textSecondary.withOpacity(0.8),
        shape: BoxShape.circle,
      ),
    );
  }
}

/// 动态三个点旋转动画 - 用于刷新加载状态
class _YgsThreeDotsSpinner extends StatefulWidget {
  const _YgsThreeDotsSpinner();

  @override
  State<_YgsThreeDotsSpinner> createState() => _YgsThreeDotsSpinnerState();
}

class _YgsThreeDotsSpinnerState extends State<_YgsThreeDotsSpinner>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1200), // 稍微加快动画节奏
      vsync: this,
    );
    
    _animation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.ease, // 更流畅的动画曲线
    ));

    _controller.repeat(); // 循环播放
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildAnimatedDot(0),
            SizedBox(width: 8.w),
            _buildAnimatedDot(1),
            SizedBox(width: 8.w),
            _buildAnimatedDot(2),
          ],
        );
      },
    );
  }

  Widget _buildAnimatedDot(int index) {
    // 为每个点创建不同的动画延迟，形成流畅的波浪效果
    final delay = index * 0.25;
    final animationValue = ((_animation.value - delay) % 1.0).clamp(0.0, 1.0);
    
    // 优化弹跳曲线，更自然的动画
    final bounce = animationValue < 0.5
        ? 2 * animationValue * animationValue
        : 1 - 2 * (1 - animationValue) * (1 - animationValue);
    
    final scale = 0.7 + bounce * 0.5; // 缩小变化幅度，更精致
    final opacity = 0.5 + bounce * 0.5; // 优化透明度变化
    
    return Transform.scale(
      scale: scale,
      child: Container(
        width: 8.w, // 稍微缩小点的尺寸
        height: 8.w,
        decoration: BoxDecoration(
          color: YgsColors.textPrimary.withOpacity(opacity),
          shape: BoxShape.circle,
        ),
      ),
    );
  }
}