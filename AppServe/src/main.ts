import "reflect-metadata";
import { NestFactory } from "@nestjs/core";
import type { NestFastifyApplication } from "@nestjs/platform-fastify";
import { FastifyAdapter } from "@nestjs/platform-fastify";
import { ConfigService } from "@nestjs/config";
import { ValidationPipe } from "@nestjs/common";
import { DocumentBuilder, SwaggerModule } from "@nestjs/swagger";

import { AppModule } from "./app.module";
import { HttpExceptionFilter } from "./common/filters/http-exception.filter";
import { TransformInterceptor } from "./common/interceptors/transform.interceptor";
import { LoggingInterceptor } from "./common/interceptors/logging.interceptor";
import * as multipart from "@fastify/multipart";

async function bootstrap() {
  // 创建应用实例
  const app = await NestFactory.create<NestFastifyApplication>(
    AppModule,
    new FastifyAdapter({
      logger: true,
      trustProxy: true, // 支持反向代理
    }),
  );

  // 获取配置服务
  const configService = app.get(ConfigService);
  const port = configService.get<number>("PORT", 3000);
  const nodeEnv = configService.get<string>("NODE_ENV", "development");

  // 全局前缀
  app.setGlobalPrefix("api/v1");

  // 注册multipart插件以支持文件上传
  await app.register(multipart, {
    limits: {
      fileSize: 10 * 1024 * 1024, // 10MB
      files: 10, // 最多10个文件
    },
  });

  // Fastify内置安全和压缩功能，无需额外中间件

  // CORS配置
  app.enableCors({
    origin:
      nodeEnv === "production"
        ? configService.get<string>("FRONTEND_URL", "https://story-app.com")
        : true,
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization", "X-Requested-With"],
  });

  // 全局管道
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
      disableErrorMessages: nodeEnv === "production",
    }),
  );

  // 全局过滤器
  app.useGlobalFilters(new HttpExceptionFilter());

  // 全局拦截器
  app.useGlobalInterceptors(
    new LoggingInterceptor(),
    new TransformInterceptor(),
  );

  // Swagger文档配置 (仅开发和测试环境)
  if (nodeEnv !== "production") {
    const config = new DocumentBuilder()
      .setTitle("有故事APP API")
      .setDescription("有故事APP后端API文档 - v1.0.0")
      .setVersion("1.0.0")
      .addTag("认证", "认证相关接口")
      .addTag("用户管理", "用户管理接口")
      .addTag("通知管理", "通知管理接口")
      .addTag("故事管理", "故事管理接口")
      .addTag("故事人物关联", "故事人物关联接口")
      .addTag("人物管理", "人物管理接口")
      .addTag("人物点亮", "人物点亮系统接口")
      .addTag("AI功能", "AI服务接口")
      .addTag("健康检查", "健康检查接口")
      .addBearerAuth(
        {
          type: "http",
          scheme: "bearer",
          bearerFormat: "JWT",
          name: "JWT",
          description: "Enter JWT token",
          in: "header",
        },
        "JWT-auth",
      )
      .build();

    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup("api/docs", app, document, {
      swaggerOptions: {
        persistAuthorization: true,
      },
      jsonDocumentUrl: "api/docs-json",
      yamlDocumentUrl: "api/docs-yaml",
    });

    console.log(`📚 API文档地址: http://localhost:${port}/api/docs`);
    console.log(`📄 OpenAPI JSON: http://localhost:${port}/api/docs-json`);
    console.log(`📄 OpenAPI YAML: http://localhost:${port}/api/docs-yaml`);
  }

  // 启动应用
  await app.listen(port, "0.0.0.0");

  console.log(`🚀 有故事APP后端服务启动成功!`);
  console.log(`📍 服务地址: http://localhost:${port}`);
  console.log(`🌍 环境: ${nodeEnv}`);
  console.log(`📊 健康检查: http://localhost:${port}/api/v1/health`);
}

// 错误处理
process.on("unhandledRejection", (reason, _promise) => {
  console.error("未处理的Promise拒绝:", reason);
  process.exit(1);
});

process.on("uncaughtException", (error) => {
  console.error("未捕获的异常:", error);
  process.exit(1);
});

// 优雅关闭
process.on("SIGTERM", () => {
  console.log("收到SIGTERM信号，正在优雅关闭...");
  process.exit(0);
});

process.on("SIGINT", () => {
  console.log("收到SIGINT信号，正在优雅关闭...");
  process.exit(0);
});

bootstrap();
