#!/usr/bin/env node

/**
 * 查看数据库中所有表的脚本
 * 用于确认生产数据库中的实际表名
 */

const { Client } = require('pg');
require('dotenv').config({ path: '.env.production' });

const dbConfig = {
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT),
  database: process.env.DB_NAME,
  user: process.env.DB_USERNAME,
  password: process.env.DB_PASSWORD,
  ssl: false,
  connectionTimeoutMillis: 30000,
  idleTimeoutMillis: 10000,
};

async function listAllTables() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔗 正在连接到生产数据库...');
    await client.connect();
    console.log('✅ 数据库连接成功');
    
    // 查询所有表
    const result = await client.query(`
      SELECT 
        schemaname,
        tablename,
        tableowner,
        hasindexes,
        hasrules,
        hastriggers
      FROM pg_tables 
      WHERE schemaname NOT IN ('information_schema', 'pg_catalog')
      ORDER BY schemaname, tablename
    `);
    
    console.log(`\n📋 数据库 ${process.env.DB_NAME} 中的所有表:`);
    console.log('=' * 60);
    
    if (result.rows.length === 0) {
      console.log('❌ 数据库中没有发现任何表');
    } else {
      result.rows.forEach((table, index) => {
        console.log(`${(index + 1).toString().padStart(2)}. ${table.schemaname}.${table.tablename}`);
      });
    }
    
    console.log(`\n📊 总表数: ${result.rows.length}`);
    
    return result.rows;
    
  } catch (error) {
    console.error('❌ 查询失败:', error.message);
    throw error;
  } finally {
    await client.end();
    console.log('\n🔐 数据库连接已关闭');
  }
}

if (require.main === module) {
  listAllTables()
    .then(() => {
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 执行失败:', error.message);
      process.exit(1);
    });
}