/**
 * 用户认证和权限管理集成测试 - 企业级安全验证
 *
 * 核心功能验证：
 * 1. 短信验证码发送和验证流程
 * 2. 用户注册和登录完整流程
 * 3. JWT令牌生成和验证
 * 4. 权限控制和角色验证
 * 5. 设备指纹安全机制
 * 6. 验证安全限制和黑名单
 * 7. 会话管理和刷新令牌
 * 8. 企业级安全防护机制
 *
 * 测试场景：
 * - 完整用户注册流程
 * - 登录认证和权限验证
 * - 多设备登录管理
 * - 安全限制和异常处理
 * - VIP用户和普通用户权限差异
 */

import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import type { DataSource } from "typeorm";
import { getDataSourceToken } from "@nestjs/typeorm";
import request from "supertest";
import type { INestApplication } from "@nestjs/common";
import { AppModule } from "../../../src/app.module";
import { IntegrationTestDataFactory } from "../setup/database-setup";
import { User } from "../../../src/modules/users/entities/user.entity";
import { AuthService } from "../../../src/modules/auth/auth.service";
import { SmsService } from "../../../src/modules/auth/services/sms.service";
import { UsersService } from "../../../src/modules/users/users.service";
import { EnhancedRedisService } from "../../../src/common/services/enhanced-redis.service";
import { DeviceFingerprintService } from "../../../src/common/services/device-fingerprint.service";
import { JwtAuthGuard } from "../../../src/modules/auth/guards/jwt-auth.guard";

describe("User Authentication Workflow - Enterprise Integration Tests", () => {
  let app: INestApplication;
  let module: TestingModule;
  let dataSource: DataSource;
  let dataFactory: IntegrationTestDataFactory;
  let testAuthService: AuthService;
  let testSmsService: SmsService;
  let testUsersService: UsersService;
  let testRedisService: EnhancedRedisService;
  let testDeviceFingerprintService: DeviceFingerprintService;

  // 测试数据
  let testUser: User;
  let testVipUser: User;
  const testPhoneNumber = "13800138001";
  const vipPhoneNumber = "13800138888";
  const testPassword = "Test123456!";

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({
        canActivate: jest.fn().mockReturnValue(true),
      })
      .compile();

    app = module.createNestApplication();
    await app.init();

    dataSource = module.get<DataSource>(getDataSourceToken());
    dataFactory = new IntegrationTestDataFactory(dataSource);
    testAuthService = module.get<AuthService>(AuthService);
    testSmsService = module.get<SmsService>(SmsService);
    testUsersService = module.get<UsersService>(UsersService);
    testRedisService = module.get<EnhancedRedisService>(EnhancedRedisService);
    testDeviceFingerprintService = module.get<DeviceFingerprintService>(
      DeviceFingerprintService,
    );

    // 清理测试环境
    await dataFactory.clearDatabase();
  });

  afterAll(async () => {
    await dataFactory.clearDatabase();
    await app.close();
  });

  beforeEach(async () => {
    // 为每个测试创建干净的数据环境
    await dataFactory.clearTestData();
  });

  describe("🔐 完整用户注册流程", () => {
    it("should complete full user registration with SMS verification", async () => {
      // 1. 发送验证码
      const smsResponse = await request(app.getHttpServer())
        .post("/auth/send-code")
        .send({
          phone: testPhoneNumber,
        })
        .expect(201);

      expect(smsResponse.body.success).toBe(true);
      expect(smsResponse.body.message).toContain("验证码发送成功");

      // 2. 模拟验证码验证（测试环境使用Mock验证码）
      const registerResponse = await request(app.getHttpServer())
        .post("/auth/register")
        .send({
          phone: testPhoneNumber,
          code: "123456", // Mock验证码
          password: testPassword,
          confirmPassword: testPassword,
        })
        .expect(201);

      expect(registerResponse.body.success).toBe(true);
      expect(registerResponse.body.data.user).toBeDefined();
      expect(registerResponse.body.data.user.phone).toBe(testPhoneNumber);
      expect(registerResponse.body.data.accessToken).toBeDefined();
      expect(registerResponse.body.data.refreshToken).toBeDefined();

      // 3. 验证用户已创建
      const user = await usersService.findByPhone(testPhoneNumber);
      expect(user).toBeDefined();
      expect(user.phone).toBe(testPhoneNumber);
      expect(user.isPhoneVerified).toBe(true);

      testUser = user;
    });

    it("should handle registration with existing phone number", async () => {
      // 先注册一个用户
      await dataFactory.createUser({
        phone: testPhoneNumber,
        password: testPassword,
        isPhoneVerified: true,
      });

      // 尝试用相同手机号注册
      await request(app.getHttpServer())
        .post("/auth/register")
        .send({
          phone: testPhoneNumber,
          code: "123456",
          password: testPassword,
          confirmPassword: testPassword,
        })
        .expect(400);
    });

    it("should validate password strength requirements", async () => {
      await request(app.getHttpServer())
        .post("/auth/send-code")
        .send({
          phone: "13800138002",
        })
        .expect(201);

      // 弱密码测试
      await request(app.getHttpServer())
        .post("/auth/register")
        .send({
          phone: "13800138002",
          code: "123456",
          password: "123", // 弱密码
          confirmPassword: "123",
        })
        .expect(400);
    });
  });

  describe("🔑 用户登录认证流程", () => {
    beforeEach(async () => {
      // 创建测试用户
      testUser = await dataFactory.createUser({
        phone: testPhoneNumber,
        password: testPassword,
        isPhoneVerified: true,
      });
    });

    it("should login successfully with correct credentials", async () => {
      const loginResponse = await request(app.getHttpServer())
        .post("/auth/login")
        .send({
          phone: testPhoneNumber,
          password: testPassword,
        })
        .expect(201);

      expect(loginResponse.body.success).toBe(true);
      expect(loginResponse.body.data.accessToken).toBeDefined();
      expect(loginResponse.body.data.refreshToken).toBeDefined();
      expect(loginResponse.body.data.user.id).toBe(testUser.id);

      // 验证JWT令牌有效性
      const userProfile = await request(app.getHttpServer())
        .get("/users/profile")
        .set("Authorization", `Bearer ${loginResponse.body.data.accessToken}`)
        .expect(200);

      expect(userProfile.body.data.id).toBe(testUser.id);
    });

    it("should reject login with incorrect password", async () => {
      await request(app.getHttpServer())
        .post("/auth/login")
        .send({
          phone: testPhoneNumber,
          password: "wrongpassword",
        })
        .expect(401);
    });

    it("should reject login with unverified phone", async () => {
      // 创建未验证手机号的用户
      const unverifiedUser = await dataFactory.createUser({
        phone: "***********",
        password: testPassword,
        isPhoneVerified: false,
      });

      await request(app.getHttpServer())
        .post("/auth/login")
        .send({
          phone: unverifiedUser.phone,
          password: testPassword,
        })
        .expect(403);
    });

    it("should handle account lockout after multiple failed attempts", async () => {
      // 多次错误登录尝试
      for (let i = 0; i < 5; i++) {
        await request(app.getHttpServer())
          .post("/auth/login")
          .send({
            phone: testPhoneNumber,
            password: "wrongpassword",
          })
          .expect(401);
      }

      // 第6次应该被锁定
      await request(app.getHttpServer())
        .post("/auth/login")
        .send({
          phone: testPhoneNumber,
          password: "wrongpassword",
        })
        .expect(429); // Too Many Requests
    });
  });

  describe("🔄 令牌刷新和会话管理", () => {
    let accessToken: string;
    let refreshToken: string;

    beforeEach(async () => {
      testUser = await dataFactory.createUser({
        phone: testPhoneNumber,
        password: testPassword,
        isPhoneVerified: true,
      });

      const loginResponse = await request(app.getHttpServer())
        .post("/auth/login")
        .send({
          phone: testPhoneNumber,
          password: testPassword,
        })
        .expect(201);

      accessToken = loginResponse.body.data.accessToken;
      refreshToken = loginResponse.body.data.refreshToken;
    });

    it("should refresh tokens successfully", async () => {
      const refreshResponse = await request(app.getHttpServer())
        .post("/auth/refresh")
        .send({
          refreshToken,
        })
        .expect(201);

      expect(refreshResponse.body.success).toBe(true);
      expect(refreshResponse.body.data.accessToken).toBeDefined();
      expect(refreshResponse.body.data.refreshToken).toBeDefined();
      expect(refreshResponse.body.data.accessToken).not.toBe(accessToken);

      // 验证新令牌有效
      await request(app.getHttpServer())
        .get("/users/profile")
        .set("Authorization", `Bearer ${refreshResponse.body.data.accessToken}`)
        .expect(200);
    });

    it("should reject invalid refresh token", async () => {
      await request(app.getHttpServer())
        .post("/auth/refresh")
        .send({
          refreshToken: "invalid-token",
        })
        .expect(401);
    });

    it("should logout and invalidate tokens", async () => {
      // 登出
      await request(app.getHttpServer())
        .post("/auth/logout")
        .set("Authorization", `Bearer ${accessToken}`)
        .expect(200);

      // 验证令牌已失效
      await request(app.getHttpServer())
        .get("/users/profile")
        .set("Authorization", `Bearer ${accessToken}`)
        .expect(401);
    });
  });

  describe("👤 VIP用户特权验证", () => {
    beforeEach(async () => {
      testVipUser = await dataFactory.createUser({
        phone: vipPhoneNumber,
        password: testPassword,
        isPhoneVerified: true,
        isVipUser: true,
      });
    });

    it("should provide VIP-specific features", async () => {
      const loginResponse = await request(app.getHttpServer())
        .post("/auth/login")
        .send({
          phone: vipPhoneNumber,
          password: testPassword,
        })
        .expect(201);

      const vipProfile = await request(app.getHttpServer())
        .get("/users/profile")
        .set("Authorization", `Bearer ${loginResponse.body.data.accessToken}`)
        .expect(200);

      expect(vipProfile.body.data.isVipUser).toBe(true);

      // VIP用户应该有更高的AI配额
      const vipQuota = await request(app.getHttpServer())
        .get("/ai/quota")
        .set("Authorization", `Bearer ${loginResponse.body.data.accessToken}`)
        .expect(200);

      expect(vipQuota.body.data.dailyLimit).toBeGreaterThan(10); // VIP用户配额更高
    });

    it("should allow VIP users to bypass certain rate limits", async () => {
      // VIP用户应该可以更频繁地发送短信验证码
      const loginResponse = await request(app.getHttpServer())
        .post("/auth/login")
        .send({
          phone: vipPhoneNumber,
          password: testPassword,
        })
        .expect(201);

      // 连续发送多个验证码请求
      for (let i = 0; i < 3; i++) {
        await request(app.getHttpServer())
          .post("/auth/send-code")
          .set("Authorization", `Bearer ${loginResponse.body.data.accessToken}`)
          .send({
            phone: `1380013800${i}`,
          })
          .expect(201);
      }
    });
  });

  describe("🛡️ 安全防护机制验证", () => {
    beforeEach(async () => {
      testUser = await dataFactory.createUser({
        phone: testPhoneNumber,
        password: testPassword,
        isPhoneVerified: true,
      });
    });

    it("should detect and handle suspicious device fingerprints", async () => {
      const suspiciousHeaders = {
        "User-Agent": "SuspiciousBot/1.0",
        "X-Forwarded-For": "127.0.0.1",
        "X-Real-IP": "*************",
      };

      // 可疑设备登录可能被额外验证
      const response = await request(app.getHttpServer())
        .post("/auth/login")
        .set(suspiciousHeaders)
        .send({
          phone: testPhoneNumber,
          password: testPassword,
        });

      // 根据安全策略，可能需要额外验证或被阻止
      expect([200, 201, 403, 429]).toContain(response.status);
    });

    it("should handle blacklisted phone numbers", async () => {
      // 模拟将号码加入黑名单
      await redisService.sadd("sms:blacklist", "13800138999");

      await request(app.getHttpServer())
        .post("/auth/send-code")
        .send({
          phone: "13800138999",
        })
        .expect(403); // 黑名单号码应该被拒绝
    });

    it("should enforce SMS rate limiting", async () => {
      // 快速连续发送多个验证码请求
      const requests = [];
      for (let i = 0; i < 5; i++) {
        requests.push(
          request(app.getHttpServer())
            .post("/auth/send-code")
            .send({
              phone: `1380013800${i}`,
            }),
        );
      }

      const responses = await Promise.all(requests);

      // 至少有一些请求应该被速率限制拒绝
      const rejectedCount = responses.filter(
        (r) => r.status === 429 || r.status === 400,
      ).length;
      expect(rejectedCount).toBeGreaterThan(0);
    });
  });

  describe("🔧 权限控制和API访问", () => {
    let userToken: string;
    let unauthorizedToken: string;

    beforeEach(async () => {
      testUser = await dataFactory.createUser({
        phone: testPhoneNumber,
        password: testPassword,
        isPhoneVerified: true,
      });

      const loginResponse = await request(app.getHttpServer())
        .post("/auth/login")
        .send({
          phone: testPhoneNumber,
          password: testPassword,
        })
        .expect(201);

      userToken = loginResponse.body.data.accessToken;
      unauthorizedToken = "invalid.jwt.token";
    });

    it("should allow authenticated users to access protected endpoints", async () => {
      await request(app.getHttpServer())
        .get("/users/profile")
        .set("Authorization", `Bearer ${userToken}`)
        .expect(200);

      await request(app.getHttpServer())
        .get("/ai/quota")
        .set("Authorization", `Bearer ${userToken}`)
        .expect(200);
    });

    it("should reject unauthenticated requests to protected endpoints", async () => {
      await request(app.getHttpServer()).get("/users/profile").expect(401);

      await request(app.getHttpServer()).get("/ai/quota").expect(401);
    });

    it("should reject invalid JWT tokens", async () => {
      await request(app.getHttpServer())
        .get("/users/profile")
        .set("Authorization", `Bearer ${unauthorizedToken}`)
        .expect(401);
    });

    it("should validate JWT token structure and claims", async () => {
      // 使用过期或格式错误的令牌
      const malformedTokens = [
        "Bearer malformed-token",
        "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.invalid",
        "", // 空令牌
      ];

      for (const token of malformedTokens) {
        await request(app.getHttpServer())
          .get("/users/profile")
          .set("Authorization", token)
          .expect(401);
      }
    });
  });

  describe("📊 用户活动追踪和统计", () => {
    beforeEach(async () => {
      testUser = await dataFactory.createUser({
        phone: testPhoneNumber,
        password: testPassword,
        isPhoneVerified: true,
      });
    });

    it("should track user login activity", async () => {
      const beforeLogin = new Date();

      await request(app.getHttpServer())
        .post("/auth/login")
        .send({
          phone: testPhoneNumber,
          password: testPassword,
        })
        .expect(201);

      // 验证登录时间更新
      const updatedUser = await usersService.findByPhone(testPhoneNumber);
      expect(updatedUser.lastLoginAt).toBeDefined();
      expect(updatedUser.lastLoginAt.getTime()).toBeGreaterThanOrEqual(
        beforeLogin.getTime(),
      );
    });

    it("should maintain login statistics", async () => {
      // 多次登录
      for (let i = 0; i < 3; i++) {
        await request(app.getHttpServer())
          .post("/auth/login")
          .send({
            phone: testPhoneNumber,
            password: testPassword,
          })
          .expect(201);
      }

      // 验证登录统计
      const userStats = await usersService.getUserStats(testUser.id);
      expect(userStats.loginCount).toBeGreaterThanOrEqual(3);
    });
  });

  describe("🔄 数据完整性和事务处理", () => {
    it("should maintain data consistency during registration", async () => {
      const registrationData = {
        phone: "13800138010",
        code: "123456",
        password: testPassword,
        confirmPassword: testPassword,
      };

      // 先发送验证码
      await request(app.getHttpServer())
        .post("/auth/send-code")
        .send({
          phone: registrationData.phone,
        })
        .expect(201);

      // 注册用户
      await request(app.getHttpServer())
        .post("/auth/register")
        .send(registrationData)
        .expect(201);

      // 验证用户数据完整性
      const user = await usersService.findByPhone(registrationData.phone);
      expect(user).toBeDefined();
      expect(user.phone).toBe(registrationData.phone);
      expect(user.isPhoneVerified).toBe(true);
      expect(user.createdAt).toBeDefined();
      expect(user.updatedAt).toBeDefined();

      // 验证没有重复记录
      const userCount = await dataSource
        .getRepository(User)
        .count({ where: { phone: registrationData.phone } });
      expect(userCount).toBe(1);
    });

    it("should rollback transaction on registration failure", async () => {
      // 模拟注册过程中的错误（如数据库约束失败）
      const invalidData = {
        phone: "invalid-phone-format",
        code: "123456",
        password: testPassword,
        confirmPassword: testPassword,
      };

      await request(app.getHttpServer())
        .post("/auth/register")
        .send(invalidData)
        .expect(400);

      // 验证没有创建不完整的用户记录
      const userCount = await dataSource
        .getRepository(User)
        .count({ where: { phone: invalidData.phone } });
      expect(userCount).toBe(0);
    });
  });
});
