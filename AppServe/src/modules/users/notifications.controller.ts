import {
  Controller,
  Get,
  Put,
  Delete,
  Param,
  Query,
  UseGuards,
  Request,
  Body,
  ValidationPipe,
  ParseUUIDPipe,
  Post,
} from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse as SwaggerApiResponse,
  ApiBearerAuth,
  ApiParam,
} from "@nestjs/swagger";
import { NotificationsService } from "./notifications.service";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import {
  NotificationQueryDto,
  BatchNotificationDto,
} from "./dto/notification.dto";
import type { PaginatedResponse } from "../../common/types/request.types";
import { AuthRequest } from "../../common/types/request.types";
import { ApiResponse } from "../../common/dto/api-response.dto";

// 临时类型定义
interface NotificationResponseDto {
  id: string;
  type: string;
  title: string;
  content: string;
  isRead: boolean;
  createdAt: Date;
}

/**
 * 通知控制器
 * 提供通知的查询、标记已读、删除等API
 */
@ApiTags("通知管理")
@Controller("notifications")
@UseGuards(JwtAuthGuard)
export class NotificationsController {
  constructor(private readonly notificationsService: NotificationsService) {}

  /**
   * 获取当前用户的通知列表
   */
  @Get()
  @ApiOperation({ summary: "获取通知列表" })
  @SwaggerApiResponse({ status: 200, description: "获取成功" })
  @ApiBearerAuth()
  async getNotifications(
    @Query() query: NotificationQueryDto,
    @Request() req: AuthRequest,
  ): Promise<ApiResponse<PaginatedResponse<NotificationResponseDto>>> {
    const page = query.page ? parseInt(query.page) : 1;
    const limit = query.limit ? parseInt(query.limit) : 20;
    const unreadOnly = query.unreadOnly === "true";

    const result = await this.notificationsService.getUserNotifications(
      req.user.id,
      page,
      limit,
      unreadOnly,
    );

    const totalPages = Math.ceil(result.total / limit);
    const paginatedData = {
      data: result.notifications,
      page,
      limit,
      total: result.total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };

    return ApiResponse.success(paginatedData, "获取通知列表成功", 200);
  }

  /**
   * 获取未读通知数量
   */
  @Get("unread-count")
  @ApiOperation({ summary: "获取未读通知数量" })
  @SwaggerApiResponse({ status: 200, description: "获取成功" })
  @ApiBearerAuth()
  async getUnreadCount(@Request() req: AuthRequest) {
    const count = await this.notificationsService.getUnreadCount(req.user.id);

    return {
      statusCode: 200,
      timestamp: Date.now(),
      message: "获取未读通知数量成功",
      data: { unreadCount: count },
    };
  }

  /**
   * 标记通知为已读
   */
  @Put(":id/read")
  @ApiOperation({ summary: "标记通知为已读" })
  @ApiParam({ name: "id", description: "通知ID", type: String })
  @SwaggerApiResponse({ status: 200, description: "标记成功" })
  @ApiBearerAuth()
  async markAsRead(
    @Param("id", ParseUUIDPipe) notificationId: string,
    @Request() req: AuthRequest,
  ): Promise<ApiResponse<{ message: string }>> {
    await this.notificationsService.markAsRead(notificationId, req.user.id);

    return ApiResponse.success(
      { message: "通知标记为已读成功" },
      "通知标记为已读成功",
      200,
    );
  }

  /**
   * 标记所有通知为已读
   */
  @Put("mark-all-read")
  @ApiOperation({ summary: "标记所有通知为已读" })
  @SwaggerApiResponse({ status: 200, description: "标记成功" })
  @ApiBearerAuth()
  async markAllAsRead(
    @Request() req: AuthRequest,
  ): Promise<ApiResponse<{ message: string }>> {
    await this.notificationsService.markAllAsRead(req.user.id);

    return ApiResponse.success(
      { message: "所有通知标记为已读成功" },
      "所有通知标记为已读成功",
      200,
    );
  }

  /**
   * 删除通知
   */
  @Delete(":id")
  @ApiOperation({ summary: "删除通知" })
  @ApiParam({ name: "id", description: "通知ID", type: String })
  @SwaggerApiResponse({ status: 200, description: "删除成功" })
  @ApiBearerAuth()
  async deleteNotification(
    @Param("id", ParseUUIDPipe) notificationId: string,
    @Request() req: AuthRequest,
  ): Promise<ApiResponse<{ message: string }>> {
    await this.notificationsService.deleteNotification(
      notificationId,
      req.user.id,
    );

    return ApiResponse.success(
      { message: "通知删除成功" },
      "通知删除成功",
      200,
    );
  }

  /**
   * 批量删除通知
   */
  @Post("batch-delete")
  @ApiOperation({ summary: "批量删除通知" })
  @SwaggerApiResponse({ status: 200, description: "批量删除成功" })
  @ApiBearerAuth()
  async batchDeleteNotifications(
    @Body(ValidationPipe) batchData: BatchNotificationDto,
    @Request() req: AuthRequest,
  ) {
    await this.notificationsService.deleteNotifications(
      batchData.notificationIds,
      req.user.id,
    );

    return {
      statusCode: 200,
      timestamp: Date.now(),
      message: `成功删除 ${batchData.notificationIds.length} 条通知`,
    };
  }

  /**
   * 清理过期通知
   * 仅管理员可调用
   */
  @Post("cleanup")
  @ApiOperation({ summary: "清理过期通知" })
  @SwaggerApiResponse({ status: 200, description: "清理成功" })
  @ApiBearerAuth()
  async cleanupOldNotifications() {
    await this.notificationsService.cleanupOldNotifications();

    return {
      statusCode: 200,
      timestamp: Date.now(),
      message: "过期通知清理完成",
    };
  }
}
