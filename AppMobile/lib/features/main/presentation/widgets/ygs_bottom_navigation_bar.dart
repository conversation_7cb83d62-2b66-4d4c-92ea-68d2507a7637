import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// YGS底部导航栏 - 严格按照Figma设计稿实现
class YgsBottomNavigationBar extends StatelessWidget {
  const YgsBottomNavigationBar({
    required this.currentIndex,
    required this.onTap,
    super.key,
  });

  final int currentIndex;
  final ValueChanged<int> onTap;

  // Tab配置
  static const List<_TabConfig> _tabs = [
    _TabConfig(
      label: '首页',
      activeIcon: 'assets/icons/tab/tab-home-active.png',
      defaultIcon: 'assets/icons/tab/tab-home-default.png',
    ),
    _TabConfig(
      label: '故事',
      activeIcon: 'assets/icons/tab/tab-story-active.png',
      defaultIcon: 'assets/icons/tab/tab-story-default.png',
    ),
    _TabConfig(
      label: '消息',
      activeIcon: 'assets/icons/tab/tab-news-active.png',
      defaultIcon: 'assets/icons/tab/tab-news-default.png',
    ),
    _TabConfig(
      label: '我的',
      activeIcon: 'assets/icons/tab/tab-my-active.png',
      defaultIcon: 'assets/icons/tab/tab-my-default.png',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return ClipRect(
      child: BackdropFilter(
        // 严格按照Figma设计稿: backdrop-blur-[10px]
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: Container(
          height: 160.h,
          width: 750.w,
          decoration: BoxDecoration(
            // 严格按照Figma设计稿: bg-[rgba(246,248,252,0.8)]
            color: const Color.fromRGBO(246, 248, 252, 0.8),
            border: Border(
              top: BorderSide(
                color: Colors.white.withOpacity(0.2),
                width: 0.5.w,
              ),
            ),
          ),
          child: Padding(
            padding: EdgeInsets.only(
              left: 40.w,
              right: 40.w,
              top: 0.h, // 使用正值的padding来控制上边距
              bottom: 60.h, // 增大底部边距，让内容整体上移
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: List.generate(4, (index) => _buildTabItem(index)),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTabItem(int index) {
    final config = _tabs[index];
    final isActive = currentIndex == index;
    
    return GestureDetector(
      onTap: () => onTap(index),
      behavior: HitTestBehavior.opaque,
      child: SizedBox(
        width: 100.w, // 稍微减小单个tab宽度，为间隔让出空间
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 图标区域 - 调整为合适大小
            SizedBox(
              width: 62.w,  // 从72.w调整为62.w
              height: 62.w, // 从72.w调整为62.w
              child: Image.asset(
                isActive ? config.activeIcon : config.defaultIcon,
                width: 62.w,
                height: 62.w,
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) {
                  return Icon(
                    _getBackupIcon(index),
                    size: 62.w,
                    color: isActive 
                        ? const Color(0xFF151523) 
                        : const Color(0xFF868691),
                  );
                },
              ),
            ),
            
            SizedBox(height: 6.h), // 图标和文字之间的间距
            
            // 文字区域 - 调整为合适字体大小
            Text(
              config.label,
              style: TextStyle(
                fontFamily: 'SF Pro',
                fontSize: 20.sp, // 从22.sp调整回20.sp
                fontWeight: isActive ? FontWeight.w600 : FontWeight.w400,
                color: isActive 
                    ? const Color(0xFF151523)
                    : const Color(0xFF868691),
                height: 1.0,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  IconData _getBackupIcon(int index) {
    switch (index) {
      case 0: return Icons.home;
      case 1: return Icons.menu_book;
      case 2: return Icons.chat_bubble_outline;
      case 3: return Icons.person_outline;
      default: return Icons.circle;
    }
  }
}

class _TabConfig {
  const _TabConfig({
    required this.label,
    required this.activeIcon,
    required this.defaultIcon,
  });

  final String label;
  final String activeIcon;
  final String defaultIcon;
}