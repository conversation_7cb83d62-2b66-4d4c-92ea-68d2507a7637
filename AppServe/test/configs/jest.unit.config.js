/**
 * 企业级单元测试配置
 * 完全隔离，零外部依赖，纯粹的单元测试
 * 
 * @version 1.0.0
 * <AUTHOR>
 * @date 2025-07-20
 */

module.exports = {
  // 测试项目标识
  displayName: {
    name: 'UNIT TESTS',
    color: 'green',
  },
  
  // 基础配置
  rootDir: '../..',
  testEnvironment: 'node',
  preset: 'ts-jest',
  
  // TypeScript配置
  transform: {
    '^.+\\.ts$': 'ts-jest',
  },
  globals: {
    'ts-jest': {
      tsconfig: '<rootDir>/tsconfig.test.json',
      diagnostics: {
        ignoreCodes: [151001]
      },
      // 🚀 性能优化：关闭类型检查
      isolatedModules: true,
      useESM: false
    }
  },
  
  // 测试文件匹配 - 只匹配src目录下的单元测试
  testMatch: [
    '<rootDir>/src/**/*.spec.ts'
  ],
  
  // 测试环境设置 - 企业级Mock环境
  setupFilesAfterEnv: [
    '<rootDir>/test/configs/unit.setup.ts'
  ],
  
  // 覆盖率配置 - 企业级标准
  collectCoverage: true,
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.spec.ts',
    '!src/**/*.interface.ts',
    '!src/**/*.dto.ts',
    '!src/**/*.entity.ts',
    '!src/main.ts',
    '!src/migrations/**/*',
    '!src/scripts/**/*',
    '!src/**/*.module.ts', // 暂时排除，Module测试属于集成测试
  ],
  
  // 企业级覆盖率阈值
  coverageThreshold: {
    global: {
      branches: 85,
      functions: 90,
      lines: 85,
      statements: 85,
    },
    // 核心业务模块更高标准
    'src/modules/lighting/': {
      branches: 88,
      functions: 95,
      lines: 90,
      statements: 90,
    },
    'src/modules/auth/': {
      branches: 90,
      functions: 95,
      lines: 95,
      statements: 95,
    },
    'src/modules/ai/': {
      branches: 85,
      functions: 90,
      lines: 85,
      statements: 85,
    },
  },
  
  // 覆盖率报告
  coverageReporters: [
    'text-summary',
    'html',
    'lcov',
    'json-summary'
  ],
  coverageDirectory: 'coverage/unit',
  
  // 模块映射
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@common/(.*)$': '<rootDir>/src/common/$1',
    '^@modules/(.*)$': '<rootDir>/src/modules/$1',
    '^@config/(.*)$': '<rootDir>/src/config/$1',
  },
  
  // 🚀 性能配置优化
  testTimeout: 10000,  // 增加超时时间应对复杂测试
  maxWorkers: 1,       // 串行执行避免资源竞争
  maxConcurrency: 1,   // 限制并发数
  
  // 质量配置
  clearMocks: true,
  restoreMocks: true,
  resetMocks: true,
  
  // 错误处理
  errorOnDeprecated: true,
  verbose: true,
  
  // 缓存配置
  cache: true,
  cacheDirectory: '<rootDir>/node_modules/.cache/jest-unit',
  
  // 环境变量
  testEnvironmentOptions: {
    NODE_ENV: 'unit-test',
  },
  
  // 忽略模式
  testPathIgnorePatterns: [
    '<rootDir>/node_modules/',
    '<rootDir>/dist/',
    '<rootDir>/test/integration/',
    '<rootDir>/test/e2e/',
  ],
  
  // 监听模式忽略
  watchPathIgnorePatterns: [
    '<rootDir>/node_modules/',
    '<rootDir>/dist/',
    '<rootDir>/coverage/',
  ],
  
  // 企业级输出格式
  reporters: [
    'default',
    ['jest-junit', {
      outputDirectory: 'test-reports/unit',
      outputName: 'unit-junit.xml',
      classNameTemplate: '{classname}',
      titleTemplate: '{title}',
      ancestorSeparator: ' › ',
      usePathForSuiteName: true
    }]
  ],
  
  // 强制退出 - 确保测试完全隔离
  forceExit: true,
  detectOpenHandles: true,
};