import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import { JwtService } from "@nestjs/jwt";
import { ConfigService } from "@nestjs/config";
import { CharactersService } from "./characters.service";
import { CharactersController } from "./characters.controller";
import { Character } from "./entities/character.entity";
import { CharacterLighting } from "./entities/character-lighting.entity";
import { LightRequest } from "./entities/light-request.entity";
import { User } from "../users/entities/user.entity";
import { Story } from "../stories/entities/story.entity";
import { SmsService } from "../auth/services/sms.service";
import { AuthService } from "../auth/auth.service";

/**
 * 人物模块测试 - v1.0.0
 * 测试人物模块的基本实例化和依赖注入
 */
describe("CharactersModule", () => {
  let module: TestingModule;
  let charactersService: CharactersService;
  let charactersController: CharactersController;

  // Mock Repository基础方法
  const mockRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
    findOneBy: jest.fn(),
    save: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    createQueryBuilder: jest.fn(() => ({
      select: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      getMany: jest.fn(),
      getOne: jest.fn(),
      leftJoinAndSelect: jest.fn().mockReturnThis(),
      innerJoinAndSelect: jest.fn().mockReturnThis(),
      leftJoin: jest.fn().mockReturnThis(),
      groupBy: jest.fn().mockReturnThis(),
      having: jest.fn().mockReturnThis(),
    })),
  };

  // Mock JWT服务
  const mockJwtService = {
    signAsync: jest.fn(),
    sign: jest.fn(),
    verify: jest.fn(),
  };

  // Mock配置服务
  const mockConfigService = {
    get: jest.fn((key: string) => {
      const config: Record<string, string> = {
        JWT_SECRET: "test-secret",
        JWT_EXPIRES_IN: "2700",
        JWT_REFRESH_EXPIRES_IN: "1209600",
        JWT_REFRESH_SECRET: "test-refresh-secret",
        BCRYPT_ROUNDS: "12",
      };
      return config[key];
    }),
  };

  // Mock SMS服务
  const mockSmsService = {
    sendVerificationCode: jest.fn(),
    verifyCode: jest.fn(),
  };

  beforeEach(async () => {
    module = await Test.createTestingModule({
      providers: [
        CharactersService,
        CharactersController,
        {
          provide: getRepositoryToken(Character),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(CharacterLighting),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(LightRequest),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(User),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(Story),
          useValue: mockRepository,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: SmsService,
          useValue: mockSmsService,
        },
        // Mock AuthService (JwtAuthGuard dependency)
        {
          provide: AuthService,
          useValue: {
            validateUser: jest.fn(),
            login: jest.fn(),
            register: jest.fn(),
          },
        },
      ],
    }).compile();

    charactersService = module.get<CharactersService>(CharactersService);
    charactersController =
      module.get<CharactersController>(CharactersController);
  });

  afterEach(async () => {
    if (module) {
      await module.close();
    }
    jest.clearAllMocks();
  });

  describe("模块实例化", () => {
    it("应该能够正确实例化模块", () => {
      expect(module).toBeDefined();
    });

    it("应该正确提供 CharactersService", () => {
      expect(charactersService).toBeDefined();
      expect(charactersService).toBeInstanceOf(CharactersService);
    });

    it("应该正确提供 CharactersController", () => {
      expect(charactersController).toBeDefined();
      expect(charactersController).toBeInstanceOf(CharactersController);
    });
  });

  describe("模块依赖注入", () => {
    it("应该正确注入所有 Repository 依赖", () => {
      const characterRepo = module.get(getRepositoryToken(Character));
      const characterLightingRepo = module.get(
        getRepositoryToken(CharacterLighting),
      );
      const lightRequestRepo = module.get(getRepositoryToken(LightRequest));
      const userRepo = module.get(getRepositoryToken(User));
      const storyRepo = module.get(getRepositoryToken(Story));

      expect(characterRepo).toBeDefined();
      expect(characterLightingRepo).toBeDefined();
      expect(lightRequestRepo).toBeDefined();
      expect(userRepo).toBeDefined();
      expect(storyRepo).toBeDefined();
    });

    it("应该正确注入外部模块服务", () => {
      const jwtService = module.get<JwtService>(JwtService);
      const configService = module.get<ConfigService>(ConfigService);

      expect(jwtService).toBeDefined();
      expect(configService).toBeDefined();
    });
  });

  describe("模块导出", () => {
    it("应该正确导出 CharactersService", () => {
      // 验证服务可以被外部模块使用
      expect(charactersService).toBeDefined();
      expect(charactersService).toBeInstanceOf(CharactersService);
    });
  });

  describe("模块配置", () => {
    it("应该正确配置 TypeORM 实体", () => {
      // 验证模块能够正确配置所有必要的数据库实体
      expect(module.get(getRepositoryToken(Character))).toBeDefined();
      expect(module.get(getRepositoryToken(CharacterLighting))).toBeDefined();
      expect(module.get(getRepositoryToken(LightRequest))).toBeDefined();
      expect(module.get(getRepositoryToken(User))).toBeDefined();
      expect(module.get(getRepositoryToken(Story))).toBeDefined();
    });

    it("应该正确处理 forwardRef 依赖", () => {
      // 验证循环依赖处理正确
      const jwtService = module.get<JwtService>(JwtService);
      expect(jwtService).toBeDefined();
    });
  });

  describe("业务功能验证", () => {
    it("应该支持人物创建和管理功能", () => {
      expect(charactersService).toBeDefined();
      expect(charactersController).toBeDefined();

      // 验证控制器和服务都已正确实例化，支持人物管理功能
      expect(typeof charactersService.createCharacter).toBe("function");
      expect(typeof charactersService.findById).toBe("function");
      expect(typeof charactersService.updateCharacter).toBe("function");
      expect(typeof charactersService.deleteCharacter).toBe("function");
    });

    it("应该支持人物点亮状态管理", () => {
      // 验证人物点亮相关的Repository已正确注入
      const characterLightingRepo = module.get(
        getRepositoryToken(CharacterLighting),
      );
      const lightRequestRepo = module.get(getRepositoryToken(LightRequest));

      expect(characterLightingRepo).toBeDefined();
      expect(lightRequestRepo).toBeDefined();
    });
  });
});
