import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { Logger } from "@nestjs/common";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { AiController } from "./ai.controller";
import { AiService } from "./ai.service";
import type {
  GenerateTitleDto,
  GenerateContentDto,
  QuotaStatusResponseDto,
  UsageStatisticsResponseDto,
} from "./dto";
/* eslint-disable @typescript-eslint/no-explicit-any */
// AuthenticatedRequest类型将通过any来绕过类型检查

describe("AiController", () => {
  let controller: AiController;
  let mockAiService: jest.Mocked<AiService>;

  // Mock数据
  const mockUser = {
    id: "user-1",
    username: "testuser",
    email: "<EMAIL>",
  };
  // 创建一个符合AuthenticatedRequest接口的Mock对象
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const mockRequest = {
    user: mockUser,
  } as any;

  const mockQuotaStatus: QuotaStatusResponseDto = {
    remainingQuota: 2,
    totalDailyQuota: 3,
    usedQuota: 1,
    resetTime: new Date("2025-07-18T00:00:00.000Z"),
  };

  const mockUsageStatistics: UsageStatisticsResponseDto = {
    period: 30,
    totalUsage: 15,
    successfulUsage: 14,
    failureRate: 6.67,
    totalCost: 0.45,
    averageCostPerRequest: 0.03,
    usageByFeature: {
      titleGeneration: 8,
      contentGeneration: 7,
    },
  };

  beforeEach(async () => {
    const mockServiceMethods = {
      generateStoryTitle: jest.fn(),
      generateStoryContent: jest.fn(),
      getRemainingQuota: jest.fn(),
      getUserUsageStatistics: jest.fn(),
      resetDailyQuota: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [AiController],
      providers: [
        {
          provide: AiService,
          useValue: mockServiceMethods,
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({ canActivate: jest.fn(() => true) })
      .compile();

    controller = module.get<AiController>(AiController);
    mockAiService = module.get(AiService) as jest.Mocked<AiService>;

    // Mock Logger to avoid console output during tests
    jest.spyOn(Logger.prototype, "log").mockImplementation(() => {});
    jest.spyOn(Logger.prototype, "debug").mockImplementation(() => {});
    jest.spyOn(Logger.prototype, "error").mockImplementation(() => {});
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("generateTitle", () => {
    it("应该成功生成故事标题", async () => {
      const generateTitleDto: GenerateTitleDto = {
        keywords: ["夏天", "青春", "友情"],
      };
      const expectedTitle = "那些年的夏天";

      mockAiService.generateStoryTitle.mockResolvedValue(expectedTitle);

      const result = await controller.generateTitle(
        generateTitleDto,
        mockRequest,
      );

      expect(mockAiService.generateStoryTitle).toHaveBeenCalledWith("user-1", [
        "夏天",
        "青春",
        "友情",
      ]);
      expect(result.success).toBe(true);
      expect(result.data).toBe(expectedTitle);
      expect(result.message).toBe("标题生成成功");
      expect(result.statusCode).toBe(200);
      expect(result.timestamp).toBeDefined();
    });

    it("应该处理生成失败的情况", async () => {
      const generateTitleDto: GenerateTitleDto = {
        keywords: ["测试"],
      };

      const error = new Error("AI服务暂时不可用");
      mockAiService.generateStoryTitle.mockRejectedValue(error);

      await expect(
        controller.generateTitle(generateTitleDto, mockRequest),
      ).rejects.toThrow("AI服务暂时不可用");

      expect(mockAiService.generateStoryTitle).toHaveBeenCalledWith("user-1", [
        "测试",
      ]);
    });

    it("应该记录请求日志", async () => {
      const generateTitleDto: GenerateTitleDto = {
        keywords: ["测试"],
      };

      mockAiService.generateStoryTitle.mockResolvedValue("测试标题");

      await controller.generateTitle(generateTitleDto, mockRequest);

      expect(Logger.prototype.log).toHaveBeenCalledWith(
        "用户 user-1 请求生成标题",
      );
    });
  });

  describe("generateContent", () => {
    it("应该成功生成故事内容", async () => {
      const generateContentDto: GenerateContentDto = {
        prompt: "写一个关于友情的故事",
      };
      const expectedContent =
        "在一个阳光明媚的下午，小明和小红在公园里相遇了...";

      mockAiService.generateStoryContent.mockResolvedValue(expectedContent);

      const result = await controller.generateContent(
        generateContentDto,
        mockRequest,
      );

      expect(mockAiService.generateStoryContent).toHaveBeenCalledWith(
        "user-1",
        "写一个关于友情的故事",
      );
      expect(result.success).toBe(true);
      expect(result.data).toBe(expectedContent);
      expect(result.message).toBe("内容生成成功");
      expect(result.statusCode).toBe(200);
      expect(result.timestamp).toBeDefined();
    });

    it("应该处理内容生成失败", async () => {
      const generateContentDto: GenerateContentDto = {
        prompt: "测试提示",
      };

      const error = new Error("配额不足");
      mockAiService.generateStoryContent.mockRejectedValue(error);

      await expect(
        controller.generateContent(generateContentDto, mockRequest),
      ).rejects.toThrow("配额不足");

      expect(mockAiService.generateStoryContent).toHaveBeenCalledWith(
        "user-1",
        "测试提示",
      );
    });

    it("应该记录内容生成日志", async () => {
      const generateContentDto: GenerateContentDto = {
        prompt: "测试",
      };

      mockAiService.generateStoryContent.mockResolvedValue("测试内容");

      await controller.generateContent(generateContentDto, mockRequest);

      expect(Logger.prototype.log).toHaveBeenCalledWith(
        "用户 user-1 请求生成内容",
      );
    });
  });

  describe("getQuotaStatus", () => {
    it("应该成功返回配额状态", async () => {
      mockAiService.getRemainingQuota.mockResolvedValue(2);

      const result = await controller.getQuotaStatus(mockRequest);

      expect(mockAiService.getRemainingQuota).toHaveBeenCalledWith("user-1");
      expect(result.success).toBe(true);
      expect(result.data.remainingQuota).toBe(2);
      expect(result.data.totalDailyQuota).toBe(3);
      expect(result.data.usedQuota).toBe(1);
      expect(result.data.resetTime).toBeInstanceOf(Date);
      expect(result.message).toBe("配额状态查询成功");
    });

    it("应该处理配额查询失败", async () => {
      const error = new Error("数据库连接失败");
      mockAiService.getRemainingQuota.mockRejectedValue(error);

      await expect(controller.getQuotaStatus(mockRequest)).rejects.toThrow(
        "数据库连接失败",
      );
    });

    it("应该计算正确的下一次重置时间", async () => {
      mockAiService.getRemainingQuota.mockResolvedValue(3);

      const result = await controller.getQuotaStatus(mockRequest);
      const resetTime = result.data.resetTime;
      const now = new Date();

      // 验证重置时间是明天的午夜
      expect(resetTime.getHours()).toBe(0);
      expect(resetTime.getMinutes()).toBe(0);
      expect(resetTime.getSeconds()).toBe(0);
      expect(resetTime.getMilliseconds()).toBe(0);
      expect(resetTime.getDate()).toBe(now.getDate() + 1);
    });
  });

  describe("getUsageStatistics", () => {
    it("应该成功返回使用统计（默认30天）", async () => {
      const mockStats = {
        totalUsage: 15,
        successfulUsage: 14,
        totalCost: 0.45,
        averageCostPerRequest: 0.03,
        usageByFeature: {
          titleGeneration: 8,
          contentGeneration: 7,
        },
      };

      mockAiService.getUserUsageStatistics.mockResolvedValue(mockStats);

      const result = await controller.getUsageStatistics(30, mockRequest);

      expect(mockAiService.getUserUsageStatistics).toHaveBeenCalledWith(
        "user-1",
        30,
      );
      expect(result.success).toBe(true);
      expect(result.data.period).toBe(30);
      expect(result.data.totalUsage).toBe(15);
      expect(result.data.successfulUsage).toBe(14);
      expect(result.data.failureRate).toBeCloseTo(6.67, 2);
      expect(result.data.totalCost).toBe(0.45);
      expect(result.data.averageCostPerRequest).toBe(0.03);
      expect(result.data.usageByFeature).toEqual(mockStats.usageByFeature);
    });

    it("应该限制查询天数范围", async () => {
      const mockStats = {
        totalUsage: 100,
        successfulUsage: 95,
        totalCost: 3.0,
        averageCostPerRequest: 0.03,
        usageByFeature: {},
      };

      mockAiService.getUserUsageStatistics.mockResolvedValue(mockStats);

      // 测试超出最大值
      await controller.getUsageStatistics(500, mockRequest);
      expect(mockAiService.getUserUsageStatistics).toHaveBeenCalledWith(
        "user-1",
        365,
      );

      // 测试低于最小值
      await controller.getUsageStatistics(0, mockRequest);
      expect(mockAiService.getUserUsageStatistics).toHaveBeenCalledWith(
        "user-1",
        1,
      );
    });

    it("应该正确计算失败率（无使用记录）", async () => {
      const mockStats = {
        totalUsage: 0,
        successfulUsage: 0,
        totalCost: 0,
        averageCostPerRequest: 0,
        usageByFeature: {},
      };

      mockAiService.getUserUsageStatistics.mockResolvedValue(mockStats);

      const result = await controller.getUsageStatistics(7, mockRequest);

      expect(result.data.failureRate).toBe(0);
    });

    it("应该处理统计查询失败", async () => {
      const error = new Error("统计数据不可用");
      mockAiService.getUserUsageStatistics.mockRejectedValue(error);

      await expect(
        controller.getUsageStatistics(30, mockRequest),
      ).rejects.toThrow("统计数据不可用");
    });
  });

  describe("resetQuota", () => {
    it("应该成功重置配额", async () => {
      mockAiService.resetDailyQuota.mockResolvedValue(undefined);

      const result = await controller.resetQuota(mockRequest);

      expect(mockAiService.resetDailyQuota).toHaveBeenCalledWith("user-1");
      expect(result.success).toBe(true);
      expect(result.data).toBeNull();
      expect(result.message).toBe("配额重置成功");
      expect(result.statusCode).toBe(200);
    });

    it("应该处理配额重置失败", async () => {
      const error = new Error("重置失败");
      mockAiService.resetDailyQuota.mockRejectedValue(error);

      await expect(controller.resetQuota(mockRequest)).rejects.toThrow(
        "重置失败",
      );
    });

    it("应该记录重置操作日志", async () => {
      mockAiService.resetDailyQuota.mockResolvedValue(undefined);

      await controller.resetQuota(mockRequest);

      expect(Logger.prototype.log).toHaveBeenCalledWith(
        "用户 user-1 请求重置配额",
      );
    });
  });

  describe("getNextResetTime", () => {
    it("应该返回明天午夜的时间", () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const resetTime = (controller as any).getNextResetTime();
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);

      expect(resetTime.getDate()).toBe(tomorrow.getDate());
      expect(resetTime.getHours()).toBe(0);
      expect(resetTime.getMinutes()).toBe(0);
      expect(resetTime.getSeconds()).toBe(0);
      expect(resetTime.getMilliseconds()).toBe(0);
    });
  });

  it("应该正确注入AiService", () => {
    expect(controller).toBeDefined();
    expect(mockAiService).toBeDefined();
  });
});
