import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:dio/dio.dart';
import '../../core/network/dio_client.dart';
import '../config/env_config.dart';

final getIt = GetIt.instance;

/// 配置依赖注入
@InjectableInit()
Future<void> configureDependencies() async {
  // 初始化SharedPreferences
  final sharedPreferences = await SharedPreferences.getInstance();
  getIt.registerSingleton<SharedPreferences>(sharedPreferences);
  
  // 注册FlutterSecureStorage
  getIt.registerSingleton<FlutterSecureStorage>(
    const FlutterSecureStorage(),
  );
  
  // 注册Dio
  getIt.registerSingleton<Dio>(DioClient.instance);
  
  // 注册其他依赖...
  // 这里可以使用injectable自动生成的代码
  // $initGetIt(getIt);
}

/// 注册模块
@module
abstract class AppModule {
  // 这里可以注册第三方库的实例
}