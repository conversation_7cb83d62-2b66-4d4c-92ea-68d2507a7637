#!/bin/bash

# ===========================================
# 有故事 APP - 快速网络状态检查
# ===========================================

echo "🔍 快速网络状态检查..."

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 检查后端服务
echo -e "${YELLOW}📡 检查后端服务状态...${NC}"
if curl -s --max-time 3 http://localhost:3000/health > /dev/null; then
    echo -e "${GREEN}✅ 后端服务运行正常${NC}"
else
    echo -e "${RED}❌ 后端服务连接失败${NC}"
    echo -e "${YELLOW}💡 请运行: cd ../AppServe && docker-compose up${NC}"
fi

# 检查Metro Bundler
echo -e "${YELLOW}📱 检查Metro状态...${NC}"
if curl -s --max-time 3 http://localhost:8081 > /dev/null; then
    echo -e "${GREEN}✅ Metro bundler运行正常${NC}"
else
    echo -e "${RED}❌ Metro bundler连接失败${NC}"
    echo -e "${YELLOW}💡 请运行: npx react-native start${NC}"
fi

# 检查端口转发
echo -e "${YELLOW}🔄 检查Android端口转发...${NC}"
if command -v adb &> /dev/null; then
    DEVICES=$(adb devices | grep -v "List of devices" | grep -v "^$")
    if [ -n "$DEVICES" ]; then
        echo -e "${GREEN}✅ Android设备已连接${NC}"
        echo -e "${YELLOW}  重新设置端口转发...${NC}"
        adb reverse tcp:8081 tcp:8081 2>/dev/null
        adb reverse tcp:3000 tcp:3000 2>/dev/null
        echo -e "${GREEN}✅ 端口转发已更新${NC}"
    else
        echo -e "${YELLOW}⚠️  未检测到Android设备${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  ADB未找到，跳过Android检查${NC}"
fi

# 测试API连接
echo -e "${YELLOW}🧪 测试API连接...${NC}"
RESPONSE=$(curl -s --max-time 5 -X POST http://localhost:3000/api/v1/auth/send-sms \
    -H "Content-Type: application/json" \
    -d '{"phone":"13800138000"}' 2>/dev/null)

if [ $? -eq 0 ] && (echo "$RESPONSE" | grep -q '"code":200' || echo "$RESPONSE" | grep -q '"code":400'); then
    echo -e "${GREEN}✅ API连接测试成功${NC}"
    MESSAGE=$(echo $RESPONSE | jq -r '.message' 2>/dev/null || echo 'API响应正常')
    echo -e "   Response: $MESSAGE"
    if echo "$RESPONSE" | grep -q '"code":400'; then
        echo -e "${YELLOW}   ℹ️  频率限制正常，说明API工作正常${NC}"
    fi
else
    echo -e "${RED}❌ API连接测试失败${NC}"
    echo -e "   Response: $RESPONSE"
fi

echo ""
echo -e "${GREEN}🎉 网络状态检查完成${NC}" 