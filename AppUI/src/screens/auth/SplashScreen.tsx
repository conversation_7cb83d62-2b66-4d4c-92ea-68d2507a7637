/**
 * 有故事 APP - 启动页面
 * 
 * 功能特性：
 * - 品牌标识展示
 * - 优雅的加载动画
 * - 自动认证状态检查
 * - 平滑页面过渡
 * - B&O 极简设计风格
 */

import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
  StatusBar,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import AsyncStorage from '@react-native-async-storage/async-storage';

// 导入类型和常量
import { RootStackParamList } from '../../navigation/RootNavigator';
import { THEME } from '../../constants';

// ===== 类型定义 =====
type SplashScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'Splash'
>;

const { width, height } = Dimensions.get('window');

/**
 * 启动页面组件
 * 
 * 负责：
 * - 应用启动动画
 * - 用户认证状态检查
 * - 自动页面跳转
 */
const SplashScreen: React.FC = () => {
  const navigation = useNavigation<SplashScreenNavigationProp>();
  
  // 动画值
  const logoScale = useRef(new Animated.Value(0.8)).current;
  const logoOpacity = useRef(new Animated.Value(0)).current;
  const loadingDots = useRef(new Animated.Value(0)).current;

  /**
   * 启动动画序列
   */
  const startAnimations = () => {
    // Logo 缩放和淡入动画
    Animated.parallel([
      Animated.timing(logoOpacity, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(logoScale, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();

    // 加载点动画
    Animated.loop(
      Animated.sequence([
        Animated.timing(loadingDots, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.timing(loadingDots, {
          toValue: 0,
          duration: 600,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  /**
   * 检查用户认证状态
   */
  const checkAuthStatus = async () => {
    try {
      const token = await AsyncStorage.getItem('authToken');
      const userId = await AsyncStorage.getItem('userId');
      
      // 模拟检查过程
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      if (token && userId) {
        // 已登录，跳转到主页
        navigation.replace('Main');
      } else {
        // 未登录，跳转到认证页
        navigation.replace('Auth');
      }
    } catch (error) {
      console.error('认证状态检查失败:', error);
      // 出错时跳转到认证页
      navigation.replace('Auth');
    }
  };

  useEffect(() => {
    startAnimations();
    checkAuthStatus();
  }, []);

  // 加载点动画插值
  const dotsOpacity = loadingDots.interpolate({
    inputRange: [0, 1],
    outputRange: [0.3, 1],
  });

  return (
    <View style={styles.container}>
      <StatusBar 
        barStyle="light-content" 
        backgroundColor={THEME.colors.primary} 
        hidden 
      />
      
      {/* 主标题区域 */}
      <Animated.View 
        style={[
          styles.logoContainer,
          {
            opacity: logoOpacity,
            transform: [{ scale: logoScale }],
          },
        ]}
      >
        <Text style={styles.logoText}>有故事</Text>
        <Text style={styles.subtitleText}>每个人都有故事</Text>
      </Animated.View>
      
      {/* 加载指示器 */}
      <View style={styles.loadingContainer}>
        <Animated.View style={[styles.loadingDots, { opacity: dotsOpacity }]}>
          <View style={[styles.dot, styles.dot1]} />
          <View style={[styles.dot, styles.dot2]} />
          <View style={[styles.dot, styles.dot3]} />
        </Animated.View>
      </View>
      
      {/* 版权信息 */}
      <View style={styles.footerContainer}>
        <Text style={styles.footerText}>有故事 v1.0.0</Text>
      </View>
    </View>
  );
};

// ===== 样式定义 =====
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: THEME.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: height * 0.15,
  },
  logoText: {
    fontSize: 48,
    fontWeight: '300',
    color: THEME.colors.surface,
    letterSpacing: 3,
    marginBottom: THEME.spacing.md,
  },
  subtitleText: {
    fontSize: 16,
    fontWeight: '300',
    color: THEME.colors.surface,
    opacity: 0.8,
    letterSpacing: 1,
  },
  loadingContainer: {
    position: 'absolute',
    bottom: height * 0.25,
    alignItems: 'center',
  },
  loadingDots: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: THEME.colors.surface,
    marginHorizontal: 4,
  },
  dot1: {
    opacity: 0.4,
  },
  dot2: {
    opacity: 0.7,
  },
  dot3: {
    opacity: 1,
  },
  footerContainer: {
    position: 'absolute',
    bottom: THEME.spacing.xl,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 12,
    color: THEME.colors.surface,
    opacity: 0.6,
    letterSpacing: 0.5,
  },
});

export default SplashScreen; 