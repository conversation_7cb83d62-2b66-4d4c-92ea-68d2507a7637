import { ApiProperty } from "@nestjs/swagger";

export class UploadResponseDto {
  @ApiProperty({ description: "文件访问URL" })
  url: string;

  @ApiProperty({ description: "文件名" })
  filename: string;

  @ApiProperty({ description: "文件大小（字节）" })
  size: number;

  @ApiProperty({ description: "文件MIME类型" })
  mimeType: string;
}

export class MultipleUploadResponseDto {
  @ApiProperty({ description: "上传成功的文件列表", type: [UploadResponseDto] })
  files: UploadResponseDto[];

  @ApiProperty({ description: "成功上传的文件数量" })
  successCount: number;

  @ApiProperty({ description: "失败上传的文件数量" })
  failureCount: number;
}
