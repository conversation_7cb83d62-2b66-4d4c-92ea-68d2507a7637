import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// 企业级响应式间距系统
/// 基于Flutter官方最佳实践，解决跨平台布局一致性问题
class AdaptiveSpacing {
  /// 企业级标准断点定义
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 1200;
  
  /// 获取设备类型
  static DeviceType getDeviceType(BuildContext context) {
    final width = MediaQuery.sizeOf(context).width;
    
    if (width < mobileBreakpoint) {
      return DeviceType.mobile;
    } else if (width < tabletBreakpoint) {
      return DeviceType.tablet;
    } else {
      return DeviceType.desktop;
    }
  }
  
  /// 核心方法：获取跨平台适配的间距
  /// 解决Web和移动端SafeArea差异导致的布局不一致问题
  static double getAdaptiveSpacing(
    BuildContext context, {
    required double baseValue,
    SpacingContext spacingContext = SpacingContext.general,
  }) {
    final deviceType = getDeviceType(context);
    final isWeb = kIsWeb;
    
    // 基础倍率计算
    double multiplier = switch (deviceType) {
      DeviceType.mobile => 1.0,
      DeviceType.tablet => 1.2,
      DeviceType.desktop => 1.4,
    };
    
    // Web端特殊处理：补偿SafeArea差异
    if (isWeb && spacingContext == SpacingContext.topSection) {
      // Web端SafeArea不生效，需要额外间距补偿
      multiplier *= 1.3;
    }
    
    // 移动端特殊处理：考虑实际SafeArea影响
    if (!isWeb && spacingContext == SpacingContext.searchToTab) {
      // 移动端SafeArea会产生额外空间，需要减少间距
      final mediaQuery = MediaQuery.of(context);
      final hasNotch = mediaQuery.padding.top > 24; // 检测刘海屏
      
      if (hasNotch) {
        multiplier *= 0.6; // 有刘海屏时大幅减少间距
      } else {
        multiplier *= 0.8; // 普通设备时适度减少间距
      }
    }
    
    return baseValue * multiplier;
  }
  
  /// 获取搜索框到Tab栏的最优间距
  static double getSearchToTabSpacing(BuildContext context) {
    return getAdaptiveSpacing(
      context,
      baseValue: 8.0,
      spacingContext: SpacingContext.searchToTab,
    );
  }
  
  /// 获取顶部安全区域间距
  static double getTopSafeAreaSpacing(BuildContext context) {
    return getAdaptiveSpacing(
      context,
      baseValue: 20.0,
      spacingContext: SpacingContext.topSection,
    );
  }
  
  /// 获取Tab内部间距
  static double getTabInternalSpacing(BuildContext context) {
    return getAdaptiveSpacing(
      context,
      baseValue: 5.0,
      spacingContext: SpacingContext.general,
    );
  }
}

/// 设备类型枚举
enum DeviceType {
  mobile,
  tablet,
  desktop,
}

/// 间距上下文枚举 - 用于特殊场景的间距调整
enum SpacingContext {
  general,       // 通用间距
  topSection,    // 顶部区域间距
  searchToTab,   // 搜索框到Tab栏间距
  bottomSection, // 底部区域间距
}

/// 企业级自适应SafeArea组件
/// 解决Web端SafeArea不生效的问题
class AdaptiveSafeArea extends StatelessWidget {
  final Widget child;
  final bool top;
  final bool bottom;
  final bool left;
  final bool right;
  
  const AdaptiveSafeArea({
    required this.child,
    super.key,
    this.top = true,
    this.bottom = true,
    this.left = true,
    this.right = true,
  });

  @override
  Widget build(BuildContext context) {
    if (kIsWeb) {
      // Web端使用自定义Padding模拟SafeArea效果
      return Padding(
        padding: EdgeInsets.only(
          top: top ? AdaptiveSpacing.getTopSafeAreaSpacing(context) : 0.0,
          bottom: bottom ? 16.0 : 0.0,
          left: left ? 16.0 : 0.0,
          right: right ? 16.0 : 0.0,
        ),
        child: child,
      );
    } else {
      // 移动端使用原生SafeArea
      return SafeArea(
        top: top,
        bottom: bottom,
        left: left,
        right: right,
        child: child,
      );
    }
  }
}

/// 基于LayoutBuilder的响应式间距组件
class ResponsiveSpacing extends StatelessWidget {
  final Widget child;
  final double baseSpacing;
  final SpacingContext context;
  
  const ResponsiveSpacing({
    required this.child,
    required this.baseSpacing,
    super.key,
    this.context = SpacingContext.general,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final spacing = AdaptiveSpacing.getAdaptiveSpacing(
          context,
          baseValue: baseSpacing,
          spacingContext: this.context,
        );
        
        return SizedBox(
          height: spacing,
          child: child,
        );
      },
    );
  }
}