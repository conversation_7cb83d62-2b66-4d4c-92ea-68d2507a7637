import {
  Controller,
  Get,
  Post,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Req,
  HttpStatus,
} from "@nestjs/common";
import type { Request } from "express";
import { SocialService } from "./social.service";
import {
  FollowUserDto,
  CreateFriendGroupDto,
  FollowListQueryDto,
  FriendGroupQueryDto,
  GroupMemberQueryDto,
} from "./dto";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { ApiResponse } from "../../common/dto/api-response.dto";
import type { PaginatedResponseDto } from "../../common/dto/paginated-response.dto";
import type {
  FollowResponseDto,
  UserBriefDto,
  FriendGroupResponseDto,
  GroupMemberResponseDto,
  SocialStatsResponseDto,
} from "./dto";

/**
 * 社交关系控制器
 * 提供用户关注、好友分组等社交功能的API接口
 */
@Controller("social")
export class SocialController {
  constructor(private readonly socialService: SocialService) {}

  // ==================== 关注关系管理 ====================

  /**
   * 关注用户
   * POST /social/follow
   */
  @Post("follow")
  @UseGuards(JwtAuthGuard)
  async followUser(
    @Body() followUserDto: FollowUserDto,
    @Req() req: Request & { user: { id: string } },
  ): Promise<ApiResponse<FollowResponseDto>> {
    const follow = await this.socialService.followUser(
      followUserDto,
      req.user.id,
    );

    return ApiResponse.success(follow, "关注成功", HttpStatus.CREATED);
  }

  /**
   * 取消关注用户
   * DELETE /social/follow/:userId
   */
  @Delete("follow/:userId")
  @UseGuards(JwtAuthGuard)
  async unfollowUser(
    @Param("userId") userId: string,
    @Req() req: Request & { user: { id: string } },
  ): Promise<ApiResponse<{ success: boolean; message: string }>> {
    const result = await this.socialService.unfollowUser(userId, req.user.id);

    return ApiResponse.success(result, "操作成功", HttpStatus.OK);
  }

  /**
   * 获取关注列表（我关注的人）
   * GET /social/following/:userId
   */
  @Get("following/:userId")
  @UseGuards(JwtAuthGuard)
  async getFollowingList(
    @Param("userId") userId: string,
    @Query() queryDto: FollowListQueryDto,
    @Req() req: Request & { user: { id: string } },
  ): Promise<ApiResponse<PaginatedResponseDto<UserBriefDto>>> {
    const result = await this.socialService.getFollowingList(
      userId,
      queryDto,
      req.user.id,
    );

    return ApiResponse.success(result, "获取关注列表成功", HttpStatus.OK);
  }

  /**
   * 获取粉丝列表（关注我的人）
   * GET /social/followers/:userId
   */
  @Get("followers/:userId")
  @UseGuards(JwtAuthGuard)
  async getFollowersList(
    @Param("userId") userId: string,
    @Query() queryDto: FollowListQueryDto,
    @Req() req: Request & { user: { id: string } },
  ): Promise<ApiResponse<PaginatedResponseDto<UserBriefDto>>> {
    const result = await this.socialService.getFollowersList(
      userId,
      queryDto,
      req.user.id,
    );

    return ApiResponse.success(result, "获取粉丝列表成功", HttpStatus.OK);
  }

  // ==================== 好友分组管理 ====================

  /**
   * 创建好友分组
   * POST /social/groups
   */
  @Post("groups")
  @UseGuards(JwtAuthGuard)
  async createFriendGroup(
    @Body() createGroupDto: CreateFriendGroupDto,
    @Req() req: Request & { user: { id: string } },
  ): Promise<ApiResponse<FriendGroupResponseDto>> {
    const group = await this.socialService.createFriendGroup(
      createGroupDto,
      req.user.id,
    );

    return ApiResponse.success(group, "创建分组成功", HttpStatus.CREATED);
  }

  /**
   * 获取好友分组列表
   * GET /social/groups
   */
  @Get("groups")
  @UseGuards(JwtAuthGuard)
  async getFriendGroups(
    @Query() queryDto: FriendGroupQueryDto,
    @Req() req: Request & { user: { id: string } },
  ): Promise<ApiResponse<PaginatedResponseDto<FriendGroupResponseDto>>> {
    const result = await this.socialService.getFriendGroups(
      req.user.id,
      queryDto,
    );

    return ApiResponse.success(result, "获取分组列表成功", HttpStatus.OK);
  }

  /**
   * 获取分组成员列表
   * GET /social/groups/:groupId/members
   */
  @Get("groups/:groupId/members")
  @UseGuards(JwtAuthGuard)
  async getGroupMembers(
    @Param("groupId") groupId: string,
    @Query() queryDto: GroupMemberQueryDto,
    @Req() req: Request & { user: { id: string } },
  ): Promise<ApiResponse<PaginatedResponseDto<GroupMemberResponseDto>>> {
    const result = await this.socialService.getGroupMembers(
      groupId,
      queryDto,
      req.user.id,
    );

    return ApiResponse.success(result, "获取分组成员成功", HttpStatus.OK);
  }

  /**
   * 向分组添加成员
   * POST /social/groups/:groupId/members
   */
  @Post("groups/:groupId/members")
  @UseGuards(JwtAuthGuard)
  async addMembersToGroup(
    @Param("groupId") groupId: string,
    @Body("userIds") userIds: string[],
    @Req() req: Request & { user: { id: string } },
  ): Promise<ApiResponse<{ addedCount: number }>> {
    const addedCount = await this.socialService.addMembersToGroup(
      groupId,
      userIds,
      req.user.id,
    );

    return ApiResponse.success(
      { addedCount },
      `成功添加 ${addedCount} 个成员`,
      HttpStatus.CREATED,
    );
  }

  /**
   * 获取社交统计数据
   * GET /social/stats/:userId
   */
  @Get("stats/:userId")
  @UseGuards(JwtAuthGuard)
  async getSocialStats(
    @Param("userId") userId: string,
  ): Promise<ApiResponse<SocialStatsResponseDto>> {
    const stats = await this.socialService.getSocialStats(userId);

    return ApiResponse.success(stats, "获取社交统计成功", HttpStatus.OK);
  }
}
