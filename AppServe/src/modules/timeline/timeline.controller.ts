import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  Query,
  UseGuards,
  HttpStatus,
  Req,
} from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from "@nestjs/swagger";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { TimelineService } from "./timeline.service";
import {
  CreateTimelineEventDto,
  UpdateTimelineEventDto,
  TimelineQueryDto,
  TimelineEventResponseDto,
  TimelineStatsResponseDto,
} from "./dto";
import type { ApiResponseDto } from "../../common/dto/api-response.dto";
import type { PaginatedResponseDto } from "../../common/dto/paginated-response.dto";
import { ApiResponse as ApiResponseUtil } from "../../common/dto/api-response.dto";

@ApiTags("时间线管理")
@Controller("timeline")
export class TimelineController {
  constructor(private readonly timelineService: TimelineService) {}

  /**
   * 创建时间线事件
   * POST /timeline/events
   */
  @Post("events")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: "创建时间线事件",
    description: "用户创建新的时间线事件",
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: "时间线事件创建成功",
    type: TimelineEventResponseDto,
  })
  async createTimelineEvent(
    @Body() createEventDto: CreateTimelineEventDto,
    @Req() req: Request & { user: { id: string } },
  ): Promise<ApiResponseDto<TimelineEventResponseDto>> {
    const event = await this.timelineService.createTimelineEvent(
      createEventDto,
      req.user.id,
    );

    return ApiResponseUtil.success(
      event,
      "时间线事件创建成功",
      HttpStatus.CREATED,
    );
  }

  /**
   * 获取用户的时间线事件列表
   * GET /timeline/users/:userId/events
   */
  @Get("users/:userId/events")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: "获取用户时间线",
    description: "获取指定用户的时间线事件列表",
  })
  @ApiParam({ name: "userId", description: "用户ID" })
  @ApiQuery({ name: "page", required: false, description: "页码", example: 1 })
  @ApiQuery({
    name: "limit",
    required: false,
    description: "每页数量",
    example: 20,
  })
  @ApiQuery({ name: "eventType", required: false, description: "事件类型筛选" })
  @ApiQuery({
    name: "isMilestone",
    required: false,
    description: "是否为里程碑事件",
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: "获取时间线成功",
  })
  async getUserTimeline(
    @Param("userId") userId: string,
    @Query() queryDto: TimelineQueryDto,
    @Req() req: Request & { user: { id: string } },
  ): Promise<ApiResponseDto<PaginatedResponseDto<TimelineEventResponseDto>>> {
    const result = await this.timelineService.getUserTimeline(
      userId,
      queryDto,
      req.user.id,
    );

    return ApiResponseUtil.success(result, "获取时间线成功", HttpStatus.OK);
  }

  /**
   * 获取时间线事件详情
   * GET /timeline/events/:eventId
   */
  @Get("events/:eventId")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: "获取时间线事件详情",
    description: "获取指定时间线事件的详细信息",
  })
  @ApiParam({ name: "eventId", description: "事件ID" })
  @ApiResponse({
    status: HttpStatus.OK,
    description: "获取事件详情成功",
    type: TimelineEventResponseDto,
  })
  async getTimelineEventById(
    @Param("eventId") eventId: string,
    @Req() req: Request & { user: { id: string } },
  ): Promise<ApiResponseDto<TimelineEventResponseDto>> {
    const event = await this.timelineService.getTimelineEventById(
      eventId,
      req.user.id,
    );

    return ApiResponseUtil.success(event, "获取事件详情成功", HttpStatus.OK);
  }

  /**
   * 更新时间线事件
   * PUT /timeline/events/:eventId
   */
  @Put("events/:eventId")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: "更新时间线事件",
    description: "更新指定时间线事件的信息",
  })
  @ApiParam({ name: "eventId", description: "事件ID" })
  @ApiResponse({
    status: HttpStatus.OK,
    description: "时间线事件更新成功",
    type: TimelineEventResponseDto,
  })
  async updateTimelineEvent(
    @Param("eventId") eventId: string,
    @Body() updateEventDto: UpdateTimelineEventDto,
    @Req() req: Request & { user: { id: string } },
  ): Promise<ApiResponseDto<TimelineEventResponseDto>> {
    const event = await this.timelineService.updateTimelineEvent(
      eventId,
      updateEventDto,
      req.user.id,
    );

    return ApiResponseUtil.success(event, "时间线事件更新成功", HttpStatus.OK);
  }

  /**
   * 删除时间线事件
   * DELETE /timeline/events/:eventId
   */
  @Delete("events/:eventId")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: "删除时间线事件",
    description: "删除指定的时间线事件",
  })
  @ApiParam({ name: "eventId", description: "事件ID" })
  @ApiResponse({
    status: HttpStatus.OK,
    description: "时间线事件删除成功",
  })
  async deleteTimelineEvent(
    @Param("eventId") eventId: string,
    @Req() req: Request & { user: { id: string } },
  ): Promise<ApiResponseDto<null>> {
    await this.timelineService.deleteTimelineEvent(eventId, req.user.id);

    return ApiResponseUtil.success(null, "时间线事件删除成功", HttpStatus.OK);
  }

  /**
   * 获取时间线统计数据
   * GET /timeline/users/:userId/stats
   */
  @Get("users/:userId/stats")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: "获取时间线统计",
    description: "获取用户的时间线统计数据",
  })
  @ApiParam({ name: "userId", description: "用户ID" })
  @ApiResponse({
    status: HttpStatus.OK,
    description: "获取时间线统计成功",
    type: TimelineStatsResponseDto,
  })
  async getTimelineStats(
    @Param("userId") userId: string,
  ): Promise<ApiResponseDto<TimelineStatsResponseDto>> {
    const stats = await this.timelineService.getTimelineStats(userId);

    return ApiResponseUtil.success(stats, "获取时间线统计成功", HttpStatus.OK);
  }
}
