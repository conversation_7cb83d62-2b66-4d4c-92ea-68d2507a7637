#!/bin/bash

# 有故事 APP - Android 快速构建脚本
# 使用国内镜像源和优化配置加速构建

set -e

echo "🚀 开始 Android 快速构建..."

# 检查 Android 环境
if [ -z "$ANDROID_HOME" ]; then
    echo "❌ 错误: 未设置 ANDROID_HOME 环境变量"
    exit 1
fi

# 进入 android 目录
cd android

# 设置构建优化参数
export GRADLE_OPTS="-Xmx4g -XX:MaxMetaspaceSize=1g -XX:+UseG1GC"

echo "📦 使用优化配置构建..."
echo "🌏 使用国内镜像源加速下载..."

# 使用初始化脚本构建
./gradlew app:assembleDebug --init-script init.gradle --parallel --daemon --build-cache

echo "📱 安装到设备..."
./gradlew app:installDebug

echo "✅ Android 构建完成！"
echo "🎉 应用已安装到设备上" 