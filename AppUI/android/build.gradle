buildscript {
    ext {
        buildToolsVersion = "35.0.0"
        minSdkVersion = 24
        compileSdkVersion = 35
        targetSdkVersion = 35
        ndkVersion = "27.1.12297006"
        kotlinVersion = "2.0.21"
    }
    repositories {
        // 阿里云镜像源（优先使用）
        maven { 
            name = "AliYun Google"
            url = uri("https://maven.aliyun.com/repository/google") 
        }
        maven { 
            name = "AliYun Central"
            url = uri("https://maven.aliyun.com/repository/central") 
        }
        maven { 
            name = "AliYun Gradle Plugin"
            url = uri("https://maven.aliyun.com/repository/gradle-plugin") 
        }
        maven { 
            name = "AliYun Public"
            url = uri("https://maven.aliyun.com/repository/public") 
        }
        
        // 腾讯云镜像源
        maven { 
            name = "Tencent"
            url = uri("https://mirrors.cloud.tencent.com/nexus/repository/maven-public/") 
        }
        
        // 华为云镜像源
        maven { 
            name = "HuaweiCloud"
            url = uri("https://repo.huaweicloud.com/repository/maven/") 
        }
        
        // 本地仓库
        mavenLocal()
        
        // 官方仓库作为最后备用
        google()
        mavenCentral()
        gradlePluginPortal()
    }
    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
    }
}

// 全局仓库配置
allprojects {
    repositories {
        // 阿里云镜像源（优先使用）
        maven { 
            name = "AliYun Google"
            url = uri("https://maven.aliyun.com/repository/google")
        }
        maven { 
            name = "AliYun Central"
            url = uri("https://maven.aliyun.com/repository/central")
        }
        maven { 
            name = "AliYun Public"
            url = uri("https://maven.aliyun.com/repository/public")
        }
        
        // 腾讯云镜像源
        maven { 
            name = "Tencent"
            url = uri("https://mirrors.cloud.tencent.com/nexus/repository/maven-public/")
        }
        
        // 华为云镜像源
        maven { 
            name = "HuaweiCloud"
            url = uri("https://repo.huaweicloud.com/repository/maven/")
        }
        
        // React Native 专用配置
        maven {
            name = "ReactNative"
            url = uri("$rootDir/../node_modules/react-native/android")
        }
        
        // 本地仓库
        mavenLocal()
        
        // 官方仓库作为最后备用
        google()
        mavenCentral()
        gradlePluginPortal()
        
        // JCenter（虽然已弃用，但某些包可能仍需要）
        jcenter() {
            content {
                includeModule("com.yqritc", "Android-ScalableVideoView")
            }
        }
    }
}

apply plugin: "com.facebook.react.rootproject"
