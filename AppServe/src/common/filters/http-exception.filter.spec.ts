import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import type { ArgumentsHost } from "@nestjs/common";
import {
  HttpException,
  HttpStatus,
  BadRequestException,
  Logger,
} from "@nestjs/common";
import { HttpExceptionFilter } from "./http-exception.filter";
import type { FastifyRequest, FastifyReply } from "fastify";

describe("HttpExceptionFilter", () => {
  let filter: HttpExceptionFilter;
  let mockRequest: Partial<FastifyRequest>;
  let mockResponse: Partial<FastifyReply>;
  let mockArgumentsHost: ArgumentsHost;
  let mockLogger: jest.SpyInstance;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [HttpExceptionFilter],
    }).compile();

    filter = module.get<HttpExceptionFilter>(HttpExceptionFilter);

    // Mock Request
    mockRequest = {
      url: "/api/test",
      method: "GET",
    };

    // Mock Response
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      send: jest.fn(),
    };

    // Mock ArgumentsHost
    mockArgumentsHost = {
      switchToHttp: jest.fn().mockReturnValue({
        getRequest: jest.fn().mockReturnValue(mockRequest),
        getResponse: jest.fn().mockReturnValue(mockResponse),
      }),
    } as unknown as ArgumentsHost;

    // Mock Logger
    mockLogger = jest
      .spyOn(Logger.prototype, "error")
      .mockImplementation(() => {});
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe("catch", () => {
    it("应该处理HttpException", () => {
      const exception = new BadRequestException("验证失败");

      filter.catch(exception, mockArgumentsHost);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.send).toHaveBeenCalledWith({
        success: false,
        code: 400,
        message: "验证失败",
        error: "Bad Request",
        timestamp: expect.any(String),
        path: "/api/test",
      });
    });

    it("应该处理带有复杂响应的HttpException", () => {
      const complexResponse = {
        message: "验证失败",
        error: "VALIDATION_ERROR",
        details: ["字段不能为空"],
      };
      const exception = new BadRequestException(complexResponse);

      filter.catch(exception, mockArgumentsHost);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.send).toHaveBeenCalledWith({
        success: false,
        code: 400,
        message: "验证失败",
        error: "VALIDATION_ERROR",
        timestamp: expect.any(String),
        path: "/api/test",
      });
    });

    it("应该处理内部服务器错误", () => {
      const exception = new HttpException(
        "Internal Error",
        HttpStatus.INTERNAL_SERVER_ERROR,
      );

      filter.catch(exception, mockArgumentsHost);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.send).toHaveBeenCalledWith({
        success: false,
        code: 500,
        message: "Internal Error",
        error: undefined,
        timestamp: expect.any(String),
        path: "/api/test",
      });
    });

    it("应该处理非HttpException错误", () => {
      const exception = new Error("系统错误");

      filter.catch(exception, mockArgumentsHost);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.send).toHaveBeenCalledWith({
        success: false,
        code: 500,
        message: "Internal server error",
        error: undefined,
        timestamp: expect.any(String),
        path: "/api/test",
      });
    });

    it("应该处理未知类型的异常", () => {
      const exception = "字符串错误";

      filter.catch(exception, mockArgumentsHost);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.send).toHaveBeenCalledWith({
        success: false,
        code: 500,
        message: "Internal server error",
        error: undefined,
        timestamp: expect.any(String),
        path: "/api/test",
      });
    });

    it("应该在开发环境下包含堆栈信息", () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = "development";

      const exception = new Error("测试错误");
      exception.stack = "Error: 测试错误\n    at test";

      filter.catch(exception, mockArgumentsHost);

      expect(mockResponse.send).toHaveBeenCalledWith(
        expect.objectContaining({
          stack: "Error: 测试错误\n    at test",
        }),
      );

      process.env.NODE_ENV = originalEnv;
    });

    it("应该在生产环境下不包含堆栈信息", () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = "production";

      const exception = new Error("测试错误");
      exception.stack = "Error: 测试错误\n    at test";

      filter.catch(exception, mockArgumentsHost);

      expect(mockResponse.send).toHaveBeenCalledWith(
        expect.not.objectContaining({
          stack: expect.any(String),
        }),
      );

      process.env.NODE_ENV = originalEnv;
    });

    it("应该记录错误日志", () => {
      const exception = new BadRequestException("验证失败");

      filter.catch(exception, mockArgumentsHost);

      expect(mockLogger).toHaveBeenCalledWith({
        statusCode: 400,
        timestamp: expect.any(String),
        path: "/api/test",
        method: "GET",
        message:
          '{"message":"验证失败","error":"Bad Request","statusCode":400}',
        stack: expect.any(String),
      });
    });

    it("应该正确处理POST请求", () => {
      const postRequest = { method: "POST", url: "/api/users" };
      const postContext = {
        switchToHttp: jest.fn().mockReturnValue({
          getRequest: jest.fn().mockReturnValue(postRequest),
          getResponse: jest.fn().mockReturnValue(mockResponse),
        }),
      } as unknown as ArgumentsHost;

      const exception = new BadRequestException("创建失败");

      filter.catch(exception, postContext);

      expect(mockResponse.send).toHaveBeenCalledWith(
        expect.objectContaining({
          path: "/api/users",
        }),
      );

      expect(mockLogger).toHaveBeenCalledWith(
        expect.objectContaining({
          method: "POST",
          path: "/api/users",
        }),
      );
    });

    it("应该正确处理PUT请求", () => {
      const putRequest = { method: "PUT", url: "/api/users/123" };
      const putContext = {
        switchToHttp: jest.fn().mockReturnValue({
          getRequest: jest.fn().mockReturnValue(putRequest),
          getResponse: jest.fn().mockReturnValue(mockResponse),
        }),
      } as unknown as ArgumentsHost;

      const exception = new BadRequestException("更新失败");

      filter.catch(exception, putContext);

      expect(mockResponse.send).toHaveBeenCalledWith(
        expect.objectContaining({
          path: "/api/users/123",
        }),
      );

      expect(mockLogger).toHaveBeenCalledWith(
        expect.objectContaining({
          method: "PUT",
          path: "/api/users/123",
        }),
      );
    });

    it("应该正确处理DELETE请求", () => {
      const deleteRequest = { method: "DELETE", url: "/api/users/123" };
      const deleteContext = {
        switchToHttp: jest.fn().mockReturnValue({
          getRequest: jest.fn().mockReturnValue(deleteRequest),
          getResponse: jest.fn().mockReturnValue(mockResponse),
        }),
      } as unknown as ArgumentsHost;

      const exception = new BadRequestException("删除失败");

      filter.catch(exception, deleteContext);

      expect(mockResponse.send).toHaveBeenCalledWith(
        expect.objectContaining({
          path: "/api/users/123",
        }),
      );

      expect(mockLogger).toHaveBeenCalledWith(
        expect.objectContaining({
          method: "DELETE",
          path: "/api/users/123",
        }),
      );
    });

    it("应该处理复杂的错误响应对象", () => {
      const complexError = {
        message: ["字段1不能为空", "字段2格式错误"],
        error: "Bad Request",
        statusCode: 400,
      };
      const exception = new BadRequestException(complexError);

      filter.catch(exception, mockArgumentsHost);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.send).toHaveBeenCalledWith({
        success: false,
        code: 400,
        message: ["字段1不能为空", "字段2格式错误"],
        error: "Bad Request",
        timestamp: expect.any(String),
        path: "/api/test",
      });
    });

    it("应该处理null消息的异常", () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const exception = new HttpException(null as any, HttpStatus.BAD_REQUEST);

      filter.catch(exception, mockArgumentsHost);

      expect(mockResponse.send).toHaveBeenCalledWith(
        expect.objectContaining({
          message: "Internal server error",
        }),
      );
    });

    it("应该记录非Error类型异常的日志", () => {
      const exception = "字符串异常";

      filter.catch(exception, mockArgumentsHost);

      expect(mockLogger).toHaveBeenCalledWith({
        statusCode: 500,
        timestamp: expect.any(String),
        path: "/api/test",
        method: "GET",
        message: "Internal server error",
        stack: undefined,
      });
    });

    it("应该正确处理数字类型的消息", () => {
      const exception = new HttpException("404", HttpStatus.NOT_FOUND);

      filter.catch(exception, mockArgumentsHost);

      expect(mockResponse.send).toHaveBeenCalledWith(
        expect.objectContaining({
          message: "404",
        }),
      );
    });

    it("应该正确格式化时间戳", () => {
      const mockDate = new Date("2023-01-01T00:00:00.000Z");
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      jest.spyOn(global, "Date").mockImplementation(() => mockDate as any);

      const exception = new BadRequestException("测试");

      filter.catch(exception, mockArgumentsHost);

      expect(mockResponse.send).toHaveBeenCalledWith(
        expect.objectContaining({
          timestamp: "2023-01-01T00:00:00.000Z",
        }),
      );

      expect(mockLogger).toHaveBeenCalledWith(
        expect.objectContaining({
          timestamp: "2023-01-01T00:00:00.000Z",
        }),
      );
    });
  });

  it("应该正确实例化", () => {
    expect(filter).toBeDefined();
    expect(filter).toBeInstanceOf(HttpExceptionFilter);
  });
});
