import { Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { SharesController } from "./shares.controller";
import { SharesService } from "./shares.service";
import { Share } from "./entities/share.entity";
import { ShareAccessLog } from "./entities/share-access-log.entity";
import { Story } from "../stories/entities/story.entity";
import { User } from "../users/entities/user.entity";
import { AuthModule } from "../auth/auth.module";

/**
 * 分享模块
 * 提供故事分享的完整功能
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([Share, ShareAccessLog, Story, User]),
    AuthModule,
  ],
  controllers: [SharesController],
  providers: [SharesService],
  exports: [SharesService],
})
export class SharesModule {}
