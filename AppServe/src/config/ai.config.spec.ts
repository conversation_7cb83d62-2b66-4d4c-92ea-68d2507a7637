/**
 * AI配置测试 - 企业级配置验证
 * 测试OpenAI和AI服务配置
 */

// Mock env.utils 先执行
jest.mock("../common/utils/env.utils", () => ({
  getStringEnv: jest.fn((value: string | undefined, defaultValue: string) => {
    return value || defaultValue; // 测试环境下返回值或默认值
  }),
  parseIntEnv: jest.fn((value: string | undefined, defaultValue: number) => {
    return value ? parseInt(value, 10) : defaultValue; // 测试环境下解析或返回默认值
  }),
  parseFloatEnv: jest.fn((value: string | undefined, defaultValue: number) => {
    return value ? parseFloat(value) : defaultValue; // 测试环境下解析或返回默认值
  }),
}));

import { aiConfig } from "./ai.config";

describe("AIConfig - 企业级配置测试", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should be defined", () => {
    expect(aiConfig).toBeDefined();
  });

  describe("OpenAI配置", () => {
    it("should provide OpenAI API configuration", () => {
      const config = aiConfig();

      expect(config.openaiApiKey).toBe("");
      expect(config.openaiBaseUrl).toBe("https://api.openai.com/v1");
      expect(config.openaiModel).toBe("gpt-3.5-turbo");
    });

    it("should provide OpenAI model parameters", () => {
      const config = aiConfig();

      expect(config.openaiMaxTokens).toBe(1000);
      expect(typeof config.openaiMaxTokens).toBe("number");

      expect(config.openaiTemperature).toBe(0.7);
      expect(typeof config.openaiTemperature).toBe("number");
    });

    it("should provide valid temperature range", () => {
      const config = aiConfig();

      expect(config.openaiTemperature).toBeGreaterThanOrEqual(0);
      expect(config.openaiTemperature).toBeLessThanOrEqual(2);
    });

    it("should provide positive max tokens", () => {
      const config = aiConfig();

      expect(config.openaiMaxTokens).toBeGreaterThan(0);
    });
  });

  describe("请求限制配置", () => {
    it("should provide request timeout configuration", () => {
      const config = aiConfig();

      expect(config.requestTimeout).toBe(30000);
      expect(typeof config.requestTimeout).toBe("number");
      expect(config.requestTimeout).toBeGreaterThan(0);
    });

    it("should provide rate limiting configuration", () => {
      const config = aiConfig();

      expect(config.maxRequestsPerUser).toBe(50);
      expect(typeof config.maxRequestsPerUser).toBe("number");
      expect(config.maxRequestsPerUser).toBeGreaterThan(0);
    });

    it("should provide time window configuration", () => {
      const config = aiConfig();

      expect(config.maxRequestsWindow).toBe(3600);
      expect(typeof config.maxRequestsWindow).toBe("number");
      expect(config.maxRequestsWindow).toBeGreaterThan(0);
    });
  });

  describe("配置完整性验证", () => {
    it("should contain all required AI configuration keys", () => {
      const config = aiConfig();

      const requiredKeys = [
        "openaiApiKey",
        "openaiBaseUrl",
        "openaiModel",
        "openaiMaxTokens",
        "openaiTemperature",
        "requestTimeout",
        "maxRequestsPerUser",
        "maxRequestsWindow",
      ];

      requiredKeys.forEach((key) => {
        expect(config).toHaveProperty(key);
      });
    });

    it("should return consistent configuration object", () => {
      const config1 = aiConfig();
      const config2 = aiConfig();

      expect(config1).toEqual(config2);
    });
  });

  describe("环境变量集成", () => {
    it("should call env utils with correct parameters", () => {
      const {
        getStringEnv,
        parseIntEnv,
        parseFloatEnv,
        // eslint-disable-next-line @typescript-eslint/no-var-requires
      } = require("../common/utils/env.utils");

      aiConfig();

      // 验证字符串环境变量调用
      expect(getStringEnv).toHaveBeenCalledWith(process.env.OPENAI_API_KEY, "");
      expect(getStringEnv).toHaveBeenCalledWith(
        process.env.OPENAI_BASE_URL,
        "https://api.openai.com/v1",
      );
      expect(getStringEnv).toHaveBeenCalledWith(
        process.env.OPENAI_MODEL,
        "gpt-3.5-turbo",
      );

      // 验证整数环境变量调用
      expect(parseIntEnv).toHaveBeenCalledWith(
        process.env.OPENAI_MAX_TOKENS,
        1000,
      );
      expect(parseIntEnv).toHaveBeenCalledWith(
        process.env.AI_REQUEST_TIMEOUT,
        30000,
      );
      expect(parseIntEnv).toHaveBeenCalledWith(
        process.env.AI_MAX_REQUESTS_PER_USER,
        50,
      );
      expect(parseIntEnv).toHaveBeenCalledWith(
        process.env.AI_MAX_REQUESTS_WINDOW,
        3600,
      );

      // 验证浮点数环境变量调用
      expect(parseFloatEnv).toHaveBeenCalledWith(
        process.env.OPENAI_TEMPERATURE,
        0.7,
      );
    });
  });

  describe("默认值验证", () => {
    it("should provide secure defaults", () => {
      const config = aiConfig();

      // API Key应该为空字符串，需要在生产环境中配置
      expect(config.openaiApiKey).toBe("");

      // 默认使用官方API地址
      expect(config.openaiBaseUrl).toBe("https://api.openai.com/v1");

      // 默认使用经济实惠的模型
      expect(config.openaiModel).toBe("gpt-3.5-turbo");
    });

    it("should provide reasonable performance defaults", () => {
      const config = aiConfig();

      // 合理的令牌限制
      expect(config.openaiMaxTokens).toBe(1000);

      // 适中的创造性参数
      expect(config.openaiTemperature).toBe(0.7);

      // 合理的超时时间（30秒）
      expect(config.requestTimeout).toBe(30000);
    });

    it("should provide reasonable rate limiting defaults", () => {
      const config = aiConfig();

      // 每用户每小时50次请求
      expect(config.maxRequestsPerUser).toBe(50);

      // 1小时时间窗口
      expect(config.maxRequestsWindow).toBe(3600);
    });
  });

  describe("数据类型验证", () => {
    it("should ensure correct data types", () => {
      const config = aiConfig();

      expect(typeof config.openaiApiKey).toBe("string");
      expect(typeof config.openaiBaseUrl).toBe("string");
      expect(typeof config.openaiModel).toBe("string");
      expect(typeof config.openaiMaxTokens).toBe("number");
      expect(typeof config.openaiTemperature).toBe("number");
      expect(typeof config.requestTimeout).toBe("number");
      expect(typeof config.maxRequestsPerUser).toBe("number");
      expect(typeof config.maxRequestsWindow).toBe("number");
    });
  });
});
