/**
 * UsersModule 单元测试
 * 测试用户模块的依赖注入和模块配置
 */

import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import { CACHE_MANAGER } from "@nestjs/cache-manager";
import { ConfigService } from "@nestjs/config";
import { JwtService } from "@nestjs/jwt";
import { UsersController } from "./users.controller";
import { UsersService } from "./users.service";
import { NotificationsController } from "./notifications.controller";
import { NotificationsService } from "./notifications.service";
import { User } from "./entities/user.entity";
import { RefreshToken } from "./entities/refresh-token.entity";
import { UserRelationship } from "./entities/user-relationship.entity";
import { Notification } from "./entities/notification.entity";

// 认证相关服务Mock
import { AuthService } from "../auth/auth.service";
import { SmsService } from "../auth/services/sms.service";

// 上传相关服务Mock
import { UploadService } from "../upload/upload.service";

describe("UsersModule - 企业级单元测试", () => {
  let module: TestingModule;

  const mockRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  };

  const mockCacheManager = {
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
    reset: jest.fn(),
  };

  // Mock 配置服务
  const mockConfigService = {
    get: jest.fn((key: string) => {
      const config: Record<string, string> = {
        JWT_SECRET: "test-jwt-secret",
        JWT_EXPIRES_IN: "15m",
        JWT_REFRESH_EXPIRES_IN: "7d",
        ALIYUN_SMS_ACCESS_KEY_ID: "test-key",
        ALIYUN_SMS_ACCESS_KEY_SECRET: "test-secret",
        ALIYUN_SMS_ENDPOINT: "test-endpoint",
      };
      return config[key];
    }),
  };

  // Mock JWT服务
  const mockJwtService = {
    sign: jest.fn().mockReturnValue("test-jwt-token"),
    verify: jest.fn().mockReturnValue({ userId: "test-user-id" }),
    decode: jest.fn(),
  };

  // Mock 认证服务
  const mockAuthService = {
    validateUser: jest.fn(),
    login: jest.fn(),
    register: jest.fn(),
    refreshToken: jest.fn(),
  };

  // Mock SMS服务
  const mockSmsService = {
    sendVerificationCode: jest.fn(),
    verifyCode: jest.fn(),
  };

  // Mock 上传服务
  const mockUploadService = {
    uploadAvatar: jest.fn(),
    deleteFile: jest.fn(),
  };

  beforeEach(async () => {
    module = await Test.createTestingModule({
      controllers: [UsersController, NotificationsController],
      providers: [
        UsersService,
        NotificationsService,
        // Mock 提供者
        { provide: getRepositoryToken(User), useValue: mockRepository },
        { provide: getRepositoryToken(RefreshToken), useValue: mockRepository },
        {
          provide: getRepositoryToken(UserRelationship),
          useValue: mockRepository,
        },
        { provide: getRepositoryToken(Notification), useValue: mockRepository },
        { provide: CACHE_MANAGER, useValue: mockCacheManager },
        { provide: ConfigService, useValue: mockConfigService },
        { provide: JwtService, useValue: mockJwtService },
        { provide: AuthService, useValue: mockAuthService },
        { provide: SmsService, useValue: mockSmsService },
        { provide: UploadService, useValue: mockUploadService },
      ],
    }).compile();
  });

  afterEach(async () => {
    if (module) {
      await module.close();
    }
  });

  it("应该成功创建模块实例", () => {
    expect(module).toBeDefined();
  });

  describe("服务提供者", () => {
    it("should provide UsersService", () => {
      const usersService = module.get<UsersService>(UsersService);
      expect(usersService).toBeDefined();
      expect(usersService).toBeInstanceOf(UsersService);
    });

    it("should provide NotificationsService", () => {
      const notificationsService =
        module.get<NotificationsService>(NotificationsService);
      expect(notificationsService).toBeDefined();
      expect(notificationsService).toBeInstanceOf(NotificationsService);
    });
  });

  describe("控制器", () => {
    it("should provide UsersController", () => {
      const usersController = module.get<UsersController>(UsersController);
      expect(usersController).toBeDefined();
      expect(usersController).toBeInstanceOf(UsersController);
    });

    it("should provide NotificationsController", () => {
      const notificationsController = module.get<NotificationsController>(
        NotificationsController,
      );
      expect(notificationsController).toBeDefined();
      expect(notificationsController).toBeInstanceOf(NotificationsController);
    });
  });

  describe("依赖注入", () => {
    it("should inject User repository", () => {
      const userRepository = module.get(getRepositoryToken(User));
      expect(userRepository).toBeDefined();
      expect(userRepository).toBe(mockRepository);
    });

    it("should inject UserRelationship repository", () => {
      const userRelationshipRepository = module.get(
        getRepositoryToken(UserRelationship),
      );
      expect(userRelationshipRepository).toBeDefined();
      expect(userRelationshipRepository).toBe(mockRepository);
    });

    it("should inject RefreshToken repository", () => {
      const refreshTokenRepository = module.get(
        getRepositoryToken(RefreshToken),
      );
      expect(refreshTokenRepository).toBeDefined();
      expect(refreshTokenRepository).toBe(mockRepository);
    });

    it("should inject Notification repository", () => {
      const notificationRepository = module.get(
        getRepositoryToken(Notification),
      );
      expect(notificationRepository).toBeDefined();
      expect(notificationRepository).toBe(mockRepository);
    });

    it("should inject CACHE_MANAGER", () => {
      const cacheManager = module.get(CACHE_MANAGER);
      expect(cacheManager).toBeDefined();
      expect(cacheManager).toBe(mockCacheManager);
    });
  });

  describe("模块配置", () => {
    it("should export UsersService for other modules", () => {
      const usersService = module.get<UsersService>(UsersService);
      expect(usersService).toBeDefined();
    });

    it("should export NotificationsService for other modules", () => {
      const notificationsService =
        module.get<NotificationsService>(NotificationsService);
      expect(notificationsService).toBeDefined();
    });
  });
});
