// 全局 Gradle 初始化脚本 - 加速依赖下载
// 使用方法：./gradlew <task> --init-script init.gradle

println "🚀 正在应用国内镜像源加速配置..."

allprojects {
    repositories {
        // 清除所有现有仓库配置
        all { ArtifactRepository repo ->
            if (repo instanceof MavenArtifactRepository) {
                def url = repo.url.toString()
                // 如果是官方仓库，则移除（优先使用镜像）
                if (url.contains('google.com') || 
                    url.contains('maven.org') || 
                    url.contains('gradle.org')) {
                    println "⚠️  移除官方仓库: $url"
                    remove repo
                }
            }
        }

        // 阿里云镜像源（最高优先级）
        maven { 
            name = "AliYun Google Mirror"
            url = uri("https://maven.aliyun.com/repository/google")
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*") 
                includeGroupByRegex("androidx.*")
            }
        }
        maven { 
            name = "AliYun Central Mirror"
            url = uri("https://maven.aliyun.com/repository/central")
        }
        maven { 
            name = "AliYun Public Mirror"
            url = uri("https://maven.aliyun.com/repository/public")
        }
        maven { 
            name = "AliYun Gradle Plugin Mirror"
            url = uri("https://maven.aliyun.com/repository/gradle-plugin")
        }
        
        // 腾讯云镜像源
        maven { 
            name = "Tencent Mirror"
            url = uri("https://mirrors.cloud.tencent.com/nexus/repository/maven-public/")
        }
        
        // 华为云镜像源
        maven { 
            name = "HuaweiCloud Mirror"
            url = uri("https://repo.huaweicloud.com/repository/maven/")
        }
        
        // 清华大学镜像源
        maven {
            name = "Tsinghua Mirror"
            url = uri("https://mirrors.tuna.tsinghua.edu.cn/maven/")
        }
        
        // 本地仓库
        mavenLocal()
        
        // 官方仓库作为最后备用
        google()
        mavenCentral()
        gradlePluginPortal()
        
        println "✅ 已配置镜像源: ${repositories.collect { it.name ?: it.toString() }}"
    }
    
    // 配置下载超时和重试
    gradle.projectsEvaluated {
        tasks.withType(JavaCompile) {
            options.compilerArgs << "-Xlint:unchecked"
            options.compilerArgs << "-Xlint:deprecation"
        }
    }
}

// Settings 阶段的插件管理配置
settingsEvaluated { settings ->
    settings.pluginManagement {
        repositories {
            // 阿里云镜像源
            maven { 
                name = "AliYun Gradle Plugin"
                url = uri("https://maven.aliyun.com/repository/gradle-plugin") 
            }
            maven { 
                name = "AliYun Central"
                url = uri("https://maven.aliyun.com/repository/central") 
            }
            maven { 
                name = "AliYun Public"
                url = uri("https://maven.aliyun.com/repository/public") 
            }
            
            // 腾讯云镜像源
            maven { 
                name = "Tencent"
                url = uri("https://mirrors.cloud.tencent.com/nexus/repository/maven-public/") 
            }
            
            // 本地仓库
            mavenLocal()
            
            // 官方仓库作为备用
            google()
            mavenCentral()
            gradlePluginPortal()
        }
    }
}

// 项目配置阶段
gradle.beforeProject { project ->
    // 为每个项目配置仓库
    project.repositories {
        // 同样的镜像源配置...
    }
}

println "🎉 国内镜像源配置完成！" 