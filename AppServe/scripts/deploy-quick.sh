#!/bin/bash

# 有故事APP快速增量部署脚本
# 仅重新构建和重启应用，不重启数据库和Redis

set -e

echo "🚀 开始快速增量部署..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 加载环境变量
load_env() {
    if [ -f ".env.production" ]; then
        echo "📋 加载环境变量..."
        source .env.production
        echo -e "${GREEN}✅ 环境变量加载完成${NC}"
    else
        echo -e "${RED}❌ .env.production 文件不存在，请先执行完整部署${NC}"
        echo "请运行: ./scripts/deploy-direct.sh"
        exit 1
    fi
}

# 检查现有服务状态
check_services() {
    echo "🔍 检查现有服务状态..."
    
    # 检查必要容器是否运行
    services=("story-app-postgres" "story-app-redis")
    missing_services=()
    
    for service in "${services[@]}"; do
        if docker ps -q -f name=$service | grep -q .; then
            echo -e "  ${GREEN}✅ $service${NC}"
        else
            echo -e "  ${RED}❌ $service${NC}"
            missing_services+=("$service")
        fi
    done
    
    if [ ${#missing_services[@]} -ne 0 ]; then
        echo -e "${RED}❌ 缺少必要服务，请执行完整部署:${NC}"
        printf '  %s\n' "${missing_services[@]}"
        echo "请运行: ./scripts/deploy-direct.sh"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 基础服务运行正常${NC}"
}

# 智能检测是否需要重新安装依赖
check_dependencies() {
    echo "🔍 检查依赖变化..."
    
    # 检查package.json是否有变化
    if [ -f ".deploy-cache/package.json.hash" ]; then
        old_hash=$(cat .deploy-cache/package.json.hash)
        new_hash=$(sha256sum package.json | cut -d' ' -f1)
        
        if [ "$old_hash" = "$new_hash" ]; then
            echo -e "${GREEN}✅ package.json 无变化，跳过依赖安装${NC}"
            return 0
        else
            echo -e "${YELLOW}⚠️ package.json 有变化，需要重新安装依赖${NC}"
            return 1
        fi
    else
        echo -e "${YELLOW}⚠️ 首次快速部署，需要安装依赖${NC}"
        return 1
    fi
}

# 快速构建应用
quick_build() {
    echo "🏗️ 快速构建应用..."
    
    # 创建缓存目录
    mkdir -p .deploy-cache
    
    # 检查是否需要重新安装依赖
    if check_dependencies; then
        echo "📦 使用现有依赖..."
    else
        echo "📥 重新安装依赖..."
        docker run --rm \
            -v $(pwd):/workspace \
            -w /workspace \
            node:22.16.0-alpine \
            sh -c "npm ci"
        
        # 保存package.json哈希
        sha256sum package.json > .deploy-cache/package.json.hash
    fi
    
    # 清理旧构建
    rm -rf dist 2>/dev/null || true
    
    # 构建应用
    echo "🔨 编译TypeScript..."
    docker run --rm \
        -v $(pwd):/workspace \
        -w /workspace \
        node:22.16.0-alpine \
        sh -c "./node_modules/.bin/nest build"
    
    # 验证构建结果
    if [ ! -f "dist/main.js" ]; then
        echo -e "${RED}❌ 构建失败：dist/main.js 不存在${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 应用构建完成${NC}"
    echo "📏 主文件大小: $(du -h dist/main.js | cut -f1)"
    
    # 构建完成后，重新安装仅生产依赖以减少容器大小
    echo "⚡ 优化生产环境依赖..."
    docker run --rm \
        -v $(pwd):/workspace \
        -w /workspace \
        node:22.16.0-alpine \
        sh -c "npm ci --omit=dev --silent"
}

# 重启应用容器
restart_app() {
    echo "🔄 重启应用容器..."
    
    # 停止旧应用容器
    if docker ps -q -f name=story-app-backend | grep -q .; then
        echo "  🛑 停止旧应用容器..."
        docker stop story-app-backend
        docker rm story-app-backend
    fi
    
    # 创建启动脚本
    cat > start-app.sh << 'APP_EOF'
#!/bin/sh
set -e

echo "📂 切换到应用目录..."
cd /app

echo "🔍 检查应用文件..."
if [ ! -f "dist/main.js" ]; then
    echo "❌ 应用文件不存在，请检查构建过程"
    exit 1
fi

echo "🚀 启动应用..."
exec node dist/main.js
APP_EOF

    chmod +x start-app.sh
    
    # 启动新应用容器
    docker run -d \
        --name story-app-backend \
        --network story-app-network \
        --restart always \
        -p 3000:3000 \
        --env-file .env.production \
        -v $(pwd):/app \
        -v $(pwd)/start-app.sh:/start-app.sh \
        -v $(pwd)/logs:/app/logs \
        -v /etc/localtime:/etc/localtime:ro \
        -w /app \
        node:22.16.0-alpine \
        /start-app.sh
    
    echo "⏳ 等待应用启动..."
    sleep 10
}

# 快速健康检查
quick_health_check() {
    echo "🏥 快速健康检查..."
    
    # 等待应用启动
    for i in {1..30}; do
        if docker exec story-app-backend netstat -tlnp 2>/dev/null | grep -q ":3000"; then
            if docker logs story-app-backend 2>/dev/null | grep -q "Nest application successfully started"; then
                echo -e "${GREEN}✅ 应用启动成功！${NC}"
                break
            fi
        fi
        
        if [ $i -eq 30 ]; then
            echo -e "${RED}❌ 应用启动超时${NC}"
            echo "📋 应用日志："
            docker logs story-app-backend --tail 10
            exit 1
        fi
        
        echo "  ⏳ 等待应用启动... ($i/30)"
        sleep 2
    done
    
    # 测试健康检查接口
    sleep 3
    if curl -f http://localhost/api/v1/health &>/dev/null; then
        echo -e "${GREEN}✅ 健康检查通过${NC}"
    else
        echo -e "${YELLOW}⚠️ 健康检查失败，但应用已启动${NC}"
    fi
}

# 显示部署信息
show_info() {
    echo ""
    echo -e "${BLUE}🎉 快速部署完成！${NC}"
    echo -e "${GREEN}================================${NC}"
    echo "⚡ 部署类型: 增量部署（仅重启应用）"
    echo "🌐 访问地址: http://************"
    echo "🔍 健康检查: http://************/api/v1/health"
    echo ""
    echo "📝 管理命令:"
    echo "  查看应用日志: docker logs story-app-backend -f"
    echo "  完整部署: ./scripts/deploy-direct.sh"
    echo "  快速部署: ./scripts/deploy-quick.sh"
    echo -e "${GREEN}================================${NC}"
}

# 主函数
main() {
    local start_time=$(date +%s)
    
    load_env
    check_services
    quick_build
    restart_app
    quick_health_check
    show_info
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    echo -e "${BLUE}⏱️ 部署耗时: ${duration}秒${NC}"
}

# 使用说明
usage() {
    echo "快速增量部署脚本"
    echo ""
    echo "用法: $0"
    echo ""
    echo "特点:"
    echo "  - 仅重新构建和重启应用容器"
    echo "  - 保留数据库和Redis容器"
    echo "  - 智能检测依赖变化"
    echo "  - 大幅缩短部署时间"
    echo ""
    echo "适用场景:"
    echo "  - 代码迭代更新"
    echo "  - 配置文件修改"
    echo "  - 应用逻辑调整"
    echo ""
    echo "注意："
    echo "  - 首次部署或基础服务变更请使用: ./deploy-direct.sh"
    echo "  - 数据库结构变更请使用完整部署"
}

# 参数处理
case "${1:-}" in
    --help)
        usage
        exit 0
        ;;
    "")
        main
        ;;
    *)
        echo "未知选项: $1"
        usage
        exit 1
        ;;
esac 