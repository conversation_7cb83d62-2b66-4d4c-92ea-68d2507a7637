import {
  IsString,
  <PERSON><PERSON><PERSON>,
  <PERSON>UUI<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "class-validator";
import { Type } from "class-transformer";
import { CommentTargetType } from "../entities/comment.entity";

/**
 * 创建评论DTO
 */
export class CreateCommentDto {
  @IsEnum(CommentTargetType, {
    message: "评论目标类型必须是 story 或 comment",
  })
  @Type(() => String)
  targetType: CommentTargetType;

  @IsUUID("4", {
    message: "目标ID必须是有效的UUID格式",
  })
  targetId: string;

  @IsString({
    message: "评论内容必须是字符串",
  })
  @MinLength(1, {
    message: "评论内容不能为空",
  })
  @MaxLength(2000, {
    message: "评论内容不能超过2000个字符",
  })
  content: string;

  @IsOptional()
  @IsUUID("4", {
    message: "父评论ID必须是有效的UUID格式",
  })
  parentCommentId?: string;

  @IsOptional()
  @IsUUID("4", {
    message: "根评论ID必须是有效的UUID格式",
  })
  rootCommentId?: string;

  @IsOptional()
  @Type(() => Object)
  metadata?: Record<string, unknown>;
}
