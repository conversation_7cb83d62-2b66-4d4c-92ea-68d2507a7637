import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  HttpCode,
  ParseUUIDPipe,
  BadRequestException,
  ForbiddenException,
  UseInterceptors,
  UploadedFile,
} from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse as SwaggerApiResponse,
  ApiBearerAuth,
  ApiParam,
} from "@nestjs/swagger";
import { FileInterceptor } from "@nestjs/platform-express";
import { MultipartFile } from "@fastify/multipart";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { StoriesService } from "./stories.service";
import {
  CreateStoryDto,
  UpdateStoryDto,
  StoryResponseDto,
  StoryQueryDto,
} from "./dto";
import type { PaginatedResponse } from "../../common/dto/paginated-response.dto";
import { ApiResponse } from "../../common/dto/api-response.dto";
import { AuthRequest } from "../../common/types/request.types";

/**
 * 故事控制器
 * 处理故事相关的HTTP请求
 */
@ApiTags("故事管理")
@Controller("stories")
@UseGuards(JwtAuthGuard)
export class StoriesController {
  constructor(private readonly storiesService: StoriesService) {}

  /**
   * 创建故事
   * POST /stories
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: "创建故事" })
  @SwaggerApiResponse({
    status: 201,
    description: "创建成功",
    type: ApiResponse,
  })
  @ApiBearerAuth()
  async createStory(
    @Body() createStoryDto: CreateStoryDto,
    @Request() req: AuthRequest,
  ): Promise<ApiResponse<StoryResponseDto>> {
    try {
      const story = await this.storiesService.createStory(
        req.user.id,
        createStoryDto,
      );
      const storyResponse = new StoryResponseDto(story);

      return new ApiResponse(
        true,
        "故事创建成功",
        storyResponse,
        HttpStatus.CREATED,
      );
    } catch (error) {
      throw new BadRequestException(
        error instanceof Error ? error.message : String(error),
      );
    }
  }

  /**
   * 更新故事
   * PUT /stories/:id
   */
  @Put(":id")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "更新故事" })
  @ApiParam({ name: "id", description: "故事ID", type: String })
  @SwaggerApiResponse({
    status: 200,
    description: "更新成功",
    type: ApiResponse,
  })
  @ApiBearerAuth()
  async updateStory(
    @Param("id", ParseUUIDPipe) id: string,
    @Body() updateStoryDto: UpdateStoryDto,
    @Request() req: AuthRequest,
  ): Promise<ApiResponse<StoryResponseDto>> {
    try {
      const story = await this.storiesService.updateStory(
        id,
        updateStoryDto,
        req.user.id,
      );
      const storyResponse = new StoryResponseDto(story);

      return new ApiResponse(
        true,
        "故事更新成功",
        storyResponse,
        HttpStatus.OK,
      );
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }
      throw new BadRequestException(
        error instanceof Error ? error.message : String(error),
      );
    }
  }

  /**
   * 删除故事
   * DELETE /stories/:id
   */
  @Delete(":id")
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: "删除故事" })
  @ApiParam({ name: "id", description: "故事ID", type: String })
  @SwaggerApiResponse({ status: 204, description: "删除成功" })
  @ApiBearerAuth()
  async deleteStory(
    @Param("id", ParseUUIDPipe) id: string,
    @Request() req: AuthRequest,
  ): Promise<void> {
    try {
      await this.storiesService.deleteStory(id, req.user.id);
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }
      throw new BadRequestException(
        error instanceof Error ? error.message : String(error),
      );
    }
  }

  /**
   * 获取故事详情
   * GET /stories/:id
   */
  @Get(":id")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "获取故事详情" })
  @ApiParam({ name: "id", description: "故事ID", type: String })
  @SwaggerApiResponse({
    status: 200,
    description: "获取成功",
    type: ApiResponse,
  })
  @ApiBearerAuth()
  async getStoryById(
    @Param("id", ParseUUIDPipe) id: string,
    @Request() req: AuthRequest,
  ): Promise<ApiResponse<StoryResponseDto>> {
    try {
      // 验证访问权限
      const hasAccess = await this.storiesService.validateStoryAccess(
        id,
        req.user.id,
      );
      if (!hasAccess) {
        throw new ForbiddenException("无权访问此故事");
      }

      const story = await this.storiesService.findById(id);

      // 增加浏览量（非作者访问时）
      if (story.userId !== req.user.id) {
        await this.storiesService.incrementViewCount(id);
      }

      const storyResponse = new StoryResponseDto(story);

      return new ApiResponse(
        true,
        "获取故事成功",
        storyResponse,
        HttpStatus.OK,
      );
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }
      throw new BadRequestException(
        error instanceof Error ? error.message : String(error),
      );
    }
  }

  /**
   * 获取故事列表
   * GET /stories
   */
  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "获取故事列表" })
  @SwaggerApiResponse({
    status: 200,
    description: "获取成功",
    type: ApiResponse,
  })
  @ApiBearerAuth()
  async getStories(
    @Query() query: StoryQueryDto,
    @Request() req: AuthRequest,
  ): Promise<ApiResponse<PaginatedResponse<StoryResponseDto>>> {
    try {
      let result: PaginatedResponse<StoryResponseDto>;

      // 如果指定了作者ID，获取该作者的故事
      if (query.authorId) {
        // 如果是查询自己的故事，返回所有状态
        if (query.authorId === req.user.id) {
          result = await this.storiesService.getUserStories(req.user.id, query);
        } else {
          // 查询其他用户的故事，只返回可访问的
          result = await this.storiesService.getAccessibleStories(
            { ...query, authorId: query.authorId },
            req.user.id,
          );
        }
      } else {
        // 获取可访问的故事列表
        result = await this.storiesService.getAccessibleStories(
          query,
          req.user.id,
        );
      }

      return new ApiResponse(true, "获取故事列表成功", result, HttpStatus.OK);
    } catch (error) {
      throw new BadRequestException(
        error instanceof Error ? error.message : String(error),
      );
    }
  }

  /**
   * 获取公开故事列表
   * GET /stories/public
   */
  @Get("public")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "获取公开故事列表" })
  @SwaggerApiResponse({
    status: 200,
    description: "获取成功",
    type: ApiResponse,
  })
  @ApiBearerAuth()
  async getPublicStories(
    @Request() req: AuthRequest,
    @Query() query: StoryQueryDto,
  ): Promise<ApiResponse<PaginatedResponse<StoryResponseDto>>> {
    try {
      const result = await this.storiesService.getPublicStories(
        query,
        req.user.id,
      );

      return new ApiResponse(
        true,
        "获取公开故事列表成功",
        result,
        HttpStatus.OK,
      );
    } catch (error) {
      throw new BadRequestException(
        error instanceof Error ? error.message : String(error),
      );
    }
  }

  /**
   * 获取我的故事列表
   * GET /stories/my
   */
  @Get("my")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "获取我的故事列表" })
  @SwaggerApiResponse({
    status: 200,
    description: "获取成功",
    type: ApiResponse,
  })
  @ApiBearerAuth()
  async getMyStories(
    @Query() query: StoryQueryDto,
    @Request() req: AuthRequest,
  ): Promise<ApiResponse<PaginatedResponse<StoryResponseDto>>> {
    try {
      const result = await this.storiesService.getUserStories(
        req.user.id,
        query,
      );

      return new ApiResponse(
        true,
        "获取我的故事列表成功",
        result,
        HttpStatus.OK,
      );
    } catch (error) {
      throw new BadRequestException(
        error instanceof Error ? error.message : String(error),
      );
    }
  }

  /**
   * 点赞故事
   * PUT /stories/:id/like
   */
  @Put(":id/like")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "点赞故事" })
  @ApiParam({ name: "id", description: "故事ID", type: String })
  @SwaggerApiResponse({ status: 200, description: "点赞成功" })
  @ApiBearerAuth()
  async likeStory(
    @Request() req: AuthRequest,
    @Param("id", ParseUUIDPipe) id: string,
  ): Promise<ApiResponse<null>> {
    try {
      // 验证访问权限
      const hasAccess = await this.storiesService.validateStoryAccess(
        id,
        req.user.id,
      );
      if (!hasAccess) {
        throw new ForbiddenException("无权访问此故事");
      }

      // 检查故事是否允许点赞
      const story = await this.storiesService.findById(id);
      if (!story.allowLikes) {
        throw new ForbiddenException("该故事不允许点赞");
      }

      await this.storiesService.incrementLikeCount(id);

      return new ApiResponse(true, "点赞成功", null, HttpStatus.OK);
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }
      throw new BadRequestException(
        error instanceof Error ? error.message : String(error),
      );
    }
  }

  /**
   * 发布故事
   * PUT /stories/:id/publish
   */
  @Put(":id/publish")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "发布故事" })
  @ApiParam({ name: "id", description: "故事ID", type: String })
  @SwaggerApiResponse({
    status: 200,
    description: "发布成功",
    type: ApiResponse,
  })
  @ApiBearerAuth()
  async publishStory(
    @Param("id", ParseUUIDPipe) id: string,
    @Request() req: AuthRequest,
  ): Promise<ApiResponse<StoryResponseDto>> {
    try {
      const story = await this.storiesService.publishStory(id, req.user.id);

      const storyResponse = new StoryResponseDto(story);

      return new ApiResponse(
        true,
        "故事发布成功",
        storyResponse,
        HttpStatus.OK,
      );
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }
      throw new BadRequestException(
        error instanceof Error ? error.message : String(error),
      );
    }
  }

  /**
   * 归档故事
   * PUT /stories/:id/archive
   */
  @Put(":id/archive")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "归档故事" })
  @ApiParam({ name: "id", description: "故事ID", type: String })
  @SwaggerApiResponse({
    status: 200,
    description: "归档成功",
    type: ApiResponse,
  })
  @ApiBearerAuth()
  async archiveStory(
    @Param("id", ParseUUIDPipe) id: string,
    @Request() req: AuthRequest,
  ): Promise<ApiResponse<StoryResponseDto>> {
    try {
      const story = await this.storiesService.updateStory(
        id,
        { status: 3 }, // 归档状态
        req.user.id,
      );

      const storyResponse = new StoryResponseDto(story);

      return new ApiResponse(
        true,
        "故事归档成功",
        storyResponse,
        HttpStatus.OK,
      );
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }
      throw new BadRequestException(
        error instanceof Error ? error.message : String(error),
      );
    }
  }

  /**
   * 上传故事封面
   * POST /stories/:id/cover
   */
  @Post(":id/cover")
  @HttpCode(HttpStatus.OK)
  @UseInterceptors(FileInterceptor("cover"))
  @ApiOperation({ summary: "上传故事封面" })
  @ApiParam({ name: "id", description: "故事ID", type: String })
  @SwaggerApiResponse({ status: 200, description: "上传成功" })
  @ApiBearerAuth()
  async uploadStoryCover(
    @Request() req: AuthRequest,
    @Param("id", ParseUUIDPipe) id: string,
    @UploadedFile() file: MultipartFile,
  ): Promise<ApiResponse<{ coverUrl: string }>> {
    try {
      if (!file) {
        throw new BadRequestException("请上传封面图片");
      }

      // 验证文件类型
      const allowedTypes = ["image/jpeg", "image/png", "image/webp"];
      if (!allowedTypes.includes(file.mimetype)) {
        throw new BadRequestException("只支持 JPEG、PNG、WebP 格式的图片");
      }

      // 验证文件大小（5MB）
      const buffer = await file.toBuffer();
      if (buffer.length > 5 * 1024 * 1024) {
        throw new BadRequestException("图片大小不能超过5MB");
      }

      // 这里应该实现文件上传逻辑
      // 暂时返回模拟URL
      const coverUrl = `https://example.com/uploads/${id}-${Date.now()}.jpg`;

      // 更新故事封面
      await this.storiesService.updateStory(
        id,
        { coverImageUrl: coverUrl },
        req.user.id,
      );

      return new ApiResponse(true, "封面上传成功", { coverUrl }, HttpStatus.OK);
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }
      throw new BadRequestException(
        error instanceof Error ? error.message : String(error),
      );
    }
  }

  /**
   * 验证故事内容
   * POST /stories/validate-content
   */
  @Post("validate-content")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "验证故事内容" })
  @SwaggerApiResponse({ status: 200, description: "验证成功" })
  @ApiBearerAuth()
  async validateContent(
    @Request() req: AuthRequest,
    @Body() body: { content: string },
  ): Promise<ApiResponse<{ isValid: boolean; suggestions?: string[] }>> {
    try {
      const result = await this.storiesService.validateStoryContent(
        body.content,
      );

      return new ApiResponse(true, "内容验证完成", result, HttpStatus.OK);
    } catch (error) {
      throw new BadRequestException(
        error instanceof Error ? error.message : String(error),
      );
    }
  }
}
