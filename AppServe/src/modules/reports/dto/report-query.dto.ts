import { IsOptional, IsEnum, IsString, IsDateString } from "class-validator";
import { Transform, Type } from "class-transformer";
import {
  ReportType,
  ReportReason,
  ReportStatus,
  ReportSeverity,
} from "../entities/report.entity";
import { PaginationQueryDto } from "../../../common/dto/pagination-query.dto";

/**
 * 举报查询DTO
 */
export class ReportQueryDto extends PaginationQueryDto {
  @IsOptional()
  @IsEnum(ReportType, {
    message: "举报类型必须是有效的选项",
  })
  reportType?: ReportType;

  @IsOptional()
  @IsEnum(ReportReason, {
    message: "举报原因必须是有效的选项",
  })
  reason?: ReportReason;

  @IsOptional()
  @IsEnum(ReportStatus, {
    message: "处理状态必须是有效的选项",
  })
  status?: ReportStatus;

  @IsOptional()
  @IsEnum(ReportSeverity, {
    message: "严重程度必须是有效的选项",
  })
  severity?: ReportSeverity;

  @IsOptional()
  @IsString({
    message: "举报者ID必须是字符串",
  })
  reporterId?: string;

  @IsOptional()
  @IsString({
    message: "审核员ID必须是字符串",
  })
  reviewerId?: string;

  @IsOptional()
  @IsDateString(
    {},
    {
      message: "开始时间必须是有效的日期格式",
    },
  )
  startDate?: string;

  @IsOptional()
  @IsDateString(
    {},
    {
      message: "结束时间必须是有效的日期格式",
    },
  )
  endDate?: string;

  @IsOptional()
  @Transform(({ value }: { value: string }) => value?.toLowerCase())
  @IsString({
    message: "排序字段必须是字符串",
  })
  sortBy?: string = "createdAt";

  @IsOptional()
  @Transform(({ value }: { value: string }) => value?.toUpperCase())
  @Type(() => String)
  sortOrder?: "ASC" | "DESC" = "DESC";
}
