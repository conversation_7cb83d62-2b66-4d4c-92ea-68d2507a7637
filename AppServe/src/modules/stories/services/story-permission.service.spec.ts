import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import type { Repository } from "typeorm";
import { StoryPermissionService } from "./story-permission.service";
import { Story } from "../entities/story.entity";
import { User } from "../../users/entities/user.entity";
import { Character } from "../../characters/entities/character.entity";
import { CharacterLighting } from "../../characters/entities/character-lighting.entity";
import { UserRelationship } from "../../users/entities/user-relationship.entity";

describe("StoryPermissionService", () => {
  let service: StoryPermissionService;
  let mockStoryRepository: jest.Mocked<Repository<Story>>;
  let mockUserRepository: jest.Mocked<Repository<User>>;
  let mockCharacterRepository: jest.Mocked<Repository<Character>>;
  let mockCharacterLightingRepository: Repository<CharacterLighting>;
  let mockUserRelationshipRepository: Repository<UserRelationship>;

  // Mock数据
  const mockAuthor = {
    id: "author-id",
    userNumber: "author123",
    nickname: "作者",
    username: "author",
    phone: "13800138001",
    email: "<EMAIL>",
    avatarUrl: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    isActive: true,
    status: "active",
  };

  const mockCurrentUser = {
    id: "user-id",
    userNumber: "user123",
    nickname: "用户",
    username: "user",
    phone: "13800138002",
    email: "<EMAIL>",
    avatarUrl: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    isActive: true,
    status: "active",
  };

  const mockStory = {
    id: "story-id",
    title: "测试故事",
    content: "这是一个测试故事的内容",
    description: "故事描述",
    userId: "author-id",
    themeId: "theme-id",
    status: 1,
    viewCount: 100,
    likeCount: 10,
    commentCount: 5,
    shareCount: 2,
    isPublic: true,
    tags: ["测试", "故事"],
    permissionLevel: "public",
    allowLikes: true,
    allowComments: true,
    allowSharing: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    publishedAt: new Date(),
  };

  const mockCharacterLighting = {
    id: "lighting-id",
    characterId: "character-id",
    lighterUserId: "user-id",
    confirmedAt: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockUserRelationship = {
    id: "relationship-id",
    userId: "author-id",
    relatedUserId: "user-id",
    status: 1, // CONFIRMED
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const mockStoryRepositoryMethods = {
      findOne: jest.fn(),
      createQueryBuilder: jest.fn(),
    };

    const mockUserRepositoryMethods = {
      findOne: jest.fn(),
    };

    const mockCharacterRepositoryMethods = {
      findOne: jest.fn(),
    };

    const mockCharacterLightingRepositoryMethods = {
      findOne: jest.fn(),
      createQueryBuilder: jest.fn(),
      count: jest.fn(),
    };

    const mockUserRelationshipRepositoryMethods = {
      findOne: jest.fn(),
      count: jest.fn(),
    };

    // Mock QueryBuilder
    const mockQueryBuilder = {
      innerJoin: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      getOne: jest.fn(),
      getCount: jest.fn(),
    };

    mockCharacterLightingRepositoryMethods.createQueryBuilder.mockReturnValue(
      mockQueryBuilder,
    );

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        StoryPermissionService,
        {
          provide: getRepositoryToken(Story),
          useValue: mockStoryRepositoryMethods,
        },
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepositoryMethods,
        },
        {
          provide: getRepositoryToken(Character),
          useValue: mockCharacterRepositoryMethods,
        },
        {
          provide: getRepositoryToken(CharacterLighting),
          useValue: mockCharacterLightingRepositoryMethods,
        },
        {
          provide: getRepositoryToken(UserRelationship),
          useValue: mockUserRelationshipRepositoryMethods,
        },
      ],
    }).compile();

    service = module.get<StoryPermissionService>(StoryPermissionService);
    mockStoryRepository = module.get<jest.Mocked<Repository<Story>>>(
      getRepositoryToken(Story),
    );
    mockUserRepository = module.get<jest.Mocked<Repository<User>>>(
      getRepositoryToken(User),
    );
    mockCharacterRepository = module.get<jest.Mocked<Repository<Character>>>(
      getRepositoryToken(Character),
    );
    mockCharacterLightingRepository = module.get<Repository<CharacterLighting>>(
      getRepositoryToken(CharacterLighting),
    );
    mockUserRelationshipRepository = module.get<Repository<UserRelationship>>(
      getRepositoryToken(UserRelationship),
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("应该正确实例化", () => {
    expect(service).toBeDefined();
  });

  describe("validateAccess", () => {
    it("应该允许作者访问自己的故事", async () => {
      const result = await service.validateAccess(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        mockStory as any,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        mockAuthor as any,
      );

      expect(result).toBe(true);
    });

    it("应该允许访问公开故事", async () => {
      const publicStory = { ...mockStory, permissionLevel: "public" };

      const result = await service.validateAccess(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        publicStory as any,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        mockCurrentUser as any,
      );

      expect(result).toBe(true);
    });

    it("应该拒绝非作者访问私密故事", async () => {
      const privateStory = { ...mockStory, permissionLevel: "private" };

      const result = await service.validateAccess(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        privateStory as any,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        mockCurrentUser as any,
      );

      expect(result).toBe(false);
    });

    it("应该允许好友访问好友故事", async () => {
      const friendsStory = { ...mockStory, permissionLevel: "friends" };
      (mockUserRelationshipRepository.findOne as jest.Mock).mockResolvedValue(
        mockUserRelationship,
      );

      const result = await service.validateAccess(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        friendsStory as any,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        mockCurrentUser as any,
      );

      expect(result).toBe(true);
      expect(mockUserRelationshipRepository.findOne).toHaveBeenCalledWith({
        where: [
          {
            userId: "author-id",
            relatedUserId: "user-id",
            status: 1,
          },
          {
            userId: "user-id",
            relatedUserId: "author-id",
            status: 1,
          },
        ],
      });
    });

    it("应该拒绝非好友访问好友故事", async () => {
      const friendsStory = { ...mockStory, permissionLevel: "friends" };
      (mockUserRelationshipRepository.findOne as jest.Mock).mockResolvedValue(
        null,
      );

      const result = await service.validateAccess(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        friendsStory as any,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        mockCurrentUser as any,
      );

      expect(result).toBe(false);
    });

    it("应该允许点亮人物的用户访问人物专享故事", async () => {
      const charactersOnlyStory = {
        ...mockStory,
        permissionLevel: "characters_only",
      };
      const mockQueryBuilder = {
        innerJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(mockCharacterLighting),
      };
      (
        mockCharacterLightingRepository.createQueryBuilder as jest.Mock
      ).mockReturnValue(mockQueryBuilder);

      const result = await service.validateAccess(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        charactersOnlyStory as any,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        mockCurrentUser as any,
      );

      expect(result).toBe(true);
      expect(mockQueryBuilder.innerJoin).toHaveBeenCalledWith(
        "lighting.character",
        "character",
      );
      expect(mockQueryBuilder.innerJoin).toHaveBeenCalledWith(
        "character.stories",
        "story",
      );
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        "story.id = :storyId",
        {
          storyId: "story-id",
        },
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "lighting.lighterUserId = :userId",
        { userId: "user-id" },
      );
    });

    it("应该拒绝未点亮人物的用户访问人物专享故事", async () => {
      const charactersOnlyStory = {
        ...mockStory,
        permissionLevel: "characters_only",
      };
      const mockQueryBuilder = {
        innerJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(null),
      };
      (
        mockCharacterLightingRepository.createQueryBuilder as jest.Mock
      ).mockReturnValue(mockQueryBuilder);

      const result = await service.validateAccess(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        charactersOnlyStory as any,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        mockCurrentUser as any,
      );

      expect(result).toBe(false);
    });

    it("应该拒绝访问未知权限级别的故事", async () => {
      const unknownPermissionStory = {
        ...mockStory,
        permissionLevel: "unknown",
      };

      const result = await service.validateAccess(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        unknownPermissionStory as any,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        mockCurrentUser as any,
      );

      expect(result).toBe(false);
    });
  });

  describe("validateEditAccess", () => {
    it("应该允许作者编辑自己的故事", async () => {
      const result = await service.validateEditAccess(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        mockStory as any,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        mockAuthor as any,
      );

      expect(result).toBe(true);
    });

    it("应该拒绝非作者编辑故事", async () => {
      const result = await service.validateEditAccess(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        mockStory as any,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        mockCurrentUser as any,
      );

      expect(result).toBe(false);
    });
  });

  describe("validateDeleteAccess", () => {
    it("应该允许作者删除自己的故事", async () => {
      const result = await service.validateDeleteAccess(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        mockStory as any,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        mockAuthor as any,
      );

      expect(result).toBe(true);
    });

    it("应该拒绝非作者删除故事", async () => {
      const result = await service.validateDeleteAccess(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        mockStory as any,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        mockCurrentUser as any,
      );

      expect(result).toBe(false);
    });
  });

  describe("validateLikeAccess", () => {
    it("应该允许有访问权限且故事允许点赞的用户点赞", async () => {
      const publicStory = {
        ...mockStory,
        permissionLevel: "public",
        allowLikes: true,
      };

      const result = await service.validateLikeAccess(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        publicStory as any,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        mockCurrentUser as any,
      );

      expect(result).toBe(true);
    });

    it("应该拒绝作者给自己的故事点赞", async () => {
      const result = await service.validateLikeAccess(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        mockStory as any,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        mockAuthor as any,
      );

      expect(result).toBe(false);
    });

    it("应该拒绝对不允许点赞的故事点赞", async () => {
      const noLikesStory = { ...mockStory, allowLikes: false };

      const result = await service.validateLikeAccess(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        noLikesStory as any,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        mockCurrentUser as any,
      );

      expect(result).toBe(false);
    });

    it("应该拒绝无访问权限的用户点赞", async () => {
      const privateStory = { ...mockStory, permissionLevel: "private" };

      const result = await service.validateLikeAccess(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        privateStory as any,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        mockCurrentUser as any,
      );

      expect(result).toBe(false);
    });
  });

  describe("validateCommentAccess", () => {
    it("应该允许有访问权限且故事允许评论的用户评论", async () => {
      const publicStory = {
        ...mockStory,
        permissionLevel: "public",
        allowComments: true,
      };

      const result = await service.validateCommentAccess(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        publicStory as any,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        mockCurrentUser as any,
      );

      expect(result).toBe(true);
    });

    it("应该拒绝对不允许评论的故事评论", async () => {
      const noCommentsStory = { ...mockStory, allowComments: false };

      const result = await service.validateCommentAccess(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        noCommentsStory as any,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        mockCurrentUser as any,
      );

      expect(result).toBe(false);
    });

    it("应该拒绝无访问权限的用户评论", async () => {
      const privateStory = { ...mockStory, permissionLevel: "private" };

      const result = await service.validateCommentAccess(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        privateStory as any,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        mockCurrentUser as any,
      );

      expect(result).toBe(false);
    });
  });

  describe("validateShareAccess", () => {
    it("应该允许有访问权限且故事允许分享的用户分享", async () => {
      const publicStory = {
        ...mockStory,
        permissionLevel: "public",
        allowSharing: true,
      };

      const result = await service.validateShareAccess(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        publicStory as any,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        mockCurrentUser as any,
      );

      expect(result).toBe(true);
    });

    it("应该拒绝对不允许分享的故事分享", async () => {
      const noSharingStory = { ...mockStory, allowSharing: false };

      const result = await service.validateShareAccess(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        noSharingStory as any,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        mockCurrentUser as any,
      );

      expect(result).toBe(false);
    });

    it("应该拒绝无访问权限的用户分享", async () => {
      const privateStory = { ...mockStory, permissionLevel: "private" };

      const result = await service.validateShareAccess(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        privateStory as any,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        mockCurrentUser as any,
      );

      expect(result).toBe(false);
    });
  });

  describe("filterAccessibleStories", () => {
    it("应该过滤出有权限访问的故事", async () => {
      const stories = [
        { ...mockStory, id: "story1", permissionLevel: "public" },
        { ...mockStory, id: "story2", permissionLevel: "private" },
        { ...mockStory, id: "story3", permissionLevel: "public" },
      ];

      const result = await service.filterAccessibleStories(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        stories as any[],
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        mockCurrentUser as any,
      );

      expect(result).toHaveLength(2);
      expect(result[0].id).toBe("story1");
      expect(result[1].id).toBe("story3");
    });

    it("应该返回空数组当没有可访问的故事时", async () => {
      const stories = [
        { ...mockStory, id: "story1", permissionLevel: "private" },
        { ...mockStory, id: "story2", permissionLevel: "private" },
      ];

      const result = await service.filterAccessibleStories(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        stories as any[],
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        mockCurrentUser as any,
      );

      expect(result).toHaveLength(0);
    });
  });

  describe("getPermissionDescription", () => {
    it("应该返回正确的权限描述", () => {
      expect(service.getPermissionDescription("private")).toBe(
        "私密 - 仅作者可见",
      );
      expect(service.getPermissionDescription("friends")).toBe(
        "好友 - 仅好友可见",
      );
      expect(service.getPermissionDescription("public")).toBe(
        "公开 - 所有人可见",
      );
      expect(service.getPermissionDescription("characters_only")).toBe(
        "人物专享 - 仅点亮人物的用户可见",
      );
      expect(service.getPermissionDescription("unknown")).toBe("未知权限级别");
    });
  });

  describe("canChangePermissionLevel", () => {
    it("应该允许作者修改权限级别", async () => {
      const mockQueryBuilder = {
        innerJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getCount: jest.fn().mockResolvedValue(0),
      };
      (
        mockCharacterLightingRepository.createQueryBuilder as jest.Mock
      ).mockReturnValue(mockQueryBuilder);

      const result = await service.canChangePermissionLevel(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        mockStory as any,
        "friends",
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        mockAuthor as any,
      );

      expect(result).toBe(true);
    });

    it("应该拒绝非作者修改权限级别", async () => {
      const result = await service.canChangePermissionLevel(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        mockStory as any,
        "friends",
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        mockCurrentUser as any,
      );

      expect(result).toBe(false);
    });

    it("应该拒绝从人物专享修改为其他权限当有点亮记录时", async () => {
      const charactersOnlyStory = {
        ...mockStory,
        permissionLevel: "characters_only",
      };
      const mockQueryBuilder = {
        innerJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getCount: jest.fn().mockResolvedValue(5), // 有点亮记录
      };
      (
        mockCharacterLightingRepository.createQueryBuilder as jest.Mock
      ).mockReturnValue(mockQueryBuilder);

      const result = await service.canChangePermissionLevel(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        charactersOnlyStory as any,
        "public",
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        mockAuthor as any,
      );

      expect(result).toBe(false);
    });

    it("应该允许从人物专享修改为人物专享", async () => {
      const charactersOnlyStory = {
        ...mockStory,
        permissionLevel: "characters_only",
      };

      const result = await service.canChangePermissionLevel(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        charactersOnlyStory as any,
        "characters_only",
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        mockAuthor as any,
      );

      expect(result).toBe(true);
    });
  });

  describe("getAccessStats", () => {
    it("应该返回访问统计信息", async () => {
      const mockQueryBuilder = {
        innerJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getCount: jest.fn().mockResolvedValue(3),
      };
      (
        mockCharacterLightingRepository.createQueryBuilder as jest.Mock
      ).mockReturnValue(mockQueryBuilder);
      (mockUserRelationshipRepository.count as jest.Mock).mockResolvedValue(5);

      const friendsStory = {
        ...mockStory,
        permissionLevel: "friends",
        viewCount: 100,
      };
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = await service.getAccessStats(friendsStory as any);

      expect(result).toEqual({
        totalViews: 100,
        uniqueViewers: 70, // Math.floor(100 * 0.7)
        lightedCharacters: 3,
        friendViews: 5,
      });
    });

    it("应该返回非好友故事的统计信息", async () => {
      const mockQueryBuilder = {
        innerJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getCount: jest.fn().mockResolvedValue(2),
      };
      (
        mockCharacterLightingRepository.createQueryBuilder as jest.Mock
      ).mockReturnValue(mockQueryBuilder);

      const publicStory = {
        ...mockStory,
        permissionLevel: "public",
        viewCount: 50,
      };
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = await service.getAccessStats(publicStory as any);

      expect(result).toEqual({
        totalViews: 50,
        uniqueViewers: 35, // Math.floor(50 * 0.7)
        lightedCharacters: 2,
        friendViews: 0,
      });
    });
  });
});
