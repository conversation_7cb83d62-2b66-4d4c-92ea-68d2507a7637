/**
 * 集成测试连接性验证
 * 专门测试数据库和Redis连接问题
 */

import { Test } from "@nestjs/testing";
import type { TestingModule } from "@nestjs/testing";
import { AppModule } from "../../../src/app.module";
import { JwtAuthGuard } from "../../../src/modules/auth/guards/jwt-auth.guard";
import type { DataSource } from "typeorm";
import { getDataSourceToken } from "@nestjs/typeorm";
import * as Redis from "ioredis";

describe("Connection Test - Integration", () => {
  let app: TestingModule;
  let dataSource: DataSource;
  let redisClient: Redis.Redis;

  beforeAll(async () => {
    console.log("🔧 连接测试初始化...");

    // 设置环境变量
    process.env.NODE_ENV = "integration-test";
    process.env.CACHE_TYPE = "redis";
    process.env.REDIS_DISABLED = "false";

    // 创建测试模块
    app = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({
        canActivate: jest.fn().mockReturnValue(true),
      })
      .compile();

    // 获取数据库连接
    dataSource = app.get<DataSource>(getDataSourceToken());

    // 创建独立的Redis连接进行测试
    redisClient = new Redis.Redis({
      host: process.env.REDIS_HOST || "localhost",
      port: parseInt(process.env.REDIS_PORT || "6379", 10),
      password: process.env.REDIS_PASSWORD,
      keyPrefix: "connection-test:",
      db: 15,
    });

    console.log("📊 连接配置:", {
      redisHost: process.env.REDIS_HOST,
      redisPort: process.env.REDIS_PORT,
      redisPassword: process.env.REDIS_PASSWORD ? "***" : "未设置",
      nodeEnv: process.env.NODE_ENV,
      actualPassword: process.env.REDIS_PASSWORD, // 临时显示实际密码用于调试
    });
  });

  afterAll(async () => {
    if (redisClient) {
      await redisClient.disconnect();
    }
    if (app) {
      await app.close();
    }
  });

  it("应该能够连接到PostgreSQL数据库", async () => {
    expect(dataSource).toBeDefined();
    expect(dataSource.isInitialized).toBe(true);

    // 执行简单查询验证连接
    const result = await dataSource.query("SELECT 1 as test");
    expect(result).toEqual([{ test: 1 }]);
  });

  it("应该能够连接到Redis", async () => {
    expect(redisClient).toBeDefined();

    // 测试Redis ping
    const pong = await redisClient.ping();
    expect(pong).toBe("PONG");

    // 测试基本操作
    await redisClient.set("test-key", "test-value");
    const value = await redisClient.get("test-key");
    expect(value).toBe("test-value");

    // 清理测试数据
    await redisClient.del("test-key");
  });

  it("应该能够在Redis中设置和获取数据", async () => {
    const testData = { id: 1, name: "test" };

    await redisClient.set("test-json", JSON.stringify(testData));
    const retrieved = await redisClient.get("test-json");

    expect(JSON.parse(retrieved!)).toEqual(testData);

    // 清理
    await redisClient.del("test-json");
  });
});
