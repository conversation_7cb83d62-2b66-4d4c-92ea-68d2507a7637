/* eslint-disable @typescript-eslint/no-explicit-any, @typescript-eslint/no-unused-vars */
import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import {
  NotFoundException,
  BadRequestException,
  ConflictException,
  ForbiddenException,
} from "@nestjs/common";
import type { Repository } from "typeorm";

import { ReportsService } from "./reports.service";
import {
  Report,
  ReportType,
  ReportReason,
  ReportStatus,
  ReportSeverity,
} from "./entities/report.entity";
import { User } from "../users/entities/user.entity";
import { Story } from "../stories/entities/story.entity";
import { Comment } from "../comments/entities/comment.entity";
import type { CreateReportDto, UpdateReportDto, ReportQueryDto } from "./dto";

describe("ReportsService - 企业级单元测试", () => {
  let service: ReportsService;
  let mockReportRepository: jest.Mocked<Repository<Report>>;
  let mockUserRepository: jest.Mocked<Repository<User>>;
  let mockStoryRepository: jest.Mocked<Repository<Story>>;
  let mockCommentRepository: jest.Mocked<Repository<Comment>>;

  // 测试数据工厂
  const mockUser = {
    id: "user-001",
    username: "testuser",
    nickname: "测试用户",
    isActive: true,
  };

  const mockReporter = {
    id: "reporter-001",
    username: "reporter",
    nickname: "举报者",
    isActive: true,
  };

  const mockReviewer = {
    id: "reviewer-001",
    username: "admin",
    nickname: "管理员",
    isActive: true,
    role: "admin",
  };

  const mockStory = {
    id: "story-001",
    title: "测试故事",
    content: "这是一个测试故事的内容",
    userId: "user-001",
    user: mockUser,
    status: "published",
    permissionLevel: "public",
  };

  const mockComment = {
    id: "comment-001",
    storyId: "story-001",
    userId: "user-001",
    content: "这是一条测试评论",
    status: "published",
    user: mockUser,
  };

  const mockReport = {
    id: "report-001",
    reportType: ReportType.STORY,
    targetId: "story-001",
    reporterId: "reporter-001",
    reason: ReportReason.INAPPROPRIATE_CONTENT,
    description: "这个故事包含不当内容",
    status: ReportStatus.PENDING,
    severity: ReportSeverity.MEDIUM,
    reviewerId: null,
    reviewNote: null,
    reviewedAt: null,
    contentSnapshot: {
      title: "测试故事",
      content: "这是一个测试故事的内容",
      authorName: "测试用户",
    },
    metadata: { source: "web", device: "desktop" },
    createdAt: new Date(),
    updatedAt: new Date(),
    reporter: mockReporter,
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ReportsService,
        {
          provide: getRepositoryToken(Report),
          useValue: {
            findOne: jest.fn(),
            find: jest.fn(),
            save: jest.fn(),
            create: jest.fn(),
            update: jest.fn(),
            createQueryBuilder: jest.fn().mockReturnValue({
              leftJoinAndSelect: jest.fn().mockReturnThis(),
              where: jest.fn().mockReturnThis(),
              andWhere: jest.fn().mockReturnThis(),
              orderBy: jest.fn().mockReturnThis(),
              skip: jest.fn().mockReturnThis(),
              take: jest.fn().mockReturnThis(),
              getCount: jest.fn(),
              getMany: jest.fn(),
              getManyAndCount: jest.fn(),
              select: jest.fn().mockReturnThis(),
              addSelect: jest.fn().mockReturnThis(),
              getRawOne: jest.fn(),
              getRawMany: jest.fn(),
              groupBy: jest.fn().mockReturnThis(),
            }),
          },
        },
        {
          provide: getRepositoryToken(User),
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(Story),
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(Comment),
          useValue: {
            findOne: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<ReportsService>(ReportsService);
    mockReportRepository = module.get(getRepositoryToken(Report));
    mockUserRepository = module.get(getRepositoryToken(User));
    mockStoryRepository = module.get(getRepositoryToken(Story));
    mockCommentRepository = module.get(getRepositoryToken(Comment));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("createReport - 创建举报", () => {
    const createReportDto: CreateReportDto = {
      reportType: ReportType.STORY,
      targetId: "story-001",
      reason: ReportReason.INAPPROPRIATE_CONTENT,
      description: "这个故事包含不当内容，请审核处理",
      metadata: { source: "web", device: "desktop" },
    };

    it("应该成功创建故事举报", async () => {
      // Arrange
      mockUserRepository.findOne.mockResolvedValue(mockReporter as any);
      mockStoryRepository.findOne.mockResolvedValue(mockStory as any);
      mockReportRepository.findOne.mockResolvedValue(null);
      mockReportRepository.create.mockReturnValue(mockReport as any);
      mockReportRepository.save.mockResolvedValue(mockReport as any);

      // Act
      const result = await service.createReport(
        createReportDto,
        "reporter-001",
      );

      // Assert
      expect(mockUserRepository.findOne).toHaveBeenCalledWith({
        where: { id: "reporter-001" },
      });
      expect(mockStoryRepository.findOne).toHaveBeenCalledWith({
        where: { id: "story-001" },
        relations: ["user"],
      });
      expect(mockReportRepository.findOne).toHaveBeenCalledWith({
        where: {
          reporterId: "reporter-001",
          reportType: ReportType.STORY,
          targetId: "story-001",
          status: ReportStatus.PENDING,
        },
      });
      expect(mockReportRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          reportType: ReportType.STORY,
          targetId: "story-001",
          reporterId: "reporter-001",
          reason: ReportReason.INAPPROPRIATE_CONTENT,
          description: "这个故事包含不当内容，请审核处理",
        }),
      );
      expect(result).toEqual(
        expect.objectContaining({
          id: "report-001",
          reportType: ReportType.STORY,
          reason: ReportReason.INAPPROPRIATE_CONTENT,
        }),
      );
    });

    it("应该成功创建评论举报", async () => {
      // Arrange
      const commentReportDto = {
        ...createReportDto,
        reportType: ReportType.COMMENT,
        targetId: "comment-001",
      };
      const commentReport = {
        ...mockReport,
        reportType: ReportType.COMMENT,
        targetId: "comment-001",
      };

      mockUserRepository.findOne.mockResolvedValue(mockReporter as any);
      mockCommentRepository.findOne.mockResolvedValue(mockComment as any);
      mockReportRepository.findOne.mockResolvedValue(null);
      mockReportRepository.create.mockReturnValue(commentReport as any);
      mockReportRepository.save.mockResolvedValue(commentReport as any);

      // Act
      const result = await service.createReport(
        commentReportDto,
        "reporter-001",
      );

      // Assert
      expect(mockCommentRepository.findOne).toHaveBeenCalledWith({
        where: { id: "comment-001" },
        relations: ["user"],
      });
      expect(result.reportType).toBe(ReportType.COMMENT);
      expect(result.targetId).toBe("comment-001");
    });

    it("应该成功创建用户举报", async () => {
      // Arrange
      const userReportDto = {
        ...createReportDto,
        reportType: ReportType.USER,
        targetId: "user-001",
        reason: ReportReason.HARASSMENT,
      };
      const userReport = {
        ...mockReport,
        reportType: ReportType.USER,
        targetId: "user-001",
        reason: ReportReason.HARASSMENT,
      };

      mockUserRepository.findOne
        .mockResolvedValueOnce(mockReporter as any) // 举报者
        .mockResolvedValueOnce(mockUser as any); // 被举报用户
      mockReportRepository.findOne.mockResolvedValue(null);
      mockReportRepository.create.mockReturnValue(userReport as any);
      mockReportRepository.save.mockResolvedValue(userReport as any);

      // Act
      const result = await service.createReport(userReportDto, "reporter-001");

      // Assert
      expect(mockUserRepository.findOne).toHaveBeenNthCalledWith(2, {
        where: { id: "user-001" },
      });
      expect(result.reportType).toBe(ReportType.USER);
      expect(result.reason).toBe(ReportReason.HARASSMENT);
    });

    it("当举报者不存在时应该抛出NotFoundException", async () => {
      // Arrange
      mockUserRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.createReport(createReportDto, "non-existent"),
      ).rejects.toThrow(NotFoundException);
      expect(mockUserRepository.findOne).toHaveBeenCalled();
    });

    it("当举报目标不存在时应该抛出NotFoundException", async () => {
      // Arrange
      mockUserRepository.findOne.mockResolvedValue(mockReporter as any);
      mockStoryRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.createReport(createReportDto, "reporter-001"),
      ).rejects.toThrow(NotFoundException);
    });

    it("当重复举报时应该抛出ConflictException", async () => {
      // Arrange
      mockUserRepository.findOne.mockResolvedValue(mockReporter as any);
      mockStoryRepository.findOne.mockResolvedValue(mockStory as any);
      mockReportRepository.findOne.mockResolvedValue(mockReport as any);

      // Act & Assert
      await expect(
        service.createReport(createReportDto, "reporter-001"),
      ).rejects.toThrow(ConflictException);
    });

    it("应该正确创建内容快照", async () => {
      // Arrange
      mockUserRepository.findOne.mockResolvedValue(mockReporter as any);
      mockStoryRepository.findOne.mockResolvedValue(mockStory as any);
      mockReportRepository.findOne.mockResolvedValue(null);
      mockReportRepository.create.mockReturnValue(mockReport as any);
      mockReportRepository.save.mockResolvedValue(mockReport as any);

      // Act
      await service.createReport(createReportDto, "reporter-001");

      // Assert
      expect(mockReportRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          contentSnapshot: {
            title: "测试故事",
            content: "这是一个测试故事的内容",
            authorName: "测试用户",
          },
        }),
      );
    });

    it("当举报自己的内容时应该抛出BadRequestException", async () => {
      // Arrange
      const selfReportDto = {
        ...createReportDto,
        targetId: "story-001",
      };
      const ownStory = {
        ...mockStory,
        userId: "reporter-001", // 同一个用户
      };

      mockUserRepository.findOne.mockResolvedValue(mockReporter as any);
      mockStoryRepository.findOne.mockResolvedValue(ownStory as any);

      // Act & Assert
      await expect(
        service.createReport(selfReportDto, "reporter-001"),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe("getReports - 获取举报列表", () => {
    const queryDto: ReportQueryDto = {
      page: 1,
      limit: 10,
      status: ReportStatus.PENDING,
    };

    it("应该成功获取举报列表", async () => {
      // Arrange
      const mockQueryBuilder = mockReportRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        [mockReport],
        1,
      ]);

      // Act
      const result = await service.getReports(queryDto);

      // Assert
      expect(mockReportRepository.createQueryBuilder).toHaveBeenCalledWith(
        "report",
      );
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledTimes(2);
      expect(result).toEqual({
        data: expect.arrayContaining([
          expect.objectContaining({
            id: "report-001",
            reportType: ReportType.STORY,
          }),
        ]),
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      });
    });

    it("应该正确应用状态筛选", async () => {
      // Arrange
      const mockQueryBuilder = mockReportRepository.createQueryBuilder();

      // Act
      await service.getReports(queryDto);

      // Assert
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "report.status = :status",
        { status: ReportStatus.PENDING },
      );
    });

    it("应该正确应用举报类型筛选", async () => {
      // Arrange
      const queryWithType = { ...queryDto, reportType: ReportType.STORY };
      const mockQueryBuilder = mockReportRepository.createQueryBuilder();

      // Act
      await service.getReports(queryWithType);

      // Assert
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "report.reportType = :reportType",
        { reportType: ReportType.STORY },
      );
    });

    it("应该正确应用严重程度筛选", async () => {
      // Arrange
      const queryWithSeverity = { ...queryDto, severity: ReportSeverity.HIGH };
      const mockQueryBuilder = mockReportRepository.createQueryBuilder();

      // Act
      await service.getReports(queryWithSeverity);

      // Assert
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "report.severity = :severity",
        { severity: ReportSeverity.HIGH },
      );
    });

    it("应该正确应用日期范围筛选", async () => {
      // Arrange
      const startDate = new Date("2024-01-01");
      const endDate = new Date("2024-01-31");
      const queryWithDateRange = {
        ...queryDto,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
      };
      const mockQueryBuilder = mockReportRepository.createQueryBuilder();

      // Act
      await service.getReports(queryWithDateRange);

      // Assert
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "report.createdAt >= :startDate",
        { startDate },
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "report.createdAt <= :endDate",
        { endDate },
      );
    });
  });

  describe("getReportById - 获取举报详情", () => {
    it("应该成功获取举报详情", async () => {
      // Arrange
      mockReportRepository.findOne.mockResolvedValue(mockReport as any);

      // Act
      const result = await service.getReportById("report-001");

      // Assert
      expect(mockReportRepository.findOne).toHaveBeenCalledWith({
        where: { id: "report-001" },
        relations: ["reporter", "reviewer"],
      });
      expect(result).toEqual(
        expect.objectContaining({
          id: "report-001",
          reportType: ReportType.STORY,
          reporterName: "举报者",
        }),
      );
    });

    it("当举报不存在时应该抛出NotFoundException", async () => {
      // Arrange
      mockReportRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(service.getReportById("non-existent")).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe("updateReport - 更新举报", () => {
    const updateDto: UpdateReportDto = {
      status: ReportStatus.RESOLVED,
      reviewNote: "已处理，内容符合规范",
      severity: ReportSeverity.LOW,
    };

    it("应该成功更新举报状态", async () => {
      // Arrange
      const updatedReport = {
        ...mockReport,
        status: ReportStatus.RESOLVED,
        reviewerId: "reviewer-001",
        reviewNote: "已处理，内容符合规范",
        severity: ReportSeverity.LOW,
        reviewedAt: new Date(),
      };

      mockReportRepository.findOne.mockResolvedValue(mockReport as any);
      mockUserRepository.findOne.mockResolvedValue(mockReviewer as any);
      mockReportRepository.save.mockResolvedValue(updatedReport as any);

      // Act
      const result = await service.updateReport(
        "report-001",
        updateDto,
        "reviewer-001",
      );

      // Assert
      expect(mockReportRepository.findOne).toHaveBeenCalledWith({
        where: { id: "report-001" },
        relations: ["reporter", "reviewer"],
      });
      expect(mockUserRepository.findOne).toHaveBeenCalledWith({
        where: { id: "reviewer-001" },
      });
      expect(result.status).toBe(ReportStatus.RESOLVED);
      expect(result.reviewNote).toBe("已处理，内容符合规范");
      expect(result.reviewedAt).toBeDefined();
    });

    it("当举报不存在时应该抛出NotFoundException", async () => {
      // Arrange
      mockReportRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.updateReport("non-existent", updateDto, "reviewer-001"),
      ).rejects.toThrow(NotFoundException);
    });

    it("当审核员不存在时应该抛出NotFoundException", async () => {
      // Arrange
      mockReportRepository.findOne.mockResolvedValue(mockReport as any);
      mockUserRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.updateReport("report-001", updateDto, "non-existent"),
      ).rejects.toThrow(NotFoundException);
    });

    it("应该正确设置审核时间", async () => {
      // Arrange
      const updatedReport = {
        ...mockReport,
        status: ReportStatus.RESOLVED,
        reviewerId: "reviewer-001",
        reviewedAt: new Date(),
      };

      mockReportRepository.findOne.mockResolvedValue(mockReport as any);
      mockUserRepository.findOne.mockResolvedValue(mockReviewer as any);
      mockReportRepository.save.mockResolvedValue(updatedReport as any);

      // Act
      const result = await service.updateReport(
        "report-001",
        updateDto,
        "reviewer-001",
      );

      // Assert
      expect(result.reviewedAt).toBeDefined();
      expect(result.reviewerId).toBe("reviewer-001");
    });
  });

  describe("getReportStats - 获取举报统计", () => {
    it("应该成功获取举报统计数据", async () => {
      // Arrange
      const mockStats = {
        totalReports: "25",
        pendingReports: "10",
        underReviewReports: "5",
        resolvedReports: "8",
        rejectedReports: "2",
        escalatedReports: "0",
        storyReports: "15",
        commentReports: "8",
        userReports: "2",
        avgProcessingTime: "24.5",
        todayReports: "3",
        thisWeekReports: "12",
        thisMonthReports: "25",
      };

      const mockReasonStats = [
        { reason: ReportReason.INAPPROPRIATE_CONTENT, count: "12" },
        { reason: ReportReason.SPAM, count: "8" },
        { reason: ReportReason.HARASSMENT, count: "3" },
        { reason: ReportReason.OTHER, count: "2" },
      ];

      const mockSeverityStats = [
        { severity: ReportSeverity.LOW, count: "8" },
        { severity: ReportSeverity.MEDIUM, count: "12" },
        { severity: ReportSeverity.HIGH, count: "4" },
        { severity: ReportSeverity.CRITICAL, count: "1" },
      ];

      const mockQueryBuilder = mockReportRepository.createQueryBuilder();
      (mockQueryBuilder.getRawOne as jest.Mock).mockResolvedValue(mockStats);
      (mockQueryBuilder.getRawMany as jest.Mock)
        .mockResolvedValueOnce(mockReasonStats)
        .mockResolvedValueOnce(mockSeverityStats);

      // Act
      const result = await service.getReportStats();

      // Assert
      expect(result).toEqual({
        totalReports: 25,
        pendingReports: 10,
        underReviewReports: 5,
        resolvedReports: 8,
        rejectedReports: 2,
        escalatedReports: 0,
        storyReports: 15,
        commentReports: 8,
        userReports: 2,
        avgProcessingTime: 24.5,
        todayReports: 3,
        thisWeekReports: 12,
        thisMonthReports: 25,
        reasonStats: expect.arrayContaining([
          expect.objectContaining({
            reason: ReportReason.INAPPROPRIATE_CONTENT,
            count: 12,
            percentage: expect.any(Number),
          }),
        ]),
        severityStats: expect.arrayContaining([
          expect.objectContaining({
            severity: ReportSeverity.MEDIUM,
            count: 12,
            percentage: expect.any(Number),
          }),
        ]),
      });
    });

    it("应该正确计算百分比", async () => {
      // Arrange
      const mockStats = { totalReports: "100" };
      const mockReasonStats = [
        { reason: ReportReason.INAPPROPRIATE_CONTENT, count: "60" },
        { reason: ReportReason.SPAM, count: "40" },
      ];
      const mockSeverityStats = [
        { severity: ReportSeverity.HIGH, count: "30" },
        { severity: ReportSeverity.MEDIUM, count: "70" },
      ];

      const mockQueryBuilder = mockReportRepository.createQueryBuilder();
      (mockQueryBuilder.getRawOne as jest.Mock).mockResolvedValue(mockStats);
      (mockQueryBuilder.getRawMany as jest.Mock)
        .mockResolvedValueOnce(mockReasonStats)
        .mockResolvedValueOnce(mockSeverityStats);

      // Act
      const result = await service.getReportStats();

      // Assert
      expect(result.reasonStats[0].percentage).toBe(60);
      expect(result.reasonStats[1].percentage).toBe(40);
      expect(result.severityStats[0].percentage).toBe(30);
      expect(result.severityStats[1].percentage).toBe(70);
    });

    it("应该正确处理空统计数据", async () => {
      // Arrange
      const emptyStats = {
        totalReports: null,
        pendingReports: null,
        underReviewReports: null,
        resolvedReports: null,
        rejectedReports: null,
        escalatedReports: null,
        storyReports: null,
        commentReports: null,
        userReports: null,
        avgProcessingTime: null,
        todayReports: null,
        thisWeekReports: null,
        thisMonthReports: null,
      };

      const mockQueryBuilder = mockReportRepository.createQueryBuilder();
      (mockQueryBuilder.getRawOne as jest.Mock).mockResolvedValue(emptyStats);
      (mockQueryBuilder.getRawMany as jest.Mock)
        .mockResolvedValueOnce([])
        .mockResolvedValueOnce([]);

      // Act
      const result = await service.getReportStats();

      // Assert
      expect(result).toEqual({
        totalReports: 0,
        pendingReports: 0,
        underReviewReports: 0,
        resolvedReports: 0,
        rejectedReports: 0,
        escalatedReports: 0,
        storyReports: 0,
        commentReports: 0,
        userReports: 0,
        avgProcessingTime: 0,
        todayReports: 0,
        thisWeekReports: 0,
        thisMonthReports: 0,
        reasonStats: [],
        severityStats: [],
      });
    });
  });

  describe("私有方法测试", () => {
    it("buildReportResponse - 应该正确构建举报响应", () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = (service as any).buildReportResponse(mockReport);

      expect(result).toEqual(
        expect.objectContaining({
          id: "report-001",
          reportType: ReportType.STORY,
          targetId: "story-001",
          reporterId: "reporter-001",
          reporterName: "举报者",
          reason: ReportReason.INAPPROPRIATE_CONTENT,
          status: ReportStatus.PENDING,
          severity: ReportSeverity.MEDIUM,
        }),
      );
    });

    it("createContentSnapshot - 应该正确创建故事快照", () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = (service as any).createContentSnapshot(
        ReportType.STORY,
        mockStory,
      );

      expect(result).toEqual({
        title: "测试故事",
        content: "这是一个测试故事的内容",
        authorName: "测试用户",
      });
    });

    it("createContentSnapshot - 应该正确创建评论快照", () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = (service as any).createContentSnapshot(
        ReportType.COMMENT,
        mockComment,
      );

      expect(result).toEqual({
        content: "这是一条测试评论",
        authorName: "测试用户",
      });
    });

    it("createContentSnapshot - 应该正确创建用户快照", () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = (service as any).createContentSnapshot(
        ReportType.USER,
        mockUser,
      );

      expect(result).toEqual({
        authorName: "测试用户",
      });
    });

    it("validateReportTarget - 应该验证举报目标存在性", async () => {
      // Arrange
      mockStoryRepository.findOne.mockResolvedValue(mockStory as any);

      // Act & Assert
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      await expect(
        (service as any).validateReportTarget(ReportType.STORY, "story-001"),
      ).resolves.toEqual(mockStory);

      expect(mockStoryRepository.findOne).toHaveBeenCalledWith({
        where: { id: "story-001" },
        relations: ["user"],
      });
    });
  });

  describe("错误边界测试", () => {
    it("应该处理数据库连接错误", async () => {
      // Arrange
      mockReportRepository.createQueryBuilder.mockImplementation(() => {
        throw new Error("数据库连接失败");
      });

      // Act & Assert
      await expect(service.getReports({ page: 1, limit: 10 })).rejects.toThrow(
        "数据库连接失败",
      );
    });

    it("应该处理无效的举报类型", async () => {
      // Arrange
      const invalidReportDto = {
        reportType: "invalid_type" as ReportType,
        targetId: "story-001",
        reason: ReportReason.SPAM,
      };

      mockUserRepository.findOne.mockResolvedValue(mockReporter as any);

      // Act & Assert
      await expect(
        service.createReport(invalidReportDto, "reporter-001"),
      ).rejects.toThrow();
    });

    it("应该处理并发举报问题", async () => {
      // Arrange
      mockUserRepository.findOne.mockResolvedValue(mockReporter as any);
      mockStoryRepository.findOne.mockResolvedValue(mockStory as any);
      mockReportRepository.findOne
        .mockResolvedValueOnce(null) // 第一次检查无重复
        .mockResolvedValueOnce(mockReport as any); // 保存时发现重复

      mockReportRepository.save.mockRejectedValue(
        new Error("duplicate key value violates unique constraint"),
      );

      // Act & Assert
      await expect(
        service.createReport(
          {
            reportType: ReportType.STORY,
            targetId: "story-001",
            reason: ReportReason.SPAM,
          },
          "reporter-001",
        ),
      ).rejects.toThrow();
    });
  });
});
