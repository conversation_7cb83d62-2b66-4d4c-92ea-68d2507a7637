import 'package:freezed_annotation/freezed_annotation.dart';

part 'story_entity.freezed.dart';
part 'story_entity.g.dart';

/// 故事实体 - 基于API接口文档设计的数据模型
@freezed
class StoryEntity with _$StoryEntity {
  const factory StoryEntity({
    required String id,
    required String title,
    required String content,
    required String authorId,
    required String authorName,
    required String authorAvatar,
    required String categoryTag,
    required int categoryColor, // 存储颜色值
    required String categoryIcon,
    required DateTime createdAt,
    required DateTime updatedAt,
    required String location,
    required int likeCount,
    required bool isLiked,
    required List<String> images,
    required List<InteractionAvatar> interactionAvatars,
    required bool isPublished,
    required bool isArchived,
    String? coverImage,
  }) = _StoryEntity;

  factory StoryEntity.fromJson(Map<String, dynamic> json) =>
      _$StoryEntityFromJson(json);
}

/// 互动头像实体
@freezed
class InteractionAvatar with _$InteractionAvatar {
  const factory InteractionAvatar({
    required String id,
    required String userId,
    required String avatarUrl,
    required bool isLightedUp,
    required DateTime interactionTime,
  }) = _InteractionAvatar;

  factory InteractionAvatar.fromJson(Map<String, dynamic> json) =>
      _$InteractionAvatarFromJson(json);
}

/// 分页响应实体
@freezed
class StoryListResponse with _$StoryListResponse {
  const factory StoryListResponse({
    required List<StoryEntity> stories,
    required int totalCount,
    required int page,
    required int limit,
    required bool hasNextPage,
    required bool hasPreviousPage,
  }) = _StoryListResponse;

  factory StoryListResponse.fromJson(Map<String, dynamic> json) =>
      _$StoryListResponseFromJson(json);
}