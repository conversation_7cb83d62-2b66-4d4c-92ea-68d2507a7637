/**
 * 测试用户实体 - 简化版本，移除所有关系
 * 仅用于E2E测试，不包含复杂的关系映射
 */
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from "typeorm";

@Entity("users")
@Index("idx_user_email", ["email"], { unique: true })
@Index("idx_user_phone", ["phone"], { unique: true })
@Index("idx_user_username", ["username"], { unique: true })
export class TestUser {
  @PrimaryGeneratedColumn("increment", { comment: "用户ID" })
  id: number;

  @Column({ length: 100, unique: true, comment: "邮箱" })
  email: string;

  @Column({ length: 255, nullable: true, comment: "密码哈希" })
  password: string | null;

  @Column({ length: 50, unique: true, comment: "用户名" })
  username: string;

  @Column({ length: 100, nullable: true, comment: "昵称" })
  nickname: string | null;

  @Column({
    name: "avatar_url",
    length: 500,
    nullable: true,
    comment: "头像URL",
  })
  avatarUrl: string | null;

  @Column({ length: 20, nullable: true, unique: true, comment: "手机号" })
  phone: string | null;

  @Column({ type: "text", nullable: true, comment: "个人简介" })
  bio: string | null;

  @Column({ type: "date", nullable: true, comment: "生日" })
  birthday: Date | null;

  @Column({ length: 10, nullable: true, comment: "性别" })
  gender: string | null;

  @Column({ length: 100, nullable: true, comment: "位置" })
  location: string | null;

  @Column({ name: "ai_daily_quota", default: 5, comment: "AI每日配额" })
  aiDailyQuota: number;

  @Column({ name: "ai_daily_used", default: 0, comment: "AI每日已用次数" })
  aiDailyUsed: number;

  @Column({
    name: "ai_quota_reset_date",
    type: "date",
    default: () => "CURRENT_DATE",
    comment: "AI配额重置日期",
  })
  aiQuotaResetDate: Date;

  @Column({ default: "active", length: 20, comment: "用户状态" })
  status: string;

  @Column({ name: "is_active", default: true, comment: "账户是否激活" })
  isActive: boolean;

  // 企业级安全字段
  @Column({
    name: "last_login_at",
    type: "datetime",
    nullable: true,
    comment: "最后登录时间",
  })
  lastLoginAt: Date;

  @Column({
    name: "last_login_ip",
    length: 45,
    nullable: true,
    comment: "最后登录IP",
  })
  lastLoginIp: string | null;

  @Column({
    name: "failed_login_attempts",
    default: 0,
    comment: "连续失败登录次数",
  })
  failedLoginAttempts: number;

  @Column({
    name: "account_locked_until",
    type: "datetime",
    nullable: true,
    comment: "账户锁定到期时间",
  })
  accountLockedUntil: Date | null;

  @CreateDateColumn({ name: "created_at", comment: "创建时间" })
  createdAt: Date;

  @UpdateDateColumn({ name: "updated_at", comment: "更新时间" })
  updatedAt: Date;
}
