import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ConflictException,
  ForbiddenException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { UserFollow, FollowStatus } from "./entities/user-follow.entity";
import {
  FriendGroup,
  GroupType,
  GroupVisibility,
} from "./entities/friend-group.entity";
import {
  FriendGroupMember,
  MemberRole,
  MemberStatus,
} from "./entities/friend-group-member.entity";
import { User } from "../users/entities/user.entity";
import type {
  FollowUserDto,
  CreateFriendGroupDto,
  FollowListQueryDto,
  FriendGroupQueryDto,
  GroupMemberQueryDto,
  FollowResponseDto,
  UserBriefDto,
  FriendGroupResponseDto,
  GroupMemberResponseDto,
  SocialStatsResponseDto,
} from "./dto";
import type { PaginatedResponseDto } from "../../common/dto/paginated-response.dto";

@Injectable()
export class SocialService {
  constructor(
    @InjectRepository(UserFollow)
    private readonly followRepository: Repository<UserFollow>,
    @InjectRepository(FriendGroup)
    private readonly groupRepository: Repository<FriendGroup>,
    @InjectRepository(FriendGroupMember)
    private readonly groupMemberRepository: Repository<FriendGroupMember>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  // ==================== 关注关系管理 ====================

  /**
   * 关注用户
   */
  async followUser(
    followUserDto: FollowUserDto,
    followerId: string,
  ): Promise<FollowResponseDto> {
    const { userId: followingId, isSpecial, settings } = followUserDto;

    // 验证不能关注自己
    if (followerId === followingId) {
      throw new BadRequestException("不能关注自己");
    }

    // 验证被关注者存在
    const followingUser = await this.userRepository.findOne({
      where: { id: followingId },
    });
    if (!followingUser) {
      throw new NotFoundException("要关注的用户不存在");
    }

    // 检查是否已经关注
    const existingFollow = await this.followRepository.findOne({
      where: { followerId, followingId },
    });

    if (existingFollow) {
      if (existingFollow.status === FollowStatus.ACTIVE) {
        throw new ConflictException("已经关注该用户");
      }

      // 重新激活关注关系
      existingFollow.status = FollowStatus.ACTIVE;
      existingFollow.isSpecial = isSpecial || false;
      existingFollow.settings = settings || {};

      const savedFollow = await this.followRepository.save(existingFollow);
      return this.formatFollowResponse(savedFollow);
    }

    // 创建新的关注关系
    const follow = this.followRepository.create({
      followerId,
      followingId,
      isSpecial: isSpecial || false,
      settings: settings || {},
      status: FollowStatus.ACTIVE,
    });

    const savedFollow = await this.followRepository.save(follow);
    return this.formatFollowResponse(savedFollow);
  }

  /**
   * 取消关注用户
   */
  async unfollowUser(
    followingId: string,
    followerId: string,
  ): Promise<{ success: boolean; message: string }> {
    const follow = await this.followRepository.findOne({
      where: { followerId, followingId },
    });

    if (!follow) {
      throw new NotFoundException("关注关系不存在");
    }

    await this.followRepository.remove(follow);

    return {
      success: true,
      message: "已取消关注",
    };
  }

  /**
   * 获取关注列表（我关注的人）
   */
  async getFollowingList(
    userId: string,
    queryDto: FollowListQueryDto,
    currentUserId?: string,
  ): Promise<PaginatedResponseDto<UserBriefDto>> {
    const {
      page = 1,
      limit = 20,
      status = FollowStatus.ACTIVE,
      isSpecial,
      search,
      sortBy = "createdAt",
      sortOrder = "DESC",
    } = queryDto;

    const queryBuilder = this.followRepository
      .createQueryBuilder("follow")
      .leftJoinAndSelect("follow.following", "user")
      .where("follow.followerId = :userId", { userId })
      .andWhere("follow.status = :status", { status });

    // 应用筛选条件
    if (isSpecial !== undefined) {
      queryBuilder.andWhere("follow.isSpecial = :isSpecial", { isSpecial });
    }

    if (search) {
      queryBuilder.andWhere(
        "(user.nickname ILIKE :search OR user.username ILIKE :search)",
        { search: `%${search}%` },
      );
    }

    // 排序
    const validSortFields = ["createdAt", "updatedAt"];
    const sortField = validSortFields.includes(sortBy) ? sortBy : "createdAt";
    queryBuilder.orderBy(`follow.${sortField}`, sortOrder);

    // 分页
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    const [follows, total] = await queryBuilder.getManyAndCount();

    const users = await Promise.all(
      follows.map((follow) =>
        this.formatUserBrief(follow.following, currentUserId),
      ),
    );

    const totalPages = Math.ceil(total / limit);
    return {
      data: users,
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  /**
   * 获取粉丝列表（关注我的人）
   */
  async getFollowersList(
    userId: string,
    queryDto: FollowListQueryDto,
    currentUserId?: string,
  ): Promise<PaginatedResponseDto<UserBriefDto>> {
    const {
      page = 1,
      limit = 20,
      status = FollowStatus.ACTIVE,
      search,
      sortBy = "createdAt",
      sortOrder = "DESC",
    } = queryDto;

    const queryBuilder = this.followRepository
      .createQueryBuilder("follow")
      .leftJoinAndSelect("follow.follower", "user")
      .where("follow.followingId = :userId", { userId })
      .andWhere("follow.status = :status", { status });

    if (search) {
      queryBuilder.andWhere(
        "(user.nickname ILIKE :search OR user.username ILIKE :search)",
        { search: `%${search}%` },
      );
    }

    // 排序
    const validSortFields = ["createdAt", "updatedAt"];
    const sortField = validSortFields.includes(sortBy) ? sortBy : "createdAt";
    queryBuilder.orderBy(`follow.${sortField}`, sortOrder);

    // 分页
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    const [follows, total] = await queryBuilder.getManyAndCount();

    const users = await Promise.all(
      follows.map((follow) =>
        this.formatUserBrief(follow.follower, currentUserId),
      ),
    );

    const totalPages = Math.ceil(total / limit);
    return {
      data: users,
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  // ==================== 好友分组管理 ====================

  /**
   * 创建好友分组
   */
  async createFriendGroup(
    createGroupDto: CreateFriendGroupDto,
    ownerId: string,
  ): Promise<FriendGroupResponseDto> {
    const {
      name,
      description,
      groupType = GroupType.CUSTOM,
      visibility = GroupVisibility.PRIVATE,
      colorTag,
      sortOrder = 0,
      permissions,
      initialMembers = [],
    } = createGroupDto;

    // 检查同名分组
    const existingGroup = await this.groupRepository.findOne({
      where: { ownerId, name },
    });

    if (existingGroup) {
      throw new ConflictException("已存在同名的分组");
    }

    // 创建分组
    const group = this.groupRepository.create({
      name,
      description,
      ownerId,
      groupType,
      visibility,
      colorTag,
      sortOrder,
      permissions: permissions || {},
      memberCount: initialMembers.length,
    });

    const savedGroup = await this.groupRepository.save(group);

    // 添加初始成员
    if (initialMembers.length > 0) {
      await this.addMembersToGroup(savedGroup.id, initialMembers, ownerId);
    }

    return this.formatGroupResponse(savedGroup);
  }

  /**
   * 获取好友分组列表
   */
  async getFriendGroups(
    ownerId: string,
    queryDto: FriendGroupQueryDto,
  ): Promise<PaginatedResponseDto<FriendGroupResponseDto>> {
    const {
      page = 1,
      limit = 20,
      groupType,
      visibility,
      isActive = true,
      search,
      sortBy = "sortOrder",
      sortOrder = "ASC",
    } = queryDto;

    const queryBuilder = this.groupRepository
      .createQueryBuilder("group")
      .where("group.ownerId = :ownerId", { ownerId })
      .andWhere("group.isActive = :isActive", { isActive });

    // 应用筛选条件
    if (groupType) {
      queryBuilder.andWhere("group.groupType = :groupType", { groupType });
    }

    if (visibility) {
      queryBuilder.andWhere("group.visibility = :visibility", { visibility });
    }

    if (search) {
      queryBuilder.andWhere(
        "(group.name ILIKE :search OR group.description ILIKE :search)",
        { search: `%${search}%` },
      );
    }

    // 排序
    const validSortFields = ["sortOrder", "createdAt", "memberCount", "name"];
    const sortField = validSortFields.includes(sortBy) ? sortBy : "sortOrder";
    queryBuilder.orderBy(`group.${sortField}`, sortOrder);

    // 分页
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    const [groups, total] = await queryBuilder.getManyAndCount();

    const formattedGroups = groups.map((group) =>
      this.formatGroupResponse(group),
    );

    const totalPages = Math.ceil(total / limit);
    return {
      data: formattedGroups,
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  /**
   * 获取分组成员列表
   */
  async getGroupMembers(
    groupId: string,
    queryDto: GroupMemberQueryDto,
    currentUserId: string,
  ): Promise<PaginatedResponseDto<GroupMemberResponseDto>> {
    // 验证分组存在且有权限查看
    const group = await this.groupRepository.findOne({
      where: { id: groupId },
    });

    if (!group) {
      throw new NotFoundException("分组不存在");
    }

    // 检查权限（只有分组所有者或成员可以查看）
    if (group.ownerId !== currentUserId) {
      const membershipCheck = await this.groupMemberRepository.findOne({
        where: {
          groupId,
          userId: currentUserId,
          status: MemberStatus.ACTIVE,
        },
      });

      if (!membershipCheck) {
        throw new ForbiddenException("无权限查看该分组成员");
      }
    }

    const {
      page = 1,
      limit = 20,
      status = MemberStatus.ACTIVE,
      role,
      search,
      sortBy = "addedAt",
      sortOrder = "DESC",
    } = queryDto;

    const queryBuilder = this.groupMemberRepository
      .createQueryBuilder("member")
      .leftJoinAndSelect("member.user", "user")
      .leftJoinAndSelect("member.adder", "adder")
      .leftJoinAndSelect("member.group", "group")
      .where("member.groupId = :groupId", { groupId })
      .andWhere("member.status = :status", { status });

    // 应用筛选条件
    if (role) {
      queryBuilder.andWhere("member.role = :role", { role });
    }

    if (search) {
      queryBuilder.andWhere(
        "(user.nickname ILIKE :search OR user.username ILIKE :search)",
        { search: `%${search}%` },
      );
    }

    // 排序
    const validSortFields = ["addedAt", "role"];
    const sortField = validSortFields.includes(sortBy) ? sortBy : "addedAt";
    queryBuilder.orderBy(`member.${sortField}`, sortOrder);

    // 分页
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    const [members, total] = await queryBuilder.getManyAndCount();

    const formattedMembers = members.map((member) =>
      this.formatGroupMemberResponse(member),
    );

    const totalPages = Math.ceil(total / limit);
    return {
      data: formattedMembers,
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  /**
   * 向分组添加成员
   */
  async addMembersToGroup(
    groupId: string,
    userIds: string[],
    addedBy: string,
  ): Promise<number> {
    // 验证分组存在且有权限
    const group = await this.groupRepository.findOne({
      where: { id: groupId },
    });

    if (!group) {
      throw new NotFoundException("分组不存在");
    }

    if (group.ownerId !== addedBy) {
      throw new ForbiddenException("只有分组所有者可以添加成员");
    }

    // 验证用户存在
    const users = await this.userRepository.findByIds(userIds);
    if (users.length !== userIds.length) {
      throw new BadRequestException("部分用户不存在");
    }

    // 检查已存在的成员
    const existingMembers = await this.groupMemberRepository.find({
      where: { groupId, userId: { $in: userIds } as any }, // eslint-disable-line @typescript-eslint/no-explicit-any
    });

    const existingUserIds = existingMembers.map((member) => member.userId);
    const newUserIds = userIds.filter((id) => !existingUserIds.includes(id));

    if (newUserIds.length === 0) {
      return 0;
    }

    // 创建新成员记录
    const newMembers = newUserIds.map((userId) =>
      this.groupMemberRepository.create({
        groupId,
        userId,
        addedBy,
        role: MemberRole.MEMBER,
        status: MemberStatus.ACTIVE,
      }),
    );

    await this.groupMemberRepository.save(newMembers);

    // 更新分组成员数量
    await this.groupRepository.update(groupId, {
      memberCount: group.memberCount + newMembers.length,
    });

    return newMembers.length;
  }

  /**
   * 获取社交统计数据
   */
  async getSocialStats(userId: string): Promise<SocialStatsResponseDto> {
    const [
      followingCount,
      followersCount,
      mutualFollowsCount,
      specialFollowsCount,
      friendGroupsCount,
      groupMembershipsCount,
      newFollowersToday,
      newFollowingToday,
    ] = await Promise.all([
      // 关注数
      this.followRepository.count({
        where: { followerId: userId, status: FollowStatus.ACTIVE },
      }),
      // 粉丝数
      this.followRepository.count({
        where: { followingId: userId, status: FollowStatus.ACTIVE },
      }),
      // 互关数
      this.getMutualFollowsCount(userId),
      // 特别关注数
      this.followRepository.count({
        where: {
          followerId: userId,
          status: FollowStatus.ACTIVE,
          isSpecial: true,
        },
      }),
      // 创建的分组数
      this.groupRepository.count({
        where: { ownerId: userId, isActive: true },
      }),
      // 所在分组数
      this.groupMemberRepository.count({
        where: { userId, status: MemberStatus.ACTIVE },
      }),
      // 今日新增粉丝
      this.followRepository
        .createQueryBuilder("follow")
        .where("follow.followingId = :userId", { userId })
        .andWhere("DATE(follow.createdAt) = CURRENT_DATE")
        .getCount(),
      // 今日新增关注
      this.followRepository
        .createQueryBuilder("follow")
        .where("follow.followerId = :userId", { userId })
        .andWhere("DATE(follow.createdAt) = CURRENT_DATE")
        .getCount(),
    ]);

    // 获取热门分组
    const topGroups = await this.groupRepository
      .createQueryBuilder("group")
      .where("group.ownerId = :userId", { userId })
      .andWhere("group.isActive = :isActive", { isActive: true })
      .orderBy("group.memberCount", "DESC")
      .limit(5)
      .getMany();

    return {
      userId,
      followingCount,
      followersCount,
      mutualFollowsCount,
      specialFollowsCount,
      friendGroupsCount,
      groupMembershipsCount,
      newFollowersToday,
      newFollowingToday,
      topGroups: topGroups.map((group) => ({
        groupId: group.id,
        groupName: group.name,
        memberCount: group.memberCount,
        groupType: group.groupType,
      })),
      topInteractors: [], // 这里需要根据实际业务逻辑实现
    };
  }

  // ==================== 私有辅助方法 ====================

  /**
   * 获取互关数量
   */
  private async getMutualFollowsCount(userId: string): Promise<number> {
    const result = await this.followRepository
      .createQueryBuilder("f1")
      .innerJoin(
        "user_follows",
        "f2",
        "f1.followingId = f2.followerId AND f1.followerId = f2.followingId",
      )
      .where("f1.followerId = :userId", { userId })
      .andWhere("f1.status = :status", { status: FollowStatus.ACTIVE })
      .andWhere("f2.status = :status", { status: FollowStatus.ACTIVE })
      .getCount();

    return result;
  }

  /**
   * 格式化关注响应
   */
  private async formatFollowResponse(
    follow: UserFollow,
  ): Promise<FollowResponseDto> {
    const [follower, following] = await Promise.all([
      this.userRepository.findOne({ where: { id: follow.followerId } }),
      this.userRepository.findOne({ where: { id: follow.followingId } }),
    ]);

    return {
      id: follow.id,
      followerId: follow.followerId,
      followingId: follow.followingId,
      followerName: follower?.nickname || "未知用户",
      followerAvatar: (follower as any)?.avatar, // eslint-disable-line @typescript-eslint/no-explicit-any
      followingName: following?.nickname || "未知用户",
      followingAvatar: (following as any)?.avatar, // eslint-disable-line @typescript-eslint/no-explicit-any
      status: follow.status,
      isSpecial: follow.isSpecial,
      settings: follow.settings as any, // eslint-disable-line @typescript-eslint/no-explicit-any
      metadata: follow.metadata,
      createdAt: follow.createdAt,
    };
  }

  /**
   * 格式化用户简要信息
   */
  private async formatUserBrief(
    user: User,
    currentUserId?: string,
  ): Promise<UserBriefDto> {
    const [followersCount, followingCount, storiesCount] = await Promise.all([
      this.followRepository.count({
        where: { followingId: user.id, status: FollowStatus.ACTIVE },
      }),
      this.followRepository.count({
        where: { followerId: user.id, status: FollowStatus.ACTIVE },
      }),
      // 这里需要根据实际Story实体实现
      Promise.resolve(0),
    ]);

    let isFollowing = false;
    let isFollowingMe = false;
    let followedAt: Date | undefined;

    if (currentUserId && currentUserId !== user.id) {
      const [followingRel, followerRel] = await Promise.all([
        this.followRepository.findOne({
          where: {
            followerId: currentUserId,
            followingId: user.id,
            status: FollowStatus.ACTIVE,
          },
        }),
        this.followRepository.findOne({
          where: {
            followerId: user.id,
            followingId: currentUserId,
            status: FollowStatus.ACTIVE,
          },
        }),
      ]);

      isFollowing = !!followingRel;
      isFollowingMe = !!followerRel;
      followedAt = followingRel?.createdAt;
    }

    return {
      id: user.id,
      username: user.username,
      name: user.nickname || user.username,
      avatar: (user as any)?.avatar, // eslint-disable-line @typescript-eslint/no-explicit-any
      bio: (user as any)?.bio, // eslint-disable-line @typescript-eslint/no-explicit-any
      followersCount,
      followingCount,
      storiesCount,
      isFollowing,
      isFollowingMe,
      isMutualFollow: isFollowing && isFollowingMe,
      followedAt,
    };
  }

  /**
   * 格式化分组响应
   */
  private formatGroupResponse(group: FriendGroup): FriendGroupResponseDto {
    return {
      id: group.id,
      name: group.name,
      description: group.description,
      ownerId: group.ownerId,
      ownerName: group.owner?.nickname || "未知用户",
      groupType: group.groupType,
      visibility: group.visibility,
      colorTag: group.colorTag,
      memberCount: group.memberCount,
      sortOrder: group.sortOrder,
      isActive: group.isActive,
      permissions: group.permissions as any, // eslint-disable-line @typescript-eslint/no-explicit-any
      metadata: group.metadata,
      createdAt: group.createdAt,
      updatedAt: group.updatedAt,
    };
  }

  /**
   * 格式化分组成员响应
   */
  private formatGroupMemberResponse(
    member: FriendGroupMember,
  ): GroupMemberResponseDto {
    return {
      id: member.id,
      groupId: member.groupId,
      groupName: member.group?.name || "未知分组",
      userId: member.userId,
      userName: member.user?.nickname || "未知用户",
      userAvatar: (member.user as any)?.avatar, // eslint-disable-line @typescript-eslint/no-explicit-any
      role: member.role,
      status: member.status,
      addedBy: member.addedBy,
      addedByName: member.adder?.nickname || "未知用户",
      note: member.note,
      permissionOverrides: member.permissionOverrides as any, // eslint-disable-line @typescript-eslint/no-explicit-any
      metadata: member.metadata,
      addedAt: member.addedAt,
    };
  }
}
