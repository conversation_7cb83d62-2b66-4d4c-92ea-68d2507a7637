/**
 * 书签模块 - 企业级单元测试
 */

import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { BookmarksModule } from "./bookmarks.module";
import { BookmarksService } from "./bookmarks.service";
import { BookmarksController } from "./bookmarks.controller";

describe("BookmarksModule - 企业级模块测试", () => {
  let module: TestingModule;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [BookmarksModule],
    })
      .overrideProvider(BookmarksService)
      .useValue({
        findAll: jest.fn(),
        findOne: jest.fn(),
        create: jest.fn(),
        update: jest.fn(),
        remove: jest.fn(),
      })
      .compile();
  });

  afterEach(async () => {
    if (module) {
      await module.close();
    }
  });

  describe("模块初始化", () => {
    it("should compile the module", () => {
      expect(module).toBeDefined();
    });

    it("should provide BookmarksService", () => {
      const service = module.get<BookmarksService>(BookmarksService);
      expect(service).toBeDefined();
    });

    it("should provide BookmarksController", () => {
      const controller = module.get<BookmarksController>(BookmarksController);
      expect(controller).toBeDefined();
    });
  });

  describe("依赖注入", () => {
    it("should inject BookmarksService into BookmarksController", () => {
      const controller = module.get<BookmarksController>(BookmarksController);
      const service = module.get<BookmarksService>(BookmarksService);

      expect(controller).toBeDefined();
      expect(service).toBeDefined();
    });
  });

  describe("模块配置", () => {
    it("should be a valid NestJS module", () => {
      expect(BookmarksModule).toBeDefined();
      expect(typeof BookmarksModule).toBe("function");
    });
  });
});
