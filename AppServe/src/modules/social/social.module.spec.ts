/**
 * 社交模块 - 企业级单元测试
 */

import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { SocialModule } from "./social.module";
import { SocialService } from "./social.service";
import { SocialController } from "./social.controller";

describe("SocialModule - 企业级模块测试", () => {
  let module: TestingModule;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [SocialModule],
    })
      .overrideProvider(SocialService)
      .useValue({
        findAll: jest.fn(),
        findOne: jest.fn(),
        create: jest.fn(),
        update: jest.fn(),
        remove: jest.fn(),
      })
      .compile();
  });

  afterEach(async () => {
    if (module) {
      await module.close();
    }
  });

  describe("模块初始化", () => {
    it("should compile the module", () => {
      expect(module).toBeDefined();
    });

    it("should provide SocialService", () => {
      const service = module.get<SocialService>(SocialService);
      expect(service).toBeDefined();
    });

    it("should provide SocialController", () => {
      const controller = module.get<SocialController>(SocialController);
      expect(controller).toBeDefined();
    });
  });

  describe("依赖注入", () => {
    it("should inject SocialService into SocialController", () => {
      const controller = module.get<SocialController>(SocialController);
      const service = module.get<SocialService>(SocialService);

      expect(controller).toBeDefined();
      expect(service).toBeDefined();
    });
  });

  describe("模块配置", () => {
    it("should be a valid NestJS module", () => {
      expect(SocialModule).toBeDefined();
      expect(typeof SocialModule).toBe("function");
    });
  });
});
