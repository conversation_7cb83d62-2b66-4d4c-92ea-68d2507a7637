# YGS 后端开发环境配置指南

> **更新日期**: 2025-07-27  
> **适用版本**: v1.0.0  
> **配置模式**: 混合模式（本地服务 + 云端资源）

## 📋 概述

本文档说明如何配置 YGS 后端开发环境，采用混合模式实现高效开发和真实测试的平衡。

### 🎯 混合模式优势

- ✅ **高开发效率**: 本地启动快速，无网络延迟
- ✅ **真实功能测试**: 使用真实的 OSS/CDN 服务
- ✅ **数据完全隔离**: 开发库与生产库物理隔离
- ✅ **成本最优化**: 复用现有云资源，无需额外投入
- ✅ **部署流程简单**: 配置切换即可部署到云端

## 🏗️ 环境架构

```
┌─────────────────────────────────────────────────────────────┐
│                        开发环境架构图                         │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│   本地环境                          阿里云环境               │
│  ┌─────────────┐                 ┌─────────────────────┐  │
│  │   NestJS    │                 │   RDS PostgreSQL    │  │
│  │  后端服务    │ ───────────────▶│    (ygs-dev)       │  │
│  └─────────────┘                 └─────────────────────┘  │
│         │                                                   │
│         │                        ┌─────────────────────┐  │
│         ├───────────────────────▶│    OSS 存储服务     │  │
│         │                        │   (ygs-oss-dev)     │  │
│         │                        └─────────────────────┘  │
│         │                                                   │
│  ┌─────────────┐                ┌─────────────────────┐  │
│  │  Redis 缓存  │                │    CDN 加速服务     │  │
│  │  (Docker)   │                │  (dev.example.com)  │  │
│  └─────────────┘                └─────────────────────┘  │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

## 📦 环境要求

### 本地环境
- Node.js >= 22.16.0
- npm >= 11.0.0
- Docker Desktop (用于运行 Redis)
- Git

### 云端资源（已配置）
- 阿里云 RDS PostgreSQL 实例
- 阿里云 OSS 存储桶（开发版本）
- 阿里云 CDN 服务
- 阿里云短信服务（可选）

## 🚀 快速开始

### 1. 克隆项目

```bash
git clone [项目地址]
cd ygs-v1.0.0/AppServe
```

### 2. 安装依赖

```bash
npm install
```

### 3. 配置环境变量

```bash
# 切换到开发环境配置
npm run env:dev
```

这将自动使用 `.env.development` 配置文件，其中包含：
- 云端 RDS 数据库连接信息
- 本地 Redis 连接配置
- OSS 开发环境配置
- 其他必要的环境变量

### 4. 启动本地 Redis

```bash
# 使用 Docker 启动 Redis
npm run docker:redis
```

### 5. 运行数据库迁移

```bash
# 初始化数据库表结构
npm run migration:run
```

### 6. 生成测试数据

```bash
# 生成企业级测试数据
npm run seed:enhanced
```

### 7. 启动开发服务器

```bash
# 启动开发模式（支持热重载）
npm run start:dev
```

服务将在 http://localhost:3000 启动

## 📊 测试账号

种子数据生成后，可使用以下测试账号：

| 角色 | 手机号 | 密码 | 说明 |
|------|--------|------|------|
| 创作者 | 13800138001 | Test123456! | 张明远，活跃内容创作者 |
| 点亮者 | 13800138002 | Test123456! | 李小红，活跃点亮用户 |
| 普通用户 | 13800138004 | Test123456! | 赵小芳，普通读者 |
| VIP用户 | 16675158665 | Test123456! | 陈总，体验高级功能 |
| 新用户 | 13800138005 | Test123456! | 刘小新，新注册用户 |

## 🔧 常用命令

### 环境管理
```bash
npm run env:dev          # 切换到开发环境
npm run env:prod         # 切换到生产环境
npm run env:check        # 查看当前环境配置
```

### 数据库管理
```bash
npm run seed:enhanced           # 生成完整测试数据
npm run seed:enhanced:clean     # 清理所有数据
npm run seed:enhanced:stats     # 查看数据统计
npm run seed:enhanced:validate  # 验证数据完整性
```

### 开发调试
```bash
npm run start:dev        # 开发模式运行
npm run start:debug      # 调试模式运行
npm run test             # 运行测试
npm run lint             # 代码检查
```

### Docker 管理
```bash
npm run docker:redis     # 启动 Redis 容器
npm run docker:stop      # 停止所有容器
npm run docker:clean     # 清理容器和数据
```

## 🛡️ 安全注意事项

1. **环境隔离**: 开发环境使用独立的 `ygs-dev` 数据库，与生产完全隔离
2. **密钥管理**: 所有敏感信息都通过环境变量管理，不要提交到代码仓库
3. **访问控制**: RDS 已配置 IP 白名单，仅允许授权 IP 访问
4. **数据保护**: 测试数据仅用于开发，不包含真实用户信息

## 🐛 故障排除

### Redis 连接失败
```bash
# 检查 Docker 是否运行
docker ps

# 重启 Redis
npm run docker:stop
npm run docker:redis
```

### RDS 连接失败
1. 检查网络连接
2. 确认 IP 已加入 RDS 白名单
3. 验证环境变量配置正确

### OSS 上传失败
1. 检查 OSS 配置信息
2. 确认 AccessKey 权限
3. 验证 Bucket 名称和区域

## 📝 开发规范

1. **代码质量**: 提交前必须通过 ESLint 和 TypeScript 检查
2. **测试覆盖**: 新功能必须包含单元测试，覆盖率 ≥80%
3. **文档更新**: API 变更需同步更新接口文档
4. **提交规范**: 使用中文描述提交信息，遵循约定格式

## 🔄 环境切换

### 切换到生产环境
```bash
npm run env:prod
npm run build
npm run start:prod
```

### 切换回开发环境
```bash
npm run env:dev
npm run start:dev
```

## 📚 相关文档

- [API 接口文档](../../对接文档/API接口文档.md)
- [数据库设计文档](./DATABASE_DESIGN.md)
- [测试指南](./COMPREHENSIVE_TESTING_GUIDE.md)
- [部署指南](./DEPLOYMENT_GUIDE.md)

## 🤝 获取帮助

如遇到问题，请查阅：
1. 本文档的故障排除部分
2. 项目 Issue 列表
3. 联系技术支持团队

---

**最后更新**: 2025-07-27  
**维护者**: YGS 技术团队