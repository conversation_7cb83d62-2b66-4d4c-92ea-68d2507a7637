import 'package:freezed_annotation/freezed_annotation.dart';

part 'auth_token.freezed.dart';

/// 认证令牌实体
@freezed
class AuthToken with _$AuthToken {
  const factory AuthToken({
    required String accessToken,
    required String refreshToken,
    required DateTime expiresAt,
    required String tokenType,
  }) = _AuthToken;
}

/// 认证令牌扩展方法
extension AuthTokenX on AuthToken {
  /// 是否已过期
  bool get isExpired => DateTime.now().isAfter(expiresAt);
  
  /// 是否需要刷新（提前5分钟刷新）
  bool get needsRefresh => DateTime.now().isAfter(
    expiresAt.subtract(const Duration(minutes: 5)),
  );
  
  /// 获取完整的认证头
  String get authorizationHeader => '$tokenType $accessToken';
}