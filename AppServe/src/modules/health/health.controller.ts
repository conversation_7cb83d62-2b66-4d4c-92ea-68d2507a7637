import { Controller, Get, Logger } from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse } from "@nestjs/swagger";
import type { HealthCheckResult } from "@nestjs/terminus";
import { HealthService } from "./health.service";

@ApiTags("健康检查")
@Controller("health")
export class HealthController {
  private readonly logger = new Logger(HealthController.name);

  constructor(private readonly healthService: HealthService) {}

  @Get()
  @ApiOperation({
    summary: "基础健康检查",
    description: "检查关键服务状态：数据库、Redis、内存、磁盘",
  })
  @ApiResponse({
    status: 200,
    description: "服务健康",
  })
  @ApiResponse({
    status: 503,
    description: "服务不健康",
  })
  async getHealth(): Promise<HealthCheckResult> {
    try {
      return await this.healthService.getHealth();
    } catch (error) {
      this.logger.error(
        "健康检查失败",
        error instanceof Error ? error.stack : undefined,
      );
      throw error;
    }
  }

  @Get("detailed")
  @ApiOperation({
    summary: "详细健康检查",
    description: "包含性能指标和应用程序状态的详细健康检查",
  })
  @ApiResponse({
    status: 200,
    description: "详细健康信息",
  })
  @ApiResponse({
    status: 503,
    description: "服务不健康",
  })
  async getDetailedHealth(): Promise<HealthCheckResult> {
    try {
      return await this.healthService.getDetailedHealth();
    } catch (error) {
      this.logger.error(
        "详细健康检查失败",
        error instanceof Error ? error.stack : undefined,
      );
      throw error;
    }
  }
}
