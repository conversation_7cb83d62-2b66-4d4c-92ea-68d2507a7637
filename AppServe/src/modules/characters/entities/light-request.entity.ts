import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  Index,
  JoinColumn,
} from "typeorm";
import { Story } from "../../stories/entities/story.entity";
import { Character } from "./character.entity";
import { User } from "../../users/entities/user.entity";

/**
 * 点亮申请实体
 * YGS核心功能 - 人物点亮系统的申请记录
 */
@Entity("light_requests")
@Index(["storyId", "status"]) // 故事申请状态查询
@Index(["requesterId", "status"]) // 申请者状态查询
@Index(["storyAuthorId", "status"], { where: "status = 'pending'" }) // 待处理申请查询
@Index(["storyId", "characterId", "requesterId"], { unique: true }) // 防止同一故事中重复申请
@Index(["characterId", "requesterId"], { unique: true }) // 🔥 企业级约束：防止同一人物实体的重复申请
@Index(["requesterId", "createdAt"]) // 申请频率限制查询优化
@Index(["characterId"]) // 人物申请历史查询
export class LightRequest {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ name: "story_id", comment: "故事ID" })
  storyId: string;

  @Column({ name: "character_id", comment: "人物ID" })
  characterId: string;

  @Column({ name: "requester_id", comment: "申请者ID" })
  requesterId: string;

  @Column({ name: "story_author_id", comment: "故事作者ID" })
  storyAuthorId: string;

  @Column({
    name: "phone_verification",
    type: "varchar",
    length: 20,
    nullable: true,
    comment: "脱敏手机号（用户选择发送时才有值）",
  })
  phoneVerification: string | null;

  @Column({
    name: "has_phone_verification",
    default: false,
    comment: "用户是否选择发送手机号",
  })
  hasPhoneVerification: boolean;

  @Column({ type: "text", nullable: true, comment: "申请留言" })
  message: string;

  @Column({
    length: 20,
    default: "active",
    comment: "状态：active/cancelled/pending/approved/rejected/expired",
  })
  status:
    | "active"
    | "cancelled"
    | "pending"
    | "approved"
    | "rejected"
    | "expired";

  @Column({ name: "expires_at", type: "timestamp", comment: "申请过期时间" })
  expiresAt: Date;

  @CreateDateColumn({ name: "created_at", comment: "创建时间" })
  createdAt: Date;

  @Column({
    name: "processed_at",
    type: "timestamp",
    nullable: true,
    comment: "处理时间",
  })
  processedAt: Date;

  // 关联关系
  @ManyToOne(() => Story, { onDelete: "CASCADE" })
  @JoinColumn({ name: "story_id" })
  story: Story;

  @ManyToOne(() => Character, { onDelete: "CASCADE" })
  @JoinColumn({ name: "character_id" })
  character: Character;

  @ManyToOne(() => User, { onDelete: "CASCADE" })
  @JoinColumn({ name: "requester_id" })
  requester: User;

  @ManyToOne(() => User, { onDelete: "CASCADE" })
  @JoinColumn({ name: "story_author_id" })
  storyAuthor: User;

  // 业务方法
  isExpired(): boolean {
    return this.expiresAt < new Date();
  }

  isPending(): boolean {
    return this.status === "pending" && !this.isExpired();
  }

  canBeProcessed(): boolean {
    return this.status === "pending" && !this.isExpired();
  }

  /**
   * 计算剩余时间
   */
  getTimeRemaining(): number {
    const now = new Date().getTime();
    const expiry = this.expiresAt.getTime();
    return Math.max(0, expiry - now);
  }

  /**
   * 获取状态描述
   */
  getStatusDescription(): string {
    const statusMap = {
      active: "活跃",
      cancelled: "已取消",
      pending: "待处理",
      approved: "已批准",
      rejected: "已拒绝",
      expired: "已过期",
    } as const;
    return statusMap[this.status] || "未知状态";
  }
}
