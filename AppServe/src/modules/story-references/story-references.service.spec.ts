/* eslint-disable @typescript-eslint/no-explicit-any, @typescript-eslint/no-unused-vars */
import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import type { Repository } from "typeorm";
import { getRepositoryToken } from "@nestjs/typeorm";
import {
  BadRequestException,
  NotFoundException,
  ForbiddenException,
} from "@nestjs/common";

import { StoryReferencesService } from "./story-references.service";
import {
  StoryReference,
  ReferenceStatus,
  ReferenceVisibility,
} from "./entities/story-reference.entity";
import {
  ReferenceCollection,
  CollectionType,
  CollectionStatus,
  CollectionVisibility,
} from "./entities/reference-collection.entity";
import { User } from "../users/entities/user.entity";
import { Story } from "../stories/entities/story.entity";
import type { CreateReferenceDto } from "./dto/create-reference.dto";
import type { CreateCollectionDto } from "./dto/create-collection.dto";
import type {
  ReferenceQueryDto,
  CollectionQueryDto,
} from "./dto/reference-query.dto";

describe("StoryReferencesService - 企业级单元测试", () => {
  let service: StoryReferencesService;
  let mockReferenceRepository: jest.Mocked<Repository<StoryReference>>;
  let mockCollectionRepository: jest.Mocked<Repository<ReferenceCollection>>;
  let mockUserRepository: jest.Mocked<Repository<User>>;
  let mockStoryRepository: jest.Mocked<Repository<Story>>;

  // Mock数据 - 使用具体类型
  const mockUser = {
    id: "test-user-id",
    username: "testuser",
    nickname: "测试用户",
    email: "<EMAIL>",
  };

  const mockStory = {
    id: "test-story-id",
    title: "测试故事",
    authorId: "author-id",
    authorName: "测试作者",
    summary: "测试故事摘要",
    publishedAt: new Date("2025-01-01"),
    chapterCount: 10,
  };

  const mockReference = {
    id: "test-reference-id",
    userId: "test-user-id",
    storyId: "test-story-id",
    collectionId: "test-collection-id",
    title: "测试引用",
    annotation: "测试评注",
    quotedSegments: [
      {
        chapterId: "chapter-1",
        startPosition: 0,
        endPosition: 100,
        text: "测试引用文本",
        note: "测试片段备注",
      },
    ],
    status: ReferenceStatus.ACTIVE,
    visibility: ReferenceVisibility.PRIVATE,
    tags: ["测试", "引用"],
    importance: 3,
    likesCount: 5,
    commentsCount: 2,
    storySnapshot: {
      title: "测试故事",
      summary: "测试故事摘要",
      authorId: "author-id",
      authorName: "测试作者",
      publishedAt: new Date("2025-01-01"),
      chapterCount: 10,
    },
    lastReadAt: new Date(),
    readCount: 10,
    metadata: {},
    createdAt: new Date(),
    updatedAt: new Date(),
    user: mockUser,
    story: mockStory,
    collection: null,
  };

  const mockCollection = {
    id: "test-collection-id",
    name: "测试集合",
    description: "测试集合描述",
    ownerId: "test-user-id",
    collectionType: CollectionType.GENERAL,
    status: CollectionStatus.ACTIVE,
    visibility: CollectionVisibility.PRIVATE,
    coverImage: "https://example.com/cover.jpg",
    colorTheme: "blue",
    tags: ["测试", "集合"],
    referenceCount: 5,
    followersCount: 10,
    likesCount: 15,
    sortOrder: 0,
    isFeatured: false,
    allowCollaboration: true,
    settings: {
      autoAddSimilar: false,
      notifyOnNewReference: true,
      allowPublicContribution: false,
      moderationRequired: false,
    },
    lastContentUpdate: new Date(),
    statistics: {
      totalViews: 100,
      monthlyViews: 20,
      totalShares: 5,
      averageRating: 4.5,
      topTags: ["测试", "集合"],
    },
    metadata: {},
    createdAt: new Date(),
    updatedAt: new Date(),
    owner: mockUser,
    references: [],
  };

  beforeEach(async () => {
    // 创建Repository Mock对象
    const mockReferenceRepositoryMethods = {
      create: jest.fn(),
      save: jest.fn(),
      find: jest.fn(),
      findOne: jest.fn(),
      findAndCount: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      increment: jest.fn().mockResolvedValue(undefined),
      createQueryBuilder: jest.fn().mockReturnValue({
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[], 0]),
        getOne: jest.fn().mockResolvedValue(null),
        getMany: jest.fn().mockResolvedValue([]),
        getRawMany: jest.fn().mockResolvedValue([]),
      }),
      count: jest.fn(),
    };

    const mockCollectionRepositoryMethods = {
      create: jest.fn(),
      save: jest.fn(),
      find: jest.fn(),
      findOne: jest.fn(),
      findAndCount: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      createQueryBuilder: jest.fn().mockReturnValue({
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[], 0]),
        getOne: jest.fn().mockResolvedValue(null),
        getRawMany: jest.fn().mockResolvedValue([]),
      }),
      count: jest.fn(),
    };

    const mockUserRepositoryMethods = {
      findOne: jest.fn(),
      find: jest.fn(),
    };

    const mockStoryRepositoryMethods = {
      findOne: jest.fn().mockResolvedValue(mockStory),
      find: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        StoryReferencesService,
        {
          provide: getRepositoryToken(StoryReference),
          useValue: mockReferenceRepositoryMethods,
        },
        {
          provide: getRepositoryToken(ReferenceCollection),
          useValue: mockCollectionRepositoryMethods,
        },
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepositoryMethods,
        },
        {
          provide: getRepositoryToken(Story),
          useValue: mockStoryRepositoryMethods,
        },
      ],
    }).compile();

    service = module.get<StoryReferencesService>(StoryReferencesService);
    mockReferenceRepository = module.get(getRepositoryToken(StoryReference));
    mockCollectionRepository = module.get(
      getRepositoryToken(ReferenceCollection),
    );
    mockUserRepository = module.get(getRepositoryToken(User));
    mockStoryRepository = module.get(getRepositoryToken(Story));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("createReference - 创建故事引用", () => {
    const createReferenceDto: CreateReferenceDto = {
      storyId: "test-story-id",
      collectionId: "test-collection-id",
      title: "测试引用",
      annotation: "测试评注",
      quotedSegments: [
        {
          chapterId: "chapter-1",
          startPosition: 0,
          endPosition: 100,
          text: "测试引用文本",
          note: "测试片段备注",
        },
      ],
      visibility: ReferenceVisibility.PRIVATE,
      tags: ["测试", "引用"],
      importance: 3,
      metadata: {},
    };

    it("应该成功创建故事引用", async () => {
      // Arrange
      const userId = "test-user-id";
      mockCollectionRepository.findOne.mockResolvedValue(mockCollection as any);
      mockReferenceRepository.create.mockReturnValue(mockReference as any);
      mockReferenceRepository.save.mockResolvedValue(mockReference as any);

      // Act
      const result = await service.createReference(createReferenceDto, userId);

      // Assert
      expect(result).toEqual(mockReference);
      expect(mockCollectionRepository.findOne).toHaveBeenCalledWith({
        where: {
          id: createReferenceDto.collectionId,
          ownerId: userId,
          status: CollectionStatus.ACTIVE,
        },
      });
      expect(mockReferenceRepository.create).toHaveBeenCalled();
      expect(mockReferenceRepository.save).toHaveBeenCalledWith(mockReference);
    });

    it("应该在集合不存在时抛出NotFoundException", async () => {
      // Arrange
      const userId = "test-user-id";
      mockCollectionRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.createReference(createReferenceDto, userId),
      ).rejects.toThrow(NotFoundException);
      expect(mockCollectionRepository.findOne).toHaveBeenCalled();
      expect(mockReferenceRepository.create).not.toHaveBeenCalled();
    });

    it("应该在集合不属于用户时抛出ForbiddenException", async () => {
      // Arrange
      const userId = "other-user-id";
      mockCollectionRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.createReference(createReferenceDto, userId),
      ).rejects.toThrow(NotFoundException);
    });

    it("应该在没有集合ID时也能创建引用", async () => {
      // Arrange
      const userId = "test-user-id";
      const dtoWithoutCollection = {
        ...createReferenceDto,
        collectionId: undefined,
      };
      mockReferenceRepository.create.mockReturnValue(mockReference as any);
      mockReferenceRepository.save.mockResolvedValue(mockReference as any);

      // Act
      const result = await service.createReference(
        dtoWithoutCollection,
        userId,
      );

      // Assert
      expect(result).toEqual(mockReference);
      expect(mockCollectionRepository.findOne).not.toHaveBeenCalled();
      expect(mockReferenceRepository.create).toHaveBeenCalled();
    });

    it("应该正确处理引用片段数据", async () => {
      // Arrange
      const userId = "test-user-id";
      const quotedSegments = [
        {
          chapterId: "chapter-1",
          startPosition: 0,
          endPosition: 50,
          text: "第一个片段",
          note: "第一个备注",
        },
        {
          chapterId: "chapter-2",
          startPosition: 100,
          endPosition: 200,
          text: "第二个片段",
          note: "第二个备注",
        },
      ];
      const dtoWithSegments = { ...createReferenceDto, quotedSegments };

      mockReferenceRepository.create.mockReturnValue({
        ...mockReference,
        quotedSegments,
      } as any);
      mockReferenceRepository.save.mockResolvedValue({
        ...mockReference,
        quotedSegments,
      } as any);

      // Act
      const result = await service.createReference(dtoWithSegments, userId);

      // Assert
      expect(result.quotedSegments).toHaveLength(2);
      expect(result.quotedSegments![0].text).toBe("第一个片段");
      expect(result.quotedSegments![1].text).toBe("第二个片段");
    });

    it("应该正确设置默认值", async () => {
      // Arrange
      const userId = "test-user-id";
      const minimalDto = {
        storyId: "test-story-id",
        title: "最小测试引用",
      };

      const expectedReference = {
        ...mockReference,
        visibility: ReferenceVisibility.PRIVATE,
        importance: 3,
      };

      mockReferenceRepository.create.mockReturnValue(expectedReference as any);
      mockReferenceRepository.save.mockResolvedValue(expectedReference as any);

      // Act
      const result = await service.createReference(
        minimalDto as CreateReferenceDto,
        userId,
      );

      // Assert
      expect(result.visibility).toBe(ReferenceVisibility.PRIVATE);
      expect(result.importance).toBe(3);
    });
  });

  describe("getUserReferences - 获取用户引用列表", () => {
    const mockReferences = [mockReference];
    const mockTotal = 1;

    it("应该成功获取用户引用列表", async () => {
      // Arrange
      const userId = "test-user-id";
      const query: ReferenceQueryDto = {
        page: 1,
        limit: 10,
        status: ReferenceStatus.ACTIVE,
      };

      const mockQueryBuilder = mockReferenceRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        mockReferences as any,
        mockTotal,
      ]);

      // Act
      const result = await service.getUserReferences(userId, query);

      // Assert
      expect(result.data).toEqual(mockReferences);
      expect(result.total).toBe(mockTotal);
      expect(result.page).toBe(1);
      expect(result.limit).toBe(10);
      expect(result.totalPages).toBe(1);
    });

    it("应该根据集合ID筛选引用", async () => {
      // Arrange
      const userId = "test-user-id";
      const query: ReferenceQueryDto = {
        page: 1,
        limit: 10,
        collectionId: "test-collection-id",
      };

      const mockQueryBuilder = mockReferenceRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        mockReferences as any,
        mockTotal,
      ]);

      // Act
      await service.getUserReferences(userId, query);

      // Assert
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "reference.collectionId = :collectionId",
        { collectionId: "test-collection-id" },
      );
    });

    it("应该根据标签筛选引用", async () => {
      // Arrange
      const userId = "test-user-id";
      const query: ReferenceQueryDto = {
        page: 1,
        limit: 10,
        tags: ["测试", "引用"],
      };

      const mockQueryBuilder = mockReferenceRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        mockReferences as any,
        mockTotal,
      ]);

      // Act
      await service.getUserReferences(userId, query);

      // Assert
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "reference.tags && :tags",
        { tags: ["测试", "引用"] },
      );
    });

    it("应该根据重要性评分筛选引用", async () => {
      // Arrange
      const userId = "test-user-id";
      const query: ReferenceQueryDto = {
        page: 1,
        limit: 10,
        minImportance: 4,
      };

      const mockQueryBuilder = mockReferenceRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        mockReferences as any,
        mockTotal,
      ]);

      // Act
      await service.getUserReferences(userId, query);

      // Assert
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "reference.importance >= :minImportance",
        { minImportance: 4 },
      );
    });

    it("应该根据搜索关键词筛选引用", async () => {
      // Arrange
      const userId = "test-user-id";
      const query: ReferenceQueryDto = {
        page: 1,
        limit: 10,
        search: "测试关键词",
      };

      const mockQueryBuilder = mockReferenceRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        mockReferences as any,
        mockTotal,
      ]);

      // Act
      await service.getUserReferences(userId, query);

      // Assert
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "(reference.title ILIKE :search OR reference.annotation ILIKE :search)",
        { search: "%测试关键词%" },
      );
    });

    it("应该正确处理排序", async () => {
      // Arrange
      const userId = "test-user-id";
      const query: ReferenceQueryDto = {
        page: 1,
        limit: 10,
        sortBy: "importance",
        sortOrder: "ASC",
      };

      const mockQueryBuilder = mockReferenceRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        mockReferences as any,
        mockTotal,
      ]);

      // Act
      await service.getUserReferences(userId, query);

      // Assert
      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith(
        "reference.importance",
        "ASC",
      );
    });

    it("应该正确处理分页", async () => {
      // Arrange
      const userId = "test-user-id";
      const query: ReferenceQueryDto = {
        page: 2,
        limit: 5,
      };

      const mockQueryBuilder = mockReferenceRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        mockReferences as any,
        mockTotal,
      ]);

      // Act
      await service.getUserReferences(userId, query);

      // Assert
      expect(mockQueryBuilder.skip).toHaveBeenCalledWith(5);
      expect(mockQueryBuilder.take).toHaveBeenCalledWith(5);
    });
  });

  describe("getReferenceById - 根据ID获取引用详情", () => {
    it("应该成功获取引用详情", async () => {
      // Arrange
      const referenceId = "test-reference-id";
      const userId = "test-user-id";

      const mockQueryBuilder = mockReferenceRepository.createQueryBuilder();
      (mockQueryBuilder.getOne as jest.Mock).mockResolvedValue(
        mockReference as any,
      );

      // Act
      const result = await service.getReferenceById(referenceId, userId);

      // Assert
      expect(result).toEqual(mockReference);
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith(
        "reference.user",
        "user",
      );
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith(
        "reference.story",
        "story",
      );
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith(
        "reference.collection",
        "collection",
      );
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        "reference.id = :id",
        { id: referenceId },
      );
    });

    it("应该在引用不存在时抛出NotFoundException", async () => {
      // Arrange
      const referenceId = "non-existent-id";
      const userId = "test-user-id";

      const mockQueryBuilder = mockReferenceRepository.createQueryBuilder();
      (mockQueryBuilder.getOne as jest.Mock).mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.getReferenceById(referenceId, userId),
      ).rejects.toThrow(NotFoundException);
    });

    it("应该正确处理可见性权限", async () => {
      // Arrange
      const referenceId = "test-reference-id";
      const userId = "other-user-id";
      const privateReference = {
        ...mockReference,
        userId: "test-user-id",
        visibility: ReferenceVisibility.PRIVATE,
      };

      const mockQueryBuilder = mockReferenceRepository.createQueryBuilder();
      (mockQueryBuilder.getOne as jest.Mock).mockResolvedValue(
        privateReference as any,
      );

      // Act
      const result = await service.getReferenceById(referenceId, userId);

      // Assert
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "(reference.visibility = :publicVisibility OR reference.userId = :currentUserId)",
        {
          publicVisibility: ReferenceVisibility.PUBLIC,
          currentUserId: userId,
        },
      );
    });

    it("应该增加阅读计数", async () => {
      // Arrange
      const referenceId = "test-reference-id";
      const userId = "test-user-id";

      const mockQueryBuilder = mockReferenceRepository.createQueryBuilder();
      (mockQueryBuilder.getOne as jest.Mock).mockResolvedValue(
        mockReference as any,
      );

      // Act
      await service.getReferenceById(referenceId, userId);

      // Assert
      expect(mockReferenceRepository.update).toHaveBeenCalledWith(referenceId, {
        readCount: expect.any(Number),
        lastReadAt: expect.any(Date),
      });
    });
  });

  describe("createCollection - 创建引用集合", () => {
    const createCollectionDto: CreateCollectionDto = {
      name: "测试集合",
      description: "测试集合描述",
      collectionType: CollectionType.GENERAL,
      visibility: CollectionVisibility.PRIVATE,
      coverImage: "https://example.com/cover.jpg",
      colorTheme: "blue",
      tags: ["测试", "集合"],
      sortOrder: 0,
      allowCollaboration: true,
      settings: {
        autoAddSimilar: false,
        notifyOnNewReference: true,
        allowPublicContribution: false,
        moderationRequired: false,
      },
      metadata: {},
    };

    it("应该成功创建引用集合", async () => {
      // Arrange
      const userId = "test-user-id";
      mockCollectionRepository.create.mockReturnValue(mockCollection as any);
      mockCollectionRepository.save.mockResolvedValue(mockCollection as any);

      // Act
      const result = await service.createCollection(
        createCollectionDto,
        userId,
      );

      // Assert
      expect(result).toEqual(mockCollection);
      expect(mockCollectionRepository.create).toHaveBeenCalledWith({
        ...createCollectionDto,
        ownerId: userId,
        status: CollectionStatus.ACTIVE,
        referenceCount: 0,
        followersCount: 0,
        likesCount: 0,
        isFeatured: false,
        lastContentUpdate: expect.any(Date),
        statistics: {
          totalViews: 0,
          monthlyViews: 0,
          totalShares: 0,
          averageRating: 0,
          topTags: [],
        },
      });
      expect(mockCollectionRepository.save).toHaveBeenCalledWith(
        mockCollection,
      );
    });

    it("应该正确设置默认值", async () => {
      // Arrange
      const userId = "test-user-id";
      const minimalDto = {
        name: "最小测试集合",
      };

      const expectedCollection = {
        ...mockCollection,
        collectionType: CollectionType.GENERAL,
        visibility: CollectionVisibility.PRIVATE,
        sortOrder: 0,
        allowCollaboration: true,
      };

      mockCollectionRepository.create.mockReturnValue(
        expectedCollection as any,
      );
      mockCollectionRepository.save.mockResolvedValue(
        expectedCollection as any,
      );

      // Act
      const result = await service.createCollection(
        minimalDto as CreateCollectionDto,
        userId,
      );

      // Assert
      expect(result.collectionType).toBe(CollectionType.GENERAL);
      expect(result.visibility).toBe(CollectionVisibility.PRIVATE);
      expect(result.allowCollaboration).toBe(true);
    });

    it("应该正确处理集合设置", async () => {
      // Arrange
      const userId = "test-user-id";
      const customSettings = {
        autoAddSimilar: true,
        notifyOnNewReference: false,
        allowPublicContribution: true,
        moderationRequired: true,
      };
      const dtoWithSettings = {
        ...createCollectionDto,
        settings: customSettings,
      };

      const expectedCollection = {
        ...mockCollection,
        settings: customSettings,
      };
      mockCollectionRepository.create.mockReturnValue(
        expectedCollection as any,
      );
      mockCollectionRepository.save.mockResolvedValue(
        expectedCollection as any,
      );

      // Act
      const result = await service.createCollection(dtoWithSettings, userId);

      // Assert
      expect(result.settings).toEqual(customSettings);
    });

    it("应该初始化统计信息", async () => {
      // Arrange
      const userId = "test-user-id";
      mockCollectionRepository.create.mockReturnValue(mockCollection as any);
      mockCollectionRepository.save.mockResolvedValue(mockCollection as any);

      // Act
      const result = await service.createCollection(
        createCollectionDto,
        userId,
      );

      // Assert
      expect(mockCollectionRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          referenceCount: 0,
          followersCount: 0,
          likesCount: 0,
          isFeatured: false,
          statistics: {
            totalViews: 0,
            monthlyViews: 0,
            totalShares: 0,
            averageRating: 0,
            topTags: [],
          },
        }),
      );
    });
  });

  describe("getUserCollections - 获取用户集合列表", () => {
    const mockCollections = [mockCollection];
    const mockTotal = 1;

    it("应该成功获取用户集合列表", async () => {
      // Arrange
      const userId = "test-user-id";
      const query: CollectionQueryDto = {
        page: 1,
        limit: 10,
        status: CollectionStatus.ACTIVE,
      };

      const mockQueryBuilder = mockCollectionRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        mockCollections as any,
        mockTotal,
      ]);

      // Act
      const result = await service.getUserCollections(userId, query);

      // Assert
      expect(result.data).toEqual(mockCollections);
      expect(result.total).toBe(mockTotal);
      expect(result.page).toBe(1);
      expect(result.limit).toBe(10);
      expect(result.totalPages).toBe(1);
    });

    it("应该根据集合类型筛选", async () => {
      // Arrange
      const userId = "test-user-id";
      const query: CollectionQueryDto = {
        page: 1,
        limit: 10,
        collectionType: CollectionType.THEMATIC,
      };

      const mockQueryBuilder = mockCollectionRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        mockCollections as any,
        mockTotal,
      ]);

      // Act
      await service.getUserCollections(userId, query);

      // Assert
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "collection.collectionType = :collectionType",
        { collectionType: CollectionType.THEMATIC },
      );
    });

    it("应该根据可见性筛选", async () => {
      // Arrange
      const userId = "test-user-id";
      const query: CollectionQueryDto = {
        page: 1,
        limit: 10,
        visibility: CollectionVisibility.PUBLIC,
      };

      const mockQueryBuilder = mockCollectionRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        mockCollections as any,
        mockTotal,
      ]);

      // Act
      await service.getUserCollections(userId, query);

      // Assert
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "collection.visibility = :visibility",
        { visibility: CollectionVisibility.PUBLIC },
      );
    });

    it("应该根据标签筛选", async () => {
      // Arrange
      const userId = "test-user-id";
      const query: CollectionQueryDto = {
        page: 1,
        limit: 10,
        tags: ["测试", "集合"],
      };

      const mockQueryBuilder = mockCollectionRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        mockCollections as any,
        mockTotal,
      ]);

      // Act
      await service.getUserCollections(userId, query);

      // Assert
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "collection.tags && :tags",
        { tags: ["测试", "集合"] },
      );
    });

    it("应该根据精选状态筛选", async () => {
      // Arrange
      const userId = "test-user-id";
      const query: CollectionQueryDto = {
        page: 1,
        limit: 10,
        isFeatured: true,
      };

      const mockQueryBuilder = mockCollectionRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        mockCollections as any,
        mockTotal,
      ]);

      // Act
      await service.getUserCollections(userId, query);

      // Assert
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "collection.isFeatured = :isFeatured",
        { isFeatured: true },
      );
    });

    it("应该根据搜索关键词筛选", async () => {
      // Arrange
      const userId = "test-user-id";
      const query: CollectionQueryDto = {
        page: 1,
        limit: 10,
        search: "测试关键词",
      };

      const mockQueryBuilder = mockCollectionRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        mockCollections as any,
        mockTotal,
      ]);

      // Act
      await service.getUserCollections(userId, query);

      // Assert
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "(collection.name ILIKE :search OR collection.description ILIKE :search)",
        { search: "%测试关键词%" },
      );
    });

    it("应该正确处理排序", async () => {
      // Arrange
      const userId = "test-user-id";
      const query: CollectionQueryDto = {
        page: 1,
        limit: 10,
        sortBy: "referenceCount",
        sortOrder: "DESC",
      };

      const mockQueryBuilder = mockCollectionRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        mockCollections as any,
        mockTotal,
      ]);

      // Act
      await service.getUserCollections(userId, query);

      // Assert
      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith(
        "collection.referenceCount",
        "DESC",
      );
    });
  });

  describe("getReferenceStats - 获取引用统计信息", () => {
    const mockStats = {
      userId: "test-user-id",
      totalReferences: 10,
      totalCollections: 3,
      publicReferences: 5,
      privateReferences: 5,
      totalLikes: 25,
      totalViews: 100,
      totalShares: 15,
      collectionStats: [
        {
          collectionType: CollectionType.GENERAL,
          count: 2,
          referenceCount: 8,
        },
        {
          collectionType: CollectionType.THEMATIC,
          count: 1,
          referenceCount: 2,
        },
      ],
      importanceStats: [
        { importance: 5, count: 2, percentage: 20 },
        { importance: 4, count: 3, percentage: 30 },
        { importance: 3, count: 4, percentage: 40 },
        { importance: 2, count: 1, percentage: 10 },
      ],
      topTags: [
        { tag: "测试", count: 5, recentUse: new Date() },
        { tag: "引用", count: 3, recentUse: new Date() },
      ],
      topCollections: [
        {
          collectionId: "collection-1",
          collectionName: "测试集合1",
          referenceCount: 5,
          likesCount: 10,
          lastUpdate: new Date(),
        },
      ],
      recentActivity: [
        { type: "reference_created" as const, date: new Date(), count: 3 },
        { type: "collection_created" as const, date: new Date(), count: 1 },
      ],
      readingStats: {
        totalReadCount: 50,
        averageImportance: 3.5,
        mostReadReference: {
          referenceId: "ref-1",
          title: "最常读的引用",
          readCount: 15,
        },
        readingTrend: [
          { date: new Date(), readCount: 5 },
          { date: new Date(), readCount: 8 },
        ],
      },
    };

    it("应该成功获取引用统计信息", async () => {
      // Arrange
      const userId = "test-user-id";

      // Mock基础统计查询
      mockReferenceRepository.count.mockResolvedValueOnce(10); // totalReferences
      mockReferenceRepository.count.mockResolvedValueOnce(5); // publicReferences
      mockReferenceRepository.count.mockResolvedValueOnce(5); // privateReferences
      mockCollectionRepository.count.mockResolvedValue(3); // totalCollections

      // Mock QueryBuilder查询
      const mockReferenceQueryBuilder =
        mockReferenceRepository.createQueryBuilder();
      const mockCollectionQueryBuilder =
        mockCollectionRepository.createQueryBuilder();

      // Mock聚合查询结果
      (mockReferenceQueryBuilder.getRawMany as jest.Mock)
        .mockResolvedValueOnce([
          { importance: 5, count: "2" },
          { importance: 4, count: "3" },
          { importance: 3, count: "4" },
          { importance: 2, count: "1" },
        ])
        .mockResolvedValueOnce([
          { tag: "测试", count: "5" },
          { tag: "引用", count: "3" },
        ]);

      (mockCollectionQueryBuilder.getRawMany as jest.Mock).mockResolvedValue([
        {
          collectionType: CollectionType.GENERAL,
          count: "2",
          referenceCount: "8",
        },
        {
          collectionType: CollectionType.THEMATIC,
          count: "1",
          referenceCount: "2",
        },
      ]);

      // Act
      const result = await service.getReferenceStats(userId);

      // Assert
      expect(result.userId).toBe(userId);
      expect(result.totalReferences).toBe(10);
      expect(result.totalCollections).toBe(3);
      expect(result.publicReferences).toBe(5);
      expect(result.privateReferences).toBe(5);
      expect(result.importanceStats).toHaveLength(4);
      expect(result.collectionStats).toHaveLength(2);
    });

    it("应该正确计算重要性统计百分比", async () => {
      // Arrange
      const userId = "test-user-id";

      mockReferenceRepository.count.mockResolvedValueOnce(10);
      mockReferenceRepository.count.mockResolvedValueOnce(5);
      mockReferenceRepository.count.mockResolvedValueOnce(5);
      mockCollectionRepository.count.mockResolvedValue(3);

      const mockReferenceQueryBuilder =
        mockReferenceRepository.createQueryBuilder();
      const mockCollectionQueryBuilder =
        mockCollectionRepository.createQueryBuilder();

      (mockReferenceQueryBuilder.getRawMany as jest.Mock)
        .mockResolvedValueOnce([
          { importance: 5, count: "2" },
          { importance: 4, count: "8" },
        ])
        .mockResolvedValueOnce([]);

      (mockCollectionQueryBuilder.getRawMany as jest.Mock).mockResolvedValue(
        [],
      );

      // Act
      const result = await service.getReferenceStats(userId);

      // Assert
      const importanceStats = result.importanceStats;
      expect(importanceStats[0].percentage).toBe(20); // 2/10 = 20%
      expect(importanceStats[1].percentage).toBe(80); // 8/10 = 80%
    });

    it("应该处理没有数据的情况", async () => {
      // Arrange
      const userId = "test-user-id";

      mockReferenceRepository.count.mockResolvedValue(0);
      mockCollectionRepository.count.mockResolvedValue(0);

      const mockReferenceQueryBuilder =
        mockReferenceRepository.createQueryBuilder();
      const mockCollectionQueryBuilder =
        mockCollectionRepository.createQueryBuilder();

      (mockReferenceQueryBuilder.getRawMany as jest.Mock).mockResolvedValue([]);
      (mockCollectionQueryBuilder.getRawMany as jest.Mock).mockResolvedValue(
        [],
      );

      // Act
      const result = await service.getReferenceStats(userId);

      // Assert
      expect(result.totalReferences).toBe(0);
      expect(result.totalCollections).toBe(0);
      expect(result.importanceStats).toHaveLength(0);
      expect(result.collectionStats).toHaveLength(0);
      expect(result.topTags).toHaveLength(0);
    });

    it("应该正确处理集合统计信息", async () => {
      // Arrange
      const userId = "test-user-id";

      mockReferenceRepository.count.mockResolvedValue(10);
      mockCollectionRepository.count.mockResolvedValue(3);

      const mockReferenceQueryBuilder =
        mockReferenceRepository.createQueryBuilder();
      const mockCollectionQueryBuilder =
        mockCollectionRepository.createQueryBuilder();

      (mockReferenceQueryBuilder.getRawMany as jest.Mock).mockResolvedValue([]);
      (mockCollectionQueryBuilder.getRawMany as jest.Mock).mockResolvedValue([
        {
          collectionType: CollectionType.GENERAL,
          count: "2",
          referenceCount: "15",
        },
        {
          collectionType: CollectionType.RESEARCH,
          count: "1",
          referenceCount: "5",
        },
      ]);

      // Act
      const result = await service.getReferenceStats(userId);

      // Assert
      expect(result.collectionStats).toHaveLength(2);
      expect(result.collectionStats[0]).toEqual({
        collectionType: CollectionType.GENERAL,
        count: 2,
        referenceCount: 15,
      });
      expect(result.collectionStats[1]).toEqual({
        collectionType: CollectionType.RESEARCH,
        count: 1,
        referenceCount: 5,
      });
    });

    it("应该正确处理标签统计", async () => {
      // Arrange
      const userId = "test-user-id";

      mockReferenceRepository.count.mockResolvedValue(10);
      mockCollectionRepository.count.mockResolvedValue(3);

      const mockReferenceQueryBuilder =
        mockReferenceRepository.createQueryBuilder();
      const mockCollectionQueryBuilder =
        mockCollectionRepository.createQueryBuilder();

      (mockReferenceQueryBuilder.getRawMany as jest.Mock)
        .mockResolvedValueOnce([])
        .mockResolvedValueOnce([
          { tag: "科幻", count: "8" },
          { tag: "悬疑", count: "5" },
          { tag: "历史", count: "3" },
        ]);

      (mockCollectionQueryBuilder.getRawMany as jest.Mock).mockResolvedValue(
        [],
      );

      // Act
      const result = await service.getReferenceStats(userId);

      // Assert
      expect(result.topTags).toHaveLength(3);
      expect(result.topTags[0].tag).toBe("科幻");
      expect(result.topTags[0].count).toBe(8);
      expect(result.topTags[1].tag).toBe("悬疑");
      expect(result.topTags[1].count).toBe(5);
    });
  });

  describe("边界条件和错误处理", () => {
    it("应该处理数据库连接错误", async () => {
      // Arrange
      const userId = "test-user-id";
      const dto = { name: "测试集合" } as CreateCollectionDto;
      mockCollectionRepository.save.mockRejectedValue(
        new Error("数据库连接失败"),
      );

      // Act & Assert
      await expect(service.createCollection(dto, userId)).rejects.toThrow(
        "数据库连接失败",
      );
    });

    it("应该处理分页边界值", async () => {
      // Arrange
      const userId = "test-user-id";
      const query: ReferenceQueryDto = {
        page: 0, // 无效页码
        limit: -1, // 无效限制
      };

      const mockQueryBuilder = mockReferenceRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        [],
        0,
      ]);

      // Act
      const result = await service.getUserReferences(userId, query);

      // Assert
      expect(result.page).toBe(1); // 应该修正为最小值1
      expect(result.limit).toBe(10); // 应该修正为默认值10
    });

    it("应该处理空字符串搜索", async () => {
      // Arrange
      const userId = "test-user-id";
      const query: ReferenceQueryDto = {
        page: 1,
        limit: 10,
        search: "",
      };

      const mockQueryBuilder = mockReferenceRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        [],
        0,
      ]);

      // Act
      await service.getUserReferences(userId, query);

      // Assert
      expect(mockQueryBuilder.andWhere).not.toHaveBeenCalledWith(
        expect.stringContaining("ILIKE"),
        expect.anything(),
      );
    });

    it("应该处理无效的排序字段", async () => {
      // Arrange
      const userId = "test-user-id";
      const query: ReferenceQueryDto = {
        page: 1,
        limit: 10,
        sortBy: "invalidField",
        sortOrder: "INVALID" as "ASC",
      };

      const mockQueryBuilder = mockReferenceRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        [],
        0,
      ]);

      // Act
      await service.getUserReferences(userId, query);

      // Assert
      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith(
        "reference.createdAt", // 应该回退到默认排序字段
        "DESC", // 应该回退到默认排序方向
      );
    });
  });

  describe("性能和优化测试", () => {
    it("应该使用适当的数据库索引", async () => {
      // Arrange
      const userId = "test-user-id";
      const query: ReferenceQueryDto = {
        page: 1,
        limit: 10,
        status: ReferenceStatus.ACTIVE,
      };

      const mockQueryBuilder = mockReferenceRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        [],
        0,
      ]);

      // Act
      await service.getUserReferences(userId, query);

      // Assert
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        "reference.userId = :userId",
        { userId },
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "reference.status = :status",
        { status: ReferenceStatus.ACTIVE },
      );
    });

    it("应该限制查询结果数量", async () => {
      // Arrange
      const userId = "test-user-id";
      const query: ReferenceQueryDto = {
        page: 1,
        limit: 1000, // 过大的限制
      };

      const mockQueryBuilder = mockReferenceRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        [],
        0,
      ]);

      // Act
      await service.getUserReferences(userId, query);

      // Assert
      expect(mockQueryBuilder.take).toHaveBeenCalledWith(100); // 应该限制为最大值
    });

    it("应该使用延迟加载避免N+1查询问题", async () => {
      // Arrange
      const referenceId = "test-reference-id";
      const userId = "test-user-id";

      const mockQueryBuilder = mockReferenceRepository.createQueryBuilder();
      (mockQueryBuilder.getOne as jest.Mock).mockResolvedValue(
        mockReference as any,
      );

      // Act
      await service.getReferenceById(referenceId, userId);

      // Assert
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith(
        "reference.user",
        "user",
      );
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith(
        "reference.story",
        "story",
      );
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith(
        "reference.collection",
        "collection",
      );
    });
  });

  describe("业务逻辑验证", () => {
    it("应该验证引用标题不为空", async () => {
      // Arrange
      const userId = "test-user-id";
      const invalidDto = {
        storyId: "test-story-id",
        title: "", // 空标题
      } as CreateReferenceDto;

      // Act & Assert
      // 注意：实际的验证由class-validator处理，这里测试服务层的逻辑
      mockReferenceRepository.create.mockReturnValue(mockReference as any);
      mockReferenceRepository.save.mockResolvedValue(mockReference as any);

      const result = await service.createReference(invalidDto, userId);
      expect(result).toBeDefined();
    });

    it("应该验证集合名称不为空", async () => {
      // Arrange
      const userId = "test-user-id";
      const invalidDto = {
        name: "", // 空名称
      } as CreateCollectionDto;

      // Act & Assert
      mockCollectionRepository.create.mockReturnValue(mockCollection as any);
      mockCollectionRepository.save.mockResolvedValue(mockCollection as any);

      const result = await service.createCollection(invalidDto, userId);
      expect(result).toBeDefined();
    });

    it("应该正确处理引用可见性", async () => {
      // Arrange
      const userId = "test-user-id";
      const publicReference = {
        ...mockReference,
        visibility: ReferenceVisibility.PUBLIC,
      };

      const mockQueryBuilder = mockReferenceRepository.createQueryBuilder();
      (mockQueryBuilder.getOne as jest.Mock).mockResolvedValue(
        publicReference as any,
      );

      // Act
      const result = await service.getReferenceById("test-id", "other-user");

      // Assert
      expect(result.visibility).toBe(ReferenceVisibility.PUBLIC);
    });

    it("应该正确处理集合协作权限", async () => {
      // Arrange
      const userId = "test-user-id";
      const collaborativeCollection = {
        ...mockCollection,
        allowCollaboration: true,
        visibility: CollectionVisibility.PUBLIC,
      };

      mockCollectionRepository.findOne.mockResolvedValue(
        collaborativeCollection as any,
      );
      mockReferenceRepository.create.mockReturnValue(mockReference as any);
      mockReferenceRepository.save.mockResolvedValue(mockReference as any);

      // Act
      const result = await service.createReference(
        {
          storyId: "test-story-id",
          collectionId: collaborativeCollection.id,
          title: "测试引用",
        } as CreateReferenceDto,
        "other-user",
      );

      // Assert
      expect(result).toBeDefined();
    });
  });
});
