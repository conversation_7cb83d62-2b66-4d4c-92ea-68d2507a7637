import {
  IsUUID,
  IsString,
  IsEnum,
  IsOptional,
  IsArray,
  IsInt,
  ValidateNested,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
} from "class-validator";
import { Type } from "class-transformer";
import { ReferenceVisibility } from "../entities/story-reference.entity";

class QuotedSegmentDto {
  @IsOptional()
  @IsUUID("4", {
    message: "章节ID必须是有效的UUID格式",
  })
  chapterId?: string;

  @IsInt({
    message: "起始位置必须是整数",
  })
  @Type(() => Number)
  @Min(0, {
    message: "起始位置不能为负数",
  })
  startPosition: number;

  @IsInt({
    message: "结束位置必须是整数",
  })
  @Type(() => Number)
  @Min(0, {
    message: "结束位置不能为负数",
  })
  endPosition: number;

  @IsString({
    message: "引用文本必须是字符串",
  })
  @MinLength(1, {
    message: "引用文本不能为空",
  })
  @MaxLength(1000, {
    message: "引用文本不能超过1000个字符",
  })
  text: string;

  @IsOptional()
  @IsString({
    message: "片段备注必须是字符串",
  })
  @MaxLength(500, {
    message: "片段备注不能超过500个字符",
  })
  note?: string;
}

/**
 * 创建故事引用DTO
 */
export class CreateReferenceDto {
  @IsUUID("4", {
    message: "故事ID必须是有效的UUID格式",
  })
  storyId: string;

  @IsOptional()
  @IsUUID("4", {
    message: "集合ID必须是有效的UUID格式",
  })
  collectionId?: string;

  @IsString({
    message: "引用标题必须是字符串",
  })
  @MinLength(1, {
    message: "引用标题不能为空",
  })
  @MaxLength(200, {
    message: "引用标题不能超过200个字符",
  })
  title: string;

  @IsOptional()
  @IsString({
    message: "个人评注必须是字符串",
  })
  @MaxLength(2000, {
    message: "个人评注不能超过2000个字符",
  })
  annotation?: string;

  @IsOptional()
  @IsArray({
    message: "引用片段必须是数组",
  })
  @ValidateNested({ each: true })
  @Type(() => QuotedSegmentDto)
  quotedSegments?: QuotedSegmentDto[];

  @IsOptional()
  @IsEnum(ReferenceVisibility, {
    message: "可见性设置必须是有效的选项",
  })
  visibility?: ReferenceVisibility = ReferenceVisibility.PRIVATE;

  @IsOptional()
  @IsArray({
    message: "标签必须是数组",
  })
  @IsString({
    each: true,
    message: "每个标签必须是字符串",
  })
  @MaxLength(30, {
    each: true,
    message: "每个标签不能超过30个字符",
  })
  tags?: string[];

  @IsOptional()
  @IsInt({
    message: "重要性评分必须是整数",
  })
  @Type(() => Number)
  @Min(1, {
    message: "重要性评分最小值为1",
  })
  @Max(5, {
    message: "重要性评分最大值为5",
  })
  importance?: number = 3;

  @IsOptional()
  @Type(() => Object)
  metadata?: Record<string, unknown>;
}
