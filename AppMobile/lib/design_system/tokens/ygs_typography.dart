import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'ygs_colors.dart';

/// YGS设计系统 - 字体排版令牌
/// 基于Figma设计规范v1.0.0，使用flutter_screenutil响应式适配
class YgsTypography {
  YgsTypography._(); // 防止实例化

  // ========== 字体家族定义 ==========
  /// 中文字体 - PingFang SC
  static const String fontFamilyChinese = 'PingFang SC';
  
  /// 英文字体 - SF Pro
  static const String fontFamilyEnglish = 'SF Pro';
  
  /// 数字字体 - Roboto
  static const String fontFamilyNumber = 'Roboto';

  // ========== 大标题样式 ==========
  /// 超大标题 - 48sp，用于应用名称等
  static TextStyle displayLarge = TextStyle(
    fontFamily: fontFamilyChinese,
    fontSize: 48.sp,
    fontWeight: FontWeight.w600,
    color: YgsColors.textPrimary,
    height: 1.2,
    letterSpacing: 0,
  );

  /// 大标题 - 36sp，用于页面标题
  static TextStyle displayMedium = TextStyle(
    fontFamily: fontFamilyChinese,
    fontSize: 36.sp,
    fontWeight: FontWeight.w600,
    color: YgsColors.textPrimary,
    height: 1.2,
    letterSpacing: 0,
  );

  /// 中标题 - 30sp，用于章节标题
  static TextStyle displaySmall = TextStyle(
    fontFamily: fontFamilyChinese,
    fontSize: 30.sp,
    fontWeight: FontWeight.w500,
    color: YgsColors.textPrimary,
    height: 1.3,
    letterSpacing: 0,
  );

  // ========== 标题样式 ==========
  /// 大标题 - 28sp
  static TextStyle headlineLarge = TextStyle(
    fontFamily: fontFamilyChinese,
    fontSize: 28.sp,
    fontWeight: FontWeight.w600,
    color: YgsColors.textPrimary,
    height: 1.3,
    letterSpacing: 0,
  );

  /// 中标题 - 24sp
  static TextStyle headlineMedium = TextStyle(
    fontFamily: fontFamilyChinese,
    fontSize: 24.sp,
    fontWeight: FontWeight.w500,
    color: YgsColors.textPrimary,
    height: 1.3,
    letterSpacing: 0,
  );

  /// 小标题 - 22sp
  static TextStyle headlineSmall = TextStyle(
    fontFamily: fontFamilyChinese,
    fontSize: 22.sp,
    fontWeight: FontWeight.w500,
    color: YgsColors.textPrimary,
    height: 1.4,
    letterSpacing: 0,
  );

  // ========== 正文样式 ==========
  /// 大正文 - 20sp
  static TextStyle bodyLarge = TextStyle(
    fontFamily: fontFamilyChinese,
    fontSize: 20.sp,
    fontWeight: FontWeight.w400,
    color: YgsColors.textPrimary,
    height: 1.4,
    letterSpacing: 0,
  );

  /// 中正文 - 18sp
  static TextStyle bodyMedium = TextStyle(
    fontFamily: fontFamilyChinese,
    fontSize: 18.sp,
    fontWeight: FontWeight.w400,
    color: YgsColors.textPrimary,
    height: 1.4,
    letterSpacing: 0,
  );

  /// 小正文 - 16sp
  static TextStyle bodySmall = TextStyle(
    fontFamily: fontFamilyChinese,
    fontSize: 16.sp,
    fontWeight: FontWeight.w400,
    color: YgsColors.textSecondary,
    height: 1.5,
    letterSpacing: 0,
  );

  // ========== 标签样式 ==========
  /// 大标签 - 16sp
  static TextStyle labelLarge = TextStyle(
    fontFamily: fontFamilyEnglish,
    fontSize: 16.sp,
    fontWeight: FontWeight.w500,
    color: YgsColors.textPrimary,
    height: 1.2,
    letterSpacing: 0.5,
  );

  /// 中标签 - 14sp
  static TextStyle labelMedium = TextStyle(
    fontFamily: fontFamilyEnglish,
    fontSize: 14.sp,
    fontWeight: FontWeight.w500,
    color: YgsColors.textSecondary,
    height: 1.2,
    letterSpacing: 0.5,
  );

  /// 小标签 - 12sp
  static TextStyle labelSmall = TextStyle(
    fontFamily: fontFamilyEnglish,
    fontSize: 12.sp,
    fontWeight: FontWeight.w400,
    color: YgsColors.textTertiary,
    height: 1.2,
    letterSpacing: 0.5,
  );

  // ========== 特殊样式 ==========
  /// 按钮文字 - 28sp
  static TextStyle button = TextStyle(
    fontFamily: fontFamilyChinese,
    fontSize: 28.sp,
    fontWeight: FontWeight.w500,
    color: YgsColors.onBrandPrimary,
    height: 1.0,
    letterSpacing: 0,
  );

  /// 输入框文字 - 30sp
  static TextStyle input = TextStyle(
    fontFamily: fontFamilyChinese,
    fontSize: 30.sp,
    fontWeight: FontWeight.w400,
    color: YgsColors.textPrimary,
    height: 1.0,
    letterSpacing: 0,
  );

  /// 输入框提示文字 - 30sp
  static TextStyle inputHint = TextStyle(
    fontFamily: fontFamilyChinese,
    fontSize: 30.sp,
    fontWeight: FontWeight.w400,
    color: YgsColors.textSecondary,
    height: 1.0,
    letterSpacing: 0,
  );

  /// 验证码数字 - 36sp
  static TextStyle codeNumber = TextStyle(
    fontFamily: fontFamilyNumber,
    fontSize: 36.sp,
    fontWeight: FontWeight.w600,
    color: YgsColors.textPrimary,
    height: 1.0,
    letterSpacing: 0,
  );

  /// Tab标签 - 20sp
  static TextStyle tabLabel = TextStyle(
    fontFamily: fontFamilyEnglish,
    fontSize: 20.sp,
    fontWeight: FontWeight.w400,
    color: YgsColors.textSecondary,
    height: 1.0,
    letterSpacing: 0,
  );

  /// Tab标签激活状态 - 20sp
  static TextStyle tabLabelActive = TextStyle(
    fontFamily: fontFamilyEnglish,
    fontSize: 20.sp,
    fontWeight: FontWeight.w500,
    color: YgsColors.textPrimary,
    height: 1.0,
    letterSpacing: 0,
  );
}