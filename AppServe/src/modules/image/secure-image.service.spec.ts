/**
 * SecureImageService 单元测试
 * 测试安全图片服务的核心功能，包括URL生成、权限控制、批量处理等
 */

import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { ConfigService } from "@nestjs/config";
import { NotFoundException } from "@nestjs/common";
import { SecureImageService, ImageAccessLevel } from "./secure-image.service";
import type { SecureImageRequest } from "./secure-image.service";

// Mock OSS
const mockOssInstance = {
  head: jest.fn(),
  signatureUrl: jest.fn(),
};

// 重置Mock方法
const resetOssMocks = () => {
  mockOssInstance.head.mockReset();
  mockOssInstance.signatureUrl.mockReset();
};

jest.mock("ali-oss", () => {
  return jest.fn().mockImplementation(() => mockOssInstance);
});

describe("SecureImageService", () => {
  let service: SecureImageService;
  let mockConfigService: jest.Mocked<ConfigService>;

  // Mock配置
  const mockOSSConfig = {
    region: "oss-cn-hangzhou",
    accessKeyId: "test-access-key-id",
    accessKeySecret: "test-access-key-secret",
    bucket: "test-bucket",
    endpoint: "oss-cn-hangzhou.aliyuncs.com",
    cdnDomain: "https://cdn.example.com",
    baseDir: "ygs/",
  };

  beforeEach(async () => {
    // 清除所有 mock
    jest.clearAllMocks();
    resetOssMocks();

    // 设置默认的Mock行为
    mockOssInstance.head.mockResolvedValue({});
    mockOssInstance.signatureUrl.mockReturnValue(
      "https://signed-url.example.com/test-image.jpg",
    );

    // Mock ConfigService
    const mockConfigServiceMethods = {
      get: jest.fn().mockImplementation((key: string) => {
        if (key === "oss") {
          return mockOSSConfig;
        }
        return undefined;
      }),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SecureImageService,
        {
          provide: ConfigService,
          useValue: mockConfigServiceMethods,
        },
      ],
    }).compile();

    service = module.get<SecureImageService>(SecureImageService);
    mockConfigService = module.get(ConfigService) as jest.Mocked<ConfigService>;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  describe("getSecureImageUrl", () => {
    const mockRequest: SecureImageRequest = {
      imagePath: "test-image.jpg",
      accessLevel: ImageAccessLevel.PUBLIC,
      userId: "user-id",
    };

    beforeEach(() => {
      // 确保每个测试前重置Mock状态
      mockOssInstance.head.mockResolvedValue({}); // 图片存在
      mockOssInstance.signatureUrl.mockReturnValue(
        "https://signed-url.example.com/test-image.jpg",
      );
    });

    it("should return CDN URL for public access", async () => {
      const result = await service.getSecureImageUrl({
        ...mockRequest,
        accessLevel: ImageAccessLevel.PUBLIC,
      });

      expect(result).toEqual({
        url: "https://cdn.example.com/ygs/test-image.jpg",
        accessLevel: ImageAccessLevel.PUBLIC,
        cacheable: true,
      });
      expect(mockOssInstance.head).toHaveBeenCalledWith("test-image.jpg");
    });

    it("should return CDN URL for friends access", async () => {
      const result = await service.getSecureImageUrl({
        ...mockRequest,
        accessLevel: ImageAccessLevel.FRIENDS,
      });

      expect(result).toEqual({
        url: "https://cdn.example.com/ygs/test-image.jpg",
        accessLevel: ImageAccessLevel.FRIENDS,
        cacheable: true,
      });
    });

    it("should return signed URL for private access", async () => {
      const mockSignedUrl =
        "https://test-bucket.oss-cn-hangzhou.aliyuncs.com/test-image.jpg?signature=abc123";
      mockOssInstance.signatureUrl.mockReturnValue(mockSignedUrl);

      const result = await service.getSecureImageUrl({
        ...mockRequest,
        accessLevel: ImageAccessLevel.PRIVATE,
        expiresIn: 3600,
      });

      expect(result).toEqual({
        url: mockSignedUrl,
        accessLevel: ImageAccessLevel.PRIVATE,
        expiresAt: expect.any(Date),
        cacheable: false,
      });
      expect(mockOssInstance.signatureUrl).toHaveBeenCalledWith(
        "test-image.jpg",
        {
          expires: 3600,
          method: "GET",
        },
      );
    });

    it("should use default expires for private access when not specified", async () => {
      const mockSignedUrl =
        "https://test-bucket.oss-cn-hangzhou.aliyuncs.com/test-image.jpg?signature=abc123";
      mockOssInstance.signatureUrl.mockReturnValue(mockSignedUrl);

      const originalEnv = process.env.OSS_SIGNED_URL_EXPIRES;
      process.env.OSS_SIGNED_URL_EXPIRES = "1800";

      const result = await service.getSecureImageUrl({
        ...mockRequest,
        accessLevel: ImageAccessLevel.PRIVATE,
      });

      expect(result.cacheable).toBe(false);
      expect(mockOssInstance.signatureUrl).toHaveBeenCalledWith(
        "test-image.jpg",
        {
          expires: 1800,
          method: "GET",
        },
      );

      process.env.OSS_SIGNED_URL_EXPIRES = originalEnv;
    });

    it("should throw error for unsupported access level", async () => {
      await expect(
        service.getSecureImageUrl({
          ...mockRequest,
          accessLevel: "unsupported" as ImageAccessLevel,
        }),
      ).rejects.toThrow("不支持的访问级别");
    });

    it("should throw NotFoundException when image does not exist", async () => {
      const error = new Error("NoSuchKey") as Error & { code: string };
      error.code = "NoSuchKey";
      mockOssInstance.head.mockRejectedValue(error);

      await expect(service.getSecureImageUrl(mockRequest)).rejects.toThrow(
        NotFoundException,
      );
      await expect(service.getSecureImageUrl(mockRequest)).rejects.toThrow(
        "图片不存在",
      );
    });

    it("should rethrow other errors from checkImageExists", async () => {
      const error = new Error("Network error");
      mockOssInstance.head.mockRejectedValue(error);

      await expect(service.getSecureImageUrl(mockRequest)).rejects.toThrow(
        "Network error",
      );
    });

    it("should handle signed URL generation error", async () => {
      const signError = new Error("Signature failed");
      mockOssInstance.signatureUrl.mockImplementation(() => {
        throw signError;
      });

      await expect(
        service.getSecureImageUrl({
          ...mockRequest,
          accessLevel: ImageAccessLevel.PRIVATE,
        }),
      ).rejects.toThrow("Signature failed");
    });

    it("should handle path with base directory already included", async () => {
      const result = await service.getSecureImageUrl({
        ...mockRequest,
        imagePath: "ygs/test-image.jpg", // 已包含baseDir
        accessLevel: ImageAccessLevel.PUBLIC,
      });

      expect(result.url).toBe("https://cdn.example.com/ygs/test-image.jpg");
    });

    it("should handle missing CDN domain configuration", async () => {
      const configWithoutCDN = {
        ...mockOSSConfig,
        cdnDomain: undefined,
      };
      mockConfigService.get.mockReturnValue(configWithoutCDN);

      const result = await service.getSecureImageUrl({
        ...mockRequest,
        accessLevel: ImageAccessLevel.PUBLIC,
      });

      expect(result.url).toBe(
        "https://test-bucket.oss-cn-hangzhou.aliyuncs.com/ygs/test-image.jpg",
      );
    });
  });

  describe("getBatchSecureUrls", () => {
    beforeEach(() => {
      mockOssInstance.head.mockResolvedValue({}); // 所有图片都存在
    });

    it("should process batch requests successfully", async () => {
      const mockRequests: SecureImageRequest[] = [
        {
          imagePath: "image1.jpg",
          accessLevel: ImageAccessLevel.PUBLIC,
          userId: "user-id",
        },
        {
          imagePath: "image2.jpg",
          accessLevel: ImageAccessLevel.FRIENDS,
          userId: "user-id",
        },
      ];

      const results = await service.getBatchSecureUrls(mockRequests);

      expect(results).toHaveLength(2);
      expect(results[0]).toEqual({
        url: "https://cdn.example.com/ygs/image1.jpg",
        accessLevel: ImageAccessLevel.PUBLIC,
        cacheable: true,
      });
      expect(results[1]).toEqual({
        url: "https://cdn.example.com/ygs/image2.jpg",
        accessLevel: ImageAccessLevel.FRIENDS,
        cacheable: true,
      });
    });

    it("should filter out failed requests", async () => {
      const mockRequests: SecureImageRequest[] = [
        {
          imagePath: "image1.jpg",
          accessLevel: ImageAccessLevel.PUBLIC,
          userId: "user-id",
        },
        {
          imagePath: "nonexistent.jpg",
          accessLevel: ImageAccessLevel.PUBLIC,
          userId: "user-id",
        },
      ];

      // Mock head to fail for the second image
      mockOssInstance.head
        .mockResolvedValueOnce({}) // 第一张图片存在
        .mockRejectedValueOnce(
          Object.assign(new Error("NoSuchKey"), { code: "NoSuchKey" }),
        ); // 第二张图片不存在

      const results = await service.getBatchSecureUrls(mockRequests);

      expect(results).toHaveLength(1);
      expect(results[0].url).toBe("https://cdn.example.com/ygs/image1.jpg");
    });

    it("should handle empty request array", async () => {
      const results = await service.getBatchSecureUrls([]);

      expect(results).toEqual([]);
    });
  });

  describe("getImageSizes", () => {
    it("should return only original URL for private images", () => {
      const baseUrl = "https://example.com/image.jpg";
      const result = service.getImageSizes(baseUrl, ImageAccessLevel.PRIVATE);

      expect(result).toEqual({
        original: baseUrl,
      });
    });

    it("should return all sizes for public images", () => {
      const baseUrl = "https://example.com/image.jpg";
      const result = service.getImageSizes(baseUrl, ImageAccessLevel.PUBLIC);

      expect(result).toEqual({
        original: baseUrl,
        large: `${baseUrl}?x-oss-process=image/resize,w_800`,
        medium: `${baseUrl}?x-oss-process=image/resize,w_400`,
        small: `${baseUrl}?x-oss-process=image/resize,w_200`,
        thumbnail: `${baseUrl}?x-oss-process=image/resize,w_100,h_100`,
      });
    });

    it("should return all sizes for friends images", () => {
      const baseUrl = "https://example.com/image.jpg";
      const result = service.getImageSizes(baseUrl, ImageAccessLevel.FRIENDS);

      expect(result).toEqual({
        original: baseUrl,
        large: `${baseUrl}?x-oss-process=image/resize,w_800`,
        medium: `${baseUrl}?x-oss-process=image/resize,w_400`,
        small: `${baseUrl}?x-oss-process=image/resize,w_200`,
        thumbnail: `${baseUrl}?x-oss-process=image/resize,w_100,h_100`,
      });
    });
  });

  describe("determineAccessLevel", () => {
    it("should return PUBLIC for public story", () => {
      const result = service.determineAccessLevel("story_image", "public");
      expect(result).toBe(ImageAccessLevel.PUBLIC);
    });

    it("should return FRIENDS for friends story", () => {
      const result = service.determineAccessLevel("story_image", "friends");
      expect(result).toBe(ImageAccessLevel.FRIENDS);
    });

    it("should return FRIENDS for followers story", () => {
      const result = service.determineAccessLevel("story_image", "followers");
      expect(result).toBe(ImageAccessLevel.FRIENDS);
    });

    it("should return FRIENDS for group story", () => {
      const result = service.determineAccessLevel("story_image", "group");
      expect(result).toBe(ImageAccessLevel.FRIENDS);
    });

    it("should return PRIVATE for private story", () => {
      const result = service.determineAccessLevel("story_image", "private");
      expect(result).toBe(ImageAccessLevel.PRIVATE);
    });

    it("should return PRIVATE for character_only story", () => {
      const result = service.determineAccessLevel(
        "story_image",
        "character_only",
      );
      expect(result).toBe(ImageAccessLevel.PRIVATE);
    });

    it("should return PRIVATE for unknown visibility", () => {
      const result = service.determineAccessLevel("story_image", "unknown");
      expect(result).toBe(ImageAccessLevel.PRIVATE);
    });
  });

  describe("cleanupExpiredUrls", () => {
    it("should execute cleanup without errors", async () => {
      await expect(service.cleanupExpiredUrls()).resolves.not.toThrow();
    });
  });

  describe("error handling", () => {
    beforeEach(() => {
      mockOssInstance.head.mockResolvedValue({}); // 图片存在
    });

    it("should handle OSS client initialization errors", () => {
      // 测试在构造函数中OSS客户端初始化
      expect(() => {
        // eslint-disable-next-line @typescript-eslint/no-var-requires
        const OSS = require("ali-oss");
        new OSS(mockOSSConfig);
      }).not.toThrow();
    });

    it("should handle non-Error objects in signed URL generation", async () => {
      mockOssInstance.signatureUrl.mockImplementation(() => {
        throw "String error";
      });

      await expect(
        service.getSecureImageUrl({
          imagePath: "test.jpg",
          accessLevel: ImageAccessLevel.PRIVATE,
          userId: "user-id",
        }),
      ).rejects.toBe("String error");
    });
  });

  describe("integration scenarios", () => {
    beforeEach(() => {
      mockOssInstance.head.mockResolvedValue({}); // 图片存在
    });

    it("should handle complete workflow for public image", async () => {
      const request: SecureImageRequest = {
        imagePath: "public/story-123/image.jpg",
        accessLevel: ImageAccessLevel.PUBLIC,
        userId: "user-123",
      };

      const result = await service.getSecureImageUrl(request);

      expect(result.cacheable).toBe(true);
      expect(result.url).toBe(
        "https://cdn.example.com/ygs/public/story-123/image.jpg",
      );
      expect(result.expiresAt).toBeUndefined();
    });

    it("should handle complete workflow for private image with custom expiry", async () => {
      const mockSignedUrl =
        "https://test-bucket.oss-cn-hangzhou.aliyuncs.com/private/image.jpg?signature=abc123";
      mockOssInstance.signatureUrl.mockReturnValue(mockSignedUrl);

      const request: SecureImageRequest = {
        imagePath: "private/user-123/sensitive.jpg",
        accessLevel: ImageAccessLevel.PRIVATE,
        userId: "user-123",
        expiresIn: 900, // 15分钟
      };

      const result = await service.getSecureImageUrl(request);

      expect(result.cacheable).toBe(false);
      expect(result.url).toBe(mockSignedUrl);
      expect(result.expiresAt).toBeInstanceOf(Date);
      expect(mockOssInstance.signatureUrl).toHaveBeenCalledWith(
        "private/user-123/sensitive.jpg",
        {
          expires: 900,
          method: "GET",
        },
      );
    });

    it("should handle mixed batch requests with different access levels", async () => {
      const mockSignedUrl =
        "https://test-bucket.oss-cn-hangzhou.aliyuncs.com/private/image.jpg?signature=abc123";
      mockOssInstance.signatureUrl.mockReturnValue(mockSignedUrl);

      const requests: SecureImageRequest[] = [
        {
          imagePath: "public/image1.jpg",
          accessLevel: ImageAccessLevel.PUBLIC,
          userId: "user-id",
        },
        {
          imagePath: "private/image2.jpg",
          accessLevel: ImageAccessLevel.PRIVATE,
          userId: "user-id",
          expiresIn: 1800,
        },
      ];

      const results = await service.getBatchSecureUrls(requests);

      expect(results).toHaveLength(2);
      expect(results[0].cacheable).toBe(true);
      expect(results[0].url).toBe(
        "https://cdn.example.com/ygs/public/image1.jpg",
      );
      expect(results[1].cacheable).toBe(false);
      expect(results[1].url).toBe(mockSignedUrl);
    });
  });
});
