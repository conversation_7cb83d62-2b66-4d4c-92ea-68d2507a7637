import 'package:freezed_annotation/freezed_annotation.dart';

part 'user_entity.freezed.dart';

/// 用户实体
@freezed
class UserEntity with _$UserEntity {
  const factory UserEntity({
    required String id,
    required String userNumber,
    String? phone,
    String? email,
    required String nickname,
    String? avatar,
    String? bio,
    @Default(false) bool isVip,
    DateTime? vipExpireAt,
    @Default(5) int aiQuota,
    @Default(5) int aiQuotaUsed,
    required DateTime createdAt,
    required DateTime updatedAt,
    
    // 统计信息
    @Default(0) int storiesCount,
    @Default(0) int charactersCount,
    @Default(0) int lightedCount,
    @Default(0) int followersCount,
    @Default(0) int followingCount,
    
    // 设置信息
    @Default(true) bool enableNotification,
    @Default(false) bool enableBiometric,
    @Default('public') String defaultStoryPermission,
  }) = _UserEntity;
}

/// 用户扩展方法
extension UserEntityX on UserEntity {
  /// 是否有剩余AI配额
  bool get hasAiQuota => aiQuotaUsed < aiQuota;
  
  /// 剩余AI配额
  int get remainingAiQuota => aiQuota - aiQuotaUsed;
  
  /// VIP是否有效
  bool get isVipValid => isVip && (vipExpireAt?.isAfter(DateTime.now()) ?? false);
  
  /// 获取显示名称
  String get displayName => nickname.isNotEmpty ? nickname : '用户$userNumber';
  
  /// 获取头像URL
  String? get avatarUrl => avatar?.isNotEmpty == true ? avatar : null;
}