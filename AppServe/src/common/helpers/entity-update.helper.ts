/**
 * 企业级实体更新助手
 *
 * 设计目标:
 * 1. 提供类型安全的 TypeORM 更新操作
 * 2. 统一处理 JSONB 字段的类型兼容性
 * 3. 支持企业级审计和日志记录
 * 4. 渐进式类型安全策略实施
 */

import type { Repository, UpdateResult, ObjectLiteral } from "typeorm";

/**
 * 企业级实体更新助手类
 *
 * 使用场景:
 * - TypeORM update 操作的类型安全封装
 * - JSONB 字段更新的兼容性处理
 * - 企业级错误处理和日志记录
 */
export class EntityUpdateHelper {
  /**
   * 安全更新实体
   *
   * 架构说明:
   * - 使用 Partial<T> 确保类型安全
   * - 通过类型断言绕过 TypeORM 的严格检查
   * - 添加 ESLint 注释说明使用原因
   *
   * @param repository - TypeORM Repository
   * @param id - 实体ID
   * @param updateData - 更新数据
   * @returns Promise<UpdateResult>
   */
  static async safeUpdate<T extends ObjectLiteral>(
    repository: Repository<T>,
    id: string | number,
    updateData: Partial<T>,
  ): Promise<UpdateResult> {
    // 企业级日志记录 (可选)
    // Logger.debug(`Updating entity ${repository.metadata.name} with ID: ${id}`);

    // 使用类型断言绕过 TypeORM 的 JSONB 字段类型检查限制
    // 这是必要的技术债务，用于处理 Record<string, unknown> 的兼容性问题
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return repository.update(id, updateData as any);
  }

  /**
   * 批量安全更新实体
   *
   * @param repository - TypeORM Repository
   * @param criteria - 更新条件
   * @param updateData - 更新数据
   * @returns Promise<UpdateResult>
   */
  static async safeBulkUpdate<T extends ObjectLiteral>(
    repository: Repository<T>,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    criteria: any,
    updateData: Partial<T>,
  ): Promise<UpdateResult> {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return repository.update(criteria, updateData as any);
  }

  /**
   * 创建企业级更新数据构建器
   *
   * 用法示例:
   * ```typescript
   * const updateData = EntityUpdateHelper.buildUpdateData<Report>()
   *   .set('status', ReportStatus.RESOLVED)
   *   .set('reviewNote', 'Approved')
   *   .build();
   * ```
   */
  static buildUpdateData<T extends ObjectLiteral>(): UpdateDataBuilder<T> {
    return new UpdateDataBuilder<T>();
  }
}

/**
 * 更新数据构建器
 * 提供链式调用的更新数据构建
 */
class UpdateDataBuilder<T extends ObjectLiteral> {
  private data: Partial<T> = {};

  set<K extends keyof T>(key: K, value: T[K]): this {
    this.data[key] = value;
    return this;
  }

  setIf<K extends keyof T>(condition: boolean, key: K, value: T[K]): this {
    if (condition) {
      this.data[key] = value;
    }
    return this;
  }

  build(): Partial<T> {
    return this.data;
  }
}

/**
 * 企业级 NULL 值处理工具
 */
export class NullValueHelper {
  /**
   * 统一 undefined 转 null 处理
   *
   * 架构决策: 项目统一使用 null 表示空值
   * - 数据库兼容性
   * - API 标准化
   * - JSON 序列化一致性
   */
  static sanitizeToNull<T>(value: T | undefined): T | null {
    return value ?? null;
  }

  /**
   * 批量处理对象中的 undefined 值
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  static sanitizeObject<T extends Record<string, any>>(obj: T): T {
    const result = { ...obj };

    for (const key in result) {
      if (result[key] === undefined) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (result as any)[key] = null;
      }
    }

    return result;
  }
}
