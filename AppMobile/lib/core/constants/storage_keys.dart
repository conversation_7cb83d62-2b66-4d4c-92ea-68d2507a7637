/// 本地存储键常量
class StorageKeys {
  StorageKeys._();
  
  // 安全存储键（使用flutter_secure_storage）
  static const String accessToken = 'access_token';
  static const String refreshToken = 'refresh_token';
  static const String userId = 'user_id';
  static const String userPhone = 'user_phone';
  static const String deviceId = 'device_id';
  
  // 普通存储键（使用shared_preferences）
  static const String isFirstLaunch = 'is_first_launch';
  static const String hasCompletedOnboarding = 'has_completed_onboarding';
  static const String themeMode = 'theme_mode';
  static const String language = 'language';
  static const String lastSyncTime = 'last_sync_time';
  static const String enableBiometric = 'enable_biometric';
  static const String enableNotifications = 'enable_notifications';
  static const String autoPlayVideo = 'auto_play_video';
  static const String imageQuality = 'image_quality';
  static const String fontSize = 'font_size';
  
  // 缓存键前缀
  static const String userCachePrefix = 'cache_user_';
  static const String storyCachePrefix = 'cache_story_';
  static const String characterCachePrefix = 'cache_character_';
  static const String draftPrefix = 'draft_';
  
  // 搜索历史
  static const String searchHistory = 'search_history';
  static const String recentViewed = 'recent_viewed';
  
  // 临时数据
  static const String tempPhoneNumber = 'temp_phone_number';
  static const String tempSmsCodeTime = 'temp_sms_code_time';
}