import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { NotificationsController } from "./notifications.controller";
import { NotificationsService } from "./notifications.service";
import type { AuthRequest } from "../../common/types/request.types";
import type {
  NotificationQueryDto,
  BatchNotificationDto,
} from "./dto/notification.dto";

// Mock类型定义
interface MockNotificationResponseDto {
  id: string;
  type: string;
  title: string;
  content: string;
  isRead: boolean;
  createdAt: Date;
}

describe("NotificationsController", () => {
  let controller: NotificationsController;
  let mockNotificationsService: jest.Mocked<NotificationsService>;

  // Mock数据
  const mockUser = { id: "user-1", username: "testuser", userNumber: "100001" };
  const mockAuthRequest = { user: mockUser } as AuthRequest;

  const mockNotifications: MockNotificationResponseDto[] = [
    {
      id: "notification-1",
      type: "system",
      title: "系统通知",
      content: "您的账户已激活",
      isRead: false,
      createdAt: new Date("2025-07-17T10:00:00Z"),
    },
    {
      id: "notification-2",
      type: "story",
      title: "故事更新",
      content: "您关注的故事有新更新",
      isRead: true,
      createdAt: new Date("2025-07-17T09:00:00Z"),
    },
  ];

  const mockPaginatedResult = {
    notifications: mockNotifications,
    total: 2,
  };

  beforeEach(async () => {
    const mockServiceMethods = {
      getUserNotifications: jest.fn(),
      getUnreadCount: jest.fn(),
      markAsRead: jest.fn(),
      markAllAsRead: jest.fn(),
      deleteNotification: jest.fn(),
      deleteNotifications: jest.fn(),
      cleanupOldNotifications: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [NotificationsController],
      providers: [
        {
          provide: NotificationsService,
          useValue: mockServiceMethods,
        },
      ],
    }).compile();

    controller = module.get<NotificationsController>(NotificationsController);
    mockNotificationsService = module.get(
      NotificationsService,
    ) as jest.Mocked<NotificationsService>;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("getNotifications", () => {
    it("应该返回用户通知列表", async () => {
      const query: NotificationQueryDto = {
        page: "1",
        limit: "20",
        unreadOnly: "false",
      };

      mockNotificationsService.getUserNotifications.mockResolvedValue(
        mockPaginatedResult,
      );

      const result = await controller.getNotifications(query, mockAuthRequest);

      expect(
        mockNotificationsService.getUserNotifications,
      ).toHaveBeenCalledWith("user-1", 1, 20, false);
      expect(result.code).toBe(200);
      expect(result.message).toBe("获取通知列表成功");
      expect(result.data.data).toEqual(mockNotifications);
      expect(result.data.total).toBe(2);
      expect(result.data.totalPages).toBe(1);
    });

    it("应该使用默认分页参数", async () => {
      const query: NotificationQueryDto = {};

      mockNotificationsService.getUserNotifications.mockResolvedValue(
        mockPaginatedResult,
      );

      const result = await controller.getNotifications(query, mockAuthRequest);

      expect(
        mockNotificationsService.getUserNotifications,
      ).toHaveBeenCalledWith("user-1", 1, 20, false);
      expect(result.data.page).toBe(1);
      expect(result.data.limit).toBe(20);
    });

    it("应该支持只获取未读通知", async () => {
      const query: NotificationQueryDto = { unreadOnly: "true" };
      const unreadNotifications = [mockNotifications[0]];

      mockNotificationsService.getUserNotifications.mockResolvedValue({
        notifications: unreadNotifications,
        total: 1,
      });

      const result = await controller.getNotifications(query, mockAuthRequest);

      expect(
        mockNotificationsService.getUserNotifications,
      ).toHaveBeenCalledWith("user-1", 1, 20, true);
      expect(result.data.data).toEqual(unreadNotifications);
      expect(result.data.total).toBe(1);
    });

    it("应该处理自定义分页参数", async () => {
      const query: NotificationQueryDto = { page: "2", limit: "10" };

      mockNotificationsService.getUserNotifications.mockResolvedValue({
        notifications: [],
        total: 25,
      });

      const result = await controller.getNotifications(query, mockAuthRequest);

      expect(
        mockNotificationsService.getUserNotifications,
      ).toHaveBeenCalledWith("user-1", 2, 10, false);
      expect(result.data.page).toBe(2);
      expect(result.data.limit).toBe(10);
      expect(result.data.total).toBe(25);
      expect(result.data.totalPages).toBe(3);
    });

    it("应该处理空通知列表", async () => {
      const query: NotificationQueryDto = {};

      mockNotificationsService.getUserNotifications.mockResolvedValue({
        notifications: [],
        total: 0,
      });

      const result = await controller.getNotifications(query, mockAuthRequest);

      expect(result.data.data).toEqual([]);
      expect(result.data.total).toBe(0);
      expect(result.data.totalPages).toBe(0);
    });
  });

  describe("getUnreadCount", () => {
    it("应该返回未读通知数量", async () => {
      mockNotificationsService.getUnreadCount.mockResolvedValue(5);

      const result = await controller.getUnreadCount(mockAuthRequest);

      expect(mockNotificationsService.getUnreadCount).toHaveBeenCalledWith(
        "user-1",
      );
      expect(result.code).toBe(200);
      expect(result.message).toBe("获取未读通知数量成功");
      expect(result.data.unreadCount).toBe(5);
    });

    it("应该处理零未读通知", async () => {
      mockNotificationsService.getUnreadCount.mockResolvedValue(0);

      const result = await controller.getUnreadCount(mockAuthRequest);

      expect(result.data.unreadCount).toBe(0);
    });

    it("应该处理获取未读数量失败", async () => {
      const error = new Error("数据库连接失败");
      mockNotificationsService.getUnreadCount.mockRejectedValue(error);

      await expect(controller.getUnreadCount(mockAuthRequest)).rejects.toThrow(
        error,
      );
    });
  });

  describe("markAsRead", () => {
    it("应该标记通知为已读", async () => {
      const notificationId = "notification-1";

      mockNotificationsService.markAsRead.mockResolvedValue(undefined);

      const result = await controller.markAsRead(
        notificationId,
        mockAuthRequest,
      );

      expect(mockNotificationsService.markAsRead).toHaveBeenCalledWith(
        notificationId,
        "user-1",
      );
      expect(result.code).toBe(200);
      expect(result.message).toBe("通知标记为已读成功");
    });

    it("应该处理标记已读失败", async () => {
      const notificationId = "notification-1";
      const error = new Error("通知不存在");

      mockNotificationsService.markAsRead.mockRejectedValue(error);

      await expect(
        controller.markAsRead(notificationId, mockAuthRequest),
      ).rejects.toThrow(error);
    });
  });

  describe("markAllAsRead", () => {
    it("应该标记所有通知为已读", async () => {
      mockNotificationsService.markAllAsRead.mockResolvedValue(undefined);

      const result = await controller.markAllAsRead(mockAuthRequest);

      expect(mockNotificationsService.markAllAsRead).toHaveBeenCalledWith(
        "user-1",
      );
      expect(result.code).toBe(200);
      expect(result.message).toBe("所有通知标记为已读成功");
    });

    it("应该处理标记所有已读失败", async () => {
      const error = new Error("操作失败");
      mockNotificationsService.markAllAsRead.mockRejectedValue(error);

      await expect(controller.markAllAsRead(mockAuthRequest)).rejects.toThrow(
        error,
      );
    });
  });

  describe("deleteNotification", () => {
    it("应该删除通知", async () => {
      const notificationId = "notification-1";

      mockNotificationsService.deleteNotification.mockResolvedValue(undefined);

      const result = await controller.deleteNotification(
        notificationId,
        mockAuthRequest,
      );

      expect(mockNotificationsService.deleteNotification).toHaveBeenCalledWith(
        notificationId,
        "user-1",
      );
      expect(result.code).toBe(200);
      expect(result.message).toBe("通知删除成功");
    });

    it("应该处理删除失败", async () => {
      const notificationId = "notification-1";
      const error = new Error("通知不存在或无权删除");

      mockNotificationsService.deleteNotification.mockRejectedValue(error);

      await expect(
        controller.deleteNotification(notificationId, mockAuthRequest),
      ).rejects.toThrow(error);
    });
  });

  describe("batchDeleteNotifications", () => {
    it("应该批量删除通知", async () => {
      const batchData: BatchNotificationDto = {
        notificationIds: ["notification-1", "notification-2"],
      };

      mockNotificationsService.deleteNotifications.mockResolvedValue(undefined);

      const result = await controller.batchDeleteNotifications(
        batchData,
        mockAuthRequest,
      );

      expect(mockNotificationsService.deleteNotifications).toHaveBeenCalledWith(
        ["notification-1", "notification-2"],
        "user-1",
      );
      expect(result.code).toBe(200);
      expect(result.message).toBe("成功删除 2 条通知");
    });

    it("应该处理单个通知批量删除", async () => {
      const batchData: BatchNotificationDto = {
        notificationIds: ["notification-1"],
      };

      mockNotificationsService.deleteNotifications.mockResolvedValue(undefined);

      const result = await controller.batchDeleteNotifications(
        batchData,
        mockAuthRequest,
      );

      expect(result.message).toBe("成功删除 1 条通知");
    });

    it("应该处理批量删除失败", async () => {
      const batchData: BatchNotificationDto = {
        notificationIds: ["notification-1", "notification-2"],
      };
      const error = new Error("批量删除失败");

      mockNotificationsService.deleteNotifications.mockRejectedValue(error);

      await expect(
        controller.batchDeleteNotifications(batchData, mockAuthRequest),
      ).rejects.toThrow(error);
    });

    it("应该处理空的批量删除请求", async () => {
      const batchData: BatchNotificationDto = {
        notificationIds: [],
      };

      mockNotificationsService.deleteNotifications.mockResolvedValue(undefined);

      const result = await controller.batchDeleteNotifications(
        batchData,
        mockAuthRequest,
      );

      expect(result.message).toBe("成功删除 0 条通知");
    });
  });

  describe("cleanupOldNotifications", () => {
    it("应该清理过期通知", async () => {
      mockNotificationsService.cleanupOldNotifications.mockResolvedValue(
        undefined,
      );

      const result = await controller.cleanupOldNotifications();

      expect(
        mockNotificationsService.cleanupOldNotifications,
      ).toHaveBeenCalledTimes(1);
      expect(result.code).toBe(200);
      expect(result.message).toBe("过期通知清理完成");
    });

    it("应该处理清理失败", async () => {
      const error = new Error("清理操作失败");
      mockNotificationsService.cleanupOldNotifications.mockRejectedValue(error);

      await expect(controller.cleanupOldNotifications()).rejects.toThrow(error);
    });
  });

  it("应该正确注入NotificationsService", () => {
    expect(controller).toBeDefined();
    expect(mockNotificationsService).toBeDefined();
  });
});
