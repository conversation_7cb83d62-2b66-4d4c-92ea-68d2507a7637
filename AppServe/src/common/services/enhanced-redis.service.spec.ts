/**
 * EnhancedRedisService 单元测试
 * 测试增强Redis服务的所有功能，包括缓存操作、分布式锁、计数器等
 */

import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { ConfigService } from "@nestjs/config";
import { CACHE_MANAGER } from "@nestjs/cache-manager";
import type { Cache } from "@nestjs/cache-manager";
import { EnhancedRedisService } from "./enhanced-redis.service";

describe("EnhancedRedisService", () => {
  let service: EnhancedRedisService;
  let mockCacheManager: jest.Mocked<Cache>;
  let mockConfigService: jest.Mocked<ConfigService>;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let mockRedisClient: any;

  beforeEach(async () => {
    // 创建Mock Redis客户端
    mockRedisClient = {
      ping: jest.fn().mockResolvedValue("PONG"),
      quit: jest.fn().mockResolvedValue("OK"),
      set: jest.fn().mockResolvedValue("OK"),
      setex: jest.fn().mockResolvedValue("OK"),
      get: jest.fn().mockResolvedValue("test-value"),
      del: jest.fn().mockResolvedValue(1),
      exists: jest.fn().mockResolvedValue(1),
      expire: jest.fn().mockResolvedValue(1),
      expireat: jest.fn().mockResolvedValue(1),
      incr: jest.fn().mockResolvedValue(1),
      decr: jest.fn().mockResolvedValue(0),
      flushdb: jest.fn().mockResolvedValue("OK"),
      eval: jest.fn().mockResolvedValue(1),
      pipeline: jest.fn().mockReturnValue({
        exec: jest.fn().mockResolvedValue([]),
      }),
    };

    // Mock CacheManager - 修复类型定义
    mockCacheManager = {
      get: jest.fn(),
      set: jest.fn(),
      del: jest.fn(),
      wrap: jest.fn(),
      // 添加缺失的顶层方法
      mget: jest.fn(),
      mset: jest.fn(),
      mdel: jest.fn(),
      ttl: jest.fn(),
      keys: jest.fn(),
      reset: jest.fn(),
      clear: jest.fn(),
      on: jest.fn(),
      off: jest.fn(),
      disconnect: jest.fn(),
      store: {
        name: "memory",
        isCacheable: jest.fn(),
        get: jest.fn(),
        set: jest.fn(),
        del: jest.fn(),
        reset: jest.fn(),
        mget: jest.fn(),
        mset: jest.fn(),
        mdel: jest.fn(),
        keys: jest.fn(),
        ttl: jest.fn(),
      },
    } as unknown as jest.Mocked<Cache>;

    // Mock ConfigService
    mockConfigService = {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      get: jest.fn((key: string, defaultValue?: any) => {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const config: Record<string, any> = {
          REDIS_HOST: "localhost",
          REDIS_PORT: 6379,
          REDIS_PASSWORD: undefined,
          REDIS_DB: 0,
        };
        return config[key] ?? defaultValue;
      }),
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EnhancedRedisService,
        {
          provide: CACHE_MANAGER,
          useValue: mockCacheManager,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<EnhancedRedisService>(EnhancedRedisService);

    // 替换内部Redis客户端为Mock
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (service as any).redis = mockRedisClient;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("服务初始化", () => {
    it("should be defined", () => {
      expect(service).toBeDefined();
    });

    it("should initialize Redis client with correct configuration", () => {
      expect(mockConfigService.get).toHaveBeenCalledWith(
        "REDIS_HOST",
        "localhost",
      );
      expect(mockConfigService.get).toHaveBeenCalledWith("REDIS_PORT", 6379);
      expect(mockConfigService.get).toHaveBeenCalledWith("REDIS_PASSWORD");
      expect(mockConfigService.get).toHaveBeenCalledWith("REDIS_DB", 0);
    });

    it("should handle onModuleInit successfully", async () => {
      const consoleSpy = jest.spyOn(console, "log").mockImplementation();

      await service.onModuleInit();

      expect(mockRedisClient.ping).toHaveBeenCalled();
      expect(consoleSpy).toHaveBeenCalledWith(
        "✅ Enhanced Redis Service 初始化成功",
      );

      consoleSpy.mockRestore();
    });

    it("should handle onModuleInit failure", async () => {
      const consoleSpy = jest.spyOn(console, "error").mockImplementation();
      mockRedisClient.ping.mockRejectedValueOnce(
        new Error("Connection failed"),
      );

      await service.onModuleInit();

      expect(consoleSpy).toHaveBeenCalledWith(
        "❌ Enhanced Redis Service 初始化失败:",
        expect.any(Error),
      );

      consoleSpy.mockRestore();
    });

    it("should handle onModuleDestroy", async () => {
      const consoleSpy = jest.spyOn(console, "log").mockImplementation();

      await service.onModuleDestroy();

      expect(mockRedisClient.quit).toHaveBeenCalled();
      expect(consoleSpy).toHaveBeenCalledWith(
        "🔒 Enhanced Redis Service 已关闭",
      );

      consoleSpy.mockRestore();
    });
  });

  describe("缓存操作", () => {
    describe("setCache", () => {
      it("should set cache with TTL", async () => {
        await service.setCache("test-key", "test-value", 5000);

        expect(mockCacheManager.set).toHaveBeenCalledWith(
          "test-key",
          "test-value",
          5000,
        );
      });

      it("should set cache without TTL", async () => {
        await service.setCache("test-key", "test-value");

        expect(mockCacheManager.set).toHaveBeenCalledWith(
          "test-key",
          "test-value",
        );
      });

      it("should handle cache set errors", async () => {
        const consoleSpy = jest.spyOn(console, "error").mockImplementation();
        const error = new Error("Cache set failed");
        mockCacheManager.set.mockRejectedValueOnce(error);

        await expect(
          service.setCache("test-key", "test-value"),
        ).rejects.toThrow(error);
        expect(consoleSpy).toHaveBeenCalledWith(
          "Failed to set cache test-key:",
          error,
        );

        consoleSpy.mockRestore();
      });
    });

    describe("getCache", () => {
      it("should get cache value", async () => {
        mockCacheManager.get.mockResolvedValueOnce("cached-value");

        const result = await service.getCache("test-key");

        expect(result).toBe("cached-value");
        expect(mockCacheManager.get).toHaveBeenCalledWith("test-key");
      });

      it("should return undefined for null cache value", async () => {
        mockCacheManager.get.mockResolvedValueOnce(null);

        const result = await service.getCache("test-key");

        expect(result).toBeUndefined();
      });

      it("should handle cache get errors", async () => {
        const consoleSpy = jest.spyOn(console, "error").mockImplementation();
        mockCacheManager.get.mockRejectedValueOnce(
          new Error("Cache get failed"),
        );

        const result = await service.getCache("test-key");

        expect(result).toBeUndefined();
        expect(consoleSpy).toHaveBeenCalledWith(
          "Failed to get cache test-key:",
          expect.any(Error),
        );

        consoleSpy.mockRestore();
      });
    });

    describe("delCache", () => {
      it("should delete cache", async () => {
        await service.delCache("test-key");

        expect(mockCacheManager.del).toHaveBeenCalledWith("test-key");
      });

      it("should handle cache delete errors", async () => {
        const consoleSpy = jest.spyOn(console, "error").mockImplementation();
        const error = new Error("Cache delete failed");
        mockCacheManager.del.mockRejectedValueOnce(error);

        await expect(service.delCache("test-key")).rejects.toThrow(error);
        expect(consoleSpy).toHaveBeenCalledWith(
          "Failed to delete cache test-key:",
          error,
        );

        consoleSpy.mockRestore();
      });
    });

    describe("clearCache", () => {
      it("should clear all cache", async () => {
        await service.clearCache();

        expect(mockRedisClient.flushdb).toHaveBeenCalled();
      });

      it("should handle clear cache errors", async () => {
        const consoleSpy = jest.spyOn(console, "error").mockImplementation();
        const error = new Error("Clear cache failed");
        mockRedisClient.flushdb.mockRejectedValueOnce(error);

        await expect(service.clearCache()).rejects.toThrow(error);
        expect(consoleSpy).toHaveBeenCalledWith(
          "Failed to clear cache:",
          error,
        );

        consoleSpy.mockRestore();
      });
    });
  });

  describe("分布式锁", () => {
    describe("acquireLock", () => {
      it("should acquire lock successfully", async () => {
        mockRedisClient.set.mockResolvedValueOnce("OK");

        const lockValue = await service.acquireLock("test-lock", 5000);

        expect(lockValue).toMatch(/^lock:\d+:\d+\.?\d*$/);
        expect(mockRedisClient.set).toHaveBeenCalledWith(
          "test-lock",
          lockValue,
          "PX",
          5000,
          "NX",
        );
      });

      it("should retry and eventually acquire lock", async () => {
        mockRedisClient.set
          .mockResolvedValueOnce(null)
          .mockResolvedValueOnce("OK");

        const lockValue = await service.acquireLock("test-lock", 5000, 2, 10);

        expect(lockValue).toMatch(/^lock:\d+:\d+\.?\d*$/);
        expect(mockRedisClient.set).toHaveBeenCalledTimes(2);
      });

      it("should return null when lock acquisition fails", async () => {
        mockRedisClient.set.mockResolvedValue(null);

        const lockValue = await service.acquireLock("test-lock", 5000, 2, 10);

        expect(lockValue).toBeNull();
        expect(mockRedisClient.set).toHaveBeenCalledTimes(2);
      });

      it("should handle lock acquisition errors", async () => {
        const consoleSpy = jest.spyOn(console, "error").mockImplementation();
        mockRedisClient.set.mockRejectedValue(new Error("Redis error"));

        const lockValue = await service.acquireLock("test-lock", 5000, 1);

        expect(lockValue).toBeNull();
        expect(consoleSpy).toHaveBeenCalledWith(
          "Failed to acquire lock test-lock:",
          expect.any(Error),
        );

        consoleSpy.mockRestore();
      });
    });

    describe("releaseLock", () => {
      it("should release lock successfully", async () => {
        mockRedisClient.eval.mockResolvedValueOnce(1);

        const result = await service.releaseLock("test-lock", "lock-value");

        expect(result).toBe(true);
        expect(mockRedisClient.eval).toHaveBeenCalledWith(
          expect.stringContaining("if redis.call"),
          1,
          "test-lock",
          "lock-value",
        );
      });

      it("should fail to release lock when value doesn't match", async () => {
        mockRedisClient.eval.mockResolvedValueOnce(0);

        const result = await service.releaseLock("test-lock", "wrong-value");

        expect(result).toBe(false);
      });

      it("should handle lock release errors", async () => {
        const consoleSpy = jest.spyOn(console, "error").mockImplementation();
        mockRedisClient.eval.mockRejectedValueOnce(new Error("Redis error"));

        const result = await service.releaseLock("test-lock", "lock-value");

        expect(result).toBe(false);
        expect(consoleSpy).toHaveBeenCalledWith(
          "Failed to release lock test-lock:",
          expect.any(Error),
        );

        consoleSpy.mockRestore();
      });
    });
  });

  describe("计数器操作", () => {
    describe("incr", () => {
      it("should increment counter", async () => {
        mockRedisClient.incr.mockResolvedValueOnce(5);

        const result = await service.incr("counter-key");

        expect(result).toBe(5);
        expect(mockRedisClient.incr).toHaveBeenCalledWith("counter-key");
      });

      it("should increment counter and set TTL for first time", async () => {
        mockRedisClient.incr.mockResolvedValueOnce(1);

        const result = await service.incr("counter-key", 60000);

        expect(result).toBe(1);
        expect(mockRedisClient.expire).toHaveBeenCalledWith("counter-key", 60);
      });

      it("should not set TTL for existing counter", async () => {
        mockRedisClient.incr.mockResolvedValueOnce(2);

        const result = await service.incr("counter-key", 60000);

        expect(result).toBe(2);
        expect(mockRedisClient.expire).not.toHaveBeenCalled();
      });

      it("should handle increment errors", async () => {
        const consoleSpy = jest.spyOn(console, "error").mockImplementation();
        mockRedisClient.incr.mockRejectedValueOnce(new Error("Redis error"));

        const result = await service.incr("counter-key");

        expect(result).toBe(0);
        expect(consoleSpy).toHaveBeenCalledWith(
          "Failed to increment counter-key:",
          expect.any(Error),
        );

        consoleSpy.mockRestore();
      });
    });

    describe("decr", () => {
      it("should decrement counter", async () => {
        mockRedisClient.decr.mockResolvedValueOnce(3);

        const result = await service.decr("counter-key");

        expect(result).toBe(3);
        expect(mockRedisClient.decr).toHaveBeenCalledWith("counter-key");
      });

      it("should handle decrement errors", async () => {
        const consoleSpy = jest.spyOn(console, "error").mockImplementation();
        mockRedisClient.decr.mockRejectedValueOnce(new Error("Redis error"));

        const result = await service.decr("counter-key");

        expect(result).toBe(0);
        expect(consoleSpy).toHaveBeenCalledWith(
          "Failed to decrement counter-key:",
          expect.any(Error),
        );

        consoleSpy.mockRestore();
      });
    });
  });

  describe("基础Redis操作", () => {
    describe("set", () => {
      it("should set key-value without TTL", async () => {
        const result = await service.set("test-key", "test-value");

        expect(result).toBe(true);
        expect(mockRedisClient.set).toHaveBeenCalledWith(
          "test-key",
          "test-value",
        );
      });

      it("should set key-value with TTL", async () => {
        const result = await service.set("test-key", "test-value", 5000);

        expect(result).toBe(true);
        expect(mockRedisClient.setex).toHaveBeenCalledWith(
          "test-key",
          5,
          "test-value",
        );
      });

      it("should handle set errors", async () => {
        const consoleSpy = jest.spyOn(console, "error").mockImplementation();
        mockRedisClient.set.mockRejectedValueOnce(new Error("Redis error"));

        const result = await service.set("test-key", "test-value");

        expect(result).toBe(false);
        expect(consoleSpy).toHaveBeenCalledWith(
          "Failed to set test-key:",
          expect.any(Error),
        );

        consoleSpy.mockRestore();
      });
    });

    describe("get", () => {
      it("should get value", async () => {
        const result = await service.get("test-key");

        expect(result).toBe("test-value");
        expect(mockRedisClient.get).toHaveBeenCalledWith("test-key");
      });

      it("should handle get errors", async () => {
        const consoleSpy = jest.spyOn(console, "error").mockImplementation();
        mockRedisClient.get.mockRejectedValueOnce(new Error("Redis error"));

        const result = await service.get("test-key");

        expect(result).toBeNull();
        expect(consoleSpy).toHaveBeenCalledWith(
          "Failed to get test-key:",
          expect.any(Error),
        );

        consoleSpy.mockRestore();
      });
    });

    describe("del", () => {
      it("should delete key successfully", async () => {
        const result = await service.del("test-key");

        expect(result).toBe(true);
        expect(mockRedisClient.del).toHaveBeenCalledWith("test-key");
      });

      it("should return false when key doesn't exist", async () => {
        mockRedisClient.del.mockResolvedValueOnce(0);

        const result = await service.del("test-key");

        expect(result).toBe(false);
      });

      it("should handle delete errors", async () => {
        const consoleSpy = jest.spyOn(console, "error").mockImplementation();
        mockRedisClient.del.mockRejectedValueOnce(new Error("Redis error"));

        const result = await service.del("test-key");

        expect(result).toBe(false);
        expect(consoleSpy).toHaveBeenCalledWith(
          "Failed to delete test-key:",
          expect.any(Error),
        );

        consoleSpy.mockRestore();
      });
    });

    describe("expire", () => {
      it("should set expiry successfully", async () => {
        const result = await service.expire("test-key", 5000);

        expect(result).toBe(true);
        expect(mockRedisClient.expire).toHaveBeenCalledWith("test-key", 5);
      });

      it("should return false when key doesn't exist", async () => {
        mockRedisClient.expire.mockResolvedValueOnce(0);

        const result = await service.expire("test-key", 5000);

        expect(result).toBe(false);
      });

      it("should handle expire errors", async () => {
        const consoleSpy = jest.spyOn(console, "error").mockImplementation();
        mockRedisClient.expire.mockRejectedValueOnce(new Error("Redis error"));

        const result = await service.expire("test-key", 5000);

        expect(result).toBe(false);
        expect(consoleSpy).toHaveBeenCalledWith(
          "Failed to set expiry for test-key:",
          expect.any(Error),
        );

        consoleSpy.mockRestore();
      });
    });

    describe("exists", () => {
      it("should return true when key exists", async () => {
        const result = await service.exists("test-key");

        expect(result).toBe(true);
        expect(mockRedisClient.exists).toHaveBeenCalledWith("test-key");
      });

      it("should return false when key doesn't exist", async () => {
        mockRedisClient.exists.mockResolvedValueOnce(0);

        const result = await service.exists("test-key");

        expect(result).toBe(false);
      });

      it("should handle exists errors", async () => {
        const consoleSpy = jest.spyOn(console, "error").mockImplementation();
        mockRedisClient.exists.mockRejectedValueOnce(new Error("Redis error"));

        const result = await service.exists("test-key");

        expect(result).toBe(false);
        expect(consoleSpy).toHaveBeenCalledWith(
          "Failed to check existence of test-key:",
          expect.any(Error),
        );

        consoleSpy.mockRestore();
      });
    });

    describe("expireAt", () => {
      it("should set expiry at timestamp successfully", async () => {
        const timestamp = Math.floor(Date.now() / 1000) + 3600;
        const result = await service.expireAt("test-key", timestamp);

        expect(result).toBe(true);
        expect(mockRedisClient.expireat).toHaveBeenCalledWith(
          "test-key",
          timestamp,
        );
      });

      it("should return false when key doesn't exist", async () => {
        mockRedisClient.expireat.mockResolvedValueOnce(0);

        const result = await service.expireAt("test-key", Date.now());

        expect(result).toBe(false);
      });

      it("should handle expireAt errors", async () => {
        const consoleSpy = jest.spyOn(console, "error").mockImplementation();
        mockRedisClient.expireat.mockRejectedValueOnce(
          new Error("Redis error"),
        );

        const result = await service.expireAt("test-key", Date.now());

        expect(result).toBe(false);
        expect(consoleSpy).toHaveBeenCalledWith(
          "Failed to set expiry at timestamp for test-key:",
          expect.any(Error),
        );

        consoleSpy.mockRestore();
      });
    });
  });

  describe("高级操作", () => {
    describe("pipeline", () => {
      it("should create pipeline", () => {
        const pipeline = service.pipeline();

        expect(pipeline).toBeDefined();
        expect(mockRedisClient.pipeline).toHaveBeenCalled();
      });
    });

    describe("getClient", () => {
      it("should return Redis client", () => {
        const client = service.getClient();

        expect(client).toBe(mockRedisClient);
      });
    });

    describe("getCacheManager", () => {
      it("should return cache manager", () => {
        const cacheManager = service.getCacheManager();

        expect(cacheManager).toBe(mockCacheManager);
      });
    });
  });

  describe("配置处理", () => {
    it("should handle localhost to IPv4 conversion", () => {
      // 测试配置处理逻辑已在构造函数中验证
      expect(mockConfigService.get).toHaveBeenCalledWith(
        "REDIS_HOST",
        "localhost",
      );
    });

    it("should use provided Redis configuration", () => {
      // 验证配置服务被正确调用
      expect(mockConfigService.get).toHaveBeenCalledWith("REDIS_PORT", 6379);
      expect(mockConfigService.get).toHaveBeenCalledWith("REDIS_PASSWORD");
      expect(mockConfigService.get).toHaveBeenCalledWith("REDIS_DB", 0);
    });
  });
});
