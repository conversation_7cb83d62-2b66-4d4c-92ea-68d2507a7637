import { NestFactory } from "@nestjs/core";
import { AppModule } from "../app.module";
import { DataSource } from "typeorm";
import { User } from "../modules/users/entities/user.entity";
import { Story } from "../modules/stories/entities/story.entity";
import { Character } from "../modules/characters/entities/character.entity";
// import { Lighting } from "../modules/lighting/entities/lighting.entity";
import { StoryChapter } from "../modules/stories/entities/story-chapter.entity";
import { StoryStatus } from "../modules/stories/entities/story.entity";
import {
  StoryShare,
  ShareType,
} from "../modules/stories/entities/story-share.entity";
import {
  Notification,
  NotificationType,
} from "../modules/users/entities/notification.entity";

async function testDatabase() {
  const app = await NestFactory.create(AppModule);
  const dataSource = app.get(DataSource);

  console.log("🔄 开始测试数据库连接...");

  try {
    // 测试数据库连接
    await dataSource.query("SELECT 1");
    console.log("✅ 数据库连接成功");

    // 获取存储库
    const userRepo = dataSource.getRepository(User);
    const storyRepo = dataSource.getRepository(Story);
    const chapterRepo = dataSource.getRepository(StoryChapter);
    const characterRepo = dataSource.getRepository(Character);
    // const lightingRepo = dataSource.getRepository(Lighting);
    const shareRepo = dataSource.getRepository(StoryShare);
    const notificationRepo = dataSource.getRepository(Notification);

    // 测试查询
    console.log("📊 测试数据统计:");

    const userCount = await userRepo.count();
    console.log(`👥 用户数量: ${userCount}`);

    const storyCount = await storyRepo.count();
    console.log(`📚 故事数量: ${storyCount}`);

    const characterCount = await characterRepo.count();
    console.log(`👤 人物数量: ${characterCount}`);

    // const lightingCount = await lightingRepo.count();
    // console.log(`💡 点亮记录: ${lightingCount}`);

    // 1. 测试创建用户
    console.log("\n📝 测试用户创建...");
    const testUser1 = userRepo.create({
      phone: "13800000001",
      nickname: "张三",
      username: "zhangsan",
      avatarUrl: "https://example.com/avatar1.jpg",
    });
    const testUser2 = userRepo.create({
      phone: "13800000002",
      nickname: "李四",
      username: "lisi",
      avatarUrl: "https://example.com/avatar2.jpg",
    });

    const [user1, user2] = await userRepo.save([testUser1, testUser2]);
    console.log(
      `✅ 用户创建成功: ${user1.nickname}(${user1.id}), ${user2.nickname}(${user2.id})`,
    );

    // 2. 测试创建故事
    console.log("\n📖 测试故事创建...");
    const testStory = storyRepo.create({
      userId: user1.id,
      title: "我的童年回忆",
      content: "这是一个关于我和弟弟童年时光的故事...",
      status: StoryStatus.PUBLISHED,
    });

    const story = await storyRepo.save(testStory);
    console.log(`✅ 故事创建成功: ${story.title}(${story.id})`);

    // 3. 测试创建故事章节
    console.log("\n📄 测试章节创建...");
    const testChapter = chapterRepo.create({
      storyId: story.id,
      title: "第一章：小时候的游戏",
      content: "那时候我们总是在院子里玩捉迷藏...",
      chapterOrder: 1,
      imageUrls: ["https://example.com/image1.jpg"],
    });

    const chapter = await chapterRepo.save(testChapter);
    console.log(`✅ 章节创建成功: ${chapter.title}(${chapter.id})`);

    // 4. 测试创建人物
    console.log("\n👤 测试人物创建...");
    const testCharacter = characterRepo.create({
      creatorId: user1.id,
      name: "弟弟",
      description: "我的亲弟弟，比我小3岁",
      relationship: "family",
      gender: "男",
    });

    const character = await characterRepo.save(testCharacter);
    console.log(`✅ 人物创建成功: ${character.name}(${character.id})`);

    // 5. 测试人物点亮
    console.log("\n💡 测试人物点亮...");
    // const testLighting = lightingRepo.create({
    //   characterId: character.id,
    //   storyId: story.id,
    //   lighterUserId: user2.id,
    //   creatorUserId: user1.id,
    //   status: "active",
    // });

    // const lighting = await lightingRepo.save(testLighting);
    // console.log(`✅ 人物点亮创建成功: ${lighting.id}`);

    // 6. 测试故事分享
    console.log("\n🔗 测试故事分享...");
    const testShare = shareRepo.create({
      storyId: story.id,
      sharerId: user1.id,
      targetCharacterId: character.id,
      shareType: ShareType.LINK,
      shareContent: `https://app.yourstory.com/shares/${story.id}`,
      shareToken: "abc123def456",
    });

    const share = await shareRepo.save(testShare);
    console.log(`✅ 故事分享创建成功: ${share.id}`);

    // 7. 测试通知
    console.log("\n🔔 测试通知创建...");
    const testNotification = notificationRepo.create({
      userId: user1.id,
      type: NotificationType.CHARACTER_LIGHTING,
      title: "有人点亮了你的人物",
      content: `${user2.nickname} 点亮了你的人物"${character.name}"`,
      relatedId: character.id,
      actionUrl: `/characters/${character.id}`,
    });

    const notification = await notificationRepo.save(testNotification);
    console.log(`✅ 通知创建成功: ${notification.title}(${notification.id})`);

    // 8. 测试关系查询
    console.log("\n🔍 测试关联查询...");

    // 查询用户的故事
    const userWithStories = await userRepo.findOne({
      where: { id: user1.id },
      relations: ["stories"],
    });
    console.log(`✅ 用户故事查询: ${userWithStories.stories.length} 个故事`);

    // 查询故事的章节
    const storyWithChapters = await storyRepo.findOne({
      where: { id: story.id },
      relations: ["chapters"],
    });
    console.log(`✅ 故事章节查询: ${storyWithChapters.chapters.length} 个章节`);

    // 查询人物的点亮记录
    const characterWithLightings = await characterRepo.findOne({
      where: { id: character.id },
      relations: ["lightings"],
    });
    console.log(
      `✅ 人物点亮查询: ${characterWithLightings.lightings.length} 个点亮记录`,
    );

    // 9. 测试索引性能（简单查询）
    console.log("\n⚡ 测试索引查询性能...");

    const start = Date.now();
    const stories = await storyRepo.find({
      where: { status: StoryStatus.PUBLISHED },
      order: { createdAt: "DESC" },
      take: 10,
    });
    const queryTime = Date.now() - start;
    console.log(
      `✅ 故事查询性能: ${queryTime}ms (查询到 ${stories.length} 条记录)`,
    );

    // 10. 清理测试数据
    console.log("\n🧹 清理测试数据...");
    await notificationRepo.remove(notification);
    await shareRepo.remove(share);
    // await lightingRepo.remove(lighting);
    await characterRepo.remove(character);
    await chapterRepo.remove(chapter);
    await storyRepo.remove(story);
    await userRepo.remove([user1, user2]);
    console.log("✅ 测试数据清理完成");

    console.log("\n🎉 数据库测试完成！所有功能正常");
  } catch (error) {
    console.error("❌ 数据库测试失败:", error);
    throw error;
  } finally {
    await dataSource.destroy();
    console.log("✅ 数据库连接已关闭");
  }
}

// 使用 import 替代 require
import("dotenv").then((dotenv) => {
  dotenv.config();
  testDatabase().catch(console.error);
});

export { testDatabase };
