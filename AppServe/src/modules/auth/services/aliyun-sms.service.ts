import { Injectable, Logger, BadRequestException } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import Dysmsapi20170525, * as $Dysmsapi20170525 from "@alicloud/dysmsapi20170525";
import * as $OpenApi from "@alicloud/openapi-client";
import * as $Util from "@alicloud/tea-util";

export interface AliyunSmsConfig {
  accessKeyId: string;
  accessKeySecret: string;
  signName: string;
  templateCode: string;
  endpoint?: string;
}

export interface SendSmsResult {
  success: boolean;
  message: string;
  bizId?: string;
  code?: string;
  requestId?: string;
}

/**
 * 阿里云短信服务
 *
 * 功能特性：
 * - 支持阿里云短信API集成
 * - 智能测试模式：VIP手机号发真实短信，其他号码Mock
 * - 完整的错误处理和日志记录
 * - 企业级配置管理
 */
@Injectable()
export class AliyunSmsService {
  private readonly logger = new Logger(AliyunSmsService.name);
  private client: Dysmsapi20170525 | null = null;
  private config: AliyunSmsConfig;

  // VIP测试手机号列表 - 这些号码将发送真实短信
  private readonly vipTestPhones = new Set([
    "16675158665", // 项目负责人手机号
    // 可以添加更多VIP测试号码
  ]);

  constructor(private readonly configService: ConfigService) {
    this.initializeConfig();
    this.initializeClient();
  }

  /**
   * 发送短信验证码（智能路由）
   */
  async sendVerificationCode(
    phone: string,
    code: string,
  ): Promise<SendSmsResult> {
    const startTime = Date.now();

    try {
      // 判断是否需要发送真实短信
      const shouldSendRealSms = this.shouldSendRealSms(phone);

      if (shouldSendRealSms) {
        // 发送真实短信
        const result = await this.sendRealSms(phone, code);
        this.logger.log(
          `📱 [真实短信] 发送到 ${phone}, 耗时: ${Date.now() - startTime}ms`,
        );
        return result;
      } else {
        // 发送Mock短信
        const result = this.sendMockSms(phone, code);
        this.logger.log(`🔧 [模拟短信] 发送到 ${phone}, 验证码: ${code}`);
        return result;
      }
    } catch (error) {
      this.logger.error(
        `短信发送失败: ${phone}`,
        error instanceof Error ? error.stack : String(error),
      );
      throw new BadRequestException(
        `短信发送失败: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  /**
   * 发送真实短信
   */
  private async sendRealSms(
    phone: string,
    code: string,
  ): Promise<SendSmsResult> {
    if (!this.client) {
      throw new BadRequestException("阿里云短信服务未正确配置");
    }

    const sendSmsRequest = new $Dysmsapi20170525.SendSmsRequest({
      phoneNumbers: phone,
      signName: this.config.signName,
      templateCode: this.config.templateCode,
      templateParam: JSON.stringify({ code }),
    });

    const runtime = new $Util.RuntimeOptions({});

    try {
      const response = await this.client.sendSmsWithOptions(
        sendSmsRequest,
        runtime,
      );

      if (response.body && response.body.code === "OK") {
        return {
          success: true,
          message: "短信发送成功",
          bizId: response.body.bizId,
          requestId: response.body.requestId,
        };
      } else {
        const body = response.body || {
          code: "Unknown" as string,
          message: "短信发送失败" as string,
          requestId: undefined as string | undefined,
        };
        this.logger.error(`阿里云短信发送失败: ${body.code} - ${body.message}`);
        return {
          success: false,
          message: body.message || "短信发送失败",
          requestId: (body as Record<string, unknown>).requestId as string,
        };
      }
    } catch (error: unknown) {
      this.logger.error("阿里云短信发送失败", {
        error,
        phone,
        code,
      });
      throw new BadRequestException("短信发送失败，请稍后重试");
    }
  }

  /**
   * 发送Mock短信
   */
  private sendMockSms(phone: string, code: string): SendSmsResult {
    // 模拟网络延迟（已删除未使用的mockDelay变量）

    return {
      success: true,
      message: "短信发送成功",
      code, // Mock模式返回验证码便于测试
      bizId: `mock_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    };
  }

  /**
   * 判断是否需要发送真实短信
   */
  private shouldSendRealSms(phone: string): boolean {
    const nodeEnv = this.configService.get("NODE_ENV");
    const smsMode = this.configService.get("SMS_MODE", "auto"); // auto | real | mock

    // 强制真实短信模式
    if (smsMode === "real") {
      return true;
    }

    // 强制Mock模式
    if (smsMode === "mock") {
      return false;
    }

    // 自动模式（默认）
    if (smsMode === "auto") {
      // 生产环境：全部真实短信
      if (nodeEnv === "production") {
        return true;
      }

      // 开发环境：全部Mock
      if (nodeEnv === "development") {
        return false;
      }

      // 测试环境：VIP手机号真实，其他Mock
      if (nodeEnv === "test") {
        return this.vipTestPhones.has(phone);
      }
    }

    // 默认返回Mock
    return false;
  }

  /**
   * 添加VIP测试手机号
   */
  addVipTestPhone(phone: string): void {
    this.vipTestPhones.add(phone);
    this.logger.log(`已添加VIP测试手机号: ${phone}`);
  }

  /**
   * 移除VIP测试手机号
   */
  removeVipTestPhone(phone: string): void {
    this.vipTestPhones.delete(phone);
    this.logger.log(`已移除VIP测试手机号: ${phone}`);
  }

  /**
   * 获取VIP测试手机号列表
   */
  getVipTestPhones(): string[] {
    return Array.from(this.vipTestPhones);
  }

  /**
   * 获取当前短信模式
   */
  getCurrentSmsMode(): {
    mode: string;
    environment: string;
    vipPhones: string[];
    clientStatus: string;
  } {
    return {
      mode: this.configService.get("SMS_MODE", "auto"),
      environment: this.configService.get("NODE_ENV", "development"),
      vipPhones: this.getVipTestPhones(),
      clientStatus: this.client ? "ready" : "not_configured",
    };
  }

  /**
   * 初始化配置
   */
  private initializeConfig(): void {
    this.config = {
      accessKeyId: this.configService.get<string>("SMS_ACCESS_KEY_ID", ""),
      accessKeySecret: this.configService.get<string>(
        "SMS_ACCESS_KEY_SECRET",
        "",
      ),
      signName: this.configService.get<string>("SMS_SIGN_NAME", "有故事APP"),
      templateCode: this.configService.get<string>("SMS_TEMPLATE_CODE", ""),
      endpoint: this.configService.get<string>(
        "SMS_ENDPOINT",
        "dysmsapi.aliyuncs.com",
      ),
    };

    // 配置验证
    if (!this.config.accessKeyId || !this.config.accessKeySecret) {
      this.logger.warn("⚠️ 阿里云短信配置不完整，将使用Mock模式");
    }
  }

  /**
   * 初始化阿里云客户端
   */
  private initializeClient(): void {
    if (!this.config.accessKeyId || !this.config.accessKeySecret) {
      this.logger.warn("阿里云短信配置不完整，无法初始化客户端");
      return;
    }

    try {
      const config = new $OpenApi.Config({
        accessKeyId: this.config.accessKeyId,
        accessKeySecret: this.config.accessKeySecret,
      });

      config.endpoint = this.config.endpoint;
      this.client = new Dysmsapi20170525(config);

      this.logger.log("✅ 阿里云短信客户端初始化成功");
    } catch (error) {
      this.logger.error("阿里云短信客户端初始化失败", error);
      this.client = null;
    }
  }

  /**
   * 测试阿里云短信服务连接
   */
  async testConnection(): Promise<{
    success: boolean;
    message: string;
    config: {
      hasAccessKeyId: boolean;
      hasAccessKeySecret: boolean;
      signName: string;
      templateCode: string;
      endpoint?: string;
    } | null;
  }> {
    if (!this.client) {
      return {
        success: false,
        message: "阿里云短信客户端未初始化",
        config: {
          hasAccessKeyId: !!this.config.accessKeyId,
          hasAccessKeySecret: !!this.config.accessKeySecret,
          signName: this.config.signName,
          templateCode: this.config.templateCode,
        },
      };
    }

    try {
      // 这里可以调用阿里云的查询接口来测试连接
      // 暂时返回配置状态
      return {
        success: true,
        message: "阿里云短信服务配置正常",
        config: {
          hasAccessKeyId: !!this.config.accessKeyId,
          hasAccessKeySecret: !!this.config.accessKeySecret,
          signName: this.config.signName,
          templateCode: this.config.templateCode,
          endpoint: this.config.endpoint,
        },
      };
    } catch (error) {
      return {
        success: false,
        message: `连接测试失败: ${error instanceof Error ? error.message : String(error)}`,
        config: null,
      };
    }
  }
}
