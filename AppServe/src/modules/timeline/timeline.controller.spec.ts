/* eslint-disable @typescript-eslint/no-explicit-any, @typescript-eslint/no-unused-vars */
import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { HttpStatus } from "@nestjs/common";

import { TimelineController } from "./timeline.controller";
import { TimelineService } from "./timeline.service";
import {
  EventType,
  EventStatus,
  EventVisibility,
  EventImportance,
} from "./entities/timeline-event.entity";
import type { CreateTimelineEventDto } from "./dto/create-timeline-event.dto";
import type { UpdateTimelineEventDto } from "./dto/timeline-response.dto";
import type { TimelineQueryDto } from "./dto/timeline-query.dto";

describe("TimelineController - 企业级单元测试", () => {
  let controller: TimelineController;
  let mockService: jest.Mocked<TimelineService>;

  // Mock数据 - 时间线事件
  const mockTimelineEvent = {
    id: "test-event-id",
    userId: "test-user-id",
    userName: "测试用户",
    title: "测试时间线事件",
    description: "测试事件描述",
    eventType: EventType.PERSONAL_MILESTONE,
    status: EventStatus.ACTIVE,
    visibility: EventVisibility.PRIVATE,
    eventDate: new Date("2025-01-01"),
    endDate: null,
    importance: EventImportance.MEDIUM,
    location: "测试地点",
    relatedPeople: ["张三", "李四"],
    tags: ["测试", "时间线"],
    likesCount: 5,
    commentsCount: 3,
    viewCount: 10,
    isMilestone: true,
    isPinned: false,
    color: "#FF5733",
    icon: "milestone",
    images: ["https://example.com/image.jpg"],
    attachments: [],
    relatedStoryId: "test-story-id",
    relatedStoryTitle: "关联故事",
    customData: {
      customField: "自定义数据",
    },
    metadata: {},
    createdAt: new Date(),
    updatedAt: new Date(),
    canEdit: true,
    canDelete: true,
    canComment: true,
  };

  // Mock数据 - 统计信息
  const mockStats = {
    userId: "test-user-id",
    totalEvents: 15,
    milestoneCount: 3,
    publicEvents: 8,
    privateEvents: 7,
    thisYearEvents: 10,
    thisMonthEvents: 2,
    totalViews: 150,
    totalLikes: 45,
    eventTypeStats: [
      { eventType: EventType.PERSONAL_MILESTONE, count: 3, percentage: 20 },
      { eventType: EventType.CAREER_ACHIEVEMENT, count: 5, percentage: 33.33 },
    ],
    importanceStats: [
      { importance: EventImportance.HIGH, count: 4, percentage: 26.67 },
      { importance: EventImportance.MEDIUM, count: 8, percentage: 53.33 },
      { importance: EventImportance.LOW, count: 3, percentage: 20 },
    ],
    topTags: [
      { tag: "工作", count: 8, recentUse: new Date() },
      { tag: "学习", count: 5, recentUse: new Date() },
    ],
    yearlyDistribution: [
      { year: 2025, count: 10 },
      { year: 2024, count: 5 },
    ],
    recentEvents: [
      { eventId: "event-1", title: "最近事件1", eventDate: new Date() },
    ],
    upcomingEvents: [
      { eventId: "event-2", title: "即将到来事件", eventDate: new Date() },
    ],
  };

  // Mock用户请求
  const mockRequest = {
    user: {
      id: "test-user-id",
      username: "testuser",
      role: "user",
    },
  };

  beforeEach(async () => {
    const mockServiceMethods = {
      createTimelineEvent: jest.fn(),
      getUserTimeline: jest.fn(),
      getTimelineEventById: jest.fn(),
      updateTimelineEvent: jest.fn(),
      deleteTimelineEvent: jest.fn(),
      getTimelineStats: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [TimelineController],
      providers: [
        {
          provide: TimelineService,
          useValue: mockServiceMethods,
        },
      ],
    }).compile();

    controller = module.get<TimelineController>(TimelineController);
    mockService = module.get(TimelineService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("POST /timeline/events - 创建时间线事件", () => {
    const createEventDto: CreateTimelineEventDto = {
      title: "测试时间线事件",
      description: "测试事件描述",
      eventType: EventType.PERSONAL_MILESTONE,
      eventDate: new Date("2025-01-01"),
      visibility: EventVisibility.PRIVATE,
      importance: EventImportance.MEDIUM,
      location: "测试地点",
      relatedPeople: ["张三", "李四"],
      tags: ["测试", "时间线"],
      isMilestone: true,
      color: "#FF5733",
      icon: "milestone",
      relatedStoryId: "test-story-id",
      customData: {
        category: "测试分类",
        outcome: "测试结果",
      },
      metadata: {},
    };

    it("应该成功创建时间线事件", async () => {
      // Arrange
      mockService.createTimelineEvent.mockResolvedValue(mockTimelineEvent);

      // Act
      const result = await controller.createTimelineEvent(
        mockRequest,
        createEventDto,
      );
      expect(result).toBeDefined();

      // Assert
      expect(result).toEqual({
        success: true,
        message: "时间线事件创建成功",
        data: mockTimelineEvent,
      });
      expect(mockService.createTimelineEvent).toHaveBeenCalledWith(
        createEventDto,
        mockRequest.user.id,
      );
    });

    it("应该处理创建事件时的服务异常", async () => {
      // Arrange
      mockService.createTimelineEvent.mockRejectedValue(new Error("创建失败"));

      // Act & Assert
      await expect(
        controller.createTimelineEvent(mockRequest, createEventDto),
      ).rejects.toThrow("创建失败");
      expect(mockService.createTimelineEvent).toHaveBeenCalledWith(
        createEventDto,
        mockRequest.user.id,
      );
    });

    it("应该验证必需的字段", async () => {
      // Arrange
      const incompleteDto = {
        title: "测试事件",
        // 缺少eventType和eventDate字段
      } as CreateTimelineEventDto;

      mockService.createTimelineEvent.mockResolvedValue(mockTimelineEvent);

      // Act
      const result = await controller.createTimelineEvent(
        mockRequest,
        incompleteDto,
      );

      // Assert
      expect(mockService.createTimelineEvent).toHaveBeenCalledWith(
        incompleteDto,
        mockRequest.user.id,
      );
    });

    it("应该正确传递用户ID", async () => {
      // Arrange
      const customUserId = "custom-user-id";
      const customRequest = {
        user: { id: customUserId, username: "customuser", role: "user" },
      };
      mockService.createTimelineEvent.mockResolvedValue(mockTimelineEvent);

      // Act
      await controller.createTimelineEvent(customRequest, createEventDto);

      // Assert
      expect(mockService.createTimelineEvent).toHaveBeenCalledWith(
        createEventDto,
        customUserId,
      );
    });

    it("应该处理里程碑事件创建", async () => {
      // Arrange
      const milestoneDto = {
        ...createEventDto,
        isMilestone: true,
        importance: EventImportance.HIGH,
      };
      const milestoneEvent = {
        ...mockTimelineEvent,
        isMilestone: true,
        importance: EventImportance.HIGH,
      };
      mockService.createTimelineEvent.mockResolvedValue(milestoneEvent);

      // Act
      const result = await controller.createTimelineEvent(
        mockRequest,
        milestoneDto,
      );

      // Assert
      expect(result.data.isMilestone).toBe(true);
      expect(result.data.importance).toBe(EventImportance.HIGH);
    });
  });

  describe("GET /timeline/users/:userId/events - 获取用户时间线", () => {
    const mockPaginatedResult = {
      data: [mockTimelineEvent],
      total: 1,
      page: 1,
      limit: 10,
      totalPages: 1,
    };

    it("应该成功获取用户时间线", async () => {
      // Arrange
      const userId = "test-user-id";
      const query: TimelineQueryDto = {
        page: 1,
        limit: 10,
        status: EventStatus.ACTIVE,
      };
      mockService.getUserTimeline.mockResolvedValue(mockPaginatedResult);

      // Act
      const result = await controller.getUserTimeline(
        userId,
        query,
        mockRequest,
      );

      // Assert
      expect(result).toEqual({
        success: true,
        message: "获取时间线成功",
        data: mockPaginatedResult.data,
        pagination: {
          total: 1,
          page: 1,
          limit: 10,
          totalPages: 1,
        },
      });
      expect(mockService.getUserTimeline).toHaveBeenCalledWith(
        userId,
        query,
        mockRequest.user.id,
      );
    });

    it("应该处理分页参数", async () => {
      // Arrange
      const userId = "test-user-id";
      const query: TimelineQueryDto = {
        page: 2,
        limit: 20,
      };
      const paginatedResult = {
        data: [mockTimelineEvent],
        total: 25,
        page: 2,
        limit: 20,
        totalPages: 2,
      };
      mockService.getUserTimeline.mockResolvedValue(paginatedResult);

      // Act
      const result = await controller.getUserTimeline(
        userId,
        query,
        mockRequest,
      );

      // Assert
      expect(result.pagination).toEqual({
        total: 25,
        page: 2,
        limit: 20,
        totalPages: 2,
      });
      expect(mockService.getUserTimeline).toHaveBeenCalledWith(
        userId,
        query,
        mockRequest.user.id,
      );
    });

    it("应该处理筛选参数", async () => {
      // Arrange
      const userId = "test-user-id";
      const query: TimelineQueryDto = {
        page: 1,
        limit: 10,
        eventType: EventType.CAREER_ACHIEVEMENT,
        importance: EventImportance.HIGH,
        tags: ["工作", "成就"],
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        isMilestone: true,
        search: "测试关键词",
        sortBy: "eventDate",
        sortOrder: "DESC",
      };
      mockService.getUserTimeline.mockResolvedValue(mockPaginatedResult);

      // Act
      const result = await controller.getUserTimeline(
        userId,
        query,
        mockRequest,
      );

      // Assert
      expect(mockService.getUserTimeline).toHaveBeenCalledWith(
        userId,
        query,
        mockRequest.user.id,
      );
      expect(result.success).toBe(true);
    });

    it("应该处理空结果", async () => {
      // Arrange
      const userId = "test-user-id";
      const query: TimelineQueryDto = { page: 1, limit: 10 };
      const emptyResult = {
        data: [],
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0,
      };
      mockService.getUserTimeline.mockResolvedValue(emptyResult);

      // Act
      const result = await controller.getUserTimeline(
        userId,
        query,
        mockRequest,
      );

      // Assert
      expect(result.data).toEqual([]);
      expect(result.pagination.total).toBe(0);
    });

    it("应该处理服务异常", async () => {
      // Arrange
      const userId = "test-user-id";
      const query: TimelineQueryDto = { page: 1, limit: 10 };
      mockService.getUserTimeline.mockRejectedValue(new Error("查询失败"));

      // Act & Assert
      await expect(
        controller.getUserTimeline(userId, query, mockRequest),
      ).rejects.toThrow("查询失败");
    });

    it("应该正确传递当前用户ID", async () => {
      // Arrange
      const targetUserId = "target-user-id";
      const currentUserId = "current-user-id";
      const query: TimelineQueryDto = { page: 1, limit: 10 };
      const customRequest = {
        user: { id: currentUserId, username: "currentuser", role: "user" },
      };
      mockService.getUserTimeline.mockResolvedValue(mockPaginatedResult);

      // Act
      await controller.getUserTimeline(targetUserId, query, customRequest);

      // Assert
      expect(mockService.getUserTimeline).toHaveBeenCalledWith(
        targetUserId,
        query,
        currentUserId,
      );
    });
  });

  describe("GET /timeline/events/:eventId - 获取事件详情", () => {
    it("应该成功获取事件详情", async () => {
      // Arrange
      const eventId = "test-event-id";
      mockService.getTimelineEventById.mockResolvedValue(mockTimelineEvent);

      // Act
      const result = await controller.getTimelineEventById(
        eventId,
        mockRequest,
      );

      // Assert
      expect(result).toEqual({
        success: true,
        message: "获取事件详情成功",
        data: mockTimelineEvent,
      });
      expect(mockService.getTimelineEventById).toHaveBeenCalledWith(
        eventId,
        mockRequest.user.id,
      );
    });

    it("应该处理事件不存在的情况", async () => {
      // Arrange
      const eventId = "non-existent-id";
      mockService.getTimelineEventById.mockRejectedValue(
        new Error("事件不存在"),
      );

      // Act & Assert
      await expect(
        controller.getTimelineEventById(eventId, mockRequest),
      ).rejects.toThrow("事件不存在");
      expect(mockService.getTimelineEventById).toHaveBeenCalledWith(
        eventId,
        mockRequest.user.id,
      );
    });

    it("应该正确传递用户ID以进行权限检查", async () => {
      // Arrange
      const eventId = "test-event-id";
      const customUserId = "custom-user-id";
      const customRequest = {
        user: { id: customUserId, username: "customuser", role: "user" },
      };
      mockService.getTimelineEventById.mockResolvedValue(mockTimelineEvent);

      // Act
      await controller.getTimelineEventById(eventId, customRequest);

      // Assert
      expect(mockService.getTimelineEventById).toHaveBeenCalledWith(
        eventId,
        customUserId,
      );
    });

    it("应该包含用户相关状态信息", async () => {
      // Arrange
      const eventId = "test-event-id";
      const eventWithUserState = {
        ...mockTimelineEvent,
        canEdit: true,
        canDelete: false,
      };
      mockService.getTimelineEventById.mockResolvedValue(eventWithUserState);

      // Act
      const result = await controller.getTimelineEventById(
        eventId,
        mockRequest,
      );

      // Assert
      expect(result.data.canEdit).toBe(true);
      expect(result.data.canDelete).toBe(false);
    });
  });

  describe("PUT /timeline/events/:eventId - 更新时间线事件", () => {
    const updateEventDto: UpdateTimelineEventDto = {
      title: "更新后的标题",
      description: "更新后的描述",
      importance: EventImportance.HIGH,
      tags: ["更新", "测试"],
      isPinned: true,
    };

    it("应该成功更新时间线事件", async () => {
      // Arrange
      const eventId = "test-event-id";
      const updatedEvent = {
        ...mockTimelineEvent,
        ...updateEventDto,
      };
      mockService.updateTimelineEvent.mockResolvedValue(updatedEvent);

      // Act
      const result = await controller.updateTimelineEvent(
        eventId,
        updateEventDto,
        mockRequest,
      );

      // Assert
      expect(result).toEqual({
        success: true,
        message: "时间线事件更新成功",
        data: updatedEvent,
      });
      expect(mockService.updateTimelineEvent).toHaveBeenCalledWith(
        eventId,
        updateEventDto,
        mockRequest.user.id,
      );
    });

    it("应该处理更新事件时的服务异常", async () => {
      // Arrange
      const eventId = "test-event-id";
      mockService.updateTimelineEvent.mockRejectedValue(new Error("更新失败"));

      // Act & Assert
      await expect(
        controller.updateTimelineEvent(eventId, updateEventDto, mockRequest),
      ).rejects.toThrow("更新失败");
      expect(mockService.updateTimelineEvent).toHaveBeenCalledWith(
        eventId,
        updateEventDto,
        mockRequest.user.id,
      );
    });

    it("应该支持部分更新", async () => {
      // Arrange
      const eventId = "test-event-id";
      const partialUpdateDto = { title: "仅更新标题" };
      const partiallyUpdatedEvent = {
        ...mockTimelineEvent,
        title: "仅更新标题",
      };
      mockService.updateTimelineEvent.mockResolvedValue(partiallyUpdatedEvent);

      // Act
      const result = await controller.updateTimelineEvent(
        eventId,
        partialUpdateDto,
        mockRequest,
      );

      // Assert
      expect(result.data.title).toBe("仅更新标题");
      expect(mockService.updateTimelineEvent).toHaveBeenCalledWith(
        eventId,
        partialUpdateDto,
        mockRequest.user.id,
      );
    });

    it("应该正确传递用户ID进行权限验证", async () => {
      // Arrange
      const eventId = "test-event-id";
      const customUserId = "custom-user-id";
      const customRequest = {
        user: { id: customUserId, username: "customuser", role: "user" },
      };
      mockService.updateTimelineEvent.mockResolvedValue(mockTimelineEvent);

      // Act
      await controller.updateTimelineEvent(
        eventId,
        updateEventDto,
        customRequest,
      );

      // Assert
      expect(mockService.updateTimelineEvent).toHaveBeenCalledWith(
        eventId,
        updateEventDto,
        customUserId,
      );
    });
  });

  describe("DELETE /timeline/events/:eventId - 删除时间线事件", () => {
    it("应该成功删除时间线事件", async () => {
      // Arrange
      const eventId = "test-event-id";
      mockService.deleteTimelineEvent.mockResolvedValue(undefined);

      // Act
      const result = await controller.deleteTimelineEvent(eventId, mockRequest);

      // Assert
      expect(result).toEqual({
        success: true,
        message: "时间线事件删除成功",
      });
      expect(mockService.deleteTimelineEvent).toHaveBeenCalledWith(
        eventId,
        mockRequest.user.id,
      );
    });

    it("应该处理删除事件时的服务异常", async () => {
      // Arrange
      const eventId = "test-event-id";
      mockService.deleteTimelineEvent.mockRejectedValue(new Error("删除失败"));

      // Act & Assert
      await expect(
        controller.deleteTimelineEvent(eventId, mockRequest),
      ).rejects.toThrow("删除失败");
      expect(mockService.deleteTimelineEvent).toHaveBeenCalledWith(
        eventId,
        mockRequest.user.id,
      );
    });

    it("应该正确传递用户ID进行权限验证", async () => {
      // Arrange
      const eventId = "test-event-id";
      const customUserId = "custom-user-id";
      const customRequest = {
        user: { id: customUserId, username: "customuser", role: "user" },
      };
      mockService.deleteTimelineEvent.mockResolvedValue(undefined);

      // Act
      await controller.deleteTimelineEvent(eventId, customRequest);

      // Assert
      expect(mockService.deleteTimelineEvent).toHaveBeenCalledWith(
        eventId,
        customUserId,
      );
    });
  });

  describe("GET /timeline/users/:userId/stats - 获取时间线统计", () => {
    it("应该成功获取统计信息", async () => {
      // Arrange
      const userId = "test-user-id";
      mockService.getTimelineStats.mockResolvedValue(mockStats);

      // Act
      const result = await controller.getTimelineStats(userId);

      // Assert
      expect(result).toEqual({
        success: true,
        message: "获取时间线统计成功",
        data: mockStats,
      });
      expect(mockService.getTimelineStats).toHaveBeenCalledWith(userId);
    });

    it("应该包含完整的统计数据结构", async () => {
      // Arrange
      const userId = "test-user-id";
      const detailedStats = {
        ...mockStats,
        eventTypeStats: [
          { eventType: EventType.PERSONAL_MILESTONE, count: 3, percentage: 20 },
          {
            eventType: EventType.CAREER_ACHIEVEMENT,
            count: 5,
            percentage: 33.33,
          },
          { eventType: EventType.EDUCATION, count: 2, percentage: 13.33 },
          { eventType: EventType.TRAVEL, count: 3, percentage: 20 },
          { eventType: EventType.HEALTH, count: 2, percentage: 13.33 },
        ],
        importanceStats: [
          { importance: EventImportance.HIGH, count: 4, percentage: 26.67 },
          { importance: EventImportance.MEDIUM, count: 8, percentage: 53.33 },
          { importance: EventImportance.LOW, count: 3, percentage: 20 },
        ],
        topTags: [
          { tag: "工作", count: 8, recentUse: new Date() },
          { tag: "学习", count: 5, recentUse: new Date() },
          { tag: "旅行", count: 4, recentUse: new Date() },
        ],
      };
      mockService.getTimelineStats.mockResolvedValue(detailedStats);

      // Act
      const result = await controller.getTimelineStats(userId);

      // Assert
      expect(result.data.eventTypeStats).toHaveLength(5);
      expect(result.data.importanceStats).toHaveLength(3);
      expect(result.data.topTags).toHaveLength(3);
      expect(result.data.totalEvents).toBe(mockStats.totalEvents);
      expect(result.data.milestoneCount).toBe(mockStats.milestoneCount);
    });

    it("应该处理统计数据为零的情况", async () => {
      // Arrange
      const userId = "test-user-id";
      const emptyStats = {
        ...mockStats,
        totalEvents: 0,
        milestoneCount: 0,
        publicEvents: 0,
        privateEvents: 0,
        eventTypeStats: [],
        importanceStats: [],
        topTags: [],
        yearlyDistribution: [],
        recentEvents: [],
        upcomingEvents: [],
      };
      mockService.getTimelineStats.mockResolvedValue(emptyStats);

      // Act
      const result = await controller.getTimelineStats(userId);

      // Assert
      expect(result.data.totalEvents).toBe(0);
      expect(result.data.milestoneCount).toBe(0);
      expect(result.data.eventTypeStats).toHaveLength(0);
      expect(result.data.importanceStats).toHaveLength(0);
      expect(result.data.topTags).toHaveLength(0);
    });

    it("应该包含年度分布统计", async () => {
      // Arrange
      const userId = "test-user-id";
      const statsWithYearlyData = {
        ...mockStats,
        yearlyDistribution: [
          { year: 2025, count: 12 },
          { year: 2024, count: 8 },
          { year: 2023, count: 5 },
        ],
      };
      mockService.getTimelineStats.mockResolvedValue(statsWithYearlyData);

      // Act
      const result = await controller.getTimelineStats(userId);

      // Assert
      expect(result.data.yearlyDistribution).toHaveLength(3);
      expect(result.data.yearlyDistribution[0].year).toBe(2025);
      expect(result.data.yearlyDistribution[0].count).toBe(12);
    });

    it("应该包含最近和即将到来的事件", async () => {
      // Arrange
      const userId = "test-user-id";
      const statsWithRecentEvents = {
        ...mockStats,
        recentEvents: [
          { eventId: "recent-1", title: "最近事件1", eventDate: new Date() },
          { eventId: "recent-2", title: "最近事件2", eventDate: new Date() },
        ],
        upcomingEvents: [
          { eventId: "upcoming-1", title: "即将到来1", eventDate: new Date() },
        ],
      };
      mockService.getTimelineStats.mockResolvedValue(statsWithRecentEvents);

      // Act
      const result = await controller.getTimelineStats(userId);

      // Assert
      expect(result.data.recentEvents).toHaveLength(2);
      expect(result.data.upcomingEvents).toHaveLength(1);
      expect(result.data.recentEvents[0].title).toBe("最近事件1");
      expect(result.data.upcomingEvents[0].title).toBe("即将到来1");
    });

    it("应该处理服务异常", async () => {
      // Arrange
      const userId = "test-user-id";
      mockService.getTimelineStats.mockRejectedValue(new Error("获取统计失败"));

      // Act & Assert
      await expect(controller.getTimelineStats(userId)).rejects.toThrow(
        "获取统计失败",
      );
      expect(mockService.getTimelineStats).toHaveBeenCalledWith(userId);
    });
  });

  describe("错误处理和边界条件", () => {
    it("应该处理无效的用户ID", async () => {
      // Arrange
      const invalidRequest = {
        user: { id: "", username: "", role: "user" },
      };
      mockService.createTimelineEvent.mockRejectedValue(new Error("无效用户"));

      // Act & Assert
      await expect(
        controller.createTimelineEvent(
          invalidRequest,
          {} as CreateTimelineEventDto,
        ),
      ).rejects.toThrow("无效用户");
    });

    it("应该处理无效的事件ID", async () => {
      // Arrange
      const invalidEventId = "invalid-id";
      mockService.getTimelineEventById.mockRejectedValue(
        new Error("无效的事件ID"),
      );

      // Act & Assert
      await expect(
        controller.getTimelineEventById(invalidEventId, mockRequest),
      ).rejects.toThrow("无效的事件ID");
    });

    it("应该处理网络或数据库异常", async () => {
      // Arrange
      const userId = "test-user-id";
      const query: TimelineQueryDto = { page: 1, limit: 10 };
      mockService.getUserTimeline.mockRejectedValue(
        new Error("数据库连接失败"),
      );

      // Act & Assert
      await expect(
        controller.getUserTimeline(userId, query, mockRequest),
      ).rejects.toThrow("数据库连接失败");
    });

    it("应该处理权限不足的情况", async () => {
      // Arrange
      const eventId = "protected-event-id";
      mockService.getTimelineEventById.mockRejectedValue(new Error("权限不足"));

      // Act & Assert
      await expect(
        controller.getTimelineEventById(eventId, mockRequest),
      ).rejects.toThrow("权限不足");
    });
  });

  describe("响应格式验证", () => {
    it("创建事件的响应应该符合ApiResponse格式", async () => {
      // Arrange
      mockService.createTimelineEvent.mockResolvedValue(mockTimelineEvent);

      // Act
      const result = await controller.createTimelineEvent(mockRequest, {
        title: "测试事件",
        eventType: EventType.PERSONAL_MILESTONE,
        eventDate: new Date(),
      } as CreateTimelineEventDto);

      // Assert
      expect(result).toHaveProperty("success", true);
      expect(result).toHaveProperty("message", "时间线事件创建成功");
      expect(result).toHaveProperty("data");
      expect(result.data).toEqual(mockTimelineEvent);
    });

    it("获取时间线的响应应该包含分页信息", async () => {
      // Arrange
      const userId = "test-user-id";
      const query: TimelineQueryDto = { page: 2, limit: 15 };
      const paginatedResult = {
        data: [mockTimelineEvent],
        total: 30,
        page: 2,
        limit: 15,
        totalPages: 2,
      };
      mockService.getUserTimeline.mockResolvedValue(paginatedResult);

      // Act
      const result = await controller.getUserTimeline(
        userId,
        query,
        mockRequest,
      );

      // Assert
      expect(result).toHaveProperty("success", true);
      expect(result).toHaveProperty("message", "获取时间线成功");
      expect(result).toHaveProperty("data");
      expect(result).toHaveProperty("pagination");
      expect(result.pagination).toEqual({
        total: 30,
        page: 2,
        limit: 15,
        totalPages: 2,
      });
    });

    it("统计信息的响应应该包含完整的数据结构", async () => {
      // Arrange
      const userId = "test-user-id";
      mockService.getTimelineStats.mockResolvedValue(mockStats);

      // Act
      const result = await controller.getTimelineStats(userId);

      // Assert
      expect(result).toHaveProperty("success", true);
      expect(result).toHaveProperty("message", "获取时间线统计成功");
      expect(result).toHaveProperty("data");
      expect(result.data).toHaveProperty("userId");
      expect(result.data).toHaveProperty("totalEvents");
      expect(result.data).toHaveProperty("milestoneCount");
      expect(result.data).toHaveProperty("eventTypeStats");
      expect(result.data).toHaveProperty("importanceStats");
      expect(result.data).toHaveProperty("topTags");
      expect(result.data).toHaveProperty("yearlyDistribution");
    });

    it("删除事件的响应应该不包含data字段", async () => {
      // Arrange
      const eventId = "test-event-id";
      mockService.deleteTimelineEvent.mockResolvedValue(undefined);

      // Act
      const result = await controller.deleteTimelineEvent(eventId, mockRequest);

      // Assert
      expect(result).toHaveProperty("success", true);
      expect(result).toHaveProperty("message", "时间线事件删除成功");
      expect(result).not.toHaveProperty("data");
    });
  });

  describe("业务逻辑验证", () => {
    it("应该验证创建事件时的业务逻辑", async () => {
      // Arrange
      const businessDto: CreateTimelineEventDto = {
        title: "重要业务事件",
        eventType: EventType.CAREER_ACHIEVEMENT,
        eventDate: new Date("2025-03-01"),
        importance: EventImportance.HIGH,
        isMilestone: true,
        visibility: EventVisibility.PUBLIC,
        tags: ["重要", "公开"],
      };
      mockService.createTimelineEvent.mockResolvedValue({
        ...mockTimelineEvent,
        importance: EventImportance.HIGH,
        visibility: EventVisibility.PUBLIC,
        isMilestone: true,
      });

      // Act
      const result = await controller.createTimelineEvent(
        mockRequest,
        businessDto,
      );

      // Assert
      expect(result.data.importance).toBe(EventImportance.HIGH);
      expect(result.data.visibility).toBe(EventVisibility.PUBLIC);
      expect(result.data.isMilestone).toBe(true);
      expect(mockService.createTimelineEvent).toHaveBeenCalledWith(
        businessDto,
        mockRequest.user.id,
      );
    });

    it("应该验证更新事件时的业务逻辑", async () => {
      // Arrange
      const eventId = "test-event-id";
      const businessDto: UpdateTimelineEventDto = {
        importance: EventImportance.HIGH,
        isPinned: true,
        tags: ["更新", "置顶"],
      };
      mockService.updateTimelineEvent.mockResolvedValue({
        ...mockTimelineEvent,
        importance: EventImportance.HIGH,
        isPinned: true,
        tags: ["更新", "置顶"],
      });

      // Act
      const result = await controller.updateTimelineEvent(
        eventId,
        businessDto,
        mockRequest,
      );

      // Assert
      expect(result.data.importance).toBe(EventImportance.HIGH);
      expect(result.data.isPinned).toBe(true);
      expect(result.data.tags).toContain("更新");
      expect(result.data.tags).toContain("置顶");
    });

    it("应该正确处理查询参数的业务逻辑", async () => {
      // Arrange
      const userId = "test-user-id";
      const businessQuery: TimelineQueryDto = {
        page: 1,
        limit: 5,
        eventType: EventType.PERSONAL_MILESTONE,
        importance: EventImportance.HIGH,
        isMilestone: true,
        tags: ["重要"],
        sortBy: "eventDate",
        sortOrder: "DESC",
      };
      mockService.getUserTimeline.mockResolvedValue({
        data: [mockTimelineEvent],
        total: 1,
        page: 1,
        limit: 5,
        totalPages: 1,
      });

      // Act
      await controller.getUserTimeline(userId, businessQuery, mockRequest);

      // Assert
      expect(mockService.getUserTimeline).toHaveBeenCalledWith(
        userId,
        businessQuery,
        mockRequest.user.id,
      );
    });

    it("应该处理事件类型的业务逻辑", async () => {
      // Arrange
      const eventTypes = [
        EventType.PERSONAL_MILESTONE,
        EventType.CAREER_ACHIEVEMENT,
        EventType.EDUCATION,
        EventType.TRAVEL,
        EventType.HEALTH,
      ];

      // Act & Assert
      for (const eventType of eventTypes) {
        const dto = {
          title: `${eventType}事件`,
          eventType,
          eventDate: new Date(),
        } as CreateTimelineEventDto;

        mockService.createTimelineEvent.mockResolvedValue({
          ...mockTimelineEvent,
          eventType,
        });

        const result = await controller.createTimelineEvent(mockRequest, dto);
        expect(result.data.eventType).toBe(eventType);
      }
    });

    it("应该处理重要性等级的业务逻辑", async () => {
      // Arrange
      const importanceLevels = [
        EventImportance.LOW,
        EventImportance.MEDIUM,
        EventImportance.HIGH,
        EventImportance.CRITICAL,
      ];

      // Act & Assert
      for (const importance of importanceLevels) {
        const dto = {
          title: "重要性测试事件",
          eventType: EventType.PERSONAL_MILESTONE,
          eventDate: new Date(),
          importance,
        } as CreateTimelineEventDto;

        mockService.createTimelineEvent.mockResolvedValue({
          ...mockTimelineEvent,
          importance,
        });

        const result = await controller.createTimelineEvent(mockRequest, dto);
        expect(result.data.importance).toBe(importance);
      }
    });
  });
});
