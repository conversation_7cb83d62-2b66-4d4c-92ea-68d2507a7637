import type { ShareType, ShareStatus } from "../entities/share.entity";

/**
 * 分享响应DTO
 */
export class ShareResponseDto {
  id: string;
  token: string;
  shareUrl: string;
  storyId: string;
  userId: string;
  shareType: ShareType;
  status: ShareStatus;
  hasPassword: boolean;
  accessLimit: number;
  accessCount: number;
  expiresAt?: Date;
  description?: string;
  storySnapshot: {
    title: string;
    summary?: string;
    coverImage?: string;
    authorName: string;
  };
  metadata?: Record<string, unknown>;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 分享内容响应DTO
 */
export class ShareContentResponseDto {
  share: {
    id: string;
    token: string;
    shareType: ShareType;
    description?: string;
    accessCount: number;
    sharerName: string;
    createdAt: Date;
  };
  story: {
    id: string;
    title: string;
    summary?: string;
    content?: string;
    coverImage?: string;
    authorId: string;
    authorName: string;
    createdAt: Date;
    isAccessible: boolean;
  };
  isOwner: boolean;
}

/**
 * 分享统计响应DTO
 */
export class ShareStatsResponseDto {
  totalShares: number;
  activeShares: number;
  totalAccessCount: number;
  uniqueVisitors: number;
  accessCountToday: number;
  accessCountThisWeek: number;
  accessCountThisMonth: number;
  topShares: Array<{
    shareId: string;
    storyTitle: string;
    accessCount: number;
    createdAt: Date;
  }>;
}
