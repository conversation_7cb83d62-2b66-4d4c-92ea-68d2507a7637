/* eslint-disable @typescript-eslint/no-explicit-any, @typescript-eslint/no-unused-vars */
import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { HttpStatus } from "@nestjs/common";
import type { Request } from "express";

import { CommentsController } from "./comments.controller";
import { CommentsService } from "./comments.service";
import type {
  CreateCommentDto,
  UpdateCommentDto,
  CommentQueryDto,
  CommentResponseDto,
} from "./dto";
import { CommentType, CommentSortBy } from "./entities/comment.entity";

describe("CommentsController - 企业级单元测试", () => {
  let controller: CommentsController;
  let mockCommentsService: jest.Mocked<CommentsService>;

  // 测试数据工厂
  const mockUser = { id: "user-001", nickname: "测试用户" };
  const mockRequest = { user: mockUser } as Request & { user: { id: string } };

  const mockCommentResponse: CommentResponseDto = {
    id: "comment-001",
    storyId: "story-001",
    userId: "user-001",
    parentId: null,
    content: "这是一条测试评论",
    type: CommentType.TEXT,
    status: "published",
    likes: 5,
    replies: 2,
    level: 0,
    user: {
      id: "user-001",
      nickname: "测试用户",
      avatar: null,
    },
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockPaginatedResponse = {
    data: [mockCommentResponse],
    total: 1,
    page: 1,
    limit: 10,
    totalPages: 1,
    hasNext: false,
    hasPrev: false,
  };

  beforeEach(async () => {
    const mockService = {
      createComment: jest.fn(),
      getComments: jest.fn(),
      getCommentById: jest.fn(),
      updateComment: jest.fn(),
      deleteComment: jest.fn(),
      toggleCommentLike: jest.fn(),
      getCommentReplies: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [CommentsController],
      providers: [
        {
          provide: CommentsService,
          useValue: mockService,
        },
      ],
    }).compile();

    controller = module.get<CommentsController>(CommentsController);
    mockCommentsService = module.get(CommentsService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("createComment - POST /comments", () => {
    const createCommentDto: CreateCommentDto = {
      storyId: "story-001",
      content: "这是一条新评论",
      type: CommentType.TEXT,
    };

    it("应该成功创建评论", async () => {
      // Arrange
      mockCommentsService.createComment.mockResolvedValue(mockCommentResponse);

      // Act
      const result = await controller.createComment(
        createCommentDto,
        mockRequest,
      );

      // Assert
      expect(mockCommentsService.createComment).toHaveBeenCalledWith(
        createCommentDto,
        "user-001",
      );
      expect(result).toEqual({
        success: true,
        message: "评论发表成功",
        data: mockCommentResponse,
        statusCode: HttpStatus.CREATED,
        timestamp: expect.any(String),
      });
    });

    it("应该正确创建回复评论", async () => {
      // Arrange
      const replyDto = { ...createCommentDto, parentId: "parent-comment-001" };
      const replyResponse = {
        ...mockCommentResponse,
        parentId: "parent-comment-001",
        level: 1,
      };
      mockCommentsService.createComment.mockResolvedValue(replyResponse);

      // Act
      const result = await controller.createComment(replyDto, mockRequest);

      // Assert
      expect(mockCommentsService.createComment).toHaveBeenCalledWith(
        replyDto,
        "user-001",
      );
      expect(result.data.parentId).toBe("parent-comment-001");
    });

    it("应该正确处理服务层错误", async () => {
      // Arrange
      mockCommentsService.createComment.mockRejectedValue(
        new Error("故事不存在"),
      );

      // Act & Assert
      await expect(
        controller.createComment(createCommentDto, mockRequest),
      ).rejects.toThrow("故事不存在");
    });
  });

  describe("getStoryComments - GET /comments", () => {
    const queryDto: CommentQueryDto = {
      page: 1,
      limit: 10,
      sortBy: CommentSortBy.CREATED_DESC,
    };

    it("应该成功获取故事评论列表", async () => {
      // Arrange
      mockCommentsService.getComments.mockResolvedValue(mockPaginatedResponse);

      // Act
      const result = await controller.getStoryComments("story-001", queryDto);

      // Assert
      expect(mockCommentsService.getComments).toHaveBeenCalledWith(
        "story-001",
        queryDto,
      );
      expect(result).toEqual({
        success: true,
        message: "获取评论列表成功",
        data: mockPaginatedResponse,
        statusCode: HttpStatus.OK,
        timestamp: expect.any(String),
      });
    });

    it("应该正确传递查询参数", async () => {
      // Arrange
      const customQuery = {
        page: 2,
        limit: 5,
        sortBy: CommentSortBy.LIKES_DESC,
        status: "published",
        userId: "user-001",
      } as CommentQueryDto;
      mockCommentsService.getComments.mockResolvedValue(mockPaginatedResponse);

      // Act
      await controller.getStoryComments("story-001", customQuery);

      // Assert
      expect(mockCommentsService.getComments).toHaveBeenCalledWith(
        "story-001",
        customQuery,
      );
    });

    it("应该处理空结果", async () => {
      // Arrange
      const emptyResponse = {
        ...mockPaginatedResponse,
        data: [],
        total: 0,
      };
      mockCommentsService.getComments.mockResolvedValue(emptyResponse);

      // Act
      const result = await controller.getStoryComments("story-001", queryDto);

      // Assert
      expect(result.data.total).toBe(0);
      expect(result.data.data).toEqual([]);
    });
  });

  describe("getCommentById - GET /comments/:id", () => {
    it("应该成功获取评论详情", async () => {
      // Arrange
      mockCommentsService.getCommentById.mockResolvedValue(mockCommentResponse);

      // Act
      const result = await controller.getCommentById("comment-001");

      // Assert
      expect(mockCommentsService.getCommentById).toHaveBeenCalledWith(
        "comment-001",
      );
      expect(result).toEqual({
        success: true,
        message: "获取评论详情成功",
        data: mockCommentResponse,
        statusCode: HttpStatus.OK,
        timestamp: expect.any(String),
      });
    });

    it("应该正确处理评论不存在的情况", async () => {
      // Arrange
      mockCommentsService.getCommentById.mockRejectedValue(
        new Error("评论不存在"),
      );

      // Act & Assert
      await expect(controller.getCommentById("non-existent")).rejects.toThrow(
        "评论不存在",
      );
    });
  });

  describe("updateComment - PUT /comments/:id", () => {
    const updateCommentDto: UpdateCommentDto = {
      content: "更新后的评论内容",
    };

    it("应该成功更新评论", async () => {
      // Arrange
      const updatedComment = {
        ...mockCommentResponse,
        content: "更新后的评论内容",
      };
      mockCommentsService.updateComment.mockResolvedValue(updatedComment);

      // Act
      const result = await controller.updateComment(
        "comment-001",
        updateCommentDto,
        mockRequest,
      );

      // Assert
      expect(mockCommentsService.updateComment).toHaveBeenCalledWith(
        "comment-001",
        updateCommentDto,
        "user-001",
      );
      expect(result).toEqual({
        success: true,
        message: "评论更新成功",
        data: updatedComment,
        statusCode: HttpStatus.OK,
        timestamp: expect.any(String),
      });
    });

    it("应该正确处理权限错误", async () => {
      // Arrange
      mockCommentsService.updateComment.mockRejectedValue(
        new Error("无权限修改此评论"),
      );

      // Act & Assert
      await expect(
        controller.updateComment("comment-001", updateCommentDto, mockRequest),
      ).rejects.toThrow("无权限修改此评论");
    });

    it("应该处理空的更新数据", async () => {
      // Arrange
      const emptyUpdateDto = {};
      mockCommentsService.updateComment.mockResolvedValue(mockCommentResponse);

      // Act
      const result = await controller.updateComment(
        "comment-001",
        emptyUpdateDto,
        mockRequest,
      );

      // Assert
      expect(mockCommentsService.updateComment).toHaveBeenCalledWith(
        "comment-001",
        emptyUpdateDto,
        "user-001",
      );
      expect(result.success).toBe(true);
    });
  });

  describe("deleteComment - DELETE /comments/:id", () => {
    it("应该成功删除评论", async () => {
      // Arrange
      const deletedComment = { ...mockCommentResponse, status: "deleted" };
      mockCommentsService.deleteComment.mockResolvedValue(deletedComment);

      // Act
      const result = await controller.deleteComment("comment-001", mockRequest);

      // Assert
      expect(mockCommentsService.deleteComment).toHaveBeenCalledWith(
        "comment-001",
        "user-001",
      );
      expect(result).toEqual({
        success: true,
        message: "评论删除成功",
        data: deletedComment,
        statusCode: HttpStatus.OK,
        timestamp: expect.any(String),
      });
    });

    it("应该正确处理删除不存在的评论", async () => {
      // Arrange
      mockCommentsService.deleteComment.mockRejectedValue(
        new Error("评论不存在"),
      );

      // Act & Assert
      await expect(
        controller.deleteComment("non-existent", mockRequest),
      ).rejects.toThrow("评论不存在");
    });

    it("应该正确处理删除权限错误", async () => {
      // Arrange
      mockCommentsService.deleteComment.mockRejectedValue(
        new Error("无权限删除此评论"),
      );

      // Act & Assert
      await expect(
        controller.deleteComment("comment-001", mockRequest),
      ).rejects.toThrow("无权限删除此评论");
    });
  });

  describe("toggleCommentLike - POST /comments/:id/like", () => {
    it("应该成功添加点赞", async () => {
      // Arrange
      const likeResponse = { liked: true, likesCount: 6 };
      mockCommentsService.toggleCommentLike.mockResolvedValue(likeResponse);

      // Act
      const result = await controller.toggleCommentLike(
        "comment-001",
        mockRequest,
      );

      // Assert
      expect(mockCommentsService.toggleCommentLike).toHaveBeenCalledWith(
        "comment-001",
        "user-001",
      );
      expect(result).toEqual({
        success: true,
        message: "点赞成功",
        data: likeResponse,
        statusCode: HttpStatus.OK,
        timestamp: expect.any(String),
      });
    });

    it("应该成功取消点赞", async () => {
      // Arrange
      const unlikeResponse = { liked: false, likesCount: 4 };
      mockCommentsService.toggleCommentLike.mockResolvedValue(unlikeResponse);

      // Act
      const result = await controller.toggleCommentLike(
        "comment-001",
        mockRequest,
      );

      // Assert
      expect(result).toEqual({
        success: true,
        message: "取消点赞成功",
        data: unlikeResponse,
        statusCode: HttpStatus.OK,
        timestamp: expect.any(String),
      });
    });

    it("应该正确处理自己给自己点赞的错误", async () => {
      // Arrange
      mockCommentsService.toggleCommentLike.mockRejectedValue(
        new Error("不能给自己的评论点赞"),
      );

      // Act & Assert
      await expect(
        controller.toggleCommentLike("comment-001", mockRequest),
      ).rejects.toThrow("不能给自己的评论点赞");
    });
  });

  describe("getCommentReplies - GET /comments/:id/replies", () => {
    const queryDto: CommentQueryDto = {
      page: 1,
      limit: 5,
      sortBy: CommentSortBy.CREATED_ASC,
    };

    it("应该成功获取评论回复", async () => {
      // Arrange
      const repliesResponse = {
        data: [{ ...mockCommentResponse, parentId: "comment-001", level: 1 }],
        total: 1,
        page: 1,
        limit: 5,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      };
      mockCommentsService.getCommentReplies.mockResolvedValue(repliesResponse);

      // Act
      const result = await controller.getCommentReplies(
        "comment-001",
        queryDto,
      );

      // Assert
      expect(mockCommentsService.getCommentReplies).toHaveBeenCalledWith(
        "comment-001",
        queryDto,
      );
      expect(result).toEqual({
        success: true,
        message: "获取评论回复成功",
        data: repliesResponse,
        statusCode: HttpStatus.OK,
        timestamp: expect.any(String),
      });
    });

    it("应该处理没有回复的情况", async () => {
      // Arrange
      const emptyReplies = {
        data: [],
        total: 0,
        page: 1,
        limit: 5,
        totalPages: 0,
        hasNext: false,
        hasPrev: false,
      };
      mockCommentsService.getCommentReplies.mockResolvedValue(emptyReplies);

      // Act
      const result = await controller.getCommentReplies(
        "comment-001",
        queryDto,
      );

      // Assert
      expect(result.data.total).toBe(0);
      expect(result.data.data).toEqual([]);
    });

    it("应该正确传递分页参数", async () => {
      // Arrange
      const customQuery = {
        page: 2,
        limit: 3,
        sortBy: CommentSortBy.CREATED_DESC,
      } as CommentQueryDto;
      const repliesResponse = {
        data: [],
        total: 0,
        page: 2,
        limit: 3,
        totalPages: 0,
        hasNext: false,
        hasPrev: true,
      };
      mockCommentsService.getCommentReplies.mockResolvedValue(repliesResponse);

      // Act
      await controller.getCommentReplies("comment-001", customQuery);

      // Assert
      expect(mockCommentsService.getCommentReplies).toHaveBeenCalledWith(
        "comment-001",
        customQuery,
      );
    });
  });

  describe("错误处理和边界测试", () => {
    it("应该正确处理服务层抛出的各种异常", async () => {
      // Arrange
      const errors = [
        "评论不存在",
        "故事不存在",
        "无权限访问",
        "评论已删除",
        "不能给自己的评论点赞",
      ];

      for (const error of errors) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        mockCommentsService.getCommentById.mockRejectedValue(new Error(error));

        // Act & Assert
        await expect(controller.getCommentById("test-id")).rejects.toThrow(
          error,
        );
      }
    });

    it("应该处理无效的UUID格式", async () => {
      // Arrange
      mockCommentsService.getCommentById.mockRejectedValue(
        new Error("无效的ID格式"),
      );

      // Act & Assert
      await expect(controller.getCommentById("invalid-uuid")).rejects.toThrow(
        "无效的ID格式",
      );
    });

    it("应该处理请求中缺少用户信息的情况", async () => {
      // Arrange
      const invalidRequest = {} as Request & { user: { id: string } };

      // Act & Assert
      await expect(
        controller.createComment(
          {
            storyId: "story-001",
            content: "测试",
            type: CommentType.TEXT,
          },
          invalidRequest,
        ),
      ).rejects.toThrow();
    });
  });

  describe("ApiResponse 格式验证", () => {
    it("所有成功响应应该包含正确的 ApiResponse 格式", async () => {
      // Arrange
      mockCommentsService.createComment.mockResolvedValue(mockCommentResponse);

      // Act
      const result = await controller.createComment(
        {
          storyId: "story-001",
          content: "测试",
          type: CommentType.TEXT,
        },
        mockRequest,
      );

      // Assert
      expect(result).toEqual({
        success: true,
        message: expect.any(String),
        data: expect.any(Object),
        statusCode: expect.any(Number),
        timestamp: expect.any(String),
      });
    });

    it("应该包含正确的HTTP状态码", async () => {
      // Arrange
      mockCommentsService.createComment.mockResolvedValue(mockCommentResponse);
      mockCommentsService.getCommentById.mockResolvedValue(mockCommentResponse);

      // Act
      const createResult = await controller.createComment(
        {
          storyId: "story-001",
          content: "测试",
          type: CommentType.TEXT,
        },
        mockRequest,
      );
      const getResult = await controller.getCommentById("comment-001");

      // Assert
      expect(createResult.statusCode).toBe(HttpStatus.CREATED);
      expect(getResult.statusCode).toBe(HttpStatus.OK);
    });
  });
});
