/* eslint-disable @typescript-eslint/no-explicit-any */
import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import type { Repository } from "typeorm";
import { getRepositoryToken } from "@nestjs/typeorm";
import { NotFoundException, ForbiddenException } from "@nestjs/common";

import { TimelineService } from "./timeline.service";
import {
  TimelineEvent,
  EventType,
  EventStatus,
  EventVisibility,
  EventImportance,
} from "./entities/timeline-event.entity";
import { User } from "../users/entities/user.entity";
import { Story } from "../stories/entities/story.entity";
import type { CreateTimelineEventDto } from "./dto/create-timeline-event.dto";
import type { UpdateTimelineEventDto } from "./dto/timeline-response.dto";
import type { TimelineQueryDto } from "./dto/timeline-query.dto";

describe("TimelineService - 企业级单元测试", () => {
  let service: TimelineService;
  let mockTimelineEventRepository: jest.Mocked<Repository<TimelineEvent>>;
  let mockUserRepository: jest.Mocked<Repository<User>>;
  let mockStoryRepository: jest.Mocked<Repository<Story>>;

  // Mock数据 - 使用具体类型
  const mockUser = {
    id: "test-user-id",
    username: "testuser",
    nickname: "测试用户",
    email: "<EMAIL>",
  };

  const mockStory = {
    id: "test-story-id",
    title: "测试故事",
    authorId: "author-id",
    authorName: "测试作者",
    summary: "测试故事摘要",
    publishedAt: new Date("2025-01-01"),
  };

  const mockTimelineEvent = {
    id: "test-event-id",
    userId: "test-user-id",
    title: "测试时间线事件",
    description: "测试事件描述",
    eventType: EventType.MILESTONE,
    status: EventStatus.ACTIVE,
    visibility: EventVisibility.PRIVATE,
    eventDate: new Date("2025-01-01"),
    endDate: null,
    importance: EventImportance.MEDIUM,
    location: "测试地点",
    relatedPeople: ["张三", "李四"],
    tags: ["测试", "时间线"],
    likesCount: 5,
    commentsCount: 3,
    viewCount: 10,
    isMilestone: true,
    isPinned: false,
    color: "#FF5733",
    icon: "milestone",
    images: ["https://example.com/image.jpg"],
    attachments: [],
    relatedStoryId: "test-story-id",
    customData: {
      category: "测试分类",
      outcome: "测试结果",
    },
    metadata: {},
    createdAt: new Date(),
    updatedAt: new Date(),
    user: mockUser,
    relatedStory: mockStory,
  };

  beforeEach(async () => {
    // 创建Repository Mock对象
    const mockTimelineEventRepositoryMethods = {
      create: jest.fn(),
      save: jest.fn(),
      find: jest.fn(),
      findOne: jest.fn(),
      findAndCount: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      increment: jest.fn().mockResolvedValue(undefined),
      count: jest.fn(),
      createQueryBuilder: jest.fn().mockReturnValue({
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        addOrderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[], 0]),
        getOne: jest.fn().mockResolvedValue(null),
        getMany: jest.fn().mockResolvedValue([]),
        getRawMany: jest.fn().mockResolvedValue([]),
      }),
    };

    const mockUserRepositoryMethods = {
      findOne: jest.fn().mockResolvedValue(mockUser),
      find: jest.fn(),
    };

    const mockStoryRepositoryMethods = {
      findOne: jest.fn().mockResolvedValue(mockStory),
      find: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TimelineService,
        {
          provide: getRepositoryToken(TimelineEvent),
          useValue: mockTimelineEventRepositoryMethods,
        },
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepositoryMethods,
        },
        {
          provide: getRepositoryToken(Story),
          useValue: mockStoryRepositoryMethods,
        },
      ],
    }).compile();

    service = module.get<TimelineService>(TimelineService);
    mockTimelineEventRepository = module.get(getRepositoryToken(TimelineEvent));
    mockUserRepository = module.get(getRepositoryToken(User));
    mockStoryRepository = module.get(getRepositoryToken(Story));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("createTimelineEvent - 创建时间线事件", () => {
    const createEventDto: CreateTimelineEventDto = {
      title: "测试时间线事件",
      description: "测试事件描述",
      eventType: EventType.MILESTONE,
      eventDate: "2025-01-01",
      visibility: EventVisibility.PRIVATE,
      importance: EventImportance.MEDIUM,
      location: "测试地点",
      relatedPeople: ["张三", "李四"],
      tags: ["测试", "时间线"],
      isMilestone: true,
      color: "#FF5733",
      icon: "milestone",
      relatedStoryId: "test-story-id",
      customData: {
        category: "测试分类",
        outcome: "测试结果",
      },
      metadata: {},
    };

    it("应该成功创建时间线事件", async () => {
      // Arrange
      const userId = "test-user-id";
      mockTimelineEventRepository.create.mockReturnValue(
        mockTimelineEvent as any,
      );
      mockTimelineEventRepository.save.mockResolvedValue(
        mockTimelineEvent as any,
      );
      mockStoryRepository.findOne.mockResolvedValue(mockStory as any);

      // Act
      const result = await service.createTimelineEvent(createEventDto, userId);

      // Assert
      expect(result.title).toBe(createEventDto.title);
      expect(result.eventType).toBe(createEventDto.eventType);
      expect(result.isMilestone).toBe(true);
      expect(mockStoryRepository.findOne).toHaveBeenCalledWith({
        where: { id: createEventDto.relatedStoryId },
      });
      expect(mockTimelineEventRepository.create).toHaveBeenCalled();
      expect(mockTimelineEventRepository.save).toHaveBeenCalledWith(
        mockTimelineEvent,
      );
    });

    it("应该在关联故事不存在时抛出NotFoundException", async () => {
      // Arrange
      const userId = "test-user-id";
      mockStoryRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.createTimelineEvent(createEventDto, userId),
      ).rejects.toThrow(NotFoundException);
      expect(mockStoryRepository.findOne).toHaveBeenCalledWith({
        where: { id: createEventDto.relatedStoryId },
      });
      expect(mockTimelineEventRepository.create).not.toHaveBeenCalled();
    });

    it("应该在没有关联故事时成功创建事件", async () => {
      // Arrange
      const userId = "test-user-id";
      const dtoWithoutStory = { ...createEventDto, relatedStoryId: undefined };
      const eventWithoutStory = { ...mockTimelineEvent, relatedStoryId: null };

      mockTimelineEventRepository.create.mockReturnValue(
        eventWithoutStory as any,
      );
      mockTimelineEventRepository.save.mockResolvedValue(
        eventWithoutStory as any,
      );

      // Act
      const result = await service.createTimelineEvent(dtoWithoutStory, userId);

      // Assert
      expect(result).toBeDefined();
      expect(mockStoryRepository.findOne).not.toHaveBeenCalled();
      expect(mockTimelineEventRepository.create).toHaveBeenCalled();
    });

    it("应该正确设置默认值", async () => {
      // Arrange
      const userId = "test-user-id";
      const minimalDto = {
        title: "最小测试事件",
        eventType: EventType.MILESTONE,
        eventDate: "2025-01-01",
      };

      const expectedEvent = {
        ...mockTimelineEvent,
        visibility: EventVisibility.PRIVATE,
        importance: EventImportance.MEDIUM,
        isMilestone: false,
        isPinned: false,
      };

      mockTimelineEventRepository.create.mockReturnValue(expectedEvent as any);
      mockTimelineEventRepository.save.mockResolvedValue(expectedEvent as any);

      // Act
      const result = await service.createTimelineEvent(
        minimalDto as CreateTimelineEventDto,
        userId,
      );

      // Assert
      expect(result.visibility).toBe(EventVisibility.PRIVATE);
      expect(result.importance).toBe(EventImportance.MEDIUM);
      expect(result.isMilestone).toBe(false);
    });

    it("应该正确处理多媒体附件", async () => {
      // Arrange
      const userId = "test-user-id";
      const dtoWithMedia = {
        ...createEventDto,
        images: [
          "https://example.com/image1.jpg",
          "https://example.com/image2.jpg",
        ],
        attachments: ["https://example.com/doc.pdf"],
      };

      const eventWithMedia = {
        ...mockTimelineEvent,
        images: dtoWithMedia.images,
        attachments: dtoWithMedia.attachments,
      };

      mockTimelineEventRepository.create.mockReturnValue(eventWithMedia as any);
      mockTimelineEventRepository.save.mockResolvedValue(eventWithMedia as any);

      // Act
      const result = await service.createTimelineEvent(dtoWithMedia, userId);

      // Assert
      expect(result.images).toHaveLength(2);
      expect(result.attachments).toHaveLength(1);
      expect(result.attachments![0]).toBe("https://example.com/doc.pdf");
    });
  });

  describe("getUserTimeline - 获取用户时间线", () => {
    const mockEvents = [mockTimelineEvent];
    const mockTotal = 1;

    it("应该成功获取用户时间线", async () => {
      // Arrange
      const targetUserId = "test-user-id";
      const queryDto: TimelineQueryDto = {
        page: 1,
        limit: 10,
        status: EventStatus.ACTIVE,
      };
      const currentUserId = "current-user-id";

      const mockQueryBuilder = mockTimelineEventRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        mockEvents as any,
        mockTotal,
      ]);

      // Act
      const result = await service.getUserTimeline(
        targetUserId,
        queryDto,
        currentUserId,
      );

      // Assert
      expect(result.data).toHaveLength(1);
      expect(result.total).toBe(mockTotal);
      expect(result.page).toBe(1);
      expect(result.limit).toBe(10);
      expect(result.totalPages).toBe(1);
    });

    it("应该根据事件类型筛选", async () => {
      // Arrange
      const targetUserId = "test-user-id";
      const queryDto: TimelineQueryDto = {
        page: 1,
        limit: 10,
        eventType: EventType.ACHIEVEMENT,
      };

      const mockQueryBuilder = mockTimelineEventRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        mockEvents as any,
        mockTotal,
      ]);

      // Act
      await service.getUserTimeline(targetUserId, queryDto);

      // Assert
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "timeline.eventType = :eventType",
        { eventType: EventType.ACHIEVEMENT },
      );
    });

    it("应该根据重要性筛选", async () => {
      // Arrange
      const targetUserId = "test-user-id";
      const queryDto: TimelineQueryDto = {
        page: 1,
        limit: 10,
        minImportance: EventImportance.HIGH,
      };

      const mockQueryBuilder = mockTimelineEventRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        mockEvents as any,
        mockTotal,
      ]);

      // Act
      await service.getUserTimeline(targetUserId, queryDto);

      // Assert
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "timeline.importance = :importance",
        { minImportance: EventImportance.HIGH },
      );
    });

    it("应该根据日期范围筛选", async () => {
      // Arrange
      const targetUserId = "test-user-id";
      const queryDto: TimelineQueryDto = {
        page: 1,
        limit: 10,
        startDate: "2025-01-01",
        endDate: "2025-12-31",
      };

      const mockQueryBuilder = mockTimelineEventRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        mockEvents as any,
        mockTotal,
      ]);

      // Act
      await service.getUserTimeline(targetUserId, queryDto);

      // Assert
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "timeline.eventDate BETWEEN :startDate AND :endDate",
        {
          startDate: queryDto.startDate,
          endDate: queryDto.endDate,
        },
      );
    });

    it("应该根据标签筛选", async () => {
      // Arrange
      const targetUserId = "test-user-id";
      const queryDto: TimelineQueryDto = {
        page: 1,
        limit: 10,
        tags: ["测试", "重要"],
      };

      const mockQueryBuilder = mockTimelineEventRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        mockEvents as any,
        mockTotal,
      ]);

      // Act
      await service.getUserTimeline(targetUserId, queryDto);

      // Assert
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "timeline.tags && :tags",
        { tags: ["测试", "重要"] },
      );
    });

    it("应该根据里程碑状态筛选", async () => {
      // Arrange
      const targetUserId = "test-user-id";
      const queryDto: TimelineQueryDto = {
        page: 1,
        limit: 10,
        isMilestone: true,
      };

      const mockQueryBuilder = mockTimelineEventRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        mockEvents as any,
        mockTotal,
      ]);

      // Act
      await service.getUserTimeline(targetUserId, queryDto);

      // Assert
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "timeline.isMilestone = :isMilestone",
        { isMilestone: true },
      );
    });

    it("应该支持全文搜索", async () => {
      // Arrange
      const targetUserId = "test-user-id";
      const queryDto: TimelineQueryDto = {
        page: 1,
        limit: 10,
        search: "测试关键词",
      };

      const mockQueryBuilder = mockTimelineEventRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        mockEvents as any,
        mockTotal,
      ]);

      // Act
      await service.getUserTimeline(targetUserId, queryDto);

      // Assert
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "(timeline.title ILIKE :search OR timeline.description ILIKE :search)",
        { search: "%测试关键词%" },
      );
    });

    it("应该正确处理置顶事件排序", async () => {
      // Arrange
      const targetUserId = "test-user-id";
      const queryDto: TimelineQueryDto = {
        page: 1,
        limit: 10,
        sortBy: "eventDate",
        sortOrder: "DESC",
      };

      const mockQueryBuilder = mockTimelineEventRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        mockEvents as any,
        mockTotal,
      ]);

      // Act
      await service.getUserTimeline(targetUserId, queryDto);

      // Assert
      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith(
        "timeline.isPinned",
        "DESC",
      );
      expect(mockQueryBuilder.addOrderBy).toHaveBeenCalledWith(
        "timeline.eventDate",
        "DESC",
      );
    });

    it("应该正确处理可见性权限", async () => {
      // Arrange
      const targetUserId = "test-user-id";
      const queryDto: TimelineQueryDto = { page: 1, limit: 10 };
      const currentUserId = "other-user-id";

      const mockQueryBuilder = mockTimelineEventRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        mockEvents as any,
        mockTotal,
      ]);

      // Act
      await service.getUserTimeline(targetUserId, queryDto, currentUserId);

      // Assert
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "(timeline.visibility = :publicVisibility OR timeline.userId = :currentUserId)",
        {
          publicVisibility: EventVisibility.PUBLIC,
          currentUserId,
        },
      );
    });
  });

  describe("getTimelineEventById - 获取事件详情", () => {
    it("应该成功获取事件详情", async () => {
      // Arrange
      const eventId = "test-event-id";
      const currentUserId = "test-user-id";

      const mockQueryBuilder = mockTimelineEventRepository.createQueryBuilder();
      (mockQueryBuilder.getOne as jest.Mock).mockResolvedValue(
        mockTimelineEvent as any,
      );

      // Act
      const result = await service.getTimelineEventById(eventId, currentUserId);

      // Assert
      expect(result).toBeDefined();
      expect(result.title).toBe(mockTimelineEvent.title);
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith(
        "timeline.user",
        "user",
      );
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith(
        "timeline.relatedStory",
        "story",
      );
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        "timeline.id = :eventId",
        { eventId },
      );
    });

    it("应该在事件不存在时抛出NotFoundException", async () => {
      // Arrange
      const eventId = "non-existent-id";
      const currentUserId = "test-user-id";

      const mockQueryBuilder = mockTimelineEventRepository.createQueryBuilder();
      (mockQueryBuilder.getOne as jest.Mock).mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.getTimelineEventById(eventId, currentUserId),
      ).rejects.toThrow(NotFoundException);
    });

    it("应该检查可见性权限", async () => {
      // Arrange
      const eventId = "test-event-id";
      const currentUserId = "other-user-id";
      const privateEvent = {
        ...mockTimelineEvent,
        visibility: EventVisibility.PRIVATE,
        userId: "different-user-id",
      };

      const mockQueryBuilder = mockTimelineEventRepository.createQueryBuilder();
      (mockQueryBuilder.getOne as jest.Mock).mockResolvedValue(
        privateEvent as any,
      );

      // Act & Assert
      await expect(
        service.getTimelineEventById(eventId, currentUserId),
      ).rejects.toThrow(ForbiddenException);
    });

    it("应该增加浏览统计", async () => {
      // Arrange
      const eventId = "test-event-id";
      const currentUserId = "test-user-id";

      const mockQueryBuilder = mockTimelineEventRepository.createQueryBuilder();
      (mockQueryBuilder.getOne as jest.Mock).mockResolvedValue(
        mockTimelineEvent as any,
      );

      // Act
      await service.getTimelineEventById(eventId, currentUserId);

      // Assert
      expect(mockTimelineEventRepository.increment).toHaveBeenCalledWith(
        { id: eventId },
        "viewCount",
        1,
      );
    });
  });

  describe("updateTimelineEvent - 更新时间线事件", () => {
    const updateEventDto: UpdateTimelineEventDto = {
      title: "更新后的标题",
      description: "更新后的描述",
      importance: EventImportance.HIGH,
      tags: ["更新", "测试"],
      isPinned: true,
    };

    it("应该成功更新时间线事件", async () => {
      // Arrange
      const eventId = "test-event-id";
      const userId = "test-user-id";
      const existingEvent = { ...mockTimelineEvent, userId };

      mockTimelineEventRepository.findOne.mockResolvedValue(
        existingEvent as any,
      );
      mockTimelineEventRepository.save.mockResolvedValue({
        ...existingEvent,
        ...updateEventDto,
      } as any);

      // Act
      const result = await service.updateTimelineEvent(
        eventId,
        updateEventDto,
        userId,
      );

      // Assert
      expect(result.title).toBe(updateEventDto.title);
      expect(result.importance).toBe(EventImportance.HIGH);
      expect(result.isPinned).toBe(true);
      expect(mockTimelineEventRepository.findOne).toHaveBeenCalledWith({
        where: { id: eventId },
      });
      expect(mockTimelineEventRepository.save).toHaveBeenCalled();
    });

    it("应该在事件不存在时抛出NotFoundException", async () => {
      // Arrange
      const eventId = "non-existent-id";
      const userId = "test-user-id";

      mockTimelineEventRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.updateTimelineEvent(eventId, updateEventDto, userId),
      ).rejects.toThrow(NotFoundException);
      expect(mockTimelineEventRepository.findOne).toHaveBeenCalledWith({
        where: { id: eventId },
      });
      expect(mockTimelineEventRepository.save).not.toHaveBeenCalled();
    });

    it("应该在无权限时抛出ForbiddenException", async () => {
      // Arrange
      const eventId = "test-event-id";
      const userId = "unauthorized-user-id";
      const existingEvent = {
        ...mockTimelineEvent,
        userId: "different-user-id",
      };

      mockTimelineEventRepository.findOne.mockResolvedValue(
        existingEvent as any,
      );

      // Act & Assert
      await expect(
        service.updateTimelineEvent(eventId, updateEventDto, userId),
      ).rejects.toThrow(ForbiddenException);
      expect(mockTimelineEventRepository.save).not.toHaveBeenCalled();
    });

    it("应该验证关联故事存在性", async () => {
      // Arrange
      const eventId = "test-event-id";
      const userId = "test-user-id";
      const dtoWithStory = {
        ...updateEventDto,
        relatedStoryId: "new-story-id",
      };
      const existingEvent = { ...mockTimelineEvent, userId };

      mockTimelineEventRepository.findOne.mockResolvedValue(
        existingEvent as any,
      );
      mockStoryRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.updateTimelineEvent(eventId, dtoWithStory, userId),
      ).rejects.toThrow(NotFoundException);
      expect(mockStoryRepository.findOne).toHaveBeenCalledWith({
        where: { id: "new-story-id" },
      });
    });

    it("应该正确处理部分更新", async () => {
      // Arrange
      const eventId = "test-event-id";
      const userId = "test-user-id";
      const partialUpdate = { title: "仅更新标题" };
      const existingEvent = { ...mockTimelineEvent, userId };

      mockTimelineEventRepository.findOne.mockResolvedValue(
        existingEvent as any,
      );
      mockTimelineEventRepository.save.mockResolvedValue({
        ...existingEvent,
        title: "仅更新标题",
      } as any);

      // Act
      const result = await service.updateTimelineEvent(
        eventId,
        partialUpdate,
        userId,
      );

      // Assert
      expect(result.title).toBe("仅更新标题");
      expect(result.description).toBe(mockTimelineEvent.description); // 保持原值
    });
  });

  describe("deleteTimelineEvent - 删除时间线事件", () => {
    it("应该成功删除时间线事件（软删除）", async () => {
      // Arrange
      const eventId = "test-event-id";
      const userId = "test-user-id";
      const existingEvent = { ...mockTimelineEvent, userId };

      mockTimelineEventRepository.findOne.mockResolvedValue(
        existingEvent as any,
      );

      // Act
      await service.deleteTimelineEvent(eventId, userId);

      // Assert
      expect(mockTimelineEventRepository.findOne).toHaveBeenCalledWith({
        where: { id: eventId },
      });
      expect(mockTimelineEventRepository.update).toHaveBeenCalledWith(eventId, {
        status: EventStatus.DELETED,
      });
    });

    it("应该在事件不存在时抛出NotFoundException", async () => {
      // Arrange
      const eventId = "non-existent-id";
      const userId = "test-user-id";

      mockTimelineEventRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.deleteTimelineEvent(eventId, userId),
      ).rejects.toThrow(NotFoundException);
      expect(mockTimelineEventRepository.update).not.toHaveBeenCalled();
    });

    it("应该在无权限时抛出ForbiddenException", async () => {
      // Arrange
      const eventId = "test-event-id";
      const userId = "unauthorized-user-id";
      const existingEvent = {
        ...mockTimelineEvent,
        userId: "different-user-id",
      };

      mockTimelineEventRepository.findOne.mockResolvedValue(
        existingEvent as any,
      );

      // Act & Assert
      await expect(
        service.deleteTimelineEvent(eventId, userId),
      ).rejects.toThrow(ForbiddenException);
      expect(mockTimelineEventRepository.update).not.toHaveBeenCalled();
    });
  });

  describe("getTimelineStats - 获取时间线统计", () => {
    const mockStats = {
      userId: "test-user-id",
      totalEvents: 15,
      milestoneCount: 3,
      publicEvents: 8,
      privateEvents: 7,
      thisYearEvents: 10,
      thisMonthEvents: 2,
      totalViews: 150,
      totalLikes: 45,
      eventTypeStats: [
        { eventType: EventType.MILESTONE, count: 3, percentage: 20 },
        { eventType: EventType.ACHIEVEMENT, count: 5, percentage: 33.33 },
      ],
      importanceStats: [
        { importance: EventImportance.HIGH, count: 4, percentage: 26.67 },
        { importance: EventImportance.MEDIUM, count: 8, percentage: 53.33 },
        { importance: EventImportance.LOW, count: 3, percentage: 20 },
      ],
      topTags: [
        { tag: "工作", count: 8, recentUse: new Date() },
        { tag: "学习", count: 5, recentUse: new Date() },
      ],
      yearlyDistribution: [
        { year: 2025, count: 10 },
        { year: 2024, count: 5 },
      ],
      recentEvents: [
        { eventId: "event-1", title: "最近事件1", eventDate: new Date() },
      ],
      upcomingEvents: [
        { eventId: "event-2", title: "即将到来事件", eventDate: new Date() },
      ],
    };

    it("应该成功获取时间线统计信息", async () => {
      // Arrange
      const userId = "test-user-id";
      const currentYear = new Date().getFullYear();
      const currentMonth = new Date().getMonth() + 1;
      expect(currentYear).toBeGreaterThan(2020);
      expect(currentMonth).toBeGreaterThan(0);

      // Mock基础统计
      mockTimelineEventRepository.count.mockResolvedValueOnce(15); // totalEvents
      mockTimelineEventRepository.count.mockResolvedValueOnce(3); // milestoneCount
      mockTimelineEventRepository.count.mockResolvedValueOnce(8); // publicEvents
      mockTimelineEventRepository.count.mockResolvedValueOnce(7); // privateEvents
      mockTimelineEventRepository.count.mockResolvedValueOnce(10); // thisYearEvents
      mockTimelineEventRepository.count.mockResolvedValueOnce(2); // thisMonthEvents

      // Mock QueryBuilder查询
      const mockQueryBuilder = mockTimelineEventRepository.createQueryBuilder();

      // Mock聚合统计查询
      (mockQueryBuilder.getRawMany as jest.Mock)
        .mockResolvedValueOnce([
          { eventType: EventType.MILESTONE, count: "3" },
          { eventType: EventType.ACHIEVEMENT, count: "5" },
        ])
        .mockResolvedValueOnce([
          { importance: EventImportance.HIGH, count: "4" },
          { importance: EventImportance.MEDIUM, count: "8" },
          { importance: EventImportance.LOW, count: "3" },
        ])
        .mockResolvedValueOnce([
          { tag: "工作", count: "8" },
          { tag: "学习", count: "5" },
        ])
        .mockResolvedValueOnce([
          { year: 2025, count: "10" },
          { year: 2024, count: "5" },
        ]);

      (mockQueryBuilder.getMany as jest.Mock)
        .mockResolvedValueOnce([
          { id: "event-1", title: "最近事件1", eventDate: new Date() },
        ])
        .mockResolvedValueOnce([
          { id: "event-2", title: "即将到来事件", eventDate: new Date() },
        ]);

      // Act
      const result = await service.getTimelineStats(userId);

      // Assert
      expect(result.userId).toBe(userId);
      expect(result.totalEvents).toBe(15);
      expect(result.milestoneEvents).toBe(3);
      expect(result.typeStats).toHaveLength(2);
      expect(result.importanceStats).toHaveLength(3);
      expect(result.topTags).toHaveLength(2);
      expect(result.yearlyDistribution).toHaveLength(2);
    });

    it("应该正确计算百分比", async () => {
      // Arrange
      const userId = "test-user-id";

      mockTimelineEventRepository.count.mockResolvedValue(10);

      const mockQueryBuilder = mockTimelineEventRepository.createQueryBuilder();
      (mockQueryBuilder.getRawMany as jest.Mock)
        .mockResolvedValueOnce([
          { eventType: EventType.MILESTONE, count: "2" },
          { eventType: EventType.ACHIEVEMENT, count: "8" },
        ])
        .mockResolvedValueOnce([])
        .mockResolvedValueOnce([])
        .mockResolvedValueOnce([]);

      (mockQueryBuilder.getMany as jest.Mock).mockResolvedValue([]);

      // Act
      const result = await service.getTimelineStats(userId);

      // Assert
      const eventTypeStats = result.typeStats;
      expect(eventTypeStats[0].percentage).toBe(20); // 2/10 = 20%
      expect(eventTypeStats[1].percentage).toBe(80); // 8/10 = 80%
    });

    it("应该处理没有数据的情况", async () => {
      // Arrange
      const userId = "test-user-id";

      mockTimelineEventRepository.count.mockResolvedValue(0);

      const mockQueryBuilder = mockTimelineEventRepository.createQueryBuilder();
      (mockQueryBuilder.getRawMany as jest.Mock).mockResolvedValue([]);
      (mockQueryBuilder.getMany as jest.Mock).mockResolvedValue([]);

      // Act
      const result = await service.getTimelineStats(userId);

      // Assert
      expect(result.totalEvents).toBe(0);
      expect(result.typeStats).toHaveLength(0);
      expect(result.importanceStats).toHaveLength(0);
      expect(result.topTags).toHaveLength(0);
    });

    it("应该获取年度分布统计", async () => {
      // Arrange
      const userId = "test-user-id";

      mockTimelineEventRepository.count.mockResolvedValue(15);

      const mockQueryBuilder = mockTimelineEventRepository.createQueryBuilder();
      (mockQueryBuilder.getRawMany as jest.Mock)
        .mockResolvedValueOnce([])
        .mockResolvedValueOnce([])
        .mockResolvedValueOnce([])
        .mockResolvedValueOnce([
          { year: 2025, count: "12" },
          { year: 2024, count: "3" },
        ]);

      (mockQueryBuilder.getMany as jest.Mock).mockResolvedValue([]);

      // Act
      const result = await service.getTimelineStats(userId);

      // Assert
      expect(result.yearlyDistribution).toHaveLength(2);
      expect(result.yearlyDistribution[0]).toEqual({
        year: 2025,
        count: 12,
      });
    });

    it("应该获取最近和即将到来的事件", async () => {
      // Arrange
      const userId = "test-user-id";

      mockTimelineEventRepository.count.mockResolvedValue(5);

      const mockQueryBuilder = mockTimelineEventRepository.createQueryBuilder();
      (mockQueryBuilder.getRawMany as jest.Mock).mockResolvedValue([]);
      (mockQueryBuilder.getMany as jest.Mock)
        .mockResolvedValueOnce([
          { id: "recent-1", title: "最近事件", eventDate: new Date() },
        ])
        .mockResolvedValueOnce([
          { id: "upcoming-1", title: "即将到来", eventDate: new Date() },
        ]);

      // Act
      const result = await service.getTimelineStats(userId);

      // Assert
      expect(result.recentEvents).toHaveLength(1);
      expect(result.upcomingEvents).toHaveLength(1);
      expect(result.recentEvents[0].title).toBe("最近事件");
      expect(result.upcomingEvents[0].title).toBe("即将到来");
    });
  });

  describe("边界条件和错误处理", () => {
    it("应该处理数据库连接错误", async () => {
      // Arrange
      const userId = "test-user-id";
      const dto = {
        title: "测试事件",
        eventType: EventType.MILESTONE,
        eventDate: "2025-01-01",
      } as CreateTimelineEventDto;
      mockTimelineEventRepository.save.mockRejectedValue(
        new Error("数据库连接失败"),
      );

      // Act & Assert
      await expect(service.createTimelineEvent(dto, userId)).rejects.toThrow(
        "数据库连接失败",
      );
    });

    it("应该处理分页边界值", async () => {
      // Arrange
      const targetUserId = "test-user-id";
      const queryDto: TimelineQueryDto = {
        page: 0, // 无效页码
        limit: -1, // 无效限制
      };

      const mockQueryBuilder = mockTimelineEventRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        [],
        0,
      ]);

      // Act
      const result = await service.getUserTimeline(targetUserId, queryDto);

      // Assert
      expect(result.page).toBeGreaterThanOrEqual(1); // 应该修正边界值
      expect(result.limit).toBeGreaterThanOrEqual(1); // 应该修正边界值
    });

    it("应该处理空搜索字符串", async () => {
      // Arrange
      const targetUserId = "test-user-id";
      const queryDto: TimelineQueryDto = {
        page: 1,
        limit: 10,
        search: "",
      };

      const mockQueryBuilder = mockTimelineEventRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        [],
        0,
      ]);

      // Act
      await service.getUserTimeline(targetUserId, queryDto);

      // Assert
      expect(mockQueryBuilder.andWhere).not.toHaveBeenCalledWith(
        expect.stringContaining("ILIKE"),
        expect.anything(),
      );
    });

    it("应该处理无效的排序字段", async () => {
      // Arrange
      const targetUserId = "test-user-id";
      const queryDto: TimelineQueryDto = {
        page: 1,
        limit: 10,
        sortBy: "invalidField",
        sortOrder: "INVALID" as "ASC",
      };

      const mockQueryBuilder = mockTimelineEventRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        [],
        0,
      ]);

      // Act
      await service.getUserTimeline(targetUserId, queryDto);

      // Assert
      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith(
        "timeline.isPinned",
        "DESC",
      );
      expect(mockQueryBuilder.addOrderBy).toHaveBeenCalledWith(
        "timeline.eventDate", // 应该回退到默认排序字段
        "DESC", // 应该回退到默认排序方向
      );
    });
  });

  describe("性能和优化测试", () => {
    it("应该使用适当的数据库索引", async () => {
      // Arrange
      const targetUserId = "test-user-id";
      const queryDto: TimelineQueryDto = {
        page: 1,
        limit: 10,
        status: EventStatus.ACTIVE,
      };

      const mockQueryBuilder = mockTimelineEventRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        [],
        0,
      ]);

      // Act
      await service.getUserTimeline(targetUserId, queryDto);

      // Assert
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        "timeline.userId = :userId",
        { userId: targetUserId },
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "timeline.status = :status",
        { status: EventStatus.ACTIVE },
      );
    });

    it("应该限制查询结果数量", async () => {
      // Arrange
      const targetUserId = "test-user-id";
      const queryDto: TimelineQueryDto = {
        page: 1,
        limit: 1000, // 过大的限制
      };

      const mockQueryBuilder = mockTimelineEventRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        [],
        0,
      ]);

      // Act
      await service.getUserTimeline(targetUserId, queryDto);

      // Assert
      expect(mockQueryBuilder.take).toHaveBeenCalledWith(100); // 应该限制为最大值
    });

    it("应该使用关联查询避免N+1问题", async () => {
      // Arrange
      const eventId = "test-event-id";
      const currentUserId = "test-user-id";

      const mockQueryBuilder = mockTimelineEventRepository.createQueryBuilder();
      (mockQueryBuilder.getOne as jest.Mock).mockResolvedValue(
        mockTimelineEvent as any,
      );

      // Act
      await service.getTimelineEventById(eventId, currentUserId);

      // Assert
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith(
        "timeline.user",
        "user",
      );
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith(
        "timeline.relatedStory",
        "story",
      );
    });
  });

  describe("业务逻辑验证", () => {
    it("应该正确处理可见性权限", async () => {
      // Arrange
      const eventId = "test-event-id";
      const currentUserId = "owner-user-id";
      const publicEvent = {
        ...mockTimelineEvent,
        userId: "different-user-id",
        visibility: EventVisibility.PUBLIC,
      };

      const mockQueryBuilder = mockTimelineEventRepository.createQueryBuilder();
      (mockQueryBuilder.getOne as jest.Mock).mockResolvedValue(
        publicEvent as any,
      );

      // Act
      const result = await service.getTimelineEventById(eventId, currentUserId);

      // Assert
      expect(result.visibility).toBe(EventVisibility.PUBLIC);
      expect(result.userId).toBe("different-user-id");
    });

    it("应该正确处理置顶事件排序", async () => {
      // Arrange
      const targetUserId = "test-user-id";
      const queryDto: TimelineQueryDto = {
        page: 1,
        limit: 10,
      };

      const mockQueryBuilder = mockTimelineEventRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        [],
        0,
      ]);

      // Act
      await service.getUserTimeline(targetUserId, queryDto);

      // Assert
      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith(
        "timeline.isPinned",
        "DESC",
      );
      expect(mockQueryBuilder.addOrderBy).toHaveBeenCalledWith(
        "timeline.eventDate",
        "DESC",
      );
    });

    it("应该验证事件所有权", async () => {
      // Arrange
      const eventId = "test-event-id";
      const userId = "test-user-id";
      const updateDto = { title: "新标题" };
      const ownedEvent = { ...mockTimelineEvent, userId };

      mockTimelineEventRepository.findOne.mockResolvedValue(ownedEvent as any);
      mockTimelineEventRepository.save.mockResolvedValue({
        ...ownedEvent,
        title: "新标题",
      } as any);

      // Act
      const result = await service.updateTimelineEvent(
        eventId,
        updateDto,
        userId,
      );

      // Assert
      expect(result.title).toBe("新标题");
      expect(mockTimelineEventRepository.save).toHaveBeenCalled();
    });

    it("应该正确处理里程碑事件", async () => {
      // Arrange
      const userId = "test-user-id";
      const milestoneDto = {
        title: "重要里程碑",
        eventType: EventType.MILESTONE,
        eventDate: "2025-01-01",
        isMilestone: true,
        importance: EventImportance.HIGH,
      };

      const milestoneEvent = {
        ...mockTimelineEvent,
        isMilestone: true,
        importance: EventImportance.HIGH,
      };

      mockTimelineEventRepository.create.mockReturnValue(milestoneEvent as any);
      mockTimelineEventRepository.save.mockResolvedValue(milestoneEvent as any);

      // Act
      const result = await service.createTimelineEvent(
        milestoneDto as any,
        userId,
      );

      // Assert
      expect(result.isMilestone).toBe(true);
      expect(result.importance).toBe(EventImportance.HIGH);
    });
  });
});
