# YGS (有故事) 项目导航中心

> **项目名称**: YGS - 基于真实社交关系的故事分享平台  
> **当前版本**: v1.0.0 企业级完整产品  
> **技术栈**: NestJS 11 + Flutter 3.32 + PostgreSQL + Redis  
> **最后更新**: 2025-07-29  
> **版本管理**: 统一使用 v1.0.0 版本

## 🎯 项目概述

YGS 是一个基于真实社交关系的故事分享平台，核心创新是**人物点亮系统**。该系统允许真实用户通过手机验证认领故事中的角色，创建经过验证的社交连接。

### ✨ 核心特性

- 🎯 **人物点亮系统**: 通过手机验证创建真实社交连接的核心创新
- 📖 **故事创作管理**: 完整的故事创作、编辑、分享功能
- 🔐 **安全认证体系**: JWT + 刷新令牌 + 多因素认证
- 🏗️ **企业级架构**: 支持高并发、高可用、可扩展
- 📱 **跨平台支持**: Flutter 3.32 实现 iOS/Android 双端

## 📁 项目文档架构

YGS 项目采用分层文档导航体系，通过 README.md 提供清晰的文档入口。每类文档都有明确的职责分工：

### 🏗️ 项目文档整体架构

```
YGS 项目文档体系:
├── README.md                    # 📍 项目整体导航中心（本文档）
├── CLAUDE.md                    # 🤖 AI开发助手规范约束
├── 产品文档/                     # 📋 业务需求与产品规范
│   ├── README.md                # 📍 产品文档导航中心
│   └── v1.0.0/ ✅              # 完整版本产品规范（统一版本）
│       ├── 产品功能清单.md       # 134个API接口功能规范
│       └── 业务流程说明.md       # 10阶段点亮流程详解
├── 对接文档/                     # 🔗 前后端对接技术规范
│   ├── README.md                # 📍 对接文档导航中心
│   └── API接口文档.md            # 完整API接口技术规范
├── 项目文档/                     # 🔧 项目执行与开发管理
│   ├── README.md                # 📍 项目文档导航中心
│   └── 项目执行-v1.0.0.md        # 当前版本执行情况
├── AppServe/                    # 🔧 后端服务代码
│   ├── README.md                # 📍 后端开发导航中心
│   └── docs/                    # 📚 后端技术文档集合
└── AppMobile/                   # 📱 Flutter移动端代码
    ├── README.md                # 📍 Flutter开发导航中心
    └── docs/                    # 📚 Flutter技术文档集合
        ├── FLUTTER_DEVELOPMENT_PLAN.md    # Flutter开发规划
        └── FLUTTER_ARCHITECTURE_DESIGN.md # Flutter架构设计
```

### 🎯 文档分类与职责

#### 📋 产品文档 (产品文档/)

**职责**: 业务需求定义、功能规范、业务流程设计  
**特点**: 业务驱动，与技术实现无关  
**用户**: 产品经理、业务分析师、开发团队

**核心内容**:

- 产品功能清单和需求规范
- 业务流程说明和用户路径
- 技术实现标准和架构指导
- 开发计划和执行方案

**使用场景**: 需求评审、开发计划、测试计划、原型设计

#### 🔗 对接文档 (对接文档/)

**职责**: 前后端对接技术标准、API接口规范  
**特点**: 技术对接导向，确保前后端协作标准  
**用户**: 前端工程师、后端工程师、测试工程师

**核心内容**:

- 完整的API接口技术规范
- 统一的请求响应格式标准
- 认证授权机制技术实现
- 错误码规范和异常处理标准

**使用场景**: 前后端对接、接口调试、集成测试、技术评审

#### 🔧 项目文档 (项目文档/)

**职责**: 开发执行进度、风险管理、下一步计划  
**特点**: 执行导向，记录"如何做"和"遇到什么问题"  
**用户**: 项目经理、开发团队、测试团队

**核心内容**:

- 项目进度跟踪和里程碑状态
- 风险识别、评估和应对措施
- 开发过程中的关键问题和解决方案
- 下一步工作计划和资源安排

**使用场景**: 进度监控、风险管控、问题追溯、决策支持

#### 💻 代码文档 (AppServe/, AppMobile/)

**职责**: 技术实现细节、API 文档、开发指南  
**特点**: 技术导向，指导具体代码开发  
**用户**: 开发团队、技术团队

**核心内容**:

- 系统架构和技术选型说明
- 代码结构和模块功能介绍
- API 接口文档和使用指南
- 开发环境搭建和部署流程

**使用场景**: 环境配置、代码开发、API 调用、技术调研

#### 🤖 开发规范 (CLAUDE.md)

**职责**: AI 开发助手行为约束和质量标准  
**特点**: 规范导向，确保开发质量和标准  
**用户**: AI 开发助手、开发团队

**核心内容**:

- Claude Code 基础配置和约束
- 技术栈稳定性和安全规范
- 企业级开发标准和质量要求
- 开发执行流程和检查机制

**使用场景**: AI 辅助开发、代码质量检查、技术决策约束

## 🔄 开发工作流程

### 📖 推荐的文档阅读顺序

遇到任何开发问题时，建议按以下顺序查阅文档：

1. **📍 项目整体了解** → 阅读本 README 了解项目架构和文档关系
2. **🤖 开发规范确认** → 查阅 [CLAUDE.md](./CLAUDE.md) 了解开发约束和质量标准
3. **📊 项目状态了解** → 查阅 [项目文档](./项目文档/README.md) 了解当前执行情况
4. **📋 业务需求理解** → 查阅 [产品文档](./产品文档/README.md) 了解对应版本的产品规范
5. **🔧 技术实现查阅** → 根据问题类型查阅对应的代码模块文档
   - 后端问题 → 查阅 [AppServe](./AppServe/README.md)
   - 前端问题 → 查阅 [AppMobile](./AppMobile/README.md)

## 🔄 全局文档联动更新机制

### ⚡ 强制联动触发场景

为确保文档时效性和准确性，以下代码变更必须同步更新对应文档：

| 代码变更类型 | 必须更新的文档 | 联动检查清单 |
|-------------|---------------|-------------|
| **新增API接口** | `对接文档/API接口文档.md` | ✅ 接口规范 ✅ Swagger注释 ✅ 测试用例 |
| **修改数据库结构** | `AppServe/docs/ARCHITECTURE.md` | ✅ 实体关系图 ✅ 迁移脚本说明 ✅ 业务影响 |
| **新增/修改模块** | 各项目`docs/CODE_STRUCTURE.md` | ✅ 目录结构 ✅ 模块职责 ✅ 依赖关系 |
| **环境配置变更** | 各项目`docs/DEVELOPMENT_*.md` | ✅ 环境变量 ✅ 启动命令 ✅ 故障排除 |
| **测试文件变更** | 各项目`docs/TESTING_*.md` | ✅ test/目录结构 ✅ 测试覆盖率 ✅ 测试命令 |
| **产品功能变更** | `产品文档/v1.0.0/` | ✅ 功能清单 ✅ 业务流程 ✅ API映射 |
| **项目里程碑** | `项目文档/项目执行-v1.0.0.md` | ✅ 进度状态 ✅ 风险评估 ✅ 下一步计划 |

### 📋 三阶段联动检查机制

#### 🔧 开发阶段 (每次代码提交前)
```bash
# 1. 代码质量检查
npm run lint && npm run type-check

# 2. 文档同步检查清单
- [ ] 相关文档已同步更新
- [ ] README导航表格链接有效
- [ ] 代码注释与实际功能一致
- [ ] API文档与代码实现对应
```

#### 🧪 测试阶段 (测试开发完成后)
```bash
# 测试文档联动更新
- [ ] test/目录结构变更已更新到测试文档
- [ ] 新增测试文件已添加到覆盖率统计
- [ ] 测试命令说明保持最新
- [ ] 各项目README中的测试导航有效
```

#### 🚀 发布阶段 (版本发布前)
```bash
# 强制文档一致性检查
- [ ] 所有核心文档版本号统一
- [ ] 各级README文档导航100%有效
- [ ] API文档与Swagger输出对应
- [ ] 环境配置文档与实际配置文件同步
- [ ] 项目执行文档状态与实际进度一致
```

### 🎯 各级README联动责任

#### 📍 根目录README (本文档)
**联动责任**: 全局项目架构变更、新增子项目、文档结构调整
- 新增/删除子项目时更新项目架构图
- 文档分类调整时更新职责说明
- 全局导航链接维护

#### 📁 子目录README (各模块导航中心)
**联动责任**: 模块内技术变更、文档结构调整、功能更новм
- `AppServe/README.md`: 后端技术栈变更、API数量变化、测试状态更新
- `AppMobile/README.md`: Flutter版本升级、架构调整、开发规范更新
- `产品文档/README.md`: 产品版本发布、功能规范变更、业务流程调整
- `项目文档/README.md`: 项目阶段推进、里程碑达成、风险状态变化

### 🚨 文档联动违规处理

**轻微违规** (文档描述不准确、链接失效):
- 立即修复并记录到开发日志
- 验证修复效果并测试相关链接

**严重违规** (代码变更未同步文档、结构描述错误):
- 立即停止后续开发工作
- 完成所有相关文档更新
- 执行完整的文档一致性检查
- 通过review后方可继续

**持续违规** (多次出现同类问题):
- 深入分析文档管理流程问题
- 强化文档联动检查机制
- 建立更严格的自动化验证

### 📊 文档质量监控建议

**自动化检查** (推荐集成到CI/CD):
```bash
# 全局链接有效性检查
npm run docs:validate-all-links

# 文档结构一致性检查
npm run docs:check-structure-sync

# README导航完整性验证
npm run docs:verify-navigation
```

### 🎯 不同角色的使用路径

**产品经理/业务分析师**:

1. 查看 [项目文档](./项目文档/README.md) 了解开发进度
2. 维护 [产品文档](./产品文档/README.md) 中的业务规范
3. 跟踪项目执行情况和风险状态

**项目经理**:

1. 重点关注 [项目文档](./项目文档/README.md) 的执行情况
2. 监控项目风险和进度里程碑
3. 协调资源和解决阻塞问题

**开发团队**:

1. 遵循 [CLAUDE.md](./CLAUDE.md) 中的开发规范
2. 参考 [产品文档](./产品文档/README.md) 理解业务需求
3. 查阅 [对接文档](./对接文档/README.md) 进行前后端协作
4. 使用 [后端代码文档](./AppServe/README.md) 和 [前端代码文档](./AppMobile/README.md) 进行技术开发
5. 及时更新 [项目文档](./项目文档/README.md) 中的执行状态

**测试团队**:

1. 基于 [产品文档](./产品文档/README.md) 制定测试计划
2. 参考 [对接文档](./对接文档/README.md) 设计接口测试
3. 使用 [后端测试文档](./AppServe/docs/TESTING_GUIDE.md) 执行测试
4. 跟踪 [项目文档](./项目文档/README.md) 中的质量指标

## 🚀 快速开始

### 📋 项目状态查询

**重要**: 项目的详细进度、开发状态、风险评估等信息统一记录在项目执行文档中

👉 **查看项目状态**: [项目执行情况 v1.0.0](./项目文档/项目执行-v1.0.0.md)

### 🔧 环境要求

- Node.js >= 22.16.0
- PostgreSQL（阿里云 RDS）
- Redis
- Flutter SDK >= 3.32.0（前端开发）

### ⚡ 快速启动

```bash
# 后端开发
cd AppServe
npm run start:dev

# 前端开发
cd AppMobile
flutter run
```

### 📚 新人入门路径

1. **了解项目** → 阅读本 README 和项目概述
2. **查看进度** → 查阅 [项目执行情况](./项目文档/项目执行-v1.0.0.md) 了解当前状态
3. **掌握规范** → 学习 [CLAUDE.md](./CLAUDE.md) 开发规范
4. **理解业务** → 查阅 [产品文档 v1.0.0](./产品文档/v1.0.0/) 了解统一版本需求
5. **技术实现** → 查阅 [API接口文档](./对接文档/API接口文档.md) 了解完整的API接口技术规范
6. **环境搭建** → 参考 [后端开发指南](./AppServe/docs/DEVELOPMENT.md)
7. **开始开发** → 遵循 [代码结构说明](./AppServe/docs/CODE_STRUCTURE.md)

## 🛡️ 企业级质量保证机制

### 🔍 智能 Pre-commit 钩子系统

YGS 项目配置了企业级的智能质量检查机制，通过 Git pre-commit 钩子自动确保代码质量：

#### 🎯 **工作原理**

```bash
git commit -m "feat: 新功能"
    ↓
🚀 自动触发智能检查
    ↓
📁 分析文件变更范围:
    ├── AppServe/ → 后端质量检查
    ├── AppMobile/ → Flutter前端质量检查
    └── 其他文件 → 跳过检查
    ↓
✅ 检查通过 → 提交成功
❌ 检查失败 → 阻止提交
```

#### 🔧 **后端检查 (AppServe/)**

- **ESLint 检查**: 自动修复代码规范问题
- **TypeScript 检查**: 确保类型安全
- **只检查暂存文件**: 高效的增量检查
- **0 错误 0 警告**: 严格的企业级标准

#### 📱 **Flutter前端检查 (AppMobile/)**

- **Dart Analyze**: Dart代码静态分析
- **Flutter Lints**: Flutter官方代码规范
- **格式化检查**: dart format代码风格统一
- **增量检查**: 只检查变更文件

#### 🎯 **智能检测规则**

| 文件变更范围 | 执行检查 | 检查内容                       |
| ------------ | -------- | ------------------------------ |
| `AppServe/`  | 后端检查 | ESLint + TypeScript            |
| `AppMobile/` | Flutter前端检查 | Dart Analyze + Flutter Lints + Format |
| 其他文件     | 跳过检查 | 文档、配置等不需要代码检查     |
| 前后端同时   | 双重检查 | 分别执行对应检查               |

#### 📋 **使用方式**

```bash
# 完全自动化，无需额外操作
git add .
git commit -m "feat: 你的功能描述"
# 系统自动执行对应的质量检查

# 手动质量检查（可选）
cd AppServe && npm run lint:check    # 后端检查
cd AppMobile && flutter analyze       # Flutter前端检查
```

#### 🚀 **企业级优势**

- **预防性质量保证**: 低质量代码无法进入代码库
- **自动化流程**: 无需手动执行检查命令
- **高效增量检查**: 只检查变更文件，提高效率
- **智能路由**: 根据文件范围执行对应检查
- **统一标准**: 前后端都遵循企业级代码规范

### 🔄 **质量检查配置**

#### 后端配置 (AppServe/package.json)

```json
{
  "scripts": {
    "lint:check": "eslint \"{src,apps,libs,test}/**/*.ts\"",
    "type-check": "tsc --noEmit"
  },
  "lint-staged": {
    "*.ts": ["eslint --fix --max-warnings 0"]
  }
}
```

#### Flutter前端配置 (AppMobile/analysis_options.yaml)

```yaml
include: package:flutter_lints/flutter.yaml

analyzer:
  language:
    strict-casts: true
    strict-inference: true
    strict-raw-types: true
  
  errors:
    missing_required_param: error
    missing_return: error
    
linter:
  rules:
    prefer_const_constructors: true
    always_declare_return_types: true
    avoid_unnecessary_containers: true
```

### 📚 **相关文档**

- **开发规范**: [CLAUDE.md](./CLAUDE.md) - TypeScript 严格开发约束
- **🧪 企业级测试标准**: [CLAUDE.md#企业级测试开发标准](./CLAUDE.md#🧪-企业级测试开发标准强制执行) - 前后端通用测试架构与质量标准
- **后端开发**: [AppServe/README.md](./AppServe/README.md) - 后端开发指南
- **前端开发**: [AppMobile/README.md](./AppMobile/README.md) - Flutter移动端开发指南

> **💡 提示**: 这套质量保证机制确保了代码库始终保持企业级标准，是项目长期成功的重要保障。

### 📊 项目关键指标

- 📋 **整体进度**: 
  - 后端: 80% (Phase 1认证模块完成)
  - 前端: 40% (核心架构完成，UI开发中)
- 📋 **测试覆盖**: 
  - 后端: 85.28% (企业级测试体系完成，单元测试80.55%，集成测试85.28%，符合商业化发布标准)
  - 前端: 待开发 (架构已支持测试)
- 📋 **代码质量**: 
  - 后端: TypeScript 0错误0警告，ESLint 0错误0警告
  - 前端: Dart Analyze 0错误0警告，Flutter Lints 100%合规
- 📋 **技术债务**: 控制在最低水平，企业级标准执行

### 📈 项目里程碑

**详细的项目里程碑、进度状态、风险评估请查看**: [项目执行情况 v1.0.0](./项目文档/项目执行-v1.0.0.md)

## 💡 项目优势与创新

### 🎯 核心创新点

1. **人物点亮系统**: 行业首创的真实社交验证机制
2. **企业级架构**: 高并发、高可用、可扩展的技术架构
3. **完整业务闭环**: 从故事创作到社交连接的完整用户体验
4. **安全可靠**: 多层次的安全保护和数据保护机制

### 🚀 技术亮点

- **并发安全**: Redis 分布式锁保证数据一致性
- **类型安全**: 全面的 TypeScript 类型安全体系
- **性能优化**: 数据库查询优化和缓存策略
- **监控完善**: 健康检查、日志记录、错误追踪

## 📞 支持与反馈

### 🆘 获取帮助

- **文档问题**: 查阅对应模块的 README 导航
- **技术问题**: 参考代码文档中的技术指南
- **项目问题**: 联系项目管理团队
- **开发规范**: 遵循 CLAUDE.md 中的约束标准

### 📝 贡献指南

- **代码贡献**: 必须遵循 CLAUDE.md 中的开发规范
- **文档更新**: 代码变更必须同步更新相关文档
- **问题报告**: 及时记录到项目文档的问题管理中
- **经验分享**: 重要经验记录到项目执行文档中

---

**项目团队**: YGS 开发团队  
**技术架构**: 企业级微服务演进架构  
**商业模式**: 基于真实社交关系的内容平台

---

> 🏠 **快速导航**  
> 📋 [产品文档](./产品文档/README.md) | 🔧 [项目文档](./项目文档/README.md) | 🔧 [后端代码](./AppServe/README.md) | 📱 [前端代码](./AppMobile/README.md) | 🤖 [开发规范](./CLAUDE.md)
