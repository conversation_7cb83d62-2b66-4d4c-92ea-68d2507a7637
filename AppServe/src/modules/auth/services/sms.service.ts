import { Injectable, Logger, BadRequestException } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { EnhancedRedisService } from "../../../common/services/enhanced-redis.service";
import { DeviceFingerprintService } from "../../../common/services/device-fingerprint.service";
import { VerificationSecurityService } from "../../../common/services/verification-security.service";
import { AliyunSmsService } from "./aliyun-sms.service";
import type { Request } from "express";
import type Redis from "ioredis";

export interface SMSLimitInfo {
  lastSent?: number;
  dailyCount: number;
  hourlyCount: number;
  minuteCount: number;
  failureCount: number;
  blocked: boolean;
  blockReason?: string;
}

export interface SMSStatistics {
  totalSent: number;
  totalFailed: number;
  dailySuccessRate: number;
  averageResponseTime: number;
  lastAnalysisTime: number;
}

@Injectable()
export class SmsService {
  private readonly logger = new Logger(SmsService.name);
  private redis: Redis;

  constructor(
    private configService: ConfigService,
    private enhancedRedisService: EnhancedRedisService,
    private deviceFingerprintService: DeviceFingerprintService,
    private verificationSecurityService: VerificationSecurityService,
    private aliyunSmsService: AliyunSmsService,
  ) {
    this.redis = this.enhancedRedisService.getClient();
  }

  /**
   * 发送验证码 (增强安全版本)
   */
  async sendVerificationCode(
    phone: string,
    request?: Request,
  ): Promise<{ success: boolean; message: string }> {
    const startTime = Date.now();

    try {
      // 检查手机号格式
      if (!this.isValidPhoneNumber(phone)) {
        throw new BadRequestException("无效的手机号格式");
      }

      // 生成设备指纹 (如果提供了request)
      let deviceInfo = null;
      if (request) {
        deviceInfo =
          this.deviceFingerprintService.generateDeviceFingerprint(request);

        // 检验证安全性
        const securityCheck =
          await this.verificationSecurityService.checkVerificationAttempt(
            phone,
            deviceInfo.ip,
            deviceInfo.fingerprint,
          );

        if (!securityCheck.allowed) {
          throw new BadRequestException(securityCheck.reason);
        }
      }

      // 检查是否在黑名单
      const isBlacklisted = await this.isPhoneBlacklisted(phone);
      if (isBlacklisted) {
        this.logger.warn(`黑名单手机号尝试发送短信: ${phone}`);
        throw new BadRequestException("该手机号已被限制发送短信");
      }

      // 检查发送限制
      const limitCheck = await this.checkSendingLimits(phone);
      if (!limitCheck.allowed) {
        throw new BadRequestException(limitCheck.reason);
      }

      // 生成验证码
      const code = this.generateVerificationCode(phone);

      // 发送短信（智能路由）
      const sendResult = await this.sendSMS(phone, code);

      if (sendResult.success) {
        // 存储验证码
        await this.storeVerificationCode(phone, code);

        // 记录设备指纹信息（如果提供了request）
        if (request && deviceInfo) {
          await this.deviceFingerprintService.recordSmsDevice(
            phone,
            deviceInfo,
          );
        }

        // 更新发送统计
        await this.updateSendingLimits(phone, true);
        await this.updateStatistics(phone, true, Date.now() - startTime);

        this.logger.log(`验证码发送成功: ${phone}`);
        return { success: true, message: "验证码发送成功" };
      } else {
        // 更新失败统计
        await this.updateSendingLimits(phone, false);
        await this.updateStatistics(phone, false, Date.now() - startTime);

        throw new BadRequestException(sendResult.message);
      }
    } catch (error) {
      this.logger.error(
        `发送验证码失败: ${phone}`,
        error instanceof Error ? error.stack : String(error),
      );

      // 更新失败统计
      await this.updateSendingLimits(phone, false);
      await this.updateStatistics(phone, false, Date.now() - startTime);

      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException("验证码发送失败，请稍后重试");
    }
  }

  /**
   * 验证验证码 (增强安全版本)
   */
  async verifyCode(
    phone: string,
    code: string,
    request?: Request,
  ): Promise<boolean> {
    let deviceInfo = null;

    try {
      // 生成设备指纹 (如果提供了request)
      if (request) {
        deviceInfo =
          this.deviceFingerprintService.generateDeviceFingerprint(request);

        // 验证设备一致性
        const deviceConsistent =
          await this.deviceFingerprintService.validateSmsDevice(
            phone,
            deviceInfo,
          );
        if (!deviceConsistent) {
          this.logger.warn(
            `设备指纹不一致: ${phone} - ${deviceInfo.fingerprint}`,
          );
          // 设备不一致时记录但不直接拒绝，增加失败记录
          await this.verificationSecurityService.recordVerificationAttempt({
            phone,
            code,
            timestamp: Date.now(),
            ip: deviceInfo.ip,
            deviceFingerprint: deviceInfo.fingerprint,
            success: false,
          });
        }
      }

      const storedCode = await this.redis.get(`sms:code:${phone}`);

      if (!storedCode) {
        this.logger.warn(`验证码已过期或不存在: ${phone}`);

        // 记录验证尝试
        if (request && deviceInfo) {
          await this.verificationSecurityService.recordVerificationAttempt({
            phone,
            code,
            timestamp: Date.now(),
            ip: deviceInfo.ip,
            deviceFingerprint: deviceInfo.fingerprint,
            success: false,
          });
        }

        return false;
      }

      const isValid = storedCode === code;

      // 记录验证尝试
      if (request && deviceInfo) {
        await this.verificationSecurityService.recordVerificationAttempt({
          phone,
          code,
          timestamp: Date.now(),
          ip: deviceInfo.ip,
          deviceFingerprint: deviceInfo.fingerprint,
          success: isValid,
        });
      }

      if (isValid) {
        // 验证成功，删除验证码
        await this.redis.del(`sms:code:${phone}`);
        this.logger.log(`验证码验证成功: ${phone}`);
      } else {
        this.logger.warn(`验证码错误: ${phone}`);
      }

      return isValid;
    } catch (error) {
      this.logger.error(
        `验证码验证失败: ${phone}`,
        error instanceof Error ? error.stack : String(error),
      );

      // 记录验证失败
      if (request && deviceInfo) {
        await this.verificationSecurityService.recordVerificationAttempt({
          phone,
          code,
          timestamp: Date.now(),
          ip: deviceInfo.ip,
          deviceFingerprint: deviceInfo.fingerprint,
          success: false,
        });
      }

      return false;
    }
  }

  /**
   * 检查发送限制
   */
  private async checkSendingLimits(
    phone: string,
  ): Promise<{ allowed: boolean; reason?: string }> {
    const nodeEnv = this.configService.get("NODE_ENV");

    // 测试环境跳过所有限制检查
    if (nodeEnv === "test") {
      this.logger.debug(`[测试环境] 跳过短信发送限制检查: ${phone}`);
      return { allowed: true };
    }

    const limitInfo = await this.getSMSLimitInfo(phone);

    // 检查是否被阻止
    if (limitInfo.blocked) {
      return {
        allowed: false,
        reason: limitInfo.blockReason || "该手机号已被暂时限制",
      };
    }

    // 检查分钟级限制 (1分钟内只能发送1次)
    const now = Date.now();
    if (limitInfo.lastSent && now - limitInfo.lastSent < 60 * 1000) {
      const remaining = Math.ceil(
        (60 * 1000 - (now - limitInfo.lastSent)) / 1000,
      );
      return { allowed: false, reason: `请等待 ${remaining} 秒后再试` };
    }

    // 检查小时级限制 (1小时内最多5次)
    if (limitInfo.hourlyCount >= 5) {
      return { allowed: false, reason: "您今天发送次数过多，请稍后再试" };
    }

    // 检查日级限制 (每天最多10次)
    if (limitInfo.dailyCount >= 10) {
      return { allowed: false, reason: "您今天发送次数已达上限，请明天再试" };
    }

    // 检查失败次数 (连续失败5次将被暂时阻止)
    if (limitInfo.failureCount >= 5) {
      await this.blockPhone(phone, "连续发送失败次数过多", 24 * 60 * 60); // 阻止24小时
      return { allowed: false, reason: "发送失败次数过多，已暂时限制该手机号" };
    }

    return { allowed: true };
  }

  /**
   * 获取SMS限制信息
   */
  private async getSMSLimitInfo(phone: string): Promise<SMSLimitInfo> {
    const pipe = this.redis.pipeline();
    const now = Date.now();
    const today = new Date().toDateString();
    const currentHour = new Date().getHours();

    pipe.get(`sms:last_sent:${phone}`);
    pipe.get(`sms:daily:${phone}:${today}`);
    pipe.get(`sms:hourly:${phone}:${today}:${currentHour}`);
    pipe.get(`sms:minute:${phone}:${Math.floor(now / 60000)}`);
    pipe.get(`sms:failures:${phone}`);
    pipe.get(`sms:blocked:${phone}`);

    const results = await pipe.exec();

    if (!results) {
      throw new Error("Redis pipeline execution failed");
    }

    return {
      lastSent: results[0]?.[1] ? parseInt(results[0][1] as string) : undefined,
      dailyCount: parseInt((results[1] && (results[1][1] as string)) || "0"),
      hourlyCount: parseInt((results[2] && (results[2][1] as string)) || "0"),
      minuteCount: parseInt((results[3] && (results[3][1] as string)) || "0"),
      failureCount: parseInt((results[4] && (results[4][1] as string)) || "0"),
      blocked: !!results[5]?.[1],
      blockReason: results[5] && (results[5][1] as string),
    };
  }

  /**
   * 更新发送限制
   */
  private async updateSendingLimits(
    phone: string,
    success: boolean,
  ): Promise<void> {
    const pipe = this.redis.pipeline();
    const now = Date.now();
    const today = new Date().toDateString();
    const currentHour = new Date().getHours();
    const currentMinute = Math.floor(now / 60000);

    if (success) {
      // 更新成功发送记录
      pipe.set(`sms:last_sent:${phone}`, now, "EX", 24 * 60 * 60);
      pipe.incr(`sms:daily:${phone}:${today}`);
      pipe.expire(`sms:daily:${phone}:${today}`, 24 * 60 * 60);
      pipe.incr(`sms:hourly:${phone}:${today}:${currentHour}`);
      pipe.expire(`sms:hourly:${phone}:${today}:${currentHour}`, 60 * 60);
      pipe.incr(`sms:minute:${phone}:${currentMinute}`);
      pipe.expire(`sms:minute:${phone}:${currentMinute}`, 60);

      // 重置失败计数
      pipe.del(`sms:failures:${phone}`);
    } else {
      // 增加失败计数
      pipe.incr(`sms:failures:${phone}`);
      pipe.expire(`sms:failures:${phone}`, 60 * 60); // 1小时过期
    }

    await pipe.exec();
  }

  /**
   * 阻止手机号
   */
  private async blockPhone(
    phone: string,
    reason: string,
    durationSeconds: number,
  ): Promise<void> {
    await this.redis.set(`sms:blocked:${phone}`, reason, "EX", durationSeconds);
    this.logger.warn(
      `手机号已被阻止: ${phone}, 原因: ${reason}, 持续时间: ${durationSeconds}秒`,
    );
  }

  /**
   * 检查手机号是否在黑名单
   */
  private async isPhoneBlacklisted(phone: string): Promise<boolean> {
    const result = await this.redis.sismember("sms:blacklist", phone);
    return result === 1;
  }

  /**
   * 更新统计信息
   */
  private async updateStatistics(
    phone: string,
    success: boolean,
    responseTime: number,
  ): Promise<void> {
    const today = new Date().toDateString();
    const pipe = this.redis.pipeline();

    pipe.incr(`sms:stats:total_sent:${today}`);
    pipe.expire(`sms:stats:total_sent:${today}`, 7 * 24 * 60 * 60); // 保存7天

    if (success) {
      pipe.incr(`sms:stats:total_success:${today}`);
      pipe.expire(`sms:stats:total_success:${today}`, 7 * 24 * 60 * 60);
    } else {
      pipe.incr(`sms:stats:total_failed:${today}`);
      pipe.expire(`sms:stats:total_failed:${today}`, 7 * 24 * 60 * 60);
    }

    // 记录响应时间
    pipe.lpush(`sms:stats:response_times:${today}`, responseTime);
    pipe.expire(`sms:stats:response_times:${today}`, 24 * 60 * 60);
    pipe.ltrim(`sms:stats:response_times:${today}`, 0, 999); // 只保留最近1000条

    await pipe.exec();
  }

  /**
   * 获取统计信息
   */
  async getStatistics(): Promise<SMSStatistics> {
    const today = new Date().toDateString();
    const pipe = this.redis.pipeline();

    pipe.get(`sms:stats:total_sent:${today}`);
    pipe.get(`sms:stats:total_success:${today}`);
    pipe.get(`sms:stats:total_failed:${today}`);
    pipe.lrange(`sms:stats:response_times:${today}`, 0, -1);

    const results = await pipe.exec();

    if (!results) {
      throw new Error("Redis pipeline execution failed");
    }

    const totalSent = parseInt(
      (results[0] && (results[0][1] as string)) || "0",
    );
    const totalSuccess = parseInt(
      (results[1] && (results[1][1] as string)) || "0",
    );
    const totalFailed = parseInt(
      (results[2] && (results[2][1] as string)) || "0",
    );
    const responseTimes = (
      (results[3] && (results[3][1] as string[])) ||
      []
    ).map((t) => parseInt(t));

    const successRate = totalSent > 0 ? (totalSuccess / totalSent) * 100 : 0;
    const averageResponseTime =
      responseTimes.length > 0
        ? responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length
        : 0;

    return {
      totalSent,
      totalFailed,
      dailySuccessRate: parseFloat(successRate.toFixed(2)),
      averageResponseTime: parseFloat(averageResponseTime.toFixed(2)),
      lastAnalysisTime: Date.now(),
    };
  }

  /**
   * 验证手机号格式
   */
  private isValidPhoneNumber(phone: string): boolean {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  }

  /**
   * 生成验证码 (4位，区分Mock和真实环境)
   */
  private generateVerificationCode(phone: string): string {
    const nodeEnv = this.configService.get("NODE_ENV");
    const isVipPhone = this.aliyunSmsService.getVipTestPhones().includes(phone);

    // VIP账户或生产环境：生成真实4位验证码
    if (isVipPhone || nodeEnv === "production") {
      const realCode = Math.floor(1000 + Math.random() * 9000).toString();
      this.logger.debug(`[真实验证码] ${phone}: ${realCode}`);
      return realCode;
    }

    // 普通测试：使用固定Mock验证码(截取4位)
    const mockCode = this.configService
      .get("SMS_MOCK_CODE", "123456")
      .substring(0, 4);
    this.logger.debug(`[模拟验证码] ${phone}: ${mockCode}`);
    return mockCode;
  }

  /**
   * 存储验证码
   */
  private async storeVerificationCode(
    phone: string,
    code: string,
  ): Promise<void> {
    const ttl = 5 * 60; // 5分钟过期
    await this.redis.set(`sms:code:${phone}`, code, "EX", ttl);
  }

  /**
   * 发送短信（智能路由：阿里云真实短信 + Mock混合模式）
   */
  private async sendSMS(
    phone: string,
    code: string,
  ): Promise<{ success: boolean; message: string }> {
    try {
      // 使用阿里云短信服务的智能路由功能
      const result = await this.aliyunSmsService.sendVerificationCode(
        phone,
        code,
      );

      return {
        success: result.success,
        message: result.message,
      };
    } catch (error) {
      this.logger.error(
        `智能短信发送失败: ${phone}`,
        error instanceof Error ? error.stack : String(error),
      );
      return {
        success: false,
        message:
          error instanceof Error ? error.message : "短信发送失败，请稍后重试",
      };
    }
  }

  /**
   * 获取当前短信服务状态
   */
  async getSmsServiceStatus(): Promise<{
    mode: string;
    environment: string;
    vipPhones: string[];
    clientStatus: string;
    limitInfo?: SMSLimitInfo;
  }> {
    const aliyunStatus = this.aliyunSmsService.getCurrentSmsMode();

    return {
      ...aliyunStatus,
      limitInfo: undefined, // 可以选择性返回限制信息
    };
  }

  /**
   * 添加VIP测试手机号
   */
  addVipTestPhone(phone: string): void {
    this.aliyunSmsService.addVipTestPhone(phone);
    this.logger.log(`✅ 已添加VIP测试手机号: ${phone}`);
  }

  /**
   * 移除VIP测试手机号
   */
  removeVipTestPhone(phone: string): void {
    this.aliyunSmsService.removeVipTestPhone(phone);
    this.logger.log(`❌ 已移除VIP测试手机号: ${phone}`);
  }
}
