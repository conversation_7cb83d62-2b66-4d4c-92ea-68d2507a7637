import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { Logger } from "@nestjs/common";
import { ServiceUnavailableException } from "@nestjs/common";
import { HealthController } from "./health.controller";
import { HealthService } from "./health.service";
import type { HealthCheckResult } from "@nestjs/terminus";

describe("HealthController", () => {
  let controller: HealthController;
  let mockHealthService: jest.Mocked<HealthService>;

  // Mock数据
  const mockBasicHealthResult: HealthCheckResult = {
    status: "ok",
    info: {
      database: {
        status: "up",
        message: "数据库连接正常",
      },
      redis: {
        status: "up",
        message: "Redis连接正常",
      },
    },
    error: {},
    details: {
      database: {
        status: "up",
        message: "数据库连接正常",
      },
      redis: {
        status: "up",
        message: "Redis连接正常",
      },
    },
  };

  const mockDetailedHealthResult: HealthCheckResult = {
    status: "ok",
    info: {
      database: {
        status: "up",
        message: "数据库连接正常",
        responseTime: 15,
      },
      redis: {
        status: "up",
        message: "Redis连接正常",
        responseTime: 8,
      },
      memory: {
        status: "up",
        message: "内存使用正常",
        usage: "256MB / 2GB",
        percentage: 12.5,
      },
      disk: {
        status: "up",
        message: "磁盘空间充足",
        usage: "45GB / 100GB",
        percentage: 45,
      },
      uptime: {
        status: "up",
        message: "应用运行时间",
        value: "2天3小时15分钟",
      },
    },
    error: {},
    details: {
      database: {
        status: "up",
        message: "数据库连接正常",
        responseTime: 15,
      },
      redis: {
        status: "up",
        message: "Redis连接正常",
        responseTime: 8,
      },
      memory: {
        status: "up",
        message: "内存使用正常",
        usage: "256MB / 2GB",
        percentage: 12.5,
      },
      disk: {
        status: "up",
        message: "磁盘空间充足",
        usage: "45GB / 100GB",
        percentage: 45,
      },
      uptime: {
        status: "up",
        message: "应用运行时间",
        value: "2天3小时15分钟",
      },
    },
  };

  const mockUnhealthyResult: HealthCheckResult = {
    status: "error",
    info: {
      redis: {
        status: "up",
        message: "Redis连接正常",
      },
    },
    error: {
      database: {
        status: "down",
        message: "数据库连接失败",
        error: "Connection timeout",
      },
    },
    details: {
      database: {
        status: "down",
        message: "数据库连接失败",
        error: "Connection timeout",
      },
      redis: {
        status: "up",
        message: "Redis连接正常",
      },
    },
  };

  beforeEach(async () => {
    const mockServiceMethods = {
      getHealth: jest.fn(),
      getDetailedHealth: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [HealthController],
      providers: [
        {
          provide: HealthService,
          useValue: mockServiceMethods,
        },
      ],
    }).compile();

    controller = module.get<HealthController>(HealthController);
    mockHealthService = module.get(HealthService) as jest.Mocked<HealthService>;

    // Mock Logger to avoid console output during tests
    jest.spyOn(Logger.prototype, "log").mockImplementation(() => {});
    jest.spyOn(Logger.prototype, "error").mockImplementation(() => {});
    jest.spyOn(Logger.prototype, "debug").mockImplementation(() => {});
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("getHealth", () => {
    it("应该返回基础健康检查结果", async () => {
      mockHealthService.getHealth.mockResolvedValue(mockBasicHealthResult);

      const result = await controller.getHealth();

      expect(mockHealthService.getHealth).toHaveBeenCalledTimes(1);
      expect(result).toEqual(mockBasicHealthResult);
      expect(result.status).toBe("ok");
      expect(result.info.database.status).toBe("up");
      expect(result.info.redis.status).toBe("up");
    });

    it("应该处理健康检查失败的情况", async () => {
      mockHealthService.getHealth.mockResolvedValue(mockUnhealthyResult);

      const result = await controller.getHealth();

      expect(result.status).toBe("error");
      expect(result.error.database.status).toBe("down");
      expect(result.info.redis.status).toBe("up");
    });

    it("应该记录健康检查异常", async () => {
      const error = new ServiceUnavailableException("服务暂时不可用");
      mockHealthService.getHealth.mockRejectedValue(error);

      await expect(controller.getHealth()).rejects.toThrow(error);

      expect(Logger.prototype.error).toHaveBeenCalledWith(
        "健康检查失败",
        error.stack,
      );
    });

    it("应该处理数据库连接超时", async () => {
      const timeoutResult: HealthCheckResult = {
        status: "error",
        info: {},
        error: {
          database: {
            status: "down",
            message: "数据库连接超时",
            timeout: 5000,
          },
        },
        details: {
          database: {
            status: "down",
            message: "数据库连接超时",
            timeout: 5000,
          },
        },
      };

      mockHealthService.getHealth.mockResolvedValue(timeoutResult);

      const result = await controller.getHealth();

      expect(result.status).toBe("error");
      expect(result.error.database.message).toBe("数据库连接超时");
    });

    it("应该处理Redis连接失败", async () => {
      const redisFailResult: HealthCheckResult = {
        status: "error",
        info: {
          database: {
            status: "up",
            message: "数据库连接正常",
          },
        },
        error: {
          redis: {
            status: "down",
            message: "Redis连接失败",
            error: "ECONNREFUSED",
          },
        },
        details: {
          database: {
            status: "up",
            message: "数据库连接正常",
          },
          redis: {
            status: "down",
            message: "Redis连接失败",
            error: "ECONNREFUSED",
          },
        },
      };

      mockHealthService.getHealth.mockResolvedValue(redisFailResult);

      const result = await controller.getHealth();

      expect(result.status).toBe("error");
      expect(result.error.redis.status).toBe("down");
      expect(result.info.database.status).toBe("up");
    });
  });

  describe("getDetailedHealth", () => {
    it("应该返回详细健康检查结果", async () => {
      mockHealthService.getDetailedHealth.mockResolvedValue(
        mockDetailedHealthResult,
      );

      const result = await controller.getDetailedHealth();

      expect(mockHealthService.getDetailedHealth).toHaveBeenCalledTimes(1);
      expect(result).toEqual(mockDetailedHealthResult);
      expect(result.status).toBe("ok");
      expect(result.info.memory).toBeDefined();
      expect(result.info.disk).toBeDefined();
      expect(result.info.uptime).toBeDefined();
    });

    it("应该包含性能指标", async () => {
      mockHealthService.getDetailedHealth.mockResolvedValue(
        mockDetailedHealthResult,
      );

      const result = await controller.getDetailedHealth();

      expect(result.info.database.responseTime).toBe(15);
      expect(result.info.redis.responseTime).toBe(8);
      expect(result.info.memory.percentage).toBe(12.5);
      expect(result.info.disk.percentage).toBe(45);
    });

    it("应该处理内存使用率过高的情况", async () => {
      const highMemoryResult: HealthCheckResult = {
        ...mockDetailedHealthResult,
        status: "error",
        error: {
          memory: {
            status: "down",
            message: "内存使用率过高",
            usage: "1.8GB / 2GB",
            percentage: 90,
          },
        },
      };

      mockHealthService.getDetailedHealth.mockResolvedValue(highMemoryResult);

      const result = await controller.getDetailedHealth();

      expect(result.status).toBe("error");
      expect(result.error.memory.percentage).toBe(90);
      expect(result.error.memory.message).toBe("内存使用率过高");
    });

    it("应该处理磁盘空间不足的情况", async () => {
      const lowDiskResult: HealthCheckResult = {
        ...mockDetailedHealthResult,
        status: "error",
        error: {
          disk: {
            status: "down",
            message: "磁盘空间不足",
            usage: "95GB / 100GB",
            percentage: 95,
          },
        },
      };

      mockHealthService.getDetailedHealth.mockResolvedValue(lowDiskResult);

      const result = await controller.getDetailedHealth();

      expect(result.status).toBe("error");
      expect(result.error.disk.percentage).toBe(95);
      expect(result.error.disk.message).toBe("磁盘空间不足");
    });

    it("应该记录详细健康检查异常", async () => {
      const error = new Error("详细检查服务异常");
      mockHealthService.getDetailedHealth.mockRejectedValue(error);

      await expect(controller.getDetailedHealth()).rejects.toThrow(error);

      expect(Logger.prototype.error).toHaveBeenCalledWith(
        "详细健康检查失败",
        error.stack,
      );
    });

    it("应该处理部分服务降级的情况", async () => {
      const degradedResult: HealthCheckResult = {
        status: "ok",
        info: {
          database: {
            status: "up",
            message: "数据库连接正常",
            responseTime: 150, // 响应时间较慢但正常
          },
          redis: {
            status: "up",
            message: "Redis连接正常",
            responseTime: 50, // 响应时间较慢
          },
          memory: {
            status: "up",
            message: "内存使用率较高但可接受",
            usage: "1.5GB / 2GB",
            percentage: 75,
          },
        },
        error: {},
        details: {
          database: {
            status: "up",
            message: "数据库连接正常",
            responseTime: 150,
          },
          redis: {
            status: "up",
            message: "Redis连接正常",
            responseTime: 50,
          },
          memory: {
            status: "up",
            message: "内存使用率较高但可接受",
            usage: "1.5GB / 2GB",
            percentage: 75,
          },
        },
      };

      mockHealthService.getDetailedHealth.mockResolvedValue(degradedResult);

      const result = await controller.getDetailedHealth();

      expect(result.status).toBe("ok");
      expect(result.info.database.responseTime).toBe(150);
      expect(result.info.memory.percentage).toBe(75);
    });

    it("应该包含应用运行时间信息", async () => {
      mockHealthService.getDetailedHealth.mockResolvedValue(
        mockDetailedHealthResult,
      );

      const result = await controller.getDetailedHealth();

      expect(result.info.uptime).toBeDefined();
      expect(result.info.uptime.status).toBe("up");
      expect(result.info.uptime.value).toBe("2天3小时15分钟");
    });
  });

  it("应该正确注入HealthService", () => {
    expect(controller).toBeDefined();
    expect(mockHealthService).toBeDefined();
  });
});
