# YGS v1.0.0 企业级种子数据使用指南

> **文档版本**: v1.0.0  
> **发布日期**: 2025年7月28日  
> **适用范围**: YGS有故事平台前后端开发、测试、部署  
> **数据库环境**: 阿里云RDS PostgreSQL (生产级)  
> **总记录数**: 228条  
> **数据完整性**: ✅ 100%覆盖15个核心业务模块

---

## 📋 目录

- [1. 概述](#1-概述)
- [2. 数据架构总览](#2-数据架构总览)
- [3. 测试账号体系](#3-测试账号体系)
- [4. 核心业务数据](#4-核心业务数据)
- [5. API接口对照](#5-api接口对照)
- [6. 前端联调指南](#6-前端联调指南)
- [7. 数据管理命令](#7-数据管理命令)
- [8. 常见问题解答](#8-常见问题解答)

---

## 1. 概述

### 1.1 文档目的

本文档为YGS v1.0.0提供完整的种子数据使用说明，帮助开发团队高效利用企业级测试数据进行前端联调、API验证、功能测试等开发工作。

### 1.2 数据特点

- ✅ **企业级标准**: 严格按照生产环境标准设计
- ✅ **完整业务覆盖**: 涵盖15个核心业务模块，128+个API接口
- ✅ **真实数据关联**: 所有数据间关系真实可用，支持复杂业务场景
- ✅ **安全合规**: 密码BCrypt加密，敏感信息妥善处理
- ✅ **性能友好**: 数据量适中，支持性能测试和优化

### 1.3 使用场景

| 使用场景 | 适用团队 | 主要价值 |
|----------|----------|----------|
| **前端联调** | 前端开发团队 | 提供真实API响应数据，验证UI交互 |
| **接口测试** | 后端开发团队 | 验证API功能正确性和数据一致性 |
| **功能测试** | QA测试团队 | 提供完整业务场景数据，支持端到端测试 |
| **性能测试** | 运维团队 | 提供基准数据，支持负载和压力测试 |
| **演示展示** | 产品团队 | 提供丰富示例数据，支持产品演示 |

---

## 2. 数据架构总览

### 2.1 数据库环境

```yaml
数据库类型: PostgreSQL 13+
部署环境: 阿里云RDS (企业级)
数据库名: ygs-prod
字符集: UTF-8
时区: Asia/Shanghai (UTC+8)
总表数: 15个核心业务表
总记录数: 228条
```

### 2.2 表结构统计

| 排名 | 表名 | 记录数 | 业务模块 | 核心功能 |
|------|------|--------|----------|----------|
| 1 | `timeline_events` | 34条 | 个人时间线 | 用户生活轨迹记录 |
| 2 | `notifications` | 23条 | 通知系统 | 消息推送管理 |
| 3 | `share_access_logs` | 21条 | 分享统计 | 访问行为分析 |
| 4 | `themes` | 20条 | 内容分类 | 故事主题管理 |
| 5 | `bookmarks` | 20条 | 收藏系统 | 内容收藏管理 |
| 6 | `user_follows` | 16条 | 社交关系 | 用户关注体系 |
| 7 | `comments` | 15条 | 评论系统 | 内容互动管理 |
| 8 | `light_requests` | 13条 | **点亮核心** | 人物点亮申请 |
| 9 | `characters` | 12条 | 内容管理 | 故事人物数据 |
| 10 | `shares` | 11条 | 分享系统 | 内容分享链接 |
| 11 | `friend_groups` | 11条 | 社交管理 | 好友分组功能 |
| 12 | `users` | 10条 | **用户核心** | 账户管理体系 |
| 13 | `stories` | 9条 | **内容核心** | 故事内容管理 |
| 14 | `comment_likes` | 8条 | 互动系统 | 评论点赞功能 |
| 15 | `character_lightings` | 5条 | **点亮核心** | 成功点亮关系 |

### 2.3 业务模块覆盖

```mermaid
graph TB
    A[用户管理] --> B[内容创作]
    B --> C[人物点亮]
    C --> D[社交互动]
    D --> E[内容分享]
    E --> F[通知推送]
    
    A --> A1[注册登录]
    A --> A2[权限管理]
    A --> A3[VIP体系]
    
    B --> B1[故事创建]
    B --> B2[主题分类]
    B --> B3[权限控制]
    
    C --> C1[人物管理]
    C --> C2[点亮申请]
    C --> C3[身份验证]
    
    D --> D1[关注系统]
    D --> D2[评论互动]
    D --> D3[好友分组]
    
    E --> E1[分享链接]
    E --> E2[访问统计]
    E --> E3[权限控制]
    
    F --> F1[系统通知]
    F --> F2[互动提醒]
    F --> F3[情绪保护]
```

---

## 3. 测试账号体系

### 3.1 账号分类设计

我们提供6个不同角色的测试账号，涵盖平台所有用户类型：

| 账号ID | 角色类型 | 手机号 | 密码 | 昵称 | 用户特征 |
|--------|----------|--------|------|------|----------|
| 1 | **创作者** | 13800138001 | Test123456! | 明远说 | 活跃内容创建者，已发布多个故事 |
| 2 | **点亮者** | 13800138002 | Test123456! | 小红姐姐 | 点亮达人，成功点亮3个人物 |
| 3 | **教师** | 13800138003 | Test123456! | 老王讲故事 | 教育工作者，专业内容创作 |
| 4 | **读者** | 13800138004 | Test123456! | 芳芳爱读书 | 活跃读者，评论收藏用户 |
| 5 | **VIP用户** | 16675158665 | Test123456! | 陈总 | 付费用户，高级功能权限 |
| 6 | **测试账号** | 13800138009 | Test123456! | 系统测试 | 功能测试专用账号 |

### 3.2 账号权限说明

#### 🎯 创作者账号 (明远说)
- **核心特征**: 平台主要内容贡献者
- **已创建内容**: 3个故事，涵盖不同主题和权限级别
- **人物数据**: 6个人物，其中2个已被成功点亮
- **社交关系**: 5个关注者，活跃的社区互动
- **适用测试**: 内容创作、人物管理、作者权限验证

#### 💡 点亮者账号 (小红姐姐)
- **核心特征**: 平台点亮功能专家用户
- **点亮成就**: 成功点亮3个人物，获得"点亮达人"称号
- **社交活跃度**: 16条评论，高互动频率
- **收藏行为**: 精选3个高质量故事收藏
- **适用测试**: 点亮功能、身份验证、社交互动

#### 👨‍🏫 教师账号 (老王讲故事)
- **核心特征**: 教育工作者，代表专业用户群体
- **内容特色**: 教育主题故事，师生情谊内容
- **被点亮经历**: 学生通过点亮系统找到他
- **社区角色**: 资深用户，正面评论引导者
- **适用测试**: 专业用户行为、教育内容管理

#### 📚 读者账号 (芳芳爱读书)
- **核心特征**: 典型内容消费者
- **阅读行为**: 高频阅读，积极收藏和评论
- **社交互动**: 温和的评论风格，正面反馈
- **成长轨迹**: 从新用户到活跃读者的完整过程
- **适用测试**: 读者体验、内容消费行为

#### 👑 VIP账号 (陈总)
- **核心特征**: 付费用户，享有高级功能
- **VIP权益**: 每日50次AI配额，优先客服支持
- **商业视角**: 商业案例收藏，投资人角度内容
- **高端功能**: 访问VIP专属功能和内容
- **适用测试**: VIP功能验证、付费用户体验

#### 🔧 系统测试账号
- **核心特征**: 功能测试专用
- **特殊权限**: 系统级测试权限，不限功能使用
- **数据纯净**: 专门用于自动化测试和功能验证
- **适用测试**: 自动化测试、功能回归测试

### 3.3 登录方式

#### 手机验证码登录 (推荐)
```javascript
// 测试用验证码：123456 (所有测试账号通用)
const loginData = {
  phone: "13800138001",
  verificationCode: "123456"
}
```

#### 密码登录
```javascript
const loginData = {
  loginId: "13800138001", // 支持手机号或用户名
  password: "Test123456!"
}
```

---

## 4. 核心业务数据

### 4.1 故事内容系统

#### 4.1.1 故事数据概览 (9个故事)

| 故事ID | 标题 | 作者 | 主题 | 权限级别 | 人物数量 | 点亮状态 |
|--------|------|------|------|----------|----------|----------|
| 1 | 《那年夏天，我们约定十年后再聚》 | 明远说 | 友情 | public | 3个 | 2个已点亮 |
| 2 | 《李明，还记得那个雨夜吗》 | 明远说 | 友情 | friends | 1个 | 1个已点亮 |
| 3 | 《创业路上，感谢有你们》 | 明远说 | 奋斗 | public | 2个 | 未点亮 |
| 4 | 《小雨，你现在过得还好吗》 | 老王讲故事 | 师生 | public | 1个 | 1个已点亮 |
| 5 | 《三十年教书，我最后悔的一件事》 | 老王讲故事 | 反思 | friends | 1个 | 未点亮 |
| 6 | 《那些年，我们一起追过的剧》 | 用户C | 青春 | public | 1个 | 1个已点亮 |
| 7 | 《妈妈，对不起，我撒谎了》 | 私密用户 | 亲情 | private | 2个 | 未点亮 |
| 8 | 《写给未来自己的信》 | 私密用户 | 成长 | private | 1个 | 未点亮 |
| 9 | 《毕业十年，我们都还好吗》 | 测试用户 | 回忆 | public | 0个 | - |

#### 4.1.2 主题分类系统 (20个主题)

```yaml
情感类主题 (4个):
  - 友情: "#4CAF50" - 朋友间珍贵情谊
  - 亲情: "#FF9800" - 家人间温暖故事  
  - 爱情: "#E91E63" - 浪漫爱情记录
  - 感动: "#9C27B0" - 触动心灵瞬间

关系类主题 (3个):
  - 师生: "#2196F3" - 师生珍贵情谊
  - 同事: "#607D8B" - 职场人际关系
  - 邻里: "#8BC34A" - 社区生活故事

成长类主题 (3个):
  - 成长: "#FF5722" - 人生蜕变时刻
  - 奋斗: "#795548" - 追求梦想历程
  - 挫折: "#9E9E9E" - 困难中的学习

日常类主题 (4个):
  - 日常: "#FFC107" - 平凡生活美好
  - 旅行: "#00BCD4" - 行万里路收获
  - 美食: "#FF9800" - 味蕾记忆文化
  - 爱好: "#3F51B5" - 兴趣爱好分享

特殊类主题 (6个):
  - 节日: "#F44336" - 节日庆祝回忆
  - 毕业: "#1976D2" - 青春岁月记忆
  - 工作: "#424242" - 职场生活故事
  - 宠物: "#8BC34A" - 萌宠陪伴时光
  - 回忆: "#9C27B0" - 时光美好印记
  - 感恩: "#FF6F00" - 感谢珍惜故事
```

### 4.2 人物点亮系统 (核心功能)

#### 4.2.1 人物数据 (12个人物)

| 人物ID | 姓名 | 故事来源 | 创建者 | 点亮状态 | 点亮者 | 关系类型 |
|--------|------|----------|--------|----------|--------|----------|
| 1 | 李明 | 《那年夏天，我们约定十年后再聚》 | 明远说 | ✅ 已点亮 | 小红姐姐 | 大学室友 |
| 2 | 王强 | 《那年夏天，我们约定十年后再聚》 | 明远说 | ❌ 未点亮 | - | 大学室友 |
| 3 | 赵华 | 《那年夏天，我们约定十年后再聚》 | 明远说 | ✅ 已点亮 | 发现自己 | 大学室友 |
| 4 | 明哥 | 《李明，还记得那个雨夜吗》 | 明远说 | ✅ 已点亮 | 小红姐姐 | 好友 |
| 5 | 小雨 | 《小雨，你现在过得还好吗》 | 老王讲故事 | ✅ 已点亮 | 发现自己 | 师生关系 |
| 6 | 晓晓 | 《那些年，我们一起追过的剧》 | 用户C | ✅ 已点亮 | 小红姐姐 | 大学室友 |
| 7 | 张伟 | 《创业路上，感谢有你们》 | 明远说 | ❌ 未点亮 | - | 创业伙伴 |
| 8 | 刘敏 | 《创业路上，感谢有你们》 | 明远说 | ❌ 未点亮 | - | 创业伙伴 |
| 9 | 小军 | 《三十年教书，我最后悔的一件事》 | 老王讲故事 | ❌ 未点亮 | - | 师生关系 |
| 10 | 妈妈 | 《妈妈，对不起，我撒谎了》 | 私密用户 | ❌ 未点亮 | - | 母子关系 |
| 11 | 自己 | 《妈妈，对不起，我撒谎了》 | 私密用户 | ❌ 未点亮 | - | 自述 |
| 12 | 未来的自己 | 《写给未来自己的信》 | 私密用户 | ❌ 未点亮 | - | 自述 |

#### 4.2.2 点亮申请流程 (13个申请)

```yaml
申请状态分布:
  - pending (待处理): 3个申请
  - approved (已批准): 5个申请  
  - rejected (已拒绝): 2个申请
  - expired (已过期): 3个申请

验证方式:
  - 手机号验证: 8个申请
  - 身份信息验证: 3个申请
  - 关系证明验证: 2个申请

处理时效:
  - 24小时内处理: 8个申请
  - 48小时内处理: 3个申请
  - 超时未处理: 2个申请
```

#### 4.2.3 成功点亮案例 (5个关系)

| 点亮ID | 故事标题 | 人物名称 | 点亮者 | 关系类型 | 验证方式 | 点亮时间 |
|--------|----------|----------|--------|----------|----------|----------|
| 1 | 《那年夏天，我们约定十年后再聚》 | 李明 | 小红姐姐 | 大学室友 | 手机验证 | 28天前 |
| 2 | 《李明，还记得那个雨夜吗》 | 明哥 | 小红姐姐 | 好友 | 关系证明 | 24天前 |
| 3 | 《小雨，你现在过得还好吗》 | 小雨 | 发现自己 | 师生关系 | 身份验证 | 14天前 |
| 4 | 《那年夏天，我们约定十年后再聚》 | 赵华 | 发现自己 | 大学室友 | 手机验证 | 19天前 |
| 5 | 《那些年，我们一起追过的剧》 | 晓晓 | 小红姐姐 | 大学室友 | 关系证明 | 9天前 |

### 4.3 社交互动系统

#### 4.3.1 关注关系网络 (16个关系)

```yaml
关注关系分析:
  核心用户节点:
    - 明远说: 5个关注者 (内容创作者影响力)
    - 小红姐姐: 3个关注者 (活跃用户吸引力)
    - 老王讲故事: 4个关注者 (教育内容价值)
    
  关注类型:
    - 普通关注: 12个
    - 特别关注: 4个 (亲密好友标记)
    
  关注状态:
    - 活跃关注: 14个
    - 静默关注: 2个
```

#### 4.3.2 好友分组管理 (11个分组)

| 分组ID | 分组名称 | 拥有者 | 成员数量 | 分组类型 |
|--------|----------|--------|----------|----------|
| 1 | 大学室友 | 明远说 | 3人 | 同学关系 |
| 2 | 创业伙伴 | 明远说 | 2人 | 工作关系 |
| 3 | 我的学生们 | 老王讲故事 | 4人 | 师生关系 |
| 4 | 点亮朋友 | 小红姐姐 | 3人 | 平台好友 |
| 5 | 书友会 | 芳芳爱读书 | 2人 | 兴趣关系 |
| 6 | 商业伙伴 | 陈总 | 3人 | 商务关系 |
| 7 | 医院同事 | 发现自己 | 2人 | 同事关系 |
| 8 | 家人亲戚 | 私密用户 | 3人 | 家庭关系 |
| 9 | 新朋友 | 小新用户 | 1人 | 新用户分组 |
| 10 | 问题用户 | 系统测试 | 1人 | 测试分组 |
| 11 | VIP朋友 | 陈总 | 2人 | 高端关系 |

#### 4.3.3 评论互动数据 (15条评论 + 8个点赞)

```yaml
评论分布:
  按故事分类:
    - 《那年夏天，我们约定十年后再聚》: 5条评论 (最热门)
    - 《小雨，你现在过得还好吗》: 4条评论 (师生互动)
    - 《三十年教书，我最后悔的一件事》: 2条评论
    - 《那些年，我们一起追过的剧》: 2条评论
    - 其他故事: 2条评论

  评论类型:
    - 直接评论: 11条
    - 回复评论: 4条 (多层级互动)
    
  评论状态:
    - 正常显示: 13条
    - 已隐藏: 1条 (负面内容保护)
    - 已删除: 1条 (用户自删)

  点赞互动:
    - 高赞评论 (20+): 3条
    - 中等热度 (10-20): 4条  
    - 一般热度 (<10): 8条
```

### 4.4 收藏系统 (20个收藏)

#### 4.4.1 收藏分类统计

```yaml
分类分布:
  - 点亮回忆: 4个 (点亮成功的纪念收藏)
  - 成功点亮: 2个 (点亮达人的成就收藏)
  - VIP精选: 3个 (VIP用户的优质推荐)
  - 友情故事: 3个 (友情主题偏好)
  - 励志故事: 2个 (正能量内容)
  - 感人故事: 1个 (情感共鸣类)
  - 其他分类: 5个

收藏行为分析:
  - 公开收藏: 12个 (愿意分享的优质内容)
  - 私人收藏: 8个 (个人珍藏的特殊内容)
  
优先级分布:
  - 高优先级: 8个 (重要纪念价值)
  - 中优先级: 9个 (日常阅读收藏)
  - 低优先级: 3个 (随手收藏)
```

#### 4.4.2 热门收藏内容

| 排名 | 故事标题 | 收藏次数 | 主要收藏原因 |
|------|----------|----------|--------------|
| 1 | 《那年夏天，我们约定十年后再聚》 | 6次 | 友情共鸣 + 点亮纪念 |
| 2 | 《小雨，你现在过得还好吗》 | 4次 | 师生情感 + 励志价值 |
| 3 | 《创业路上，感谢有你们》 | 3次 | 奋斗精神 + 商业价值 |

### 4.5 分享系统

#### 4.5.1 分享链接数据 (11个链接)

```yaml
分享类型分布:
  - 公开分享: 6个 (无访问限制)
  - 密码保护: 3个 (设置访问密码)
  - 限制访问: 2个 (限制访问次数)

分享状态:
  - 活跃分享: 9个
  - 已过期: 2个

访问统计:
  - 总访问量: 589次
  - 成功访问: 445次 (75.6%)
  - 失败访问: 144次 (24.4%)
```

#### 4.5.2 访问行为分析 (21条访问记录)

```yaml
访问来源分析:
  - 微信: 8次 (38.1%) - 主要社交分享渠道
  - 直接访问: 6次 (28.6%) - 用户收藏链接
  - LinkedIn: 3次 (14.3%) - 商务社交分享
  - 知乎: 2次 (9.5%) - 知识分享平台
  - 其他平台: 2次 (9.5%)

地理分布:
  - 上海: 5次 (23.8%)
  - 北京: 4次 (19.0%)
  - 苏州: 3次 (14.3%)
  - 深圳: 3次 (14.3%)
  - 其他城市: 6次 (28.6%)

访问设备:
  - Windows PC: 10次 (47.6%)
  - iPhone: 6次 (28.6%)
  - Android: 3次 (14.3%)
  - Mac: 2次 (9.5%)
```

### 4.6 通知系统 (23条通知)

#### 4.6.1 通知类型分布

```yaml
通知类型统计:
  - lighting_success: 6条 (点亮成功通知)
  - lighting_confirmed: 3条 (点亮确认通知)
  - story_like: 3条 (故事点赞通知)
  - comment_reply: 3条 (评论回复通知)
  - comment_like: 2条 (评论点赞通知)
  - follow_new: 2条 (新关注者通知)
  - achievement: 2条 (成就获得通知)
  - system_welcome: 3条 (系统欢迎通知)

优先级分布:
  - 高优先级: 12条 (重要业务通知)
  - 中优先级: 8条 (日常互动通知)
  - 低优先级: 3条 (系统提醒通知)

阅读状态:
  - 已读通知: 14条 (60.9%)
  - 未读通知: 9条 (39.1%)
```

#### 4.6.2 情绪保护机制体现

```yaml
正面通知设计:
  ✅ 只推送正面消息: 所有23条通知都是积极正面的
  ✅ 避免负面情绪: 拒绝申请、取消关注等不发送通知
  ✅ 温暖用词: 使用"恭喜"、"成功"、"感谢"等积极词汇
  ✅ 成就导向: 突出用户的成就和价值
  ✅ 连接价值: 强调人与人之间的美好连接

通知示例:
  - "🎉 您的人物被成功点亮！" (而非"有人申请点亮")
  - "👥 您有新的关注者" (而非"关注者取消关注")  
  - "🏆 恭喜获得成就：点亮达人" (成就激励)
```

### 4.7 个人时间线系统 (34个事件)

#### 4.7.1 时间线事件分布

```yaml
事件类型统计:
  - birth (出生): 6个 - 人生起点记录
  - education (教育): 8个 - 求学经历
  - work (工作): 4个 - 职业发展
  - relationship (关系): 2个 - 人际关系
  - achievement (成就): 6个 - 重要成就
  - milestone (里程碑): 5个 - 人生节点
  - celebration (庆祝): 1个 - 特殊庆祝
  - loss (失落): 1个 - 挫折经历
  - other (其他): 1个 - 其他事件

重要程度分布:
  - critical (关键): 6个 - 人生关键节点
  - major (重要): 22个 - 重要人生事件
  - normal (普通): 5个 - 日常重要事件
  - minor (次要): 1个 - 一般生活事件

可见性设置:
  - 公开事件: 25个 (73.5%) - 愿意分享的经历
  - 私密事件: 9个 (26.5%) - 个人隐私事件
```

#### 4.7.2 用户时间线特色

```yaml
张明远的时间线 (7个事件):
  特色: 创业者成长轨迹
  关键节点: 大学友谊 → 职场发展 → 创业历程
  
李小红的时间线 (6个事件):
  特色: 点亮者的平台体验
  关键节点: 大学时光 → 平台发现 → 点亮成功
  
王建国的时间线 (6个事件):
  特色: 教育工作者职业生涯
  关键节点: 教师家庭 → 教学生涯 → 师生重逢
  
周发现的时间线 (6个事件):
  特色: 学生到医生的成长
  关键节点: 教师家庭 → 医学院 → 点亮恩师
  
其他用户时间线 (9个事件):
  涵盖不同人生阶段和职业背景
```

---

## 5. API接口对照

### 5.1 用户管理接口

#### 5.1.1 认证相关接口

| 接口路径 | 方法 | 功能描述 | 测试数据 |
|----------|------|----------|----------|
| `/auth/login` | POST | 用户登录 | 手机号/密码登录 |
| `/auth/phone-login` | POST | 手机验证码登录 | 验证码：123456 |
| `/auth/register` | POST | 用户注册 | 支持手机/邮箱注册 |
| `/auth/logout` | POST | 用户登出 | JWT token处理 |
| `/auth/refresh` | POST | 刷新令牌 | refresh token更新 |

```javascript
// 登录测试数据示例
const loginTestData = {
  password: {
    loginId: "13800138001",
    password: "Test123456!"
  },
  phone: {
    phone: "13800138001", 
    verificationCode: "123456"
  }
}
```

#### 5.1.2 用户信息接口

| 接口路径 | 方法 | 功能描述 | 数据特点 |
|----------|------|----------|----------|
| `/users/profile` | GET | 获取用户资料 | 完整用户信息，39个字段 |
| `/users/profile` | PUT | 更新用户资料 | 支持头像、昵称、简介等 |
| `/users/{id}` | GET | 获取他人资料 | 根据隐私设置显示 |
| `/users/search` | GET | 搜索用户 | 支持昵称、用户名搜索 |

### 5.2 故事管理接口

#### 5.2.1 故事CRUD接口

| 接口路径 | 方法 | 功能描述 | 测试场景 |
|----------|------|----------|----------|
| `/stories` | GET | 获取故事列表 | 分页、筛选、排序 |
| `/stories` | POST | 创建故事 | 权限级别、主题分类 |
| `/stories/{id}` | GET | 获取故事详情 | 权限验证、访问统计 |
| `/stories/{id}` | PUT | 更新故事 | 作者权限验证 |
| `/stories/{id}` | DELETE | 删除故事 | 软删除、关联处理 |

```javascript
// 故事列表筛选参数
const storyFilters = {
  theme: "友情",           // 主题筛选
  status: "published",    // 状态筛选  
  permission: "public",   // 权限筛选
  author: "明远说",       // 作者筛选
  hasCharacters: true,    // 是否包含人物
  sortBy: "createdAt",    // 排序字段
  order: "desc"           // 排序方向
}
```

#### 5.2.2 故事权限接口

| 接口路径 | 方法 | 功能描述 | 权限级别 |
|----------|------|----------|----------|
| `/stories/{id}/permission` | GET | 检查访问权限 | 6级权限系统 |
| `/stories/{id}/permission` | PUT | 修改权限设置 | 作者专用 |
| `/stories/my-stories` | GET | 我的故事列表 | 创作者视图 |
| `/stories/accessible` | GET | 可访问故事 | 权限过滤 |

### 5.3 人物点亮接口 (核心)

#### 5.3.1 人物管理接口

| 接口路径 | 方法 | 功能描述 | 测试数据 |
|----------|------|----------|----------|
| `/stories/{id}/characters` | GET | 获取故事人物 | 12个测试人物 |
| `/characters/{id}` | GET | 获取人物详情 | 包含点亮状态 |
| `/characters/my-lightings` | GET | 我的点亮集合 | 点亮者专用 |
| `/characters/search` | GET | 搜索人物 | 按姓名、关系搜索 |

#### 5.3.2 点亮申请接口

| 接口路径 | 方法 | 功能描述 | 业务场景 |
|----------|------|----------|----------|
| `/characters/{id}/light-request` | POST | 申请点亮人物 | 身份验证流程 |
| `/light-requests` | GET | 我的申请列表 | 申请者视图 |
| `/light-requests/received` | GET | 收到的申请 | 作者审核视图 |
| `/light-requests/{id}/approve` | POST | 批准申请 | 作者权限 |
| `/light-requests/{id}/reject` | POST | 拒绝申请 | 作者权限 |

```javascript
// 点亮申请测试数据
const lightingRequestData = {
  characterId: "uuid-character-id",
  phoneVerification: "13800138002",
  hasPhoneVerification: true,
  message: "我就是故事中提到的李明，我们是大学室友。",
  relationshipProof: "可以提供大学毕业照和联系方式验证"
}
```

#### 5.3.3 点亮验证接口

| 接口路径 | 方法 | 功能描述 | 验证方式 |
|----------|------|----------|----------|
| `/light-requests/{id}/verify-phone` | POST | 手机号验证 | 短信验证码 |
| `/light-requests/{id}/verify-identity` | POST | 身份验证 | 证件信息 |
| `/light-requests/{id}/verify-relationship` | POST | 关系验证 | 关系证明 |

### 5.4 社交互动接口

#### 5.4.1 关注系统接口

| 接口路径 | 方法 | 功能描述 | 社交数据 |
|----------|------|----------|----------|
| `/users/{id}/follow` | POST | 关注用户 | 建立关注关系 |
| `/users/{id}/unfollow` | POST | 取消关注 | 解除关注关系 |
| `/users/{id}/followers` | GET | 获取关注者 | 16个关注关系 |
| `/users/{id}/following` | GET | 获取关注列表 | 活跃用户数据 |
| `/follow/mutual` | GET | 共同关注 | 社交网络分析 |

#### 5.4.2 好友分组接口

| 接口路径 | 方法 | 功能描述 | 分组数据 |
|----------|------|----------|----------|
| `/friend-groups` | GET | 获取分组列表 | 11个测试分组 |
| `/friend-groups` | POST | 创建分组 | 自定义分组管理 |
| `/friend-groups/{id}/members` | GET | 获取分组成员 | 成员关系数据 |
| `/friend-groups/{id}/members` | POST | 添加成员 | 分组管理功能 |

### 5.5 评论系统接口

#### 5.5.1 评论CRUD接口

| 接口路径 | 方法 | 功能描述 | 评论数据 |
|----------|------|----------|----------|
| `/stories/{id}/comments` | GET | 获取评论列表 | 15条测试评论 |
| `/stories/{id}/comments` | POST | 发表评论 | 多层级评论支持 |
| `/comments/{id}` | PUT | 编辑评论 | 编辑状态标记 |
| `/comments/{id}` | DELETE | 删除评论 | 软删除处理 |
| `/comments/{id}/replies` | GET | 获取回复 | 嵌套回复结构 |

#### 5.5.2 评论互动接口

| 接口路径 | 方法 | 功能描述 | 互动数据 |
|----------|------|----------|----------|
| `/comments/{id}/like` | POST | 点赞评论 | 点赞状态切换 |
| `/comments/{id}/unlike` | POST | 取消点赞 | 点赞数量管理 |
| `/comments/{id}/report` | POST | 举报评论 | 内容审核机制 |

### 5.6 收藏系统接口

| 接口路径 | 方法 | 功能描述 | 收藏数据 |
|----------|------|----------|----------|
| `/bookmarks` | GET | 获取收藏列表 | 20个收藏记录 |
| `/stories/{id}/bookmark` | POST | 收藏故事 | 分类标签管理 |
| `/bookmarks/{id}` | DELETE | 取消收藏 | 收藏状态管理 |
| `/bookmarks/categories` | GET | 获取收藏分类 | 7种收藏分类 |

### 5.7 分享系统接口

#### 5.7.1 分享管理接口

| 接口路径 | 方法 | 功能描述 | 分享数据 |
|----------|------|----------|----------|
| `/stories/{id}/share` | POST | 创建分享链接 | 3种分享类型 |
| `/shares` | GET | 我的分享列表 | 11个分享链接 |
| `/shares/{token}` | GET | 访问分享链接 | 权限验证处理 |
| `/shares/{id}/stats` | GET | 分享统计 | 访问数据分析 |

#### 5.7.2 分享统计接口

| 接口路径 | 方法 | 功能描述 | 统计数据 |
|----------|------|----------|----------|
| `/shares/{id}/access-logs` | GET | 访问记录 | 21条访问日志 |
| `/shares/analytics` | GET | 分享分析 | 地域、设备统计 |

### 5.8 通知系统接口

| 接口路径 | 方法 | 功能描述 | 通知数据 |
|----------|------|----------|----------|
| `/notifications` | GET | 获取通知列表 | 23条通知记录 |
| `/notifications/{id}/read` | POST | 标记已读 | 阅读状态管理 |
| `/notifications/unread-count` | GET | 未读数量 | 红点提醒功能 |
| `/notifications/mark-all-read` | POST | 全部已读 | 批量状态更新 |

### 5.9 时间线系统接口

| 接口路径 | 方法 | 功能描述 | 时间线数据 |
|----------|------|----------|----------|
| `/timeline` | GET | 获取时间线 | 34个生活事件 |
| `/timeline` | POST | 添加事件 | 事件类型管理 |
| `/timeline/{id}` | PUT | 编辑事件 | 隐私设置控制 |
| `/timeline/{id}` | DELETE | 删除事件 | 事件状态管理 |

---

## 6. 前端联调指南

### 6.1 环境配置

#### 6.1.1 API基础配置

```javascript
// 开发环境配置
const apiConfig = {
  baseURL: 'http://localhost:3000/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
}

// 生产环境配置  
const prodApiConfig = {
  baseURL: 'https://api.youguishi.com/api/v1',
  timeout: 15000
}
```

#### 6.1.2 认证拦截器配置

```javascript
// JWT Token 管理
axios.interceptors.request.use(
  config => {
    const token = localStorage.getItem('accessToken')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => Promise.reject(error)
)

// Token 自动刷新
axios.interceptors.response.use(
  response => response,
  async error => {
    if (error.response?.status === 401) {
      const refreshToken = localStorage.getItem('refreshToken')
      if (refreshToken) {
        // 调用刷新接口
        const { data } = await refreshAccessToken(refreshToken)
        localStorage.setItem('accessToken', data.accessToken)
        // 重试原请求
        return axios.request(error.config)
      }
    }
    return Promise.reject(error)
  }
)
```

### 6.2 核心功能联调

#### 6.2.1 用户认证流程

```javascript
// 1. 手机验证码登录 (推荐方式)
const phoneLogin = async (phone) => {
  // 发送验证码
  await sendVerificationCode(phone)
  
  // 登录 (测试验证码: 123456)
  const loginData = {
    phone: phone,
    verificationCode: '123456'
  }
  
  const response = await api.post('/auth/phone-login', loginData)
  
  // 存储Token
  localStorage.setItem('accessToken', response.data.accessToken)
  localStorage.setItem('refreshToken', response.data.refreshToken)
  
  return response.data.user
}

// 2. 密码登录
const passwordLogin = async (loginId, password) => {
  const response = await api.post('/auth/login', {
    loginId, // 支持手机号或用户名
    password
  })
  
  return response.data
}

// 测试账号快速登录
const quickLogin = {
  creator: () => phoneLogin('13800138001'),      // 创作者
  lighter: () => phoneLogin('13800138002'),      // 点亮者  
  teacher: () => phoneLogin('13800138003'),      // 教师
  reader: () => phoneLogin('13800138004'),       // 读者
  vip: () => phoneLogin('16675158665'),          // VIP用户
  tester: () => phoneLogin('13800138009')        // 测试账号
}
```

#### 6.2.2 故事列表加载

```javascript
// 故事列表组件数据获取
const StoryList = () => {
  const [stories, setStories] = useState([])
  const [loading, setLoading] = useState(false)
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0
  })

  const loadStories = async (filters = {}) => {
    setLoading(true)
    try {
      const params = {
        page: pagination.page,
        limit: pagination.limit,
        ...filters
      }
      
      const response = await api.get('/stories', { params })
      
      setStories(response.data.items)
      setPagination(prev => ({
        ...prev,
        total: response.data.total
      }))
    } catch (error) {
      console.error('加载故事列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 测试筛选场景
  const testFilters = {
    byTheme: () => loadStories({ theme: '友情' }),
    byHotness: () => loadStories({ sortBy: 'viewCount', order: 'desc' }),
    withCharacters: () => loadStories({ hasCharacters: true }),
    byAuthor: () => loadStories({ author: '明远说' })
  }

  return (
    // 故事列表UI组件
  )
}
```

#### 6.2.3 人物点亮功能

```javascript
// 人物点亮完整流程
const CharacterLighting = ({ storyId }) => {
  const [characters, setCharacters] = useState([])
  const [lightingModal, setLightingModal] = useState(false)
  const [selectedCharacter, setSelectedCharacter] = useState(null)

  // 1. 加载故事人物
  const loadCharacters = async () => {
    const response = await api.get(`/stories/${storyId}/characters`)
    setCharacters(response.data)
  }

  // 2. 发起点亮申请
  const submitLightingRequest = async (characterId, requestData) => {
    try {
      const response = await api.post(`/characters/${characterId}/light-request`, {
        phoneVerification: requestData.phone,
        hasPhoneVerification: true,
        message: requestData.message,
        relationshipProof: requestData.proof
      })
      
      // 显示申请成功提示
      showSuccess('点亮申请已提交，请等待作者审核')
      
      return response.data
    } catch (error) {
      showError('申请提交失败，请重试')
      throw error
    }
  }

  // 3. 查看我的点亮集合
  const loadMyLightings = async () => {
    const response = await api.get('/characters/my-lightings')
    return response.data
  }

  // 测试数据 - 可直接使用的人物ID
  const testCharacters = {
    liming: '李明的人物ID',    // 可点亮 (张明远的故事)
    wangqiang: '王强的人物ID', // 可点亮 (张明远的故事)  
    zhaohua: '赵华的人物ID',   // 已被点亮 (周发现点亮)
    xiaoyu: '小雨的人物ID'     // 已被点亮 (周发现点亮)
  }

  return (
    // 人物点亮UI组件
  )
}
```

#### 6.2.4 评论系统实现

```javascript
// 评论组件完整实现
const CommentSystem = ({ storyId }) => {
  const [comments, setComments] = useState([])
  const [newComment, setNewComment] = useState('')
  const [replyTo, setReplyTo] = useState(null)

  // 1. 加载评论列表
  const loadComments = async () => {
    const response = await api.get(`/stories/${storyId}/comments`)
    setComments(response.data)
  }

  // 2. 发表评论
  const submitComment = async (content, parentId = null) => {
    try {
      const response = await api.post(`/stories/${storyId}/comments`, {
        content,
        parentCommentId: parentId
      })
      
      // 更新评论列表
      await loadComments()
      setNewComment('')
      setReplyTo(null)
      
      return response.data
    } catch (error) {
      showError('评论发表失败')
    }
  }

  // 3. 点赞评论
  const likeComment = async (commentId) => {
    try {
      await api.post(`/comments/${commentId}/like`)
      await loadComments() // 刷新数据
    } catch (error) {
      showError('点赞失败')
    }
  }

  // 测试评论数据 - 来自种子数据
  const testComments = [
    {
      id: 'comment-1',
      content: '看到这个故事，眼泪都出来了。我也有这样的大学室友...',
      author: '小红姐姐',
      likeCount: 23,
      createdAt: '5天前'
    }
    // 更多测试评论...
  ]

  return (
    // 评论系统UI
  )
}
```

### 6.3 数据状态管理

#### 6.3.1 Redux/Zustand 状态设计

```javascript
// 用户状态管理
const useUserStore = create((set, get) => ({
  user: null,
  isAuthenticated: false,
  loading: false,

  // 登录
  login: async (credentials) => {
    set({ loading: true })
    try {
      const user = await authService.login(credentials)
      set({ user, isAuthenticated: true })
    } finally {
      set({ loading: false })
    }
  },

  // 获取用户信息
  fetchProfile: async () => {
    const response = await api.get('/users/profile')
    set({ user: response.data })
  },

  // 登出
  logout: () => {
    localStorage.removeItem('accessToken')
    localStorage.removeItem('refreshToken')
    set({ user: null, isAuthenticated: false })
  }
}))

// 故事状态管理
const useStoryStore = create((set) => ({
  stories: [],
  currentStory: null,
  loading: false,

  fetchStories: async (params) => {
    set({ loading: true })
    try {
      const response = await api.get('/stories', { params })
      set({ stories: response.data.items })
    } finally {
      set({ loading: false })
    }
  },

  fetchStoryDetail: async (id) => {
    const response = await api.get(`/stories/${id}`)
    set({ currentStory: response.data })
  }
}))
```

#### 6.3.2 缓存策略

```javascript
// React Query 数据缓存配置
import { useQuery, useMutation, useQueryClient } from 'react-query'

// 故事列表缓存
const useStories = (filters) => {
  return useQuery(
    ['stories', filters],
    () => api.get('/stories', { params: filters }),
    {
      staleTime: 5 * 60 * 1000, // 5分钟缓存
      cacheTime: 10 * 60 * 1000, // 10分钟保留
    }
  )
}

// 点亮申请mutation
const useLightingRequest = () => {
  const queryClient = useQueryClient()
  
  return useMutation(
    ({ characterId, requestData }) => 
      api.post(`/characters/${characterId}/light-request`, requestData),
    {
      onSuccess: () => {
        // 刷新相关缓存
        queryClient.invalidateQueries(['characters'])
        queryClient.invalidateQueries(['light-requests'])
      }
    }
  )
}
```

### 6.4 错误处理策略

#### 6.4.1 统一错误处理

```javascript
// 全局错误处理
const errorHandler = {
  // 认证错误
  401: (error) => {
    // 清除本地token
    localStorage.removeItem('accessToken')
    localStorage.removeItem('refreshToken')
    
    // 跳转到登录页
    window.location.href = '/login'
  },

  // 权限错误
  403: (error) => {
    showError('您没有权限访问此内容')
  },

  // 资源不存在
  404: (error) => {
    showError('请求的资源不存在')
  },

  // 服务器错误
  500: (error) => {
    showError('服务器内部错误，请稍后重试')
  },

  // 业务错误
  handleBusinessError: (error) => {
    const { code, message } = error.response.data
    
    const businessErrors = {
      'CHARACTER_ALREADY_LIGHTED': '该人物已被点亮',
      'INVALID_PHONE_VERIFICATION': '手机号验证失败',
      'STORY_NOT_ACCESSIBLE': '您没有权限访问此故事',
      'LIGHTING_REQUEST_EXPIRED': '点亮申请已过期'
    }
    
    showError(businessErrors[code] || message)
  }
}
```

#### 6.4.2 组件级错误边界

```javascript
// 错误边界组件
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props)
    this.state = { hasError: false, error: null }
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error }
  }

  componentDidCatch(error, errorInfo) {
    // 错误上报
    console.error('ErrorBoundary caught an error:', error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="error-fallback">
          <h2>出现了一些问题</h2>
          <p>页面加载失败，请刷新页面重试</p>
          <button onClick={() => window.location.reload()}>
            刷新页面
          </button>
        </div>
      )
    }

    return this.props.children
  }
}
```

### 6.5 性能优化建议

#### 6.5.1 列表虚拟化

```javascript
// 大列表优化 - 使用react-window
import { FixedSizeList as List } from 'react-window'

const VirtualizedStoryList = ({ stories }) => {
  const Row = ({ index, style }) => (
    <div style={style}>
      <StoryCard story={stories[index]} />
    </div>
  )

  return (
    <List
      height={600}
      itemCount={stories.length}
      itemSize={200}
      itemData={stories}
    >
      {Row}
    </List>
  )
}
```

#### 6.5.2 图片懒加载

```javascript
// 图片懒加载组件
const LazyImage = ({ src, alt, ...props }) => {
  const [imageSrc, setImageSrc] = useState(null)
  const [imageRef, setImageRef] = useState()

  useEffect(() => {
    let observer
    
    if (imageRef && imageSrc !== src) {
      if (IntersectionObserver) {
        observer = new IntersectionObserver(
          entries => {
            entries.forEach(entry => {
              if (entry.isIntersecting) {
                setImageSrc(src)
                observer.unobserve(imageRef)
              }
            })
          },
          { threshold: 0.1 }
        )
        observer.observe(imageRef)
      } else {
        setImageSrc(src)
      }
    }
    
    return () => {
      if (observer && observer.unobserve) {
        observer.unobserve(imageRef)
      }
    }
  }, [src, imageSrc, imageRef])

  return (
    <img
      ref={setImageRef}
      src={imageSrc || '/placeholder.jpg'}
      alt={alt}
      {...props}
    />
  )
}
```

---

## 7. 数据管理命令

### 7.1 种子数据命令

```bash
# 基础数据管理
npm run seed:all           # 生成所有种子数据 (推荐)
npm run seed:clean         # 清理所有数据
npm run seed:stats         # 显示数据统计
npm run seed:validate      # 验证数据完整性

# 分模块数据生成
npm run seed:users         # 只生成用户数据
npm run seed:stories       # 只生成故事数据  
npm run seed:characters    # 只生成人物数据
npm run seed:lighting      # 只生成点亮数据
npm run seed:social        # 只生成社交数据
npm run seed:comments      # 只生成评论数据
```

### 7.2 数据库管理

```bash
# 数据库连接测试
npm run db:test-connection  # 测试数据库连接

# 数据库备份恢复
npm run db:backup          # 备份当前数据
npm run db:restore         # 恢复备份数据

# 数据库状态查询
npm run db:status          # 查看数据库状态
npm run db:tables          # 列出所有表
npm run db:counts          # 统计各表记录数
```

### 7.3 开发辅助命令

```bash
# 数据重置 (开发环境)
npm run dev:reset-data     # 重置为初始种子数据
npm run dev:clear-cache    # 清理Redis缓存

# 测试数据生成
npm run test:generate-data # 生成测试专用数据
npm run test:clean-data    # 清理测试数据

# 数据导出导入
npm run export:seed-data   # 导出种子数据
npm run import:seed-data   # 导入种子数据
```

### 7.4 生产环境命令

```bash
# 生产数据管理 (谨慎使用)
npm run prod:seed:safe     # 安全的生产数据初始化
npm run prod:backup        # 生产数据备份
npm run prod:validate      # 生产数据完整性验证
```

---

## 8. 常见问题解答

### 8.1 认证相关问题

**Q: 登录时提示"账号不存在"？**
A: 请确认使用正确的测试账号。种子数据中提供6个测试账号，手机号范围：13800138001-13800138004、16675158665、13800138009。

**Q: 验证码始终显示错误？**
A: 测试环境统一使用验证码 `123456`，所有测试账号通用。

**Q: Token过期如何处理？**
A: 系统支持自动刷新Token。Access Token有效期45分钟，Refresh Token有效期14天。前端应实现自动刷新机制。

### 8.2 数据访问问题

**Q: 无法访问某些故事？**
A: 检查故事的权限级别设置：
- `private`: 仅作者可见
- `friends`: 仅好友可见
- `characters_only`: 仅点亮人物的用户可见
- `public`: 所有人可见

**Q: 点亮申请一直显示pending状态？**
A: 点亮申请需要故事作者手动审核。测试环境中可使用故事作者账号登录进行审核操作。

**Q: 评论无法加载？**
A: 检查故事的`allowComments`设置，部分故事可能关闭了评论功能。

### 8.3 功能测试问题

**Q: 如何测试人物点亮功能？**
A: 推荐测试流程：
1. 使用账号A（如13800138002）申请点亮人物
2. 使用故事作者账号（如13800138001）审核申请
3. 验证点亮成功后的数据变化

**Q: 分享链接无法访问？**
A: 检查分享链接状态：
- 确认链接未过期
- 如果是密码保护分享，需要提供正确密码
- 检查访问次数是否超过限制

**Q: 通知不显示？**
A: 通知系统采用情绪保护机制，只推送正面通知。负面操作（如取消关注、拒绝申请）不会发送通知。

### 8.4 性能相关问题

**Q: 接口响应缓慢？**
A: 种子数据量适中，性能问题可能原因：
- 数据库连接池配置
- 复杂查询未使用索引
- 网络延迟问题

**Q: 如何优化大列表加载？**
A: 建议实现：
- 分页加载 (默认每页10条)
- 虚拟滚动 (超过100条记录)
- 图片懒加载
- 适当缓存策略

### 8.5 开发环境问题

**Q: 数据库连接失败？**
A: 检查配置文件中的数据库连接信息：
- 主机地址：pgm-bp14hy1423z0mg05vo.pg.rds.aliyuncs.com
- 端口：5432
- 数据库名称：ygs-prod
- 确认网络连接和权限设置

**Q: 种子数据生成失败？**
A: 常见解决方案：
1. 清理现有数据：`npm run seed:clean`
2. 检查数据库权限
3. 查看详细错误日志
4. 重新生成：`npm run seed:all`

**Q: 如何重置所有数据？**
A: 完整重置流程：
```bash
npm run seed:clean        # 清理数据
npm run db:backup         # 备份空数据库
npm run seed:all          # 重新生成
npm run seed:validate     # 验证完整性
```

---

## 📞 技术支持

### 联系方式
- **技术文档**: 参考本文档及API接口文档
- **代码仓库**: Gitee仓库 Issues 区域
- **开发团队**: 内部开发群组

### 更新日志
- **v1.0.0 (2025-07-28)**: 首次发布，包含完整种子数据体系
- 后续版本将根据开发需求持续更新

---

**© 2025 YGS有故事平台 - 企业级种子数据使用指南**

*本文档将随着平台功能发展持续更新，请关注最新版本。*