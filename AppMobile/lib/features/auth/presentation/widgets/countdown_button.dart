import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 倒计时按钮
class CountdownButton extends StatefulWidget {
  final VoidCallback? onPressed;
  final int duration;
  final Widget child;
  final EdgeInsetsGeometry? padding;

  const CountdownButton({
    super.key,
    required this.onPressed,
    required this.duration,
    required this.child,
    this.padding,
  });

  @override
  State<CountdownButton> createState() => _CountdownButtonState();
}

class _CountdownButtonState extends State<CountdownButton> {
  Timer? _timer;
  int _countdown = 0;

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _startCountdown() {
    if (widget.onPressed == null) return;
    
    widget.onPressed!();
    
    setState(() {
      _countdown = widget.duration;
    });
    
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_countdown > 0) {
        setState(() {
          _countdown--;
        });
      } else {
        timer.cancel();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final isCountingDown = _countdown > 0;
    
    return SizedBox(
      width: 120.w, // 使用ScreenUtil响应式宽度
      child: TextButton(
        onPressed: isCountingDown ? null : _startCountdown,
        style: TextButton.styleFrom(
          padding: widget.padding ?? EdgeInsets.symmetric(horizontal: 16.w), // 使用ScreenUtil响应式内边距
        ),
        child: isCountingDown
            ? Text('${_countdown}秒后重试')
            : widget.child,
      ),
    );
  }
}