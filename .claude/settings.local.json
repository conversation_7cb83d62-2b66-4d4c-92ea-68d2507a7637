{"permissions": {"allow": ["Bash(ls:*)", "<PERSON><PERSON>(docker:*)", "<PERSON><PERSON>(docker-compose:*)", "Bash(cd:*)", "Bash(npm run migration:run:*)", "<PERSON><PERSON>(mv:*)", "Bash(npm run start:dev:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(pkill:*)", "Bash(kill:*)", "Bash(npm run build:*)", "Bash(npm install:*)", "Bash(npm run start:prod:*)", "Bash(ss:*)", "Bash(node:*)", "<PERSON><PERSON>(cat:*)", "Bash(git add:*)", "Bash(git config:*)", "Bash(git commit:*)", "Bash(grep:*)", "Bash(for file in src/modules/characters/characters.controller.ts src/modules/stories/controllers/story-characters.controller.ts src/modules/stories/stories.controller.ts src/modules/users/notifications.controller.ts src/modules/users/users.controller.ts)", "Bash(do echo \"=== $file ===\")", "Bash(done)", "Bash(rm:*)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(chmod:*)", "mcp__figma-dev-mode-mcp-server__get_variable_defs", "mcp__figma-dev-mode-mcp-server__get_image", "<PERSON><PERSON>(mkdir:*)", "Bash(git push:*)", "Bash(flutter analyze:*)", "mcp__figma-dev-mode-mcp-server__get_code", "mcp__figma-dev-mode-mcp-server__get_code_connect_map", "Bash(cp:*)", "Bash(copy:*)", "Bash(find:*)"], "deny": []}}