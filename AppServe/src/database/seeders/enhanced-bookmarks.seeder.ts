/**
 * 企业级收藏系统种子数据
 *
 * 基于 YGS v1.0.0 收藏系统设计
 * 涵盖故事收藏、分类管理等功能场景
 */

import { USER_ROLES } from "./enhanced-user.seeder";
import { STORY_TITLES } from "./enhanced-story.seeder";

export interface EnhancedBookmarkSeedData {
  userUsername: string;
  storyTitle: string;
  category: string;
  note?: string;
  isPublic: boolean;
  tags: string[];
  createdDaysAgo: number;
  priority: "high" | "medium" | "low";
}

export const ENHANCED_BOOKMARK_SEED_DATA: EnhancedBookmarkSeedData[] = [
  // ========== 李小红的收藏 - 点亮相关故事 ==========
  {
    userUsername: USER_ROLES.LI_XIAOHONG,
    storyTitle: STORY_TITLES.UNIVERSITY_PROMISE,
    category: "点亮回忆",
    note: "这是我点亮的第一个人物的故事，非常有纪念意义。看到李明的故事，想起了大学时光。",
    isPublic: true,
    tags: ["点亮", "大学", "友情", "珍贵回忆"],
    createdDaysAgo: 28,
    priority: "high",
  },
  {
    userUsername: USER_ROLES.LI_XIAOHONG,
    storyTitle: STORY_TITLES.RAINY_NIGHT,
    category: "点亮回忆",
    note: "另一个李明的故事，更加深刻和感人。那个雨夜的陪伴，让我想起了自己的经历。",
    isPublic: true,
    tags: ["点亮", "深度", "友情", "感动时刻"],
    createdDaysAgo: 24,
    priority: "high",
  },
  {
    userUsername: USER_ROLES.LI_XIAOHONG,
    storyTitle: STORY_TITLES.DRAMA_MEMORIES,
    category: "点亮回忆",
    note: "我就是故事里的晓晓！追剧的日子真的很怀念，收藏起来时不时回味一下。",
    isPublic: true,
    tags: ["点亮", "大学", "追剧", "青春"],
    createdDaysAgo: 6,
    priority: "high",
  },
  {
    userUsername: USER_ROLES.LI_XIAOHONG,
    storyTitle: STORY_TITLES.STUDENT_XIAOYU,
    category: "感人故事",
    note: "师生情深的故事，小雨的坚强让人敬佩，王老师的关爱让人感动。",
    isPublic: false,
    tags: ["师生情", "励志", "成长"],
    createdDaysAgo: 12,
    priority: "medium",
  },

  // ========== 周发现的收藏 - 点亮达人的精选 ==========
  {
    userUsername: USER_ROLES.ZHOU_FAXIAN,
    storyTitle: STORY_TITLES.STUDENT_XIAOYU,
    category: "成功点亮",
    note: "我点亮的小雨的故事！能够通过文字找到自己，这种感觉太棒了。",
    isPublic: true,
    tags: ["点亮成功", "师生情", "医生", "励志"],
    createdDaysAgo: 14,
    priority: "high",
  },
  {
    userUsername: USER_ROLES.ZHOU_FAXIAN,
    storyTitle: STORY_TITLES.UNIVERSITY_PROMISE,
    category: "成功点亮",
    note: "点亮了赵华这个角色，重新联系上了大学同学，真的很有意义。",
    isPublic: true,
    tags: ["点亮成功", "大学", "友情", "重逢"],
    createdDaysAgo: 19,
    priority: "high",
  },
  {
    userUsername: USER_ROLES.ZHOU_FAXIAN,
    storyTitle: STORY_TITLES.STARTUP_JOURNEY,
    category: "创业励志",
    note: "创业的故事总是很励志，朋友间的支持让人感动。虽然没有点亮角色，但很值得收藏。",
    isPublic: false,
    tags: ["创业", "友情", "坚持", "励志"],
    createdDaysAgo: 20,
    priority: "medium",
  },
  {
    userUsername: USER_ROLES.ZHOU_FAXIAN,
    storyTitle: STORY_TITLES.TEACHING_REGRET,
    category: "教育反思",
    note: "作为教师，这个故事让我深思。教育不仅仅是传授知识，更要呵护学生的心灵。",
    isPublic: false,
    tags: ["教育", "反思", "成长", "师者"],
    createdDaysAgo: 8,
    priority: "medium",
  },

  // ========== 赵小芳的收藏 - 读者视角 ==========
  {
    userUsername: USER_ROLES.ZHAO_XIAOFANG,
    storyTitle: STORY_TITLES.UNIVERSITY_PROMISE,
    category: "友情故事",
    note: "十年之约真的很感人，希望自己也能有这样的朋友。收藏起来时不时看看。",
    isPublic: false,
    tags: ["友情", "约定", "温暖", "羡慕"],
    createdDaysAgo: 15,
    priority: "medium",
  },
  {
    userUsername: USER_ROLES.ZHAO_XIAOFANG,
    storyTitle: STORY_TITLES.STUDENT_XIAOYU,
    category: "励志故事",
    note: "小雨的坚强很让人敬佩，在困难面前不放弃，最终成为医生。很励志！",
    isPublic: true,
    tags: ["励志", "坚强", "成长", "医生"],
    createdDaysAgo: 10,
    priority: "high",
  },
  {
    userUsername: USER_ROLES.ZHAO_XIAOFANG,
    storyTitle: STORY_TITLES.DRAMA_MEMORIES,
    category: "青春回忆",
    note: "大学时光真美好，虽然我没有这样的室友情谊，但看着也很温暖。",
    isPublic: false,
    tags: ["大学", "室友", "追剧", "青春"],
    createdDaysAgo: 5,
    priority: "low",
  },

  // ========== 王建国的收藏 - 教师视角 ==========
  {
    userUsername: USER_ROLES.WANG_JIANGUO,
    storyTitle: STORY_TITLES.UNIVERSITY_PROMISE,
    category: "青春记忆",
    note: "看到年轻人的友情故事，想起了自己教过的学生们。青春真好！",
    isPublic: false,
    tags: ["青春", "友情", "回忆", "学生"],
    createdDaysAgo: 25,
    priority: "low",
  },
  {
    userUsername: USER_ROLES.WANG_JIANGUO,
    storyTitle: STORY_TITLES.STARTUP_JOURNEY,
    category: "励志故事",
    note: "创业路上朋友的支持很重要，这个故事让我想起了自己年轻时的梦想。",
    isPublic: false,
    tags: ["创业", "友情", "支持", "梦想"],
    createdDaysAgo: 18,
    priority: "medium",
  },

  // ========== 刘小新的收藏 - 新用户探索 ==========
  {
    userUsername: USER_ROLES.LIU_XIAOXIN,
    storyTitle: STORY_TITLES.UNIVERSITY_PROMISE,
    category: "喜欢的故事",
    note: "刚注册就看到这么好的故事，这个平台真不错！",
    isPublic: true,
    tags: ["新人", "发现", "友情"],
    createdDaysAgo: 3,
    priority: "medium",
  },
  {
    userUsername: USER_ROLES.LIU_XIAOXIN,
    storyTitle: STORY_TITLES.STUDENT_XIAOYU,
    category: "喜欢的故事",
    note: "很励志的故事，新用户学习如何写出好故事。",
    isPublic: false,
    tags: ["新人", "学习", "励志"],
    createdDaysAgo: 2,
    priority: "low",
  },

  // ========== 陈总的收藏 - VIP用户精选 ==========
  {
    userUsername: USER_ROLES.CHEN_VIP,
    storyTitle: STORY_TITLES.UNIVERSITY_PROMISE,
    category: "VIP精选",
    note: "VIP用户发现的优质内容，这个故事的情感价值很高，值得推荐。",
    isPublic: true,
    tags: ["VIP精选", "优质内容", "友情", "推荐"],
    createdDaysAgo: 20,
    priority: "high",
  },
  {
    userUsername: USER_ROLES.CHEN_VIP,
    storyTitle: STORY_TITLES.STARTUP_JOURNEY,
    category: "商业案例",
    note: "创业故事很有参考价值，朋友支持的重要性在商业中也很关键。",
    isPublic: true,
    tags: ["商业", "创业", "朋友支持", "案例学习"],
    createdDaysAgo: 16,
    priority: "high",
  },
  {
    userUsername: USER_ROLES.CHEN_VIP,
    storyTitle: STORY_TITLES.STUDENT_XIAOYU,
    category: "VIP精选",
    note: "励志教育故事，体现了教育的价值和意义，推荐给更多人看。",
    isPublic: true,
    tags: ["VIP精选", "教育价值", "励志", "推荐"],
    createdDaysAgo: 12,
    priority: "high",
  },
  {
    userUsername: USER_ROLES.CHEN_VIP,
    storyTitle: STORY_TITLES.TEACHING_REGRET,
    category: "深度思考",
    note: "教育反思类故事，老师的自我反省很有深度，值得深入思考。",
    isPublic: false,
    tags: ["深度", "教育", "反思", "自省"],
    createdDaysAgo: 8,
    priority: "medium",
  },

  // ========== 吴私密的收藏 - 私密用户精选 ==========
  {
    userUsername: USER_ROLES.WU_SIMI,
    storyTitle: STORY_TITLES.UNIVERSITY_PROMISE,
    category: "情感共鸣",
    note: "虽然我比较私密，但这个故事真的很打动人。友情的珍贵让我想起了自己的朋友。",
    isPublic: false,
    tags: ["私人收藏", "情感", "友情"],
    createdDaysAgo: 22,
    priority: "medium",
  },
];

// 导出收藏统计数据
export const BOOKMARK_STATS = {
  total: ENHANCED_BOOKMARK_SEED_DATA.length,
  public: ENHANCED_BOOKMARK_SEED_DATA.filter((b) => b.isPublic).length,
  private: ENHANCED_BOOKMARK_SEED_DATA.filter((b) => !b.isPublic).length,

  // 按优先级分类
  highPriority: ENHANCED_BOOKMARK_SEED_DATA.filter((b) => b.priority === "high")
    .length,
  mediumPriority: ENHANCED_BOOKMARK_SEED_DATA.filter(
    (b) => b.priority === "medium",
  ).length,
  lowPriority: ENHANCED_BOOKMARK_SEED_DATA.filter((b) => b.priority === "low")
    .length,

  // 按分类统计
  categories: {
    点亮回忆: ENHANCED_BOOKMARK_SEED_DATA.filter(
      (b) => b.category === "点亮回忆",
    ).length,
    成功点亮: ENHANCED_BOOKMARK_SEED_DATA.filter(
      (b) => b.category === "成功点亮",
    ).length,
    友情故事: ENHANCED_BOOKMARK_SEED_DATA.filter(
      (b) => b.category === "友情故事",
    ).length,
    励志故事: ENHANCED_BOOKMARK_SEED_DATA.filter(
      (b) => b.category === "励志故事",
    ).length,
    VIP精选: ENHANCED_BOOKMARK_SEED_DATA.filter((b) => b.category === "VIP精选")
      .length,
    感人故事: ENHANCED_BOOKMARK_SEED_DATA.filter(
      (b) => b.category === "感人故事",
    ).length,
    其他: ENHANCED_BOOKMARK_SEED_DATA.filter(
      (b) =>
        ![
          "点亮回忆",
          "成功点亮",
          "友情故事",
          "励志故事",
          "VIP精选",
          "感人故事",
        ].includes(b.category),
    ).length,
  },

  // 最热门的故事（被收藏最多）
  mostBookmarkedStories: (() => {
    const storyCount: { [key: string]: number } = {};
    ENHANCED_BOOKMARK_SEED_DATA.forEach((b) => {
      storyCount[b.storyTitle] = (storyCount[b.storyTitle] || 0) + 1;
    });
    return Object.entries(storyCount)
      .sort((a, b) => (b[1] as number) - (a[1] as number))
      .slice(0, 3)
      .map(([title, count]) => ({ title, count }));
  })(),
};
