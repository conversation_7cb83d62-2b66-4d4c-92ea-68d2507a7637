{"timestamp": "2025/7/28 21:40:00", "database": "ygs-prod", "host": "pgm-bp14hy1423z0mg05vo.pg.rds.aliyuncs.com", "tables": {"users": {"count": 10, "schema": [{"name": "id", "type": "uuid", "nullable": false, "default": "uuid_generate_v4()"}, {"name": "phone", "type": "character varying", "nullable": true, "default": null}, {"name": "email", "type": "character varying", "nullable": true, "default": null}, {"name": "passwordHash", "type": "character varying", "nullable": true, "default": null}, {"name": "user_number", "type": "character varying", "nullable": true, "default": null}, {"name": "nickname", "type": "character varying", "nullable": false, "default": "''::character varying"}, {"name": "username", "type": "character varying", "nullable": true, "default": null}, {"name": "avatar_url", "type": "character varying", "nullable": true, "default": null}, {"name": "birth_date", "type": "date", "nullable": true, "default": null}, {"name": "bio", "type": "text", "nullable": true, "default": null}, {"name": "ai_quota_remaining", "type": "integer", "nullable": false, "default": "3"}, {"name": "ai_quota_reset_date", "type": "date", "nullable": false, "default": "('now'::text)::date"}, {"name": "profile_display_settings", "type": "jsonb", "nullable": false, "default": "'{\"showBio\": true, \"showStories\": true, \"showBirthday\": false, \"showTimeline\": true, \"showBookmarks\": false, \"showCharacters\": true, \"showFollowList\": false, \"showStatistics\": true, \"showReferenceCollections\": true}'::jsonb"}, {"name": "anomalyStatus", "type": "character varying", "nullable": false, "default": "'normal'::character varying"}, {"name": "status", "type": "character varying", "nullable": false, "default": "'active'::character varying"}, {"name": "is_active", "type": "boolean", "nullable": false, "default": "true"}, {"name": "is_phone_verified", "type": "boolean", "nullable": false, "default": "false"}, {"name": "phone_verified_at", "type": "timestamp without time zone", "nullable": true, "default": null}, {"name": "is_email_verified", "type": "boolean", "nullable": false, "default": "false"}, {"name": "email_verified_at", "type": "timestamp without time zone", "nullable": true, "default": null}, {"name": "is_identity_verified", "type": "boolean", "nullable": false, "default": "false"}, {"name": "identity_verified_at", "type": "timestamp without time zone", "nullable": true, "default": null}, {"name": "identity_number", "type": "character varying", "nullable": true, "default": null}, {"name": "real_name", "type": "character varying", "nullable": true, "default": null}, {"name": "security_level", "type": "integer", "nullable": false, "default": "0"}, {"name": "is_vip_user", "type": "boolean", "nullable": false, "default": "false"}, {"name": "last_security_verification", "type": "timestamp without time zone", "nullable": true, "default": null}, {"name": "last_login_at", "type": "timestamp without time zone", "nullable": true, "default": null}, {"name": "last_login_ip", "type": "character varying", "nullable": true, "default": null}, {"name": "failed_login_attempts", "type": "integer", "nullable": false, "default": "0"}, {"name": "locked_until", "type": "timestamp without time zone", "nullable": true, "default": null}, {"name": "anomaly_warning_count", "type": "integer", "nullable": false, "default": "0"}, {"name": "anomaly_restriction_count", "type": "integer", "nullable": false, "default": "0"}, {"name": "lighting_restricted_until", "type": "timestamp without time zone", "nullable": true, "default": null}, {"name": "last_invalid_lighting_attempt", "type": "timestamp without time zone", "nullable": true, "default": null}, {"name": "daily_lighting_attempts", "type": "integer", "nullable": false, "default": "0"}, {"name": "lighting_attempt_reset_date", "type": "date", "nullable": false, "default": "('now'::text)::date"}, {"name": "created_at", "type": "timestamp without time zone", "nullable": false, "default": "now()"}, {"name": "updated_at", "type": "timestamp without time zone", "nullable": false, "default": "now()"}], "samples": [{"id": "835952e2-d4e5-47c0-8470-179d338d81de", "phone": "13800138001", "email": "<EMAIL>", "passwordHash": "$2b$10$MW/Ij3veUBa1rlbodzVMYuXyE25vu.3DqnxkzioU7cu4tGR63E5mq", "user_number": "100001", "nickname": "明远说", "username": "story_creator_zhang", "avatar_url": "https://ygs-oss-dev.oss-cn-hangzhou.aliyuncs.com/avatars/zhang_mingyuan.jpg", "birth_date": "1990-05-14T15:00:00.000Z", "bio": "用文字记录生活，用故事温暖人心。已创作50+故事，记录了身边100+位朋友的人生片段。", "ai_quota_remaining": 5, "ai_quota_reset_date": "2025-07-27T16:00:00.000Z", "profile_display_settings": {"showBio": true, "showStories": true, "showBirthday": false, "showTimeline": true, "showBookmarks": false, "showCharacters": true, "showFollowList": false, "showStatistics": true, "showReferenceCollections": true}, "anomalyStatus": "normal", "status": "active", "is_active": true, "is_phone_verified": true, "phone_verified_at": null, "is_email_verified": true, "email_verified_at": null, "is_identity_verified": false, "identity_verified_at": null, "identity_number": null, "real_name": null, "security_level": 2, "is_vip_user": false, "last_security_verification": null, "last_login_at": null, "last_login_ip": null, "failed_login_attempts": 0, "locked_until": null, "anomaly_warning_count": 0, "anomaly_restriction_count": 0, "lighting_restricted_until": null, "last_invalid_lighting_attempt": null, "daily_lighting_attempts": 0, "lighting_attempt_reset_date": "2025-07-27T16:00:00.000Z", "created_at": "2025-07-28T13:38:02.272Z", "updated_at": "2025-07-28T13:38:02.272Z"}, {"id": "c750db1e-57ff-4fee-8bcd-************", "phone": "13800138002", "email": "li_xia<PERSON><PERSON>@example.com", "passwordHash": "$2b$10$PSXnzSY5mtbPFRx/uHP2..qiR.KOVZ2mrni5pitpgKlWAltIL4KU.", "user_number": "100002", "nickname": "小红姐姐", "username": "character_lighter_li", "avatar_url": "https://ygs-oss-dev.oss-cn-hangzhou.aliyuncs.com/avatars/li_xiaohong.jpg", "birth_date": "1992-08-19T16:00:00.000Z", "bio": "在别人的故事里找到了自己。已成功点亮15个人物，每个都是珍贵的回忆。", "ai_quota_remaining": 3, "ai_quota_reset_date": "2025-07-27T16:00:00.000Z", "profile_display_settings": {"showBio": true, "showStories": true, "showBirthday": true, "showTimeline": true, "showBookmarks": true, "showCharacters": true, "showFollowList": true, "showStatistics": true, "showReferenceCollections": true}, "anomalyStatus": "normal", "status": "active", "is_active": true, "is_phone_verified": true, "phone_verified_at": null, "is_email_verified": false, "email_verified_at": null, "is_identity_verified": false, "identity_verified_at": null, "identity_number": null, "real_name": null, "security_level": 1, "is_vip_user": false, "last_security_verification": null, "last_login_at": null, "last_login_ip": null, "failed_login_attempts": 0, "locked_until": null, "anomaly_warning_count": 0, "anomaly_restriction_count": 0, "lighting_restricted_until": null, "last_invalid_lighting_attempt": null, "daily_lighting_attempts": 0, "lighting_attempt_reset_date": "2025-07-27T16:00:00.000Z", "created_at": "2025-07-28T13:38:02.272Z", "updated_at": "2025-07-28T13:38:02.272Z"}]}, "stories": {"count": 9, "schema": [{"name": "id", "type": "uuid", "nullable": false, "default": "uuid_generate_v4()"}, {"name": "user_id", "type": "uuid", "nullable": false, "default": null}, {"name": "title", "type": "character varying", "nullable": false, "default": null}, {"name": "content", "type": "text", "nullable": true, "default": null}, {"name": "cover_image_url", "type": "character varying", "nullable": true, "default": null}, {"name": "status", "type": "smallint", "nullable": false, "default": "'1'::smallint"}, {"name": "view_count", "type": "integer", "nullable": false, "default": "0"}, {"name": "like_count", "type": "integer", "nullable": false, "default": "0"}, {"name": "theme_id", "type": "uuid", "nullable": true, "default": null}, {"name": "story_date", "type": "date", "nullable": true, "default": null}, {"name": "location", "type": "character varying", "nullable": true, "default": null}, {"name": "images", "type": "jsonb", "nullable": true, "default": null}, {"name": "permission_level", "type": "character varying", "nullable": false, "default": "'public'::character varying"}, {"name": "allow_comments", "type": "boolean", "nullable": false, "default": "true"}, {"name": "allow_likes", "type": "boolean", "nullable": false, "default": "true"}, {"name": "allow_sharing", "type": "boolean", "nullable": false, "default": "true"}, {"name": "ai_safety_score", "type": "numeric", "nullable": true, "default": null}, {"name": "comment_count", "type": "integer", "nullable": false, "default": "0"}, {"name": "created_at", "type": "timestamp without time zone", "nullable": false, "default": "now()"}, {"name": "updated_at", "type": "timestamp without time zone", "nullable": false, "default": "now()"}], "samples": [{"id": "c96ee67e-3cc5-4921-b75a-cdded0755de0", "user_id": "391510d8-2dab-4a89-a72c-f34674f1856f", "title": "写给十年后的自己", "content": "今天是2025年7月27日，我35岁了。\n\n十年前的今天，25岁的我刚刚大学毕业，满怀憧憬地踏入社会。那时的我，有很多梦想：环游世界、写一本书、找到真爱、事业有成...\n\n现在回头看，有些梦想实现了，有些还在路上，有些已经改变了。\n\n我没有环游世界，但去了十几个国家；没有写成一本书，但一直在坚持写作；找到了爱情，虽然平淡但很幸福；事业算不上大成，但也小有成就。\n\n最重要的是，我学会了接纳不完美的自己。不再追求别人眼中的成功，而是寻找内心的平静。\n\n如果可以对25岁的自己说一句话，我想说：别着急，该来的都会来。人生不是百米冲刺，而是一场马拉松。\n\n写给十年后45岁的自己：希望你依然保持好奇心，依然相信美好，依然在自己选择的路上坚定前行。", "cover_image_url": null, "status": 2, "view_count": 1, "like_count": 0, "theme_id": "2546cc14-f722-412d-a731-ba69655be7f8", "story_date": "2025-07-26T16:00:00.000Z", "location": "家中书房", "images": null, "permission_level": "private", "allow_comments": false, "allow_likes": false, "allow_sharing": false, "ai_safety_score": "1.00", "comment_count": 0, "created_at": "2025-07-28T13:38:02.272Z", "updated_at": "2025-07-28T13:38:02.272Z"}, {"id": "019afdc6-c93d-47d4-9ce7-baa2d14820c9", "user_id": "835952e2-d4e5-47c0-8470-179d338d81de", "title": "李明，还记得那个雨夜吗", "content": "这个故事只想让你看到。\n\n2014年11月的一个雨夜，我失恋了。整个人像是被掏空了一样，一个人坐在宿舍楼顶，雨水混着眼泪，分不清哪是哪。\n\n你不知道从哪里找到我，什么都没说，就那样陪我坐在雨里。\n\n后来你说：\"兄弟，天还没塌，咱们还有梦想要实现呢。\"\n\n那一夜，我们聊了很多，关于理想，关于未来，关于如何成为更好的自己。你说，失恋不可怕，可怕的是失去前进的勇气。\n\n第二天，我们都感冒了，但心情却好了很多。\n\n这件事我从来没有对别人说过，连王强和赵华都不知道。但我一直记得，在我最脆弱的时候，是你陪我度过了最难的一夜。\n\n谢谢你，我的兄弟。", "cover_image_url": null, "status": 2, "view_count": 12, "like_count": 3, "theme_id": "32c66bdd-99aa-4c39-b84b-6ad174f96244", "story_date": "2014-11-19T16:00:00.000Z", "location": "大学宿舍楼顶", "images": null, "permission_level": "characters_only", "allow_comments": true, "allow_likes": true, "allow_sharing": false, "ai_safety_score": "0.99", "comment_count": 1, "created_at": "2025-07-28T13:38:02.272Z", "updated_at": "2025-07-28T13:38:02.272Z"}]}, "characters": {"count": 12, "schema": [{"name": "id", "type": "uuid", "nullable": false, "default": "uuid_generate_v4()"}, {"name": "creator_id", "type": "uuid", "nullable": false, "default": null}, {"name": "name", "type": "character varying", "nullable": false, "default": null}, {"name": "description", "type": "text", "nullable": true, "default": null}, {"name": "avatar_url", "type": "character varying", "nullable": true, "default": null}, {"name": "gender", "type": "character varying", "nullable": true, "default": null}, {"name": "relationship", "type": "character varying", "nullable": true, "default": null}, {"name": "custom_relationship", "type": "character varying", "nullable": true, "default": null}, {"name": "is_lighted", "type": "boolean", "nullable": false, "default": "false"}, {"name": "lighting_count", "type": "integer", "nullable": false, "default": "0"}, {"name": "lighter_user_id", "type": "uuid", "nullable": true, "default": null}, {"name": "first_lighted_at", "type": "timestamp without time zone", "nullable": true, "default": null}, {"name": "is_active", "type": "boolean", "nullable": false, "default": "true"}, {"name": "created_at", "type": "timestamp without time zone", "nullable": false, "default": "now()"}, {"name": "updated_at", "type": "timestamp without time zone", "nullable": false, "default": "now()"}], "samples": [{"id": "35a75c46-0722-46b8-b729-4a1f52daec93", "creator_id": "835952e2-d4e5-47c0-8470-179d338d81de", "name": "<PERSON>", "description": "大学室友，现在在深圳做程序员。性格内向但很靠谱，是宿舍里的技术担当。当年最爱熬夜写代码，经常帮大家修电脑。", "avatar_url": "https://ygs-oss-dev.oss-cn-hangzhou.aliyuncs.com/characters/liming.jpg", "gender": "male", "relationship": "室友", "custom_relationship": null, "is_lighted": true, "lighting_count": 1, "lighter_user_id": "c750db1e-57ff-4fee-8bcd-************", "first_lighted_at": "2025-07-28T13:38:05.667Z", "is_active": true, "created_at": "2025-07-28T13:38:02.272Z", "updated_at": "2025-07-28T13:38:02.272Z"}, {"id": "a30341af-4e74-4590-85c7-7f754cd1e143", "creator_id": "835952e2-d4e5-47c0-8470-179d338d81de", "name": "王强", "description": "大学室友，现在北京创业。宿舍里的开心果，总是充满正能量。创业虽然辛苦，但他从不抱怨。", "avatar_url": "https://ygs-oss-dev.oss-cn-hangzhou.aliyuncs.com/characters/wangqiang.jpg", "gender": "male", "relationship": "室友", "custom_relationship": null, "is_lighted": false, "lighting_count": 0, "lighter_user_id": null, "first_lighted_at": null, "is_active": true, "created_at": "2025-07-28T13:38:02.272Z", "updated_at": "2025-07-28T13:38:02.272Z"}]}, "themes": {"count": 20, "schema": [{"name": "id", "type": "uuid", "nullable": false, "default": "uuid_generate_v4()"}, {"name": "name", "type": "character varying", "nullable": false, "default": null}, {"name": "description", "type": "text", "nullable": true, "default": null}, {"name": "icon", "type": "character varying", "nullable": true, "default": null}, {"name": "color", "type": "character varying", "nullable": true, "default": null}, {"name": "sort_order", "type": "integer", "nullable": false, "default": "0"}, {"name": "is_active", "type": "boolean", "nullable": false, "default": "true"}, {"name": "created_at", "type": "timestamp without time zone", "nullable": false, "default": "now()"}], "samples": [{"id": "32c66bdd-99aa-4c39-b84b-6ad174f96244", "name": "友情", "description": "关于朋友之间珍贵情谊的故事，记录友谊的美好时光", "icon": null, "color": null, "sort_order": 1, "is_active": true, "created_at": "2025-07-28T13:38:02.272Z"}, {"id": "46388628-c9f1-47b2-b222-2db6c52cdb2f", "name": "亲情", "description": "家人之间的温暖故事，血浓于水的情感纽带", "icon": null, "color": null, "sort_order": 2, "is_active": true, "created_at": "2025-07-28T13:38:02.272Z"}]}, "comments": {"count": 15, "schema": [{"name": "id", "type": "uuid", "nullable": false, "default": "uuid_generate_v4()"}, {"name": "targetType", "type": "USER-DEFINED", "nullable": false, "default": null}, {"name": "targetId", "type": "uuid", "nullable": false, "default": null}, {"name": "content", "type": "text", "nullable": false, "default": null}, {"name": "authorId", "type": "uuid", "nullable": false, "default": null}, {"name": "status", "type": "USER-DEFINED", "nullable": false, "default": "'active'::comments_status_enum"}, {"name": "likeCount", "type": "integer", "nullable": false, "default": "0"}, {"name": "replyCount", "type": "integer", "nullable": false, "default": "0"}, {"name": "rootCommentId", "type": "uuid", "nullable": true, "default": null}, {"name": "parentCommentId", "type": "uuid", "nullable": true, "default": null}, {"name": "depth", "type": "integer", "nullable": false, "default": "0"}, {"name": "metadata", "type": "jsonb", "nullable": true, "default": null}, {"name": "createdAt", "type": "timestamp with time zone", "nullable": false, "default": "now()"}, {"name": "updatedAt", "type": "timestamp with time zone", "nullable": false, "default": "now()"}], "samples": [{"id": "64d74186-caf8-4a26-8c50-ae74610ac6dd", "targetType": "story", "targetId": "7f3616ec-3c82-4517-b02a-5950340f611b", "content": "看到这个故事，眼泪都出来了。我也有这样的大学室友，虽然现在各奔东西，但感情依然很深。真希望能像你们一样有个十年之约！", "authorId": "c750db1e-57ff-4fee-8bcd-************", "status": "active", "likeCount": 23, "replyCount": 0, "rootCommentId": null, "parentCommentId": null, "depth": 0, "metadata": null, "createdAt": "2025-07-23T13:38:09.882Z", "updatedAt": "2025-07-28T13:38:02.272Z"}, {"id": "7cfc4ec0-ff18-4d81-9259-01de2e1f0b45", "targetType": "story", "targetId": "7f3616ec-3c82-4517-b02a-5950340f611b", "content": "年轻真好啊！看着你们的故事，想起了我当年教过的学生们。现在他们也都成家立业了，偶尔会想念那些青春岁月。", "authorId": "39ef402f-7c2c-46c8-8938-f6efa376a49f", "status": "active", "likeCount": 15, "replyCount": 0, "rootCommentId": null, "parentCommentId": null, "depth": 0, "metadata": null, "createdAt": "2025-07-24T13:38:09.925Z", "updatedAt": "2025-07-28T13:38:02.272Z"}]}, "comment_likes": {"count": 8, "schema": [{"name": "id", "type": "uuid", "nullable": false, "default": "uuid_generate_v4()"}, {"name": "commentId", "type": "uuid", "nullable": false, "default": null}, {"name": "userId", "type": "uuid", "nullable": false, "default": null}, {"name": "createdAt", "type": "timestamp with time zone", "nullable": false, "default": "now()"}], "samples": [{"id": "4cfd7019-8e29-43b6-a3c5-7e24e22c63ea", "commentId": "64d74186-caf8-4a26-8c50-ae74610ac6dd", "userId": "835952e2-d4e5-47c0-8470-179d338d81de", "createdAt": "2025-07-23T13:38:10.539Z"}, {"id": "c784f20e-a594-4d5e-9025-d91aa67f6f5a", "commentId": "64d74186-caf8-4a26-8c50-ae74610ac6dd", "userId": "39ef402f-7c2c-46c8-8938-f6efa376a49f", "createdAt": "2025-07-24T13:38:10.582Z"}]}, "bookmarks": {"count": 20, "schema": [{"name": "id", "type": "uuid", "nullable": false, "default": "uuid_generate_v4()"}, {"name": "userId", "type": "uuid", "nullable": false, "default": null}, {"name": "storyId", "type": "uuid", "nullable": false, "default": null}, {"name": "category", "type": "USER-DEFINED", "nullable": false, "default": "'default'::bookmarks_category_enum"}, {"name": "customCategoryName", "type": "character varying", "nullable": true, "default": null}, {"name": "status", "type": "USER-DEFINED", "nullable": false, "default": "'active'::bookmarks_status_enum"}, {"name": "note", "type": "text", "nullable": true, "default": null}, {"name": "storySnapshot", "type": "jsonb", "nullable": true, "default": null}, {"name": "metadata", "type": "jsonb", "nullable": true, "default": null}, {"name": "createdAt", "type": "timestamp with time zone", "nullable": false, "default": "now()"}, {"name": "updatedAt", "type": "timestamp with time zone", "nullable": false, "default": "now()"}], "samples": [{"id": "fb082288-9315-4ba3-a904-3efc2ea60035", "userId": "c750db1e-57ff-4fee-8bcd-************", "storyId": "7f3616ec-3c82-4517-b02a-5950340f611b", "category": "favorites", "customCategoryName": null, "status": "active", "note": "这是我点亮的第一个人物的故事，非常有纪念意义。看到李明的故事，想起了大学时光。", "storySnapshot": null, "metadata": {"tags": ["点亮", "大学", "友情", "珍贵回忆"], "isPublic": true, "priority": "high"}, "createdAt": "2025-06-30T13:38:10.885Z", "updatedAt": "2025-07-28T13:38:02.272Z"}, {"id": "e0031501-f0e2-4931-ac54-9e2bb741bedd", "userId": "c750db1e-57ff-4fee-8bcd-************", "storyId": "019afdc6-c93d-47d4-9ce7-baa2d14820c9", "category": "favorites", "customCategoryName": null, "status": "active", "note": "另一个李明的故事，更加深刻和感人。那个雨夜的陪伴，让我想起了自己的经历。", "storySnapshot": null, "metadata": {"tags": ["点亮", "深度", "友情", "感动时刻"], "isPublic": true, "priority": "high"}, "createdAt": "2025-07-04T13:38:10.928Z", "updatedAt": "2025-07-28T13:38:02.272Z"}]}, "notifications": {"count": 23, "schema": [{"name": "id", "type": "uuid", "nullable": false, "default": "uuid_generate_v4()"}, {"name": "user_id", "type": "uuid", "nullable": false, "default": null}, {"name": "type", "type": "character varying", "nullable": false, "default": null}, {"name": "title", "type": "character varying", "nullable": false, "default": null}, {"name": "content", "type": "text", "nullable": true, "default": null}, {"name": "related_id", "type": "character varying", "nullable": true, "default": null}, {"name": "action_url", "type": "character varying", "nullable": true, "default": null}, {"name": "is_read", "type": "boolean", "nullable": false, "default": "false"}, {"name": "read_at", "type": "timestamp without time zone", "nullable": true, "default": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": false, "default": "now()"}], "samples": [{"id": "6ee0e104-b9a9-4d1e-abee-c94c95e578cd", "user_id": "835952e2-d4e5-47c0-8470-179d338d81de", "type": "lighting_confirmed", "title": "🎉 您的人物被成功点亮！", "content": "李小红 成功点亮了您故事《那年夏天，我们约定十年后再聚》中的人物\"李明\"。这是一次真实身份的确认，恭喜您找到了故事中的朋友！", "related_id": null, "action_url": null, "is_read": false, "read_at": null, "created_at": "2025-06-30T13:38:14.592Z"}, {"id": "a6238b7c-e784-40c9-88ec-8f3db41e252a", "user_id": "835952e2-d4e5-47c0-8470-179d338d81de", "type": "lighting_confirmed", "title": "🌟 又一个人物被点亮！", "content": "李小红 成功点亮了您故事《李明，还记得那个雨夜吗》中的人物\"明哥\"。您的故事触动了真实的人，这是写作的最高荣誉！", "related_id": null, "action_url": null, "is_read": true, "read_at": null, "created_at": "2025-07-04T13:38:14.635Z"}]}, "timeline_events": {"count": 34, "schema": [{"name": "id", "type": "uuid", "nullable": false, "default": "uuid_generate_v4()"}, {"name": "userId", "type": "uuid", "nullable": false, "default": null}, {"name": "title", "type": "character varying", "nullable": false, "default": null}, {"name": "description", "type": "text", "nullable": true, "default": null}, {"name": "eventType", "type": "USER-DEFINED", "nullable": false, "default": null}, {"name": "status", "type": "USER-DEFINED", "nullable": false, "default": "'active'::timeline_events_status_enum"}, {"name": "visibility", "type": "USER-DEFINED", "nullable": false, "default": "'private'::timeline_events_visibility_enum"}, {"name": "importance", "type": "USER-DEFINED", "nullable": false, "default": "'2'::timeline_events_importance_enum"}, {"name": "eventDate", "type": "date", "nullable": false, "default": null}, {"name": "endDate", "type": "date", "nullable": true, "default": null}, {"name": "location", "type": "character varying", "nullable": true, "default": null}, {"name": "relatedPeople", "type": "text", "nullable": true, "default": null}, {"name": "tags", "type": "text", "nullable": true, "default": null}, {"name": "relatedStoryId", "type": "uuid", "nullable": true, "default": null}, {"name": "images", "type": "text", "nullable": true, "default": null}, {"name": "attachments", "type": "text", "nullable": true, "default": null}, {"name": "color", "type": "character varying", "nullable": true, "default": null}, {"name": "icon", "type": "character varying", "nullable": true, "default": null}, {"name": "isMilestone", "type": "boolean", "nullable": false, "default": "false"}, {"name": "isPinned", "type": "boolean", "nullable": false, "default": "false"}, {"name": "likesCount", "type": "integer", "nullable": false, "default": "0"}, {"name": "commentsCount", "type": "integer", "nullable": false, "default": "0"}, {"name": "viewCount", "type": "integer", "nullable": false, "default": "0"}, {"name": "customData", "type": "jsonb", "nullable": true, "default": null}, {"name": "metadata", "type": "jsonb", "nullable": true, "default": null}, {"name": "createdAt", "type": "timestamp with time zone", "nullable": false, "default": "now()"}, {"name": "updatedAt", "type": "timestamp with time zone", "nullable": false, "default": "now()"}], "samples": [{"id": "1060cec0-28b6-46c9-8367-ee5a6a729fdb", "userId": "835952e2-d4e5-47c0-8470-179d338d81de", "title": "出生在美丽的杭州", "description": "1990年5月15日，我来到了这个世界。父母说那是一个阳光明媚的春日，正如他们对我人生的期待。", "eventType": "birth", "status": "active", "visibility": "public", "importance": "4", "eventDate": "1990-05-14T15:00:00.000Z", "endDate": null, "location": "杭州市第一人民医院", "relatedPeople": null, "tags": "出生,家庭,起点", "relatedStoryId": null, "images": null, "attachments": null, "color": null, "icon": null, "isMilestone": false, "isPinned": false, "likesCount": 0, "commentsCount": 0, "viewCount": 0, "customData": null, "metadata": null, "createdAt": "2025-07-28T13:38:02.272Z", "updatedAt": "2025-07-28T13:38:02.272Z"}, {"id": "b3449914-909a-4b02-adba-d6e17a4e3b3f", "userId": "835952e2-d4e5-47c0-8470-179d338d81de", "title": "考入理想大学", "description": "经过高三一年的努力，终于考入了心仪的大学。那一刻，觉得所有的辛苦都值得了。", "eventType": "education", "status": "active", "visibility": "public", "importance": "3", "eventDate": "2008-08-31T16:00:00.000Z", "endDate": null, "location": "某大学", "relatedPeople": null, "tags": "大学,教育,成长", "relatedStoryId": null, "images": null, "attachments": null, "color": null, "icon": null, "isMilestone": false, "isPinned": false, "likesCount": 0, "commentsCount": 0, "viewCount": 0, "customData": null, "metadata": null, "createdAt": "2025-07-28T13:38:02.272Z", "updatedAt": "2025-07-28T13:38:02.272Z"}]}, "shares": {"count": 11, "schema": [{"name": "id", "type": "uuid", "nullable": false, "default": "uuid_generate_v4()"}, {"name": "token", "type": "character varying", "nullable": false, "default": null}, {"name": "storyId", "type": "uuid", "nullable": false, "default": null}, {"name": "userId", "type": "uuid", "nullable": false, "default": null}, {"name": "shareType", "type": "USER-DEFINED", "nullable": false, "default": null}, {"name": "status", "type": "USER-DEFINED", "nullable": false, "default": "'active'::shares_status_enum"}, {"name": "password", "type": "character varying", "nullable": true, "default": null}, {"name": "accessLimit", "type": "integer", "nullable": false, "default": "'-1'::integer"}, {"name": "accessCount", "type": "integer", "nullable": false, "default": "0"}, {"name": "expiresAt", "type": "timestamp with time zone", "nullable": true, "default": null}, {"name": "description", "type": "text", "nullable": true, "default": null}, {"name": "storySnapshot", "type": "jsonb", "nullable": true, "default": null}, {"name": "metadata", "type": "jsonb", "nullable": true, "default": null}, {"name": "createdAt", "type": "timestamp with time zone", "nullable": false, "default": "now()"}, {"name": "updatedAt", "type": "timestamp with time zone", "nullable": false, "default": "now()"}], "samples": [{"id": "eef52343-6f19-485d-bdf4-984e0c09e9f9", "token": "share_vip_recommend_001", "storyId": "7f3616ec-3c82-4517-b02a-5950340f611b", "userId": "1876b870-e94d-4c92-832a-eabe42313210", "shareType": "public", "status": "active", "password": null, "accessLimit": -1, "accessCount": 156, "expiresAt": null, "description": null, "storySnapshot": null, "metadata": null, "createdAt": "2025-06-28T13:38:13.217Z", "updatedAt": "2025-07-28T13:38:02.272Z"}, {"id": "c8ebed83-c7eb-49b5-aabb-b0c1e8e59b8c", "token": "share_student_xiaoyu_001", "storyId": "c53ef769-b804-4133-b3c1-f34c56b0f279", "userId": "39ef402f-7c2c-46c8-8938-f6efa376a49f", "shareType": "public", "status": "active", "password": null, "accessLimit": -1, "accessCount": 89, "expiresAt": null, "description": null, "storySnapshot": null, "metadata": null, "createdAt": "2025-07-03T13:38:13.262Z", "updatedAt": "2025-07-28T13:38:02.272Z"}]}, "share_access_logs": {"count": 21, "schema": [{"name": "id", "type": "uuid", "nullable": false, "default": "uuid_generate_v4()"}, {"name": "shareId", "type": "uuid", "nullable": true, "default": null}, {"name": "ip<PERSON><PERSON><PERSON>", "type": "inet", "nullable": false, "default": null}, {"name": "userAgent", "type": "text", "nullable": true, "default": null}, {"name": "referer", "type": "text", "nullable": true, "default": null}, {"name": "userId", "type": "uuid", "nullable": true, "default": null}, {"name": "deviceType", "type": "character varying", "nullable": true, "default": null}, {"name": "isSuccessful", "type": "boolean", "nullable": false, "default": "true"}, {"name": "failureReason", "type": "text", "nullable": true, "default": null}, {"name": "metadata", "type": "jsonb", "nullable": true, "default": null}, {"name": "createdAt", "type": "timestamp with time zone", "nullable": false, "default": "now()"}], "samples": [{"id": "bb756074-219c-49f8-abcc-a001d252690f", "shareId": "92140f51-a417-4888-9b96-e0e93982a070", "ipAddress": "*************", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", "referer": "https://weixin.qq.com", "userId": "c750db1e-57ff-4fee-8bcd-************", "deviceType": null, "isSuccessful": true, "failureReason": null, "metadata": {"location": "上海"}, "createdAt": "2025-07-09T13:38:13.694Z"}, {"id": "5f114c01-a146-4a23-a4b5-c57de2376ede", "shareId": "92140f51-a417-4888-9b96-e0e93982a070", "ipAddress": "*************", "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15", "referer": "https://weixin.qq.com", "userId": "39ef402f-7c2c-46c8-8938-f6efa376a49f", "deviceType": null, "isSuccessful": true, "failureReason": null, "metadata": {"location": "北京"}, "createdAt": "2025-07-10T13:38:13.737Z"}]}, "light_requests": {"count": 13, "schema": [{"name": "id", "type": "uuid", "nullable": false, "default": "uuid_generate_v4()"}, {"name": "story_id", "type": "uuid", "nullable": false, "default": null}, {"name": "character_id", "type": "uuid", "nullable": false, "default": null}, {"name": "requester_id", "type": "uuid", "nullable": false, "default": null}, {"name": "story_author_id", "type": "uuid", "nullable": false, "default": null}, {"name": "phone_verification", "type": "character varying", "nullable": true, "default": null}, {"name": "has_phone_verification", "type": "boolean", "nullable": false, "default": "false"}, {"name": "message", "type": "text", "nullable": true, "default": null}, {"name": "status", "type": "character varying", "nullable": false, "default": "'active'::character varying"}, {"name": "expires_at", "type": "timestamp without time zone", "nullable": false, "default": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": false, "default": "now()"}, {"name": "processed_at", "type": "timestamp without time zone", "nullable": true, "default": null}], "samples": [{"id": "c362efc0-b818-46d9-9772-d38d76d889cb", "story_id": "7f3616ec-3c82-4517-b02a-5950340f611b", "character_id": "35a75c46-0722-46b8-b729-4a1f52daec93", "requester_id": "c750db1e-57ff-4fee-8bcd-************", "story_author_id": "835952e2-d4e5-47c0-8470-179d338d81de", "phone_verification": "138****8002", "has_phone_verification": true, "message": "李明，我是小红！还记得我吗？大学时我们一起参加过编程比赛，你还帮我调试代码到凌晨。看到这个故事，满满都是回忆。", "status": "approved", "expires_at": "2025-07-05T13:38:09.106Z", "created_at": "2025-06-28T13:38:09.106Z", "processed_at": "2025-06-30T13:38:09.106Z"}, {"id": "da7d8e90-c52b-4f19-a912-2fd9f6c6be24", "story_id": "019afdc6-c93d-47d4-9ce7-baa2d14820c9", "character_id": "1a615fe3-0fc4-472f-962a-5c695ac136f8", "requester_id": "c750db1e-57ff-4fee-8bcd-************", "story_author_id": "835952e2-d4e5-47c0-8470-179d338d81de", "phone_verification": "138****8002", "has_phone_verification": true, "message": "明远，我就是那个雨夜陪你的李明。看到这个故事，我也哭了。那些年的友谊，是我最珍贵的回忆。", "status": "approved", "expires_at": "2025-07-10T13:38:09.194Z", "created_at": "2025-07-03T13:38:09.194Z", "processed_at": "2025-07-04T13:38:09.194Z"}]}, "character_lightings": {"count": 5, "schema": [{"name": "id", "type": "uuid", "nullable": false, "default": "uuid_generate_v4()"}, {"name": "character_id", "type": "uuid", "nullable": false, "default": null}, {"name": "story_id", "type": "uuid", "nullable": false, "default": null}, {"name": "lighter_user_id", "type": "uuid", "nullable": false, "default": null}, {"name": "creator_user_id", "type": "uuid", "nullable": false, "default": null}, {"name": "request_id", "type": "uuid", "nullable": true, "default": null}, {"name": "status", "type": "character varying", "nullable": false, "default": "'active'::character varying"}, {"name": "confirmed_at", "type": "timestamp without time zone", "nullable": true, "default": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": false, "default": "now()"}], "samples": [{"id": "4e3dd0f2-ef6e-4079-8553-6145659ea63d", "character_id": "35a75c46-0722-46b8-b729-4a1f52daec93", "story_id": "7f3616ec-3c82-4517-b02a-5950340f611b", "lighter_user_id": "c750db1e-57ff-4fee-8bcd-************", "creator_user_id": "835952e2-d4e5-47c0-8470-179d338d81de", "request_id": "c362efc0-b818-46d9-9772-d38d76d889cb", "status": "active", "confirmed_at": "2025-06-30T13:38:09.106Z", "created_at": "2025-07-28T13:38:02.272Z"}, {"id": "def74427-2b4a-445d-a3d6-8f764cc58b94", "character_id": "1a615fe3-0fc4-472f-962a-5c695ac136f8", "story_id": "019afdc6-c93d-47d4-9ce7-baa2d14820c9", "lighter_user_id": "c750db1e-57ff-4fee-8bcd-************", "creator_user_id": "835952e2-d4e5-47c0-8470-179d338d81de", "request_id": "da7d8e90-c52b-4f19-a912-2fd9f6c6be24", "status": "active", "confirmed_at": "2025-07-04T13:38:09.194Z", "created_at": "2025-07-28T13:38:02.272Z"}]}, "user_follows": {"count": 16, "schema": [{"name": "id", "type": "uuid", "nullable": false, "default": "uuid_generate_v4()"}, {"name": "followerId", "type": "uuid", "nullable": false, "default": null}, {"name": "followingId", "type": "uuid", "nullable": false, "default": null}, {"name": "status", "type": "USER-DEFINED", "nullable": false, "default": "'active'::user_follows_status_enum"}, {"name": "isSpecial", "type": "boolean", "nullable": false, "default": "false"}, {"name": "settings", "type": "jsonb", "nullable": true, "default": null}, {"name": "metadata", "type": "jsonb", "nullable": true, "default": null}, {"name": "createdAt", "type": "timestamp with time zone", "nullable": false, "default": "now()"}], "samples": [{"id": "e15181d6-a21e-43d7-89bd-f601be1023b4", "followerId": "835952e2-d4e5-47c0-8470-179d338d81de", "followingId": "c750db1e-57ff-4fee-8bcd-************", "status": "active", "isSpecial": true, "settings": null, "metadata": {"note": "点亮了我故事中的李明"}, "createdAt": "2025-07-28T13:38:02.272Z"}, {"id": "3648d807-8098-4842-ac55-0b5cc9faffcb", "followerId": "c750db1e-57ff-4fee-8bcd-************", "followingId": "835952e2-d4e5-47c0-8470-179d338d81de", "status": "active", "isSpecial": true, "settings": null, "metadata": {"note": "写出了感人的故事"}, "createdAt": "2025-07-28T13:38:02.272Z"}]}, "friend_groups": {"count": 11, "schema": [{"name": "id", "type": "uuid", "nullable": false, "default": "uuid_generate_v4()"}, {"name": "name", "type": "character varying", "nullable": false, "default": null}, {"name": "description", "type": "text", "nullable": true, "default": null}, {"name": "ownerId", "type": "uuid", "nullable": false, "default": null}, {"name": "groupType", "type": "USER-DEFINED", "nullable": false, "default": "'custom'::friend_groups_grouptype_enum"}, {"name": "visibility", "type": "USER-DEFINED", "nullable": false, "default": "'private'::friend_groups_visibility_enum"}, {"name": "colorTag", "type": "character varying", "nullable": true, "default": null}, {"name": "memberCount", "type": "integer", "nullable": false, "default": "0"}, {"name": "sortOrder", "type": "integer", "nullable": false, "default": "0"}, {"name": "isActive", "type": "boolean", "nullable": false, "default": "true"}, {"name": "permissions", "type": "jsonb", "nullable": true, "default": null}, {"name": "metadata", "type": "jsonb", "nullable": true, "default": null}, {"name": "createdAt", "type": "timestamp with time zone", "nullable": false, "default": "now()"}, {"name": "updatedAt", "type": "timestamp with time zone", "nullable": false, "default": "now()"}], "samples": [{"id": "bd67ffc0-ca21-4f3d-8b4e-0791226bc8c7", "name": "大学兄弟", "description": "大学时期的室友和好友", "ownerId": "835952e2-d4e5-47c0-8470-179d338d81de", "groupType": "custom", "visibility": "private", "colorTag": null, "memberCount": 2, "sortOrder": 0, "isActive": true, "permissions": null, "metadata": null, "createdAt": "2025-07-28T13:38:02.272Z", "updatedAt": "2025-07-28T13:38:02.272Z"}, {"id": "f725ba53-3acd-4d88-8c3c-cf59e2ac8122", "name": "创业伙伴", "description": "一起创业的朋友们", "ownerId": "835952e2-d4e5-47c0-8470-179d338d81de", "groupType": "custom", "visibility": "private", "colorTag": null, "memberCount": 1, "sortOrder": 0, "isActive": true, "permissions": null, "metadata": null, "createdAt": "2025-07-28T13:38:02.272Z", "updatedAt": "2025-07-28T13:38:02.272Z"}]}}}