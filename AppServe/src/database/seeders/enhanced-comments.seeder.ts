/**
 * 企业级评论系统种子数据
 *
 * 基于 YGS v1.0.0 多级评论系统设计
 * 涵盖评论、回复、点赞等完整互动场景
 */

import { USER_ROLES } from "./enhanced-user.seeder";
import { STORY_TITLES } from "./enhanced-story.seeder";

export interface EnhancedCommentSeedData {
  storyTitle: string;
  authorUsername: string;
  content: string;
  status: "active" | "deleted" | "hidden" | "reported";
  parentCommentContent?: string; // 父评论内容（用于查找父评论）
  likeCount: number;
  isEdited: boolean;
  createdDaysAgo: number;
  tags: string[];
}

export interface EnhancedCommentLikeSeedData {
  commentContent: string; // 评论内容（用于查找评论）
  likerUsername: string;
  createdDaysAgo: number;
}

export const ENHANCED_COMMENT_SEED_DATA: EnhancedCommentSeedData[] = [
  // ========== 《那年夏天，我们约定十年后再聚》的评论区 ==========
  {
    storyTitle: STORY_TITLES.UNIVERSITY_PROMISE,
    authorUsername: USER_ROLES.LI_XIAOHONG,
    content:
      "看到这个故事，眼泪都出来了。我也有这样的大学室友，虽然现在各奔东西，但感情依然很深。真希望能像你们一样有个十年之约！",
    status: "active",
    likeCount: 23,
    isEdited: false,
    createdDaysAgo: 5,
    tags: ["感动", "共鸣", "友情"],
  },
  {
    storyTitle: STORY_TITLES.UNIVERSITY_PROMISE,
    authorUsername: USER_ROLES.WANG_JIANGUO,
    content:
      "年轻真好啊！看着你们的故事，想起了我当年教过的学生们。现在他们也都成家立业了，偶尔会想念那些青春岁月。",
    status: "active",
    likeCount: 15,
    isEdited: false,
    createdDaysAgo: 4,
    tags: ["怀念", "师者心得"],
  },
  {
    storyTitle: STORY_TITLES.UNIVERSITY_PROMISE,
    authorUsername: USER_ROLES.ZHANG_MINGYUAN,
    content:
      "@小红姐姐 谢谢你的点亮！没想到能在这里找到你，真的是太意外了。期待我们也能有机会见面聊聊这些年的变化。",
    status: "active",
    parentCommentContent:
      "看到这个故事，眼泪都出来了。我也有这样的大学室友，虽然现在各奔东西，但感情依然很深。真希望能像你们一样有个十年之约！",
    likeCount: 8,
    isEdited: false,
    createdDaysAgo: 3,
    tags: ["回复", "感谢"],
  },
  {
    storyTitle: STORY_TITLES.UNIVERSITY_PROMISE,
    authorUsername: USER_ROLES.ZHAO_XIAOFANG,
    content:
      "好温暖的故事！友情真的是人生最珍贵的财富。希望你们的十年之约能如期举行，也希望我能找到这样的朋友。",
    status: "active",
    likeCount: 12,
    isEdited: false,
    createdDaysAgo: 2,
    tags: ["祝福", "羡慕"],
  },

  // ========== 《小雨，你现在过得还好吗》的评论区 ==========
  {
    storyTitle: STORY_TITLES.STUDENT_XIAOYU,
    authorUsername: USER_ROLES.ZHOU_FAXIAN,
    content:
      "王老师，我就是小雨！看到这个故事真的很感动，您还记得我。是您的鼓励让我有勇气面对困难，现在我在医院工作，也在用自己的方式帮助别人。谢谢您！",
    status: "active",
    likeCount: 45,
    isEdited: false,
    createdDaysAgo: 7,
    tags: ["本人回应", "感谢师恩"],
  },
  {
    storyTitle: STORY_TITLES.STUDENT_XIAOYU,
    authorUsername: USER_ROLES.WANG_JIANGUO,
    content:
      "@发现自己 小雨！真的是你！看到你成为医生，我真的很欣慰。你的坚强一直是我教学路上的动力，谢谢你让我明白教育的意义。",
    status: "active",
    parentCommentContent:
      "王老师，我就是小雨！看到这个故事真的很感动，您还记得我。是您的鼓励让我有勇气面对困难，现在我在医院工作，也在用自己的方式帮助别人。谢谢您！",
    likeCount: 38,
    isEdited: false,
    createdDaysAgo: 6,
    tags: ["师生重逢", "欣慰"],
  },
  {
    storyTitle: STORY_TITLES.STUDENT_XIAOYU,
    authorUsername: USER_ROLES.LI_XIAOHONG,
    content:
      "太感人了！师生情真的很珍贵。小雨你真的很棒，从困难中走出来还成为了医生，你是很多人的榜样！",
    status: "active",
    likeCount: 20,
    isEdited: false,
    createdDaysAgo: 5,
    tags: ["赞扬", "励志"],
  },
  {
    storyTitle: STORY_TITLES.STUDENT_XIAOYU,
    authorUsername: USER_ROLES.CHEN_VIP,
    content:
      "这样的师生关系真让人羡慕。王老师和小雨的故事证明了教育的力量，也证明了坚持的意义。向你们致敬！",
    status: "active",
    likeCount: 16,
    isEdited: false,
    createdDaysAgo: 4,
    tags: ["致敬", "教育意义"],
  },

  // ========== 《三十年教书，我最后悔的一件事》的评论区 ==========
  {
    storyTitle: STORY_TITLES.TEACHING_REGRET,
    authorUsername: USER_ROLES.ZHAO_XIAOFANG,
    content:
      "王老师，您的反思让我想起了我的小学老师。她也曾经严厉地批评过我，但我知道老师们都是为了学生好。您能认识到这一点，说明您是一位真正的好老师。",
    status: "active",
    likeCount: 28,
    isEdited: false,
    createdDaysAgo: 8,
    tags: ["理解", "宽容"],
  },
  {
    storyTitle: STORY_TITLES.TEACHING_REGRET,
    authorUsername: USER_ROLES.LI_XIAOHONG,
    content:
      "每个人都会犯错，重要的是能够反思和成长。小军能原谅您，说明他也成长为了一个宽容的人。这或许也是您教育的成果。",
    status: "active",
    likeCount: 22,
    isEdited: false,
    createdDaysAgo: 7,
    tags: ["成长", "救赎"],
  },

  // ========== 《那些年，我们一起追过的剧》的评论区 ==========
  {
    storyTitle: STORY_TITLES.DRAMA_MEMORIES,
    authorUsername: USER_ROLES.LI_XIAOHONG,
    content:
      "哈哈，我就是评论里提到的晓晓！现在看电视剧还是会哭，真的是改不了的毛病。那时候追剧的日子真的很快乐！",
    status: "active",
    likeCount: 35,
    isEdited: false,
    createdDaysAgo: 6,
    tags: ["本人回应", "怀念"],
  },
  {
    storyTitle: STORY_TITLES.DRAMA_MEMORIES,
    authorUsername: USER_ROLES.ZHAO_XIAOFANG,
    content:
      "大学时光真的很美好！我也有过这样的室友情谊，一起追剧、一起哭一起笑。现在大家都忙着工作，很难再有这样纯粹的快乐了。",
    status: "active",
    likeCount: 18,
    isEdited: false,
    createdDaysAgo: 5,
    tags: ["共鸣", "怀念青春"],
  },

  // ========== 争议评论和管理场景 ==========
  {
    storyTitle: STORY_TITLES.UNIVERSITY_PROMISE,
    authorUsername: USER_ROLES.SUN_WENTI,
    content: "这种故事有什么意思？都是编的吧，现在谁还相信真友情？",
    status: "hidden", // 因为负面情绪被隐藏
    likeCount: 0,
    isEdited: false,
    createdDaysAgo: 10,
    tags: ["负面", "质疑"],
  },
  {
    storyTitle: STORY_TITLES.STUDENT_XIAOYU,
    authorUsername: USER_ROLES.LIU_XIAOXIN,
    content: "这个评论已经被作者删除了。", // 模拟删除的评论
    status: "deleted",
    likeCount: 3,
    isEdited: false,
    createdDaysAgo: 12,
    tags: ["已删除"],
  },

  // ========== 多级回复示例 ==========
  {
    storyTitle: STORY_TITLES.UNIVERSITY_PROMISE,
    authorUsername: USER_ROLES.CHEN_VIP,
    content:
      "@小红姐姐 @明远说 你们这样的重逢真的很难得！希望更多人能通过点亮系统找到自己的朋友。",
    status: "active",
    parentCommentContent:
      "@小红姐姐 谢谢你的点亮！没想到能在这里找到你，真的是太意外了。期待我们也能有机会见面聊聊这些年的变化。",
    likeCount: 5,
    isEdited: false,
    createdDaysAgo: 1,
    tags: ["祝福", "点亮系统"],
  },
];

export const ENHANCED_COMMENT_LIKE_SEED_DATA: EnhancedCommentLikeSeedData[] = [
  // 热门评论的点赞数据
  {
    commentContent:
      "看到这个故事，眼泪都出来了。我也有这样的大学室友，虽然现在各奔东西，但感情依然很深。真希望能像你们一样有个十年之约！",
    likerUsername: USER_ROLES.ZHANG_MINGYUAN,
    createdDaysAgo: 5,
  },
  {
    commentContent:
      "看到这个故事，眼泪都出来了。我也有这样的大学室友，虽然现在各奔东西，但感情依然很深。真希望能像你们一样有个十年之约！",
    likerUsername: USER_ROLES.WANG_JIANGUO,
    createdDaysAgo: 4,
  },
  {
    commentContent:
      "看到这个故事，眼泪都出来了。我也有这样的大学室友，虽然现在各奔东西，但感情依然很深。真希望能像你们一样有个十年之约！",
    likerUsername: USER_ROLES.CHEN_VIP,
    createdDaysAgo: 4,
  },
  {
    commentContent:
      "王老师，我就是小雨！看到这个故事真的很感动，您还记得我。是您的鼓励让我有勇气面对困难，现在我在医院工作，也在用自己的方式帮助别人。谢谢您！",
    likerUsername: USER_ROLES.WANG_JIANGUO,
    createdDaysAgo: 7,
  },
  {
    commentContent:
      "王老师，我就是小雨！看到这个故事真的很感动，您还记得我。是您的鼓励让我有勇气面对困难，现在我在医院工作，也在用自己的方式帮助别人。谢谢您！",
    likerUsername: USER_ROLES.LI_XIAOHONG,
    createdDaysAgo: 6,
  },
  {
    commentContent:
      "王老师，我就是小雨！看到这个故事真的很感动，您还记得我。是您的鼓励让我有勇气面对困难，现在我在医院工作，也在用自己的方式帮助别人。谢谢您！",
    likerUsername: USER_ROLES.CHEN_VIP,
    createdDaysAgo: 6,
  },
  {
    commentContent:
      "哈哈，我就是评论里提到的晓晓！现在看电视剧还是会哭，真的是改不了的毛病。那时候追剧的日子真的很快乐！",
    likerUsername: USER_ROLES.ZHAO_XIAOFANG,
    createdDaysAgo: 6,
  },
  {
    commentContent:
      "哈哈，我就是评论里提到的晓晓！现在看电视剧还是会哭，真的是改不了的毛病。那时候追剧的日子真的很快乐！",
    likerUsername: USER_ROLES.WANG_JIANGUO,
    createdDaysAgo: 5,
  },
];

// 导出评论统计数据
export const COMMENT_STATS = {
  total: ENHANCED_COMMENT_SEED_DATA.length,
  active: ENHANCED_COMMENT_SEED_DATA.filter((c) => c.status === "active")
    .length,
  deleted: ENHANCED_COMMENT_SEED_DATA.filter((c) => c.status === "deleted")
    .length,
  hidden: ENHANCED_COMMENT_SEED_DATA.filter((c) => c.status === "hidden")
    .length,
  replies: ENHANCED_COMMENT_SEED_DATA.filter((c) => c.parentCommentContent)
    .length,
  totalLikes: ENHANCED_COMMENT_LIKE_SEED_DATA.length,
  avgLikesPerComment: Math.round(
    ENHANCED_COMMENT_SEED_DATA.reduce((sum, c) => sum + c.likeCount, 0) /
      ENHANCED_COMMENT_SEED_DATA.length,
  ),
};
