/**
 * 评论模块 - 企业级单元测试
 */

import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { CommentsModule } from "./comments.module";
import { CommentsService } from "./comments.service";
import { CommentsController } from "./comments.controller";

describe("CommentsModule - 企业级模块测试", () => {
  let module: TestingModule;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [CommentsModule],
    })
      .overrideProvider(CommentsService)
      .useValue({
        findAll: jest.fn(),
        findOne: jest.fn(),
        create: jest.fn(),
        update: jest.fn(),
        remove: jest.fn(),
      })
      .compile();
  });

  afterEach(async () => {
    if (module) {
      await module.close();
    }
  });

  describe("模块初始化", () => {
    it("should compile the module", () => {
      expect(module).toBeDefined();
    });

    it("should provide CommentsService", () => {
      const service = module.get<CommentsService>(CommentsService);
      expect(service).toBeDefined();
    });

    it("should provide CommentsController", () => {
      const controller = module.get<CommentsController>(CommentsController);
      expect(controller).toBeDefined();
    });
  });

  describe("依赖注入", () => {
    it("should inject CommentsService into CommentsController", () => {
      const controller = module.get<CommentsController>(CommentsController);
      const service = module.get<CommentsService>(CommentsService);

      expect(controller).toBeDefined();
      expect(service).toBeDefined();
    });
  });

  describe("模块配置", () => {
    it("should be a valid NestJS module", () => {
      expect(CommentsModule).toBeDefined();
      expect(typeof CommentsModule).toBe("function");
    });
  });
});
