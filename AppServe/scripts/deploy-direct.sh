#!/bin/bash

# 有故事APP直接部署脚本 - 不构建镜像，直接运行
# 完全避免Docker构建过程中的网络请求

set -e

echo "🚀 开始直接部署有故事APP生产环境..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 加载环境变量
load_env() {
    if [ -f ".env.production" ]; then
        echo "📋 加载环境变量..."
        source .env.production
        echo -e "${GREEN}✅ 环境变量加载完成${NC}"
    else
        echo -e "${YELLOW}⚠️ .env.production 文件不存在，正在创建...${NC}"
        create_production_env
        source .env.production
    fi
}

# 创建生产环境配置
create_production_env() {
    cat > .env.production << 'ENV_EOF'
# 生产环境配置文件
NODE_ENV=production
PORT=3000
FRONTEND_URL=http://************

# 数据库配置 (Docker网络中的容器名)
DB_HOST=story-app-postgres
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=StoryApp2024!
DB_NAME=story_app_prod
DB_SSL=false

# 生产环境关闭自动同步
AUTO_SYNC=false

# Redis配置 (Docker网络中的容器名)
REDIS_HOST=story-app-redis
REDIS_PORT=6379
REDIS_PASSWORD=StoryApp2024!
REDIS_DB=0
REDIS_KEY_PREFIX=story-app:
REDIS_TTL=3600

# JWT配置 (生产环境使用强密钥)
JWT_SECRET=StoryApp2024-JWT-Secret-Key-Change-This-In-Production
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=StoryApp2024-Refresh-Secret-Key
JWT_REFRESH_EXPIRES_IN=30d
BCRYPT_ROUNDS=12

# 短信服务配置
SMS_PROVIDER=aliyun
SMS_ACCESS_KEY_ID=
SMS_ACCESS_KEY_SECRET=
SMS_SIGN_NAME=有故事APP
SMS_TEMPLATE_CODE=SMS_123456789
VERIFICATION_CODE_EXPIRATION=300

# OpenAI配置
OPENAI_API_KEY=
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_MAX_TOKENS=1000
OPENAI_TEMPERATURE=0.7

# AI服务限制
AI_REQUEST_TIMEOUT=30000
AI_MAX_REQUESTS_PER_USER=50
AI_MAX_REQUESTS_WINDOW=3600

# 限流配置
THROTTLE_TTL=60
THROTTLE_LIMIT=100

# 日志配置
LOG_LEVEL=info
LOG_CONSOLE=true
LOG_FILE=true
LOG_DIRECTORY=./logs
LOG_MAX_FILES=30
LOG_MAX_SIZE=100m
ENV_EOF
    echo -e "${GREEN}✅ 生产环境配置创建完成${NC}"
}

# 验证必要镜像存在
verify_images() {
    echo "🔍 验证镜像存在性..."
    
    local images=("node:22.16.0-alpine" "postgres:15-alpine" "redis:7-alpine" "nginx:alpine")
    local missing_images=()
    
    for image in "${images[@]}"; do
        if docker image inspect "$image" &> /dev/null; then
            echo -e "${GREEN}✅ $image${NC}"
        else
            echo -e "${RED}❌ $image${NC}"
            missing_images+=("$image")
        fi
    done
    
    if [ ${#missing_images[@]} -ne 0 ]; then
        echo -e "${RED}❌ 缺少以下镜像:${NC}"
        printf '%s\n' "${missing_images[@]}"
        echo "请先加载所有必要的镜像"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 所有必要镜像已就绪${NC}"
}

# 创建网络
create_network() {
    echo "🌐 创建Docker网络..."
    if ! docker network inspect story-app-network &> /dev/null; then
        docker network create story-app-network
        echo -e "${GREEN}✅ 网络创建完成${NC}"
    else
        echo -e "${GREEN}✅ 网络已存在${NC}"
    fi
}

# 停止现有容器
stop_containers() {
    echo "🛑 停止现有容器..."
    
    containers=("story-app-backend" "story-app-postgres" "story-app-redis" "story-app-nginx")
    
    for container in "${containers[@]}"; do
        # 检查容器是否存在（运行中或停止状态）
        if docker ps -a -q -f name=^${container}$ | grep -q .; then
            echo "  🔍 发现容器: $container"
            # 强制停止并移除
            docker stop $container 2>/dev/null || true
            docker rm -f $container 2>/dev/null || true
            echo "  ✅ 强制清理 $container"
        else
            echo "  ⚪ 容器不存在: $container"
        fi
    done
    
    # 额外清理：移除所有相关容器（防止名称变体）
    echo "🧹 清理所有相关容器..."
    docker ps -a --format "{{.Names}}" | grep "story-app" | xargs -r docker rm -f 2>/dev/null || true
    
    echo -e "${GREEN}✅ 容器清理完成${NC}"
}

# 创建数据卷
create_volumes() {
    echo "💾 创建数据卷..."
    
    docker volume create postgres_data &> /dev/null || true
    docker volume create redis_data &> /dev/null || true
    
    echo -e "${GREEN}✅ 数据卷创建完成${NC}"
}

# 准备应用代码（包含完整构建流程）
prepare_app() {
    echo "📦 准备应用代码..."
    
    # 确保必要的目录存在
    mkdir -p logs uploads
    
    # 检查必要文件
    if [ ! -f "package.json" ]; then
        echo -e "${RED}❌ package.json 不存在${NC}"
        exit 1
    fi
    
    if [ ! -d "src" ]; then
        echo -e "${RED}❌ src 目录不存在${NC}"
        exit 1
    fi
    
    # 总是重新构建（确保最新代码）
    echo "🧹 清理旧的构建..."
    rm -rf dist node_modules/.cache 2>/dev/null || true
    
    echo "📥 安装所有依赖（包含开发依赖）..."
    
    # 检查是否有 package-lock.json
    if [ -f "package-lock.json" ]; then
        echo "📋 使用 npm ci（有 package-lock.json）..."
        npm_command="npm ci --include=dev --verbose"
    else
        echo "📋 使用 npm install（无 package-lock.json）..."
        npm_command="npm install --verbose"
    fi
    
    docker run --rm \
        -v $(pwd):/workspace \
        -w /workspace \
        node:22.16.0-alpine \
        sh -c "
            echo '🔍 检查项目文件...'
            echo 'package.json:' && ls -la package.json
            echo 'package-lock.json:' && ls -la package-lock.json 2>/dev/null || echo 'package-lock.json 不存在'
            echo ''
            echo '📥 安装依赖...'
            $npm_command
            echo ''
            echo '🔍 验证安装结果...'
            ls -la node_modules/ | head -5
            echo '✅ 依赖安装完成'
        " || {
        echo -e "${RED}❌ 依赖安装失败${NC}"
        exit 1
    }
    
    echo "🏗️ 构建应用..."
    docker run --rm \
        -v $(pwd):/workspace \
        -w /workspace \
        node:22.16.0-alpine \
        sh -c "
            echo '🔍 检查可用脚本...'
            npm run --silent 2>/dev/null || npm run
            echo ''
            echo '🔍 验证 nest 命令...'
            which nest || echo 'nest 命令不在 PATH 中，检查 node_modules/.bin/nest'
            echo '🔍 检查 node_modules/.bin/nest...'
            ls -la node_modules/.bin/nest 2>/dev/null || echo 'nest 二进制文件不存在'
            echo '🔍 使用完整路径构建...'
            echo '🏗️ 开始构建...'
            ./node_modules/.bin/nest build
            echo ''
            echo '📋 验证构建结果...'
            ls -la dist/ 2>/dev/null || echo '❌ dist目录不存在'
        " || {
        echo -e "${RED}❌ 应用构建失败${NC}"
        echo "🔍 调试信息："
        echo "检查依赖是否正确安装..."
        docker run --rm -v $(pwd):/workspace -w /workspace node:22.16.0-alpine sh -c "ls -la node_modules/.bin/ | grep nest"
        exit 1
    }
    
    # 验证关键文件
    if [ ! -f "dist/main.js" ]; then
        echo -e "${RED}❌ 构建失败：dist/main.js 不存在${NC}"
        echo "📁 dist 目录内容："
        ls -la dist/ 2>/dev/null || echo "dist 目录不存在"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 应用构建成功！${NC}"
    echo "📏 主文件大小: $(du -h dist/main.js | cut -f1)"
    
    echo "🔍 验证环境配置..."
    echo "DB_HOST: $(grep DB_HOST .env.production | cut -d'=' -f2)"
    echo "DB_SSL: $(grep DB_SSL .env.production | cut -d'=' -f2)"
    
    echo "⚡ 优化生产环境依赖..."
    docker run --rm \
        -v $(pwd):/workspace \
        -w /workspace \
        node:22.16.0-alpine \
        sh -c "npm ci --only=production --silent"
    
    echo -e "${GREEN}✅ 应用代码准备完成${NC}"
}

# 启动PostgreSQL
start_postgres() {
    echo "🗄️ 启动PostgreSQL..."
    
    docker run -d \
        --name story-app-postgres \
        --network story-app-network \
        --restart always \
        -p 127.0.0.1:5432:5432 \
        -e POSTGRES_DB=$DB_NAME \
        -e POSTGRES_USER=$DB_USERNAME \
        -e POSTGRES_PASSWORD=$DB_PASSWORD \
        -v postgres_data:/var/lib/postgresql/data \
        -v $(pwd)/sql/init.sql:/docker-entrypoint-initdb.d/init.sql:ro \
        -v /etc/localtime:/etc/localtime:ro \
        postgres:15-alpine
    
    echo "⏳ 等待PostgreSQL启动..."
    sleep 10
    
    # 健康检查
    for i in {1..30}; do
        if docker exec story-app-postgres pg_isready -U $DB_USERNAME -d $DB_NAME &> /dev/null; then
            echo -e "${GREEN}✅ PostgreSQL 启动成功${NC}"
            break
        fi
        echo "  等待PostgreSQL... ($i/30)"
        sleep 2
    done
}

# 启动Redis
start_redis() {
    echo "🔄 启动Redis..."
    
    docker run -d \
        --name story-app-redis \
        --network story-app-network \
        --restart always \
        -p 127.0.0.1:6379:6379 \
        -e REDIS_PASSWORD=$REDIS_PASSWORD \
        -v redis_data:/data \
        -v /etc/localtime:/etc/localtime:ro \
        redis:7-alpine \
        redis-server --requirepass $REDIS_PASSWORD
    
    echo "⏳ 等待Redis启动..."
    sleep 5
    
    # 健康检查
    for i in {1..10}; do
        if docker exec story-app-redis redis-cli --no-auth-warning -a $REDIS_PASSWORD ping &> /dev/null; then
            echo -e "${GREEN}✅ Redis 启动成功${NC}"
            break
        fi
        echo "  等待Redis... ($i/10)"
        sleep 2
    done
}

# 启动应用（直接使用Node.js镜像）
start_app() {
    echo "🚀 启动应用服务..."
    
    # 创建启动脚本
    cat > start-app.sh << 'APP_EOF'
#!/bin/sh
set -e

echo "📂 切换到应用目录..."
cd /app

echo "🔍 检查应用文件..."
if [ ! -f "dist/main.js" ]; then
    echo "❌ 应用文件不存在，请检查构建过程"
    ls -la dist/ 2>/dev/null || echo "dist目录不存在"
    exit 1
fi

echo "🔍 检查环境变量..."
echo "NODE_ENV: $NODE_ENV"
echo "PORT: $PORT"
echo "DB_HOST: $DB_HOST"

echo "🚀 直接启动应用..."
exec node dist/main.js
APP_EOF

    chmod +x start-app.sh
    
    docker run -d \
        --name story-app-backend \
        --network story-app-network \
        --restart always \
        -p 3000:3000 \
        --env-file .env.production \
        -v $(pwd):/app \
        -v $(pwd)/start-app.sh:/start-app.sh \
        -v $(pwd)/logs:/app/logs \
        -v /etc/localtime:/etc/localtime:ro \
        -w /app \
        node:22.16.0-alpine \
        /start-app.sh
    
    echo "⏳ 等待应用启动..."
    sleep 15
    
    # 健康检查 - 更智能的检查策略
    echo "🏥 应用健康检查..."
    app_ready=false
    
        for i in {1..60}; do
        # 首先检查容器是否还在运行
        if ! docker ps -q -f name=story-app-backend | grep -q .; then
            echo "  ❌ 应用容器已停止运行"
            break
        fi
        
        # 检查应用是否真正监听端口
        if docker exec story-app-backend netstat -tlnp 2>/dev/null | grep -q ":3000"; then
            echo "  ✅ 端口3000已监听"
            
            # 检查应用日志确认启动成功
            if docker logs story-app-backend 2>/dev/null | grep -q "Nest application successfully started"; then
                echo -e "${GREEN}✅ 应用服务启动成功！${NC}"
                app_ready=true
                break
            else
                echo "  ⏳ 端口已监听，等待应用完全就绪... ($i/60)"
            fi
        else
            echo "  ⏳ 等待应用监听端口... ($i/60)"
        fi
        
        sleep 2
        
        # 显示日志帮助调试
        if [ $i -eq 15 ] || [ $i -eq 30 ]; then
            echo ""
            echo "📋 应用日志 (最后15行):"
            docker logs story-app-backend --tail 15
            echo ""
        fi
    done
    
    if [ "$app_ready" = false ]; then
        echo -e "${RED}❌ 应用启动超时，请检查日志${NC}"
        echo "📋 完整应用日志:"
        docker logs story-app-backend
        exit 1
    fi
}

# 启动Nginx
start_nginx() {
    echo "🌐 启动Nginx..."
    
    # 创建必要目录
    mkdir -p logs/nginx ssl
    
    # 验证nginx配置文件存在
    if [ ! -f "nginx/nginx.conf" ]; then
        echo -e "${RED}❌ nginx/nginx.conf 不存在${NC}"
        exit 1
    fi
    
    if [ ! -f "nginx/conf.d/default.conf" ]; then
        echo -e "${RED}❌ nginx/conf.d/default.conf 不存在${NC}"
        exit 1
    fi
    
    echo "✅ 使用项目中的nginx配置文件"
    echo "📍 主配置: nginx/nginx.conf"
    echo "📍 站点配置: nginx/conf.d/default.conf"
    
    docker run -d \
        --name story-app-nginx \
        --network story-app-network \
        --restart always \
        -p 80:80 \
        -p 443:443 \
        -v $(pwd)/nginx/nginx.conf:/etc/nginx/nginx.conf:ro \
        -v $(pwd)/nginx/conf.d:/etc/nginx/conf.d:ro \
        -v $(pwd)/ssl:/etc/nginx/ssl:ro \
        -v $(pwd)/logs/nginx:/var/log/nginx \
        -v /etc/localtime:/etc/localtime:ro \
        nginx:alpine \
        nginx -g "daemon off;"
    
    echo "⏳ 等待Nginx启动..."
    sleep 5
    
    echo -e "${GREEN}✅ Nginx 启动成功${NC}"
}

# 健康检查
health_check() {
    echo "🏥 执行健康检查..."
    
    echo "📊 容器状态："
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep story-app
    
    echo ""
    echo "🔍 服务连通性测试："
    
    # 测试应用服务 - 使用正确的健康检查路径
    if wget -q --spider --timeout=5 http://localhost:3000/api/v1/health 2>/dev/null; then
        echo -e "${GREEN}✅ 应用服务正常${NC}"
    else
        echo -e "${YELLOW}⚠️ 应用服务测试失败，查看日志${NC}"
        docker logs story-app-backend --tail 5
    fi
    
    # 测试Nginx代理
    if wget -q --spider --timeout=5 http://localhost/api/v1/health 2>/dev/null; then
        echo -e "${GREEN}✅ Nginx代理正常${NC}"
    else
        echo -e "${YELLOW}⚠️ Nginx代理测试失败${NC}"
    fi
}

# 显示部署信息
show_info() {
    echo ""
    echo "🎉 直接部署完成！"
    echo -e "${GREEN}================================${NC}"
    echo "🌐 访问地址: http://************"
    echo "🔍 健康检查: http://************/api/v1/health"
    echo "📊 应用端口: http://************:3000"
    echo ""
    echo "📝 容器管理命令:"
    echo "  查看状态: docker ps | grep story-app"
    echo "  查看日志: docker logs story-app-backend -f"
    echo "  重启应用: docker restart story-app-backend"
    echo ""
    echo "🛠️ 数据库连接:"
    echo "  主机: localhost:5432"
    echo "  数据库: $DB_NAME"
    echo "  用户: $DB_USERNAME"
    echo -e "${GREEN}================================${NC}"
}

# 主函数
main() {
    load_env
    verify_images
    create_network
    stop_containers
    create_volumes
    prepare_app
    start_postgres
    start_redis
    start_app
    start_nginx
    health_check
    show_info
}

# 使用说明
usage() {
    echo "直接部署脚本 - 不构建镜像，直接运行"
    echo ""
    echo "用法: $0"
    echo ""
    echo "特点:"
    echo "  - 不构建新镜像，避免网络请求"
    echo "  - 直接使用现有镜像运行应用"
    echo "  - 代码通过卷挂载"
    echo "  - 包含完整的健康检查"
}

# 参数处理
case "${1:-}" in
    --help)
        usage
        exit 0
        ;;
    "")
        main
        ;;
    *)
        echo "未知选项: $1"
        usage
        exit 1
        ;;
esac 