import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../tokens/ygs_colors.dart';
import '../tokens/ygs_typography.dart';
import '../tokens/ygs_spacing.dart';

/// YGS设计系统 - 按钮组件
/// 基于Figma设计规范v1.0.0
class YgsButton extends StatelessWidget {
  const YgsButton({
    required this.text,
    required this.onPressed,
    this.type = YgsButtonType.primary,
    this.size = YgsButtonSize.large,
    this.isLoading = false,
    this.isEnabled = true,
    this.width,
    this.height,
    this.icon,
    super.key,
  });

  final String text;
  final VoidCallback? onPressed;
  final YgsButtonType type;
  final YgsButtonSize size;
  final bool isLoading;
  final bool isEnabled;
  final double? width;
  final double? height;
  final Widget? icon;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width ?? _getButtonWidth(),
      height: height ?? _getButtonHeight(),
      decoration: BoxDecoration(
        color: _getBackgroundColor(),
        borderRadius: BorderRadius.circular(YgsSpacing.buttonBorderRadius),
        border: _getBorder(),
        boxShadow: _getShadow(),
      ),
      child: ElevatedButton(
        onPressed: isEnabled && !isLoading ? onPressed : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          foregroundColor: _getTextColor(),
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(YgsSpacing.buttonBorderRadius),
          ),
          elevation: 0,
          padding: EdgeInsets.zero,
        ),
        child: _buildButtonContent(),
      ),
    );
  }

  /// 构建按钮内容
  Widget _buildButtonContent() {
    if (isLoading) {
      return SizedBox(
        width: 24.w,
        height: 24.w,
        child: CircularProgressIndicator(
          color: _getTextColor(),
          strokeWidth: 2.5.w,
        ),
      );
    }

    if (icon != null) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          icon!,
          SizedBox(width: 8.w),
          Text(
            text,
            style: _getTextStyle(),
          ),
        ],
      );
    }

    return Text(
      text,
      style: _getTextStyle(),
    );
  }

  /// 获取按钮宽度
  double _getButtonWidth() {
    switch (size) {
      case YgsButtonSize.small:
        return 120.w;
      case YgsButtonSize.medium:
        return 200.w;
      case YgsButtonSize.large:
        return YgsSpacing.inputStandardWidth; // 600.w
    }
  }

  /// 获取按钮高度
  double _getButtonHeight() {
    switch (size) {
      case YgsButtonSize.small:
        return 40.h;
      case YgsButtonSize.medium:
        return 50.h;
      case YgsButtonSize.large:
        return YgsSpacing.buttonHeight; // 70.h
    }
  }

  /// 获取背景颜色
  Color _getBackgroundColor() {
    if (!isEnabled) {
      return YgsColors.textTertiary;
    }

    switch (type) {
      case YgsButtonType.primary:
        return YgsColors.brandPrimary;
      case YgsButtonType.secondary:
        return YgsColors.brandSecondary;
      case YgsButtonType.outline:
        return Colors.transparent;
      case YgsButtonType.text:
        return Colors.transparent;
    }
  }

  /// 获取文字颜色
  Color _getTextColor() {
    if (!isEnabled) {
      return YgsColors.backgroundCard;
    }

    switch (type) {
      case YgsButtonType.primary:
        return YgsColors.onBrandPrimary;
      case YgsButtonType.secondary:
        return YgsColors.onBrandPrimary;
      case YgsButtonType.outline:
        return YgsColors.brandPrimary;
      case YgsButtonType.text:
        return YgsColors.brandPrimary;
    }
  }

  /// 获取文字样式
  TextStyle _getTextStyle() {
    TextStyle baseStyle;
    
    switch (size) {
      case YgsButtonSize.small:
        baseStyle = YgsTypography.labelMedium;
        break;
      case YgsButtonSize.medium:
        baseStyle = YgsTypography.labelLarge;
        break;
      case YgsButtonSize.large:
        baseStyle = YgsTypography.button;
        break;
    }

    return baseStyle.copyWith(color: _getTextColor());
  }

  /// 获取边框
  Border? _getBorder() {
    if (type == YgsButtonType.outline) {
      return Border.all(
        color: isEnabled ? YgsColors.brandPrimary : YgsColors.textTertiary,
        width: 2.w,
      );
    }
    return null;
  }

  /// 获取阴影
  List<BoxShadow>? _getShadow() {
    if (type == YgsButtonType.primary || type == YgsButtonType.secondary) {
      return YgsColors.buttonShadow;
    }
    return null;
  }
}

/// 按钮类型枚举
enum YgsButtonType {
  /// 主要按钮 - 红色背景
  primary,
  
  /// 次要按钮 - 蓝色背景
  secondary,
  
  /// 轮廓按钮 - 透明背景，红色边框
  outline,
  
  /// 文字按钮 - 透明背景，无边框
  text,
}

/// 按钮尺寸枚举
enum YgsButtonSize {
  /// 小按钮 - 120x40
  small,
  
  /// 中按钮 - 200x50
  medium,
  
  /// 大按钮 - 600x70
  large,
}