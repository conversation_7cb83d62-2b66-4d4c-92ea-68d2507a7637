import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/repositories/auth_repository.dart';
import '../../domain/usecases/login_with_phone.dart';
import '../../domain/usecases/send_sms_code.dart';
import 'auth_state.dart';

/// 认证状态管理器
class AuthNotifier extends StateNotifier<AuthState> {
  final AuthRepository authRepository;
  final LoginWithPhone loginWithPhone;
  final SendSmsCode sendSmsCode;

  AuthNotifier({
    required this.authRepository,
    required this.loginWithPhone,
    required this.sendSmsCode,
  }) : super(const AuthState.initial()) {
    // 初始化时检查登录状态
    checkAuthStatus();
  }

  /// 检查认证状态
  Future<void> checkAuthStatus() async {
    final result = await authRepository.isLoggedIn();
    
    await result.fold(
      (failure) async {
        state = const AuthState.unauthenticated();
      },
      (isLoggedIn) async {
        if (isLoggedIn) {
          // 获取当前用户信息
          final userResult = await authRepository.getCurrentUser();
          userResult.fold(
            (failure) => state = const AuthState.unauthenticated(),
            (user) {
              if (user != null) {
                state = AuthState.authenticated(user);
              } else {
                state = const AuthState.unauthenticated();
              }
            },
          );
        } else {
          state = const AuthState.unauthenticated();
        }
      },
    );
  }

  /// 发送短信验证码
  Future<void> sendSms(String phone) async {
    state = const AuthState.loading();
    
    final result = await sendSmsCode.call(phone);
    
    result.fold(
      (failure) => state = AuthState.error(failure.message),
      (_) => state = const AuthState.unauthenticated(),
    );
  }

  /// 手机号登录
  Future<void> loginPhone({
    required String phone,
    required String code,
  }) async {
    state = const AuthState.loading();
    
    final result = await loginWithPhone(
      phone: phone,
      code: code,
    );
    
    result.fold(
      (failure) => state = AuthState.error(failure.message),
      (data) {
        final (user, _) = data;
        state = AuthState.authenticated(user);
      },
    );
  }

  /// 邮箱登录
  Future<void> loginEmail({
    required String email,
    required String password,
  }) async {
    state = const AuthState.loading();
    
    final result = await authRepository.loginWithEmail(
      email: email,
      password: password,
    );
    
    result.fold(
      (failure) => state = AuthState.error(failure.message),
      (data) async {
        final (user, token) = data;
        // 保存认证信息
        await authRepository.saveAuthData(user: user, token: token);
        state = AuthState.authenticated(user);
      },
    );
  }

  /// 退出登录
  Future<void> logout() async {
    state = const AuthState.loading();
    
    final result = await authRepository.logout();
    
    result.fold(
      (failure) {
        // 即使退出失败也清除本地状态
        state = const AuthState.unauthenticated();
      },
      (_) => state = const AuthState.unauthenticated(),
    );
  }

  /// 更新用户信息
  Future<void> updateUserInfo({
    String? nickname,
    String? bio,
    String? avatar,
  }) async {
    final currentUser = state.user;
    if (currentUser == null) return;
    
    state = const AuthState.loading();
    
    final result = await authRepository.updateUserInfo(
      nickname: nickname,
      bio: bio,
      avatar: avatar,
    );
    
    result.fold(
      (failure) {
        state = AuthState.authenticated(currentUser);
        // 可以通过其他方式通知错误
      },
      (user) => state = AuthState.authenticated(user),
    );
  }

  /// 修改密码
  Future<bool> changePassword({
    required String oldPassword,
    required String newPassword,
  }) async {
    final result = await authRepository.changePassword(
      oldPassword: oldPassword,
      newPassword: newPassword,
    );
    
    return result.fold(
      (failure) => false,
      (_) => true,
    );
  }

  /// 绑定手机号
  Future<bool> bindPhone({
    required String phone,
    required String code,
  }) async {
    final currentUser = state.user;
    if (currentUser == null) return false;
    
    final result = await authRepository.bindPhone(
      phone: phone,
      code: code,
    );
    
    return result.fold(
      (failure) => false,
      (user) {
        state = AuthState.authenticated(user);
        return true;
      },
    );
  }

  /// 绑定邮箱
  Future<bool> bindEmail({
    required String email,
    required String password,
  }) async {
    final currentUser = state.user;
    if (currentUser == null) return false;
    
    final result = await authRepository.bindEmail(
      email: email,
      password: password,
    );
    
    return result.fold(
      (failure) => false,
      (user) {
        state = AuthState.authenticated(user);
        return true;
      },
    );
  }

  /// 刷新用户信息
  Future<void> refreshUser() async {
    final currentUser = state.user;
    if (currentUser == null) return;
    
    final result = await authRepository.getCurrentUser();
    
    result.fold(
      (failure) {
        // 忽略错误，保持当前状态
      },
      (user) {
        if (user != null) {
          state = AuthState.authenticated(user);
        }
      },
    );
  }
}