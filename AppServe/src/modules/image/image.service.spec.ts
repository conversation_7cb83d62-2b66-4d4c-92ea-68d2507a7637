import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { ConfigService } from "@nestjs/config";
import { NotFoundException } from "@nestjs/common";
import type { ImageAccessRequest } from "./image.service";
import { ImageService, ImageType } from "./image.service";
import { EnhancedRedisService } from "../../common/services/enhanced-redis.service";

describe("ImageService", () => {
  let service: ImageService;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let mockClient: any;

  const mockConfigService = {
    get: jest.fn((key: string) => {
      const config: Record<string, string> = {
        OSS_REGION: "oss-cn-hangzhou",
        OSS_ACCESS_KEY_ID: "test-access-key",
        OSS_ACCESS_KEY_SECRET: "test-secret-key",
        OSS_BUCKET: "test-bucket",
        CDN_DOMAIN: "https://test-cdn.com",
      };
      return config[key];
    }),
  };

  const mockRedisService = {
    setCache: jest.fn(),
    getCache: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ImageService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: EnhancedRedisService,
          useValue: mockRedisService,
        },
      ],
    }).compile();

    service = module.get<ImageService>(ImageService);

    // 手动替换服务内部的OSS客户端为完整Mock实例
    const fullMockClient = {
      head: jest.fn(),
      get: jest.fn(),
      put: jest.fn(),
      signatureUrl: jest.fn(),
      delete: jest.fn(),
    };

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (service as any).client = fullMockClient;
    mockClient = fullMockClient;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("constructor", () => {
    it("should be defined", () => {
      expect(service).toBeDefined();
      expect(mockClient).toBeDefined();

      // 调试输出
      console.log("🔍 mockClient类型:", typeof mockClient);
      console.log("🔍 mockClient属性:", Object.keys(mockClient));
      console.log("🔍 mockClient.head类型:", typeof mockClient.head);
      console.log("🔍 mockClient.get类型:", typeof mockClient.get);
      console.log(
        "🔍 mockClient.signatureUrl类型:",
        typeof mockClient.signatureUrl,
      );
    });

    it("should initialize OSS client with correct config", () => {
      expect(service).toBeDefined();
      // OSS客户端应该通过Mock正确初始化
      expect(mockClient).toBeDefined();
    });
  });

  describe("checkImageAccess", () => {
    const mockRequest: ImageAccessRequest = {
      imagePath: "avatars/user-123.jpg",
      imageType: ImageType.AVATAR,
      userId: "user-123",
    };

    it("should return true for avatar image access", async () => {
      // Mock head方法成功返回
      mockClient.head.mockResolvedValue({
        name: "test-image.jpg",
        size: 1024,
        lastModified: new Date(),
      });

      const result = await service.checkImageAccess(mockRequest);
      expect(result).toBe(true);
      expect(mockClient.head).toHaveBeenCalledWith("avatars/user-123.jpg");
    });

    it("should throw NotFoundException when image does not exist", async () => {
      const notFoundError = new Error("NoSuchKey");
      (notFoundError as Error & { code: string }).code = "NoSuchKey";
      mockClient.head.mockRejectedValue(notFoundError);

      await expect(service.checkImageAccess(mockRequest)).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe("generateTemporaryUrl", () => {
    it("should generate temporary URL successfully", async () => {
      const expectedUrl = "https://mock-signature-url.com/test-image.jpg";
      mockClient.signatureUrl.mockReturnValue(expectedUrl);

      const result = await service.generateTemporaryUrl("test-image.jpg");
      expect(result).toBe(expectedUrl);
      expect(mockClient.signatureUrl).toHaveBeenCalledWith("test-image.jpg", {
        expires: 3600,
        method: "GET",
      });
    });
  });

  describe("getImageStream", () => {
    it("should return image stream successfully", async () => {
      const mockContent = Buffer.from("mock-image-content");
      mockClient.get.mockResolvedValue({ content: mockContent });

      const result = await service.getImageStream("test-image.jpg");
      expect(result).toEqual(mockContent);
      expect(mockClient.get).toHaveBeenCalledWith("test-image.jpg");
    });

    it("should throw NotFoundException when image does not exist", async () => {
      const notFoundError = new Error("NoSuchKey");
      (notFoundError as Error & { code: string }).code = "NoSuchKey";
      mockClient.get.mockRejectedValue(notFoundError);

      await expect(service.getImageStream("test-image.jpg")).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe("parseImagePath", () => {
    it("should parse avatar image path", () => {
      const result = service.parseImagePath("avatars/user-123.jpg");
      expect(result).toEqual({
        type: ImageType.AVATAR,
      });
    });

    it("should parse story cover image path", () => {
      const result = service.parseImagePath(
        "ygs-app/stories/story-456/cover.jpg",
      );
      expect(result).toEqual({
        type: ImageType.STORY_COVER,
        resourceId: "story-456",
      });
    });
  });

  describe("generateSignedUrl", () => {
    it("should generate signed URL with default parameters", async () => {
      const expectedUrl = "https://mock-signature-url.com/test-image.jpg";
      mockClient.signatureUrl.mockReturnValue(expectedUrl);

      const result = await service.generateSignedUrl("test-image.jpg");
      expect(result).toBe(expectedUrl);
    });

    it("should throw NotFoundException when URL generation fails", async () => {
      mockClient.signatureUrl.mockImplementation(() => {
        throw new Error("URL generation failed");
      });

      await expect(service.generateSignedUrl("test-image.jpg")).rejects.toThrow(
        NotFoundException,
      );
    });
  });
});
