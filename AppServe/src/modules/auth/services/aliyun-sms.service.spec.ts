import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { ConfigService } from "@nestjs/config";
import { BadRequestException } from "@nestjs/common";
import { AliyunSmsService } from "./aliyun-sms.service";

// 企业级阿里云SDK Mock配置 - 作用域提升
const mockSmsClient = {
  sendSmsWithOptions: jest.fn(),
};

// Mock 阿里云 SDK - 企业级类型安全方式（借鉴lighting.service成功经验）
jest.mock("@alicloud/dysmsapi20170525", () => {
  // 在Mock函数内部创建自己的mockSmsClient以解决作用域问题
  const internalMockSmsClient = {
    sendSmsWithOptions: jest.fn(),
  };

  // 确保每次实例化都返回同一个mockSmsClient实例
  const MockDysmsapi = jest.fn().mockImplementation(() => {
    console.log("🔧 MockDysmsapi构造函数被调用，返回internalMockSmsClient");
    return internalMockSmsClient;
  });

  // Mock SendSmsRequest类
  const MockSendSmsRequest = jest
    .fn()
    .mockImplementation((params: unknown) => params);

  // 正确的default导出和命名导出
  const mockModule = {
    __esModule: true,
    default: MockDysmsapi,
    SendSmsRequest: MockSendSmsRequest,
    // 导出内部mock客户端供测试使用
    __mockSmsClient: internalMockSmsClient,
  };

  // 为了支持命名空间导入 (* as $Dysmsapi20170525)，我们返回的对象本身就包含所有需要的属性
  return mockModule;
});

jest.mock("@alicloud/openapi-client", () => ({
  Config: jest.fn().mockImplementation((params: unknown) => params),
}));

jest.mock("@alicloud/tea-util", () => ({
  RuntimeOptions: jest.fn().mockImplementation((params: unknown) => params),
}));

describe("AliyunSmsService", () => {
  let service: AliyunSmsService;
  let internalMockSmsClient: { sendSmsWithOptions: jest.Mock };

  // Mock 数据
  const mockPhone = "13800138000";
  const mockVipPhone = "16675158665";
  const mockCode = "1234";

  beforeEach(async () => {
    // 重置所有Mock
    jest.clearAllMocks();

    // 获取内部Mock客户端
    const mockModule = jest.requireMock("@alicloud/dysmsapi20170525");
    internalMockSmsClient = mockModule.__mockSmsClient;

    // 重置mockSmsClient方法
    mockSmsClient.sendSmsWithOptions.mockReset();
    if (internalMockSmsClient) {
      internalMockSmsClient.sendSmsWithOptions.mockReset();
    }

    // 创建配置服务mock
    const mockConfigService = {
      get: jest.fn((key: string, defaultValue?: unknown) => {
        const config: { [key: string]: unknown } = {
          NODE_ENV: "test",
          SMS_MODE: "auto",
          SMS_ACCESS_KEY_ID: "test-access-key-id",
          SMS_ACCESS_KEY_SECRET: "test-access-key-secret",
          SMS_SIGN_NAME: "有故事APP",
          SMS_TEMPLATE_CODE: "SMS_123456",
          SMS_ENDPOINT: "dysmsapi.aliyuncs.com",
        };
        return config[key] !== undefined ? config[key] : defaultValue;
      }),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AliyunSmsService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<AliyunSmsService>(AliyunSmsService);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  describe("sendVerificationCode", () => {
    beforeEach(() => {
      // 手动替换服务内部的阿里云SMS客户端 - 解决Mock配置问题
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (service as any).client = mockSmsClient;
    });

    it("should send real SMS for VIP phone in test environment", async () => {
      // 设置成功的阿里云API响应
      mockSmsClient.sendSmsWithOptions.mockResolvedValue({
        body: {
          code: "OK",
          message: "OK",
          bizId: "test-biz-id",
          requestId: "test-request-id",
        },
      });

      const result = await service.sendVerificationCode(mockVipPhone, mockCode);

      expect(result).toEqual({
        success: true,
        message: "短信发送成功",
        bizId: "test-biz-id",
        requestId: "test-request-id",
      });
      expect(mockSmsClient.sendSmsWithOptions).toHaveBeenCalled();
    });

    it("should send mock SMS for non-VIP phone in test environment", async () => {
      const result = await service.sendVerificationCode(mockPhone, mockCode);

      expect(result).toEqual({
        success: true,
        message: "短信发送成功",
        code: mockCode,
        bizId: expect.stringMatching(/^mock_\d+_/),
      });
      expect(mockSmsClient.sendSmsWithOptions).not.toHaveBeenCalled();
    });

    it("should handle SMS API failure", async () => {
      // 设置API失败响应
      mockSmsClient.sendSmsWithOptions.mockResolvedValue({
        body: {
          code: "isv.BUSINESS_LIMIT_CONTROL",
          message: "业务限流",
          requestId: "test-request-id",
        },
      });

      const result = await service.sendVerificationCode(mockVipPhone, mockCode);

      expect(result).toEqual({
        success: false,
        message: "业务限流",
        requestId: "test-request-id",
      });
    });

    it("should handle SMS API error", async () => {
      // 设置API错误
      mockSmsClient.sendSmsWithOptions.mockRejectedValue(
        new Error("Network error"),
      );

      await expect(
        service.sendVerificationCode(mockVipPhone, mockCode),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe("VIP phone management", () => {
    it("should add VIP test phone", () => {
      const testPhone = "***********";
      service.addVipTestPhone(testPhone);

      const vipPhones = service.getVipTestPhones();
      expect(vipPhones).toContain(testPhone);
    });

    it("should remove VIP test phone", () => {
      const testPhone = "***********";
      service.addVipTestPhone(testPhone);
      service.removeVipTestPhone(testPhone);

      const vipPhones = service.getVipTestPhones();
      expect(vipPhones).not.toContain(testPhone);
    });

    it("should get VIP test phones", () => {
      const vipPhones = service.getVipTestPhones();
      expect(Array.isArray(vipPhones)).toBe(true);
      expect(vipPhones).toContain("16675158665"); // 默认VIP号码
    });
  });

  describe("getCurrentSmsMode", () => {
    it("should return current SMS mode configuration", () => {
      const modeInfo = service.getCurrentSmsMode();
      expect(modeInfo).toBeDefined();
      expect(modeInfo).toEqual({
        mode: "auto",
        environment: "test",
        vipPhones: expect.arrayContaining(["16675158665"]),
        clientStatus: "ready",
      });
    });
  });

  describe("testConnection", () => {
    it("should return success when client is configured", async () => {
      const result = await service.testConnection();
      expect(result).toEqual({
        success: true,
        message: "阿里云短信服务配置正常",
        config: {
          hasAccessKeyId: true,
          hasAccessKeySecret: true,
          signName: "有故事APP",
          templateCode: "SMS_123456",
          endpoint: "dysmsapi.aliyuncs.com",
        },
      });
    });
  });
});
