# YGS Flutter 架构设计文档 v2.0

> **文档版本**: v2.0.0  
> **Flutter版本**: 3.32.0  
> **创建时间**: 2025-07-23  
> **更新时间**: 2025-07-29  
> **架构模式**: Clean Architecture + Riverpod  
> **设计原则**: SOLID原则、DRY原则、企业级标准

---

## 📋 架构设计概述

本文档详细定义了YGS Flutter应用的技术架构设计，采用Clean Architecture分层架构和Riverpod状态管理，实现了企业级的移动应用架构。

## 🏗️ 整体架构设计

### Clean Architecture 分层模型

```
┌─────────────────────────────────────────────────────────────┐
│                   Presentation Layer                         │
│              (Pages, Widgets & Providers)                    │
├─────────────────────────────────────────────────────────────┤
│                     Domain Layer                             │
│           (Repositories, Entities & UseCases)                │
├─────────────────────────────────────────────────────────────┤
│                      Data Layer                              │
│      (API Services, Models & Local DataSources)             │
├─────────────────────────────────────────────────────────────┤
│                      Core Layer                              │
│          (Network, Utils, Constants & Widgets)               │
└─────────────────────────────────────────────────────────────┘
```

### 数据流架构图

```
┌─────────┐     ┌──────────────┐     ┌─────────────┐
│   UI    │────▶│   Provider   │────▶│ Repository  │
│         │◀────│  (Notifier)  │◀────│             │
└─────────┘     └──────────────┘     └─────────────┘
                                             │
                                             ▼
┌─────────┐     ┌──────────────┐     ┌─────────────┐
│  State  │◀────│ API Service  │◀────│ Dio Client  │
│ (Model) │     │              │     │             │
└─────────┘     └──────────────┘     └─────────────┘
```

## 📁 项目目录结构

```
lib/
├── app/                        # 应用配置层
│   ├── app.dart               # 应用主入口
│   ├── config/                # 配置文件
│   │   ├── env_config.dart    # 环境配置
│   │   └── api_config.dart    # API配置
│   ├── di/                    # 依赖注入
│   │   └── injection.dart     # 注入配置
│   └── routes/                # 路由配置
│       ├── app_router.dart    # 路由定义
│       └── route_guards.dart  # 路由守卫
│
├── core/                      # 核心基础设施
│   ├── constants/             # 常量定义
│   │   ├── api_constants.dart
│   │   ├── app_constants.dart
│   │   └── storage_keys.dart
│   ├── errors/                # 错误处理
│   │   ├── exceptions.dart
│   │   ├── failures.dart
│   │   └── failures.freezed.dart
│   ├── network/               # 网络基础
│   │   ├── dio_client.dart
│   │   ├── base_api_service.dart
│   │   ├── api_result.dart
│   │   ├── api_result.freezed.dart
│   │   └── interceptors/
│   │       ├── auth_interceptor.dart
│   │       └── error_interceptor.dart
│   ├── themes/                # 主题系统
│   │   ├── app_theme.dart
│   │   ├── app_colors.dart
│   │   └── app_typography.dart
│   ├── utils/                 # 工具类
│   │   ├── validators.dart
│   │   ├── logger.dart
│   │   └── app_logger.dart
│   └── widgets/               # 通用组件
│       ├── empty_state.dart
│       ├── error_state.dart
│       └── loading_overlay.dart
│
├── features/                  # 功能模块（按领域划分）
│   ├── auth/                  # 认证模块
│   │   ├── data/
│   │   │   ├── datasources/
│   │   │   │   ├── auth_api_service.dart
│   │   │   │   └── auth_local_datasource.dart
│   │   │   ├── models/
│   │   │   │   ├── auth_request_models.dart
│   │   │   │   └── auth_response_models.dart
│   │   │   └── repositories/
│   │   │       └── auth_repository_impl.dart
│   │   ├── domain/
│   │   │   └── repositories/
│   │   │       └── auth_repository.dart
│   │   └── presentation/
│   │       ├── pages/
│   │       │   └── login_page.dart
│   │       ├── widgets/
│   │       │   ├── phone_login_form.dart
│   │       │   ├── email_login_form.dart
│   │       │   └── countdown_button.dart
│   │       └── providers/
│   │           ├── auth_provider.dart
│   │           ├── auth_state.dart
│   │           └── auth_notifier.dart
│   │
│   ├── stories/               # 故事模块
│   │   ├── data/
│   │   │   ├── datasources/
│   │   │   │   └── story_api_service.dart
│   │   │   └── models/
│   │   │       ├── story_models.dart
│   │   │       └── content_validation_model.dart
│   │   ├── domain/
│   │   └── presentation/
│   │
│   ├── characters/            # 人物模块
│   │   ├── data/
│   │   │   └── models/
│   │   │       └── character_models.dart
│   │   ├── domain/
│   │   └── presentation/
│   │
│   ├── lighting/              # 点亮系统
│   │   ├── data/
│   │   │   └── models/
│   │   │       └── lighting_models.dart
│   │   ├── domain/
│   │   └── presentation/
│   │
│   └── home/                  # 主页模块
│       └── presentation/
│           └── pages/
│               └── home_page.dart
│
└── main.dart                  # 应用入口
```

## 🔄 状态管理架构

### Riverpod 2.5 架构设计

#### Provider层次结构

```dart
// 1. 基础Provider - 提供单一数据源
final sharedPreferencesProvider = Provider<SharedPreferences>((ref) {
  return getIt<SharedPreferences>();
});

// 2. Service Provider - API服务
final authApiServiceProvider = Provider<AuthApiService>((ref) {
  return AuthApiService(dio: ref.watch(dioProvider));
});

// 3. Repository Provider - 业务逻辑
final authRepositoryProvider = Provider<AuthRepository>((ref) {
  return AuthRepositoryImpl(
    apiService: ref.watch(authApiServiceProvider),
    localDataSource: ref.watch(authLocalDataSourceProvider),
  );
});

// 4. StateNotifierProvider - 复杂状态管理
final authStateProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  return AuthNotifier(
    repository: ref.watch(authRepositoryProvider),
    logger: Logger.instance,
  );
});
```

#### 状态管理最佳实践

```dart
// 状态类定义（使用Freezed）
@freezed
class AuthState with _$AuthState {
  const factory AuthState.initial() = _Initial;
  const factory AuthState.checking() = _Checking;
  const factory AuthState.loading() = _Loading;
  const factory AuthState.authenticated(User user) = _Authenticated;
  const factory AuthState.unauthenticated() = _Unauthenticated;
  const factory AuthState.error(String message, [String? code]) = _Error;
}

// StateNotifier实现
class AuthNotifier extends StateNotifier<AuthState> {
  final AuthRepository _repository;
  final Logger _logger;

  AuthNotifier({
    required AuthRepository repository,
    required Logger logger,
  }) : _repository = repository,
       _logger = logger,
       super(const AuthState.initial()) {
    checkAuthStatus();
  }
  
  Future<void> phoneLogin({
    required String phone,
    required String code,
  }) async {
    state = const AuthState.loading();
    
    final result = await _repository.phoneLogin(
      phone: phone,
      code: code,
    );
    
    result.fold(
      (failure) {
        _logger.e('手机号登录失败', failure);
        state = AuthState.error(failure.message, failure.code);
      },
      (authResponse) {
        _logger.i('手机号登录成功: ${authResponse.user.username}');
        state = AuthState.authenticated(authResponse.user);
      },
    );
  }
}
```

## 🗄️ 数据层架构

### Repository模式实现

```dart
// Repository接口定义（Domain层）
abstract class AuthRepository {
  Future<Either<Failure, void>> sendSmsCode(String phone);
  Future<Either<Failure, AuthResponse>> phoneLogin({
    required String phone,
    required String code,
  });
  Future<Either<Failure, User>> getUserProfile();
  Future<Either<Failure, void>> logout();
}

// Repository实现（Data层）
class AuthRepositoryImpl implements AuthRepository {
  final AuthApiService apiService;
  final AuthLocalDataSource localDataSource;
  final Logger _logger = Logger.instance;

  AuthRepositoryImpl({
    required this.apiService,
    required this.localDataSource,
  });

  @override
  Future<Either<Failure, AuthResponse>> phoneLogin({
    required String phone,
    required String code,
  }) async {
    try {
      final result = await apiService.phoneLogin(PhoneLoginRequest(
        phone: phone,
        code: code,
      ));
      
      if (result.isSuccess && result.data != null) {
        // 保存认证信息
        await localDataSource.saveAuthResponse(result.data!);
        return Right(result.data!);
      } else {
        return Left(ServerFailure(result.message));
      }
    } on AppException catch (e) {
      _logger.e('手机号登录失败', e);
      return Left(_handleException(e));
    } catch (e) {
      _logger.e('手机号登录异常', e);
      return Left(UnknownFailure(e.toString()));
    }
  }
}
```

### API Service设计

```dart
// 基础API服务类
abstract class BaseApiService {
  late final Dio _dio;
  late final Logger _logger;

  BaseApiService({Dio? dio}) {
    _dio = dio ?? DioClient.instance;
    _logger = Logger.instance;
  }

  // GET请求
  Future<ApiResult<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? parser,
  }) async {
    try {
      final response = await _dio.get(
        path,
        queryParameters: queryParameters,
      );
      return _handleResponse<T>(response, parser);
    } on DioException catch (e) {
      throw DioClient.handleDioError(e);
    }
  }
}

// 具体API服务实现
class AuthApiService extends BaseApiService {
  AuthApiService({super.dio});

  Future<ApiResult<AuthResponse>> phoneLogin(PhoneLoginRequest request) async {
    return post<AuthResponse>(
      '/auth/login/phone',
      data: request.toJson(),
      parser: (data) => AuthResponse.fromJson(data),
    );
  }
}
```

## 🔐 安全架构设计

### Token管理机制

```dart
// 认证拦截器
class AuthInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    final token = await ref.read(authLocalDataSourceProvider).getAccessToken();
    if (token != null) {
      options.headers['Authorization'] = 'Bearer $token';
    }
    handler.next(options);
  }
  
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    if (err.response?.statusCode == 401) {
      // Token过期，尝试刷新
      final refreshed = await ref.read(authStateProvider.notifier).refreshToken();
      if (refreshed) {
        // 重试原请求
        final clonedRequest = await _dio.request(
          err.requestOptions.path,
          options: err.requestOptions,
        );
        return handler.resolve(clonedRequest);
      }
    }
    handler.next(err);
  }
}
```

### 本地数据安全存储

```dart
class AuthLocalDataSourceImpl implements AuthLocalDataSource {
  final FlutterSecureStorage secureStorage;
  final SharedPreferences sharedPreferences;

  @override
  Future<void> saveTokens(String accessToken, String refreshToken) async {
    // 敏感数据使用安全存储
    await secureStorage.write(
      key: StorageKeys.accessToken,
      value: accessToken,
    );
    await secureStorage.write(
      key: StorageKeys.refreshToken,
      value: refreshToken,
    );
  }

  @override
  Future<void> saveUser(User user) async {
    // 用户基本信息加密存储
    await secureStorage.write(
      key: StorageKeys.userId,
      value: user.id,
    );
    
    // 详细信息使用普通存储
    final userJson = json.encode(user.toJson());
    await sharedPreferences.setString(
      '${StorageKeys.userCachePrefix}${user.id}',
      userJson,
    );
  }
}
```

## 🎨 UI组件架构

### 统一错误处理

```dart
// 统一的错误状态处理
ref.listen(authStateProvider, (previous, next) {
  next.whenOrNull(
    error: (message, code) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
        ),
      );
    },
  );
});
```

### 表单验证规范

```dart
class Validators {
  static final RegExp _phoneRegExp = RegExp(r'^1[3-9]\d{9}$');
  
  static String? validatePhone(String? value) {
    if (value == null || value.isEmpty) {
      return '请输入手机号';
    }
    if (!_phoneRegExp.hasMatch(value)) {
      return '请输入正确的手机号';
    }
    return null;
  }
}
```

## 🚀 性能优化架构

### 状态缓存策略

```dart
// 用户信息缓存
Future<Either<Failure, User>> getUserProfile() async {
  try {
    // 先尝试从本地获取
    final cachedUser = await localDataSource.getUser();
    if (cachedUser != null) {
      // 异步更新用户信息
      _updateUserProfile();
      return Right(cachedUser);
    }

    // 本地没有，从服务器获取
    final result = await apiService.getUserProfile();
    if (result.isSuccess && result.data != null) {
      await localDataSource.saveUser(result.data!);
      return Right(result.data!);
    }
  } catch (e) {
    return Left(UnknownFailure(e.toString()));
  }
}
```

## 📊 错误处理架构

### 统一异常处理

```dart
// 异常类型定义
abstract class AppException implements Exception {
  final String message;
  final String? code;
  final dynamic data;
  
  const AppException({
    required this.message,
    this.code,
    this.data,
  });
}

class NetworkException extends AppException {
  const NetworkException({required super.message, super.code, super.data});
}

class ServerException extends AppException {
  final int? statusCode;
  const ServerException({
    required super.message,
    this.statusCode,
    super.code,
    super.data,
  });
}

// Failure类型定义（使用Freezed）
@freezed
class Failure with _$Failure {
  const factory Failure.network({
    required String message,
    String? code,
    dynamic data,
  }) = NetworkFailure;
  
  const factory Failure.server({
    required String message,
    String? code,
    int? statusCode,
    dynamic data,
  }) = ServerFailure;
  
  const factory Failure.auth({
    required String message,
    String? code,
    dynamic data,
  }) = AuthFailure;
}
```

## 📈 实现状态

### ✅ 已实现模块

1. **核心基础设施**
   - 网络层：Dio客户端、BaseApiService、统一响应处理
   - 错误处理：异常类型定义、Failure封装
   - 主题系统：颜色、字体、样式定义
   - 工具类：验证器、日志系统

2. **认证模块**
   - 数据模型：User、AuthResponse、DisplaySettings
   - API服务：手机号/邮箱登录、Token管理
   - 本地存储：SecureStorage、SharedPreferences
   - 状态管理：AuthNotifier、AuthState

3. **故事模块**
   - 数据模型：Story、StoryStats、PermissionLevel
   - API服务：CRUD操作、分页查询
   - 内容验证：ContentValidation

4. **人物模块**
   - 数据模型：Character、CharacterStats
   - 创建者信息、点亮信息

5. **点亮系统**
   - 数据模型：LightRequest、LightingStatus
   - 申请流程、状态管理

### 🚧 待实现模块

1. **社交互动**
   - 评论系统
   - 点赞、收藏
   - 关注、粉丝

2. **UI组件库**
   - 业务组件
   - 动画效果
   - 响应式布局

3. **性能优化**
   - 图片缓存
   - 列表优化
   - 内存管理

---

**架构设计**: YGS技术架构组  
**最后更新**: 2025-07-29