import {
  IsString,
  <PERSON><PERSON><PERSON>,
  IsOptional,
  IsBoolean,
  IsUUID,
} from "class-validator";
import { NotificationType } from "../entities/notification.entity";

/**
 * 创建通知DTO
 */
export class CreateNotificationDto {
  @IsUUID()
  userId: string;

  @IsEnum(NotificationType)
  type: NotificationType;

  @IsString()
  title: string;

  @IsString()
  @IsOptional()
  content?: string;

  @IsString()
  @IsOptional()
  relatedId?: string;

  @IsString()
  @IsOptional()
  actionUrl?: string;
}

/**
 * 更新通知DTO
 */
export class UpdateNotificationDto {
  @IsString()
  @IsOptional()
  title?: string;

  @IsString()
  @IsOptional()
  content?: string;

  @IsString()
  @IsOptional()
  actionUrl?: string;

  @IsBoolean()
  @IsOptional()
  isRead?: boolean;
}

/**
 * 通知查询DTO
 */
export class NotificationQueryDto {
  @IsString()
  @IsOptional()
  page?: string;

  @IsString()
  @IsOptional()
  limit?: string;

  @IsString()
  @IsOptional()
  unreadOnly?: string;

  @IsEnum(NotificationType)
  @IsOptional()
  type?: NotificationType;
}

/**
 * 批量操作DTO
 */
export class BatchNotificationDto {
  @IsString({ each: true })
  notificationIds: string[];
}
