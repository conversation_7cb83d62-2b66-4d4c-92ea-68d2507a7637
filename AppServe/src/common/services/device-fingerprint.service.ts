import { Injectable, Logger } from "@nestjs/common";
import { EnhancedRedisService } from "./enhanced-redis.service";
import type { Request } from "express";
import * as crypto from "crypto";
import type Redis from "ioredis";

export interface DeviceInfo {
  userAgent: string;
  platform?: string;
  ip: string;
  acceptLanguage?: string;
  acceptEncoding?: string;
  fingerprint: string;
  firstSeen?: number;
  lastUsed?: number;
}

export interface DeviceValidationResult {
  isValid: boolean;
  isNewDevice: boolean;
  riskScore: number;
  reasons: string[];
}

/**
 * 设备指纹服务
 *
 * 功能特性：
 * - 设备指纹生成和验证
 * - 异常设备检测
 * - 设备行为分析
 * - 风险评分机制
 */
@Injectable()
export class DeviceFingerprintService {
  private readonly logger = new Logger(DeviceFingerprintService.name);
  private redis: Redis;

  constructor(private enhancedRedisService: EnhancedRedisService) {
    this.redis = this.enhancedRedisService.getClient();
  }

  /**
   * 生成设备指纹
   */
  generateDeviceFingerprint(request: Request): DeviceInfo {
    const userAgent = request.headers["user-agent"] || "";
    const ip = this.getClientIP(request);
    const acceptLanguage = request.headers["accept-language"] || "";
    const acceptEncoding = request.headers["accept-encoding"] || "";

    // 生成设备指纹哈希
    const fingerprintData = [
      userAgent,
      ip,
      acceptLanguage,
      acceptEncoding,
    ].join("|");

    const fingerprint = crypto
      .createHash("sha256")
      .update(fingerprintData)
      .digest("hex")
      .substring(0, 32);

    return {
      userAgent,
      platform: this.extractPlatform(userAgent),
      ip,
      acceptLanguage,
      acceptEncoding,
      fingerprint,
    };
  }

  /**
   * 验证设备指纹
   */
  async validateDeviceFingerprint(
    userId: string,
    currentDevice: DeviceInfo,
  ): Promise<DeviceValidationResult> {
    try {
      // 获取用户历史设备
      const knownDevices = await this.getUserKnownDevices(userId);

      // 检查是否为已知设备
      const isKnownDevice = knownDevices.some(
        (device) => device.fingerprint === currentDevice.fingerprint,
      );

      if (isKnownDevice) {
        // 更新设备最后使用时间
        await this.updateDeviceLastUsed(userId, currentDevice.fingerprint);

        return {
          isValid: true,
          isNewDevice: false,
          riskScore: 0,
          reasons: [],
        };
      }

      // 新设备风险评估
      const riskAnalysis = await this.analyzeDeviceRisk(
        userId,
        currentDevice,
        knownDevices,
      );

      if (riskAnalysis.riskScore <= 50) {
        // 低风险，自动信任并记录
        await this.recordKnownDevice(userId, currentDevice);

        return {
          isValid: true,
          isNewDevice: true,
          riskScore: riskAnalysis.riskScore,
          reasons: riskAnalysis.reasons,
        };
      } else {
        // 高风险，需要额外验证
        return {
          isValid: false,
          isNewDevice: true,
          riskScore: riskAnalysis.riskScore,
          reasons: riskAnalysis.reasons,
        };
      }
    } catch (error) {
      this.logger.error(
        `设备指纹验证失败: ${userId}`,
        error instanceof Error ? error.stack : String(error),
      );

      // 验证失败时，默认允许但记录风险
      return {
        isValid: true,
        isNewDevice: true,
        riskScore: 30,
        reasons: ["设备验证服务异常"],
      };
    }
  }

  /**
   * 记录短信发送的设备信息
   */
  async recordSmsDevice(phone: string, deviceInfo: DeviceInfo): Promise<void> {
    const key = `sms:device:${phone}`;
    const deviceRecord = {
      ...deviceInfo,
      timestamp: Date.now(),
    };

    await this.redis.setex(key, 24 * 60 * 60, JSON.stringify(deviceRecord)); // 24小时过期

    this.logger.debug(`记录短信设备信息: ${phone} - ${deviceInfo.fingerprint}`);
  }

  /**
   * 验证短信验证设备一致性
   */
  async validateSmsDevice(
    phone: string,
    currentDevice: DeviceInfo,
  ): Promise<boolean> {
    try {
      const key = `sms:device:${phone}`;
      const storedDeviceStr = await this.redis.get(key);

      if (!storedDeviceStr) {
        // 没有记录，允许通过但记录风险
        this.logger.warn(`没有找到短信设备记录: ${phone}`);
        return true;
      }

      const storedDevice = JSON.parse(storedDeviceStr);

      // 检查设备指纹一致性
      const isConsistent =
        storedDevice.fingerprint === currentDevice.fingerprint;

      if (!isConsistent) {
        this.logger.warn(
          `短信验证设备不一致: ${phone} - 发送: ${storedDevice.fingerprint}, 验证: ${currentDevice.fingerprint}`,
        );
      }

      return isConsistent;
    } catch (error) {
      this.logger.error(
        `短信设备验证失败: ${phone}`,
        error instanceof Error ? error.stack : String(error),
      );
      return true; // 验证失败时默认允许
    }
  }

  /**
   * 分析设备风险
   */
  private async analyzeDeviceRisk(
    userId: string,
    currentDevice: DeviceInfo,
    knownDevices: DeviceInfo[],
  ): Promise<{ riskScore: number; reasons: string[] }> {
    let riskScore = 0;
    const reasons: string[] = [];

    // 1. 检查IP地理位置变化
    if (knownDevices.length > 0) {
      const hasIPChange = !knownDevices.some(
        (device) => device.ip === currentDevice.ip,
      );
      if (hasIPChange) {
        riskScore += 20;
        reasons.push("IP地址变化");
      }
    }

    // 2. 检查User-Agent异常
    if (this.isSuspiciousUserAgent(currentDevice.userAgent)) {
      riskScore += 40;
      reasons.push("可疑User-Agent");
    }

    // 3. 检查设备类型一致性
    if (knownDevices.length > 0) {
      const platformChange = !knownDevices.some(
        (device) => device.platform === currentDevice.platform,
      );
      if (platformChange) {
        riskScore += 15;
        reasons.push("设备平台变化");
      }
    }

    // 4. 检查频繁设备变更
    const recentDeviceCount = await this.getRecentDeviceCount(userId);
    if (recentDeviceCount > 3) {
      riskScore += 25;
      reasons.push("频繁设备变更");
    }

    return { riskScore: Math.min(riskScore, 100), reasons };
  }

  /**
   * 获取用户已知设备
   */
  private async getUserKnownDevices(userId: string): Promise<DeviceInfo[]> {
    const key = `user:devices:${userId}`;
    const devicesStr = await this.redis.get(key);

    if (!devicesStr) {
      return [];
    }

    return JSON.parse(devicesStr);
  }

  /**
   * 记录已知设备
   */
  private async recordKnownDevice(
    userId: string,
    deviceInfo: DeviceInfo,
  ): Promise<void> {
    const key = `user:devices:${userId}`;
    const knownDevices = await this.getUserKnownDevices(userId);

    // 添加新设备
    const deviceWithTimestamp = {
      ...deviceInfo,
      firstSeen: Date.now(),
      lastUsed: Date.now(),
    };

    knownDevices.push(deviceWithTimestamp);

    // 限制最多保存10个设备
    if (knownDevices.length > 10) {
      knownDevices.sort((a, b) => (b.lastUsed || 0) - (a.lastUsed || 0));
      knownDevices.splice(10);
    }

    await this.redis.setex(
      key,
      90 * 24 * 60 * 60,
      JSON.stringify(knownDevices),
    ); // 90天

    this.logger.log(`记录新设备: ${userId} - ${deviceInfo.fingerprint}`);
  }

  /**
   * 更新设备最后使用时间
   */
  private async updateDeviceLastUsed(
    userId: string,
    fingerprint: string,
  ): Promise<void> {
    const knownDevices = await this.getUserKnownDevices(userId);
    const deviceIndex = knownDevices.findIndex(
      (device) => device.fingerprint === fingerprint,
    );

    if (deviceIndex >= 0) {
      knownDevices[deviceIndex].lastUsed = Date.now();

      const key = `user:devices:${userId}`;
      await this.redis.setex(
        key,
        90 * 24 * 60 * 60,
        JSON.stringify(knownDevices),
      );
    }
  }

  /**
   * 获取最近设备数量
   */
  private async getRecentDeviceCount(userId: string): Promise<number> {
    const key = `user:recent_devices:${userId}`;
    const count = await this.redis.get(key);
    return parseInt(count || "0");
  }

  /**
   * 检查可疑User-Agent
   */
  private isSuspiciousUserAgent(userAgent: string): boolean {
    const suspiciousPatterns = [
      /curl/i,
      /wget/i,
      /python/i,
      /java/i,
      /bot/i,
      /crawler/i,
      /spider/i,
      /scanner/i,
      /^$/, // 空User-Agent
    ];

    return suspiciousPatterns.some((pattern) => pattern.test(userAgent));
  }

  /**
   * 提取平台信息
   */
  private extractPlatform(userAgent: string): string {
    if (/android/i.test(userAgent)) return "Android";
    if (/iphone|ipad|ipod/i.test(userAgent)) return "iOS";
    if (/windows/i.test(userAgent)) return "Windows";
    if (/macintosh|mac os x/i.test(userAgent)) return "macOS";
    if (/linux/i.test(userAgent)) return "Linux";
    return "Unknown";
  }

  /**
   * 获取客户端IP
   */
  private getClientIP(request: Request): string {
    return (
      (request.headers["x-forwarded-for"] as string)?.split(",")[0] ||
      (request.headers["x-real-ip"] as string) ||
      request.connection?.remoteAddress ||
      request.socket?.remoteAddress ||
      "127.0.0.1"
    ).replace(/^::ffff:/, "");
  }

  /**
   * 清理过期设备记录
   */
  async cleanupExpiredDevices(): Promise<void> {
    // 这个方法可以通过定时任务调用，清理过期的设备记录
    this.logger.log("开始清理过期设备记录");

    // 实现清理逻辑...
    // 这里可以添加清理90天前的设备记录的逻辑
  }
}
