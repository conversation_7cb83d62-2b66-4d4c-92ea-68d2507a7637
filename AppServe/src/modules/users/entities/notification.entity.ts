import {
  Entity,
  PrimaryGeneratedColumn,
  <PERSON>umn,
  CreateDateColumn,
  ManyToOne,
  JoinC<PERSON><PERSON><PERSON>,
} from "typeorm";
import { User } from "./user.entity";

export enum NotificationType {
  CHARACTER_LIGHTING = "character_lighting",
  RELATIONSHIP_CONFIRMED = "relationship_confirmed",
  STORY_SHARED = "story_shared",
  STORY_LIKED = "story_liked",
  STORY_COMMENTED = "story_commented",
  SYSTEM_NOTICE = "system_notice", // 系统通知（警告、限制、暂停等）
}

@Entity("notifications")
export class Notification {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ name: "user_id" })
  userId: string;

  @Column({
    type: "varchar",
    length: 50,
    comment:
      "通知类型：character_lighting, relationship_confirmed, story_shared等",
  })
  type: NotificationType;

  @Column({ length: 255, comment: "通知标题" })
  title: string;

  @Column({ type: "text", nullable: true, comment: "通知内容" })
  content: string;

  @Column({ name: "related_id", nullable: true, comment: "关联的记录ID" })
  relatedId: string;

  @Column({ name: "action_url", nullable: true, comment: "点击跳转的URL" })
  actionUrl: string;

  @Column({ name: "is_read", default: false, comment: "是否已读" })
  isRead: boolean;

  @Column({ name: "read_at", nullable: true, comment: "已读时间" })
  readAt: Date;

  @CreateDateColumn({ name: "created_at", comment: "创建时间" })
  createdAt: Date;

  // 关联关系
  @ManyToOne(() => User, (user) => user.notifications, { onDelete: "CASCADE" })
  @JoinColumn({ name: "user_id" })
  user: User;
}
