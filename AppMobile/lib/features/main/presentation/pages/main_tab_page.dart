import 'package:flutter/material.dart';
import '../widgets/ygs_bottom_navigation_bar.dart';
import '../../../home/<USER>/pages/home_page.dart';
import '../../../story/presentation/pages/story_page.dart';

/// 主Tab导航页面 - 静态版本
class MainTabPage extends StatefulWidget {
  const MainTabPage({super.key});

  @override
  State<MainTabPage> createState() => _MainTabPageState();
}

class _MainTabPageState extends State<MainTabPage> {
  int _currentIndex = 0;

  // Tab页面列表 - 4个模块：首页、故事、消息、我的
  final List<Widget> _pages = [
    const HomePage(),      // 首页（"我关注的"内容）
    const StoryPage(),     // 故事页
    const MessagePage(),   // 消息页
    const ProfilePage(),   // 我的页
  ];


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // 关键修复：使用Stack布局，让内容延伸到导航栏下方
      body: Stack(
        children: [
          // 主内容区域 - 延伸到屏幕底部
          Positioned.fill(
            child: IndexedStack(
              index: _currentIndex,
              children: _pages,
            ),
          ),
          
          // 底部导航栏 - 浮动覆盖在内容上方
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: YgsBottomNavigationBar(
              currentIndex: _currentIndex,
              onTap: (index) {
                setState(() {
                  _currentIndex = index;
                });
              },
            ),
          ),
        ],
      ),
    );
  }
}



/// 消息页
class MessagePage extends StatelessWidget {
  const MessagePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF4F7FA), // 设计稿背景色
      appBar: AppBar(
        title: const Text('消息'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: const Center(
        child: Text(
          '消息页内容待实现',
          style: TextStyle(
            fontSize: 24,
            color: Color(0xFF151523),
          ),
        ),
      ),
    );
  }
}

/// 我的页
class ProfilePage extends StatelessWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF4F7FA), // 设计稿背景色
      appBar: AppBar(
        title: const Text('我的'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: const Center(
        child: Text(
          '我的页内容待实现',
          style: TextStyle(
            fontSize: 24,
            color: Color(0xFF151523),
          ),
        ),
      ),
    );
  }
}