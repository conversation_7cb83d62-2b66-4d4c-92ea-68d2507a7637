/* eslint-disable @typescript-eslint/no-explicit-any, @typescript-eslint/no-unused-vars */
import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { HttpStatus } from "@nestjs/common";
import type { Request } from "express";

import { ReportsController } from "./reports.controller";
import { ReportsService } from "./reports.service";
import {
  ReportType,
  ReportReason,
  ReportStatus,
  ReportSeverity,
} from "./entities/report.entity";
import type {
  CreateReportDto,
  UpdateReportDto,
  ReportQueryDto,
  ReportResponseDto,
  ReportStatsResponseDto,
} from "./dto";

describe("ReportsController - 企业级单元测试", () => {
  let controller: ReportsController;
  let mockReportsService: jest.Mocked<ReportsService>;

  // 测试数据工厂
  const mockUser = { id: "user-001", nickname: "测试用户" };
  const mockRequest = {
    user: mockUser,
  } as unknown as Request & { user: { id: string } };

  const mockReportResponse: ReportResponseDto = {
    id: "report-001",
    reportType: ReportType.STORY,
    targetId: "story-001",
    reporterId: "reporter-001",
    reporterName: "举报者",
    reason: ReportReason.INAPPROPRIATE_CONTENT,
    description: "这个故事包含不当内容",
    status: ReportStatus.PENDING,
    severity: ReportSeverity.MEDIUM,
    reviewerId: undefined,
    reviewerName: undefined,
    reviewNote: undefined,
    reviewedAt: undefined,
    contentSnapshot: {
      title: "测试故事",
      content: "这是一个测试故事的内容",
      authorName: "故事作者",
    },
    metadata: { source: "web", device: "desktop" },
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockPaginatedResponse = {
    data: [mockReportResponse],
    total: 1,
    page: 1,
    limit: 10,
    totalPages: 1,
    hasNext: false,
    hasPrev: false,
  };

  const mockStatsResponse: ReportStatsResponseDto = {
    totalReports: 25,
    pendingReports: 10,
    underReviewReports: 5,
    resolvedReports: 8,
    rejectedReports: 2,
    escalatedReports: 0,
    storyReports: 15,
    commentReports: 8,
    userReports: 2,
    reasonStats: [
      {
        reason: ReportReason.INAPPROPRIATE_CONTENT,
        count: 12,
        percentage: 48,
      },
      {
        reason: ReportReason.SPAM,
        count: 8,
        percentage: 32,
      },
      {
        reason: ReportReason.HARASSMENT,
        count: 3,
        percentage: 12,
      },
      {
        reason: ReportReason.OTHER,
        count: 2,
        percentage: 8,
      },
    ],
    severityStats: [
      {
        severity: ReportSeverity.LOW,
        count: 8,
        percentage: 32,
      },
      {
        severity: ReportSeverity.MEDIUM,
        count: 12,
        percentage: 48,
      },
      {
        severity: ReportSeverity.HIGH,
        count: 4,
        percentage: 16,
      },
      {
        severity: ReportSeverity.CRITICAL,
        count: 1,
        percentage: 4,
      },
    ],
    avgProcessingTime: 24.5,
    todayReports: 3,
    thisWeekReports: 12,
    thisMonthReports: 25,
  };

  beforeEach(async () => {
    const mockService = {
      createReport: jest.fn(),
      getReports: jest.fn(),
      getReportById: jest.fn(),
      updateReport: jest.fn(),
      getReportStats: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [ReportsController],
      providers: [
        {
          provide: ReportsService,
          useValue: mockService,
        },
      ],
    }).compile();

    controller = module.get<ReportsController>(ReportsController);
    mockReportsService = module.get(ReportsService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("createReport - POST /reports", () => {
    const createReportDto: CreateReportDto = {
      reportType: ReportType.STORY,
      targetId: "story-001",
      reason: ReportReason.INAPPROPRIATE_CONTENT,
      description: "这个故事包含不当内容，请审核处理",
      metadata: { source: "web", device: "desktop" },
    };

    it("应该成功创建故事举报", async () => {
      // Arrange
      mockReportsService.createReport.mockResolvedValue(mockReportResponse);

      // Act
      const result = await controller.createReport(
        createReportDto,
        mockRequest,
      );

      // Assert
      expect(mockReportsService.createReport).toHaveBeenCalledWith(
        createReportDto,
        "user-001",
      );
      expect(result).toEqual({
        success: true,
        message: "举报提交成功",
        data: mockReportResponse,
        statusCode: HttpStatus.CREATED,
        timestamp: expect.any(String),
      });
    });

    it("应该正确处理评论举报", async () => {
      // Arrange
      const commentReportDto = {
        ...createReportDto,
        reportType: ReportType.COMMENT,
        targetId: "comment-001",
        reason: ReportReason.HARASSMENT,
      };
      const commentReportResponse = {
        ...mockReportResponse,
        reportType: ReportType.COMMENT,
        targetId: "comment-001",
        reason: ReportReason.HARASSMENT,
      };
      mockReportsService.createReport.mockResolvedValue(commentReportResponse);

      // Act
      const result = await controller.createReport(
        commentReportDto,
        mockRequest,
      );

      // Assert
      expect(mockReportsService.createReport).toHaveBeenCalledWith(
        commentReportDto,
        "user-001",
      );
      expect(result.data.reportType).toBe(ReportType.COMMENT);
      expect(result.data.reason).toBe(ReportReason.HARASSMENT);
    });

    it("应该正确处理用户举报", async () => {
      // Arrange
      const userReportDto = {
        ...createReportDto,
        reportType: ReportType.USER,
        targetId: "user-002",
        reason: ReportReason.FRAUD,
      };
      const userReportResponse = {
        ...mockReportResponse,
        reportType: ReportType.USER,
        targetId: "user-002",
        reason: ReportReason.FRAUD,
      };
      mockReportsService.createReport.mockResolvedValue(userReportResponse);

      // Act
      const result = await controller.createReport(userReportDto, mockRequest);

      // Assert
      expect(result.data.reportType).toBe(ReportType.USER);
      expect(result.data.reason).toBe(ReportReason.FRAUD);
    });

    it("应该正确处理服务层错误", async () => {
      // Arrange
      mockReportsService.createReport.mockRejectedValue(
        new Error("举报目标不存在"),
      );

      // Act & Assert
      await expect(
        controller.createReport(createReportDto, mockRequest),
      ).rejects.toThrow("举报目标不存在");
    });

    it("应该正确处理重复举报错误", async () => {
      // Arrange
      mockReportsService.createReport.mockRejectedValue(
        new Error("您已经举报过该内容"),
      );

      // Act & Assert
      await expect(
        controller.createReport(createReportDto, mockRequest),
      ).rejects.toThrow("您已经举报过该内容");
    });

    it("应该正确处理自己举报自己的错误", async () => {
      // Arrange
      mockReportsService.createReport.mockRejectedValue(
        new Error("不能举报自己的内容"),
      );

      // Act & Assert
      await expect(
        controller.createReport(createReportDto, mockRequest),
      ).rejects.toThrow("不能举报自己的内容");
    });
  });

  describe("getReports - GET /reports", () => {
    const queryDto: ReportQueryDto = {
      page: 1,
      limit: 10,
      status: ReportStatus.PENDING,
    };

    it("应该成功获取举报列表", async () => {
      // Arrange
      mockReportsService.getReports.mockResolvedValue(mockPaginatedResponse);

      // Act
      const result = await controller.getReports(queryDto);

      // Assert
      expect(mockReportsService.getReports).toHaveBeenCalledWith(queryDto);
      expect(result).toEqual({
        success: true,
        message: "获取举报列表成功",
        data: mockPaginatedResponse,
        statusCode: HttpStatus.OK,
        timestamp: expect.any(String),
      });
    });

    it("应该正确传递查询参数", async () => {
      // Arrange
      const customQuery = {
        page: 2,
        limit: 5,
        status: ReportStatus.UNDER_REVIEW,
        reportType: ReportType.COMMENT,
        severity: ReportSeverity.HIGH,
        startDate: "2024-01-01T00:00:00.000Z",
        endDate: "2024-01-31T23:59:59.999Z",
        reporterId: "reporter-001",
      } as ReportQueryDto;
      mockReportsService.getReports.mockResolvedValue(mockPaginatedResponse);

      // Act
      await controller.getReports(customQuery);

      // Assert
      expect(mockReportsService.getReports).toHaveBeenCalledWith(customQuery);
    });

    it("应该处理空结果", async () => {
      // Arrange
      const emptyResponse = {
        ...mockPaginatedResponse,
        data: [],
        total: 0,
      };
      mockReportsService.getReports.mockResolvedValue(emptyResponse);

      // Act
      const result = await controller.getReports(queryDto);

      // Assert
      expect(result.data.total).toBe(0);
      expect(result.data.data).toEqual([]);
    });

    it("应该正确处理分页参数", async () => {
      // Arrange
      const paginationQuery = {
        page: 3,
        limit: 20,
      } as ReportQueryDto;
      const paginatedResponse = {
        ...mockPaginatedResponse,
        page: 3,
        limit: 20,
        totalPages: 5,
        hasNext: true,
        hasPrev: true,
      };
      mockReportsService.getReports.mockResolvedValue(paginatedResponse);

      // Act
      const result = await controller.getReports(paginationQuery);

      // Assert
      expect(result.data.page).toBe(3);
      expect(result.data.limit).toBe(20);
      expect(result.data.hasNext).toBe(true);
      expect(result.data.hasPrev).toBe(true);
    });
  });

  describe("getReportStats - GET /reports/stats", () => {
    it("应该成功获取举报统计", async () => {
      // Arrange
      mockReportsService.getReportStats.mockResolvedValue(mockStatsResponse);

      // Act
      const result = await controller.getReportStats();

      // Assert
      expect(mockReportsService.getReportStats).toHaveBeenCalled();
      expect(result).toEqual({
        success: true,
        message: "获取举报统计成功",
        data: mockStatsResponse,
        statusCode: HttpStatus.OK,
        timestamp: expect.any(String),
      });
    });

    it("应该正确返回详细统计数据", async () => {
      // Arrange
      const detailedStats = {
        ...mockStatsResponse,
        totalReports: 100,
        reasonStats: [
          {
            reason: ReportReason.INAPPROPRIATE_CONTENT,
            count: 40,
            percentage: 40,
          },
          { reason: ReportReason.SPAM, count: 30, percentage: 30 },
          { reason: ReportReason.HARASSMENT, count: 20, percentage: 20 },
          { reason: ReportReason.OTHER, count: 10, percentage: 10 },
        ],
      };
      mockReportsService.getReportStats.mockResolvedValue(detailedStats);

      // Act
      const result = await controller.getReportStats();

      // Assert
      expect(result.data.totalReports).toBe(100);
      expect(result.data.reasonStats).toHaveLength(4);
      expect(result.data.reasonStats[0].percentage).toBe(40);
    });

    it("应该正确处理空统计数据", async () => {
      // Arrange
      const emptyStats = {
        totalReports: 0,
        pendingReports: 0,
        underReviewReports: 0,
        resolvedReports: 0,
        rejectedReports: 0,
        escalatedReports: 0,
        storyReports: 0,
        commentReports: 0,
        userReports: 0,
        reasonStats: [],
        severityStats: [],
        avgProcessingTime: 0,
        todayReports: 0,
        thisWeekReports: 0,
        thisMonthReports: 0,
      };
      mockReportsService.getReportStats.mockResolvedValue(emptyStats);

      // Act
      const result = await controller.getReportStats();

      // Assert
      expect(result.data.totalReports).toBe(0);
      expect(result.data.reasonStats).toEqual([]);
      expect(result.data.severityStats).toEqual([]);
    });
  });

  describe("getReportById - GET /reports/:id", () => {
    it("应该成功获取举报详情", async () => {
      // Arrange
      mockReportsService.getReportById.mockResolvedValue(mockReportResponse);

      // Act
      const result = await controller.getReportById("report-001");

      // Assert
      expect(mockReportsService.getReportById).toHaveBeenCalledWith(
        "report-001",
      );
      expect(result).toEqual({
        success: true,
        message: "获取举报详情成功",
        data: mockReportResponse,
        statusCode: HttpStatus.OK,
        timestamp: expect.any(String),
      });
    });

    it("应该正确处理举报不存在的情况", async () => {
      // Arrange
      mockReportsService.getReportById.mockRejectedValue(
        new Error("举报记录不存在"),
      );

      // Act & Assert
      await expect(controller.getReportById("non-existent")).rejects.toThrow(
        "举报记录不存在",
      );
    });

    it("应该正确返回完整的举报信息", async () => {
      // Arrange
      const completeReport = {
        ...mockReportResponse,
        reviewerId: "reviewer-001",
        reviewerName: "审核员",
        reviewNote: "已处理，内容符合规范",
        reviewedAt: new Date(),
        status: ReportStatus.RESOLVED,
      };
      mockReportsService.getReportById.mockResolvedValue(completeReport);

      // Act
      const result = await controller.getReportById("report-001");

      // Assert
      expect(result.data.reviewerId).toBe("reviewer-001");
      expect(result.data.reviewerName).toBe("审核员");
      expect(result.data.reviewNote).toBe("已处理，内容符合规范");
      expect(result.data.status).toBe(ReportStatus.RESOLVED);
      expect(result.data.reviewedAt).toBeDefined();
    });
  });

  describe("updateReport - PUT /reports/:id", () => {
    const updateReportDto: UpdateReportDto = {
      status: ReportStatus.RESOLVED,
      reviewNote: "已处理，内容符合规范",
      severity: ReportSeverity.LOW,
    };

    it("应该成功更新举报状态", async () => {
      // Arrange
      const updatedReport = {
        ...mockReportResponse,
        status: ReportStatus.RESOLVED,
        reviewerId: "user-001",
        reviewerName: "测试用户",
        reviewNote: "已处理，内容符合规范",
        severity: ReportSeverity.LOW,
        reviewedAt: new Date(),
      };
      mockReportsService.updateReport.mockResolvedValue(updatedReport);

      // Act
      const result = await controller.updateReport(
        "report-001",
        updateReportDto,
        mockRequest,
      );

      // Assert
      expect(mockReportsService.updateReport).toHaveBeenCalledWith(
        "report-001",
        updateReportDto,
        "user-001",
      );
      expect(result).toEqual({
        success: true,
        message: "举报状态更新成功",
        data: updatedReport,
        statusCode: HttpStatus.OK,
        timestamp: expect.any(String),
      });
    });

    it("应该正确处理状态转换", async () => {
      // Arrange
      const statusUpdates = [
        { status: ReportStatus.UNDER_REVIEW, note: "开始审核" },
        { status: ReportStatus.RESOLVED, note: "已解决" },
        { status: ReportStatus.REJECTED, note: "举报无效" },
        { status: ReportStatus.ESCALATED, note: "升级处理" },
      ];

      for (const update of statusUpdates) {
        const updateDto = {
          status: update.status,
          reviewNote: update.note,
        };
        const updatedReport = {
          ...mockReportResponse,
          status: update.status,
          reviewNote: update.note,
          reviewedAt: new Date(),
        };
        mockReportsService.updateReport.mockResolvedValue(updatedReport);

        // Act
        const result = await controller.updateReport(
          "report-001",
          updateDto,
          mockRequest,
        );

        // Assert
        expect(result.data.status).toBe(update.status);
        expect(result.data.reviewNote).toBe(update.note);
      }
    });

    it("应该正确处理严重程度更新", async () => {
      // Arrange
      const severityUpdateDto = {
        severity: ReportSeverity.CRITICAL,
        reviewNote: "提升为紧急处理",
      };
      const updatedReport = {
        ...mockReportResponse,
        severity: ReportSeverity.CRITICAL,
        reviewNote: "提升为紧急处理",
      };
      mockReportsService.updateReport.mockResolvedValue(updatedReport);

      // Act
      const result = await controller.updateReport(
        "report-001",
        severityUpdateDto,
        mockRequest,
      );

      // Assert
      expect(result.data.severity).toBe(ReportSeverity.CRITICAL);
      expect(result.data.reviewNote).toBe("提升为紧急处理");
    });

    it("应该正确处理权限错误", async () => {
      // Arrange
      mockReportsService.updateReport.mockRejectedValue(
        new Error("无权限处理此举报"),
      );

      // Act & Assert
      await expect(
        controller.updateReport("report-001", updateReportDto, mockRequest),
      ).rejects.toThrow("无权限处理此举报");
    });

    it("应该正确处理举报不存在的情况", async () => {
      // Arrange
      mockReportsService.updateReport.mockRejectedValue(
        new Error("举报记录不存在"),
      );

      // Act & Assert
      await expect(
        controller.updateReport("non-existent", updateReportDto, mockRequest),
      ).rejects.toThrow("举报记录不存在");
    });
  });

  describe("错误处理和边界测试", () => {
    it("应该正确处理服务层抛出的各种异常", async () => {
      // Arrange
      const errors = [
        "举报目标不存在",
        "您已经举报过该内容",
        "不能举报自己的内容",
        "举报记录不存在",
        "无权限处理此举报",
        "无效的举报状态",
      ];

      for (const error of errors) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        mockReportsService.createReport.mockRejectedValue(new Error(error));

        // Act & Assert
        await expect(
          controller.createReport(
            {
              reportType: ReportType.STORY,
              targetId: "story-001",
              reason: ReportReason.SPAM,
            },
            mockRequest,
          ),
        ).rejects.toThrow(error);
      }
    });

    it("应该处理无效的UUID格式", async () => {
      // Arrange
      mockReportsService.getReportById.mockRejectedValue(
        new Error("无效的ID格式"),
      );

      // Act & Assert
      await expect(controller.getReportById("invalid-uuid")).rejects.toThrow(
        "无效的ID格式",
      );
    });

    it("应该处理请求中缺少用户信息的情况", async () => {
      // Arrange
      const invalidRequest = {} as Request & { user: { id: string } };

      // Act & Assert
      await expect(
        controller.createReport(
          {
            reportType: ReportType.STORY,
            targetId: "story-001",
            reason: ReportReason.SPAM,
          },
          invalidRequest,
        ),
      ).rejects.toThrow();
    });
  });

  describe("ApiResponse 格式验证", () => {
    it("所有成功响应应该包含正确的 ApiResponse 格式", async () => {
      // Arrange
      mockReportsService.createReport.mockResolvedValue(mockReportResponse);

      // Act
      const result = await controller.createReport(
        {
          reportType: ReportType.STORY,
          targetId: "story-001",
          reason: ReportReason.SPAM,
        },
        mockRequest,
      );

      // Assert
      expect(result).toEqual({
        success: true,
        message: expect.any(String),
        data: expect.any(Object),
        statusCode: expect.any(Number),
        timestamp: expect.any(String),
      });
    });

    it("应该包含正确的HTTP状态码", async () => {
      // Arrange
      mockReportsService.createReport.mockResolvedValue(mockReportResponse);
      mockReportsService.getReports.mockResolvedValue(mockPaginatedResponse);
      mockReportsService.getReportStats.mockResolvedValue(mockStatsResponse);

      // Act
      const createResult = await controller.createReport(
        {
          reportType: ReportType.STORY,
          targetId: "story-001",
          reason: ReportReason.SPAM,
        },
        mockRequest,
      );
      const listResult = await controller.getReports({});
      const statsResult = await controller.getReportStats();

      // Assert
      expect(createResult.statusCode).toBe(HttpStatus.CREATED);
      expect(listResult.statusCode).toBe(HttpStatus.OK);
      expect(statsResult.statusCode).toBe(HttpStatus.OK);
    });
  });

  describe("举报类型处理", () => {
    it("应该正确处理不同类型的举报", async () => {
      // Arrange
      const reportTypes = [
        { type: ReportType.STORY, targetId: "story-001" },
        { type: ReportType.COMMENT, targetId: "comment-001" },
        { type: ReportType.USER, targetId: "user-002" },
      ];

      for (const reportType of reportTypes) {
        const reportResponse = {
          ...mockReportResponse,
          reportType: reportType.type,
          targetId: reportType.targetId,
        };
        mockReportsService.createReport.mockResolvedValue(reportResponse);

        // Act
        const result = await controller.createReport(
          {
            reportType: reportType.type,
            targetId: reportType.targetId,
            reason: ReportReason.SPAM,
          },
          mockRequest,
        );

        // Assert
        expect(result.data.reportType).toBe(reportType.type);
        expect(result.data.targetId).toBe(reportType.targetId);
      }
    });

    it("应该正确处理不同的举报原因", async () => {
      // Arrange
      const reasons = [
        ReportReason.SPAM,
        ReportReason.INAPPROPRIATE_CONTENT,
        ReportReason.HARASSMENT,
        ReportReason.COPYRIGHT_VIOLATION,
        ReportReason.PERSONAL_INFO_LEAK,
        ReportReason.FRAUD,
        ReportReason.VIOLENCE,
        ReportReason.HATE_SPEECH,
        ReportReason.OTHER,
      ];

      for (const reason of reasons) {
        const reportResponse = {
          ...mockReportResponse,
          reason,
        };
        mockReportsService.createReport.mockResolvedValue(reportResponse);

        // Act
        const result = await controller.createReport(
          {
            reportType: ReportType.STORY,
            targetId: "story-001",
            reason,
          },
          mockRequest,
        );

        // Assert
        expect(result.data.reason).toBe(reason);
      }
    });
  });

  describe("分页参数处理", () => {
    it("应该正确处理默认分页参数", async () => {
      // Arrange
      mockReportsService.getReports.mockResolvedValue(mockPaginatedResponse);

      // Act
      await controller.getReports({});

      // Assert
      expect(mockReportsService.getReports).toHaveBeenCalledWith(
        expect.objectContaining({
          page: undefined, // 由服务层设置默认值
          limit: undefined,
        }),
      );
    });

    it("应该正确验证分页参数范围", async () => {
      // Arrange
      const invalidQuery = {
        page: -1,
        limit: 0,
      } as ReportQueryDto;

      // 模拟服务层验证
      mockReportsService.getReports.mockRejectedValue(
        new Error("无效的分页参数"),
      );

      // Act & Assert
      await expect(controller.getReports(invalidQuery)).rejects.toThrow(
        "无效的分页参数",
      );
    });
  });

  describe("举报状态流转", () => {
    it("应该正确处理举报状态流转", async () => {
      // Arrange
      const statusFlow = [
        ReportStatus.PENDING,
        ReportStatus.UNDER_REVIEW,
        ReportStatus.RESOLVED,
      ];

      for (const status of statusFlow) {
        const reportResponse = {
          ...mockReportResponse,
          status,
        };
        mockReportsService.updateReport.mockResolvedValue(reportResponse);

        // Act
        const result = await controller.updateReport(
          "report-001",
          { status },
          mockRequest,
        );

        // Assert
        expect(result.data.status).toBe(status);
      }
    });

    it("应该正确处理严重程度调整", async () => {
      // Arrange
      const severities = [
        ReportSeverity.LOW,
        ReportSeverity.MEDIUM,
        ReportSeverity.HIGH,
        ReportSeverity.CRITICAL,
      ];

      for (const severity of severities) {
        const reportResponse = {
          ...mockReportResponse,
          severity,
        };
        mockReportsService.updateReport.mockResolvedValue(reportResponse);

        // Act
        const result = await controller.updateReport(
          "report-001",
          { severity },
          mockRequest,
        );

        // Assert
        expect(result.data.severity).toBe(severity);
      }
    });
  });
});
