/* eslint-disable @typescript-eslint/no-explicit-any */
import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { HttpStatus } from "@nestjs/common";

import { HomepageController } from "./homepage.controller";
import { HomepageService } from "./homepage.service";
import { HomepageTheme, HomepageLayout } from "./dto";
import type {
  UpdateDisplaySettingsDto,
  UpdateHomepageSettingsDto,
  HomepageDataResponseDto,
  PersonalStatsResponseDto,
} from "./dto";

describe("HomepageController - 企业级单元测试", () => {
  let controller: HomepageController;
  let mockService: jest.Mocked<HomepageService>;

  // Mock数据 - 个人主页数据
  const mockHomepageData: HomepageDataResponseDto = {
    userId: "test-user-id",
    nickname: "测试用户",
    username: "testuser",
    avatarUrl: "https://example.com/avatar.jpg",
    birthDate: new Date("1990-01-01"),
    bio: "这是一个测试用户",
    userNumber: "YGS001",
    displaySettings: {
      showBirthday: true,
      showBio: true,
      showStatistics: true,
      showCharacters: true,
      showTimeline: true,
      showStories: true,
      showBookmarks: false,
      showFollowList: false,
      showReferenceCollections: true,
    },
    advancedSettings: {
      theme: HomepageTheme.AUTO,
      layout: HomepageLayout.MODERN,
      customBio: "个性化简介",
      backgroundImage: "https://example.com/bg.jpg",
      pinnedItems: ["story-1", "character-1"],
      socialLinks: [
        {
          platform: "github",
          url: "https://github.com/testuser",
          display: true,
        },
      ],
    },
    statistics: {
      storiesCount: 5,
      charactersCount: 3,
      lightedCharactersCount: 2,
      bookmarksCount: 10,
      followersCount: 25,
      followingCount: 30,
      timelineEventsCount: 8,
      referenceCollectionsCount: 2,
      totalLikesReceived: 150,
      totalViewsReceived: 1000,
      memberSince: new Date("2024-01-01"),
      lastActiveAt: new Date("2025-01-01"),
    },
    content: {
      recentStories: [],
      recentCharacters: [],
      timelineHighlights: [],
      bookmarkHighlights: [],
      referenceCollectionHighlights: [],
    },
    canEdit: false,
    isFollowing: false,
    isFriend: false,
  };

  // Mock数据 - 个人统计数据
  const mockPersonalStats: PersonalStatsResponseDto = {
    userId: "test-user-id",
    basicStats: {
      storiesCount: 5,
      charactersCount: 3,
      lightedCharactersCount: 2,
      bookmarksCount: 10,
      timelineEventsCount: 8,
      referenceCollectionsCount: 2,
    },
    socialStats: {
      followersCount: 25,
      followingCount: 30,
      friendsCount: 10,
      mutualFollowsCount: 5,
    },
    interactionStats: {
      totalLikesReceived: 150,
      totalCommentsReceived: 80,
      totalViewsReceived: 1000,
      totalSharesReceived: 20,
      averageLikesPerStory: 30,
      averageViewsPerStory: 200,
    },
    creationStats: {
      storiesThisMonth: 2,
      charactersThisMonth: 1,
      timelineEventsThisMonth: 3,
      mostActiveMonth: {
        month: "2024-12",
        storiesCount: 5,
      },
      longestStoryStreak: 15,
      currentStoryStreak: 5,
    },
    trendData: {
      monthlyCreation: [
        {
          month: "2025-01",
          storiesCount: 2,
          charactersCount: 1,
          timelineEventsCount: 3,
        },
        {
          month: "2024-12",
          storiesCount: 3,
          charactersCount: 2,
          timelineEventsCount: 5,
        },
      ],
      dailyInteraction: [
        {
          date: "2025-01-01",
          likesReceived: 10,
          viewsReceived: 100,
          commentsReceived: 5,
        },
      ],
    },
    achievements: [
      {
        id: "achievement-1",
        title: "首个故事",
        description: "发布第一个故事",
        icon: "story",
        achievedAt: new Date("2024-01-15"),
        category: "creation",
      },
    ],
    accountInfo: {
      memberSince: new Date("2024-01-01"),
      lastActiveAt: new Date("2025-01-01"),
      totalDaysActive: 300,
      averageDailyActivity: 2.5,
    },
  };

  beforeEach(async () => {
    const mockHomepageService = {
      getHomepageData: jest.fn(),
      updateDisplaySettings: jest.fn(),
      updateHomepageSettings: jest.fn(),
      getPersonalStats: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [HomepageController],
      providers: [
        {
          provide: HomepageService,
          useValue: mockHomepageService,
        },
      ],
    }).compile();

    controller = module.get<HomepageController>(HomepageController);
    mockService = module.get(HomepageService) as jest.Mocked<HomepageService>;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("getHomepageData - 获取个人主页完整数据", () => {
    it("应该成功获取个人主页数据（未登录用户）", async () => {
      // Arrange
      const userId = "test-user-id";
      mockService.getHomepageData.mockResolvedValue(mockHomepageData);

      // Act
      const result = await controller.getHomepageData(userId);

      // Assert
      expect(result).toEqual({
        success: true,
        data: mockHomepageData,
        message: "获取个人主页数据成功",
      });
      expect(mockService.getHomepageData).toHaveBeenCalledWith(
        userId,
        undefined,
      );
    });

    it("应该成功获取个人主页数据（已登录用户）", async () => {
      // Arrange
      const userId = "test-user-id";
      const currentUserId = "current-user-id";
      const mockRequest = {
        user: { id: currentUserId },
      };

      mockService.getHomepageData.mockResolvedValue(mockHomepageData);

      // Act
      const result = await controller.getHomepageData(userId, mockRequest);

      // Assert
      expect(result).toEqual({
        success: true,
        data: mockHomepageData,
        message: "获取个人主页数据成功",
      });
      expect(mockService.getHomepageData).toHaveBeenCalledWith(
        userId,
        currentUserId,
      );
    });

    it("应该处理服务层抛出的异常", async () => {
      // Arrange
      const userId = "non-existent-user";
      mockService.getHomepageData.mockRejectedValue(new Error("用户不存在"));

      // Act & Assert
      await expect(controller.getHomepageData(userId)).rejects.toThrow(
        "用户不存在",
      );
      expect(mockService.getHomepageData).toHaveBeenCalledWith(
        userId,
        undefined,
      );
    });

    it("应该正确处理空的request对象", async () => {
      // Arrange
      const userId = "test-user-id";
      mockService.getHomepageData.mockResolvedValue(mockHomepageData);

      // Act
      const result = await controller.getHomepageData(userId, undefined);

      // Assert
      expect(result.success).toBe(true);
      expect(mockService.getHomepageData).toHaveBeenCalledWith(
        userId,
        undefined,
      );
    });
  });

  describe("updateDisplaySettings - 更新个人主页展示设置", () => {
    const updateDto: UpdateDisplaySettingsDto = {
      showBirthday: false,
      showBio: true,
      showStatistics: true,
      showCharacters: true,
      showTimeline: false,
      showStories: true,
      showBookmarks: true,
      showFollowList: false,
      showReferenceCollections: true,
    };

    it("应该成功更新展示设置", async () => {
      // Arrange
      const userId = "test-user-id";
      const mockRequest = {
        user: { id: userId },
      };
      const mockResult = { message: "展示设置更新成功" };

      mockService.updateDisplaySettings.mockResolvedValue(mockResult);

      // Act
      const result = await controller.updateDisplaySettings(
        mockRequest,
        updateDto,
      );

      // Assert
      expect(result).toEqual({
        success: true,
        data: mockResult,
        message: "展示设置更新成功",
      });
      expect(mockService.updateDisplaySettings).toHaveBeenCalledWith(
        userId,
        updateDto,
      );
    });

    it("应该处理部分字段更新", async () => {
      // Arrange
      const userId = "test-user-id";
      const mockRequest = {
        user: { id: userId },
      };
      const partialUpdateDto: UpdateDisplaySettingsDto = {
        showBirthday: false,
        showStatistics: true,
      };
      const mockResult = { message: "展示设置更新成功" };

      mockService.updateDisplaySettings.mockResolvedValue(mockResult);

      // Act
      const result = await controller.updateDisplaySettings(
        mockRequest,
        partialUpdateDto,
      );

      // Assert
      expect(result.success).toBe(true);
      expect(mockService.updateDisplaySettings).toHaveBeenCalledWith(
        userId,
        partialUpdateDto,
      );
    });

    it("应该处理服务层抛出的异常", async () => {
      // Arrange
      const userId = "test-user-id";
      const mockRequest = {
        user: { id: userId },
      };

      mockService.updateDisplaySettings.mockRejectedValue(
        new Error("更新失败"),
      );

      // Act & Assert
      await expect(
        controller.updateDisplaySettings(mockRequest, updateDto),
      ).rejects.toThrow("更新失败");
      expect(mockService.updateDisplaySettings).toHaveBeenCalledWith(
        userId,
        updateDto,
      );
    });

    it("应该验证用户认证", async () => {
      // Arrange - 这个测试主要验证控制器的行为，实际的JWT验证由Guard处理
      const userId = "test-user-id";
      const mockRequest = {
        user: { id: userId },
      };
      const mockResult = { message: "展示设置更新成功" };

      mockService.updateDisplaySettings.mockResolvedValue(mockResult);

      // Act
      const result = await controller.updateDisplaySettings(
        mockRequest,
        updateDto,
      );

      // Assert
      expect(result.success).toBe(true);
      expect(mockService.updateDisplaySettings).toHaveBeenCalledWith(
        userId,
        updateDto,
      );
    });
  });

  describe("updateHomepageSettings - 更新个人主页高级设置", () => {
    const updateDto: UpdateHomepageSettingsDto = {
      theme: HomepageTheme.DARK,
      layout: HomepageLayout.MINIMAL,
      customBio: "新的个性化简介",
      backgroundImage: "https://example.com/new-bg.jpg",
      pinnedItems: ["story-2", "character-2"],
      socialLinks: [
        {
          platform: "twitter",
          url: "https://twitter.com/testuser",
          display: true,
        },
      ],
      showBirthday: true,
      showBio: false,
    };

    it("应该成功更新高级设置", async () => {
      // Arrange
      const userId = "test-user-id";
      const mockRequest = {
        user: { id: userId },
      };
      const mockResult = { message: "主页设置更新成功" };

      mockService.updateHomepageSettings.mockResolvedValue(mockResult);

      // Act
      const result = await controller.updateHomepageSettings(
        mockRequest,
        updateDto,
      );

      // Assert
      expect(result).toEqual({
        success: true,
        data: mockResult,
        message: "主页设置更新成功",
      });
      expect(mockService.updateHomepageSettings).toHaveBeenCalledWith(
        userId,
        updateDto,
      );
    });

    it("应该正确处理枚举值", async () => {
      // Arrange
      const userId = "test-user-id";
      const mockRequest = {
        user: { id: userId },
      };
      const enumUpdateDto: UpdateHomepageSettingsDto = {
        theme: HomepageTheme.AUTO,
        layout: HomepageLayout.TIMELINE_FOCUSED,
      };
      const mockResult = { message: "主页设置更新成功" };

      mockService.updateHomepageSettings.mockResolvedValue(mockResult);

      // Act
      const result = await controller.updateHomepageSettings(
        mockRequest,
        enumUpdateDto,
      );

      // Assert
      expect(result.success).toBe(true);
      expect(mockService.updateHomepageSettings).toHaveBeenCalledWith(
        userId,
        expect.objectContaining({
          theme: HomepageTheme.AUTO,
          layout: HomepageLayout.TIMELINE_FOCUSED,
        }),
      );
    });

    it("应该处理复杂的社交链接数据", async () => {
      // Arrange
      const userId = "test-user-id";
      const mockRequest = {
        user: { id: userId },
      };
      const complexUpdateDto: UpdateHomepageSettingsDto = {
        socialLinks: [
          {
            platform: "github",
            url: "https://github.com/user",
            display: true,
          },
          {
            platform: "twitter",
            url: "https://twitter.com/user",
            display: false,
          },
          {
            platform: "linkedin",
            url: "https://linkedin.com/in/user",
            display: true,
          },
        ],
        pinnedItems: ["story-1", "story-2", "character-1", "timeline-1"],
      };
      const mockResult = { message: "主页设置更新成功" };

      mockService.updateHomepageSettings.mockResolvedValue(mockResult);

      // Act
      const result = await controller.updateHomepageSettings(
        mockRequest,
        complexUpdateDto,
      );

      // Assert
      expect(result.success).toBe(true);
      expect(mockService.updateHomepageSettings).toHaveBeenCalledWith(
        userId,
        expect.objectContaining({
          socialLinks: complexUpdateDto.socialLinks,
          pinnedItems: complexUpdateDto.pinnedItems,
        }),
      );
    });

    it("应该处理服务层异常", async () => {
      // Arrange
      const userId = "test-user-id";
      const mockRequest = {
        user: { id: userId },
      };

      mockService.updateHomepageSettings.mockRejectedValue(
        new Error("设置更新失败"),
      );

      // Act & Assert
      await expect(
        controller.updateHomepageSettings(mockRequest, updateDto),
      ).rejects.toThrow("设置更新失败");
      expect(mockService.updateHomepageSettings).toHaveBeenCalledWith(
        userId,
        updateDto,
      );
    });

    it("应该处理空的设置更新", async () => {
      // Arrange
      const userId = "test-user-id";
      const mockRequest = {
        user: { id: userId },
      };
      const emptyUpdateDto: UpdateHomepageSettingsDto = {};
      const mockResult = { message: "主页设置更新成功" };

      mockService.updateHomepageSettings.mockResolvedValue(mockResult);

      // Act
      const result = await controller.updateHomepageSettings(
        mockRequest,
        emptyUpdateDto,
      );

      // Assert
      expect(result.success).toBe(true);
      expect(mockService.updateHomepageSettings).toHaveBeenCalledWith(
        userId,
        emptyUpdateDto,
      );
    });
  });

  describe("getPersonalStats - 获取个人统计数据", () => {
    it("应该成功获取个人统计数据", async () => {
      // Arrange
      const userId = "test-user-id";
      mockService.getPersonalStats.mockResolvedValue(mockPersonalStats);

      // Act
      const result = await controller.getPersonalStats(userId);

      // Assert
      expect(result).toEqual({
        success: true,
        data: mockPersonalStats,
        message: "获取个人统计数据成功",
      });
      expect(mockService.getPersonalStats).toHaveBeenCalledWith(userId);
    });

    it("应该正确处理复杂的统计数据", async () => {
      // Arrange
      const userId = "test-user-id";
      const complexStats: PersonalStatsResponseDto = {
        ...mockPersonalStats,
        trendData: {
          monthlyCreation: [
            {
              month: "2025-01",
              storiesCount: 5,
              charactersCount: 2,
              timelineEventsCount: 3,
            },
            {
              month: "2024-12",
              storiesCount: 8,
              charactersCount: 4,
              timelineEventsCount: 6,
            },
            {
              month: "2024-11",
              storiesCount: 3,
              charactersCount: 1,
              timelineEventsCount: 2,
            },
          ],
          dailyInteraction: [
            {
              date: "2025-01-01",
              likesReceived: 25,
              viewsReceived: 200,
              commentsReceived: 12,
            },
          ],
        },
        interactionStats: {
          ...mockPersonalStats.interactionStats,
          averageLikesPerStory: 25.6,
          averageViewsPerStory: 156.8,
        },
        achievements: [
          {
            id: "popular-story",
            title: "热门创作者",
            description: "故事获得超过100个点赞",
            icon: "trending",
            achievedAt: new Date("2024-06-15"),
            category: "popularity",
          },
          {
            id: "character-master",
            title: "人物大师",
            description: "创建超过50个人物",
            icon: "character",
            achievedAt: new Date("2024-08-20"),
            category: "creation",
          },
        ],
      };

      mockService.getPersonalStats.mockResolvedValue(complexStats);

      // Act
      const result = await controller.getPersonalStats(userId);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toEqual(complexStats);
      expect(result.data.trendData.monthlyCreation).toHaveLength(3);
      expect(result.data.interactionStats.averageLikesPerStory).toBe(25.6);
      expect(result.data.achievements).toHaveLength(2);
    });

    it("应该处理服务层抛出的异常", async () => {
      // Arrange
      const userId = "non-existent-user";
      mockService.getPersonalStats.mockRejectedValue(new Error("用户不存在"));

      // Act & Assert
      await expect(controller.getPersonalStats(userId)).rejects.toThrow(
        "用户不存在",
      );
      expect(mockService.getPersonalStats).toHaveBeenCalledWith(userId);
    });

    it("应该处理统计数据为零的情况", async () => {
      // Arrange
      const userId = "test-user-id";
      const zeroStats: PersonalStatsResponseDto = {
        userId,
        basicStats: {
          storiesCount: 0,
          charactersCount: 0,
          lightedCharactersCount: 0,
          bookmarksCount: 0,
          timelineEventsCount: 0,
          referenceCollectionsCount: 0,
        },
        socialStats: {
          followersCount: 0,
          followingCount: 0,
          friendsCount: 0,
          mutualFollowsCount: 0,
        },
        interactionStats: {
          totalLikesReceived: 0,
          totalCommentsReceived: 0,
          totalViewsReceived: 0,
          totalSharesReceived: 0,
          averageLikesPerStory: 0,
          averageViewsPerStory: 0,
        },
        creationStats: {
          storiesThisMonth: 0,
          charactersThisMonth: 0,
          timelineEventsThisMonth: 0,
          mostActiveMonth: {
            month: "2025-01",
            storiesCount: 0,
          },
          longestStoryStreak: 0,
          currentStoryStreak: 0,
        },
        trendData: {
          monthlyCreation: [],
          dailyInteraction: [],
        },
        achievements: [],
        accountInfo: {
          memberSince: new Date("2025-01-01"),
          lastActiveAt: new Date("2025-01-01"),
          totalDaysActive: 0,
          averageDailyActivity: 0,
        },
      };

      mockService.getPersonalStats.mockResolvedValue(zeroStats);

      // Act
      const result = await controller.getPersonalStats(userId);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data.basicStats.storiesCount).toBe(0);
      expect(result.data.interactionStats.totalLikesReceived).toBe(0);
      expect(result.data.trendData.monthlyCreation).toHaveLength(0);
      expect(result.data.achievements).toHaveLength(0);
    });
  });

  describe("API响应格式验证", () => {
    it("所有成功响应应该符合标准格式", async () => {
      // Arrange
      const userId = "test-user-id";
      const mockRequest = { user: { id: userId } };
      const updateDto: UpdateDisplaySettingsDto = { showBirthday: true };

      mockService.getHomepageData.mockResolvedValue(mockHomepageData);
      mockService.updateDisplaySettings.mockResolvedValue({
        message: "更新成功",
      });
      mockService.updateHomepageSettings.mockResolvedValue({
        message: "更新成功",
      });
      mockService.getPersonalStats.mockResolvedValue(mockPersonalStats);

      // Act
      const homepageResult = await controller.getHomepageData(userId);
      const displayResult = await controller.updateDisplaySettings(
        mockRequest,
        updateDto,
      );
      const settingsResult = await controller.updateHomepageSettings(
        mockRequest,
        {},
      );
      const statsResult = await controller.getPersonalStats(userId);

      // Assert
      [homepageResult, displayResult, settingsResult, statsResult].forEach(
        (result) => {
          expect(result).toHaveProperty("success", true);
          expect(result).toHaveProperty("data");
          expect(result).toHaveProperty("message");
          expect(result).toHaveProperty("statusCode", HttpStatus.OK);
        },
      );
    });

    it("应该返回正确的HTTP状态码", async () => {
      // Arrange
      const userId = "test-user-id";
      mockService.getHomepageData.mockResolvedValue(mockHomepageData);
      mockService.getPersonalStats.mockResolvedValue(mockPersonalStats);

      // Act
      const homepageResult = await controller.getHomepageData(userId);
      const statsResult = await controller.getPersonalStats(userId);

      // Assert
      // 验证返回的数据结构
      expect(homepageResult.success).toBe(true);
      expect(statsResult.success).toBe(true);
    });
  });

  describe("边界条件和错误处理", () => {
    it("应该正确处理null和undefined参数", async () => {
      // Arrange
      mockService.getHomepageData.mockResolvedValue(mockHomepageData);

      // Act & Assert - 这些调用应该不会崩溃
      await expect(
        controller.getHomepageData("valid-id", null as any),
      ).resolves.toBeDefined();
      await expect(
        controller.getHomepageData("valid-id", undefined),
      ).resolves.toBeDefined();
    });

    it("应该处理异步操作异常", async () => {
      // Arrange
      const userId = "test-user-id";
      const asyncError = new Promise((_, reject) => {
        setTimeout(() => reject(new Error("异步操作失败")), 1);
      });

      mockService.getHomepageData.mockImplementation(() => asyncError as any);

      // Act & Assert
      await expect(controller.getHomepageData(userId)).rejects.toThrow(
        "异步操作失败",
      );
    });

    it("应该保持服务调用的正确顺序", async () => {
      // Arrange
      const userId = "test-user-id";
      const mockRequest = { user: { id: userId } };
      const callOrder: string[] = [];

      mockService.getHomepageData.mockImplementation(async () => {
        callOrder.push("getHomepageData");
        return mockHomepageData;
      });

      mockService.updateDisplaySettings.mockImplementation(async () => {
        callOrder.push("updateDisplaySettings");
        return { message: "更新成功" };
      });

      mockService.getPersonalStats.mockImplementation(async () => {
        callOrder.push("getPersonalStats");
        return mockPersonalStats;
      });

      // Act
      await Promise.all([
        controller.getHomepageData(userId),
        controller.updateDisplaySettings(mockRequest, { showBirthday: true }),
        controller.getPersonalStats(userId),
      ]);

      // Assert
      expect(callOrder).toContain("getHomepageData");
      expect(callOrder).toContain("updateDisplaySettings");
      expect(callOrder).toContain("getPersonalStats");
      expect(callOrder).toHaveLength(3);
    });
  });

  describe("性能和并发测试", () => {
    it("应该能处理并发请求", async () => {
      // Arrange
      const userIds = ["user-1", "user-2", "user-3"];
      mockService.getHomepageData.mockImplementation(async (userId) => ({
        ...mockHomepageData,
        userId,
      }));

      // Act
      const promises = userIds.map((userId) =>
        controller.getHomepageData(userId),
      );
      const results = await Promise.all(promises);

      // Assert
      expect(results).toHaveLength(3);
      results.forEach((result, index) => {
        expect(result.success).toBe(true);
        expect(result.data.userId).toBe(userIds[index]);
      });
    });

    it("应该正确处理大量数据", async () => {
      // Arrange
      const userId = "test-user-id";
      const largeDataStats = {
        ...mockPersonalStats,
        trendData: {
          monthlyCreation: Array.from({ length: 12 }, (_, i) => ({
            month: `2024-${String(i + 1).padStart(2, "0")}`,
            storiesCount: Math.floor(Math.random() * 100),
            charactersCount: Math.floor(Math.random() * 50),
            timelineEventsCount: Math.floor(Math.random() * 30),
          })),
          dailyInteraction: Array.from({ length: 30 }, (_, i) => ({
            date: `2025-01-${String(i + 1).padStart(2, "0")}`,
            likesReceived: Math.floor(Math.random() * 50),
            viewsReceived: Math.floor(Math.random() * 200),
            commentsReceived: Math.floor(Math.random() * 20),
          })),
        },
        achievements: Array.from({ length: 20 }, (_, i) => ({
          id: `achievement-${i}`,
          title: `成就 ${i}`,
          description: `描述 ${i}`,
          icon: "star",
          achievedAt: new Date(
            `2024-${String((i % 12) + 1).padStart(2, "0")}-01`,
          ),
          category: "test",
        })),
      };

      mockService.getPersonalStats.mockResolvedValue(largeDataStats);

      // Act
      const result = await controller.getPersonalStats(userId);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data.trendData.monthlyCreation).toHaveLength(12);
      expect(result.data.trendData.dailyInteraction).toHaveLength(30);
      expect(result.data.achievements).toHaveLength(20);
    });
  });
});
