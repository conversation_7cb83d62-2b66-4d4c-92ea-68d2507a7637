/**
 * AuthModule 单元测试
 * 测试认证模块的依赖注入和模块配置
 */

import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { JwtService } from "@nestjs/jwt";
import { getRepositoryToken } from "@nestjs/typeorm";
import { CACHE_MANAGER } from "@nestjs/cache-manager";
import { AuthModule } from "./auth.module";
import { AuthService } from "./auth.service";
import { AuthController } from "./auth.controller";
import { JwtAuthGuard } from "./guards/jwt-auth.guard";
import { SmsService } from "./sms.service";
import { User } from "../users/entities/user.entity";
import { RefreshToken } from "../users/entities/refresh-token.entity";

describe("AuthModule", () => {
  let module: TestingModule;

  const mockRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  };

  const mockCacheManager = {
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
    reset: jest.fn(),
  };

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [AuthModule],
    })
      .overrideProvider(getRepositoryToken(User))
      .useValue(mockRepository)
      .overrideProvider(getRepositoryToken(RefreshToken))
      .useValue(mockRepository)
      .overrideProvider(CACHE_MANAGER)
      .useValue(mockCacheManager)
      .compile();
  });

  afterEach(async () => {
    if (module) {
      await module.close();
    }
  });

  it("should be defined", () => {
    expect(module).toBeDefined();
  });

  describe("服务提供者", () => {
    it("should provide AuthService", () => {
      const authService = module.get<AuthService>(AuthService);
      expect(authService).toBeDefined();
      expect(authService).toBeInstanceOf(AuthService);
    });

    it("should provide SmsService", () => {
      const smsService = module.get<SmsService>(SmsService);
      expect(smsService).toBeDefined();
      expect(smsService).toBeInstanceOf(SmsService);
    });

    it("should provide JwtService", () => {
      const jwtService = module.get<JwtService>(JwtService);
      expect(jwtService).toBeDefined();
      expect(jwtService).toBeInstanceOf(JwtService);
    });

    it("should provide JwtAuthGuard", () => {
      const jwtAuthGuard = module.get<JwtAuthGuard>(JwtAuthGuard);
      expect(jwtAuthGuard).toBeDefined();
      expect(jwtAuthGuard).toBeInstanceOf(JwtAuthGuard);
    });
  });

  describe("控制器", () => {
    it("should provide AuthController", () => {
      const authController = module.get<AuthController>(AuthController);
      expect(authController).toBeDefined();
      expect(authController).toBeInstanceOf(AuthController);
    });
  });

  describe("依赖注入", () => {
    it("should inject User repository", () => {
      const userRepository = module.get(getRepositoryToken(User));
      expect(userRepository).toBeDefined();
      expect(userRepository).toBe(mockRepository);
    });

    it("should inject RefreshToken repository", () => {
      const refreshTokenRepository = module.get(
        getRepositoryToken(RefreshToken),
      );
      expect(refreshTokenRepository).toBeDefined();
      expect(refreshTokenRepository).toBe(mockRepository);
    });

    it("should inject CACHE_MANAGER", () => {
      const cacheManager = module.get(CACHE_MANAGER);
      expect(cacheManager).toBeDefined();
      expect(cacheManager).toBe(mockCacheManager);
    });
  });

  describe("模块配置", () => {
    it("should export AuthService for other modules", () => {
      const authService = module.get<AuthService>(AuthService);
      expect(authService).toBeDefined();
    });

    it("should export JwtAuthGuard for other modules", () => {
      const jwtAuthGuard = module.get<JwtAuthGuard>(JwtAuthGuard);
      expect(jwtAuthGuard).toBeDefined();
    });
  });
});
