# 有故事 - 产品功能清单 V1.0.0

## 📋 文档信息

**文档版本**: V1.0.0  
**创建时间**: 2025-07-22  
**更新时间**: 2025-07-23  
**维护团队**: YGS Development Team  
**文档类型**: 产品功能规格说明（纯功能描述）  
**文档用途**: 产品功能定义与业务规则说明

---

## 🎯 产品概览

### 核心价值主张
基于真实社交关系的故事分享平台，通过**人物点亮系统**验证身份，建立可信的内容生态。

### 关键指标
- **🎯 功能模块**: 15个核心模块，完整的故事社交生态
- **🔐 权限体系**: 故事访问权限（6层）+ 个人主页展示控制（8项）
- **🤖 AI功能**: 普通用户每天5次限制，会员用户50次限制
- **👥 用户容量**: 7位有故事号支持800万用户（1000000开始）
- **🔑 安全机制**: 45分钟+14天Token轮换，企业级安全

### 🌟 产品核心特色

#### 🎭 人物点亮系统（独创核心）
基于真实社交关系的身份验证机制，通过手机号验证建立点亮状态，获得故事引用权限。

#### 🔒 企业级内容安全
AI预检 + 人工复审双重保障，3级违规处理，完整申诉机制。

#### 🔑 45分钟+14天Token轮换制度
极致用户体验与企业级安全的完美平衡，智能刷新机制。

#### 🎯 双权限体系
故事访问权限（6层）+ 个人主页展示控制（8项），分离管理不混淆概念。

#### 💝 情感保护机制
正面通知策略，不发送拒绝、取消等负面通知，保护用户情绪。

#### 🤖 AI智能功能（成本可控）
普通用户每天5次限制，会员用户50次限制，完整的配额管理体系。

---

## 🗂️ API设计规范

### RESTful API标准
```
基础路径: https://api.yougushi.com/v1
认证方式: Bearer Token (JWT)
响应格式: JSON
字符编码: UTF-8
```

### 统一响应格式
```json
{
  "success": boolean,
  "code": number,
  "message": string,
  "data": object | array | null,
  "timestamp": "ISO8601",
  "requestId": "uuid"
}
```

### HTTP状态码规范
- `200` - 成功
- `201` - 创建成功
- `400` - 请求参数错误
- `401` - 未认证
- `403` - 权限不足
- `404` - 资源不存在
- `409` - 资源冲突
- `429` - 请求过于频繁
- `500` - 服务器内部错误

### 分页查询标准
```json
{
  "page": 1,
  "limit": 20,
  "total": 100,
  "totalPages": 5,
  "hasNext": true,
  "hasPrev": false,
  "items": []
}
```

---

## 📊 功能模块总览

| 模块 | 业务功能 | 核心能力 | 业务价值 |
|------|----------|----------|----------|
| 用户认证与账户管理 | 注册登录、身份验证、Token管理 | 7位有故事号，45+14天Token轮换 | 用户身份管理与安全保障 |
| 人物点亮系统 | 身份验证、点亮确认、状态管理 | 基于真实关系的身份验证创新 | 核心创新功能，建立信任关系 |
| 故事管理 | 内容创作、权限控制、安全审核 | 6层权限控制，AI+人工双重审核 | 核心内容生态，内容安全保障 |
| 人物管理 | 人物档案、关系设定、状态管理 | 人物创建、编辑、关系锁定机制 | 社交关系基础，数据一致性 |
| AI智能功能 | 故事生成、配额管理、成本控制 | 智能内容辅助，分层配额策略 | 差异化竞争，成本可控 |
| 文件与图片管理 | 上传存储、权限控制、批量处理 | 阿里云OSS，CDN加速，权限验证 | 基础设施服务，用户体验 |
| 消息通知 | 正面通知、情绪保护、个性化设置 | 情绪友好，只推送正面消息 | 用户体验优化，情感关怀 |
| 健康检查与监控 | 系统监控、性能指标、告警机制 | 实时监控，性能分析，故障预警 | 系统稳定性，运维保障 |
| 故事引用系统 | 个人引用集、分类管理、失效处理 | 点亮用户专享，引用状态管理 | 个性化内容管理，点亮权益 |
| 内容互动 | 点赞评论、收藏分享、举报机制 | 多层级评论，社区互动，内容监管 | 用户参与互动，社区活跃 |
| 社交关系管理 | 关注好友、分组管理、权限传递 | 关注/粉丝关系，用户分组，权限继承 | 社交网络构建，关系管理 |
| 个人主页管理 | 信息展示、权限设置、统计数据 | 8项展示控制，隐私保护，数据统计 | 用户形象展示，隐私控制 |
| 时间线管理 | 人生时间轴、权限控制 | 生命历程记录，时间轴展示 | 生命历程记录，情感价值 |
| 权限管理 | 双权限体系、精细化控制 | 故事权限+展示权限分离管理 | 隐私安全保障，用户自主权 |
| 内容分类管理 | 主题标签、内容聚合、个性化推荐 | 故事主题分类，智能推荐算法 | 内容组织，发现优化 |

---

## 🏗️ 核心模块详细设计

## 1️⃣ 用户认证与账户管理模块

### 业务概述
负责用户注册、登录、身份验证、账户管理等核心功能，确保系统安全性和用户体验。

### 核心业务规则

#### 7位有故事号系统
- **号码范围**: 1000000-9999999（900万个可用号码）
- **分配策略**: 注册时顺序分配，跳过敏感数字组合
- **扩展机制**: 首批使用200001-999999，达到85%使用率时启用1000000开始
- **唯一性**: 全平台唯一，用户主要身份标识

#### Token安全策略
- **Access Token**: 45分钟有效期，用于API访问
- **Refresh Token**: 14天有效期，用于刷新访问令牌
- **自动刷新**: 过期前10分钟自动刷新
- **设备绑定**: 每个Token绑定设备指纹
- **并发限制**: 每用户最多3个活跃会话

### 业务功能清单

| 功能 | 方法 | 路径 | 功能描述 | 业务规则 |
|------|------|------|----------|----------|
| 手机号注册 | POST | `/auth/register` | 手机号+验证码注册，生成7位有故事号 | 验证码5分钟有效，每天最多5次发送 |
| 手机号登录 | POST | `/auth/login` | 主要登录方式，快速便捷 | 失败5次锁定15分钟，防暴力破解 |
| 邮箱密码设置 | POST | `/auth/setup-email` | 第二种登录方式，完善账户安全 | 邮箱全平台唯一，密码8-50位强度要求 |
| 邮箱密码登录 | POST | `/auth/login-email` | 邮箱+密码备选登录方式 | 支持双因子认证，企业级安全 |
| Token刷新 | POST | `/auth/refresh` | 45分钟+14天Token轮换制度 | 自动撤销旧Token，无缝用户体验 |
| Token验证 | POST | `/auth/verify` | 验证令牌有效性和用户状态 | 返回用户信息和过期时间 |
| 安全退出 | POST | `/auth/logout` | 安全退出，清理会话信息 | 支持单设备或全设备退出 |
| 发送验证码 | POST | `/auth/send-verification-code` | 发送手机验证码 | 每分钟最多1次，6位数字，防刷机制 |

### 关键业务逻辑

#### 手机号注册流程
1. **验证码校验**: 5分钟有效期，每天最多5次
2. **号码分配**: 顺序分配7位有故事号，跳过666、888等敏感数字
3. **账户创建**: 初始化用户档案，设置默认权限
4. **Token生成**: 双Token机制，立即可用
5. **统计初始化**: AI配额、权限设置初始化

---

## 2️⃣ 故事管理模块

### 业务概述
用户创作、发布、管理故事的核心功能模块，包含完整的内容安全机制和权限控制。

### 核心业务规则

#### 故事内容规范
- **标题长度**: 1-100字符
- **内容长度**: 10-10000字符
- **图片数量**: 最多9张，每张最大5MB
- **支持格式**: JPG、PNG、WebP
- **内容审核**: AI预检+人工复审双重保障

#### 权限控制体系（6层精细化控制）
```typescript
enum StoryPermissionLevel {
  CHARACTERS_ONLY = 'characters_only',  // 仅故事中人物可见（最高权限）
  PRIVATE = 'private',                   // 私密（仅自己可见）
  FRIENDS = 'friends',                   // 好友可见
  FOLLOWERS = 'followers',               // 关注者可见
  GROUP_VISIBLE = 'group_visible',       // 分组可见
  PUBLIC = 'public'                      // 公开（默认权限）
}
```

### 业务功能清单

| 功能 | 方法 | 路径 | 功能描述 | 业务规则 |
|------|------|------|----------|----------|
| 创建故事 | POST | `/stories` | 企业级内容安全审核，AI预检+人工复审 | 每用户每天最多10个故事 |
| 故事流 | GET | `/stories` | 推荐算法+权限过滤 | 支持关注/推荐/最新三种模式 |
| 个人故事 | GET | `/users/me/stories` | 草稿/已发布/已删除分类管理 | 支持状态筛选和分页 |
| 故事详情 | GET | `/stories/{id}` | 6层权限控制，人物优先权限 | 记录浏览统计，权限验证 |
| 更新故事 | PUT | `/stories/{id}` | 更新故事内容和权限设置 | 作者权限验证 |
| 删除故事 | DELETE | `/stories/{id}` | 软删除机制，不可恢复 | 自动取消所有点亮状态 |
| 发布故事 | PUT | `/stories/{id}/publish` | 发布草稿故事 | 内容安全检测 |
| 归档故事 | PUT | `/stories/{id}/archive` | 归档已发布故事 | 作者权限验证 |
| 上传封面 | POST | `/stories/{id}/cover` | 上传故事封面图片 | 图片格式和大小限制 |
| 验证内容 | POST | `/stories/validate-content` | AI内容安全检测 | 返回安全评分 |
| 点赞故事 | PUT | `/stories/{id}/like` | 点赞/取消点赞故事 | 权限验证 |
| 公开故事列表 | GET | `/stories/public` | 获取公开故事列表 | 推荐算法排序 |

---

## 3️⃣ 人物管理模块

### 业务概述
管理故事中的人物角色，支持人物创建、编辑、关系设定等功能。是点亮系统的基础模块。

### 核心业务规则

#### 人物信息规范
- **人物姓名**: 1-20字符，同一用户内唯一
- **关系设定**: 支持预设关系和自定义关系
- **头像管理**: 预设头像库+自定义上传
- **编辑权限**: 点亮前可编辑，点亮后关系锁定

#### 关系管理策略
- **预设关系**: 父母、配偶、子女、兄弟姐妹、朋友、同学、同事等
- **自定义关系**: 用户可自定义关系描述
- **关系锁定**: 人物被点亮后关系不可修改，保护已建立的社交关系
- **多故事支持**: 同一人物可出现在多个故事中，关系一致

#### 展示逻辑（双层展示系统）
**第一层 - 人物集列表展示**:
- **人物集**: 头像、名字、关系、是否高亮（已点亮显示特殊标识）
- **点亮集**: 点亮人物名字、故事用户名字、该人物出现在几个故事中

**第二层 - 人物详情展示**:
- **人物集详情**: 人物完整信息 + 该人物出现的故事列表
- **点亮集详情**: 人物完整信息 + 该人物在故事用户中出现的故事列表（卡片形式）

### 业务功能清单

| 功能 | 方法 | 路径 | 功能描述 | 业务规则 |
|------|------|------|----------|----------|
| 创建人物 | POST | `/characters` | 预设头像+关系设定，点亮前可编辑 | 姓名用户内唯一，最多100个人物 |
| 人物列表 | GET | `/characters` | 轻量级列表，支持状态筛选 | 支持点亮状态筛选 |
| 人物详情 | GET | `/characters/{id}` | 详细信息，按需加载 | 包含点亮历史和相关故事 |
| 更新人物 | PUT | `/characters/{id}` | 点亮前可修改，点亮后关系锁定 | 点亮后只能修改描述 |
| 删除人物 | DELETE | `/characters/{id}` | 仅未关联故事的人物可删除 | 检查故事关联关系 |
| 上传头像 | POST | `/characters/upload-avatar` | 自定义头像上传 | 最大2MB，支持JPG/PNG |
| 人物搜索 | GET | `/characters/search` | 搜索人物功能 | 多维度搜索 |
| 热门人物 | GET | `/characters/popular` | 获取热门人物 | 点亮次数排序 |
| 人物统计 | GET | `/characters/statistics` | 人物相关统计 | 点亮统计数据 |
| 验证人物名称 | POST | `/characters/validate-name` | 验证人物名称唯一性 | 用户内唯一检查 |

---

## 4️⃣ 人物点亮系统（核心创新功能）

### 业务概述
平台的核心创新功能，通过真实身份验证建立可信的社交关系，是整个产品的价值核心。

### 核心业务规则

#### 点亮机制设计
- **申请条件**: 仅故事中出现的人物可申请点亮
- **身份验证**: 通过手机号验证真实身份（与注册手机号一致）
- **确认机制**: 故事作者确认或拒绝申请
- **有效期**: 申请7天内有效，过期自动失效
- **权限获得**: 点亮后获得故事引用权限和特殊展示标识

#### 双重排他性约束
1. **人物排他性**: 每个人物只能被一个用户点亮（一旦建立不可被其他用户申请）
2. **作者排他性**: 每个用户对每个故事作者只能点亮一个人物（防止占用多个角色）

#### 故事级点亮申请
- **申请粒度**: 点亮申请必须指定具体故事ID（不是全局点亮）
- **灵活选择**: 同一人物在不同故事中需要分别申请点亮
- **用户自主**: 用户可以选择性地参与不同故事的点亮

#### 状态管理策略
- **多故事支持**: 同一人物可在多个故事中被点亮
- **状态计算**: 只要有任意故事中被点亮，人物集显示"已点亮"（高亮显示）
- **取消逻辑**: 只有所有相关故事都取消点亮，才完全取消状态
- **独立管理**: 点亮关系与人物管理独立，避免混淆

#### 已点亮人物的新故事通知机制
- **场景触发**: 已被点亮的人物出现在同一作者的新故事中
- **自动识别**: 系统检测已存在的点亮关系
- **通知格式**: "您点亮过的人物X，出现在作者A的新故事B中，是否确认点亮？"
- **快速确认**: 用户确认后直接建立点亮关系，跳过作者审批环节
- **保持选择**: 用户可以选择不点亮新故事中的人物

#### 点亮权益详解
1. **点亮集展示**: 在个人点亮集中展示该人物，显示特殊身份
2. **故事引用权限**: 可以引用被点亮的故事到个人引用集
3. **特殊访问权限**: 对设置为"仅人物可见"的故事获得永久访问权
4. **高亮显示**: 在前端界面中获得独特的视觉标识
5. **优先互动**: 评论显示特殊标识，互动权重更高

### 业务功能清单

| 功能 | 方法 | 路径 | 功能描述 | 业务规则 |
|------|------|------|----------|----------|
| 提交点亮申请 | POST | `/stories/{storyId}/characters/{characterId}/light-request` | 人物用户申请点亮故事中的人物 | 手机号验证，7天有效期 |
| 获取待处理申请 | GET | `/light-requests` | 故事用户查看收到的点亮申请 | 支持状态筛选和时间排序 |
| 获取我的申请记录 | GET | `/my-requests` | 获取我提交的申请记录 | 个人申请历史 |
| 查询点亮状态 | GET | `/characters/{characterId}/lighting-status` | 查询人物点亮状态 | 多故事状态聚合 |
| 检查申请资格 | GET | `/characters/{characterId}/can-apply` | 检查是否可以申请点亮 | 权限和重复检查 |
| 确认点亮申请 | PUT | `/light-requests/{requestId}/confirm` | 故事用户确认点亮，建立点亮状态 | 分布式锁防并发，状态一致性 |
| 拒绝点亮申请 | PUT | `/light-requests/{requestId}/reject` | 故事用户拒绝申请，无负面通知 | 情绪保护机制 |
| 获取用户点亮集 | GET | `/users/{userId}/lighted-characters` | 获取用户的所有点亮人物 | 统计和管理功能 |

---

## 5️⃣ AI智能功能模块

### 业务概述
AI辅助内容创作功能，包含严格的配额管理和成本控制机制。

### 核心业务规则

#### 配额管理策略
- **普通用户**: 每天5次AI生成限制
- **会员用户**: 每天50次限制（预留接口）
- **重置机制**: 每日零点自动重置配额
- **成本控制**: 完整的使用统计和成本分析

### 业务功能清单

| 功能 | 方法 | 路径 | 功能描述 | 配额管理 |
|------|------|------|----------|----------|
| 生成故事标题 | POST | `/ai/generate-title` | 基于关键词生成故事标题 | 普通用户每日5次 |
| 生成故事内容 | POST | `/ai/generate-content` | 基于提示生成故事内容 | 普通用户每日5次 |
| 查询配额状态 | GET | `/ai/quota-status` | 查询当前AI使用配额 | 实时查询 |
| 查询使用统计 | GET | `/ai/usage-statistics` | 查询AI使用历史统计 | 历史数据分析 |
| 重置配额 | POST | `/ai/reset-quota` | 管理员重置用户配额 | 管理员功能 |

---

## 6️⃣ 消息通知系统

### 业务概述
实现正面通知策略的消息推送系统，包含情绪保护机制。

### 核心业务规则

#### 情绪保护策略
- **正面通知**: 只发送确认、成功等正面消息
- **负面过滤**: 拒绝、取消等负面操作不发送通知
- **用户控制**: 用户可自定义通知偏好设置

### 业务功能清单

| 功能 | 方法 | 路径 | 功能描述 | 特性 |
|------|------|------|----------|------|
| 获取通知列表 | GET | `/notifications` | 获取用户通知列表 | 分页、过滤 |
| 获取未读数量 | GET | `/notifications/unread-count` | 获取未读通知数量 | 实时统计 |
| 标记已读 | PUT | `/notifications/{id}/read` | 标记特定通知为已读 | 单个操作 |
| 全部标记已读 | PUT | `/notifications/mark-all-read` | 批量标记所有通知已读 | 批量操作 |
| 删除通知 | DELETE | `/notifications/{id}` | 删除特定通知 | 单个删除 |
| 批量删除 | POST | `/notifications/batch-delete` | 批量删除通知 | 批量操作 |

---

## 7️⃣ 故事引用系统

### 业务概述
允许点亮用户引用自己参与的故事到个人引用集，实现内容的个性化管理。

### 核心业务规则
- **引用资格**: 仅故事中的点亮人物用户可引用
- **引用范围**: 只能引用自己被点亮的故事
- **独立管理**: 引用状态与原故事解耦
- **失效处理**: 原故事删除时不通知，用户自主清理

### 业务功能清单

| 功能 | 方法 | 路径 | 功能描述 | 业务规则 |
|------|------|------|----------|----------|
| 创建引用 | POST | `/stories/{id}/reference` | 仅点亮人物用户可引用 | 每故事每用户只能引用一次 |
| 引用集查询 | GET | `/users/{id}/references` | 智能状态检测，友好提示失效引用 | 支持分类筛选和状态过滤 |
| 更新引用 | PUT | `/story-references/{id}` | 标签排序管理 | 支持标签、分类、排序修改 |
| 删除引用 | DELETE | `/story-references/{id}` | 用户自主管理，可删除失效引用 | 支持批量删除失效引用 |

---

## 8️⃣ 内容互动系统

### 业务概述
完整的内容互动体系，包括评论、收藏、分享、举报等社区功能。

### 核心业务规则
- **评论系统**: 支持多级回复，实时通知
- **收藏管理**: 个人收藏夹，分类管理
- **分享机制**: 安全分享链接，访问控制
- **举报机制**: 多类型内容举报，完整处理流程

### 业务功能清单

#### 评论系统
| 功能 | 方法 | 路径 | 功能描述 |
|------|------|------|----------|
| 创建评论 | POST | `/comments` | 对故事或评论进行评论 |
| 获取评论 | GET | `/stories/{id}/comments` | 获取故事评论列表 |
| 更新评论 | PUT | `/comments/{id}` | 编辑自己的评论 |
| 删除评论 | DELETE | `/comments/{id}` | 删除自己的评论 |
| 点赞评论 | PUT | `/comments/{id}/like` | 点赞/取消点赞评论 |

#### 收藏系统
| 功能 | 方法 | 路径 | 功能描述 |
|------|------|------|----------|
| 收藏故事 | POST | `/bookmarks` | 收藏故事到个人收藏夹 |
| 获取收藏 | GET | `/bookmarks` | 获取个人收藏列表 |
| 收藏分类 | PUT | `/bookmarks/{id}/category` | 设置收藏分类 |
| 取消收藏 | DELETE | `/bookmarks/{id}` | 移除收藏 |

#### 分享系统
| 功能 | 方法 | 路径 | 功能描述 |
|------|------|------|----------|
| 生成分享链接 | POST | `/stories/{id}/share` | 生成安全分享链接 |
| 访问分享内容 | GET | `/share/{token}` | 通过分享链接访问内容 |
| 分享统计 | GET | `/stories/{id}/share-stats` | 获取分享数据统计 |

#### 举报系统
| 功能 | 方法 | 路径 | 功能描述 |
|------|------|------|----------|
| 提交举报 | POST | `/reports` | 举报不当内容 |
| 获取举报记录 | GET | `/reports/my` | 获取个人举报记录 |
| 举报处理结果 | GET | `/reports/{id}/result` | 查看举报处理结果 |

---

## 9️⃣ 社交关系管理系统

### 业务概述
构建完整的社交网络，包括关注、好友、分组等关系管理功能。

### 核心业务规则
- **关注机制**: 单向关注，构建关注/粉丝关系
- **好友系统**: 基于互相关注建立好友关系
- **分组管理**: 自定义用户分组，精细化权限控制
- **权限传递**: 社交关系影响故事访问权限

### 业务功能清单

| 功能 | 方法 | 路径 | 功能描述 | 业务规则 |
|------|------|------|----------|----------|
| 关注用户 | POST | `/follows` | 关注其他用户 | 不能关注自己 |
| 取消关注 | DELETE | `/follows/{userId}` | 取消关注用户 | 自动解除好友关系 |
| 获取关注列表 | GET | `/users/{id}/following` | 获取用户关注列表 | 支持分页 |
| 获取粉丝列表 | GET | `/users/{id}/followers` | 获取用户粉丝列表 | 支持分页 |
| 创建分组 | POST | `/groups` | 创建用户分组 | 最多创建20个分组 |
| 管理分组成员 | POST | `/groups/{id}/members` | 添加用户到分组 | 批量操作支持 |
| 更新分组 | PUT | `/groups/{id}` | 修改分组信息 | 名称、描述、颜色 |
| 删除分组 | DELETE | `/groups/{id}` | 删除用户分组 | 确认无故事使用该分组 |

---

## 🔟 个人主页管理系统

### 业务概述
用户个人形象展示和隐私控制中心。

### 核心业务规则

#### 8项展示控制（个人主页模块开关）
```typescript
interface ProfileDisplaySettings {
  showThemes: boolean;              // 主题模块展示开关
  showTimeline: boolean;            // 时间线模块展示开关
  showReferences: boolean;          // 引用模块展示开关
  showCharacters: boolean;          // 人物集模块展示开关
  showLightedCharacters: boolean;   // 点亮集模块展示开关
  showBirthday: boolean;            // 生日信息展示开关
  showBio: boolean;                 // 个人简介展示开关
  showStatistics: boolean;          // 统计信息展示开关
}
```

### 业务功能清单

| 功能 | 方法 | 路径 | 功能描述 |
|------|------|------|----------|
| 获取个人主页 | GET | `/users/{id}/profile` | 获取用户公开主页信息 |
| 更新展示设置 | PUT | `/users/display-settings` | 更新8项展示控制设置 |
| 获取统计信息 | GET | `/users/{id}/statistics` | 获取用户详细统计数据 |

---

## 1️⃣1️⃣ 时间线管理系统

### 业务概述
个人生命历程记录，支持权限控制的时间轴展示。

### 核心业务规则
- **时间轴数据**: 重要人生事件记录
- **权限控制**: 继承个人主页展示设置
- **自动聚合**: 与故事时间关联，自动生成时间线

### 业务功能清单

| 功能 | 方法 | 路径 | 功能描述 |
|------|------|------|----------|
| 创建时间线事件 | POST | `/timeline/events` | 创建个人时间线事件 |
| 获取时间线 | GET | `/users/{id}/timeline` | 获取用户时间线数据 |
| 更新时间线事件 | PUT | `/timeline/events/{id}` | 更新时间线事件信息 |
| 删除时间线事件 | DELETE | `/timeline/events/{id}` | 删除时间线事件 |
| 获取时间线统计 | GET | `/timeline/users/{id}/stats` | 获取时间线统计数据 |

---

## 1️⃣2️⃣ 权限管理系统

### 业务概述
双权限体系的统一管理，提供精细化权限控制。

### 核心业务规则

#### 双权限体系架构
- **故事访问权限（6层）**: characters_only → private → friends → followers → group_visible → public
- **个人主页展示控制（8项）**: 各功能模块的显示开关

### 业务功能清单

| 功能 | 方法 | 路径 | 功能描述 |
|------|------|------|----------|
| 权限检查 | GET | `/permissions/check` | 统一权限验证接口 |
| 批量权限更新 | PUT | `/permissions/bulk-update` | 批量更新权限设置 |
| 权限历史 | GET | `/permissions/history` | 权限变更历史记录 |
| 权限统计 | GET | `/permissions/stats` | 权限使用统计分析 |

---

## 1️⃣3️⃣ 用户管理模块

### 业务概述
用户资料管理、社交关系、统计数据等用户中心功能。

### 业务功能清单

| 功能 | 方法 | 路径 | 功能描述 |
|------|------|------|----------|
| 获取当前用户资料 | GET | `/users/profile` | 获取登录用户详细资料 |
| 获取指定用户资料 | GET | `/users/{id}/profile` | 获取其他用户公开资料 |
| 更新个人信息 | PUT | `/users/profile` | 更新个人基本信息 |
| 更新展示设置 | PUT | `/users/profile/display-settings` | 更新个人主页展示设置 |
| 获取用户统计数据 | GET | `/users/{id}/statistics` | 获取用户详细统计信息 |
| 搜索用户 | GET | `/users/search` | 按关键词搜索用户 |
| 根据有故事号查找 | GET | `/users/by-number/{userNumber}` | 通过7位有故事号查找用户 |
| 朋友管理 | POST/DELETE | `/users/{id}/friends` | 添加或移除朋友关系 |
| 获取朋友列表 | GET | `/users/friends` | 获取当前用户朋友列表 |
| 检查朋友关系 | GET | `/users/{id}/friends/status` | 检查与指定用户的朋友关系 |

---

## 1️⃣4️⃣ 文件与图片管理模块

### 业务概述
文件上传、图片处理、CDN加速、权限控制的完整文件管理系统。

### 核心业务规则
- **存储方案**: 阿里云OSS对象存储
- **CDN加速**: 全球加速节点
- **权限控制**: 基于用户身份和内容权限的访问控制
- **格式支持**: JPG、PNG、WebP、GIF等主流格式

### 业务功能清单

| 功能 | 方法 | 路径 | 功能描述 |
|------|------|------|----------|
| 上传单个文件 | POST | `/upload/single` | 上传图片或文件到OSS |
| 批量获取图片URL | GET | `/images/batch-urls` | 批量获取图片临时访问链接 |

---

## 1️⃣5️⃣ 系统监控与健康检查

### 业务概述
系统健康状态监控、性能指标收集、故障预警机制。

### 业务功能清单

| 功能 | 方法 | 路径 | 功能描述 |
|------|------|------|----------|
| 基础健康检查 | GET | `/health` | 系统基础状态检查 |
| 详细健康检查 | GET | `/health/detailed` | 详细的系统诊断信息 |

---

## 📈 产品功能特性总结

### 🎯 核心创新点
1. **人物点亮系统**: 基于真实身份验证的独创社交机制
2. **双权限体系**: 故事访问权限与个人主页展示权限分离管理
3. **情感保护机制**: 正面通知策略，保护用户情绪体验
4. **7位有故事号**: 独特的用户身份标识系统

### 🔒 安全与隐私
- **企业级Token轮换**: 45分钟+14天双Token安全机制
- **内容安全双重保障**: AI预检+人工复审
- **精细化权限控制**: 6层故事访问权限
- **隐私保护**: 8项个人主页展示控制

### 🤖 智能化功能
- **AI内容辅助**: 故事标题和内容智能生成
- **成本可控配额**: 分层用户配额管理策略
- **智能推荐**: 基于用户行为的个性化内容推荐

### 👥 社交生态
- **完整的社交关系**: 关注、好友、分组管理
- **丰富的互动功能**: 评论、点赞、收藏、分享
- **故事引用系统**: 点亮用户专享的内容管理功能
- **时间线记录**: 个人生命历程的时间轴展示

---

**文档维护**: 产品设计团队  
**最后更新**: 2025-07-23  
**版本**: V1.0.0  
**文档性质**: 产品功能规格说明书