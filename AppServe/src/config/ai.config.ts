import { registerAs } from "@nestjs/config";
import {
  getStringEnv,
  parseIntEnv,
  parseFloatEnv,
} from "../common/utils/env.utils";

export const aiConfig = registerAs("ai", () => ({
  openaiApiKey: getStringEnv(process.env.OPENAI_API_KEY, ""),
  openaiBaseUrl: getStringEnv(
    process.env.OPENAI_BASE_URL,
    "https://api.openai.com/v1",
  ),
  openaiModel: getStringEnv(process.env.OPENAI_MODEL, "gpt-3.5-turbo"),
  openaiMaxTokens: parseIntEnv(process.env.OPENAI_MAX_TOKENS, 1000),
  openaiTemperature: parseFloatEnv(process.env.OPENAI_TEMPERATURE, 0.7),
  requestTimeout: parseIntEnv(process.env.AI_REQUEST_TIMEOUT, 30000),
  maxRequestsPerUser: parseIntEnv(process.env.AI_MAX_REQUESTS_PER_USER, 50),
  maxRequestsWindow: parseIntEnv(process.env.AI_MAX_REQUESTS_WINDOW, 3600), // 1小时
}));
