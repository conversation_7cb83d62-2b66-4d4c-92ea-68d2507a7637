/**
 * 举报模块 - 企业级单元测试
 */

import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { ReportsModule } from "./reports.module";
import { ReportsService } from "./reports.service";
import { ReportsController } from "./reports.controller";

describe("ReportsModule - 企业级模块测试", () => {
  let module: TestingModule;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [ReportsModule],
    })
      .overrideProvider(ReportsService)
      .useValue({
        findAll: jest.fn(),
        findOne: jest.fn(),
        create: jest.fn(),
        update: jest.fn(),
        remove: jest.fn(),
      })
      .compile();
  });

  afterEach(async () => {
    if (module) {
      await module.close();
    }
  });

  describe("模块初始化", () => {
    it("should compile the module", () => {
      expect(module).toBeDefined();
    });

    it("should provide ReportsService", () => {
      const service = module.get<ReportsService>(ReportsService);
      expect(service).toBeDefined();
    });

    it("should provide ReportsController", () => {
      const controller = module.get<ReportsController>(ReportsController);
      expect(controller).toBeDefined();
    });
  });

  describe("依赖注入", () => {
    it("should inject ReportsService into ReportsController", () => {
      const controller = module.get<ReportsController>(ReportsController);
      const service = module.get<ReportsService>(ReportsService);

      expect(controller).toBeDefined();
      expect(service).toBeDefined();
    });
  });

  describe("模块配置", () => {
    it("should be a valid NestJS module", () => {
      expect(ReportsModule).toBeDefined();
      expect(typeof ReportsModule).toBe("function");
    });
  });
});
