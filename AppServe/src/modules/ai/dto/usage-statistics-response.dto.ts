import { ApiProperty } from "@nestjs/swagger";

/**
 * 使用统计响应DTO
 * 用于返回用户AI功能使用统计数据
 */
export class UsageStatisticsResponseDto {
  @ApiProperty({
    description: "统计周期（天数）",
    example: 30,
  })
  period: number;

  @ApiProperty({
    description: "总使用次数",
    example: 25,
  })
  totalUsage: number;

  @ApiProperty({
    description: "成功使用次数",
    example: 23,
  })
  successfulUsage: number;

  @ApiProperty({
    description: "失败率（百分比）",
    example: 8.0,
  })
  failureRate: number;

  @ApiProperty({
    description: "总成本（元）",
    example: 0.25,
  })
  totalCost: number;

  @ApiProperty({
    description: "平均每次请求成本（元）",
    example: 0.01,
  })
  averageCostPerRequest: number;

  @ApiProperty({
    description: "按功能类型统计使用次数",
    example: {
      title_generation: 15,
      content_generation: 10,
      content_optimization: 0,
    },
  })
  usageByFeature: Record<string, number>;
}
