import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import type {
  CacheModuleOptions,
  CacheOptionsFactory,
} from "@nestjs/cache-manager";
import { redisStore } from "cache-manager-redis-yet";

/**
 * 官方缓存配置服务
 * 使用 @nestjs/cache-manager + cache-manager-redis-yet
 * 替代 @liaoliaots/nestjs-redis，完全兼容 NestJS 11
 */
@Injectable()
export class CacheConfigService implements CacheOptionsFactory {
  constructor(private configService: ConfigService) {}

  async createCacheOptions(): Promise<CacheModuleOptions> {
    const nodeEnv = this.configService.get("NODE_ENV");
    const redisDisabled = this.configService.get("REDIS_DISABLED") === "true";
    const cacheType = this.configService.get("CACHE_TYPE", "redis");

    // 测试环境或Redis被禁用时使用内存缓存
    if (nodeEnv === "test" || redisDisabled || cacheType === "memory") {
      console.log(`🔧 初始化内存缓存系统 (环境: ${nodeEnv})`);
      return {
        isGlobal: true,
        ttl: this.configService.get("CACHE_TTL", 300) * 1000, // 默认5分钟
      };
    }

    const redisHost = this.configService.get("REDIS_HOST", "localhost");
    const redisPort = this.configService.get("REDIS_PORT", 6379);
    const redisPassword = this.configService.get("REDIS_PASSWORD");
    const redisDb = this.configService.get("REDIS_DB", 0);

    // 强制使用IPv4地址
    const actualHost = redisHost === "localhost" ? "127.0.0.1" : redisHost;

    console.log(`🔧 初始化Redis缓存系统: ${actualHost}:${redisPort}`);

    try {
      return {
        store: await redisStore({
          socket: {
            host: actualHost,
            port: redisPort,
            family: 4, // 强制IPv4
          },
          password: redisPassword,
          database: redisDb,
        }),
        isGlobal: true,
        ttl: 3600 * 1000, // 1小时 TTL(毫秒)
      };
    } catch (error) {
      console.error(
        "Redis连接失败，回退到内存缓存:",
        error instanceof Error ? error.message : String(error),
      );
      return {
        isGlobal: true,
        ttl: this.configService.get("CACHE_TTL", 300) * 1000, // 默认5分钟
      };
    }
  }
}
