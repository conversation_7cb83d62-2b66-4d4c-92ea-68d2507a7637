/**
 * 企业级集成测试全局设置
 *
 * 职责:
 * 1. 测试数据库初始化和清理
 * 2. Redis测试环境配置
 * 3. 测试数据种子创建
 * 4. 企业级测试环境隔离
 */

import * as dotenv from "dotenv";

export default async function setup(): Promise<void> {
  console.log("🚀 企业级集成测试环境初始化开始...");

  // 1. 加载测试环境变量
  dotenv.config({ path: ".env.integration.test" });

  // 2. 确保使用测试数据库
  const testDbName = process.env.DB_NAME || "story_app_integration_test";
  if (!testDbName.includes("test")) {
    throw new Error("❌ 安全检查失败: 必须使用测试数据库");
  }

  console.log(`📊 使用测试数据库: ${testDbName}`);

  try {
    // 3. 检查并准备测试数据库
    console.log("🗄️ 准备测试数据库...");
    // 由于使用阿里云RDS，跳过createdb命令，确保使用正确的测试数据库名
    console.log(`📊 使用阿里云RDS测试数据库: ${testDbName}`);

    // 4. 设置环境变量供测试使用
    process.env.NODE_ENV = "integration-test";
    process.env.DB_NAME = testDbName;
    process.env.REDIS_PREFIX = "integration-test:";

    console.log("✅ 企业级集成测试环境初始化完成");
    console.log("🎯 测试环境特性: 真实数据库，完整业务流程验证");
  } catch (error) {
    console.error("❌ 集成测试环境初始化失败:", (error as Error).message);
    throw error;
  }
}
