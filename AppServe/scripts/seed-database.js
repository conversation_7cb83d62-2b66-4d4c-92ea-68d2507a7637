#!/usr/bin/env node

/**
 * 企业级数据播种脚本
 * 
 * 用途: 为前后端联调生成完整测试数据 (增强版)
 * 包含: 10个用户、9个故事、15个人物、13个点亮申请、27个社交关系
 * 使用: npm run seed:all | npm run seed:clean | npm run seed:stats
 */

const { NestFactory } = require('@nestjs/core');

async function main() {
  const command = process.argv[2] || 'all';
  
  console.log('🚀 启动企业级数据播种脚本...');
  console.log(`📋 执行命令: ${command}`);
  
  try {
    // 动态导入编译后的模块
    const { AppModule } = await import('../dist/app.module.js');
    const { EnhancedDatabaseSeederService } = await import('../dist/database/enhanced-database-seeder.service.js');

    // 创建应用实例
    const app = await NestFactory.createApplicationContext(AppModule, {
      logger: ['error', 'warn', 'log'],
    });

    // 获取数据播种服务
    const seederService = app.get(EnhancedDatabaseSeederService);

    switch (command) {
      case 'all':
        console.log('📦 开始生成完整测试数据...');
        const result = await seederService.seedAll({
          clearExistingData: true,
          environment: 'development',
        });
        
        console.log('\n🎉 数据生成完成！');
        console.log('已成功生成所有业务数据，包括用户、故事、人物、点亮关系、评论、收藏、时间线、分享、通知等。');
        
        console.log('\n🔑 测试账号 (手机号/密码):');
        console.log('   📝 创作者张明远: 13800138001 / Test123456!');
        console.log('   💡 点亮者李小红: 13800138002 / Test123456!');
        console.log('   👨‍🏫 教师王建国: 13800138003 / Test123456!');
        console.log('   📚 读者赵小芳: 13800138004 / Test123456!');
        console.log('   👑 VIP陈总: 16675158665 / Test123456!');
        console.log('   🔧 系统测试: 13800138009 / Test123456!');
        break;

      case 'clean':
        console.log('🧹 清理所有测试数据...');
        await seederService.clearAll();
        console.log('✅ 数据清理完成');
        break;

      case 'stats':
        console.log('📊 获取数据统计...');
        const currentStats = await seederService.getStats();
        console.log('📈 当前数据统计:');
        Object.entries(currentStats).forEach(([key, value]) => {
          console.log(`   ${key}: ${value} 个`);
        });
        break;

      case 'validate':
        console.log('🔍 验证数据完整性...');
        const isValid = await seederService.validateData();
        if (isValid) {
          console.log('✅ 数据完整性验证通过');
          process.exit(0);
        } else {
          console.log('❌ 数据完整性验证失败');
          process.exit(1);
        }
        break;

      case 'users':
        console.log('👥 仅生成用户数据...');
        await seederService.seedUsers(null);
        console.log('✅ 用户数据生成完成');
        break;

      default:
        console.log('❌ 未知命令');
        console.log('📋 可用命令:');
        console.log('   all      - 生成完整测试数据');
        console.log('   clean    - 清理所有数据');
        console.log('   stats    - 显示数据统计');
        console.log('   validate - 验证数据完整性');
        console.log('   users    - 仅生成用户数据');
        process.exit(1);
    }

    await app.close();
    console.log('\n🏁 脚本执行完成');
    
  } catch (error) {
    console.error('❌ 脚本执行失败:', error.message);
    if (error.stack) {
      console.error(error.stack);
    }
    process.exit(1);
  }
}

// 处理未捕获的异常
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 未处理的Promise拒绝:', reason);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('❌ 未捕获的异常:', error);
  process.exit(1);
});

// 启动脚本
main();