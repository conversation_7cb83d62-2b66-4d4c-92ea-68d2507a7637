import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../design_system/tokens/ygs_colors.dart';
import '../../../../design_system/tokens/ygs_typography.dart';

/// 创建新主题卡片 - 与主题卡片相同尺寸的创建入口
/// 设计风格：简洁现代，突出创建功能，符合YGS设计系统
class CreateNewThemeCard extends StatefulWidget {
  final VoidCallback? onTap;

  const CreateNewThemeCard({
    super.key,
    this.onTap,
  });

  @override
  State<CreateNewThemeCard> createState() => _CreateNewThemeCardState();
}

class _CreateNewThemeCardState extends State<CreateNewThemeCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    setState(() {
      _isPressed = true;
    });
    _animationController.forward();
  }

  void _handleTapUp(TapUpDetails details) {
    setState(() {
      _isPressed = false;
    });
    _animationController.reverse();
  }

  void _handleTapCancel() {
    setState(() {
      _isPressed = false;
    });
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      onTapDown: _handleTapDown,
      onTapUp: _handleTapUp,
      onTapCancel: _handleTapCancel,
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              width: 345.w,
              height: 195.h, // 按Figma比例调整：390/2 = 195
              decoration: BoxDecoration(
                // 基础白色背景 #FFFFFF
                color: const Color(0xFFFFFFFF),
                borderRadius: BorderRadius.circular(16.r),
              ),
              clipBehavior: Clip.hardEdge,
              child: Stack(
                children: [
                  // Ellipse 501 - 右上角装饰圆 (按比例缩放)
                  Positioned(
                    top: -53.h, // -106/2 = -53
                    right: -35.w, // 560-690 = -130, -130/2 = -65, 调整到-35保持视觉效果
                    child: Container(
                      width: 115.w, // 231/2 = 115.5 ≈ 115
                      height: 115.w,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: const Color(0xFFFFA6A6), // 精确的Figma颜色
                      ),
                      child: BackdropFilter(
                        filter: ImageFilter.blur(sigmaX: 50.0, sigmaY: 50.0), // 100/2 = 50
                        child: Container(
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: const Color(0xFFFFA6A6).withValues(alpha: 0.8),
                          ),
                        ),
                      ),
                    ),
                  ),

                  // Ellipse 500 - 左下角装饰圆 (按比例缩放)
                  Positioned(
                    bottom: -31.h, // 390-252-190 = -52, -52/2 = -26, 调整到-31
                    left: -36.w, // -73/2 = -36.5 ≈ -36
                    child: Container(
                      width: 95.w, // 190/2 = 95
                      height: 95.w,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: const Color(0xFFFEE7E7), // 精确的Figma颜色
                      ),
                      child: BackdropFilter(
                        filter: ImageFilter.blur(sigmaX: 37.5, sigmaY: 37.5), // 75/2 = 37.5
                        child: Container(
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: const Color(0xFFFEE7E7).withValues(alpha: 0.8),
                          ),
                        ),
                      ),
                    ),
                  ),

                  // 虚线边框层
                  Positioned.fill(
                    child: CustomPaint(
                      painter: DashedBorderPainter(
                        color: YgsColors.borderPrimary,
                        strokeWidth: 2.w,
                        dashLength: 8.w,
                        dashSpace: 6.w,
                        borderRadius: 16.r,
                      ),
                    ),
                  ),

                  // 主要内容 - 按Figma精确定位
                  // icon-add-02 - 按Figma设计稿精确实现
                  Positioned(
                    left: 149.w, // 298/2 = 149
                    top: 36.h, // 72/2 = 36
                    child: Container(
                      width: 47.w, // 94/2 = 47
                      height: 47.w,
                      decoration: BoxDecoration(
                        color: Colors.transparent,
                        borderRadius: BorderRadius.circular(8.r), // 16/2 = 8
                        border: Border.all(
                          color: const Color(0xFF151523),
                          width: 2.w, // 4/2 = 2
                        ),
                      ),
                      child: Stack(
                        children: [
                          // Vector 456 - 垂直线
                          Positioned(
                            left: 21.5.w, // 居中：47/2 - 2/2 = 22.5，调整到21.5
                            top: 9.5.h, // (47-20)/2 = 13.5，调整到9.5
                            child: Container(
                              width: 2.w, // 4/2 = 2
                              height: 20.h, // 40/2 = 20
                              color: const Color(0xFF151523),
                            ),
                          ),
                          // Vector 457 - 水平线
                          Positioned(
                            left: 13.5.w, // (47-20)/2 = 13.5
                            top: 21.5.h, // 居中：47/2 - 2/2 = 22.5，调整到21.5
                            child: Container(
                              width: 20.w, // 40/2 = 20
                              height: 2.h, // 4/2 = 2
                              color: const Color(0xFF151523),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // 主标题 "创建新主题" - 按Figma精确定位
                  Positioned(
                    left: 97.5.w, // (345-150)/2 = 97.5，居中对齐
                    top: 93.h, // 186/2 = 93
                    child: Container(
                      width: 150.w, // 按Figma: 150px
                      height: 42.h, // 按Figma: 42px
                      child: Text(
                        '创建新主题',
                        style: TextStyle(
                          fontFamily: 'PingFang SC',
                          fontSize: 30.sp, // 按Figma: 30px
                          fontWeight: FontWeight.w500, // 按Figma: 500
                          color: const Color(0xFF151523), // 字体颜色01
                          height: 42.h / 30.sp, // line-height: 42px
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),

                  // 副标题 - 按Figma精确定位
                  Positioned(
                    left: 52.5.w, // (345-240)/2 = 52.5，居中对齐
                    top: 131.5.h, // 263/2 = 131.5
                    child: Container(
                      width: 240.w, // 按Figma: 240px
                      height: 56.h, // 按Figma: 56px
                      child: Text(
                        '点击开始创建新的故事主题\n记录属于你的精彩回忆',
                        style: TextStyle(
                          fontFamily: 'PingFang SC',
                          fontSize: 20.sp, // 按Figma: 20px
                          fontWeight: FontWeight.w400, // 按Figma: 400
                          color: const Color(0xFF868691), // 字体颜色03
                          height: 28.h / 20.sp, // line-height: 28px
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ],
              ),
            ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

/// 虚线边框绘制器
class DashedBorderPainter extends CustomPainter {
  final Color color;
  final double strokeWidth;
  final double dashLength;
  final double dashSpace;
  final double borderRadius;

  DashedBorderPainter({
    required this.color,
    required this.strokeWidth,
    required this.dashLength,
    required this.dashSpace,
    required this.borderRadius,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    final path = Path()
      ..addRRect(RRect.fromRectAndRadius(
        Rect.fromLTWH(strokeWidth / 2, strokeWidth / 2,
                     size.width - strokeWidth, size.height - strokeWidth),
        Radius.circular(borderRadius),
      ));

    _drawDashedPath(canvas, path, paint);
  }

  void _drawDashedPath(Canvas canvas, Path path, Paint paint) {
    final pathMetrics = path.computeMetrics();
    for (final pathMetric in pathMetrics) {
      double distance = 0.0;
      while (distance < pathMetric.length) {
        final nextDistance = distance + dashLength;
        final extractPath = pathMetric.extractPath(
          distance,
          nextDistance > pathMetric.length ? pathMetric.length : nextDistance,
        );
        canvas.drawPath(extractPath, paint);
        distance = nextDistance + dashSpace;
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

