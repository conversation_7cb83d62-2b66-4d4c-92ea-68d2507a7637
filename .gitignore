# ===========================================
# YGS 项目 Git 忽略配置
# ===========================================

# Node.js 依赖
**/node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
# package-lock.json  # 注释掉，package-lock.json 应该被提交
yarn.lock
.pnpm-debug.log*

# React Native 相关
# Metro
.metro-health-check*
.metro-runtime-cache
.metro-session-cache
.metro-symlinks

# Build outputs
**/android/app/build/
**/ios/build/
**/ios/DerivedData/
**/ios/Pods/
**/android/app/src/main/assets/index.android.bundle
**/android/app/src/main/assets/index.android.bundle.map
**/ios/main.jsbundle
**/ios/main.jsbundle.map

# Android specific
**/android/.gradle/
**/android/gradle.properties.lock
**/android/local.properties
**/android/.cxx/
**/android/app/release/
**/android/app/debug/
**/*.apk
**/*.aab
**/*.keystore

# iOS specific
**/ios/*.xcworkspace/xcuserdata/
**/ios/*.xcodeproj/xcuserdata/
**/ios/*.xcodeproj/project.xcworkspace/xcuserdata/
**/*.ipa
**/*.app
**/*.dSYM

# CMake 文件
CMakeFiles/
CMakeCache.txt
CMakeOutput.log
CMakeError.log
cmake_install.cmake
Makefile
*.cmake

# 系统文件
**/.DS_Store
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 编辑器和IDE
.vscode/
.idea/
*.swp
*.swo
*~
.project
.classpath
.settings/

# ===== 企业级环境变量安全策略 =====

# 🔴 绝对禁止提交 - 包含真实密钥的环境文件
.env
.env.local
.env.development
.env.development.local
.env.production
.env.production.local

# 🟡 测试环境文件 - 已脱敏但仍建议忽略
.env.test
.env.test.local
.env.integration.test
.env.e2e.test

# 🟢 允许提交 - 配置模板文件
# .env.example

# ===== AppServe 后端环境文件 =====
AppServe/.env
AppServe/.env.local
AppServe/.env.development
AppServe/.env.production
AppServe/.env.test
AppServe/.env.integration.test
AppServe/.env.e2e.test

# ===== AppMobile Flutter 环境文件 =====
AppMobile/.env
AppMobile/.env.local
AppMobile/.env.development
AppMobile/.env.production

# 日志文件
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 临时文件
*.tmp
*.temp
*.bak
*.orig
*.cache
*.pid
*.seed
*.pid.lock

# 运行时文件
.nyc_output
coverage/
.sass-cache/

# Flipper
**/.flipper/

# Gradle Wrapper
gradle-wrapper.properties

# Jest
coverage/

# ESLint
.eslintcache

# TypeScript
*.tsbuildinfo

# Fastlane
**/fastlane/report.xml
**/fastlane/Preview.html
**/fastlane/screenshots
**/fastlane/test_output

# Hermes
**/*-debug.aar
**/*-release.aar
**/*-debug.tar.gz
**/*-release.tar.gz

# Bundle artifact
*.jsbundle

# CocoaPods
**/ios/Pods/

# Xcode
**/ios/build/
**/ios/DerivedData/
**/ios/*.xcworkspace
**/ios/*.xcodeproj
!**/ios/*.xcodeproj/project.pbxproj

# Auto linking
**/react-native.config.js
**/autolinked_build/

# Flipper
**/ios/Pods/Flipper*
**/android/app/src/debug/java/com/*/ReactNativeFlipper.java

# Watchman
.watchmanconfig

# Buck
buck-out/
\.buckd/
*.keystore
!debug.keystore

# Additional Node.js
.npm
.nyc_output
lib-cov
coverage
.grunt
.lock-wscript
build/Release
node_modules/
jspm_packages/
typings/
.npm
.eslintcache
.node_repl_history
*.tgz
.yarn-integrity
.env
.env.test
.cache
.parcel-cache
.next
out
.nuxt
dist
.cache/
.vuepress/dist
.serverless
.fusebox/
.dynamodb/
.tern-port
.vscode-test
.DS_Store
