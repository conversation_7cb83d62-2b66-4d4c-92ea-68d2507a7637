import { registerAs } from "@nestjs/config";
import { parseIntEnv } from "../common/utils/env.utils";

export const ossConfig = registerAs("oss", () => ({
  region: process.env.OSS_REGION || "oss-cn-hangzhou",
  accessKeyId: process.env.OSS_ACCESS_KEY_ID,
  accessKeySecret: process.env.OSS_ACCESS_KEY_SECRET,
  bucket: process.env.OSS_BUCKET || "ygs-oss-dev",
  endpoint: process.env.OSS_ENDPOINT || "https://oss-cn-hangzhou.aliyuncs.com",

  // 安全方案：私有Bucket + CDN配置
  privateBucket: process.env.OSS_PRIVATE_BUCKET || process.env.OSS_BUCKET,
  cdnDomain: process.env.OSS_CDN_DOMAIN, // CDN加速域名
  domain:
    process.env.OSS_CDN_DOMAIN ||
    `https://${process.env.OSS_BUCKET || "ygs-oss-dev"}.oss-cn-hangzhou.aliyuncs.com`,

  // 签名URL配置
  signedUrlExpires: parseIntEnv(process.env.OSS_SIGNED_URL_EXPIRES, 1800), // 30分钟

  baseDir: process.env.OSS_BASE_DIR || "ygs-app/",
  // 文件大小限制（10MB）
  maxFileSize: parseIntEnv(process.env.MAX_FILE_SIZE, 10 * 1024 * 1024),
  // 允许的文件类型
  allowedMimeTypes: [
    "image/jpeg",
    "image/png",
    "image/gif",
    "image/webp",
    "image/svg+xml",
    "video/mp4",
    "video/quicktime",
    "video/x-msvideo",
  ],
  // 文件夹结构
  folders: {
    avatar: "avatars/", // 用户头像
    story: "stories/", // 故事封面
    character: "characters/", // 人物头像
    storyContent: "story-content/", // 故事内容图片
    temp: "temp/", // 临时文件
  },
}));
