/**
 * 企业级集成测试通用设置
 *
 * 提供:
 * 1. 测试工具函数
 * 2. 通用断言辅助
 * 3. 性能基准验证
 * 4. 企业级测试标准
 */

import type { INestApplication } from "@nestjs/common";
import { ValidationPipe } from "@nestjs/common";
import { HttpExceptionFilter } from "@/common/filters/http-exception.filter";
import { TransformInterceptor } from "@/common/interceptors/transform.interceptor";

/**
 * 企业级测试应用配置
 * 与生产环境保持一致的配置
 */
export async function configureTestApp(
  app: INestApplication,
): Promise<INestApplication> {
  // 1. 全局验证管道
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }),
  );

  // 2. 全局异常过滤器
  app.useGlobalFilters(new HttpExceptionFilter());

  // 3. 全局响应转换拦截器
  app.useGlobalInterceptors(new TransformInterceptor());

  // 4. 启动应用
  await app.init();

  return app;
}

/**
 * 企业级性能基准验证
 */
export class PerformanceBenchmark {
  /**
   * 验证API响应时间
   * 企业级标准: 95%请求 < 500ms
   */
  static expectResponseTime(duration: number, maxMs: number = 500): void {
    expect(duration).toBeLessThan(maxMs);
  }

  /**
   * 验证数据库查询性能
   * 企业级标准: 简单查询 < 50ms，复杂查询 < 200ms
   */
  static expectDatabaseQueryTime(
    duration: number,
    complexity: "simple" | "complex" = "simple",
  ): void {
    const maxMs = complexity === "simple" ? 50 : 200;
    expect(duration).toBeLessThan(maxMs);
  }
}

/**
 * 企业级业务断言工具
 */
export class BusinessAssertions {
  /**
   * 验证用户认证状态
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  static expectValidUserAuth(user: any): void {
    expect(user).toBeDefined();
    expect(user.id).toBeDefined();
    expect(user.userNumber).toMatch(/^\d{6}$/);
    expect(user.phone).toMatch(/^1[3-9]\d{9}$/);
  }

  /**
   * 验证点亮申请状态
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  static expectValidLightingRequest(request: any): void {
    expect(request).toBeDefined();
    expect(request.id).toBeDefined();
    expect(request.status).toBeOneOf([
      "pending",
      "confirmed",
      "rejected",
      "expired",
    ]);
    expect(request.requesterId).toBeDefined();
    expect(request.storyId).toBeDefined();
    expect(request.characterId).toBeDefined();
  }

  /**
   * 验证故事权限控制
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  static expectValidStoryPermission(story: any, permissionLevel: string): void {
    expect(story).toBeDefined();
    expect(story.permissionLevel).toBe(permissionLevel);
    expect(["private", "friends", "public", "characters_only"]).toContain(
      story.permissionLevel,
    );
  }
}

/**
 * 测试数据验证工具
 */
export class TestDataValidator {
  /**
   * 验证数据库数据一致性
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  static async verifyDataConsistency(
    entity: string,
    expectedCount: number,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    condition: Record<string, any> = {},
  ): Promise<void> {
    const repository = global.testDataSource.getRepository(entity);
    const actualCount = await repository.count({ where: condition });
    expect(actualCount).toBe(expectedCount);
  }

  /**
   * 验证Redis缓存状态
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  static async verifyCacheState(
    key: string,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    expectedValue: any = null,
  ): Promise<void> {
    if (expectedValue === null) {
      const exists = await global.testRedis.exists(key);
      expect(exists).toBe(0);
    } else {
      const value = await global.testRedis.get(key);
      expect(value).toBe(JSON.stringify(expectedValue));
    }
  }
}

/**
 * 扩展Jest匹配器
 */
declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace jest {
    interface Matchers<R> {
      toBeOneOf(expected: unknown[]): R;
    }
  }
}

// 自定义Jest匹配器
expect.extend({
  toBeOneOf(received: unknown, expected: unknown[]) {
    const pass = expected.includes(received);
    return {
      message: () =>
        pass
          ? `expected ${received} not to be one of ${expected.join(", ")}`
          : `expected ${received} to be one of ${expected.join(", ")}`,
      pass,
    };
  },
});

/**
 * 集成测试数据操作工具类
 * 合并原 IntegrationTestUtils 的实用功能
 */
class IntegrationTestUtilsClass {
  /**
   * 获取数据库连接
   */
  static getDataSource() {
    return global.testDataSource;
  }

  /**
   * 获取Redis连接
   */
  static getRedisClient() {
    return global.testRedis;
  }

  /**
   * 创建测试用户
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  static async createTestUser(userData: Record<string, any> = {}) {
    const repository = global.testDataSource.getRepository("User");
    const user = repository.create({
      phone: "13800138000",
      email: "<EMAIL>",
      username: "testuser",
      ygsNumber: "YGS001",
      isActive: true,
      ...userData,
    });
    return await repository.save(user);
  }

  /**
   * 创建测试故事
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  static async createTestStory(storyData: Record<string, any> = {}) {
    const repository = global.testDataSource.getRepository("Story");
    const story = repository.create({
      title: "Test Story",
      content: "This is a test story",
      permissionLevel: "public",
      ...storyData,
    });
    return await repository.save(story);
  }

  /**
   * 创建测试人物
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  static async createTestCharacter(characterData: Record<string, any> = {}) {
    const repository = global.testDataSource.getRepository("Character");
    const character = repository.create({
      name: "Test Character",
      description: "This is a test character",
      isLighted: false,
      lightingCount: 0,
      ...characterData,
    });
    return await repository.save(character);
  }

  /**
   * 设置Redis缓存
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  static async setRedisCache(key: string, value: any, ttl?: number) {
    const stringValue =
      typeof value === "string" ? value : JSON.stringify(value);
    if (ttl) {
      await global.testRedis.setex(key, ttl, stringValue);
    } else {
      await global.testRedis.set(key, stringValue);
    }
  }

  /**
   * 获取Redis缓存
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  static async getRedisCache(key: string): Promise<any> {
    const value = await global.testRedis.get(key);
    if (!value) return null;

    try {
      return JSON.parse(value);
    } catch {
      return value;
    }
  }

  /**
   * 清理Redis缓存
   */
  static async clearRedisCache(pattern?: string) {
    if (pattern) {
      const keys = await global.testRedis.keys(pattern);
      if (keys.length > 0) {
        await global.testRedis.del(...keys);
      }
    } else {
      await global.testRedis.flushdb();
    }
  }

  /**
   * 检查数据库连接
   */
  static async checkDatabaseConnection(): Promise<boolean> {
    try {
      await global.testDataSource.query("SELECT 1");
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 检查Redis连接
   */
  static async checkRedisConnection(): Promise<boolean> {
    try {
      await global.testRedis.ping();
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 创建事务
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  static async createTransaction(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    callback: (manager: any) => Promise<void>,
  ): Promise<void> {
    await global.testDataSource.transaction(callback);
  }

  /**
   * 获取表记录数
   */
  static async getTableCount(tableName: string): Promise<number> {
    const result = await global.testDataSource.query(
      `SELECT COUNT(*) as count FROM ${tableName}`,
    );
    return parseInt(result[0].count);
  }
}

// 创建IntegrationTestUtils实例导出
export const IntegrationTestUtils = IntegrationTestUtilsClass;
export { configureTestApp as default };
