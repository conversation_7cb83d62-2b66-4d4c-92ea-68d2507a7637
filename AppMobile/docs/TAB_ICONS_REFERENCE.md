# YGS Tab图标资源清单

## 📱 底部导航Tab图标说明

### 图标功能和命名规范

| 功能 | 中文名称 | 图标描述 | 选中状态文件名 | 未选中状态文件名 |
|------|----------|----------|---------------|-----------------|
| 首页 | 首页 | 房子图标 | `home_selected.svg` | `home_unselected.svg` |
| 课程 | 课程 | 书本/文档图标 | `course_selected.svg` | `course_unselected.svg` |
| 活动 | 活动 | 日历图标 | `activity_selected.svg` | `activity_unselected.svg` |
| 我的 | 我的 | 人物头像图标 | `profile_selected.svg` | `profile_unselected.svg` |

### 目录结构
```
assets/icons/tab/
├── selected/        # 选中状态图标
│   ├── home_selected.svg
│   ├── course_selected.svg
│   ├── activity_selected.svg
│   └── profile_selected.svg
└── unselected/      # 未选中状态图标
    ├── home_unselected.svg
    ├── course_unselected.svg
    ├── activity_unselected.svg
    └── profile_unselected.svg
```

### 下载待办
- [ ] 首页图标（选中/未选中）
- [ ] 课程图标（选中/未选中）
- [ ] 活动图标（选中/未选中）
- [ ] 我的图标（选中/未选中）

### Figma节点ID获取方法
1. 在Figma中打开设计稿：https://www.figma.com/design/yV5Lcg4MAKkntsTK68OxZy/ygs-ui-v1.0.0?node-id=12039-102
2. 找到"标签栏icon"区域
3. 分别点击每个图标的选中和未选中状态
4. 复制节点ID（格式如：12345-678）
5. 提供给AI进行批量下载

### 注意事项
- 所有图标都应该是SVG格式
- 保持设计稿中的原始尺寸和比例
- 确保图标颜色和状态区分明确