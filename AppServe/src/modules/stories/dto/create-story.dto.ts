import {
  IsString,
  <PERSON><PERSON><PERSON>al,
  IsEnum,
  IsBoolean,
  IsArray,
  IsDateString,
  IsUUID,
} from "class-validator";
import { Transform } from "class-transformer";

/**
 * 创建故事DTO
 * 用于创建新故事的数据传输对象
 */
export class CreateStoryDto {
  @IsString()
  title: string;

  @IsOptional()
  @IsString()
  content?: string;

  @IsOptional()
  @IsString()
  coverImageUrl?: string;

  @IsOptional()
  @IsUUID()
  themeId?: string;

  @IsOptional()
  @IsDateString()
  storyDate?: string;

  @IsOptional()
  @IsString()
  location?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  images?: string[];

  @IsOptional()
  @IsEnum(["private", "friends", "public", "characters_only"])
  permissionLevel?: "private" | "friends" | "public" | "characters_only";

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === "true" || value === true)
  allowComments?: boolean;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === "true" || value === true)
  allowLikes?: boolean;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === "true" || value === true)
  allowSharing?: boolean;

  @IsOptional()
  @IsArray()
  @IsUUID(undefined, { each: true })
  characterIds?: string[];
}
