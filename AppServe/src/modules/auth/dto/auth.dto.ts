import {
  IsString,
  IsEmail,
  IsOptional,
  Length,
  Matches,
} from "class-validator";
import { ApiProperty } from "@nestjs/swagger";

export class SendSmsDto {
  @ApiProperty({ description: "手机号", example: "13800138000" })
  @IsString()
  @Length(11, 11, { message: "手机号必须为11位" })
  @Matches(/^1[3-9]\d{9}$/, { message: "手机号格式不正确" })
  phone: string;
}

export class LoginWithPhoneDto {
  @ApiProperty({ description: "手机号", example: "13800138000" })
  @IsString()
  @Length(11, 11, { message: "手机号必须为11位" })
  @Matches(/^1[3-9]\d{9}$/, { message: "手机号格式不正确" })
  phone: string;

  @ApiProperty({ description: "短信验证码", example: "1234" })
  @IsString()
  @Length(4, 4, { message: "验证码必须为4位" })
  code: string;
}

export class RegisterWithEmailDto {
  @ApiProperty({ description: "邮箱", example: "<EMAIL>" })
  @IsEmail({}, { message: "邮箱格式不正确" })
  email: string;

  @ApiProperty({ description: "密码", example: "Password123" })
  @IsString()
  @Length(8, 50, { message: "密码长度必须在8-50个字符之间" })
  @Matches(/^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/, {
    message: "密码必须包含至少一个字母和一个数字",
  })
  password: string;

  @ApiProperty({ description: "用户名", example: "张三", required: false })
  @IsOptional()
  @IsString()
  @Length(1, 50, { message: "用户名长度必须在1-50个字符之间" })
  username?: string;
}

export class LoginWithEmailDto {
  @ApiProperty({ description: "邮箱", example: "<EMAIL>" })
  @IsEmail({}, { message: "邮箱格式不正确" })
  email: string;

  @ApiProperty({ description: "密码", example: "Password123" })
  @IsString()
  @Length(8, 50, { message: "密码长度必须在8-50个字符之间" })
  password: string;
}

export class SetEmailPasswordDto {
  @ApiProperty({ description: "邮箱", example: "<EMAIL>" })
  @IsEmail({}, { message: "邮箱格式不正确" })
  email: string;

  @ApiProperty({ description: "密码", example: "Password123" })
  @IsString()
  @Length(8, 50, { message: "密码长度必须在8-50个字符之间" })
  @Matches(/^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/, {
    message: "密码必须包含至少一个字母和一个数字",
  })
  password: string;
}

export class RefreshTokenDto {
  @ApiProperty({ description: "刷新Token" })
  @IsString()
  refreshToken: string;
}
