import 'package:freezed_annotation/freezed_annotation.dart';

part 'auth_request_models.freezed.dart';
part 'auth_request_models.g.dart';

/// 发送短信验证码请求
@freezed
class SendSmsRequest with _$SendSmsRequest {
  const factory SendSmsRequest({
    required String phone,
  }) = _SendSmsRequest;

  factory SendSmsRequest.fromJson(Map<String, dynamic> json) =>
      _$SendSmsRequestFromJson(json);
}

/// 手机号登录请求
@freezed
class PhoneLoginRequest with _$PhoneLoginRequest {
  const factory PhoneLoginRequest({
    required String phone,
    required String code,
    String? deviceId,
    String? deviceName,
  }) = _PhoneLoginRequest;

  factory PhoneLoginRequest.fromJson(Map<String, dynamic> json) =>
      _$PhoneLoginRequestFromJson(json);
}

/// 邮箱注册请求
@freezed
class EmailRegisterRequest with _$EmailRegisterRequest {
  const factory EmailRegisterRequest({
    required String email,
    required String password,
    required String username,
  }) = _EmailRegisterRequest;

  factory EmailRegisterRequest.fromJson(Map<String, dynamic> json) =>
      _$EmailRegisterRequestFromJson(json);
}

/// 邮箱登录请求
@freezed
class EmailLoginRequest with _$EmailLoginRequest {
  const factory EmailLoginRequest({
    required String email,
    required String password,
    String? deviceId,
    String? deviceName,
  }) = _EmailLoginRequest;

  factory EmailLoginRequest.fromJson(Map<String, dynamic> json) =>
      _$EmailLoginRequestFromJson(json);
}

/// 设置邮箱密码请求
@freezed
class SetEmailPasswordRequest with _$SetEmailPasswordRequest {
  const factory SetEmailPasswordRequest({
    required String email,
    required String password,
  }) = _SetEmailPasswordRequest;

  factory SetEmailPasswordRequest.fromJson(Map<String, dynamic> json) =>
      _$SetEmailPasswordRequestFromJson(json);
}

/// 刷新Token请求
@freezed
class RefreshTokenRequest with _$RefreshTokenRequest {
  const factory RefreshTokenRequest({
    required String refreshToken,
  }) = _RefreshTokenRequest;

  factory RefreshTokenRequest.fromJson(Map<String, dynamic> json) =>
      _$RefreshTokenRequestFromJson(json);
}