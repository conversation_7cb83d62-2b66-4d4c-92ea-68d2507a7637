import type {
  ReferenceStatus,
  ReferenceVisibility,
} from "../entities/story-reference.entity";
import type {
  CollectionType,
  CollectionStatus,
  CollectionVisibility,
} from "../entities/reference-collection.entity";

/**
 * 引用片段响应DTO
 */
export class QuotedSegmentResponseDto {
  chapterId?: string;
  startPosition: number;
  endPosition: number;
  text: string;
  note?: string;
}

/**
 * 故事引用响应DTO
 */
export class ReferenceResponseDto {
  id: string;
  userId: string;
  userName: string;
  storyId: string;
  storyTitle: string;
  storyAuthorName: string;
  collectionId?: string;
  collectionName?: string;
  title: string;
  annotation?: string;
  quotedSegments?: QuotedSegmentResponseDto[];
  status: ReferenceStatus;
  visibility: ReferenceVisibility;
  tags?: string[];
  importance: number;
  likesCount: number;
  commentsCount: number;
  storySnapshot?: {
    title: string;
    summary?: string;
    authorId: string;
    authorName: string;
    publishedAt?: Date;
    chapterCount?: number;
  };
  lastReadAt?: Date;
  readCount: number;
  metadata?: Record<string, unknown>;
  createdAt: Date;
  updatedAt: Date;

  // 当前用户相关状态
  isLiked?: boolean;
  canEdit?: boolean;
  canDelete?: boolean;
}

/**
 * 引用集合响应DTO
 */
export class CollectionResponseDto {
  id: string;
  name: string;
  description?: string;
  ownerId: string;
  ownerName: string;
  ownerAvatar?: string;
  collectionType: CollectionType;
  status: CollectionStatus;
  visibility: CollectionVisibility;
  coverImage?: string;
  colorTheme?: string;
  tags?: string[];
  referenceCount: number;
  followersCount: number;
  likesCount: number;
  sortOrder: number;
  isFeatured: boolean;
  allowCollaboration: boolean;
  settings?: {
    autoAddSimilar?: boolean;
    notifyOnNewReference?: boolean;
    allowPublicContribution?: boolean;
    moderationRequired?: boolean;
  };
  lastContentUpdate?: Date;
  statistics?: {
    totalViews?: number;
    monthlyViews?: number;
    totalShares?: number;
    averageRating?: number;
    topTags?: string[];
  };
  metadata?: Record<string, unknown>;
  createdAt: Date;
  updatedAt: Date;

  // 当前用户相关状态
  isFollowing?: boolean;
  isLiked?: boolean;
  canEdit?: boolean;
  canContribute?: boolean;

  // 最新的几个引用预览
  recentReferences?: Array<{
    id: string;
    title: string;
    storyTitle: string;
    createdAt: Date;
  }>;
}

/**
 * 引用统计响应DTO
 */
export class ReferenceStatsResponseDto {
  userId: string;
  totalReferences: number;
  totalCollections: number;
  publicReferences: number;
  privateReferences: number;
  totalLikes: number;
  totalViews: number;
  totalShares: number;

  // 按集合类型统计
  collectionStats: Array<{
    collectionType: CollectionType;
    count: number;
    referenceCount: number;
  }>;

  // 按重要性统计
  importanceStats: Array<{
    importance: number;
    count: number;
    percentage: number;
  }>;

  // 热门标签
  topTags: Array<{
    tag: string;
    count: number;
    recentUse: Date;
  }>;

  // 最活跃的集合
  topCollections: Array<{
    collectionId: string;
    collectionName: string;
    referenceCount: number;
    likesCount: number;
    lastUpdate: Date;
  }>;

  // 最近活动
  recentActivity: Array<{
    type: "reference_created" | "collection_created" | "reference_liked";
    date: Date;
    count: number;
  }>;

  // 阅读统计
  readingStats: {
    totalReadCount: number;
    averageImportance: number;
    mostReadReference?: {
      referenceId: string;
      title: string;
      readCount: number;
    };
    readingTrend: Array<{
      date: Date;
      readCount: number;
    }>;
  };
}
