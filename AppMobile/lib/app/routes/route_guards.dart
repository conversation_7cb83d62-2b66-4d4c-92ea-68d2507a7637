import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../features/auth/presentation/providers/auth_provider.dart';
import '../../features/auth/domain/entities/user_entity.dart';

/// 认证路由守卫
class AuthGuard {
  final Ref ref;

  AuthGuard(this.ref);

  /// 检查是否已登录
  String? checkAuth(GoRouterState state) {
    final isLoggedIn = ref.read(isLoggedInProvider);
    final path = state.matchedLocation;
    
    // 公开页面列表
    final publicPaths = [
      '/login',
      '/splash',
      '/about',
      '/privacy',
      '/terms',
    ];
    
    // 如果是公开页面，不需要认证
    if (publicPaths.contains(path)) {
      return null;
    }
    
    // 如果未登录，重定向到登录页
    if (!isLoggedIn) {
      return '/login?from=${Uri.encodeComponent(path)}';
    }
    
    return null;
  }

  /// 检查是否有特定权限
  String? checkPermission(GoRouterState state, String permission) {
    final user = ref.read(currentUserProvider);
    
    if (user == null) {
      return '/login';
    }
    
    // TODO: 实现权限检查逻辑
    // 这里可以根据用户的权限信息进行判断
    
    return null;
  }

  /// 检查是否是VIP用户
  String? checkVip(GoRouterState state) {
    final user = ref.read(currentUserProvider);
    
    if (user == null) {
      return '/login';
    }
    
    if (!user.isVipValid) {
      return '/vip/upgrade?from=${Uri.encodeComponent(state.matchedLocation)}';
    }
    
    return null;
  }
}