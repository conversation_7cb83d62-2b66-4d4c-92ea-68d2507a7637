import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../../../design_system/tokens/ygs_colors.dart';
import '../../../../design_system/tokens/ygs_typography.dart';
import '../../domain/entities/story_entity.dart';
import '../providers/story_list_provider.dart';

/// 高性能故事卡片组件 - 实现复用和懒加载
class StoryCardWidget extends ConsumerWidget {
  const StoryCardWidget({
    required this.story,
    super.key,
  });

  final StoryEntity story;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      width: 690.w,
      margin: EdgeInsets.only(bottom: 21.h),
      decoration: BoxDecoration(
        color: YgsColors.backgroundCard,
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Stack(
        children: [
          // 主要内容
          Padding(
            padding: EdgeInsets.fromLTRB(44.w, 32.w, 44.w, 32.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 作者信息
                _buildAuthorInfo(),
                
                SizedBox(height: 38.h),
                
                // 故事标题
                _buildStoryTitle(),
                
                SizedBox(height: 18.h),
                
                // 故事内容
                _buildStoryContent(),
                
                SizedBox(height: 45.h),
                
                // 故事图片展示区域
                _buildStoryImages(),
                
                SizedBox(height: 34.h),
                
                // 互动头像
                _buildInteractionAvatars(),
                
                SizedBox(height: 16.h),
                
                // 底部信息
                _buildBottomInfo(ref),
              ],
            ),
          ),
          
          // 分类图标（右上角）
          _buildCategoryIcon(),
        ],
      ),
    );
  }

  /// 构建作者信息
  Widget _buildAuthorInfo() {
    return Row(
      children: [
        // 作者头像 - 使用缓存图片优化性能
        Builder(
          builder: (context) => ClipOval(
            child: _buildOptimizedImage(
              context,
              story.authorAvatar,
              width: 92.w,
              height: 92.w,
              placeholder: Container(
                width: 92.w,
                height: 92.w,
                decoration: const BoxDecoration(
                  color: Color(0xFFF0F0F0),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.person,
                  size: 46.w,
                  color: YgsColors.textSecondary,
                ),
              ),
            ),
          ),
        ),
        
        SizedBox(width: 10.w),
        
        // 作者信息
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                story.authorName,
                style: YgsTypography.headlineSmall.copyWith(
                  fontSize: 30.sp,
                  fontWeight: FontWeight.w600,
                  color: YgsColors.textPrimary,
                ),
              ),
              
              SizedBox(height: 8.h),
              
              Row(
                children: [
                  Text(
                    '分享了故事到',
                    style: YgsTypography.bodyMedium.copyWith(
                      fontSize: 20.sp,
                      color: YgsColors.textSecondary,
                    ),
                  ),
                  
                  SizedBox(width: 7.w),
                  
                  Text(
                    story.categoryTag,
                    style: YgsTypography.bodyMedium.copyWith(
                      fontSize: 20.sp,
                      fontWeight: FontWeight.w500,
                      color: Color(story.categoryColor),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建故事标题
  Widget _buildStoryTitle() {
    return Text(
      story.title,
      style: YgsTypography.headlineSmall.copyWith(
        fontSize: 28.sp,
        fontWeight: FontWeight.w500,
        color: YgsColors.textPrimary,
      ),
    );
  }

  /// 构建故事内容 - 显示3行，超出部分用省略号
  Widget _buildStoryContent() {
    return Text(
      story.content,
      style: YgsTypography.bodyMedium.copyWith(
        fontSize: 22.sp,
        color: YgsColors.textSecondary,
        height: 1.4,
      ),
      maxLines: 3, // 显示3行内容
      overflow: TextOverflow.ellipsis, // 超出部分显示省略号
    );
  }

  /// 构建故事图片展示区域
  Widget _buildStoryImages() {
    if (story.images.isEmpty) return const SizedBox.shrink();
    
    if (story.images.length <= 3) {
      // 3张或以下：使用固定行布局
      return Row(
        children: [
          for (int i = 0; i < story.images.length; i++)
            Padding(
              padding: EdgeInsets.only(right: i < story.images.length - 1 ? 20.w : 0),
              child: _buildImageItem(story.images[i]),
            ),
        ],
      );
    } else {
      // 超过3张：显示前3张完整 + 第4张部分，支持滑动
      return SizedBox(
        height: 160.w,
        child: Stack(
          children: [
            // 可滑动的图片列表 - 使用ListView.builder优化性能
            ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: EdgeInsets.zero,
              itemCount: story.images.length,
              cacheExtent: 200.w, // 预缓存范围
              itemBuilder: (context, index) {
                return Padding(
                  padding: EdgeInsets.only(
                    right: index < story.images.length - 1 ? 20.w : 0,
                  ),
                  child: _buildImageItem(story.images[index]),
                );
              },
            ),
            
            // 右侧渐变遮罩，提示还有更多图片
            if (story.images.length > 3)
              Positioned(
                right: 0,
                top: 0,
                bottom: 0,
                child: Container(
                  width: 80.w,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                      colors: [
                        YgsColors.backgroundPrimary.withOpacity(0.0),
                        YgsColors.backgroundPrimary.withOpacity(0.3),
                        YgsColors.backgroundPrimary.withOpacity(0.8),
                      ],
                      stops: const [0.0, 0.5, 1.0],
                    ),
                  ),
                  child: Center(
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.6),
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      child: Text(
                        '+${story.images.length - 3}',
                        style: YgsTypography.labelSmall.copyWith(
                          color: Colors.white,
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      );
    }
  }

  /// 构建单个图片项 - 优化图片加载性能
  Widget _buildImageItem(String imagePath) {
    return Builder(
      builder: (context) => ClipRRect(
        borderRadius: BorderRadius.circular(8.r),
        child: _buildOptimizedImage(
          context,
          imagePath,
          width: 160.w,
          height: 160.w,
          placeholder: Container(
            width: 160.w,
            height: 160.w,
            decoration: BoxDecoration(
              color: const Color(0xFFF0F0F0),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Icon(
              Icons.image,
              size: 40.w,
              color: YgsColors.textSecondary,
            ),
          ),
        ),
      ),
    );
  }

  /// 构建互动头像
  Widget _buildInteractionAvatars() {
    return Row(
      children: [
        for (int i = 0; i < story.interactionAvatars.length && i < 3; i++)
          Padding(
            padding: EdgeInsets.only(right: i < 2 ? 40.w : 0),
            child: _buildInteractionAvatar(story.interactionAvatars[i]),
          ),
      ],
    );
  }

  /// 构建带有点亮效果的互动头像
  Widget _buildInteractionAvatar(InteractionAvatar avatar) {
    if (!avatar.isLightedUp) {
      // 未点亮状态：普通头像
      return SizedBox(
        width: 48.w,
        height: 48.w,
        child: Builder(
          builder: (context) => ClipOval(
            child: _buildOptimizedImage(
              context,
              avatar.avatarUrl,
              width: 48.w,
              height: 48.w,
              placeholder: Container(
                width: 48.w,
                height: 48.w,
                decoration: const BoxDecoration(
                  color: Color(0xFFF0F0F0),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.person,
                  size: 24.w,
                  color: YgsColors.textSecondary,
                ),
              ),
            ),
          ),
        ),
      );
    }

    // 点亮状态：带渐变边框的头像
    return Container(
      width: 54.w,
      height: 54.w,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFFFFD700), // 亮金色
            Color(0xFFFFB800), // 中金色
            Color(0xFFFF8C00), // 深金色
            Color(0xFFFF6B35), // 橙金色
          ],
          stops: [0.0, 0.3, 0.7, 1.0],
        ),
      ),
      child: Container(
        margin: EdgeInsets.all(3.w),
        decoration: const BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.white,
        ),
        child: Builder(
          builder: (context) => ClipOval(
            child: _buildOptimizedImage(
              context,
              avatar.avatarUrl,
              width: 48.w,
              height: 48.w,
              placeholder: Container(
                width: 48.w,
                height: 48.w,
                decoration: const BoxDecoration(
                  color: Color(0xFFF0F0F0),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.person,
                  size: 24.w,
                  color: YgsColors.textSecondary,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建底部信息
  Widget _buildBottomInfo(WidgetRef ref) {
    final dateStr = '${story.createdAt.year}.${story.createdAt.month.toString().padLeft(2, '0')}.${story.createdAt.day.toString().padLeft(2, '0')}';
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          '$dateStr · ${story.location}',
          style: YgsTypography.bodySmall.copyWith(
            fontSize: 22.sp,
            color: YgsColors.textTertiary,
          ),
        ),
        
        GestureDetector(
          onTap: () => ref.read(storyListProvider.notifier).likeStory(story.id),
          behavior: HitTestBehavior.opaque,
          child: Padding(
            padding: EdgeInsets.all(8.w), // 扩大点击区域
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 爱心图标 - 固定尺寸避免位移
                SizedBox(
                  width: 40.w,
                  height: 40.w,
                  child: AnimatedSwitcher(
                    duration: const Duration(milliseconds: 200),
                    child: Icon(
                      story.isLiked ? Icons.favorite : Icons.favorite_border,
                      key: ValueKey(story.isLiked),
                      size: 40.w,
                      color: story.isLiked 
                          ? Colors.red 
                          : YgsColors.textTertiary,
                    ),
                  ),
                ),
                
                SizedBox(width: 1.w),
                
                // 点赞数字 - 固定容器宽度避免位移
                Container(
                  constraints: BoxConstraints(minWidth: 60.w),
                  child: AnimatedSwitcher(
                    duration: const Duration(milliseconds: 300),
                    transitionBuilder: (Widget child, Animation<double> animation) {
                      return FadeTransition(
                        opacity: animation,
                        child: SlideTransition(
                          position: Tween<Offset>(
                            begin: const Offset(0, 0.3),
                            end: Offset.zero,
                          ).animate(animation),
                          child: child,
                        ),
                      );
                    },
                    child: Text(
                      _formatLikeCount(story.likeCount),
                      key: ValueKey(story.likeCount),
                      style: YgsTypography.bodySmall.copyWith(
                        fontSize: 22.sp,
                        color: YgsColors.textTertiary,
                      ),
                      textAlign: TextAlign.left,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 构建分类图标（右上角）
  Widget _buildCategoryIcon() {
    return Positioned(
      top: 11.h,
      right: 17.w,
      child: Transform.rotate(
        angle: 0.26, // 约15度旋转
        child: Builder(
          builder: (context) => _buildOptimizedImage(
            context,
            story.categoryIcon,
            width: 125.w,
            height: 117.h,
            placeholder: Container(
              width: 125.w,
              height: 117.h,
              decoration: BoxDecoration(
                color: const Color(0xFFF0F0F0),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Icon(
                Icons.category,
                size: 40.w,
                color: YgsColors.textSecondary,
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建优化的图片组件 - 支持缓存和懒加载
  Widget _buildOptimizedImage(
    BuildContext context,
    String imagePath, {
    required double width,
    required double height,
    Widget? placeholder,
  }) {
    // 检查是否为网络图片
    if (imagePath.startsWith('http')) {
      return CachedNetworkImage(
        imageUrl: imagePath,
        width: width,
        height: height,
        fit: BoxFit.cover,
        placeholder: (context, url) => placeholder ?? _defaultPlaceholder(width, height),
        errorWidget: (context, url, error) => placeholder ?? _defaultPlaceholder(width, height),
        // 移除缓存尺寸设置，避免图片变形
        // memCacheWidth: (width * MediaQuery.of(context).devicePixelRatio).round(),
        // memCacheHeight: (height * MediaQuery.of(context).devicePixelRatio).round(),
      );
    } else {
      // 本地资源图片
      return Image.asset(
        imagePath,
        width: width,
        height: height,
        fit: BoxFit.cover,
        // 移除缓存尺寸设置，避免图片变形
        // cacheWidth: (width * MediaQuery.of(context).devicePixelRatio).round(),
        // cacheHeight: (height * MediaQuery.of(context).devicePixelRatio).round(),
        errorBuilder: (context, error, stackTrace) {
          return placeholder ?? _defaultPlaceholder(width, height);
        },
      );
    }
  }

  /// 默认占位符
  Widget _defaultPlaceholder(double width, double height) {
    return Container(
      width: width,
      height: height,
      color: const Color(0xFFF0F0F0),
      child: Icon(
        Icons.image,
        size: (width * 0.3).clamp(16.0, 40.0),
        color: YgsColors.textSecondary,
      ),
    );
  }

  /// 格式化点赞数
  String _formatLikeCount(int count) {
    if (count < 1000) {
      return count.toString();
    } else if (count < 10000) {
      final k = (count / 1000).toStringAsFixed(1);
      return '${k.endsWith('.0') ? k.substring(0, k.length - 2) : k}k';
    } else {
      final w = (count / 10000).toStringAsFixed(1);
      return '${w.endsWith('.0') ? w.substring(0, w.length - 2) : w}w';
    }
  }
}