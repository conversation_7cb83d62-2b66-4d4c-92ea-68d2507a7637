# 有故事 APP - 脚本使用说明

## 概述

本目录包含有故事 APP 前端开发所需的各种脚本工具，用于简化开发流程、提高开发效率。所有脚本都遵循企业级开发规范，确保稳定性和可维护性。

## 脚本列表

### 🚀 核心开发脚本

#### 1. `dev-start.sh` - 智能开发启动脚本
**用途**: 一键启动开发环境，自动配置网络和端口转发
**功能**:
- 检查后端服务状态
- 自动配置 Android 端口转发
- 可选启动 Metro bundler
- 提供开发提示和故障排查指导

**使用方法**:
```bash
# 完整启动流程
./scripts/dev-start.sh

# 强制重新配置端口转发
./scripts/dev-start.sh --force

# 仅检查网络状态
./scripts/dev-start.sh --check

# 仅启动 Metro
./scripts/dev-start.sh --metro
```

#### 2. `check-network.sh` - 快速网络状态检查
**用途**: 快速诊断网络连接问题
**功能**:
- 检查后端服务状态
- 检查 Metro bundler 状态
- 自动更新 Android 端口转发
- 测试 API 连接

**使用方法**:
```bash
./scripts/check-network.sh
```

#### 3. `setup-android-network.sh` - Android 网络配置
**用途**: 专门配置 Android 模拟器网络连接
**功能**:
- 检查 ADB 环境
- 设置端口转发 (8081, 3000)
- 测试网络连接
- 显示配置信息

**使用方法**:
```bash
./scripts/setup-android-network.sh
```

### 🧹 维护脚本

#### 4. `clean-build.sh` - 构建清理脚本
**用途**: 清理构建产物和缓存文件
**功能**:
- 清理 Android 构建文件
- 清理 iOS 构建文件
- 清理日志和临时文件
- 清理 Metro 缓存

**使用方法**:
```bash
./scripts/clean-build.sh
```

#### 5. `fast-android.sh` - Android 快速构建
**用途**: 优化 Android 构建速度
**功能**:
- 使用国内镜像源
- 优化 Gradle 参数
- 并行构建
- 自动安装到设备

**使用方法**:
```bash
./scripts/fast-android.sh
```

### 🔧 配置脚本

#### 6. `aliases.sh` - 开发命令别名配置
**用途**: 生成便捷的命令别名
**功能**:
- 显示推荐的命令别名
- 提供快速添加到 shell 配置的命令
- 自动获取项目路径

**使用方法**:
```bash
./scripts/aliases.sh
```

## 快速开始

### 第一次使用
1. 配置开发别名（推荐）:
   ```bash
   ./scripts/aliases.sh
   # 复制输出的命令到你的 ~/.zshrc
   source ~/.zshrc
   ```

2. 启动开发环境:
   ```bash
   ygs-start  # 如果已配置别名
   # 或
   ./scripts/dev-start.sh
   ```

### 日常开发流程
1. **启动开发环境**: `ygs-start` 或 `./scripts/dev-start.sh`
2. **检查网络状态**: `ygs-check` 或 `./scripts/check-network.sh`
3. **清理构建文件**: `ygs-clean` 或 `./scripts/clean-build.sh`
4. **快速 Android 构建**: `ygs-android-fast` 或 `./scripts/fast-android.sh`

## 常见问题与解决方案

### 网络连接问题
- **症状**: API 请求失败，Metro 连接异常
- **解决**: 运行 `./scripts/check-network.sh` 或 `ygs-check`

### 构建失败
- **症状**: Android/iOS 构建报错
- **解决**: 先运行 `./scripts/clean-build.sh` 清理缓存

### 端口冲突
- **症状**: Metro 或 API 端口被占用
- **解决**: 运行 `./scripts/dev-start.sh --force` 重新配置

### ADB 连接问题
- **症状**: 找不到 Android 设备
- **解决**: 运行 `./scripts/setup-android-network.sh` 重新配置

## 推荐的命令别名

配置以下别名可以极大提高开发效率:
```bash
alias ygs-start='cd /path/to/AppUI && ./scripts/dev-start.sh'
alias ygs-check='cd /path/to/AppUI && ./scripts/check-network.sh'
alias ygs-setup='cd /path/to/AppUI && ./scripts/setup-android-network.sh'
alias ygs-clean='cd /path/to/AppUI && ./scripts/clean-build.sh'
alias ygs-android-fast='cd /path/to/AppUI && ./scripts/fast-android.sh'
alias ygs-android='cd /path/to/AppUI && npx react-native run-android'
alias ygs-ios='cd /path/to/AppUI && npx react-native run-ios'
alias ygs-metro='cd /path/to/AppUI && npx react-native start'
alias ygs-backend='cd /path/to/AppServe && docker-compose up'
```

## 脚本开发规范

### 新增脚本时需要遵循的规范:

1. **文件头注释**: 包含项目名称、脚本用途说明
2. **颜色输出**: 使用标准的颜色定义提高可读性
3. **错误处理**: 适当的错误检查和退出码
4. **参数支持**: 支持必要的命令行参数
5. **文档更新**: 在本文档中添加新脚本的说明

### 颜色标准
```bash
RED='\033[0;31m'      # 错误信息
GREEN='\033[0;32m'    # 成功信息
YELLOW='\033[1;33m'   # 警告信息
BLUE='\033[0;34m'     # 提示信息
NC='\033[0m'          # 无颜色
```

### 输出格式标准
- ✅ 成功操作
- ❌ 失败操作
- ⚠️  警告信息
- 💡 提示信息
- 🔧 配置操作
- 📱 移动端相关
- 🖥️  后端相关

## 维护日志

- **2024-12**: 初始化脚本集合，统一管理开发工具脚本
- 后续更新请在此记录...

---

如有问题或建议，请联系开发团队或在项目中提出 Issue。 