/**
 * 有故事 APP - 认证服务
 * 
 * 功能特性：
 * - 发送短信验证码
 * - 手机号登录/注册
 * - 用户认证状态管理
 */

import { api, ApiResponse, tokenManager } from './api';

// ===== 认证数据类型 =====
export interface SendSmsRequest {
  phone: string;
}

export interface LoginWithPhoneRequest {
  phone: string;
  code: string;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
}

export interface UserProfile {
  id: string;
  phone: string;
  username?: string;
  avatarUrl?: string;
  email?: string;
  isFirstLogin?: boolean;
}

export interface LoginResponse {
  user: UserProfile;
  tokens: AuthTokens;
}

/**
 * 认证服务类
 */
class AuthService {
  /**
   * 发送短信验证码 - 企业级标准
   */
  async sendSms(request: SendSmsRequest): Promise<any> {
    try {
      const response = await api.post<ApiResponse<any>>('/auth/send-sms', request);
      return response.data;  // 企业级标准：统一响应格式
    } catch (error: any) {
      throw this.handleError(error);
    }
  }

  /**
   * 手机号+验证码登录/注册 - 企业级标准
   */
  async loginWithPhone(request: LoginWithPhoneRequest): Promise<LoginResponse> {
    try {
      const response = await api.post<ApiResponse<LoginResponse>>('/auth/login/phone', request);
      const { user, tokens } = response.data.data;  // 企业级标准：只需一层data
      
      // 保存 Token
      await tokenManager.setTokens(tokens.accessToken, tokens.refreshToken);
      
      return { user, tokens };
    } catch (error: any) {
      throw this.handleError(error);
    }
  }

  /**
   * 获取用户信息
   */
  async getProfile(): Promise<UserProfile> {
    try {
      const response = await api.get<ApiResponse<UserProfile>>('/auth/profile');
      return response.data.data;
    } catch (error: any) {
      throw this.handleError(error);
    }
  }

  /**
   * 注销登录
   */
  async logout(): Promise<void> {
    try {
      await api.post('/auth/logout');
    } catch (error) {
      // 即使接口调用失败，也要清除本地 Token
      console.warn('注销接口调用失败:', error);
    } finally {
      // 清除本地 Token
      await tokenManager.clearTokens();
    }
  }

  /**
   * 检查认证状态
   */
  async checkAuthStatus(): Promise<boolean> {
    try {
      const isAuthenticated = await tokenManager.isAuthenticated();
      if (!isAuthenticated) {
        return false;
      }

      // 验证 Token 有效性
      await this.getProfile();
      return true;
    } catch (error) {
      // Token 无效，清除本地存储
      await tokenManager.clearTokens();
      return false;
    }
  }

  /**
   * 错误处理
   */
  private handleError(error: any): Error {
    if (error.response?.data) {
      const apiError = error.response.data;
      return new Error(apiError.message || '请求失败');
    }
    
    if (error.code === 'NETWORK_ERROR') {
      return new Error('网络连接失败，请检查网络设置');
    }
    
    if (error.code === 'TIMEOUT') {
      return new Error('请求超时，请稍后重试');
    }
    
    return new Error(error.message || '未知错误');
  }
}

// ===== 导出服务实例 =====
export const authService = new AuthService(); 