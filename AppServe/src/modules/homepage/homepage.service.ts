import { Injectable, NotFoundException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { NullValueHelper } from "../../common/helpers/entity-update.helper";
import { User } from "../users/entities/user.entity";
import { Story } from "../stories/entities/story.entity";
import { Character } from "../characters/entities/character.entity";
import { Bookmark } from "../bookmarks/entities/bookmark.entity";
import { TimelineEvent } from "../timeline/entities/timeline-event.entity";
import { ReferenceCollection } from "../story-references/entities/reference-collection.entity";
import { UserFollow } from "../social/entities/user-follow.entity";
import { FriendGroup } from "../social/entities/friend-group.entity";
import { HomepageTheme, HomepageLayout } from "./dto";
import type {
  UpdateDisplaySettingsDto,
  UpdateHomepageSettingsDto,
  HomepageDataResponseDto,
  PersonalStatsResponseDto,
} from "./dto";

@Injectable()
export class HomepageService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Story)
    private readonly storyRepository: Repository<Story>,
    @InjectRepository(Character)
    private readonly characterRepository: Repository<Character>,
    @InjectRepository(Bookmark)
    private readonly bookmarkRepository: Repository<Bookmark>,
    @InjectRepository(TimelineEvent)
    private readonly timelineEventRepository: Repository<TimelineEvent>,
    @InjectRepository(ReferenceCollection)
    private readonly referenceCollectionRepository: Repository<ReferenceCollection>,
    @InjectRepository(UserFollow)
    private readonly userFollowRepository: Repository<UserFollow>,
    @InjectRepository(FriendGroup)
    private readonly friendGroupRepository: Repository<FriendGroup>,
  ) {}

  // ==================== 个人主页数据获取 ====================

  /**
   * 获取个人主页完整数据
   */
  async getHomepageData(
    targetUserId: string,
    currentUserId?: string,
  ): Promise<HomepageDataResponseDto> {
    const user = await this.userRepository.findOne({
      where: { id: targetUserId },
    });

    if (!user) {
      throw new NotFoundException("用户不存在");
    }

    // 获取用户的展示设置
    const displaySettings = user.profileDisplaySettings;

    // 获取高级设置（如果用户有的话，从扩展字段中获取）
    const advancedSettings = this.getAdvancedSettings(user);

    // 获取统计信息
    const statistics = await this.getUserStatistics(targetUserId);

    // 获取内容预览（根据展示设置和权限）
    const content = await this.getContentPreviews(
      targetUserId,
      displaySettings,
      currentUserId,
    );

    // 检查关系状态
    const relationshipStatus = await this.getRelationshipStatus(
      targetUserId,
      currentUserId,
    );

    return {
      userId: user.id,
      nickname: user.nickname,
      username: user.username,
      avatarUrl: user.avatarUrl,
      birthDate: displaySettings.showBirthday ? user.birthDate : undefined,
      bio: displaySettings.showBio ? user.bio : undefined,
      userNumber: user.userNumber,
      displaySettings,
      advancedSettings,
      statistics: displaySettings.showStatistics
        ? statistics
        : this.getEmptyStatistics(),
      content,
      canEdit: currentUserId === targetUserId,
      isFollowing: relationshipStatus.isFollowing,
      isFriend: relationshipStatus.isFriend,
    };
  }

  /**
   * 更新个人主页展示设置
   */
  async updateDisplaySettings(
    userId: string,
    updateDto: UpdateDisplaySettingsDto,
  ): Promise<{ message: string }> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException("用户不存在");
    }

    const newDisplaySettings = {
      ...user.profileDisplaySettings,
      ...updateDto,
    };

    await this.userRepository.update(userId, {
      profileDisplaySettings: newDisplaySettings,
    });

    return { message: "展示设置更新成功" };
  }

  /**
   * 更新个人主页高级设置
   */
  async updateHomepageSettings(
    userId: string,
    updateDto: UpdateHomepageSettingsDto,
  ): Promise<{ message: string }> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException("用户不存在");
    }

    // 更新展示设置
    const newDisplaySettings = {
      ...user.profileDisplaySettings,
    };

    // 直接更新每个字段，避免使用动态键访问
    if (updateDto.showBirthday !== undefined) {
      newDisplaySettings.showBirthday = updateDto.showBirthday;
    }
    if (updateDto.showBio !== undefined) {
      newDisplaySettings.showBio = updateDto.showBio;
    }
    if (updateDto.showStatistics !== undefined) {
      newDisplaySettings.showStatistics = updateDto.showStatistics;
    }
    if (updateDto.showCharacters !== undefined) {
      newDisplaySettings.showCharacters = updateDto.showCharacters;
    }
    if (updateDto.showTimeline !== undefined) {
      newDisplaySettings.showTimeline = updateDto.showTimeline;
    }
    if (updateDto.showStories !== undefined) {
      newDisplaySettings.showStories = updateDto.showStories;
    }
    if (updateDto.showBookmarks !== undefined) {
      newDisplaySettings.showBookmarks = updateDto.showBookmarks;
    }
    if (updateDto.showFollowList !== undefined) {
      newDisplaySettings.showFollowList = updateDto.showFollowList;
    }
    if (updateDto.showReferenceCollections !== undefined) {
      newDisplaySettings.showReferenceCollections =
        updateDto.showReferenceCollections;
    }

    // 构建高级设置（存储在用户的扩展字段中）
    const currentAdvancedSettings = this.getAdvancedSettings(user);
    const newAdvancedSettings = {
      ...currentAdvancedSettings,
    };

    // 更新高级设置字段
    if (updateDto.theme !== undefined) {
      newAdvancedSettings.theme = updateDto.theme;
    }
    if (updateDto.layout !== undefined) {
      newAdvancedSettings.layout = updateDto.layout;
    }
    if (updateDto.customBio !== undefined) {
      newAdvancedSettings.customBio = updateDto.customBio;
    }
    if (updateDto.backgroundImage !== undefined) {
      newAdvancedSettings.backgroundImage = updateDto.backgroundImage;
    }
    if (updateDto.pinnedItems !== undefined) {
      newAdvancedSettings.pinnedItems = updateDto.pinnedItems;
    }
    if (updateDto.socialLinks !== undefined) {
      newAdvancedSettings.socialLinks = updateDto.socialLinks;
    }

    // 更新用户信息，包括高级设置
    const updateData: Record<string, unknown> = {
      profileDisplaySettings: newDisplaySettings,
    };

    // 将高级设置存储到扩展字段中（假设User实体有metadata字段）
    // 如果没有metadata字段，可以考虑创建一个单独的UserSettings实体
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    if ((user as any).metadata) {
      updateData.metadata = {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        ...(user as any).metadata,
        homepageSettings: newAdvancedSettings,
      };
    } else {
      // 如果没有metadata字段，我们可以考虑其他存储方式
      // 这里暂时模拟存储到一个假设的字段中
    }

    await this.userRepository.update(userId, updateData);

    return { message: "主页设置更新成功" };
  }

  /**
   * 获取个人统计数据
   */
  async getPersonalStats(userId: string): Promise<PersonalStatsResponseDto> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException("用户不存在");
    }

    // 基础统计
    const basicStats = await this.getBasicStats(userId);

    // 社交统计
    const socialStats = await this.getSocialStats(userId);

    // 互动统计
    const interactionStats = await this.getInteractionStats(userId);

    // 创作统计
    const creationStats = await this.getCreationStats(userId);

    // 趋势数据
    const trendData = await this.getTrendData(userId);

    // 成就数据
    const achievements = await this.getAchievements(userId);

    // 账户信息
    const accountInfo = await this.getAccountInfo(userId);

    return {
      userId,
      basicStats,
      socialStats,
      interactionStats,
      creationStats,
      trendData,
      achievements,
      accountInfo,
    };
  }

  // ==================== 私有辅助方法 ====================

  /**
   * 获取用户的高级设置
   */
  private getAdvancedSettings(user: User): {
    theme: HomepageTheme;
    layout: HomepageLayout;
    customBio?: string;
    backgroundImage?: string;
    pinnedItems?: string[];
    socialLinks?: Array<{
      platform: string;
      url: string;
      display: boolean;
    }>;
  } {
    // 从用户的metadata字段中获取高级设置，如果没有则返回默认值
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const metadata = (user as any).metadata;
    const homepageSettings = metadata?.homepageSettings || {};

    return {
      theme: homepageSettings.theme || HomepageTheme.AUTO,
      layout: homepageSettings.layout || HomepageLayout.MODERN,
      customBio: homepageSettings.customBio,
      backgroundImage: homepageSettings.backgroundImage,
      pinnedItems: homepageSettings.pinnedItems || [],
      socialLinks: homepageSettings.socialLinks || [],
    };
  }

  /**
   * 获取用户统计信息
   */
  private async getUserStatistics(userId: string): Promise<{
    storiesCount: number;
    charactersCount: number;
    lightedCharactersCount: number;
    bookmarksCount: number;
    followersCount: number;
    followingCount: number;
    timelineEventsCount: number;
    referenceCollectionsCount: number;
    totalLikesReceived: number;
    totalViewsReceived: number;
    memberSince: Date | null;
    lastActiveAt: Date | null;
  }> {
    const [
      storiesCount,
      charactersCount,
      lightedCharactersCount,
      bookmarksCount,
      timelineEventsCount,
      referenceCollectionsCount,
      followersCount,
      followingCount,
    ] = await Promise.all([
      this.storyRepository.count({ where: { userId } }),
      this.characterRepository.count({ where: { creatorId: userId } }),
      this.characterRepository.count({ where: { lighterUserId: userId } }),
      this.bookmarkRepository.count({ where: { userId } }),
      this.timelineEventRepository.count({ where: { userId } }),
      this.referenceCollectionRepository.count({ where: { ownerId: userId } }),
      this.userFollowRepository.count({ where: { followingId: userId } }),
      this.userFollowRepository.count({ where: { followerId: userId } }),
    ]);

    // 计算总获得点赞数（需要从各个内容类型聚合）
    const storyLikes = await this.storyRepository
      .createQueryBuilder("story")
      .select("SUM(story.likeCount)", "total")
      .where("story.userId = :userId", { userId })
      .getRawOne();

    const totalLikesReceived = parseInt(storyLikes?.total || "0", 10);

    // 计算总浏览量
    const storyViews = await this.storyRepository
      .createQueryBuilder("story")
      .select("SUM(story.viewCount)", "total")
      .where("story.userId = :userId", { userId })
      .getRawOne();

    const totalViewsReceived = parseInt(storyViews?.total || "0", 10);

    // 获取用户创建时间
    const userInfo = await this.userRepository.findOne({
      where: { id: userId },
      select: ["createdAt", "lastLoginAt"],
    });

    return {
      storiesCount,
      charactersCount,
      lightedCharactersCount,
      bookmarksCount,
      followersCount,
      followingCount,
      timelineEventsCount,
      referenceCollectionsCount,
      totalLikesReceived,
      totalViewsReceived,
      memberSince: NullValueHelper.sanitizeToNull(userInfo?.createdAt),
      lastActiveAt: NullValueHelper.sanitizeToNull(userInfo?.lastLoginAt),
    };
  }

  /**
   * 获取内容预览
   */
  private async getContentPreviews(
    userId: string,
    displaySettings: Record<string, unknown>,
    _currentUserId?: string,
  ): Promise<Record<string, unknown>> {
    const content: Record<string, unknown> = {};

    // 最新故事
    if (displaySettings.showStories) {
      const recentStories = await this.storyRepository
        .createQueryBuilder("story")
        .where("story.userId = :userId", { userId })
        .andWhere("story.status = :status", { status: 2 }) // 已发布状态
        .orderBy("story.createdAt", "DESC")
        .limit(3)
        .getMany();

      content.recentStories = recentStories.map((story) => ({
        id: story.id,
        title: story.title,
        summary: story.content?.substring(0, 150) + "...",
        coverImageUrl: story.coverImageUrl,
        createdAt: story.createdAt,
        likesCount: story.likeCount,
        viewCount: story.viewCount,
      }));
    }

    // 最新人物
    if (displaySettings.showCharacters) {
      const recentCharacters = await this.characterRepository
        .createQueryBuilder("character")
        .where("character.creatorId = :userId", { userId })
        .orderBy("character.createdAt", "DESC")
        .limit(3)
        .getMany();

      content.recentCharacters = recentCharacters.map((character) => ({
        id: character.id,
        name: character.name,
        description: character.description,
        avatarUrl: character.avatarUrl,
        isLighted: character.isLighted,
        createdAt: character.createdAt,
      }));
    }

    // 时间线亮点
    if (displaySettings.showTimeline) {
      const timelineHighlights = await this.timelineEventRepository
        .createQueryBuilder("event")
        .where("event.userId = :userId", { userId })
        .andWhere("event.isMilestone = :isMilestone", { isMilestone: true })
        .orderBy("event.eventDate", "DESC")
        .limit(3)
        .getMany();

      content.timelineHighlights = timelineHighlights.map((event) => ({
        id: event.id,
        title: event.title,
        eventType: event.eventType,
        eventDate: event.eventDate,
        isMilestone: event.isMilestone,
      }));
    }

    // 其他内容预览...
    if (displaySettings.showBookmarks) {
      content.bookmarkHighlights = [];
    }

    if (displaySettings.showReferenceCollections) {
      content.referenceCollectionHighlights = [];
    }

    return content;
  }

  /**
   * 获取关系状态
   */
  private async getRelationshipStatus(
    targetUserId: string,
    currentUserId?: string,
  ): Promise<{ isFollowing: boolean; isFriend: boolean }> {
    if (!currentUserId || currentUserId === targetUserId) {
      return { isFollowing: false, isFriend: false };
    }

    const [followRelation, friendRelation] = await Promise.all([
      this.userFollowRepository.findOne({
        where: { followerId: currentUserId, followingId: targetUserId },
      }),
      // 朋友关系检查（双向关注）
      this.userFollowRepository.count({
        where: [
          { followerId: currentUserId, followingId: targetUserId },
          { followerId: targetUserId, followingId: currentUserId },
        ],
      }),
    ]);

    return {
      isFollowing: !!followRelation,
      isFriend: friendRelation >= 2, // 双向关注才算朋友
    };
  }

  /**
   * 获取空的统计信息（用于隐私设置）
   */
  private getEmptyStatistics(): {
    storiesCount: number;
    charactersCount: number;
    lightedCharactersCount: number;
    bookmarksCount: number;
    followersCount: number;
    followingCount: number;
    timelineEventsCount: number;
    referenceCollectionsCount: number;
    totalLikesReceived: number;
    totalViewsReceived: number;
    memberSince: Date | null;
    lastActiveAt: Date | null;
  } {
    return {
      storiesCount: 0,
      charactersCount: 0,
      lightedCharactersCount: 0,
      bookmarksCount: 0,
      followersCount: 0,
      followingCount: 0,
      timelineEventsCount: 0,
      referenceCollectionsCount: 0,
      totalLikesReceived: 0,
      totalViewsReceived: 0,
      memberSince: null,
      lastActiveAt: null,
    };
  }

  // 统计相关的私有方法
  private async getBasicStats(_userId: string): Promise<{
    storiesCount: number;
    charactersCount: number;
    lightedCharactersCount: number;
    bookmarksCount: number;
    timelineEventsCount: number;
    referenceCollectionsCount: number;
  }> {
    // 实现基础统计逻辑
    return {
      storiesCount: 0,
      charactersCount: 0,
      lightedCharactersCount: 0,
      bookmarksCount: 0,
      timelineEventsCount: 0,
      referenceCollectionsCount: 0,
    };
  }

  private async getSocialStats(_userId: string): Promise<{
    followersCount: number;
    followingCount: number;
    friendsCount: number;
    mutualFollowsCount: number;
  }> {
    // 实现社交统计逻辑
    return {
      followersCount: 0,
      followingCount: 0,
      friendsCount: 0,
      mutualFollowsCount: 0,
    };
  }

  private async getInteractionStats(_userId: string): Promise<{
    totalLikesReceived: number;
    totalCommentsReceived: number;
    totalViewsReceived: number;
    totalSharesReceived: number;
    averageLikesPerStory: number;
    averageViewsPerStory: number;
  }> {
    // 实现互动统计逻辑
    return {
      totalLikesReceived: 0,
      totalCommentsReceived: 0,
      totalViewsReceived: 0,
      totalSharesReceived: 0,
      averageLikesPerStory: 0,
      averageViewsPerStory: 0,
    };
  }

  private async getCreationStats(_userId: string): Promise<{
    storiesThisMonth: number;
    charactersThisMonth: number;
    timelineEventsThisMonth: number;
    mostActiveMonth: { month: string; storiesCount: number };
    longestStoryStreak: number;
    currentStoryStreak: number;
  }> {
    // 实现创作统计逻辑
    return {
      storiesThisMonth: 0,
      charactersThisMonth: 0,
      timelineEventsThisMonth: 0,
      mostActiveMonth: { month: "", storiesCount: 0 },
      longestStoryStreak: 0,
      currentStoryStreak: 0,
    };
  }

  private async getTrendData(_userId: string): Promise<{
    monthlyCreation: Array<{
      month: string;
      storiesCount: number;
      charactersCount: number;
      timelineEventsCount: number;
    }>;
    dailyInteraction: Array<{
      date: string;
      likesReceived: number;
      viewsReceived: number;
      commentsReceived: number;
    }>;
  }> {
    // 实现趋势数据逻辑
    return {
      monthlyCreation: [],
      dailyInteraction: [],
    };
  }

  private async getAchievements(_userId: string): Promise<
    Array<{
      id: string;
      title: string;
      description: string;
      icon: string;
      achievedAt: Date;
      category: string;
    }>
  > {
    // 实现成就数据逻辑
    return [];
  }

  private async getAccountInfo(_userId: string): Promise<{
    memberSince: Date;
    lastActiveAt?: Date;
    totalDaysActive: number;
    averageDailyActivity: number;
  }> {
    // 实现账户信息逻辑
    return {
      memberSince: new Date(),
      lastActiveAt: new Date(),
      totalDaysActive: 0,
      averageDailyActivity: 0,
    };
  }
}
