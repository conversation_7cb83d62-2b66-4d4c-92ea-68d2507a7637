module.exports = {
  parser: '@typescript-eslint/parser',
  parserOptions: {
    project: ['tsconfig.json', 'tsconfig.test.json'],
    tsconfigRootDir: __dirname,
    sourceType: 'module',
    // 🚀 性能优化：并行解析
    EXPERIMENTAL_useSourceOfProjectReferenceRedirect: true,
  },
  plugins: ['@typescript-eslint/eslint-plugin'],
  extends: [
    'plugin:@typescript-eslint/recommended',
    'plugin:prettier/recommended',
  ],
  root: true,
  env: {
    node: true,
    jest: true,
  },
  ignorePatterns: [
    '.eslintrc.js', 
    'templates/**/*', 
    'docs/**/*',
    'node_modules/**/*',
    'dist/**/*',
    'coverage/**/*',
    'test-reports/**/*',
    '*.js',
    '*.d.ts'
  ],
  rules: {
    '@typescript-eslint/interface-name-prefix': 'off',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/no-explicit-any': 'error',
    '@typescript-eslint/no-unused-vars': ['error', { 
      'argsIgnorePattern': '^_',
      'varsIgnorePattern': '^_',
      'destructuredArrayIgnorePattern': '^_'
    }],
    '@typescript-eslint/prefer-nullish-coalescing': 'off',
    '@typescript-eslint/prefer-optional-chain': 'error',
    '@typescript-eslint/strict-boolean-expressions': 'off',
    
    // 🔧 新增规则：规范测试代码
    '@typescript-eslint/ban-ts-comment': ['error', {
      'ts-expect-error': 'allow-with-description',
      'ts-ignore': 'allow-with-description',
      'ts-nocheck': false,
      'ts-check': false,
      minimumDescriptionLength: 10,
    }],
    
    // 🔧 Import规则优化
    '@typescript-eslint/consistent-type-imports': ['error', {
      prefer: 'type-imports',
      disallowTypeAnnotations: false
    }],
  },
  overrides: [
    {
      // 🧪 测试文件专用规则
      files: ['**/*.spec.ts', '**/*.test.ts', '**/test/**/*.ts'],
      rules: {
        // 测试文件中允许使用any，但需要eslint-disable注释
        '@typescript-eslint/no-explicit-any': ['error', {
          ignoreRestArgs: true,
        }],
        // 测试文件中允许未使用的变量（用于依赖注入）
        '@typescript-eslint/no-unused-vars': ['error', {
          'argsIgnorePattern': '^_',
          'varsIgnorePattern': '^(mock|Mock|test|Test|spec|Spec|jest)',
          'destructuredArrayIgnorePattern': '^_',
          'ignoreRestSiblings': true
        }],
        // 测试文件中允许空函数
        '@typescript-eslint/no-empty-function': 'off',
        // 测试文件中允许非空断言
        '@typescript-eslint/no-non-null-assertion': 'off',
        // 测试文件中允许魔法数字
        '@typescript-eslint/no-magic-numbers': 'off',
      }
    },
    {
      // 🏗️ 模块文件专用规则
      files: ['**/*.module.ts'],
      rules: {
        '@typescript-eslint/no-unused-vars': ['error', {
          'argsIgnorePattern': '^_',
          'varsIgnorePattern': '^_',
          'ignoreRestSiblings': true
        }],
      }
    }
  ],
};