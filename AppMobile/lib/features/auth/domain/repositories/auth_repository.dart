import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/user_entity.dart';
import '../entities/auth_token.dart';

/// 认证仓库接口
abstract class AuthRepository {
  /// 发送短信验证码
  Future<Either<Failure, void>> sendSmsCode(String phone);

  /// 手机号登录
  Future<Either<Failure, (UserEntity, AuthToken)>> loginWithPhone({
    required String phone,
    required String code,
  });

  /// 邮箱登录
  Future<Either<Failure, (UserEntity, AuthToken)>> loginWithEmail({
    required String email,
    required String password,
  });

  /// 刷新令牌
  Future<Either<Failure, AuthToken>> refreshToken(String refreshToken);

  /// 退出登录
  Future<Either<Failure, void>> logout();

  /// 获取当前用户
  Future<Either<Failure, UserEntity?>> getCurrentUser();

  /// 更新用户信息
  Future<Either<Failure, UserEntity>> updateUserInfo({
    String? nickname,
    String? bio,
    String? avatar,
  });

  /// 修改密码
  Future<Either<Failure, void>> changePassword({
    required String oldPassword,
    required String newPassword,
  });

  /// 绑定手机号
  Future<Either<Failure, UserEntity>> bindPhone({
    required String phone,
    required String code,
  });

  /// 绑定邮箱
  Future<Either<Failure, UserEntity>> bindEmail({
    required String email,
    required String password,
  });

  /// 保存认证信息
  Future<Either<Failure, void>> saveAuthData({
    required UserEntity user,
    required AuthToken token,
  });

  /// 清除认证信息
  Future<Either<Failure, void>> clearAuthData();

  /// 获取保存的令牌
  Future<Either<Failure, AuthToken?>> getSavedToken();

  /// 检查是否已登录
  Future<Either<Failure, bool>> isLoggedIn();
}