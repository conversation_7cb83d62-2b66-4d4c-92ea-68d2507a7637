import { IsEnum, IsString, IsOptional, MaxLength } from "class-validator";
import { ReportStatus, ReportSeverity } from "../entities/report.entity";

/**
 * 更新举报DTO（用于管理员审核）
 */
export class UpdateReportDto {
  @IsOptional()
  @IsEnum(ReportStatus, {
    message: "处理状态必须是有效的选项",
  })
  status?: ReportStatus;

  @IsOptional()
  @IsEnum(ReportSeverity, {
    message: "严重程度必须是有效的选项",
  })
  severity?: ReportSeverity;

  @IsOptional()
  @IsString({
    message: "审核说明必须是字符串",
  })
  @MaxLength(500, {
    message: "审核说明不能超过500个字符",
  })
  reviewNote?: string;
}
