/**
 * CharactersService 单元测试
 * 测试人物服务的核心业务逻辑，包括人物管理、点亮状态维护等
 */

import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import type { Repository } from "typeorm";
import {
  NotFoundException,
  ForbiddenException,
  BadRequestException,
  ConflictException,
} from "@nestjs/common";
import { CharactersService } from "./characters.service";
import { Character } from "./entities/character.entity";
import { CharacterLighting } from "./entities/character-lighting.entity";
import { User } from "../users/entities/user.entity";
import { Story } from "../stories/entities/story.entity";
import type { CreateCharacterDto, UpdateCharacterDto } from "./dto";

describe("CharactersService", () => {
  let service: CharactersService;
  let characterRepository: Repository<Character>;
  let userRepository: Repository<User>;
  let storyRepository: Repository<Story>;

  // Mock数据
  const mockUser = {
    id: "user-id",
    userNumber: "123456",
    nickname: "测试用户",
    username: "testuser",
    phone: "13800138000",
    email: "<EMAIL>",
    avatarUrl: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    isActive: true,
    status: "active",
  };

  const mockStory = {
    id: "story-id",
    title: "测试故事",
    content: "这是一个测试故事的内容",
    description: "故事描述",
    userId: "user-id",
    themeId: "theme-id",
    status: 1,
    viewCount: 0,
    likeCount: 0,
    commentCount: 0,
    shareCount: 0,
    isPublic: true,
    tags: ["测试", "故事"],
    createdAt: new Date(),
    updatedAt: new Date(),
    publishedAt: new Date(),
  };

  const mockCharacter = {
    id: "character-id",
    name: "测试角色",
    description: "这是一个测试角色",
    relationship: "朋友",
    gender: "男",
    customRelationship: "",
    creatorId: "user-id",
    avatarUrl: "",
    isLighted: false,
    lightingCount: 0,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    creator: mockUser,
    stories: [mockStory],
    lightings: [],
  };

  const mockCharacterLighting = {
    id: "lighting-id",
    characterId: "character-id",
    lighterUserId: "lighter-user-id",
    confirmedAt: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
    character: mockCharacter,
    lighterUser: mockUser,
  };

  beforeEach(async () => {
    // Mock Repository - 企业级命名规范
    const mockCharacterRepositoryMethods = {
      create: jest.fn(),
      save: jest.fn(),
      findOne: jest.fn(),
      find: jest.fn(),
      update: jest.fn(),
      remove: jest.fn(),
      createQueryBuilder: jest.fn(),
      count: jest.fn(),
    };

    const mockCharacterLightingRepositoryMethods = {
      find: jest.fn(),
      count: jest.fn(),
      createQueryBuilder: jest.fn(),
    };

    const mockUserRepositoryMethods = {
      findOne: jest.fn(),
    };

    const mockStoryRepositoryMethods = {
      findOne: jest.fn(),
      createQueryBuilder: jest.fn(),
    };

    // Mock QueryBuilder
    const mockQueryBuilder = {
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      orWhere: jest.fn().mockReturnThis(),
      leftJoinAndSelect: jest.fn().mockReturnThis(),
      innerJoin: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      addSelect: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      addOrderBy: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      take: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      offset: jest.fn().mockReturnThis(),
      getMany: jest.fn(),
      getOne: jest.fn(),
      getManyAndCount: jest.fn(),
      getCount: jest.fn(),
      relation: jest.fn().mockReturnThis(),
      of: jest.fn().mockReturnThis(),
      add: jest.fn().mockReturnThis(),
      remove: jest.fn().mockReturnThis(),
    };

    mockCharacterRepositoryMethods.createQueryBuilder.mockReturnValue(
      mockQueryBuilder,
    );
    mockCharacterLightingRepositoryMethods.createQueryBuilder.mockReturnValue(
      mockQueryBuilder,
    );
    mockStoryRepositoryMethods.createQueryBuilder.mockReturnValue(
      mockQueryBuilder,
    );

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CharactersService,
        {
          provide: getRepositoryToken(Character),
          useValue: mockCharacterRepositoryMethods,
        },
        {
          provide: getRepositoryToken(CharacterLighting),
          useValue: mockCharacterLightingRepositoryMethods,
        },
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepositoryMethods,
        },
        {
          provide: getRepositoryToken(Story),
          useValue: mockStoryRepositoryMethods,
        },
      ],
    }).compile();

    service = module.get<CharactersService>(CharactersService);
    characterRepository = module.get<Repository<Character>>(
      getRepositoryToken(Character),
    );
    userRepository = module.get<Repository<User>>(getRepositoryToken(User));
    storyRepository = module.get<Repository<Story>>(getRepositoryToken(Story));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  describe("createCharacter", () => {
    const createCharacterDto: CreateCharacterDto = {
      name: "新角色",
      description: "这是一个新角色",
      relationship: "朋友",
      gender: "女",
    };

    it("should create a character successfully", async () => {
      (userRepository.findOne as jest.Mock).mockResolvedValue(mockUser);
      (characterRepository.findOne as jest.Mock).mockResolvedValue(null);
      (characterRepository.create as jest.Mock).mockReturnValue(mockCharacter);
      (characterRepository.save as jest.Mock).mockResolvedValue(mockCharacter);
      (characterRepository.findOne as jest.Mock)
        .mockResolvedValueOnce(null)
        .mockResolvedValueOnce(mockCharacter);

      const result = await service.createCharacter(
        "user-id",
        createCharacterDto,
      );

      expect(result).toEqual(mockCharacter);
      expect(userRepository.findOne).toHaveBeenCalledWith({
        where: { id: "user-id" },
      });
      expect(characterRepository.create).toHaveBeenCalledWith({
        ...createCharacterDto,
        creatorId: "user-id",
      });
      expect(characterRepository.save).toHaveBeenCalled();
    });

    it("should throw NotFoundException when creator not found", async () => {
      (userRepository.findOne as jest.Mock).mockResolvedValue(null);

      await expect(
        service.createCharacter("nonexistent-user", createCharacterDto),
      ).rejects.toThrow(NotFoundException);
      await expect(
        service.createCharacter("nonexistent-user", createCharacterDto),
      ).rejects.toThrow("创建者不存在");
    });

    it("should throw ConflictException when character name already exists", async () => {
      (userRepository.findOne as jest.Mock).mockResolvedValue(mockUser);
      (characterRepository.findOne as jest.Mock).mockResolvedValue(
        mockCharacter,
      );

      await expect(
        service.createCharacter("user-id", createCharacterDto),
      ).rejects.toThrow(ConflictException);
      await expect(
        service.createCharacter("user-id", createCharacterDto),
      ).rejects.toThrow("该人物名称已存在");
    });
  });

  describe("updateCharacter", () => {
    const updateCharacterDto: UpdateCharacterDto = {
      name: "更新的角色",
      description: "更新的描述",
      relationship: "家人",
      gender: "男",
    };

    it("should update character successfully", async () => {
      const updatedCharacter = {
        ...mockCharacter,
        ...updateCharacterDto,
      };

      (characterRepository.findOne as jest.Mock)
        .mockResolvedValueOnce(mockCharacter) // 第一次调用用于更新验证
        .mockResolvedValueOnce(null) // 第二次调用用于名称唯一性检查
        .mockResolvedValueOnce(updatedCharacter); // 第三次调用用于findById返回
      (characterRepository.update as jest.Mock).mockResolvedValue({
        affected: 1,
      });

      const result = await service.updateCharacter(
        "character-id",
        updateCharacterDto,
        "user-id",
      );

      expect(result.name).toBe("更新的角色");
      expect(characterRepository.update).toHaveBeenCalledWith(
        "character-id",
        updateCharacterDto,
      );
    });

    it("should throw NotFoundException when character not found", async () => {
      (characterRepository.findOne as jest.Mock).mockResolvedValue(null);

      await expect(
        service.updateCharacter(
          "nonexistent-character",
          updateCharacterDto,
          "user-id",
        ),
      ).rejects.toThrow(NotFoundException);
      await expect(
        service.updateCharacter(
          "nonexistent-character",
          updateCharacterDto,
          "user-id",
        ),
      ).rejects.toThrow("人物不存在");
    });

    it("should throw ForbiddenException when user is not the creator", async () => {
      const otherUserCharacter = {
        ...mockCharacter,
        creatorId: "other-user-id",
      };
      (characterRepository.findOne as jest.Mock).mockResolvedValue(
        otherUserCharacter,
      );

      await expect(
        service.updateCharacter("character-id", updateCharacterDto, "user-id"),
      ).rejects.toThrow(ForbiddenException);
      await expect(
        service.updateCharacter("character-id", updateCharacterDto, "user-id"),
      ).rejects.toThrow("无权编辑此人物");
    });

    it("should throw ConflictException when updating name to existing name", async () => {
      const existingCharacter = { ...mockCharacter, name: "更新的角色" };
      (characterRepository.findOne as jest.Mock)
        .mockResolvedValueOnce(mockCharacter) // 第一次调用：查找要更新的人物
        .mockResolvedValueOnce(existingCharacter) // 第二次调用：检查名称唯一性
        .mockResolvedValueOnce(mockCharacter) // 第三次调用：用于第二次测试执行
        .mockResolvedValueOnce(existingCharacter); // 第四次调用：用于第二次测试执行

      await expect(
        service.updateCharacter("character-id", updateCharacterDto, "user-id"),
      ).rejects.toThrow(ConflictException);
      await expect(
        service.updateCharacter("character-id", updateCharacterDto, "user-id"),
      ).rejects.toThrow("该人物名称已存在");
    });
  });

  describe("deleteCharacter", () => {
    it("should delete character successfully", async () => {
      (characterRepository.findOne as jest.Mock).mockResolvedValue(
        mockCharacter,
      );
      (characterRepository.update as jest.Mock).mockResolvedValue({
        affected: 1,
      });

      await service.deleteCharacter("character-id", "user-id");

      expect(characterRepository.update).toHaveBeenCalledWith("character-id", {
        isActive: false,
      });
    });

    it("should throw NotFoundException when character not found", async () => {
      (characterRepository.findOne as jest.Mock).mockResolvedValue(null);

      await expect(
        service.deleteCharacter("nonexistent-character", "user-id"),
      ).rejects.toThrow(NotFoundException);
      await expect(
        service.deleteCharacter("nonexistent-character", "user-id"),
      ).rejects.toThrow("人物不存在");
    });

    it("should throw ForbiddenException when user is not the creator", async () => {
      const otherUserCharacter = {
        ...mockCharacter,
        creatorId: "other-user-id",
      };
      (characterRepository.findOne as jest.Mock).mockResolvedValue(
        otherUserCharacter,
      );

      await expect(
        service.deleteCharacter("character-id", "user-id"),
      ).rejects.toThrow(ForbiddenException);
      await expect(
        service.deleteCharacter("character-id", "user-id"),
      ).rejects.toThrow("无权删除此人物");
    });

    it("should throw BadRequestException when character is already lighted", async () => {
      const lightedCharacter = { ...mockCharacter, isLighted: true };
      (characterRepository.findOne as jest.Mock).mockResolvedValue(
        lightedCharacter,
      );

      await expect(
        service.deleteCharacter("character-id", "user-id"),
      ).rejects.toThrow(BadRequestException);
      await expect(
        service.deleteCharacter("character-id", "user-id"),
      ).rejects.toThrow("已点亮的人物不能删除，请先取消点亮");
    });
  });

  describe("findById", () => {
    it("should return character when found", async () => {
      (characterRepository.findOne as jest.Mock).mockResolvedValue(
        mockCharacter,
      );

      const result = await service.findById("character-id");

      expect(result).toEqual(mockCharacter);
      expect(characterRepository.findOne).toHaveBeenCalledWith({
        where: { id: "character-id", isActive: true },
        relations: ["creator", "stories", "lightings", "lightings.lighterUser"],
      });
    });

    it("should throw NotFoundException when character not found", async () => {
      (characterRepository.findOne as jest.Mock).mockResolvedValue(null);

      await expect(service.findById("nonexistent-character")).rejects.toThrow(
        NotFoundException,
      );
      await expect(service.findById("nonexistent-character")).rejects.toThrow(
        "人物不存在",
      );
    });
  });

  describe("validateCharacterName", () => {
    it("should return true when name is available", async () => {
      (characterRepository.findOne as jest.Mock).mockResolvedValue(null);

      const result = await service.validateCharacterName(
        "user-id",
        "新角色名称",
      );

      expect(result).toBe(true);
      expect(characterRepository.findOne).toHaveBeenCalledWith({
        where: {
          creatorId: "user-id",
          name: "新角色名称",
          isActive: true,
        },
      });
    });

    it("should return false when name is already taken", async () => {
      (characterRepository.findOne as jest.Mock).mockResolvedValue(
        mockCharacter,
      );

      const result = await service.validateCharacterName("user-id", "测试角色");

      expect(result).toBe(false);
    });
  });

  describe("addCharacterToStory", () => {
    it("should add character to story successfully", async () => {
      (storyRepository.findOne as jest.Mock).mockResolvedValue(mockStory);
      (characterRepository.findOne as jest.Mock).mockResolvedValue(
        mockCharacter,
      );
      const mockQueryBuilder = {
        relation: jest.fn().mockReturnThis(),
        of: jest.fn().mockReturnThis(),
        add: jest.fn().mockResolvedValue(undefined),
      };
      (storyRepository.createQueryBuilder as jest.Mock).mockReturnValue(
        mockQueryBuilder,
      );

      await service.addCharacterToStory("story-id", "character-id", "user-id");

      expect(storyRepository.findOne).toHaveBeenCalledWith({
        where: { id: "story-id" },
      });
      expect(characterRepository.findOne).toHaveBeenCalledWith({
        where: { id: "character-id", creatorId: "user-id", isActive: true },
      });
      expect(mockQueryBuilder.add).toHaveBeenCalledWith("character-id");
    });

    it("should throw NotFoundException when story not found", async () => {
      (storyRepository.findOne as jest.Mock).mockResolvedValue(null);

      await expect(
        service.addCharacterToStory(
          "nonexistent-story",
          "character-id",
          "user-id",
        ),
      ).rejects.toThrow(NotFoundException);
      await expect(
        service.addCharacterToStory(
          "nonexistent-story",
          "character-id",
          "user-id",
        ),
      ).rejects.toThrow("故事不存在");
    });

    it("should throw ForbiddenException when user cannot modify story", async () => {
      const otherUserStory = { ...mockStory, userId: "other-user-id" };
      (storyRepository.findOne as jest.Mock).mockResolvedValue(otherUserStory);

      await expect(
        service.addCharacterToStory("story-id", "character-id", "user-id"),
      ).rejects.toThrow(ForbiddenException);
      await expect(
        service.addCharacterToStory("story-id", "character-id", "user-id"),
      ).rejects.toThrow("无权修改此故事");
    });

    it("should throw NotFoundException when character not found or no access", async () => {
      (storyRepository.findOne as jest.Mock).mockResolvedValue(mockStory);
      (characterRepository.findOne as jest.Mock).mockResolvedValue(null);

      await expect(
        service.addCharacterToStory("story-id", "character-id", "user-id"),
      ).rejects.toThrow(NotFoundException);
      await expect(
        service.addCharacterToStory("story-id", "character-id", "user-id"),
      ).rejects.toThrow("人物不存在或无权访问");
    });
  });

  describe("removeCharacterFromStory", () => {
    it("should remove character from story successfully", async () => {
      (storyRepository.findOne as jest.Mock).mockResolvedValue(mockStory);
      const mockQueryBuilder = {
        relation: jest.fn().mockReturnThis(),
        of: jest.fn().mockReturnThis(),
        remove: jest.fn().mockResolvedValue(undefined),
      };
      (storyRepository.createQueryBuilder as jest.Mock).mockReturnValue(
        mockQueryBuilder,
      );

      await service.removeCharacterFromStory(
        "story-id",
        "character-id",
        "user-id",
      );

      expect(storyRepository.findOne).toHaveBeenCalledWith({
        where: { id: "story-id" },
      });
      expect(mockQueryBuilder.remove).toHaveBeenCalledWith("character-id");
    });

    it("should throw NotFoundException when story not found", async () => {
      (storyRepository.findOne as jest.Mock).mockResolvedValue(null);

      await expect(
        service.removeCharacterFromStory(
          "nonexistent-story",
          "character-id",
          "user-id",
        ),
      ).rejects.toThrow(NotFoundException);
      await expect(
        service.removeCharacterFromStory(
          "nonexistent-story",
          "character-id",
          "user-id",
        ),
      ).rejects.toThrow("故事不存在");
    });

    it("should throw ForbiddenException when user cannot modify story", async () => {
      const otherUserStory = { ...mockStory, userId: "other-user-id" };
      (storyRepository.findOne as jest.Mock).mockResolvedValue(otherUserStory);

      await expect(
        service.removeCharacterFromStory("story-id", "character-id", "user-id"),
      ).rejects.toThrow(ForbiddenException);
      await expect(
        service.removeCharacterFromStory("story-id", "character-id", "user-id"),
      ).rejects.toThrow("无权修改此故事");
    });
  });

  describe("getUserCharacters", () => {
    it("should return user characters with pagination", async () => {
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[mockCharacter], 1]),
      };

      (characterRepository.createQueryBuilder as jest.Mock).mockReturnValue(
        mockQueryBuilder,
      );

      const result = await service.getUserCharacters("user-id", {
        page: 1,
        limit: 10,
      });

      expect(result).toEqual({
        data: expect.any(Array),
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      });
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        "character.creatorId = :userId",
        { userId: "user-id" },
      );
    });
  });

  describe("getStoryCharacters", () => {
    it("should return story characters with pagination", async () => {
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        innerJoin: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[mockCharacter], 1]),
      };

      (characterRepository.createQueryBuilder as jest.Mock).mockReturnValue(
        mockQueryBuilder,
      );

      const result = await service.getStoryCharacters("story-id", {
        page: 1,
        limit: 10,
      });

      expect(result).toEqual({
        data: expect.any(Array),
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      });
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        "story.id = :storyId",
        { storyId: "story-id" },
      );
    });
  });

  describe("updateLightingStatus", () => {
    it("should update lighting status successfully", async () => {
      const characterWithLightings = {
        ...mockCharacter,
        lightings: [
          { ...mockCharacterLighting, confirmedAt: new Date() },
          { ...mockCharacterLighting, confirmedAt: null },
        ],
      };

      (characterRepository.findOne as jest.Mock).mockResolvedValue(
        characterWithLightings,
      );
      (characterRepository.update as jest.Mock).mockResolvedValue({
        affected: 1,
      });

      await service.updateLightingStatus("character-id");

      expect(characterRepository.update).toHaveBeenCalledWith("character-id", {
        isLighted: true,
        lightingCount: 1,
      });
    });

    it("should throw NotFoundException when character not found", async () => {
      (characterRepository.findOne as jest.Mock).mockResolvedValue(null);

      await expect(
        service.updateLightingStatus("nonexistent-character"),
      ).rejects.toThrow(NotFoundException);
      await expect(
        service.updateLightingStatus("nonexistent-character"),
      ).rejects.toThrow("人物不存在");
    });
  });

  describe("getUserCharacterStatistics", () => {
    it("should return user character statistics", async () => {
      const characters = [
        {
          ...mockCharacter,
          isLighted: true,
          lightingCount: 2,
          relationship: "朋友",
          gender: "男",
        },
        {
          ...mockCharacter,
          isLighted: false,
          lightingCount: 0,
          relationship: "家人",
          gender: "女",
        },
      ];

      (characterRepository.find as jest.Mock).mockResolvedValue(characters);

      const result = await service.getUserCharacterStatistics("user-id");

      expect(result).toEqual({
        totalCharacters: 2,
        lightedCharacters: 1,
        unlightedCharacters: 1,
        totalLightings: 2,
        lightingRate: 50,
        relationshipDistribution: {
          朋友: 1,
          家人: 1,
        },
        genderDistribution: {
          男: 1,
          女: 1,
        },
      });
    });
  });

  describe("searchCharacters", () => {
    it("should search characters with query", async () => {
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[mockCharacter], 1]),
      };

      (characterRepository.createQueryBuilder as jest.Mock).mockReturnValue(
        mockQueryBuilder,
      );

      const result = await service.searchCharacters(
        { search: "测试", page: 1, limit: 10 },
        "user-id",
      );

      expect(result).toEqual({
        data: expect.any(Array),
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      });
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "(character.name ILIKE :search OR character.description ILIKE :search)",
        { search: "%测试%" },
      );
    });
  });

  describe("getPopularCharacters", () => {
    it("should return popular characters", async () => {
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        addOrderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[mockCharacter], 1]),
      };

      (characterRepository.createQueryBuilder as jest.Mock).mockReturnValue(
        mockQueryBuilder,
      );

      const result = await service.getPopularCharacters({ page: 1, limit: 10 });

      expect(result).toEqual({
        data: expect.any(Array),
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      });
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        "character.isLighted = true",
      );
      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith(
        "character.lightingCount",
        "DESC",
      );
    });
  });

  describe("getAccessibleCharacters", () => {
    it("should return accessible characters for user", async () => {
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[mockCharacter], 1]),
      };

      (characterRepository.createQueryBuilder as jest.Mock).mockReturnValue(
        mockQueryBuilder,
      );

      const result = await service.getAccessibleCharacters(
        { page: 1, limit: 10 },
        "user-id",
      );

      expect(result).toEqual({
        data: expect.any(Array),
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      });
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        "(character.creatorId = :currentUserId OR character.isLighted = true)",
        { currentUserId: "user-id" },
      );
    });

    it("should handle empty query parameters", async () => {
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[], 0]),
      };

      (characterRepository.createQueryBuilder as jest.Mock).mockReturnValue(
        mockQueryBuilder,
      );

      const result = await service.getAccessibleCharacters({}, "user-id");

      expect(result.total).toBe(0);
      expect(result.data).toEqual([]);
    });
  });

  describe("getLightedCharacters", () => {
    it("should return user's lighted characters with pagination", async () => {
      (userRepository.findOne as jest.Mock).mockResolvedValue(mockUser);

      const mockQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        innerJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        addOrderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[mockCharacter], 1]),
      };

      (characterRepository.createQueryBuilder as jest.Mock).mockReturnValue(
        mockQueryBuilder,
      );

      const result = await service.getLightedCharacters("user-id", {
        page: 1,
        limit: 10,
      });

      expect(result).toEqual({
        data: expect.any(Array),
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      });

      // 验证查询构建器调用
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith(
        "character.creator",
        "creator",
      );
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith(
        "character.stories",
        "stories",
      );
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith(
        "character.lightings",
        "lightings",
      );
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith(
        "lightings.lighterUser",
        "lighterUser",
      );

      expect(mockQueryBuilder.innerJoin).toHaveBeenCalledWith(
        "character_lightings",
        "userLighting",
        "userLighting.character_id = character.id",
      );

      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        "character.isActive = true",
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "userLighting.lighter_user_id = :userId",
        { userId: "user-id" },
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "userLighting.status = :status",
        { status: "active" },
      );

      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith(
        "userLighting.confirmed_at",
        "DESC",
      );
      expect(mockQueryBuilder.addOrderBy).toHaveBeenCalledWith(
        "character.name",
        "ASC",
      );
    });

    it("should apply search filter correctly", async () => {
      (userRepository.findOne as jest.Mock).mockResolvedValue(mockUser);

      const mockQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        innerJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        addOrderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[mockCharacter], 1]),
      };

      (characterRepository.createQueryBuilder as jest.Mock).mockReturnValue(
        mockQueryBuilder,
      );

      await service.getLightedCharacters("user-id", {
        page: 1,
        limit: 10,
        search: "测试",
      });

      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "(character.name ILIKE :search OR character.description ILIKE :search)",
        { search: "%测试%" },
      );
    });

    it("should apply gender filter correctly", async () => {
      (userRepository.findOne as jest.Mock).mockResolvedValue(mockUser);

      const mockQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        innerJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        addOrderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[mockCharacter], 1]),
      };

      (characterRepository.createQueryBuilder as jest.Mock).mockReturnValue(
        mockQueryBuilder,
      );

      await service.getLightedCharacters("user-id", {
        page: 1,
        limit: 10,
        gender: "男",
      });

      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "character.gender = :gender",
        {
          gender: "男",
        },
      );
    });

    it("should apply relationship filter correctly", async () => {
      (userRepository.findOne as jest.Mock).mockResolvedValue(mockUser);

      const mockQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        innerJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        addOrderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[mockCharacter], 1]),
      };

      (characterRepository.createQueryBuilder as jest.Mock).mockReturnValue(
        mockQueryBuilder,
      );

      await service.getLightedCharacters("user-id", {
        page: 1,
        limit: 10,
        relationship: "朋友",
      });

      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "character.relationship = :relationship",
        {
          relationship: "朋友",
        },
      );
    });

    it("should throw NotFoundException when user not found", async () => {
      (userRepository.findOne as jest.Mock).mockResolvedValue(null);

      await expect(
        service.getLightedCharacters("nonexistent-user", {
          page: 1,
          limit: 10,
        }),
      ).rejects.toThrow(NotFoundException);
      await expect(
        service.getLightedCharacters("nonexistent-user", {
          page: 1,
          limit: 10,
        }),
      ).rejects.toThrow("用户不存在");
    });

    it("should handle empty results", async () => {
      (userRepository.findOne as jest.Mock).mockResolvedValue(mockUser);

      const mockQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        innerJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        addOrderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[], 0]),
      };

      (characterRepository.createQueryBuilder as jest.Mock).mockReturnValue(
        mockQueryBuilder,
      );

      const result = await service.getLightedCharacters("user-id", {
        page: 1,
        limit: 10,
      });

      expect(result).toEqual({
        data: [],
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0,
        hasNext: false,
        hasPrev: false,
      });
    });

    it("should handle default pagination parameters", async () => {
      (userRepository.findOne as jest.Mock).mockResolvedValue(mockUser);

      const mockQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        innerJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        addOrderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[mockCharacter], 1]),
      };

      (characterRepository.createQueryBuilder as jest.Mock).mockReturnValue(
        mockQueryBuilder,
      );

      await service.getLightedCharacters("user-id", {});

      // 验证默认分页参数
      expect(mockQueryBuilder.skip).toHaveBeenCalledWith(0); // (1-1) * 10
      expect(mockQueryBuilder.take).toHaveBeenCalledWith(10);
    });

    it("should apply complex filters correctly", async () => {
      (userRepository.findOne as jest.Mock).mockResolvedValue(mockUser);

      const mockQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        innerJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        addOrderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[mockCharacter], 1]),
      };

      (characterRepository.createQueryBuilder as jest.Mock).mockReturnValue(
        mockQueryBuilder,
      );

      await service.getLightedCharacters("user-id", {
        page: 2,
        limit: 5,
        search: "测试@#$%^&*()",
        gender: "女",
        relationship: "家人",
      });

      // 验证分页参数
      expect(mockQueryBuilder.skip).toHaveBeenCalledWith(5); // (2-1) * 5
      expect(mockQueryBuilder.take).toHaveBeenCalledWith(5);

      // 验证所有过滤条件
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "(character.name ILIKE :search OR character.description ILIKE :search)",
        { search: "%测试@#$%^&*()%" },
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "character.gender = :gender",
        {
          gender: "女",
        },
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "character.relationship = :relationship",
        {
          relationship: "家人",
        },
      );
    });
  });

  describe("Edge Cases and Error Handling", () => {
    it("should handle updateCharacter with empty updateData", async () => {
      (characterRepository.findOne as jest.Mock).mockResolvedValue(
        mockCharacter,
      );
      (characterRepository.update as jest.Mock).mockResolvedValue({
        affected: 1,
      });

      const result = await service.updateCharacter(
        "character-id",
        {},
        "user-id",
      );

      expect(result).toBeDefined();
      expect(characterRepository.update).toHaveBeenCalledWith(
        "character-id",
        {},
      );
    });

    it("should handle getUserCharacterStatistics with no characters", async () => {
      (characterRepository.find as jest.Mock).mockResolvedValue([]);

      const result = await service.getUserCharacterStatistics("user-id");

      expect(result).toEqual({
        totalCharacters: 0,
        lightedCharacters: 0,
        unlightedCharacters: 0,
        totalLightings: 0,
        lightingRate: 0,
        relationshipDistribution: {},
        genderDistribution: {},
      });
    });

    it("should handle updateLightingStatus with character having multiple lightings", async () => {
      const characterWithMultipleLightings = {
        ...mockCharacter,
        lightings: [
          { ...mockCharacterLighting, confirmedAt: new Date() },
          {
            ...mockCharacterLighting,
            id: "lighting-2",
            confirmedAt: new Date(),
          },
          { ...mockCharacterLighting, id: "lighting-3", confirmedAt: null },
        ],
      };

      (characterRepository.findOne as jest.Mock).mockResolvedValue(
        characterWithMultipleLightings,
      );
      (characterRepository.update as jest.Mock).mockResolvedValue({
        affected: 1,
      });

      await service.updateLightingStatus("character-id");

      expect(characterRepository.update).toHaveBeenCalledWith("character-id", {
        isLighted: true,
        lightingCount: 2,
      });
    });

    it("should handle getUserCharacterStatistics with mixed character data", async () => {
      const mixedCharacters = [
        {
          ...mockCharacter,
          relationship: "朋友",
          gender: "男",
          isLighted: true,
          lightingCount: 2,
        },
        {
          ...mockCharacter,
          id: "char-2",
          relationship: "朋友",
          gender: "女",
          isLighted: false,
          lightingCount: 0,
        },
        {
          ...mockCharacter,
          id: "char-3",
          relationship: "家人",
          gender: "男",
          isLighted: true,
          lightingCount: 1,
        },
        {
          ...mockCharacter,
          id: "char-4",
          relationship: null,
          gender: null,
          isLighted: false,
          lightingCount: 0,
        },
      ];

      (characterRepository.find as jest.Mock).mockResolvedValue(
        mixedCharacters,
      );

      const result = await service.getUserCharacterStatistics("user-id");

      expect(result.totalCharacters).toBe(4);
      expect(result.lightedCharacters).toBe(2);
      expect(result.unlightedCharacters).toBe(2);
      expect(result.totalLightings).toBe(3);
      expect(result.lightingRate).toBe(50);
      expect(result.relationshipDistribution).toEqual({
        朋友: 2,
        家人: 1,
        未知: 1,
      });
      expect(result.genderDistribution).toEqual({
        男: 2,
        女: 1,
        未知: 1,
      });
    });

    it("should handle deleteCharacter when character is lighted", async () => {
      const lightedCharacter = { ...mockCharacter, isLighted: true };
      (characterRepository.findOne as jest.Mock).mockResolvedValue(
        lightedCharacter,
      );

      await expect(
        service.deleteCharacter("character-id", "user-id"),
      ).rejects.toThrow(BadRequestException);
      await expect(
        service.deleteCharacter("character-id", "user-id"),
      ).rejects.toThrow("已点亮的人物不能删除");
    });

    it("should handle searchCharacters with special characters in search term", async () => {
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[mockCharacter], 1]),
      };

      (characterRepository.createQueryBuilder as jest.Mock).mockReturnValue(
        mockQueryBuilder,
      );

      const result = await service.searchCharacters(
        {
          search: "测试@#$%^&*()",
          page: 1,
          limit: 10,
        },
        "user-1",
      );

      expect(result.total).toBe(1);
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "(character.name ILIKE :search OR character.description ILIKE :search)",
        { search: "%测试@#$%^&*()%" },
      );
    });

    it("should handle validateCharacterName with case sensitivity", async () => {
      (characterRepository.findOne as jest.Mock).mockResolvedValue(null);

      const result = await service.validateCharacterName("用户ID", "测试角色");

      expect(result).toBe(true);
      expect(characterRepository.findOne).toHaveBeenCalledWith({
        where: {
          creatorId: "用户ID",
          name: "测试角色",
          isActive: true,
        },
      });
    });

    it("should handle addCharacterToStory with character already in story", async () => {
      const storyWithCharacter = {
        ...mockStory,
        characters: [mockCharacter],
      };
      (storyRepository.findOne as jest.Mock).mockResolvedValue(
        storyWithCharacter,
      );
      (characterRepository.findOne as jest.Mock).mockResolvedValue(
        mockCharacter,
      );

      const mockQueryBuilder = {
        relation: jest.fn().mockReturnThis(),
        of: jest.fn().mockReturnThis(),
        add: jest.fn().mockResolvedValue(undefined),
      };
      (storyRepository.createQueryBuilder as jest.Mock).mockReturnValue(
        mockQueryBuilder,
      );

      await service.addCharacterToStory("story-id", "character-id", "user-id");

      expect(mockQueryBuilder.add).toHaveBeenCalledWith("character-id");
    });
  });

  describe("Performance and Optimization Tests", () => {
    it("should handle large dataset in getUserCharacterStatistics", async () => {
      const largeCharacterSet = Array.from({ length: 100 }, (_, i) => ({
        ...mockCharacter,
        id: `char-${i}`,
        relationship: i % 3 === 0 ? "朋友" : i % 3 === 1 ? "家人" : "同事",
        gender: i % 2 === 0 ? "男" : "女",
        isLighted: i % 4 === 0,
        lightingCount: i % 4 === 0 ? Math.floor(Math.random() * 5) + 1 : 0,
      }));

      (characterRepository.find as jest.Mock).mockResolvedValue(
        largeCharacterSet,
      );

      const result = await service.getUserCharacterStatistics("user-id");

      expect(result.totalCharacters).toBe(100);
      expect(result.relationshipDistribution).toBeDefined();
      expect(result.genderDistribution).toBeDefined();
      expect(typeof result.lightingRate).toBe("number");
    });

    it("should handle concurrent character creation attempts", async () => {
      (userRepository.findOne as jest.Mock).mockResolvedValue(mockUser);
      (characterRepository.findOne as jest.Mock).mockResolvedValue(null);
      (characterRepository.create as jest.Mock).mockReturnValue(mockCharacter);
      (characterRepository.save as jest.Mock).mockResolvedValue(mockCharacter);

      // Mock findById 方法，因为 createCharacter 最后会调用它
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      jest.spyOn(service, "findById").mockResolvedValue(mockCharacter as any);

      const createPromises = Array.from({ length: 5 }, (_, i) =>
        service.createCharacter("user-id", {
          name: `角色${i}`,
          description: `描述${i}`,
          relationship: "朋友",
          gender: "男",
        }),
      );

      const results = await Promise.all(createPromises);

      expect(results).toHaveLength(5);
      results.forEach((result) => {
        expect(result).toBeDefined();
      });
    });
  });
});
