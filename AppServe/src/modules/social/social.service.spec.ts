/* eslint-disable @typescript-eslint/no-explicit-any, @typescript-eslint/no-unused-vars */
import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import {
  NotFoundException,
  BadRequestException,
  ConflictException,
  ForbiddenException,
} from "@nestjs/common";
import type { Repository } from "typeorm";

import { SocialService } from "./social.service";
import { UserFollow, FollowStatus } from "./entities/user-follow.entity";
import {
  FriendGroup,
  GroupType,
  GroupVisibility,
} from "./entities/friend-group.entity";
import {
  FriendGroupMember,
  MemberRole,
  MemberStatus,
} from "./entities/friend-group-member.entity";
import { User } from "../users/entities/user.entity";
import type {
  FollowUserDto,
  CreateFriendGroupDto,
  FollowListQueryDto,
  FriendGroupQueryDto,
  GroupMemberQueryDto,
} from "./dto";

describe("SocialService - 企业级单元测试", () => {
  let service: SocialService;
  let mockFollowRepository: jest.Mocked<Repository<UserFollow>>;
  let mockGroupRepository: jest.Mocked<Repository<FriendGroup>>;
  let mockGroupMemberRepository: jest.Mocked<Repository<FriendGroupMember>>;
  let mockUserRepository: jest.Mocked<Repository<User>>;

  // 测试数据工厂
  const mockUser = {
    id: "user-001",
    username: "testuser",
    nickname: "测试用户",
    email: "<EMAIL>",
    isActive: true,
  };

  const mockFollowingUser = {
    id: "user-002",
    username: "following",
    nickname: "被关注用户",
    email: "<EMAIL>",
    isActive: true,
  };

  const mockFollower = {
    id: "user-003",
    username: "follower",
    nickname: "粉丝用户",
    email: "<EMAIL>",
    isActive: true,
  };

  const mockUserFollow = {
    id: "follow-001",
    followerId: "user-001",
    followingId: "user-002",
    status: FollowStatus.ACTIVE,
    isSpecial: false,
    settings: {
      notifyOnPost: true,
      notifyOnComment: false,
      showInTimeline: true,
    },
    metadata: { source: "web" },
    createdAt: new Date(),
    follower: mockUser,
    following: mockFollowingUser,
  };

  const mockFriendGroup = {
    id: "group-001",
    name: "好友分组",
    description: "我的好友分组",
    ownerId: "user-001",
    groupType: GroupType.FRIENDS,
    visibility: GroupVisibility.PRIVATE,
    colorTag: "#FF5733",
    memberCount: 5,
    sortOrder: 1,
    isActive: true,
    permissions: {
      canViewProfile: true,
      canViewStories: true,
      canComment: true,
      canShare: false,
      canMessage: true,
    },
    metadata: { created_source: "mobile" },
    createdAt: new Date(),
    updatedAt: new Date(),
    owner: mockUser,
  };

  const mockGroupMember = {
    id: "member-001",
    groupId: "group-001",
    userId: "user-002",
    role: MemberRole.MEMBER,
    status: MemberStatus.ACTIVE,
    addedBy: "user-001",
    note: "通过共同好友添加",
    permissionOverrides: {
      canViewProfile: true,
    },
    metadata: { added_method: "manual" },
    addedAt: new Date(),
    group: mockFriendGroup,
    user: mockFollowingUser,
    adder: mockUser,
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SocialService,
        {
          provide: getRepositoryToken(UserFollow),
          useValue: {
            findOne: jest.fn(),
            find: jest.fn(),
            save: jest.fn(),
            create: jest.fn(),
            remove: jest.fn(),
            count: jest.fn(),
            createQueryBuilder: jest.fn().mockReturnValue({
              leftJoinAndSelect: jest.fn().mockReturnThis(),
              innerJoin: jest.fn().mockReturnThis(),
              where: jest.fn().mockReturnThis(),
              andWhere: jest.fn().mockReturnThis(),
              orderBy: jest.fn().mockReturnThis(),
              skip: jest.fn().mockReturnThis(),
              take: jest.fn().mockReturnThis(),
              limit: jest.fn().mockReturnThis(),
              getCount: jest.fn(),
              getMany: jest.fn(),
              getManyAndCount: jest.fn(),
            }),
          },
        },
        {
          provide: getRepositoryToken(FriendGroup),
          useValue: {
            findOne: jest.fn(),
            find: jest.fn(),
            save: jest.fn(),
            create: jest.fn(),
            update: jest.fn(),
            count: jest.fn(),
            createQueryBuilder: jest.fn().mockReturnValue({
              leftJoinAndSelect: jest.fn().mockReturnThis(),
              where: jest.fn().mockReturnThis(),
              andWhere: jest.fn().mockReturnThis(),
              orderBy: jest.fn().mockReturnThis(),
              skip: jest.fn().mockReturnThis(),
              take: jest.fn().mockReturnThis(),
              limit: jest.fn().mockReturnThis(),
              getCount: jest.fn(),
              getMany: jest.fn(),
              getManyAndCount: jest.fn(),
            }),
          },
        },
        {
          provide: getRepositoryToken(FriendGroupMember),
          useValue: {
            findOne: jest.fn(),
            find: jest.fn(),
            save: jest.fn(),
            create: jest.fn(),
            count: jest.fn(),
            createQueryBuilder: jest.fn().mockReturnValue({
              leftJoinAndSelect: jest.fn().mockReturnThis(),
              where: jest.fn().mockReturnThis(),
              andWhere: jest.fn().mockReturnThis(),
              orderBy: jest.fn().mockReturnThis(),
              skip: jest.fn().mockReturnThis(),
              take: jest.fn().mockReturnThis(),
              getCount: jest.fn(),
              getMany: jest.fn(),
              getManyAndCount: jest.fn(),
            }),
          },
        },
        {
          provide: getRepositoryToken(User),
          useValue: {
            findOne: jest.fn(),
            findByIds: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<SocialService>(SocialService);
    mockFollowRepository = module.get(getRepositoryToken(UserFollow));
    mockGroupRepository = module.get(getRepositoryToken(FriendGroup));
    mockGroupMemberRepository = module.get(
      getRepositoryToken(FriendGroupMember),
    );
    mockUserRepository = module.get(getRepositoryToken(User));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("followUser - 关注用户", () => {
    const followUserDto: FollowUserDto = {
      userId: "user-002",
      isSpecial: false,
      settings: {
        notifyOnPost: true,
        notifyOnComment: false,
        showInTimeline: true,
      },
    };

    it("应该成功关注用户", async () => {
      // Arrange
      mockUserRepository.findOne.mockResolvedValue(mockFollowingUser as any);
      mockFollowRepository.findOne.mockResolvedValue(null);
      mockFollowRepository.create.mockReturnValue(mockUserFollow as any);
      mockFollowRepository.save.mockResolvedValue(mockUserFollow as any);

      // Act
      const result = await service.followUser(followUserDto, "user-001");

      // Assert
      expect(mockUserRepository.findOne).toHaveBeenCalledWith({
        where: { id: "user-002" },
      });
      expect(mockFollowRepository.findOne).toHaveBeenCalledWith({
        where: { followerId: "user-001", followingId: "user-002" },
      });
      expect(mockFollowRepository.create).toHaveBeenCalledWith({
        followerId: "user-001",
        followingId: "user-002",
        isSpecial: false,
        settings: {
          notifyOnPost: true,
          notifyOnComment: false,
          showInTimeline: true,
        },
        status: FollowStatus.ACTIVE,
      });
      expect(result).toEqual(
        expect.objectContaining({
          id: "follow-001",
          followerId: "user-001",
          followingId: "user-002",
          status: FollowStatus.ACTIVE,
        }),
      );
    });

    it("当关注自己时应该抛出BadRequestException", async () => {
      // Arrange
      const selfFollowDto = { ...followUserDto, userId: "user-001" };

      // Act & Assert
      await expect(
        service.followUser(selfFollowDto, "user-001"),
      ).rejects.toThrow(BadRequestException);
      await expect(
        service.followUser(selfFollowDto, "user-001"),
      ).rejects.toThrow("不能关注自己");
    });

    it("当被关注用户不存在时应该抛出NotFoundException", async () => {
      // Arrange
      mockUserRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.followUser(followUserDto, "user-001"),
      ).rejects.toThrow(NotFoundException);
      await expect(
        service.followUser(followUserDto, "user-001"),
      ).rejects.toThrow("要关注的用户不存在");
    });

    it("当已经关注时应该抛出ConflictException", async () => {
      // Arrange
      mockUserRepository.findOne.mockResolvedValue(mockFollowingUser as any);
      mockFollowRepository.findOne.mockResolvedValue(mockUserFollow as any);

      // Act & Assert
      await expect(
        service.followUser(followUserDto, "user-001"),
      ).rejects.toThrow(ConflictException);
      await expect(
        service.followUser(followUserDto, "user-001"),
      ).rejects.toThrow("已经关注该用户");
    });

    it("应该正确重新激活已取消的关注关系", async () => {
      // Arrange
      const inactiveFollow = {
        ...mockUserFollow,
        status: FollowStatus.BLOCKED,
      };
      const reactivatedFollow = {
        ...inactiveFollow,
        status: FollowStatus.ACTIVE,
        isSpecial: true,
      };

      mockUserRepository.findOne.mockResolvedValue(mockFollowingUser as any);
      mockFollowRepository.findOne.mockResolvedValue(inactiveFollow as any);
      mockFollowRepository.save.mockResolvedValue(reactivatedFollow as any);

      const specialFollowDto = { ...followUserDto, isSpecial: true };

      // Act
      const result = await service.followUser(specialFollowDto, "user-001");

      // Assert
      expect(inactiveFollow.status).toBe(FollowStatus.ACTIVE);
      expect(inactiveFollow.isSpecial).toBe(true);
      expect(result.status).toBe(FollowStatus.ACTIVE);
    });

    it("应该正确设置特别关注", async () => {
      // Arrange
      const specialFollowDto = {
        ...followUserDto,
        isSpecial: true,
        settings: {
          notifyOnPost: true,
          notifyOnComment: true,
          showInTimeline: true,
        },
      };
      const specialFollow = {
        ...mockUserFollow,
        isSpecial: true,
      };

      mockUserRepository.findOne.mockResolvedValue(mockFollowingUser as any);
      mockFollowRepository.findOne.mockResolvedValue(null);
      mockFollowRepository.create.mockReturnValue(specialFollow as any);
      mockFollowRepository.save.mockResolvedValue(specialFollow as any);

      // Act
      const result = await service.followUser(specialFollowDto, "user-001");

      // Assert
      expect(mockFollowRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          isSpecial: true,
          settings: {
            notifyOnPost: true,
            notifyOnComment: true,
            showInTimeline: true,
          },
        }),
      );
      expect(result.isSpecial).toBe(true);
    });
  });

  describe("unfollowUser - 取消关注", () => {
    it("应该成功取消关注", async () => {
      // Arrange
      mockFollowRepository.findOne.mockResolvedValue(mockUserFollow as any);
      mockFollowRepository.remove.mockResolvedValue(mockUserFollow as any);

      // Act
      const result = await service.unfollowUser("user-002", "user-001");

      // Assert
      expect(mockFollowRepository.findOne).toHaveBeenCalledWith({
        where: { followerId: "user-001", followingId: "user-002" },
      });
      expect(mockFollowRepository.remove).toHaveBeenCalledWith(mockUserFollow);
      expect(result).toEqual({
        success: true,
        message: "已取消关注",
      });
    });

    it("当关注关系不存在时应该抛出NotFoundException", async () => {
      // Arrange
      mockFollowRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.unfollowUser("user-002", "user-001"),
      ).rejects.toThrow(NotFoundException);
      await expect(
        service.unfollowUser("user-002", "user-001"),
      ).rejects.toThrow("关注关系不存在");
    });
  });

  describe("getFollowingList - 获取关注列表", () => {
    const queryDto: FollowListQueryDto = {
      page: 1,
      limit: 10,
      status: FollowStatus.ACTIVE,
      sortBy: "createdAt",
      sortOrder: "DESC",
    };

    it("应该成功获取关注列表", async () => {
      // Arrange
      const mockQueryBuilder = mockFollowRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        [mockUserFollow],
        1,
      ]);

      // Mock用户统计数据
      mockFollowRepository.count
        .mockResolvedValueOnce(100) // followers count
        .mockResolvedValueOnce(50); // following count

      // Act
      const result = await service.getFollowingList(
        "user-001",
        queryDto,
        "user-003",
      );

      // Assert
      expect(mockFollowRepository.createQueryBuilder).toHaveBeenCalledWith(
        "follow",
      );
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith(
        "follow.following",
        "user",
      );
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        "follow.followerId = :userId",
        { userId: "user-001" },
      );
      expect(result).toEqual({
        data: expect.any(Array),
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      });
    });

    it("应该正确应用特别关注筛选", async () => {
      // Arrange
      const specialQuery = { ...queryDto, isSpecial: true };
      const mockQueryBuilder = mockFollowRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        [mockUserFollow],
        1,
      ]);
      mockFollowRepository.count
        .mockResolvedValueOnce(100) // followers count
        .mockResolvedValueOnce(50); // following count

      // Act
      await service.getFollowingList("user-001", specialQuery);

      // Assert
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "follow.isSpecial = :isSpecial",
        { isSpecial: true },
      );
    });

    it("应该正确应用搜索筛选", async () => {
      // Arrange
      const searchQuery = { ...queryDto, search: "测试" };
      const mockQueryBuilder = mockFollowRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        [mockUserFollow],
        1,
      ]);
      mockFollowRepository.count
        .mockResolvedValueOnce(100) // followers count
        .mockResolvedValueOnce(50); // following count

      // Act
      await service.getFollowingList("user-001", searchQuery);

      // Assert
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "(user.nickname ILIKE :search OR user.username ILIKE :search)",
        { search: "%测试%" },
      );
    });

    it("应该正确处理分页", async () => {
      // Arrange
      const paginationQuery = { ...queryDto, page: 3, limit: 5 };
      const mockQueryBuilder = mockFollowRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        [mockUserFollow],
        1,
      ]);
      mockFollowRepository.count
        .mockResolvedValueOnce(100) // followers count
        .mockResolvedValueOnce(50); // following count

      // Act
      await service.getFollowingList("user-001", paginationQuery);

      // Assert
      expect(mockQueryBuilder.skip).toHaveBeenCalledWith(10); // (3-1) * 5
      expect(mockQueryBuilder.take).toHaveBeenCalledWith(5);
    });

    it("应该正确处理排序", async () => {
      // Arrange
      const sortQuery = {
        ...queryDto,
        sortBy: "updatedAt",
        sortOrder: "ASC" as const,
      };
      const mockQueryBuilder = mockFollowRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        [mockUserFollow],
        1,
      ]);
      mockFollowRepository.count
        .mockResolvedValueOnce(100) // followers count
        .mockResolvedValueOnce(50); // following count

      // Act
      await service.getFollowingList("user-001", sortQuery);

      // Assert
      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith(
        "follow.updatedAt",
        "ASC",
      );
    });
  });

  describe("getFollowersList - 获取粉丝列表", () => {
    const queryDto: FollowListQueryDto = {
      page: 1,
      limit: 10,
      status: FollowStatus.ACTIVE,
    };

    it("应该成功获取粉丝列表", async () => {
      // Arrange
      const followerFollow = {
        ...mockUserFollow,
        followerId: "user-003",
        followingId: "user-001",
        follower: mockFollower,
      };
      const mockQueryBuilder = mockFollowRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        [followerFollow],
        1,
      ]);

      // Mock用户统计数据
      mockFollowRepository.count
        .mockResolvedValueOnce(100) // followers count
        .mockResolvedValueOnce(50); // following count

      // Act
      const result = await service.getFollowersList(
        "user-001",
        queryDto,
        "user-002",
      );

      // Assert
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith(
        "follow.follower",
        "user",
      );
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        "follow.followingId = :userId",
        { userId: "user-001" },
      );
      expect(result).toEqual({
        data: expect.any(Array),
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      });
    });
  });

  describe("createFriendGroup - 创建好友分组", () => {
    const createGroupDto: CreateFriendGroupDto = {
      name: "测试分组",
      description: "这是一个测试分组",
      groupType: GroupType.FRIENDS,
      visibility: GroupVisibility.PRIVATE,
      colorTag: "#FF5733",
      sortOrder: 1,
      permissions: {
        canViewProfile: true,
        canViewStories: true,
        canComment: true,
        canShare: false,
        canMessage: true,
      },
      initialMembers: ["user-002", "user-003"],
    };

    it("应该成功创建好友分组", async () => {
      // Arrange
      mockGroupRepository.findOne.mockResolvedValue(null);
      mockGroupRepository.create.mockReturnValue(mockFriendGroup as any);
      mockGroupRepository.save.mockResolvedValue(mockFriendGroup as any);

      // Mock添加初始成员
      const addMembersSpy = jest
        .spyOn(service, "addMembersToGroup")
        .mockResolvedValue(2);

      // Act
      const result = await service.createFriendGroup(
        createGroupDto,
        "user-001",
      );

      // Assert
      expect(mockGroupRepository.findOne).toHaveBeenCalledWith({
        where: { ownerId: "user-001", name: "测试分组" },
      });
      expect(mockGroupRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          name: "测试分组",
          description: "这是一个测试分组",
          ownerId: "user-001",
          groupType: GroupType.FRIENDS,
          visibility: GroupVisibility.PRIVATE,
          colorTag: "#FF5733",
          sortOrder: 1,
          memberCount: 2,
        }),
      );
      expect(addMembersSpy).toHaveBeenCalledWith(
        "group-001",
        ["user-002", "user-003"],
        "user-001",
      );
      expect(result).toEqual(
        expect.objectContaining({
          id: "group-001",
          name: "好友分组", // 使用mock数据中的实际名称
          groupType: GroupType.FRIENDS,
        }),
      );
    });

    it("当分组名重复时应该抛出ConflictException", async () => {
      // Arrange
      mockGroupRepository.findOne.mockResolvedValue(mockFriendGroup as any);

      // Act & Assert
      await expect(
        service.createFriendGroup(createGroupDto, "user-001"),
      ).rejects.toThrow(ConflictException);
      await expect(
        service.createFriendGroup(createGroupDto, "user-001"),
      ).rejects.toThrow("已存在同名的分组");
    });

    it("应该正确设置默认值", async () => {
      // Arrange
      const minimalGroupDto = {
        name: "简单分组",
      };
      mockGroupRepository.findOne.mockResolvedValue(null);
      mockGroupRepository.create.mockReturnValue(mockFriendGroup as any);
      mockGroupRepository.save.mockResolvedValue(mockFriendGroup as any);

      // Act
      await service.createFriendGroup(minimalGroupDto, "user-001");

      // Assert
      expect(mockGroupRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          groupType: GroupType.CUSTOM,
          visibility: GroupVisibility.PRIVATE,
          sortOrder: 0,
          memberCount: 0,
          permissions: {},
        }),
      );
    });
  });

  describe("getFriendGroups - 获取好友分组列表", () => {
    const queryDto: FriendGroupQueryDto = {
      page: 1,
      limit: 10,
      isActive: true,
    };

    it("应该成功获取分组列表", async () => {
      // Arrange
      const mockQueryBuilder = mockGroupRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        [mockFriendGroup],
        1,
      ]);

      // Act
      const result = await service.getFriendGroups("user-001", queryDto);

      // Assert
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        "group.ownerId = :ownerId",
        { ownerId: "user-001" },
      );
      expect(result).toEqual({
        data: expect.any(Array),
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      });
    });

    it("应该正确应用分组类型筛选", async () => {
      // Arrange
      const typeQuery = { ...queryDto, groupType: GroupType.FAMILY };
      const mockQueryBuilder = mockGroupRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        [mockFriendGroup],
        1,
      ]);

      // Act
      await service.getFriendGroups("user-001", typeQuery);

      // Assert
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "group.groupType = :groupType",
        { groupType: GroupType.FAMILY },
      );
    });

    it("应该正确应用可见性筛选", async () => {
      // Arrange
      const visibilityQuery = {
        ...queryDto,
        visibility: GroupVisibility.PUBLIC,
      };
      const mockQueryBuilder = mockGroupRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        [mockFriendGroup],
        1,
      ]);

      // Act
      await service.getFriendGroups("user-001", visibilityQuery);

      // Assert
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "group.visibility = :visibility",
        { visibility: GroupVisibility.PUBLIC },
      );
    });
  });

  describe("getGroupMembers - 获取分组成员", () => {
    const queryDto: GroupMemberQueryDto = {
      page: 1,
      limit: 10,
      status: MemberStatus.ACTIVE,
    };

    it("应该成功获取分组成员列表", async () => {
      // Arrange
      mockGroupRepository.findOne.mockResolvedValue(mockFriendGroup as any);
      const mockQueryBuilder = mockGroupMemberRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        [mockGroupMember],
        1,
      ]);

      // Act
      const result = await service.getGroupMembers(
        "group-001",
        queryDto,
        "user-001",
      );

      // Assert
      expect(mockGroupRepository.findOne).toHaveBeenCalledWith({
        where: { id: "group-001" },
      });
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        "member.groupId = :groupId",
        { groupId: "group-001" },
      );
      expect(result).toEqual({
        data: expect.any(Array),
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      });
    });

    it("当分组不存在时应该抛出NotFoundException", async () => {
      // Arrange
      mockGroupRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.getGroupMembers("non-existent", queryDto, "user-001"),
      ).rejects.toThrow(NotFoundException);
      await expect(
        service.getGroupMembers("non-existent", queryDto, "user-001"),
      ).rejects.toThrow("分组不存在");
    });

    it("当无权限查看时应该抛出ForbiddenException", async () => {
      // Arrange
      const otherUserGroup = { ...mockFriendGroup, ownerId: "user-999" };
      mockGroupRepository.findOne.mockResolvedValue(otherUserGroup as any);
      mockGroupMemberRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.getGroupMembers("group-001", queryDto, "user-001"),
      ).rejects.toThrow(ForbiddenException);
      await expect(
        service.getGroupMembers("group-001", queryDto, "user-001"),
      ).rejects.toThrow("无权限查看该分组成员");
    });
  });

  describe("addMembersToGroup - 添加分组成员", () => {
    const userIds = ["user-002", "user-003"];

    it("应该成功添加成员到分组", async () => {
      // Arrange
      mockGroupRepository.findOne.mockResolvedValue(mockFriendGroup as any);
      mockUserRepository.findByIds.mockResolvedValue([
        mockFollowingUser,
        mockFollower,
      ] as any[]);
      mockGroupMemberRepository.find.mockResolvedValue([]);

      const newMembers = userIds.map((userId, index) => ({
        id: `member-${index + 1}`,
        groupId: "group-001",
        userId,
        addedBy: "user-001",
        role: MemberRole.MEMBER,
        status: MemberStatus.ACTIVE,
      }));
      mockGroupMemberRepository.create.mockImplementation(
        (data) => data as any,
      );
      mockGroupMemberRepository.save.mockResolvedValue(newMembers as any);
      mockGroupRepository.update.mockResolvedValue(undefined as any);

      // Act
      const result = await service.addMembersToGroup(
        "group-001",
        userIds,
        "user-001",
      );

      // Assert
      expect(mockGroupRepository.findOne).toHaveBeenCalledWith({
        where: { id: "group-001" },
      });
      expect(mockUserRepository.findByIds).toHaveBeenCalledWith(userIds);
      expect(mockGroupMemberRepository.save).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            groupId: "group-001",
            userId: "user-002",
            addedBy: "user-001",
            role: MemberRole.MEMBER,
            status: MemberStatus.ACTIVE,
          }),
        ]),
      );
      expect(mockGroupRepository.update).toHaveBeenCalledWith("group-001", {
        memberCount: 7, // 原有5个 + 新增2个
      });
      expect(result).toBe(2);
    });

    it("当分组不存在时应该抛出NotFoundException", async () => {
      // Arrange
      mockGroupRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.addMembersToGroup("non-existent", userIds, "user-001"),
      ).rejects.toThrow(NotFoundException);
      await expect(
        service.addMembersToGroup("non-existent", userIds, "user-001"),
      ).rejects.toThrow("分组不存在");
    });

    it("当非分组所有者时应该抛出ForbiddenException", async () => {
      // Arrange
      const otherUserGroup = { ...mockFriendGroup, ownerId: "user-999" };
      mockGroupRepository.findOne.mockResolvedValue(otherUserGroup as any);

      // Act & Assert
      await expect(
        service.addMembersToGroup("group-001", userIds, "user-001"),
      ).rejects.toThrow(ForbiddenException);
      await expect(
        service.addMembersToGroup("group-001", userIds, "user-001"),
      ).rejects.toThrow("只有分组所有者可以添加成员");
    });

    it("当用户不存在时应该抛出BadRequestException", async () => {
      // Arrange
      mockGroupRepository.findOne.mockResolvedValue(mockFriendGroup as any);
      mockUserRepository.findByIds.mockResolvedValue([
        mockFollowingUser,
      ] as any[]); // 只返回一个用户

      // Act & Assert
      await expect(
        service.addMembersToGroup("group-001", userIds, "user-001"),
      ).rejects.toThrow(BadRequestException);
      await expect(
        service.addMembersToGroup("group-001", userIds, "user-001"),
      ).rejects.toThrow("部分用户不存在");
    });

    it("应该正确处理已存在的成员", async () => {
      // Arrange
      const existingMember = { userId: "user-002" };
      mockGroupRepository.findOne.mockResolvedValue(mockFriendGroup as any);
      mockUserRepository.findByIds.mockResolvedValue([
        mockFollowingUser,
        mockFollower,
      ] as any[]);
      mockGroupMemberRepository.find.mockResolvedValue([
        existingMember,
      ] as any[]);

      const newMember = {
        id: "member-new",
        groupId: "group-001",
        userId: "user-003",
        addedBy: "user-001",
        role: MemberRole.MEMBER,
        status: MemberStatus.ACTIVE,
      };
      mockGroupMemberRepository.create.mockReturnValue(newMember as any);
      mockGroupMemberRepository.save.mockResolvedValue([newMember] as any);

      // Act
      const result = await service.addMembersToGroup(
        "group-001",
        userIds,
        "user-001",
      );

      // Assert
      expect(result).toBe(1); // 只添加了一个新成员
      expect(mockGroupRepository.update).toHaveBeenCalledWith("group-001", {
        memberCount: 6, // 原有5个 + 新增1个
      });
    });

    it("当所有用户都已存在时应该返回0", async () => {
      // Arrange
      const existingMembers = userIds.map((userId) => ({ userId }));
      mockGroupRepository.findOne.mockResolvedValue(mockFriendGroup as any);
      mockUserRepository.findByIds.mockResolvedValue([
        mockFollowingUser,
        mockFollower,
      ] as any[]);
      mockGroupMemberRepository.find.mockResolvedValue(
        existingMembers as any[],
      );

      // Act
      const result = await service.addMembersToGroup(
        "group-001",
        userIds,
        "user-001",
      );

      // Assert
      expect(result).toBe(0);
      expect(mockGroupMemberRepository.save).not.toHaveBeenCalled();
      expect(mockGroupRepository.update).not.toHaveBeenCalled();
    });
  });

  describe("getSocialStats - 获取社交统计", () => {
    it("应该成功获取社交统计数据", async () => {
      // Arrange
      const mockStats = {
        followingCount: 150,
        followersCount: 200,
        mutualFollowsCount: 50,
        specialFollowsCount: 20,
        friendGroupsCount: 5,
        groupMembershipsCount: 8,
        newFollowersToday: 3,
        newFollowingToday: 1,
      };

      mockFollowRepository.count
        .mockResolvedValueOnce(150) // following count
        .mockResolvedValueOnce(200) // followers count
        .mockResolvedValueOnce(20); // special follows count

      mockGroupRepository.count.mockResolvedValue(5); // friend groups count
      mockGroupMemberRepository.count.mockResolvedValue(8); // group memberships count

      const mockTodayQueryBuilder = mockFollowRepository.createQueryBuilder();
      (mockTodayQueryBuilder.getCount as jest.Mock)
        .mockResolvedValueOnce(3) // new followers today
        .mockResolvedValueOnce(1); // new following today

      // Mock getMutualFollowsCount私有方法
      jest.spyOn(service as any, "getMutualFollowsCount").mockResolvedValue(50);

      const mockTopGroups = [
        {
          id: "group-001",
          name: "最佳朋友",
          memberCount: 25,
          groupType: GroupType.FRIENDS,
        },
        {
          id: "group-002",
          name: "同事",
          memberCount: 15,
          groupType: GroupType.COLLEAGUES,
        },
      ];
      const mockTopGroupsBuilder = mockGroupRepository.createQueryBuilder();
      (mockTopGroupsBuilder.getMany as jest.Mock).mockResolvedValue(
        mockTopGroups,
      );

      // Act
      const result = await service.getSocialStats("user-001");

      // Assert
      expect(result).toEqual({
        userId: "user-001",
        followingCount: 150,
        followersCount: 200,
        mutualFollowsCount: 50,
        specialFollowsCount: 20,
        friendGroupsCount: 5,
        groupMembershipsCount: 8,
        newFollowersToday: 3,
        newFollowingToday: 1,
        topGroups: expect.arrayContaining([
          expect.objectContaining({
            groupId: "group-001",
            groupName: "最佳朋友",
            memberCount: 25,
            groupType: GroupType.FRIENDS,
          }),
        ]),
        topInteractors: [],
      });
    });

    it("应该正确处理空统计数据", async () => {
      // Arrange
      mockFollowRepository.count.mockResolvedValue(0);
      mockGroupRepository.count.mockResolvedValue(0);
      mockGroupMemberRepository.count.mockResolvedValue(0);

      const mockQueryBuilder = mockFollowRepository.createQueryBuilder();
      (mockQueryBuilder.getCount as jest.Mock).mockResolvedValue(0);

      jest.spyOn(service as any, "getMutualFollowsCount").mockResolvedValue(0);

      const mockTopGroupsBuilder = mockGroupRepository.createQueryBuilder();
      (mockTopGroupsBuilder.getMany as jest.Mock).mockResolvedValue([]);

      // Act
      const result = await service.getSocialStats("user-001");

      // Assert
      expect(result).toEqual({
        userId: "user-001",
        followingCount: 0,
        followersCount: 0,
        mutualFollowsCount: 0,
        specialFollowsCount: 0,
        friendGroupsCount: 0,
        groupMembershipsCount: 0,
        newFollowersToday: 0,
        newFollowingToday: 0,
        topGroups: [],
        topInteractors: [],
      });
    });
  });

  describe("getMutualFollowsCount - 私有方法测试", () => {
    it("应该正确计算互关数量", async () => {
      // Arrange
      const mockMutualQueryBuilder = mockFollowRepository.createQueryBuilder();
      (mockMutualQueryBuilder.getCount as jest.Mock).mockResolvedValue(25);

      // Act
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = await (service as any).getMutualFollowsCount("user-001");

      // Assert
      expect(mockFollowRepository.createQueryBuilder).toHaveBeenCalledWith(
        "f1",
      );
      expect(mockMutualQueryBuilder.innerJoin).toHaveBeenCalledWith(
        "user_follows",
        "f2",
        "f1.followingId = f2.followerId AND f1.followerId = f2.followingId",
      );
      expect(mockMutualQueryBuilder.where).toHaveBeenCalledWith(
        "f1.followerId = :userId",
        { userId: "user-001" },
      );
      expect(result).toBe(25);
    });
  });

  describe("formatFollowResponse - 私有方法测试", () => {
    it("应该正确格式化关注响应", async () => {
      // Arrange
      mockUserRepository.findOne
        .mockResolvedValueOnce(mockUser as any) // follower
        .mockResolvedValueOnce(mockFollowingUser as any); // following

      // Act
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = await (service as any).formatFollowResponse(
        mockUserFollow,
      );

      // Assert
      expect(result).toEqual(
        expect.objectContaining({
          id: "follow-001",
          followerId: "user-001",
          followingId: "user-002",
          followerName: "测试用户",
          followingName: "被关注用户",
          status: FollowStatus.ACTIVE,
          isSpecial: false,
        }),
      );
    });

    it("应该处理用户不存在的情况", async () => {
      // Arrange
      mockUserRepository.findOne
        .mockResolvedValueOnce(null) // follower not found
        .mockResolvedValueOnce(null); // following not found

      // Act
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = await (service as any).formatFollowResponse(
        mockUserFollow,
      );

      // Assert
      expect(result.followerName).toBe("未知用户");
      expect(result.followingName).toBe("未知用户");
    });
  });

  describe("formatUserBrief - 私有方法测试", () => {
    it("应该正确格式化用户简要信息", async () => {
      // Arrange
      mockFollowRepository.count
        .mockResolvedValueOnce(100) // followers count
        .mockResolvedValueOnce(50); // following count

      mockFollowRepository.findOne
        .mockResolvedValueOnce(mockUserFollow as any) // is following
        .mockResolvedValueOnce(null); // is not following me

      // Act
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = await (service as any).formatUserBrief(
        mockFollowingUser,
        "user-001",
      );

      // Assert
      expect(result).toEqual(
        expect.objectContaining({
          id: "user-002",
          username: "following",
          name: "被关注用户",
          followersCount: 100,
          followingCount: 50,
          storiesCount: 0,
          isFollowing: true,
          isFollowingMe: false,
          isMutualFollow: false,
        }),
      );
    });

    it("应该正确识别互关关系", async () => {
      // Arrange
      mockFollowRepository.count
        .mockResolvedValueOnce(100)
        .mockResolvedValueOnce(50);

      mockFollowRepository.findOne
        .mockResolvedValueOnce(mockUserFollow as any) // is following
        .mockResolvedValueOnce(mockUserFollow as any); // is following me

      // Act
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = await (service as any).formatUserBrief(
        mockFollowingUser,
        "user-001",
      );

      // Assert
      expect(result.isFollowing).toBe(true);
      expect(result.isFollowingMe).toBe(true);
      expect(result.isMutualFollow).toBe(true);
    });
  });

  describe("formatGroupResponse - 私有方法测试", () => {
    it("应该正确格式化分组响应", () => {
      // Act
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = (service as any).formatGroupResponse(mockFriendGroup);

      // Assert
      expect(result).toEqual(
        expect.objectContaining({
          id: "group-001",
          name: "好友分组",
          description: "我的好友分组",
          ownerId: "user-001",
          ownerName: "测试用户",
          groupType: GroupType.FRIENDS,
          visibility: GroupVisibility.PRIVATE,
          colorTag: "#FF5733",
          memberCount: 5,
          sortOrder: 1,
          isActive: true,
        }),
      );
    });

    it("应该处理分组所有者不存在的情况", () => {
      // Arrange
      const groupWithoutOwner = { ...mockFriendGroup, owner: null };

      // Act
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = (service as any).formatGroupResponse(groupWithoutOwner);

      // Assert
      expect(result.ownerName).toBe("未知用户");
    });
  });

  describe("formatGroupMemberResponse - 私有方法测试", () => {
    it("应该正确格式化分组成员响应", () => {
      // Act
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = (service as any).formatGroupMemberResponse(
        mockGroupMember,
      );

      // Assert
      expect(result).toEqual(
        expect.objectContaining({
          id: "member-001",
          groupId: "group-001",
          groupName: "好友分组",
          userId: "user-002",
          userName: "被关注用户",
          role: MemberRole.MEMBER,
          status: MemberStatus.ACTIVE,
          addedBy: "user-001",
          addedByName: "测试用户",
          note: "通过共同好友添加",
        }),
      );
    });

    it("应该处理缺失信息的情况", () => {
      // Arrange
      const memberWithoutInfo = {
        ...mockGroupMember,
        group: null,
        user: null,
        adder: null,
      };

      // Act
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = (service as any).formatGroupMemberResponse(
        memberWithoutInfo,
      );

      // Assert
      expect(result.groupName).toBe("未知分组");
      expect(result.userName).toBe("未知用户");
      expect(result.addedByName).toBe("未知用户");
    });
  });

  describe("错误边界测试", () => {
    it("应该处理数据库连接错误", async () => {
      // Arrange
      mockFollowRepository.createQueryBuilder.mockImplementation(() => {
        throw new Error("数据库连接失败");
      });

      // Act & Assert
      await expect(
        service.getFollowingList("user-001", { page: 1, limit: 10 }),
      ).rejects.toThrow("数据库连接失败");
    });

    it("应该处理无效的分页参数", async () => {
      // Arrange
      const mockQueryBuilder = mockFollowRepository.createQueryBuilder();
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockResolvedValue([
        [],
        0,
      ]);

      // Act
      const result = await service.getFollowingList("user-001", {
        page: -1,
        limit: 0,
      });

      // Assert
      expect(mockQueryBuilder.skip).toHaveBeenCalledWith(-0); // (-1-1) * 0 = -0
      expect(mockQueryBuilder.take).toHaveBeenCalledWith(0);
      expect(result.data).toEqual([]);
    });

    it("应该处理并发创建分组的情况", async () => {
      // Arrange
      mockGroupRepository.findOne.mockResolvedValue(null);
      mockGroupRepository.create.mockReturnValue(mockFriendGroup as any);
      mockGroupRepository.save.mockRejectedValue(
        new Error("duplicate key value violates unique constraint"),
      );

      // Act & Assert
      await expect(
        service.createFriendGroup({ name: "重复分组" }, "user-001"),
      ).rejects.toThrow();
    });
  });
});
