/**
 * HealthModule 单元测试
 * 测试健康检查模块的依赖注入和模块配置
 */

import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { CACHE_MANAGER } from "@nestjs/cache-manager";
import { HealthModule } from "./health.module";
import { HealthService, RedisHealthIndicator } from "./health.service";
import { HealthController } from "./health.controller";
import { EnhancedRedisService } from "../../common/services/enhanced-redis.service";

describe("HealthModule", () => {
  let module: TestingModule;

  const mockCacheManager = {
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
    reset: jest.fn(),
    store: {
      name: "memory",
      isCacheable: jest.fn(),
      get: jest.fn(),
      set: jest.fn(),
      del: jest.fn(),
      reset: jest.fn(),
      mget: jest.fn(),
      mset: jest.fn(),
      mdel: jest.fn(),
      keys: jest.fn(),
      ttl: jest.fn(),
    },
  };

  const mockEnhancedRedisService = {
    getClient: jest.fn().mockReturnValue({
      ping: jest.fn().mockResolvedValue("PONG"),
      set: jest.fn().mockResolvedValue("OK"),
      get: jest.fn().mockResolvedValue("test"),
      del: jest.fn().mockResolvedValue(1),
      info: jest.fn().mockResolvedValue("redis_version:6.2.0"),
    }),
    setCache: jest.fn(),
    getCache: jest.fn(),
    delCache: jest.fn(),
  };

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [HealthModule],
    })
      .overrideProvider(CACHE_MANAGER)
      .useValue(mockCacheManager)
      .overrideProvider(EnhancedRedisService)
      .useValue(mockEnhancedRedisService)
      .overrideProvider(RedisHealthIndicator)
      .useValue({
        isHealthy: jest.fn().mockResolvedValue({
          redis: { status: "up", responseTime: 10 },
        }),
      })
      .compile();
  });

  afterEach(async () => {
    if (module) {
      await module.close();
    }
  });

  it("should be defined", () => {
    expect(module).toBeDefined();
  });

  describe("服务提供者", () => {
    it("should provide HealthService", () => {
      const healthService = module.get<HealthService>(HealthService);
      expect(healthService).toBeDefined();
      expect(healthService).toBeInstanceOf(HealthService);
    });
  });

  describe("控制器", () => {
    it("should provide HealthController", () => {
      const healthController = module.get<HealthController>(HealthController);
      expect(healthController).toBeDefined();
      expect(healthController).toBeInstanceOf(HealthController);
    });
  });

  describe("依赖注入", () => {
    it("should inject CACHE_MANAGER", () => {
      // HealthModule可能没有直接暴露CACHE_MANAGER，这里只验证模块能正常加载
      expect(module).toBeDefined();
      expect(module.get<HealthService>(HealthService)).toBeDefined();
    });
  });

  describe("模块配置", () => {
    it("should export HealthService for other modules", () => {
      const healthService = module.get<HealthService>(HealthService);
      expect(healthService).toBeDefined();
    });
  });

  describe("健康检查功能", () => {
    it("should be able to perform health checks", async () => {
      const healthService = module.get<HealthService>(HealthService);

      // 这里可以添加更多健康检查相关的测试
      expect(healthService).toBeDefined();
      expect(typeof healthService.getHealth).toBe("function");
      expect(typeof healthService.getDetailedHealth).toBe("function");
    });
  });
});
