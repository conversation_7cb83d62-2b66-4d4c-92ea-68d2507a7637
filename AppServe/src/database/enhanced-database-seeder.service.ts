/**
 * 企业级数据库种子服务 v2.0
 *
 * 基于 YGS v1.0.0 完整业务逻辑实现
 * 支持复杂的数据关系和业务场景
 */

import { Injectable, Logger } from "@nestjs/common";
import { InjectDataSource } from "@nestjs/typeorm";
import type { EntityManager } from "typeorm";
import { DataSource } from "typeorm";
import * as bcrypt from "bcrypt";

// 实体导入
import { User } from "../modules/users/entities/user.entity";
import { Story, StoryStatus } from "../modules/stories/entities/story.entity";
import { Character } from "../modules/characters/entities/character.entity";
import { LightRequest } from "../modules/characters/entities/light-request.entity";
import { CharacterLighting } from "../modules/characters/entities/character-lighting.entity";
import {
  UserFollow,
  FollowStatus,
} from "../modules/social/entities/user-follow.entity";
import { FriendGroup } from "../modules/social/entities/friend-group.entity";
import { FriendGroupMember } from "../modules/social/entities/friend-group-member.entity";
import type { CommentStatus } from "../modules/comments/entities/comment.entity";
import {
  Comment,
  CommentTargetType,
} from "../modules/comments/entities/comment.entity";
import { CommentLike } from "../modules/comments/entities/comment-like.entity";
import {
  Bookmark,
  BookmarkCategory,
} from "../modules/bookmarks/entities/bookmark.entity";
import type { NotificationType } from "../modules/users/entities/notification.entity";
import { Notification } from "../modules/users/entities/notification.entity";
import { Theme } from "../modules/stories/entities/theme.entity";
import {
  TimelineEvent,
  EventType,
  EventImportance,
  EventVisibility,
} from "../modules/timeline/entities/timeline-event.entity";
import {
  Share,
  ShareType,
  ShareStatus,
} from "../modules/shares/entities/share.entity";
import { ShareAccessLog } from "../modules/shares/entities/share-access-log.entity";
// StoryCharacter 通过 TypeORM 的 ManyToMany 关系自动管理

// 种子数据导入
import { ENHANCED_USER_SEED_DATA } from "./seeders/enhanced-user.seeder";
import { ENHANCED_STORY_SEED_DATA } from "./seeders/enhanced-story.seeder";
import { ENHANCED_CHARACTER_SEED_DATA } from "./seeders/enhanced-character.seeder";
import { ENHANCED_LIGHT_REQUEST_SEED_DATA } from "./seeders/enhanced-lighting.seeder";
import {
  ENHANCED_FOLLOW_SEED_DATA,
  ENHANCED_FRIEND_GROUP_SEED_DATA,
} from "./seeders/enhanced-social.seeder";
import {
  ENHANCED_COMMENT_SEED_DATA,
  ENHANCED_COMMENT_LIKE_SEED_DATA,
} from "./seeders/enhanced-comments.seeder";
import { ENHANCED_BOOKMARK_SEED_DATA } from "./seeders/enhanced-bookmarks.seeder";
import { ENHANCED_NOTIFICATION_SEED_DATA } from "./seeders/enhanced-notifications.seeder";
import { ENHANCED_THEME_SEED_DATA } from "./seeders/enhanced-themes.seeder";
import { ENHANCED_TIMELINE_EVENT_SEED_DATA } from "./seeders/enhanced-timeline.seeder";
import {
  ENHANCED_SHARE_SEED_DATA,
  ENHANCED_SHARE_ACCESS_LOG_SEED_DATA,
} from "./seeders/enhanced-sharing.seeder";

@Injectable()
export class EnhancedDatabaseSeederService {
  private readonly logger = new Logger(EnhancedDatabaseSeederService.name);

  constructor(@InjectDataSource() private dataSource: DataSource) {}

  /**
   * 执行完整的数据播种
   */
  async seedAll(
    options: {
      clearExistingData?: boolean;
      environment?: "development" | "test" | "production";
    } = {},
  ): Promise<{
    users: User[];
    stories: Story[];
    characters: Character[];
    lightRequests: LightRequest[];
    follows: UserFollow[];
    friendGroups: FriendGroup[];
  }> {
    const { clearExistingData = true, environment = "development" } = options;

    this.logger.log("🌱 开始执行企业级数据播种 v2.0...");
    this.logger.log(`📋 环境: ${environment}`);
    this.logger.log(`🧹 清理现有数据: ${clearExistingData ? "是" : "否"}`);

    try {
      return await this.dataSource.transaction(async (manager) => {
        // 1. 清理现有数据
        if (clearExistingData) {
          await this.clearExistingData(manager);
        }

        // 2. 创建主题数据
        this.logger.log("🎨 创建主题数据...");
        const themes = await this.seedThemes(manager);
        this.logger.log(`✅ 创建主题: ${themes.length} 个`);

        // 3. 创建用户
        this.logger.log("👤 创建用户数据...");
        const users = await this.seedUsers(manager);
        this.logger.log(`✅ 创建用户: ${users.length} 个`);

        // 4. 创建故事
        this.logger.log("📖 创建故事数据...");
        const stories = await this.seedStories(manager, users, themes);
        this.logger.log(`✅ 创建故事: ${stories.length} 个`);

        // 5. 创建人物
        this.logger.log("🎭 创建人物数据...");
        const characters = await this.seedCharacters(manager, stories, users);
        this.logger.log(`✅ 创建人物: ${characters.length} 个`);

        // 6. 创建故事-人物关联
        this.logger.log("🔗 创建故事-人物关联...");
        await this.seedStoryCharacters(manager, stories, characters);

        // 7. 创建社交关系
        this.logger.log("👥 创建社交关系...");
        const socialStats = await this.seedSocialRelations(manager, users);
        this.logger.log(`✅ 创建关注关系: ${socialStats.follows} 个`);
        this.logger.log(`✅ 创建好友分组: ${socialStats.groups} 个`);

        // 8. 创建点亮申请和关系
        this.logger.log("💡 创建点亮数据...");
        const lightingStats = await this.seedLightingData(
          manager,
          stories,
          characters,
          users,
        );
        this.logger.log(`✅ 创建点亮申请: ${lightingStats.requests} 个`);
        this.logger.log(`✅ 成功点亮: ${lightingStats.approved} 个`);

        // 9. 创建评论系统数据
        this.logger.log("💬 创建评论数据...");
        const commentStats = await this.seedComments(manager, stories, users);
        this.logger.log(`✅ 创建评论: ${commentStats.comments} 个`);
        this.logger.log(`✅ 评论点赞: ${commentStats.likes} 个`);

        // 10. 创建收藏数据
        this.logger.log("⭐ 创建收藏数据...");
        const bookmarkCount = await this.seedBookmarks(manager, stories, users);
        this.logger.log(`✅ 创建收藏: ${bookmarkCount} 个`);

        // 11. 创建时间线数据
        this.logger.log("📅 创建时间线数据...");
        const timelineCount = await this.seedTimeline(manager, users);
        this.logger.log(`✅ 创建时间线事件: ${timelineCount} 个`);

        // 12. 创建分享数据
        this.logger.log("🔗 创建分享数据...");
        const shareStats = await this.seedShares(manager, stories, users);
        this.logger.log(`✅ 创建分享链接: ${shareStats.shares} 个`);
        this.logger.log(`✅ 访问记录: ${shareStats.accessLogs} 个`);

        // 13. 创建通知数据（最后创建，依赖其他数据）
        this.logger.log("🔔 创建通知数据...");
        const notificationCount = await this.seedNotifications(manager, users);
        this.logger.log(`✅ 创建通知: ${notificationCount} 个`);

        // 8. 生成统计报告
        const stats = await this.generateStats(manager);

        this.logger.log("\n🎉 企业级数据播种完成！");
        this.logger.log("📊 数据统计:");
        this.logger.log(`   👥 用户总数: ${stats.users}`);
        this.logger.log(`   📖 故事总数: ${stats.stories}`);
        this.logger.log(`   🎭 人物总数: ${stats.characters}`);
        this.logger.log(`   💡 点亮申请: ${stats.lightRequests}`);
        this.logger.log(`   ✅ 成功点亮: ${stats.successfulLightings}`);
        this.logger.log(`   👫 好友对数: ${stats.friendPairs}`);
        this.logger.log(`   📁 分组总数: ${stats.friendGroups}`);

        this.logger.log("\n🔑 测试账号信息:");
        this.logger.log("   📝 创作者: 13800138001 / Test123456!");
        this.logger.log("   💡 点亮者: 13800138002 / Test123456!");
        this.logger.log("   👤 普通用户: 13800138004 / Test123456!");
        this.logger.log("   🌟 VIP用户: 16675158665 / Test123456!");

        return {
          users,
          stories,
          characters,
          lightRequests: [] as LightRequest[], // lightingStats contains count, not actual objects
          follows: [] as UserFollow[], // socialStats contains count, not actual objects
          friendGroups: [] as FriendGroup[], // socialStats contains count, not actual objects
        };
      });
    } catch (error) {
      this.logger.error("❌ 数据播种失败:", error);
      throw error;
    }
  }

  /**
   * 清理现有数据
   */
  private async clearExistingData(manager: EntityManager): Promise<void> {
    this.logger.log("🧹 清理现有测试数据...");

    // 按依赖关系顺序清理
    const tables = [
      "share_access_logs",
      "shares",
      "timeline_events",
      "notifications",
      "bookmarks",
      "comment_likes",
      "comments",
      "friend_group_members",
      "friend_groups",
      "user_follows",
      "character_lightings",
      "light_requests",
      "story_characters",
      "characters",
      "story_shares",
      "story_chapters",
      "stories",
      "themes",
      "refresh_tokens",
      "users",
    ];

    for (const table of tables) {
      try {
        await manager.query(`TRUNCATE TABLE ${table} CASCADE`);
      } catch (error) {
        this.logger.warn(`清理表 ${table} 失败，可能不存在`);
      }
    }

    this.logger.log("✅ 数据清理完成");
  }

  /**
   * 创建用户数据
   */
  private async seedUsers(manager: EntityManager): Promise<User[]> {
    const users: User[] = [];

    for (const userData of ENHANCED_USER_SEED_DATA) {
      const hashedPassword = await bcrypt.hash(userData.password, 10);

      const user = manager.create(User, {
        phone: userData.phone,
        email: userData.email,
        passwordHash: hashedPassword,
        userNumber: userData.userNumber,
        nickname: userData.nickname,
        username: userData.username,
        avatarUrl: userData.avatarUrl,
        birthDate: userData.birthDate ? new Date(userData.birthDate) : null,
        bio: userData.bio,
        profileDisplaySettings: userData.profileDisplaySettings,
        aiQuotaRemaining: userData.aiQuotaRemaining,
        aiQuotaResetDate: new Date(),
        isPhoneVerified: userData.isPhoneVerified,
        isEmailVerified: userData.isEmailVerified,
        isVipUser: userData.isVipUser,
        anomalyStatus: userData.anomalyStatus,
        status: "active",
        isActive: true,
        securityLevel:
          userData.isPhoneVerified && userData.isEmailVerified ? 2 : 1,
      });

      const savedUser = await manager.save(User, user);
      users.push(savedUser);
    }

    return users;
  }

  /**
   * 创建主题数据
   */
  private async seedThemes(manager: EntityManager): Promise<Theme[]> {
    const themes: Theme[] = [];

    for (const themeData of ENHANCED_THEME_SEED_DATA) {
      const theme = manager.create(Theme, {
        name: themeData.name,
        description: themeData.description,
        colorCode: themeData.colorCode,
        iconName: themeData.iconName,
        isActive: themeData.isActive,
        sortOrder: themeData.sortOrder,
        category: themeData.category,
      });

      const savedTheme = await manager.save(Theme, theme);
      themes.push(savedTheme);
    }

    return themes;
  }

  /**
   * 创建故事数据
   */
  private async seedStories(
    manager: EntityManager,
    users: User[],
    themes: Theme[],
  ): Promise<Story[]> {
    const stories: Story[] = [];

    for (const storyData of ENHANCED_STORY_SEED_DATA) {
      const author = users.find((u) => u.username === storyData.authorUsername);
      if (!author) {
        this.logger.warn(`找不到作者: ${storyData.authorUsername}`);
        continue;
      }

      const statusMap = {
        DRAFT: StoryStatus.DRAFT,
        PUBLISHED: StoryStatus.PUBLISHED,
        ARCHIVED: StoryStatus.ARCHIVED,
      };

      // 查找主题
      const theme = themes.find((t) => t.name === storyData.theme);

      const story = manager.create(Story, {
        userId: author.id,
        title: storyData.title,
        content: storyData.content,
        coverImageUrl: storyData.coverImageUrl,
        status: statusMap[storyData.status],
        permissionLevel: storyData.permissionLevel,
        viewCount: storyData.viewCount,
        likeCount: storyData.likeCount,
        commentCount: storyData.commentCount,
        storyDate: storyData.storyDate ? new Date(storyData.storyDate) : null,
        location: storyData.location,
        images: storyData.images || null,
        allowComments: storyData.allowComments,
        allowLikes: storyData.allowLikes,
        allowSharing: storyData.allowSharing,
        aiSafetyScore: storyData.aiSafetyScore,
        themeId: theme ? theme.id : null,
      });

      const savedStory = await manager.save(Story, story);
      stories.push(savedStory);
    }

    return stories;
  }

  /**
   * 创建人物数据
   */
  private async seedCharacters(
    manager: EntityManager,
    stories: Story[],
    users: User[],
  ): Promise<Character[]> {
    const characters: Character[] = [];
    const characterMap = new Map<string, Character>(); // 用于处理重复人物

    for (const characterData of ENHANCED_CHARACTER_SEED_DATA) {
      const story = stories.find((s) => s.title === characterData.storyTitle);
      if (!story) {
        this.logger.warn(`找不到故事: ${characterData.storyTitle}`);
        continue;
      }

      const creator = users.find((u) => u.id === story.userId);
      if (!creator) {
        this.logger.warn(`找不到创建者`);
        continue;
      }

      // 检查是否已存在同名人物
      const characterKey = `${creator.id}-${characterData.name}`;
      let character = characterMap.get(characterKey);

      if (!character) {
        // 创建新人物
        let lighterUserId = null;
        if (characterData.isLighted && characterData.lighterUsername) {
          const lighter = users.find(
            (u) => u.username === characterData.lighterUsername,
          );
          if (lighter) {
            lighterUserId = lighter.id;
          }
        }

        character = manager.create(Character, {
          creatorId: creator.id,
          name: characterData.name,
          description: characterData.description,
          avatarUrl: characterData.avatarUrl,
          gender: characterData.gender,
          relationship: characterData.relationship,
          customRelationship: characterData.customRelationship,
          isLighted: characterData.isLighted,
          lightingCount: characterData.isLighted ? 1 : 0,
          lighterUserId: lighterUserId,
          firstLightedAt: characterData.isLighted ? new Date() : null,
          isActive: true,
        });

        const savedCharacter = await manager.save(Character, character);
        characters.push(savedCharacter);
        characterMap.set(characterKey, savedCharacter);
      }
    }

    return characters;
  }

  /**
   * 创建故事-人物关联
   */
  private async seedStoryCharacters(
    manager: EntityManager,
    stories: Story[],
    characters: Character[],
  ): Promise<void> {
    for (const story of stories) {
      const storyCharacters: Character[] = [];

      // 找到属于这个故事的所有人物
      for (const characterData of ENHANCED_CHARACTER_SEED_DATA) {
        if (characterData.storyTitle === story.title) {
          const character = characters.find(
            (c) =>
              c.name === characterData.name && c.creatorId === story.userId,
          );
          if (character) {
            storyCharacters.push(character);
          }
        }
      }

      // 如果有人物，更新故事的人物关联
      if (storyCharacters.length > 0) {
        story.characters = storyCharacters;
        await manager.save(Story, story);
      }
    }
  }

  /**
   * 创建社交关系
   */
  private async seedSocialRelations(
    manager: EntityManager,
    users: User[],
  ): Promise<{ follows: number; groups: number }> {
    let followCount = 0;
    let groupCount = 0;

    // 创建关注关系
    for (const followData of ENHANCED_FOLLOW_SEED_DATA) {
      const follower = users.find(
        (u) => u.username === followData.followerUsername,
      );
      const following = users.find(
        (u) => u.username === followData.followingUsername,
      );

      if (!follower || !following) continue;

      const follow = manager.create(UserFollow, {
        followerId: follower.id,
        followingId: following.id,
        status: followData.status,
        isSpecial: followData.isSpecial,
        metadata: followData.note ? { note: followData.note } : null,
      });

      await manager.save(UserFollow, follow);
      followCount++;
    }

    // 创建好友分组
    for (const groupData of ENHANCED_FRIEND_GROUP_SEED_DATA) {
      const owner = users.find((u) => u.username === groupData.ownerUsername);
      if (!owner) continue;

      const group = manager.create(FriendGroup, {
        ownerId: owner.id,
        name: groupData.groupName,
        description: groupData.description,
        memberCount: groupData.members.length,
      });

      const savedGroup = await manager.save(FriendGroup, group);
      groupCount++;

      // 添加成员
      for (const memberUsername of groupData.members) {
        const member = users.find((u) => u.username === memberUsername);
        if (!member) continue;

        const groupMember = manager.create(FriendGroupMember, {
          groupId: savedGroup.id,
          userId: member.id,
          addedBy: owner.id,
        });

        await manager.save(FriendGroupMember, groupMember);
      }
    }

    return { follows: followCount, groups: groupCount };
  }

  /**
   * 创建点亮数据
   */
  private async seedLightingData(
    manager: EntityManager,
    stories: Story[],
    characters: Character[],
    users: User[],
  ): Promise<{ requests: number; approved: number }> {
    let requestCount = 0;
    let approvedCount = 0;

    for (const requestData of ENHANCED_LIGHT_REQUEST_SEED_DATA) {
      const story = stories.find((s) => s.title === requestData.storyTitle);
      if (!story) continue;

      const character = characters.find(
        (c) =>
          c.name === requestData.characterName && c.creatorId === story.userId,
      );
      if (!character) continue;

      const requester = users.find(
        (u) => u.username === requestData.requesterUsername,
      );
      if (!requester) continue;

      // 计算时间
      const createdAt = new Date();
      createdAt.setDate(createdAt.getDate() - requestData.daysAgo);

      const expiresAt = new Date(createdAt);
      expiresAt.setDate(expiresAt.getDate() + 7);

      let processedAt = null;
      if (requestData.processedDaysAgo !== undefined) {
        processedAt = new Date();
        processedAt.setDate(
          processedAt.getDate() - requestData.processedDaysAgo,
        );
      }

      const request = manager.create(LightRequest, {
        storyId: story.id,
        characterId: character.id,
        requesterId: requester.id,
        storyAuthorId: story.userId,
        phoneVerification: requestData.phoneVerification,
        hasPhoneVerification: requestData.hasPhoneVerification,
        message: requestData.message,
        status: requestData.status,
        expiresAt: expiresAt,
        processedAt: processedAt,
        createdAt: createdAt,
      });

      await manager.save(LightRequest, request);
      requestCount++;

      // 如果是已批准的申请，创建点亮关系
      if (requestData.status === "approved") {
        const lighting = manager.create(CharacterLighting, {
          characterId: character.id,
          lighterUserId: requester.id,
          creatorUserId: story.userId,
          requestId: request.id,
          storyId: story.id,
          confirmedAt: processedAt,
        });

        await manager.save(CharacterLighting, lighting);
        approvedCount++;
      }
    }

    return { requests: requestCount, approved: approvedCount };
  }

  /**
   * 创建评论系统数据
   */
  private async seedComments(
    manager: EntityManager,
    stories: Story[],
    users: User[],
  ): Promise<{ comments: number; likes: number }> {
    const comments: Comment[] = [];
    let commentCount = 0;
    let likeCount = 0;

    for (const commentData of ENHANCED_COMMENT_SEED_DATA) {
      const story = stories.find((s) => s.title === commentData.storyTitle);
      if (!story) continue;

      const author = users.find(
        (u) => u.username === commentData.authorUsername,
      );
      if (!author) continue;

      // 暂时只创建对故事的直接评论，不处理回复关系
      const targetType = CommentTargetType.STORY;
      const targetId = story.id;
      const depth = 0;

      const createdAt = new Date();
      createdAt.setDate(createdAt.getDate() - commentData.createdDaysAgo);

      const comment = manager.create(Comment, {
        targetType: targetType,
        targetId: targetId,
        content: commentData.content,
        authorId: author.id,
        status: commentData.status as CommentStatus,
        likeCount: commentData.likeCount,
        depth: depth,
        createdAt: createdAt,
      });

      const savedComment = await manager.save(Comment, comment);
      comments.push(savedComment);
      commentCount++;
    }

    // 创建评论点赞
    for (const likeData of ENHANCED_COMMENT_LIKE_SEED_DATA) {
      const comment = comments.find(
        (c) => c.content === likeData.commentContent,
      );
      if (!comment) continue;

      const liker = users.find((u) => u.username === likeData.likerUsername);
      if (!liker) continue;

      const createdAt = new Date();
      createdAt.setDate(createdAt.getDate() - likeData.createdDaysAgo);

      const commentLike = manager.create(CommentLike, {
        commentId: comment.id,
        userId: liker.id,
        createdAt: createdAt,
      });

      await manager.save(CommentLike, commentLike);
      likeCount++;
    }

    return { comments: commentCount, likes: likeCount };
  }

  /**
   * 创建收藏数据
   */
  private async seedBookmarks(
    manager: EntityManager,
    stories: Story[],
    users: User[],
  ): Promise<number> {
    let bookmarkCount = 0;

    for (const bookmarkData of ENHANCED_BOOKMARK_SEED_DATA) {
      const user = users.find((u) => u.username === bookmarkData.userUsername);
      if (!user) continue;

      const story = stories.find((s) => s.title === bookmarkData.storyTitle);
      if (!story) continue;

      const createdAt = new Date();
      createdAt.setDate(createdAt.getDate() - bookmarkData.createdDaysAgo);

      // 映射中文分类到英文enum值
      const categoryMap: { [key: string]: BookmarkCategory } = {
        点亮回忆: BookmarkCategory.FAVORITES,
        成功点亮: BookmarkCategory.FAVORITES,
        友情故事: BookmarkCategory.INSPIRATION,
        励志故事: BookmarkCategory.INSPIRATION,
        VIP精选: BookmarkCategory.FAVORITES,
        感人故事: BookmarkCategory.INSPIRATION,
        喜欢的故事: BookmarkCategory.DEFAULT,
        青春回忆: BookmarkCategory.INSPIRATION,
        创业励志: BookmarkCategory.REFERENCE,
        教育反思: BookmarkCategory.REFERENCE,
        商业案例: BookmarkCategory.REFERENCE,
        深度思考: BookmarkCategory.REFERENCE,
        情感共鸣: BookmarkCategory.INSPIRATION,
      };

      const bookmark = manager.create(Bookmark, {
        userId: user.id,
        storyId: story.id,
        category: categoryMap[bookmarkData.category] || BookmarkCategory.CUSTOM,
        customCategoryName: categoryMap[bookmarkData.category]
          ? null
          : bookmarkData.category,
        note: bookmarkData.note,
        metadata: {
          isPublic: bookmarkData.isPublic,
          tags: bookmarkData.tags,
          priority: bookmarkData.priority,
        },
        createdAt: createdAt,
      });

      await manager.save(Bookmark, bookmark);
      bookmarkCount++;
    }

    return bookmarkCount;
  }

  /**
   * 创建时间线数据
   */
  private async seedTimeline(
    manager: EntityManager,
    users: User[],
  ): Promise<number> {
    let timelineCount = 0;

    for (const eventData of ENHANCED_TIMELINE_EVENT_SEED_DATA) {
      const user = users.find((u) => u.username === eventData.userUsername);
      if (!user) continue;

      // 映射种子数据中的字符串到实际的enum值
      const eventTypeMap: { [key: string]: EventType } = {
        birth: EventType.BIRTH,
        education: EventType.EDUCATION,
        work: EventType.WORK,
        relationship: EventType.RELATIONSHIP,
        achievement: EventType.ACHIEVEMENT,
        travel: EventType.TRAVEL,
        milestone: EventType.MILESTONE,
        loss: EventType.CUSTOM,
        celebration: EventType.CUSTOM,
        other: EventType.CUSTOM,
      };

      const importanceMap: { [key: string]: EventImportance } = {
        critical: EventImportance.CRITICAL,
        major: EventImportance.HIGH,
        normal: EventImportance.MEDIUM,
        minor: EventImportance.LOW,
      };

      const timelineEvent = manager.create(TimelineEvent, {
        userId: user.id,
        title: eventData.title,
        description: eventData.description,
        eventDate: new Date(eventData.eventDate),
        eventType: eventTypeMap[eventData.eventType] || EventType.CUSTOM,
        importance:
          importanceMap[eventData.importance] || EventImportance.MEDIUM,
        visibility: eventData.isPublic
          ? EventVisibility.PUBLIC
          : EventVisibility.PRIVATE,
        location: eventData.location,
        tags: eventData.tags,
        attachments: eventData.attachments || null,
        relatedStoryId: null, // 暂时不处理关联故事
        customData: eventData.relatedStoryTitle
          ? {
              linkedEvents: [eventData.relatedStoryTitle],
            }
          : null,
      });

      await manager.save(TimelineEvent, timelineEvent);
      timelineCount++;
    }

    return timelineCount;
  }

  /**
   * 创建分享数据
   */
  private async seedShares(
    manager: EntityManager,
    stories: Story[],
    users: User[],
  ): Promise<{ shares: number; accessLogs: number }> {
    const shares: Share[] = [];
    let shareCount = 0;
    let accessLogCount = 0;

    // 创建分享链接
    for (const shareData of ENHANCED_SHARE_SEED_DATA) {
      const sharer = users.find((u) => u.username === shareData.sharerUsername);
      if (!sharer) continue;

      const story = stories.find((s) => s.title === shareData.storyTitle);
      if (!story) continue;

      const createdAt = new Date();
      createdAt.setDate(createdAt.getDate() - shareData.createdDaysAgo);

      let expiresAt = null;
      if (shareData.expiresAt) {
        expiresAt = new Date(shareData.expiresAt);
      }

      // 映射种子数据的分享类型到实体枚举
      const shareTypeMap: { [key: string]: ShareType } = {
        public: ShareType.PUBLIC,
        password: ShareType.LIMITED,
        limited: ShareType.LIMITED,
      };

      const share = manager.create(Share, {
        storyId: story.id,
        userId: sharer.id,
        token: shareData.shareToken,
        shareType: shareTypeMap[shareData.shareType] || ShareType.PUBLIC,
        password: shareData.password,
        accessLimit: shareData.maxAccessCount || -1,
        accessCount: shareData.currentAccessCount,
        expiresAt: expiresAt,
        status: shareData.isActive ? ShareStatus.ACTIVE : ShareStatus.DISABLED,
        createdAt: createdAt,
      });

      const savedShare = await manager.save(Share, share);
      shares.push(savedShare);
      shareCount++;
    }

    // 创建访问日志
    for (const logData of ENHANCED_SHARE_ACCESS_LOG_SEED_DATA) {
      const share = shares.find((s) => s.token === logData.shareToken);
      if (!share) continue;

      let accessorUserId = null;
      if (logData.accessorUsername) {
        const accessor = users.find(
          (u) => u.username === logData.accessorUsername,
        );
        if (accessor) {
          accessorUserId = accessor.id;
        }
      }

      const accessedAt = new Date();
      accessedAt.setDate(accessedAt.getDate() - logData.accessDaysAgo);

      // 修复无效的IP地址
      const fixInvalidIp = (ip: string): string => {
        const parts = ip.split(".");
        if (parts.length === 4) {
          const fixedParts = parts.map((part) => {
            const num = parseInt(part);
            return isNaN(num) || num > 255
              ? Math.floor(Math.random() * 255).toString()
              : part;
          });
          return fixedParts.join(".");
        }
        return "192.168.1." + Math.floor(Math.random() * 255);
      };

      const accessLog = manager.create(ShareAccessLog, {
        shareId: share.id,
        ipAddress: fixInvalidIp(logData.accessorIp),
        userAgent: logData.accessorUserAgent,
        userId: accessorUserId,
        isSuccessful: logData.accessResult === "success",
        failureReason:
          logData.accessResult !== "success" ? logData.accessResult : null,
        referer: logData.referrer,
        metadata: logData.accessLocation
          ? { location: logData.accessLocation }
          : null,
        createdAt: accessedAt,
      });

      await manager.save(ShareAccessLog, accessLog);
      accessLogCount++;
    }

    return { shares: shareCount, accessLogs: accessLogCount };
  }

  /**
   * 创建通知数据
   */
  private async seedNotifications(
    manager: EntityManager,
    users: User[],
  ): Promise<number> {
    let notificationCount = 0;

    for (const notificationData of ENHANCED_NOTIFICATION_SEED_DATA) {
      const recipient = users.find(
        (u) => u.username === notificationData.recipientUsername,
      );
      if (!recipient) continue;

      const createdAt = new Date();
      createdAt.setDate(createdAt.getDate() - notificationData.createdDaysAgo);

      const notification = manager.create(Notification, {
        userId: recipient.id,
        type: notificationData.type as NotificationType,
        title: notificationData.title,
        content: notificationData.content,
        data: notificationData.data,
        isRead: notificationData.isRead,
        priority: notificationData.priority,
        createdAt: createdAt,
      });

      await manager.save(Notification, notification);
      notificationCount++;
    }

    return notificationCount;
  }

  /**
   * 生成统计信息
   */
  private async generateStats(
    manager: EntityManager,
  ): Promise<Record<string, number>> {
    const users = await manager.count(User);
    const stories = await manager.count(Story);
    const characters = await manager.count(Character);
    const lightRequests = await manager.count(LightRequest);
    const successfulLightings = await manager.count(CharacterLighting);
    const friendGroups = await manager.count(FriendGroup);

    // 计算好友对数
    const follows = await manager.find(UserFollow, {
      where: { status: FollowStatus.ACTIVE },
    });
    const friendPairs = new Set<string>();

    follows.forEach((f1: UserFollow) => {
      const reverse = follows.find(
        (f2: UserFollow) =>
          f2.followerId === f1.followingId && f2.followingId === f1.followerId,
      );
      if (reverse) {
        const pair = [f1.followerId, f1.followingId].sort().join("-");
        friendPairs.add(pair);
      }
    });

    return {
      users,
      stories,
      characters,
      lightRequests,
      successfulLightings,
      friendPairs: friendPairs.size,
      friendGroups,
    };
  }

  /**
   * 清空所有数据
   */
  async clearAll(): Promise<void> {
    await this.dataSource.transaction(async (manager) => {
      await this.clearExistingData(manager);
    });
  }

  /**
   * 获取当前数据统计
   */
  async getStats(): Promise<Record<string, number>> {
    return await this.dataSource.transaction(async (manager) => {
      return await this.generateStats(manager);
    });
  }

  /**
   * 验证数据完整性
   */
  async validateData(): Promise<boolean> {
    try {
      const stats = await this.getStats();

      // 基本验证
      if (stats.users === 0 || stats.stories === 0 || stats.characters === 0) {
        this.logger.error("数据验证失败：缺少基础数据");
        return false;
      }

      // 验证点亮关系
      const lightings = await this.dataSource
        .getRepository(CharacterLighting)
        .find();
      for (const lighting of lightings) {
        const character = await this.dataSource
          .getRepository(Character)
          .findOne({
            where: { id: lighting.characterId },
          });
        if (!character || !character.isLighted) {
          this.logger.error(
            `数据验证失败：点亮关系不一致 - 人物 ${lighting.characterId}`,
          );
          return false;
        }
      }

      this.logger.log("✅ 数据完整性验证通过");
      return true;
    } catch (error) {
      this.logger.error("数据验证失败:", error);
      return false;
    }
  }
}
