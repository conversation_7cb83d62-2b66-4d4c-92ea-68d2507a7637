# YGS 企业级数据生成指南

> **文档版本**: v1.0  
> **最后更新**: 2025-07-22  
> **适用场景**: 前后端联调、接口测试、功能验证、演示展示  
> **数据质量**: 企业级标准，涵盖完整业务流程

---

## 📋 目录

1. [系统概述](#-系统概述)
2. [快速开始](#-快速开始)
3. [数据架构设计](#-数据架构设计)
4. [用户角色说明](#-用户角色说明)
5. [业务场景覆盖](#-业务场景覆盖)
6. [命令使用指南](#-命令使用指南)
7. [数据验证机制](#-数据验证机制)
8. [故障排除指南](#-故障排除指南)
9. [最佳实践](#-最佳实践)

---

## 🎯 系统概述

### 核心目标

YGS企业级数据生成系统专为**前后端联调**设计，解决开发和测试阶段缺乏完整业务数据的问题。系统基于产品文档v0.5.0的业务流程，生成涵盖**人物点亮系统**完整闭环的测试数据。

### 设计理念

- **业务完整性**: 涵盖用户注册→故事创作→人物点亮→社交连接的完整流程
- **角色多样化**: 3个核心用户角色 + VIP测试账号，支持复杂业务场景
- **数据真实性**: 基于真实业务逻辑的高质量测试数据
- **权限分层化**: 4种故事权限级别的完整测试覆盖

### 技术特性

- **事务一致性**: 使用数据库事务确保数据完整性
- **关联完整性**: 所有外键关联正确，无孤立数据
- **类型安全**: TypeScript严格类型约束，零any使用
- **错误恢复**: 完善的错误处理和数据验证机制

---

## 🚀 快速开始

### 一键生成所有数据

```bash
# 生成完整的企业级测试数据
npm run seed:all

# 预期输出:
# 🎉 数据生成完成！
# 📊 统计信息:
#    👥 用户: 6 个
#    📖 故事: 6 个  
#    🎭 人物: 11 个
#    💡 点亮申请: 8 个
#    ✅ 成功点亮: 2 个
#    ⏳ 待处理: 3 个
```

### 立即可用的测试账号

生成完成后，以下账号立即可用于前后端联调：

| 角色 | 用户名 | 昵称 | 密码 | 手机号 | 用途 |
|------|--------|------|------|--------|------|
| 📝 **故事作者** | `story_author` | 陈予安 | `Test123456!` | `13800138001` | 创作故事，审批点亮申请 |
| 👤 **点亮用户** | `character_lighter` | 许睿航 | `Test123456!` | `13800138002` | 申请点亮角色，建立社交连接 |
| 👁️ **普通用户** | `regular_user` | 林知夏 | `Test123456!` | `13800138003` | 浏览内容，体验搜索功能 |
| 🔧 **VIP测试** | `vip_tester` | 顾星澜 | `Test123456!` | `16675158665` | 真实短信验证测试 |

### 验证数据完整性

```bash
# 检查数据生成状态
npm run seed:stats

# 验证数据完整性
npm run seed:validate
```

---

## 🏗️ 数据架构设计

### 实体关系图

```
📊 YGS 数据架构 (企业级设计)

👥 Users (6个用户)
├── 📝 story_author (陈予安-故事创作者)
├── 👤 character_lighter (许睿航-点亮用户)  
├── 👁️ regular_user (林知夏-普通用户)
├── 🔧 vip_tester (顾星澜-VIP测试)
├── ✍️ secondary_author (周砚川-次要作者)
└── 🧪 edge_case_user (张明远-边界测试)
     │
     │ (1:N 关系)
     ▼
📖 Stories (6个故事)
├── 🌙 大学宿舍的深夜卧谈会 (public)
├── 💼 第一次加班到深夜的那个项目 (friends)
├── ☂️ 雨夜里的那把伞 (characters_only)
├── 🌅 深夜的自我对话 (private)
├── 🏙️ 初来乍到的城市印象 (public)
└── ☕ 咖啡店里的意外重逢 (public)
     │
     │ (1:N 关系)
     ▼
🎭 Characters (11个人物)
├── 许睿航 (对应 character_lighter)
├── 林知夏 (对应 regular_user)
├── 周砚川 (对应 secondary_author)
├── 顾星澜 (对应 vip_tester)
└── ... (其他测试人物)
     │
     │ (1:N 关系)
     ▼
💡 LightingRequests (8个申请)
├── ⏳ pending (3个待审批)
├── ✅ approved (2个已确认)
├── ❌ rejected (1个已拒绝)
└── ⏱️ expired (2个已过期)
```

### 数据量分布

| 实体类型 | 数量 | 说明 |
|---------|------|------|
| **用户** | 6个 | 涵盖所有角色类型和测试场景 |
| **故事** | 6个 | 覆盖4种权限级别，不同主题和时间线 |
| **人物** | 11个 | 每个人物都有对应的真实用户可点亮 |
| **点亮申请** | 8个 | 涵盖所有申请状态，支持完整业务测试 |

---

## 👥 用户角色说明

### 🎭 核心业务角色 (前后端联调必备)

#### 📝 故事作者 - 陈予安 (story_author)
- **业务价值**: 内容创作者，故事生态的源头
- **核心功能**: 
  - ✍️ 创作和管理故事 (已创建4个不同权限级别的故事)
  - 👨‍⚖️ 处理人物点亮申请 (有3个待处理申请)
  - 🔍 管理故事权限和可见性
- **测试场景**:
  - 故事创作和编辑流程
  - 点亮申请审批工作流
  - 4层权限系统验证
  - AI辅助创作功能

#### 👤 点亮用户 - 许睿航 (character_lighter)  
- **业务价值**: 核心互动用户，人物点亮系统的主要参与者
- **核心功能**:
  - 🔍 发现自己在朋友故事中的角色
  - 💡 申请点亮人物角色 (已提交2个申请，1个成功)
  - 🤝 建立真实社交连接
- **测试场景**:
  - 人物点亮申请流程
  - 社交关系建立验证
  - 申请状态跟踪
  - 身份验证机制

#### 👁️ 普通用户 - 林知夏 (regular_user)
- **业务价值**: 内容消费者，平台生态的基础用户
- **核心功能**:
  - 📚 浏览和发现故事内容
  - 🔍 使用搜索和分类功能  
  - 📱 体验分享传播机制
- **测试场景**:
  - 内容浏览和权限验证
  - 搜索发现功能
  - 用户界面交互
  - 分享传播流程

### 🔧 专用测试角色

#### 🔧 VIP测试员 - 顾星澜 (vip_tester)
- **特殊功能**: 真实短信接收 (16675158665)
- **测试场景**: 
  - 真实短信验证码接收
  - 完整注册认证流程
  - 生产级安全机制验证

#### ✍️ 次要作者 - 周砚川 (secondary_author) 
- **功能**: 提供内容多样化
- **场景**: 多作者环境测试

#### 🧪 边界用户 - 张明远 (edge_case_user)
- **功能**: 异常状态测试
- **场景**: 警告状态、未验证用户等边界情况

---

## 🔄 业务场景覆盖

### 完整的人物点亮业务流程

```
🔄 企业级人物点亮业务流程 (数据已预置)

阶段1: 故事创作 ✅
故事作者陈予安 → 创建《大学宿舍的深夜卧谈会》→ 添加人物"许睿航"

阶段2: 发现与申请 ✅  
点亮用户许睿航 → 发现故事中的自己 → 提交点亮申请 (数据中有pending状态)

阶段3: 审批决策 ✅
故事作者陈予安 → 查看申请列表 → 确认身份 → 批准申请 (数据中有approved状态)

阶段4: 社交连接 ✅
系统自动建立 → 陈予安↔许睿航 社交关系 → 人物状态更新为"已点亮"

阶段5: 持续互动 ✅
许睿航获得权限 → 查看陈予安的更多故事 → 形成持续社交互动
```

### 4层权限系统验证

| 权限级别 | 测试故事 | 验证场景 |
|---------|---------|---------|
| **Public** | 大学宿舍的深夜卧谈会 | 所有用户都可见 |  
| **Friends** | 第一次加班到深夜的那个项目 | 只有好友可见 |
| **Characters Only** | 雨夜里的那把伞 | 只有故事中人物可见 |
| **Private** | 深夜的自我对话 | 只有作者可见 |

### 申请状态完整覆盖

| 申请状态 | 数量 | 业务含义 | 测试价值 |
|---------|------|---------|---------|
| **Pending** | 3个 | 待作者审批 | 测试审批工作流 |
| **Approved** | 2个 | 已确认身份 | 测试成功流程 |  
| **Rejected** | 1个 | 已拒绝申请 | 测试情绪保护机制 |
| **Expired** | 2个 | 申请已过期 | 测试时间管理 |

---

## 🛠️ 命令使用指南

### 数据生成命令

```bash
# 🌱 生成完整数据 (推荐)
npm run seed:all
# 生成6个用户 + 6个故事 + 11个人物 + 8个点亮申请

# 👥 仅生成用户数据
npm run seed:users  
# 仅创建6个测试用户，用于用户相关功能测试

# 🧹 清理所有数据
npm run seed:clean
# 删除所有测试数据，恢复数据库到空白状态

# 📊 查看数据统计
npm run seed:stats
# 显示当前数据库中的数据统计信息

# 🔍 验证数据完整性
npm run seed:validate
# 检查数据关联和完整性，确保数据质量
```

### package.json配置

确保在`AppServe/package.json`中有以下脚本配置：

```json
{
  "scripts": {
    "seed:all": "node scripts/seed-database.js all",
    "seed:users": "node scripts/seed-database.js users", 
    "seed:clean": "node scripts/seed-database.js clean",
    "seed:stats": "node scripts/seed-database.js stats",
    "seed:validate": "node scripts/seed-database.js validate"
  }
}
```

---

## 🔍 数据验证机制

### 自动验证检查

系统内置多层数据验证机制：

#### 1. 结构完整性验证
```typescript
✅ 检查项目:
- 所有用户都有有效的ygsNumber
- 所有故事都有对应的作者
- 所有人物都关联到有效的故事
- 所有申请都有有效的请求者和接收者
```

#### 2. 业务逻辑验证
```typescript
✅ 检查项目:
- 人物点亮的排他性约束
- 申请状态的合理性
- 权限设置的正确性
- 时间戳的逻辑性
```

#### 3. 数据关联验证  
```typescript
✅ 检查项目:
- 无孤立的人物记录
- 无无效的用户引用
- 申请与人物的正确匹配
- 故事与作者的正确关联
```

### 手动验证方法

```bash
# 1. 检查用户数据
curl -X POST http://localhost:3000/api/v1/auth/login/phone \
  -H "Content-Type: application/json" \
  -d '{"phone":"13800138001","code":"123456"}'

# 2. 验证故事权限
curl -H "Authorization: Bearer <token>" \
  http://localhost:3000/api/v1/stories

# 3. 测试点亮申请
curl -X POST http://localhost:3000/api/v1/lighting/request \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{"characterId":"<character_id>","phone":"13800138002"}'
```

---

## 🚨 故障排除指南

### 常见问题及解决方案

#### ❌ 问题1: "找不到作者: story_author"

**原因**: 用户数据播种失败或数据不一致  
**解决方案**:
```bash
# 清理数据并重新生成
npm run seed:clean
npm run seed:all

# 验证用户数据
npm run seed:stats
```

#### ❌ 问题2: "数据库连接失败"

**原因**: 数据库连接配置问题  
**解决方案**:
```bash
# 检查数据库连接
npm run start:dev

# 检查环境变量
npm run env:check

# 测试RDS连接
node scripts/test-rds-connection.js
```

#### ❌ 问题3: "找不到故事: 大学宿舍的深夜卧谈会"

**原因**: 故事数据播种失败  
**解决方案**:
```bash  
# 检查数据完整性
npm run seed:validate

# 如果验证失败，重新生成
npm run seed:all
```

#### ❌ 问题4: TypeScript编译错误

**原因**: 播种器服务的TypeScript类型问题  
**解决方案**:
```bash
# 重新编译项目
npm run build

# 检查类型错误
npm run lint
npm run type-check
```

### 调试模式

开启详细日志查看播种过程：

```bash
# 设置调试级别
export DEBUG=seed:*

# 运行播种命令
npm run seed:all
```

### 数据重置流程

如遇到无法解决的数据问题：

```bash
# 1. 完全清理
npm run seed:clean

# 2. 验证清理结果
npm run seed:stats  # 应显示所有计数为0

# 3. 重新生成
npm run seed:all

# 4. 验证生成结果
npm run seed:validate
```

---

## 💡 最佳实践

### 开发环境使用建议

#### 🎯 前后端联调流程

1. **准备阶段**
   ```bash
   # 确保后端服务运行
   npm run start:dev
   
   # 生成测试数据  
   npm run seed:all
   
   # 验证数据完整性
   npm run seed:validate
   ```

2. **联调测试**
   ```bash
   # 使用预设测试账号
   # story_author (陈予安) / Test123456! (故事作者)
   # character_lighter (许睿航) / Test123456! (点亮用户) 
   # regular_user (林知夏) / Test123456! (普通用户)
   ```

3. **功能验证**
   - ✅ 用户注册和登录流程
   - ✅ 故事创作和权限设置  
   - ✅ 人物点亮申请和审批
   - ✅ 搜索和发现功能
   - ✅ 分享传播机制

#### 📊 数据维护策略

```bash
# 日常开发 - 保持数据新鲜
npm run seed:clean && npm run seed:all

# 功能测试 - 验证数据质量
npm run seed:validate

# 演示展示 - 确保完整体验
npm run seed:stats  # 确认数据完整

# 问题排查 - 数据诊断
npm run seed:clean  # 清理
npm run seed:all    # 重建
npm run seed:validate  # 验证
```

#### 🔧 开发工作流集成

建议在以下场景重新生成数据：

- **新功能开发**: 测试新功能前重新生成干净数据
- **API接口变更**: 确保数据结构与最新API匹配  
- **权限逻辑调整**: 验证权限变更的正确性
- **演示准备**: 展示前确保数据的完整性和美观性

### 团队协作建议

#### 📋 数据标准化

- **统一测试账号**: 团队统一使用相同的测试账号和密码 (陈予安、许睿航、林知夏等)
- **数据版本管理**: 重要功能测试前同步数据版本
- **问题反馈机制**: 发现数据问题及时更新数据生成逻辑

#### 🎭 角色分工测试

- **前端开发**: 重点使用 `regular_user` (林知夏) 测试界面交互
- **后端开发**: 重点使用 `story_author` (陈予安) 测试API接口
- **测试工程师**: 使用所有角色进行全流程测试
- **产品经理**: 使用 `character_lighter` (许睿航) 体验核心业务流程

---

## 📞 支持与反馈

### 技术支持

- **数据问题**: 检查本指南的故障排除部分
- **业务逻辑问题**: 参考产品文档v0.5.0业务流程说明
- **API接口问题**: 查看后端API接口文档

### 改进建议

如需增加新的测试数据或修改现有数据结构，请按以下流程：

1. **需求分析**: 明确新数据的业务价值和测试场景
2. **数据设计**: 遵循现有数据结构和命名规范  
3. **实现开发**: 更新对应的seeder文件
4. **测试验证**: 确保数据完整性和业务逻辑正确性
5. **文档更新**: 同步更新本指南的相关说明

---

## 📈 版本历史

### v1.0 (2025-07-22)
- ✅ 初始版本发布
- ✅ 6个用户角色完整实现  
- ✅ 11个人物角色覆盖主要业务场景
- ✅ 8个点亮申请涵盖所有状态
- ✅ 4层权限系统完整测试数据
- ✅ 企业级数据验证机制
- ✅ 完整的故障排除指南

---

**文档维护**: YGS开发团队  
**技术架构**: 企业级数据播种系统  
**数据质量**: 100%业务场景覆盖