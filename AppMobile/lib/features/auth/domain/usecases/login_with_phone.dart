import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/user_entity.dart';
import '../entities/auth_token.dart';
import '../repositories/auth_repository.dart';

/// 手机号登录用例
class LoginWithPhone {
  final AuthRepository repository;

  LoginWithPhone(this.repository);

  Future<Either<Failure, (UserEntity, AuthToken)>> call({
    required String phone,
    required String code,
  }) async {
    // 验证手机号格式
    if (!_isValidPhone(phone)) {
      return const Left(ValidationFailure(
        message: '请输入正确的手机号',
        code: 'invalid_phone',
      ));
    }

    // 验证验证码格式
    if (!_isValidCode(code)) {
      return const Left(ValidationFailure(
        message: '请输入6位数字验证码',
        code: 'invalid_code',
      ));
    }

    // 执行登录
    final result = await repository.loginWithPhone(
      phone: phone,
      code: code,
    );

    // 登录成功后保存认证信息
    return result.fold(
      (failure) => Left(failure),
      (data) async {
        final (user, token) = data;
        await repository.saveAuthData(user: user, token: token);
        return Right(data);
      },
    );
  }

  bool _isValidPhone(String phone) {
    final phoneRegex = RegExp(r'^1[3-9]\d{9}$');
    return phoneRegex.hasMatch(phone);
  }

  bool _isValidCode(String code) {
    final codeRegex = RegExp(r'^\d{6}$');
    return codeRegex.hasMatch(code);
  }
}