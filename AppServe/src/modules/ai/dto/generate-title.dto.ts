import {
  IsArray,
  IsString,
  ArrayMaxSize,
  ArrayMinSize,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "class-validator";
import { ApiProperty } from "@nestjs/swagger";

/**
 * 生成标题请求DTO
 * 用于AI标题生成功能的请求参数验证
 */
export class GenerateTitleDto {
  @ApiProperty({
    description: "关键词数组，用于生成标题",
    example: ["友情", "成长", "青春"],
    type: [String],
    minItems: 1,
    maxItems: 5,
  })
  @IsArray({ message: "关键词必须是数组格式" })
  @ArrayMinSize(1, { message: "至少需要1个关键词" })
  @ArrayMaxSize(5, { message: "最多只能有5个关键词" })
  @IsString({ each: true, message: "每个关键词必须是字符串" })
  @MinLength(1, { each: true, message: "关键词不能为空" })
  @MaxLength(10, { each: true, message: "单个关键词不能超过10个字符" })
  keywords: string[];
}
