/**
 * 服务层相关的类型定义
 * 提供业务逻辑层的类型安全
 */

/**
 * 服务操作结果接口
 * 标准化的服务层返回格式
 */
export interface ServiceResult<T = unknown> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  code?: string;
}

/**
 * 缓存配置接口
 */
export interface CacheConfig {
  key: string;
  ttl?: number; // 秒
  compress?: boolean;
  serialize?: boolean;
}

/**
 * 数据库查询选项接口
 */
export interface QueryOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "ASC" | "DESC";
  filters?: Record<string, unknown>;
  include?: string[];
  exclude?: string[];
}

/**
 * 批量操作结果接口
 */
export interface BatchOperationResult<T = unknown> {
  total: number;
  processed: number;
  succeeded: number;
  failed: number;
  errors: Array<{
    index: number;
    error: string;
    data?: T;
  }>;
  results: T[];
}

/**
 * 文件处理结果接口
 */
export interface FileProcessResult {
  filename: string;
  originalName: string;
  size: number;
  mimeType: string;
  url: string;
  path?: string;
  hash?: string;
  metadata?: Record<string, unknown>;
}

/**
 * 搜索结果接口
 */
export interface SearchResult<T = unknown> {
  items: T[];
  total: number;
  query: string;
  filters?: Record<string, unknown>;
  aggregations?: Record<string, unknown>;
  suggestions?: string[];
}

/**
 * 统计数据接口
 */
export interface StatisticsData {
  [key: string]: number | string | Date | StatisticsData;
}

/**
 * 时间范围接口
 */
export interface TimeRange {
  startDate: Date;
  endDate: Date;
}

/**
 * 排序选项接口
 */
export interface SortOption {
  field: string;
  direction: "ASC" | "DESC";
}
