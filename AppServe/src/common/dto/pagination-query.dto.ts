import { IsOptional, <PERSON>I<PERSON>, <PERSON>, <PERSON> } from "class-validator";
import { Type } from "class-transformer";

/**
 * 分页查询DTO
 * 提供统一的分页参数
 */
export class PaginationQueryDto {
  @IsOptional()
  @Type(() => Number)
  @IsInt({
    message: "页码必须是整数",
  })
  @Min(1, {
    message: "页码最小值为1",
  })
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @IsInt({
    message: "每页数量必须是整数",
  })
  @Min(1, {
    message: "每页数量最小值为1",
  })
  @Max(100, {
    message: "每页数量最大值为100",
  })
  limit?: number = 10;
}
