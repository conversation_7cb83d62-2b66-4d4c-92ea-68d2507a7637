import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
  ConflictException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import type { SelectQueryBuilder } from "typeorm";
import { Repository } from "typeorm";
import { Character } from "./entities/character.entity";
import { CharacterLighting } from "./entities/character-lighting.entity";
import { User } from "../users/entities/user.entity";
import { Story } from "../stories/entities/story.entity";
import type {
  CreateCharacterDto,
  UpdateCharacterDto,
  CharacterQueryDto,
  CharacterStatisticsDto,
} from "./dto";
import { CharacterResponseDto } from "./dto";
import { PaginatedResponse } from "../../common/dto/paginated-response.dto";

/**
 * 人物服务类
 * 负责人物的创建、管理、点亮状态维护和业务逻辑处理
 */
@Injectable()
export class CharactersService {
  constructor(
    @InjectRepository(Character)
    private readonly characterRepository: Repository<Character>,
    @InjectRepository(CharacterLighting)
    private readonly characterLightingRepository: Repository<CharacterLighting>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Story)
    private readonly storyRepository: Repository<Story>,
  ) {}

  /**
   * 创建人物
   * @param creatorId 创建者ID
   * @param createData 创建数据
   * @returns 创建的人物
   */
  async createCharacter(
    creatorId: string,
    createData: CreateCharacterDto,
  ): Promise<Character> {
    // 验证创建者存在
    const creator = await this.userRepository.findOne({
      where: { id: creatorId },
    });
    if (!creator) {
      throw new NotFoundException("创建者不存在");
    }

    // 验证人物名称在该创建者下是否唯一
    const existingCharacter = await this.characterRepository.findOne({
      where: {
        creatorId,
        name: createData.name,
        isActive: true,
      },
    });

    if (existingCharacter) {
      throw new ConflictException("该人物名称已存在");
    }

    // 创建人物
    const character = this.characterRepository.create({
      ...createData,
      creatorId,
    });

    const savedCharacter = await this.characterRepository.save(character);

    return this.findById(savedCharacter.id);
  }

  /**
   * 更新人物
   * @param characterId 人物ID
   * @param updateData 更新数据
   * @param userId 当前用户ID
   * @returns 更新后的人物
   */
  async updateCharacter(
    characterId: string,
    updateData: UpdateCharacterDto,
    userId: string,
  ): Promise<Character> {
    const character = await this.characterRepository.findOne({
      where: { id: characterId },
      relations: ["creator"],
    });

    if (!character) {
      throw new NotFoundException("人物不存在");
    }

    // 权限验证：只有创建者可以编辑
    if (character.creatorId !== userId) {
      throw new ForbiddenException("无权编辑此人物");
    }

    // 如果更新名称，需要验证唯一性
    if (updateData.name && updateData.name !== character.name) {
      const existingCharacter = await this.characterRepository.findOne({
        where: {
          creatorId: userId,
          name: updateData.name,
          isActive: true,
        },
      });

      if (existingCharacter) {
        throw new ConflictException("该人物名称已存在");
      }
    }

    // 更新人物
    await this.characterRepository.update(characterId, updateData);

    return this.findById(characterId);
  }

  /**
   * 删除人物
   * @param characterId 人物ID
   * @param userId 当前用户ID
   */
  async deleteCharacter(characterId: string, userId: string): Promise<void> {
    const character = await this.characterRepository.findOne({
      where: { id: characterId },
      relations: ["creator", "lightings"],
    });

    if (!character) {
      throw new NotFoundException("人物不存在");
    }

    // 权限验证：只有创建者可以删除
    if (character.creatorId !== userId) {
      throw new ForbiddenException("无权删除此人物");
    }

    // 检查是否已被点亮
    if (character.isLighted) {
      throw new BadRequestException("已点亮的人物不能删除，请先取消点亮");
    }

    // 软删除：设置为非激活状态
    await this.characterRepository.update(characterId, { isActive: false });
  }

  /**
   * 根据ID查找人物
   * @param characterId 人物ID
   * @returns 人物详情
   */
  async findById(characterId: string): Promise<Character> {
    const character = await this.characterRepository.findOne({
      where: { id: characterId, isActive: true },
      relations: ["creator", "stories", "lightings", "lightings.lighterUser"],
    });

    if (!character) {
      throw new NotFoundException("人物不存在");
    }

    return character;
  }

  /**
   * 验证人物名称唯一性
   * @param creatorId 创建者ID
   * @param name 人物名称
   * @returns 是否可用
   */
  async validateCharacterName(
    creatorId: string,
    name: string,
  ): Promise<boolean> {
    const existingCharacter = await this.characterRepository.findOne({
      where: {
        creatorId,
        name,
        isActive: true,
      },
    });

    return !existingCharacter;
  }

  /**
   * 添加人物到故事
   * @param storyId 故事ID
   * @param characterId 人物ID
   * @param userId 当前用户ID
   */
  async addCharacterToStory(
    storyId: string,
    characterId: string,
    userId: string,
  ): Promise<void> {
    // 验证故事存在且用户有权限
    const story = await this.storyRepository.findOne({
      where: { id: storyId },
    });

    if (!story) {
      throw new NotFoundException("故事不存在");
    }

    if (story.userId !== userId) {
      throw new ForbiddenException("无权修改此故事");
    }

    // 验证人物存在且属于当前用户
    const character = await this.characterRepository.findOne({
      where: { id: characterId, creatorId: userId, isActive: true },
    });

    if (!character) {
      throw new NotFoundException("人物不存在或无权访问");
    }

    // 建立关联
    await this.storyRepository
      .createQueryBuilder()
      .relation(Story, "characters")
      .of(storyId)
      .add(characterId);
  }

  /**
   * 从故事中移除人物
   * @param storyId 故事ID
   * @param characterId 人物ID
   * @param userId 当前用户ID
   */
  async removeCharacterFromStory(
    storyId: string,
    characterId: string,
    userId: string,
  ): Promise<void> {
    // 验证故事存在且用户有权限
    const story = await this.storyRepository.findOne({
      where: { id: storyId },
    });

    if (!story) {
      throw new NotFoundException("故事不存在");
    }

    if (story.userId !== userId) {
      throw new ForbiddenException("无权修改此故事");
    }

    // 移除关联
    await this.storyRepository
      .createQueryBuilder()
      .relation(Story, "characters")
      .of(storyId)
      .remove(characterId);
  }

  /**
   * 获取用户的人物列表
   * @param userId 用户ID
   * @param query 查询参数
   * @returns 分页的人物列表
   */
  async getUserCharacters(
    userId: string,
    query: CharacterQueryDto,
  ): Promise<PaginatedResponse<CharacterResponseDto>> {
    const queryBuilder = this.createCharacterQueryBuilder(query);

    queryBuilder.where("character.creatorId = :userId", { userId });

    return this.executeCharacterQuery(queryBuilder, query);
  }

  /**
   * 获取故事的人物列表
   * @param storyId 故事ID
   * @param query 查询参数
   * @returns 分页的人物列表
   */
  async getStoryCharacters(
    storyId: string,
    query: CharacterQueryDto,
  ): Promise<PaginatedResponse<CharacterResponseDto>> {
    const queryBuilder = this.createCharacterQueryBuilder(query);

    queryBuilder.innerJoin("character.stories", "story");
    queryBuilder.where("story.id = :storyId", { storyId });

    return this.executeCharacterQuery(queryBuilder, query);
  }

  /**
   * 获取可访问的人物列表
   * @param query 查询参数
   * @param currentUserId 当前用户ID
   * @returns 分页的人物列表
   */
  async getAccessibleCharacters(
    query: CharacterQueryDto,
    currentUserId: string,
  ): Promise<PaginatedResponse<CharacterResponseDto>> {
    const queryBuilder = this.createCharacterQueryBuilder(query);

    // 可访问的人物：自己创建的 + 已点亮的 + 公开故事中的
    queryBuilder.where(
      "(character.creatorId = :currentUserId OR character.isLighted = true)",
      {
        currentUserId,
      },
    );

    return this.executeCharacterQuery(queryBuilder, query);
  }

  /**
   * 更新人物点亮状态
   * @param characterId 人物ID
   */
  async updateLightingStatus(characterId: string): Promise<void> {
    const character = await this.characterRepository.findOne({
      where: { id: characterId },
      relations: ["lightings"],
    });

    if (!character) {
      throw new NotFoundException("人物不存在");
    }

    // 检查是否有已确认的点亮
    const hasConfirmedLighting = character.lightings.some(
      (lighting) => lighting.confirmedAt !== null,
    );

    // 更新点亮状态和计数
    const lightingCount = character.lightings.filter(
      (lighting) => lighting.confirmedAt !== null,
    ).length;

    await this.characterRepository.update(characterId, {
      isLighted: hasConfirmedLighting,
      lightingCount,
    });
  }

  /**
   * 获取人物统计数据
   * @param userId 用户ID
   * @returns 统计数据
   */
  async getUserCharacterStatistics(
    userId: string,
  ): Promise<CharacterStatisticsDto> {
    const characters = await this.characterRepository.find({
      where: { creatorId: userId, isActive: true },
      relations: ["lightings"],
    });

    const totalCharacters = characters.length;
    const lightedCharacters = characters.filter((c) => c.isLighted).length;
    const unlightedCharacters = totalCharacters - lightedCharacters;
    const totalLightings = characters.reduce(
      (sum, c) => sum + c.lightingCount,
      0,
    );
    const lightingRate =
      totalCharacters > 0 ? (lightedCharacters / totalCharacters) * 100 : 0;

    // 关系分布统计
    const relationshipDistribution: Record<string, number> = {};
    characters.forEach((character) => {
      const relationship = character.relationship || "未知";
      relationshipDistribution[relationship] =
        (relationshipDistribution[relationship] || 0) + 1;
    });

    // 性别分布统计
    const genderDistribution: Record<string, number> = {};
    characters.forEach((character) => {
      const gender = character.gender || "未知";
      genderDistribution[gender] = (genderDistribution[gender] || 0) + 1;
    });

    return {
      totalCharacters,
      lightedCharacters,
      unlightedCharacters,
      totalLightings,
      lightingRate: Math.round(lightingRate * 100) / 100,
      relationshipDistribution,
      genderDistribution,
    };
  }

  /**
   * 搜索人物
   * @param query 查询参数
   * @param currentUserId 当前用户ID
   * @returns 搜索结果
   */
  async searchCharacters(
    query: CharacterQueryDto,
    currentUserId: string,
  ): Promise<PaginatedResponse<CharacterResponseDto>> {
    const queryBuilder = this.createCharacterQueryBuilder(query);

    // 搜索条件
    if (query.search) {
      queryBuilder.andWhere(
        "(character.name ILIKE :search OR character.description ILIKE :search)",
        {
          search: `%${query.search}%`,
        },
      );
    }

    // 权限过滤：只能搜索自己创建的或已点亮的人物
    queryBuilder.andWhere(
      "(character.creatorId = :currentUserId OR character.isLighted = true)",
      {
        currentUserId,
      },
    );

    return this.executeCharacterQuery(queryBuilder, query);
  }

  /**
   * 获取热门人物
   * @param query 查询参数
   * @returns 热门人物列表
   */
  async getPopularCharacters(
    query: CharacterQueryDto,
  ): Promise<PaginatedResponse<CharacterResponseDto>> {
    const queryBuilder = this.createCharacterQueryBuilder(query);

    queryBuilder.where("character.isLighted = true");
    queryBuilder.orderBy("character.lightingCount", "DESC");
    queryBuilder.addOrderBy("character.createdAt", "DESC");

    return this.executeCharacterQuery(queryBuilder, query);
  }

  /**
   * 获取用户已点亮的人物列表
   * @param userId 用户ID
   * @param query 查询参数
   * @returns 点亮人物列表
   */
  async getLightedCharacters(
    userId: string,
    query: CharacterQueryDto,
  ): Promise<PaginatedResponse<CharacterResponseDto>> {
    // 验证用户存在
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException("用户不存在");
    }

    // 基于CharacterLighting表查询用户点亮的人物
    const queryBuilder = this.characterRepository
      .createQueryBuilder("character")
      .leftJoinAndSelect("character.creator", "creator")
      .leftJoinAndSelect("character.stories", "stories")
      .leftJoinAndSelect("character.lightings", "lightings")
      .leftJoinAndSelect("lightings.lighterUser", "lighterUser")
      .innerJoin(
        "character_lightings",
        "userLighting",
        "userLighting.character_id = character.id",
      )
      .where("character.isActive = true")
      .andWhere("userLighting.lighter_user_id = :userId", { userId })
      .andWhere("userLighting.status = :status", { status: "active" });

    // 应用搜索条件
    if (query.search) {
      queryBuilder.andWhere(
        "(character.name ILIKE :search OR character.description ILIKE :search)",
        {
          search: `%${query.search}%`,
        },
      );
    }

    // 应用过滤条件
    if (query.gender) {
      queryBuilder.andWhere("character.gender = :gender", {
        gender: query.gender,
      });
    }

    if (query.relationship) {
      queryBuilder.andWhere("character.relationship = :relationship", {
        relationship: query.relationship,
      });
    }

    // 按点亮时间倒序排列
    queryBuilder.orderBy("userLighting.confirmed_at", "DESC");
    queryBuilder.addOrderBy("character.name", "ASC");

    // 分页参数
    const page = query.page || 1;
    const limit = query.limit || 10;
    const skip = (page - 1) * limit;

    const [characters, total] = await queryBuilder
      .skip(skip)
      .take(limit)
      .getManyAndCount();

    const items = characters.map(
      (character) => new CharacterResponseDto(character),
    );

    return new PaginatedResponse(items, total, page, limit);
  }

  /**
   * 创建人物查询构建器
   * @param query 查询参数
   * @returns 查询构建器
   */
  private createCharacterQueryBuilder(
    query: CharacterQueryDto,
  ): SelectQueryBuilder<Character> {
    const queryBuilder =
      this.characterRepository.createQueryBuilder("character");

    // 关联查询
    queryBuilder.leftJoinAndSelect("character.creator", "creator");
    queryBuilder.leftJoinAndSelect("character.stories", "stories");
    queryBuilder.leftJoinAndSelect("character.lightings", "lightings");
    queryBuilder.leftJoinAndSelect("lightings.lighterUser", "lighterUser");

    // 基础过滤
    queryBuilder.where("character.isActive = true");

    // 过滤条件
    if (query.creatorId) {
      queryBuilder.andWhere("character.creatorId = :creatorId", {
        creatorId: query.creatorId,
      });
    }

    if (query.isLighted !== undefined) {
      queryBuilder.andWhere("character.isLighted = :isLighted", {
        isLighted: query.isLighted,
      });
    }

    if (query.gender) {
      queryBuilder.andWhere("character.gender = :gender", {
        gender: query.gender,
      });
    }

    if (query.relationship) {
      queryBuilder.andWhere("character.relationship = :relationship", {
        relationship: query.relationship,
      });
    }

    if (query.search) {
      queryBuilder.andWhere(
        "(character.name ILIKE :search OR character.description ILIKE :search)",
        {
          search: `%${query.search}%`,
        },
      );
    }

    // 排序
    switch (query.sortBy) {
      case "newest":
        queryBuilder.orderBy("character.createdAt", "DESC");
        break;
      case "oldest":
        queryBuilder.orderBy("character.createdAt", "ASC");
        break;
      case "name_asc":
        queryBuilder.orderBy("character.name", "ASC");
        break;
      case "name_desc":
        queryBuilder.orderBy("character.name", "DESC");
        break;
      case "most_lighted":
        queryBuilder.orderBy("character.lightingCount", "DESC");
        break;
      default:
        queryBuilder.orderBy("character.createdAt", "DESC");
    }

    return queryBuilder;
  }

  /**
   * 执行人物查询
   * @param queryBuilder 查询构建器
   * @param query 查询参数
   * @returns 分页结果
   */
  private async executeCharacterQuery(
    queryBuilder: SelectQueryBuilder<Character>,
    query: CharacterQueryDto,
  ): Promise<PaginatedResponse<CharacterResponseDto>> {
    const page = query.page || 1;
    const limit = query.limit || 10;
    const skip = (page - 1) * limit;

    const [characters, total] = await queryBuilder
      .skip(skip)
      .take(limit)
      .getManyAndCount();

    const items = characters.map(
      (character) => new CharacterResponseDto(character),
    );

    return new PaginatedResponse(items, total, page, limit);
  }
}
