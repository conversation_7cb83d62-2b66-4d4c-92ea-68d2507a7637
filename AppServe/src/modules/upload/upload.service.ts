import {
  Injectable,
  Logger,
  BadRequestException,
  InternalServerErrorException,
} from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import * as OSS from "ali-oss";
import { v4 as uuidv4 } from "uuid";
// import { OSSClient } from "../../common/types";

export interface UploadResult {
  url: string;
  filename: string;
  size: number;
  mimeType: string;
}

export enum UploadType {
  AVATAR = "avatar",
  STORY = "story",
  CHARACTER = "character",
  STORY_CONTENT = "storyContent",
  TEMP = "temp",
}

@Injectable()
export class UploadService {
  private readonly logger = new Logger(UploadService.name);
  private readonly ossClient: OSS;
  private readonly bucket: string;
  private readonly domain: string;
  private readonly baseDir: string;
  private readonly maxFileSize: number;
  private readonly allowedMimeTypes: string[];
  private readonly folders: Record<string, string>;

  constructor(private readonly configService: ConfigService) {
    const ossConfig = this.configService.get("oss");

    if (!ossConfig.accessKeyId || !ossConfig.accessKeySecret) {
      throw new Error("OSS配置缺失: accessKeyId 和 accessKeySecret 是必需的");
    }

    this.bucket = ossConfig.bucket;
    this.domain = ossConfig.domain;
    this.baseDir = ossConfig.baseDir;
    this.maxFileSize = ossConfig.maxFileSize;
    this.allowedMimeTypes = ossConfig.allowedMimeTypes;
    this.folders = ossConfig.folders;

    // 初始化OSS客户端
    this.ossClient = new OSS({
      region: ossConfig.region,
      accessKeyId: ossConfig.accessKeyId,
      accessKeySecret: ossConfig.accessKeySecret,
      bucket: this.bucket,
      endpoint: ossConfig.endpoint,
    });

    this.logger.log(
      `OSS客户端初始化成功 - Region: ${ossConfig.region}, Bucket: ${this.bucket}`,
    );
  }

  /**
   * 上传文件到OSS
   */
  async uploadFile(
    buffer: Buffer,
    originalName: string,
    mimeType: string,
    type: UploadType,
    userId?: string,
  ): Promise<UploadResult> {
    try {
      // 验证文件
      this.validateFile(buffer, mimeType);

      // 生成文件名
      const filename = this.generateFilename(originalName, type, userId);

      // 上传到OSS（私有存储，通过签名URL访问）
      await this.ossClient.put(filename, buffer, {
        headers: {
          "Content-Type": mimeType,
          "Cache-Control": "public, max-age=31536000", // 1年缓存
        },
      });

      this.logger.log(
        `文件上传成功: ${filename}, 大小: ${buffer.length} bytes`,
      );

      return {
        url: await this.getAccessibleUrl(filename),
        filename,
        size: buffer.length,
        mimeType,
      };
    } catch (error) {
      // 如果是BadRequestException，直接重新抛出
      if (error instanceof BadRequestException) {
        throw error;
      }

      this.logger.error(
        `文件上传失败: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined,
      );
      throw new InternalServerErrorException("文件上传失败");
    }
  }

  /**
   * 上传多个文件
   */
  async uploadFiles(
    files: Array<{ buffer: Buffer; originalName: string; mimeType: string }>,
    type: UploadType,
    userId?: string,
  ): Promise<UploadResult[]> {
    const uploadPromises = files.map(async (file) => {
      try {
        return await this.uploadFile(
          file.buffer,
          file.originalName,
          file.mimeType,
          type,
          userId,
        );
      } catch (error) {
        // 对于部分失败，记录错误但继续处理其他文件
        this.logger.error(
          `批量上传中单个文件失败: ${file.originalName}`,
          error instanceof Error ? error.stack : undefined,
        );
        return null; // 返回null表示失败
      }
    });

    const results = await Promise.all(uploadPromises);
    // 过滤掉失败的结果
    return results.filter((result): result is UploadResult => result !== null);
  }

  /**
   * 删除文件
   */
  async deleteFile(filename: string): Promise<void> {
    try {
      await this.ossClient.delete(filename);
      this.logger.log(`文件删除成功: ${filename}`);
    } catch (error) {
      this.logger.error(
        `文件删除失败: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined,
      );
      throw new InternalServerErrorException("文件删除失败");
    }
  }

  /**
   * 批量删除文件
   */
  async deleteFiles(filenames: string[]): Promise<void> {
    try {
      if (filenames.length === 0) return;

      const result = await this.ossClient.deleteMulti(filenames);
      this.logger.log(
        `批量删除文件成功, 删除数量: ${result.deleted?.length || 0}`,
      );
    } catch (error) {
      this.logger.error(
        `批量删除文件失败: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined,
      );
      throw new InternalServerErrorException("批量删除文件失败");
    }
  }

  /**
   * 获取文件信息
   */
  async getFileInfo(filename: string) {
    try {
      const result = await this.ossClient.head(filename);
      return {
        exists: true,
        size: parseInt(
          (result.res.headers as Record<string, string>)["content-length"],
        ),
        mimeType: (result.res.headers as Record<string, string>)[
          "content-type"
        ],
        lastModified: new Date(
          (result.res.headers as Record<string, string>)["last-modified"],
        ),
      };
    } catch (error) {
      if (
        error instanceof Error &&
        (error as Error & { code: string }).code === "NoSuchKey"
      ) {
        return { exists: false };
      }
      throw new InternalServerErrorException("获取文件信息失败");
    }
  }

  /**
   * 生成预签名URL（用于前端直传）
   */
  async generatePresignedUrl(
    filename: string,
    expiresIn: number = 3600, // 1小时
  ): Promise<string> {
    try {
      const url = this.ossClient.signatureUrl(filename, {
        expires: expiresIn,
        method: "PUT",
      });
      return url;
    } catch (error) {
      this.logger.error(
        `生成预签名URL失败: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined,
      );
      throw new InternalServerErrorException("生成预签名URL失败");
    }
  }

  /**
   * 验证文件
   */
  private validateFile(buffer: Buffer, mimeType: string): void {
    // 检查文件大小
    if (buffer.length > this.maxFileSize) {
      throw new BadRequestException(
        `文件大小超过限制 (${Math.round(this.maxFileSize / 1024 / 1024)}MB)`,
      );
    }

    // 检查文件类型
    if (!this.allowedMimeTypes.includes(mimeType)) {
      throw new BadRequestException(
        `不支持的文件类型: ${mimeType}。支持的类型: ${this.allowedMimeTypes.join(", ")}`,
      );
    }
  }

  /**
   * 生成文件名
   */
  private generateFilename(
    originalName: string,
    type: UploadType,
    userId?: string,
  ): string {
    const fileExt = this.getFileExtension(originalName);
    const timestamp = Date.now();
    const uuid = uuidv4();

    let folder = this.folders[type] || "";

    // 如果有用户ID，在文件夹中包含用户ID子目录
    if (userId) {
      folder = `${folder}${userId}/`;
    } else {
      folder = `${folder}/`;
    }

    const filename = `${timestamp}-${uuid}${fileExt}`;
    return `${this.baseDir}/${folder}${filename}`;
  }

  /**
   * 获取文件扩展名
   */
  private getFileExtension(filename: string): string {
    const lastDot = filename.lastIndexOf(".");
    return lastDot !== -1 ? filename.substring(lastDot) : "";
  }

  /**
   * 智能获取可访问的URL
   * 优先级：CDN > 签名URL > 代理URL
   */
  private async getAccessibleUrl(filename: string): Promise<string> {
    const ossConfig = this.configService.get("oss");

    // 调试日志
    this.logger.log(
      `OSS配置检查: cdnDomain=${ossConfig.cdnDomain}, domain=${ossConfig.domain}`,
    );

    // 方案1: 如果配置了CDN域名，使用CDN访问
    if (ossConfig.cdnDomain) {
      this.logger.log(`使用CDN访问: ${ossConfig.cdnDomain}/${filename}`);
      return `${ossConfig.cdnDomain}/${filename}`;
    }

    // 方案2: 使用签名URL（私有bucket安全访问）
    try {
      const signedUrl = this.ossClient.signatureUrl(filename, {
        expires: ossConfig.signedUrlExpires || 1800, // 30分钟
      });
      this.logger.log(`使用签名URL访问: ${filename}`);
      return signedUrl;
    } catch (error) {
      this.logger.warn(
        `签名URL生成失败: ${error instanceof Error ? error.message : String(error)}`,
      );
    }

    // 方案3: 使用应用代理访问（兜底方案）
    const proxyUrl = `http://localhost:3000/api/v1/images/test/${filename}`;
    this.logger.log(`使用代理访问: ${proxyUrl}`);
    return proxyUrl;
  }

  /**
   * 获取公共访问URL（微博模式）
   */
  private getPublicUrl(filename: string): string {
    const ossConfig = this.configService.get("oss");
    // 优先使用CDN域名，否则使用公开域名
    const domain = ossConfig.cdnDomain || ossConfig.publicDomain || this.domain;
    return `${domain}/${filename}`;
  }

  /**
   * 从URL提取文件名
   */
  extractFilenameFromUrl(url: string): string | null {
    try {
      const urlObj = new URL(url);
      const pathname = urlObj.pathname;

      // 移除开头的斜杠
      return pathname.startsWith("/") ? pathname.substring(1) : pathname;
    } catch {
      return null;
    }
  }
}
