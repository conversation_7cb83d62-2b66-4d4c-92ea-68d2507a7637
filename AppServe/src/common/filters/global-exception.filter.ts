import type { ExceptionFilter, ArgumentsHost } from "@nestjs/common";
import {
  Catch,
  HttpException,
  HttpStatus,
  Logger,
  Injectable,
} from "@nestjs/common";
import type { FastifyRequest, FastifyReply } from "fastify";
import { QueryFailedError, EntityNotFoundError } from "typeorm";
import { ValidationError } from "class-validator";
import type {
  ErrorDetails,
  ErrorContext as BaseErrorContext,
  RequestBody,
} from "../types";

export enum ErrorSeverity {
  LOW = "LOW",
  MEDIUM = "MEDIUM",
  HIGH = "HIGH",
  CRITICAL = "CRITICAL",
}

// 扩展基础错误上下文
export interface ErrorContext extends BaseErrorContext {
  requestId?: string;
  constraint?: string;
  table?: string;
  column?: string;
  violations?: Array<{
    property: string;
    value: unknown;
    constraints?: Record<string, string>;
  }>;
}

export interface StandardErrorResponse {
  code: number;
  message: string;
  error?: string;
  details?: ErrorDetails;
  timestamp: string;
  path: string;
  requestId?: string;
  // 开发环境专用
  stack?: string;
  context?: ErrorContext;
}

@Injectable()
@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(GlobalExceptionFilter.name);

  catch(exception: unknown, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const request = ctx.getRequest<FastifyRequest>();
    const response = ctx.getResponse<FastifyReply>();

    const errorContext = this.buildErrorContext(request);
    const { status, errorResponse, severity } = this.processException(
      exception,
      errorContext,
    );

    // 记录错误日志（分级记录）
    this.logError(exception, errorContext, severity, errorResponse);

    // 发送告警（高严重级别）
    if (severity >= ErrorSeverity.HIGH) {
      this.sendAlert(exception, errorContext, errorResponse).catch((err) => {
        this.logger.error("Failed to send alert:", err);
      });
    }

    response.status(status).send(errorResponse);
  }

  private processException(
    exception: unknown,
    context: ErrorContext,
  ): {
    status: number;
    errorResponse: StandardErrorResponse;
    severity: ErrorSeverity;
  } {
    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = "Internal server error";
    let error: string | undefined;
    let details: ErrorDetails | undefined;
    let severity = ErrorSeverity.MEDIUM;

    // HTTP异常
    if (exception instanceof HttpException) {
      status = exception.getStatus();
      const response = exception.getResponse();

      if (typeof response === "string") {
        message = response;
      } else if (typeof response === "object" && response !== null) {
        const httpResponse = response as {
          message?: string;
          error?: string;
          details?: ErrorDetails;
        };
        message = httpResponse.message || message;
        error = httpResponse.error;
        details = httpResponse.details;
      }

      severity = status >= 500 ? ErrorSeverity.HIGH : ErrorSeverity.LOW;
    }
    // 数据库异常
    else if (exception instanceof QueryFailedError) {
      status = HttpStatus.BAD_REQUEST;
      message = "Database operation failed";
      error = "DATABASE_ERROR";
      details = this.sanitizeDatabaseError(exception);
      severity = ErrorSeverity.MEDIUM;
    }
    // 实体未找到异常
    else if (exception instanceof EntityNotFoundError) {
      status = HttpStatus.NOT_FOUND;
      message = "Resource not found";
      error = "ENTITY_NOT_FOUND";
      severity = ErrorSeverity.LOW;
    }
    // 验证异常
    else if (exception instanceof ValidationError) {
      status = HttpStatus.BAD_REQUEST;
      message = "Validation failed";
      error = "VALIDATION_ERROR";
      details = this.formatValidationErrors([exception]);
      severity = ErrorSeverity.LOW;
    }
    // 系统异常
    else if (exception instanceof Error) {
      message =
        process.env.NODE_ENV === "production"
          ? "Internal server error"
          : exception.message;
      error = "SYSTEM_ERROR";
      severity = ErrorSeverity.HIGH;
    }

    const errorResponse: StandardErrorResponse = {
      code: status,
      message,
      error,
      details,
      timestamp: new Date().toISOString(),
      path: context.url || "unknown",
      requestId: context.requestId,
    };

    // 开发环境添加调试信息
    if (process.env.NODE_ENV === "development") {
      if (exception instanceof Error) {
        errorResponse.stack = exception.stack;
      }
      errorResponse.context = context;
    }

    return { status, errorResponse, severity };
  }

  private buildErrorContext(request: FastifyRequest): ErrorContext {
    return {
      requestId:
        (request.headers["x-request-id"] as string) || this.generateRequestId(),
      userId: (request as FastifyRequest & { user?: { id: string } }).user?.id,
      userAgent: request.headers["user-agent"],
      ip: request.ip,
      method: request.method,
      url: request.url,
      body: this.sanitizeBody(request.body),
      query: request.query as Record<string, unknown>,
      params: request.params as Record<string, unknown>,
    };
  }

  private sanitizeBody(body: unknown): RequestBody | undefined {
    if (!body) return undefined;

    // 确保body是对象类型
    if (typeof body !== "object" || body === null) {
      return { value: body } as RequestBody;
    }

    const sensitiveFields = [
      "password",
      "token",
      "secret",
      "key",
      "authorization",
    ];
    const sanitized = { ...(body as Record<string, unknown>) };

    for (const field of sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = "***REDACTED***";
      }
    }

    return sanitized as RequestBody;
  }

  private sanitizeDatabaseError(error: QueryFailedError): ErrorDetails {
    // 隐藏敏感的数据库信息
    const dbError = error as QueryFailedError & {
      constraint?: string;
      table?: string;
      column?: string;
    };

    return {
      constraint: dbError.constraint,
      table: dbError.table,
      column: dbError.column,
      // 不暴露完整的查询语句
    };
  }

  private formatValidationErrors(errors: ValidationError[]): ErrorDetails {
    return {
      violations: errors.map((error) => ({
        property: error.property,
        value: error.value,
        constraints: error.constraints,
      })),
    };
  }

  private logError(
    exception: unknown,
    context: ErrorContext,
    severity: ErrorSeverity,
    errorResponse: StandardErrorResponse,
  ): void {
    const logData = {
      exception: exception instanceof Error ? exception.name : "Unknown",
      message: errorResponse.message,
      severity,
      context,
      errorResponse,
      stack: exception instanceof Error ? exception.stack : undefined,
    };

    switch (severity) {
      case ErrorSeverity.CRITICAL:
      case ErrorSeverity.HIGH:
        this.logger.error(logData);
        break;
      case ErrorSeverity.MEDIUM:
        this.logger.warn(logData);
        break;
      case ErrorSeverity.LOW:
      default:
        this.logger.log(logData);
        break;
    }
  }

  private async sendAlert(
    exception: unknown,
    context: ErrorContext,
    errorResponse: StandardErrorResponse,
  ): Promise<void> {
    // 这里可以集成钉钉、企业微信、邮件等告警系统
    // 示例：发送到监控系统
    try {
      const alertData = {
        service: "story-app-backend",
        environment: process.env.NODE_ENV,
        severity: "high",
        title: `${errorResponse.error || "System Error"} - ${errorResponse.message}`,
        description:
          exception instanceof Error ? exception.message : "Unknown error",
        context,
        timestamp: errorResponse.timestamp,
      };

      // TODO: 集成实际的告警系统
      console.warn("🚨 High Severity Error Alert:", alertData);
    } catch (err) {
      this.logger.error("Failed to send alert:", err);
    }
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
