import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../features/auth/presentation/pages/login_page_001.dart';
import '../../features/splash/presentation/pages/splash_page.dart';
import '../../features/main/presentation/pages/main_tab_page.dart';

/// 路由名称
abstract class AppRoutes {
  static const String splash = 'splash';
  static const String debugSplash = 'debug-splash'; // 调试用启动页
  static const String login = 'login';
  static const String home = 'home';
  static const String profile = 'profile';
  static const String stories = 'stories';
  static const String storyDetail = 'story-detail';
  static const String createStory = 'create-story';
  static const String characters = 'characters';
  static const String characterDetail = 'character-detail';
  static const String lighting = 'lighting';
  static const String notifications = 'notifications';
  static const String settings = 'settings';
}

/// 路由路径
abstract class AppPaths {
  static const String splash = '/splash';
  static const String debugSplash = '/debug-splash'; // 调试用启动页
  static const String login = '/login';
  static const String home = '/';
  static const String profile = '/profile';
  static const String stories = '/stories';
  static const String storyDetail = '/stories/:id';
  static const String createStory = '/stories/create';
  static const String characters = '/characters';
  static const String characterDetail = '/characters/:id';
  static const String lighting = '/lighting';
  static const String notifications = '/notifications';
  static const String settings = '/settings';
}

/// 应用路由Provider
final appRouterProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    // 开发模式：直接进入首页，避免每次热重载回到启动页
    initialLocation: AppPaths.home, // 临时开发设置，正式版本改回 AppPaths.splash
    debugLogDiagnostics: true,
    
    // 路由守卫 - 静态开发模式，无权限限制
    redirect: (context, state) {
      // 静态开发模式：禁用所有权限检查，允许自由导航
      return null;
    },
    
    // 路由列表
    routes: [
      // 启动页
      GoRoute(
        name: AppRoutes.splash,
        path: AppPaths.splash,
        builder: (context, state) => const SplashPage(),
      ),
      
      // 登录页
      GoRoute(
        name: AppRoutes.login,
        path: AppPaths.login,
        builder: (context, state) => const LoginPage001(),
      ),
      
      // 首页
      GoRoute(
        name: AppRoutes.home,
        path: AppPaths.home,
        builder: (context, state) => const MainTabPage(),
        routes: [
          // 个人中心
          GoRoute(
            name: AppRoutes.profile,
            path: 'profile',
            builder: (context, state) => const Scaffold(
              body: Center(
                child: Text('个人中心 - 待实现'),
              ),
            ),
          ),
          
          // 故事列表
          GoRoute(
            name: AppRoutes.stories,
            path: 'stories',
            builder: (context, state) => const Scaffold(
              body: Center(
                child: Text('故事列表 - 待实现'),
              ),
            ),
            routes: [
              // 故事详情
              GoRoute(
                name: AppRoutes.storyDetail,
                path: ':id',
                builder: (context, state) {
                  final id = state.pathParameters['id']!;
                  return Scaffold(
                    body: Center(
                      child: Text('故事详情 $id - 待实现'),
                    ),
                  );
                },
              ),
              
              // 创建故事
              GoRoute(
                name: AppRoutes.createStory,
                path: 'create',
                builder: (context, state) => const Scaffold(
                  body: Center(
                    child: Text('创建故事 - 待实现'),
                  ),
                ),
              ),
            ],
          ),
          
          // 人物列表
          GoRoute(
            name: AppRoutes.characters,
            path: 'characters',
            builder: (context, state) => const Scaffold(
              body: Center(
                child: Text('人物列表 - 待实现'),
              ),
            ),
            routes: [
              // 人物详情
              GoRoute(
                name: AppRoutes.characterDetail,
                path: ':id',
                builder: (context, state) {
                  final id = state.pathParameters['id']!;
                  return Scaffold(
                    body: Center(
                      child: Text('人物详情 $id - 待实现'),
                    ),
                  );
                },
              ),
            ],
          ),
          
          // 点亮系统
          GoRoute(
            name: AppRoutes.lighting,
            path: 'lighting',
            builder: (context, state) => const Scaffold(
              body: Center(
                child: Text('点亮系统 - 待实现'),
              ),
            ),
          ),
          
          // 通知中心
          GoRoute(
            name: AppRoutes.notifications,
            path: 'notifications',
            builder: (context, state) => const Scaffold(
              body: Center(
                child: Text('通知中心 - 待实现'),
              ),
            ),
          ),
          
          // 设置
          GoRoute(
            name: AppRoutes.settings,
            path: 'settings',
            builder: (context, state) => const Scaffold(
              body: Center(
                child: Text('设置 - 待实现'),
              ),
            ),
          ),
        ],
      ),
    ],
    
    // 错误页面
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            const Text(
              '页面不存在',
              style: TextStyle(fontSize: 20),
            ),
            const SizedBox(height: 8),
            Text(
              state.error?.toString() ?? '',
              style: const TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go(AppPaths.home),
              child: const Text('返回首页'),
            ),
          ],
        ),
      ),
    ),
  );
});