import { Module } from "@nestjs/common";
import { JwtModule } from "@nestjs/jwt";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { TypeOrmModule } from "@nestjs/typeorm";

// 实体
import { User } from "../users/entities/user.entity";
import { RefreshToken } from "../users/entities/refresh-token.entity";

// 控制器
import { AuthController } from "./auth.controller";

// 服务
import { AuthService } from "./auth.service";
import { SmsService } from "./services/sms.service";
import { AliyunSmsService } from "./services/aliyun-sms.service";
import { OptimizedSessionManagerService } from "./services/session-manager-optimized.service";

// 守卫
import { JwtAuthGuard } from "./guards/jwt-auth.guard";

// 认证模块 - v1.0.0
@Module({
  imports: [
    TypeOrmModule.forFeature([User, RefreshToken]),

    // JWT配置
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>("JWT_SECRET", "your-default-secret"),
        signOptions: {
          expiresIn: configService.get<string>("JWT_EXPIRES_IN", "15m"),
        },
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [AuthController],
  providers: [
    AuthService,
    SmsService,
    AliyunSmsService,
    OptimizedSessionManagerService,
    JwtAuthGuard,
  ],
  exports: [
    AuthService,
    SmsService,
    AliyunSmsService,
    OptimizedSessionManagerService,
    JwtAuthGuard,
  ],
})
export class AuthModule {}
