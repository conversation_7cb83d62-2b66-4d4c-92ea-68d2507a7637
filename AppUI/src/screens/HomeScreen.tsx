/**
 * 有故事 APP - 首页屏幕
 *
 * 功能特性：
 * - 用户界面展示
 * - 基础布局设计
 * - 主题系统集成
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';

// 导入主题
import {THEME} from '../constants';

// 导入类型
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import type {RootStackParamList} from '../navigation/RootNavigator';

// 类型定义
type HomeScreenProps = NativeStackScreenProps<RootStackParamList, 'Main'>;

/**
 * 首页屏幕组件
 *
 * 功能：
 * - 展示应用主要内容
 * - 提供导航入口
 * - 用户交互界面
 */
const HomeScreen: React.FC<HomeScreenProps> = ({navigation: _navigation}) => {
  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.content}>
      {/* 欢迎区域 */}
      <View style={styles.welcomeSection}>
        <Text style={styles.title}>欢迎使用有故事</Text>
        <Text style={styles.subtitle}>企业级 React Native 应用框架</Text>
      </View>

      {/* 功能卡片区域 */}
      <View style={styles.cardContainer}>
        <TouchableOpacity
          style={styles.card}
          onPress={() => {
            // 导航到其他页面的示例
            console.log('功能卡片点击');
          }}
          activeOpacity={0.7}>
          <Text style={styles.cardTitle}>故事分享</Text>
          <Text style={styles.cardDescription}>
            分享你的精彩故事，连接世界各地的用户
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.card}
          onPress={() => {
            console.log('社区卡片点击');
          }}
          activeOpacity={0.7}>
          <Text style={styles.cardTitle}>社区互动</Text>
          <Text style={styles.cardDescription}>
            加入活跃的社区，与他人交流互动
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.card}
          onPress={() => {
            console.log('个人中心卡片点击');
          }}
          activeOpacity={0.7}>
          <Text style={styles.cardTitle}>个人中心</Text>
          <Text style={styles.cardDescription}>管理你的个人信息和偏好设置</Text>
        </TouchableOpacity>
      </View>

      {/* 底部信息 */}
      <View style={styles.footer}>
        <Text style={styles.footerText}>
          版本 1.0.0 • 基于 React Native 构建
        </Text>
      </View>
    </ScrollView>
  );
};

// ===== 样式定义 =====
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: THEME.colors.background,
  },
  content: {
    flexGrow: 1,
    padding: THEME.spacing.md,
  },
  welcomeSection: {
    alignItems: 'center',
    paddingVertical: THEME.spacing.xl,
  },
  title: {
    fontSize: THEME.fonts.sizes['3xl'],
    fontWeight: THEME.fonts.weights.bold,
    color: THEME.colors.text,
    marginBottom: THEME.spacing.sm,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: THEME.fonts.sizes.lg,
    fontWeight: THEME.fonts.weights.normal,
    color: THEME.colors.textSecondary,
    textAlign: 'center',
  },
  cardContainer: {
    flex: 1,
    gap: THEME.spacing.md,
  },
  card: {
    backgroundColor: THEME.colors.card,
    borderRadius: THEME.radius.lg,
    padding: THEME.spacing.lg,
    ...THEME.shadows.md,
    borderWidth: 1,
    borderColor: THEME.colors.border,
  },
  cardTitle: {
    fontSize: THEME.fonts.sizes.xl,
    fontWeight: THEME.fonts.weights.semibold,
    color: THEME.colors.text,
    marginBottom: THEME.spacing.sm,
  },
  cardDescription: {
    fontSize: THEME.fonts.sizes.md,
    fontWeight: THEME.fonts.weights.normal,
    color: THEME.colors.textSecondary,
    lineHeight: 22,
  },
  footer: {
    alignItems: 'center',
    paddingTop: THEME.spacing.xl,
    paddingBottom: THEME.spacing.lg,
  },
  footerText: {
    fontSize: THEME.fonts.sizes.sm,
    fontWeight: THEME.fonts.weights.normal,
    color: THEME.colors.textTertiary,
    textAlign: 'center',
  },
});

export default HomeScreen;
