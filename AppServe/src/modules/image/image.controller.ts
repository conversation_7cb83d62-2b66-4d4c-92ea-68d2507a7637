import {
  Controller,
  Get,
  UseGuards,
  Request,
  Query,
  BadRequestException,
} from "@nestjs/common";
import { AuthRequest } from "../../common/types";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from "@nestjs/swagger";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { ImageService } from "./image.service";

@ApiTags("图片访问")
@Controller("images")
export class ImageController {
  constructor(private readonly imageService: ImageService) {}

  @Get("batch-urls")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "批量获取图片临时访问URL" })
  @ApiQuery({ name: "paths", description: "图片路径列表（逗号分隔）" })
  @ApiQuery({
    name: "expires",
    type: Number,
    required: false,
    description: "URL有效期（秒）",
  })
  @ApiResponse({ status: 200, description: "返回批量临时访问URL" })
  async getBatchImageUrls(
    @Query("paths") paths: string,
    @Request() req: AuthRequest,
    @Query("expires") expires?: number,
  ) {
    const userId = req.user.id;
    const imagePaths = paths.split(",").filter((path) => path.trim());

    if (imagePaths.length === 0) {
      throw new BadRequestException("请提供有效的图片路径");
    }

    if (imagePaths.length > 50) {
      throw new BadRequestException("一次最多处理50张图片");
    }

    const results = [];
    const expiresIn = expires || 3600;

    for (const imagePath of imagePaths) {
      try {
        // 解析图片信息
        const imageInfo = this.imageService.parseImagePath(imagePath.trim());

        // 检查访问权限
        const hasAccess = await this.imageService.checkImageAccess({
          imagePath: imagePath.trim(),
          imageType: imageInfo.type,
          userId,
          storyId: imageInfo.resourceId,
          characterId: imageInfo.resourceId,
        });

        if (hasAccess) {
          const temporaryUrl = await this.imageService.generateTemporaryUrl(
            imagePath.trim(),
            expiresIn,
          );
          results.push({
            imagePath: imagePath.trim(),
            url: temporaryUrl,
            success: true,
          });
        } else {
          results.push({
            imagePath: imagePath.trim(),
            error: "无权访问此图片",
            success: false,
          });
        }
      } catch (error) {
        results.push({
          imagePath: imagePath.trim(),
          error: error instanceof Error ? error.message : String(error),
          success: false,
        });
      }
    }

    return {
      success: true,
      data: {
        results,
        total: imagePaths.length,
        successful: results.filter((r) => r.success).length,
        expires: expiresIn,
      },
      message: "批量URL生成完成",
    };
  }

  /**
   * 根据文件扩展名获取Content-Type
   */
  private getContentType(imagePath: string): string {
    const ext = imagePath.toLowerCase().split(".").pop();

    switch (ext) {
      case "jpg":
      case "jpeg":
        return "image/jpeg";
      case "png":
        return "image/png";
      case "gif":
        return "image/gif";
      case "webp":
        return "image/webp";
      case "bmp":
        return "image/bmp";
      case "svg":
        return "image/svg+xml";
      default:
        return "image/jpeg";
    }
  }
}
