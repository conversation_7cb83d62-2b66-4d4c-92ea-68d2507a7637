### AI润色的功能
1. 用户必须输入一定字数（50字）才能进行AI润色，不然浪费模型请求次数，前端进行判断。
2. 前端同时保存原文、AI润色后的文章，以供用户选择保存或者发布
3. 保存应该做在前端和后端
4. 这部分功能，前端难点是要特殊处理图片和人物，发起AI润色时，只针对文字部分，当模型返回文字后，要丝滑填入图片、人物卡片到原本的位置，
这里涉及到位置记录，要记录图片插入到段落和段落之间的位置（采用 Image1 image2 等占位符），把占位符使用API一起传入大模型，返回后，根据大模型中的图片占位符进行图片回显。
人物卡片，在前端是一组人物属性数据（头像、人物名、编号、是否点亮），应该把人物名+编号通过API传入大模型，当AI返回文字流后，前端匹配带有属性的人物，添加上相应的人物卡片（头像、点亮状态等）。
5. AI交互规则，要集合所有故事信息作为上下文，包括故事名、时间、地点、主题、正文（图片、属性人物）等。规则是：图片只能插入到段落和段落中间，处理文字中的图片时，应该使用传入的图片占位符（image1 image2等），人物则是以属性对象（人物名+编号）传入大模型，这两者等传回到前端后进行渲染。

### 主题封面颜色适配封面图片颜色
1. 主题封面使用用户随机上传的图片，所以不能使用统一的遮罩层，需要根据图片来计算遮罩层的颜色，这样体验才能达标。
2. 预设的十张主题，提前计算好每个主题封面的遮罩颜色，存储在后端，前端渲染时直接加载出来。
3. 新增的主题，根据用户上传的图片，在前端计算好遮罩颜色，展示给用户后，等用户完成主题的所有信息填写，标题、描述、图标后上传到后端。
4. 用户再次获取主题时，统一从后端获取到主题的所有信息， 标题、描述、图标、封面、遮罩颜色，前端直接渲染并缓存。