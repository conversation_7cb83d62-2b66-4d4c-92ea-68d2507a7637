-- 添加用户验证相关字段的迁移脚本
-- 执行时间: 2025-07-25

-- 添加手机验证字段
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS is_phone_verified BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS phone_verified_at TIMESTAMP NULL;

-- 添加邮箱验证字段  
ALTER TABLE users
ADD COLUMN IF NOT EXISTS is_email_verified BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS email_verified_at TIMESTAMP NULL;

-- 添加身份验证字段
ALTER TABLE users
ADD COLUMN IF NOT EXISTS is_identity_verified BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS identity_verified_at TIMESTAMP NULL,
ADD COLUMN IF NOT EXISTS identity_number VARCHAR(18) NULL,
ADD COLUMN IF NOT EXISTS real_name VARCHAR(50) NULL;

-- 添加安全等级字段
ALTER TABLE users
ADD COLUMN IF NOT EXISTS security_level INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_security_verification TIMESTAMP NULL;

-- 添加VIP用户字段
ALTER TABLE users
ADD COLUMN IF NOT EXISTS is_vip_user BOOLEAN DEFAULT FALSE;

-- 为验证字段添加注释
COMMENT ON COLUMN users.is_phone_verified IS '手机号是否已验证';
COMMENT ON COLUMN users.phone_verified_at IS '手机验证时间';
COMMENT ON COLUMN users.is_email_verified IS '邮箱是否已验证';
COMMENT ON COLUMN users.email_verified_at IS '邮箱验证时间';
COMMENT ON COLUMN users.is_identity_verified IS '身份是否已验证';
COMMENT ON COLUMN users.identity_verified_at IS '身份验证时间';
COMMENT ON COLUMN users.identity_number IS '身份证号';
COMMENT ON COLUMN users.real_name IS '真实姓名';
COMMENT ON COLUMN users.security_level IS '安全等级';
COMMENT ON COLUMN users.last_security_verification IS '最后安全验证时间';
COMMENT ON COLUMN users.is_vip_user IS '是否VIP用户';

-- 为身份证号添加索引（用于查重）
CREATE INDEX IF NOT EXISTS idx_users_identity_number ON users(identity_number) WHERE identity_number IS NOT NULL;

-- 为验证状态添加索引
CREATE INDEX IF NOT EXISTS idx_users_verification_status ON users(is_phone_verified, is_email_verified, is_identity_verified);

-- 为VIP用户添加索引
CREATE INDEX IF NOT EXISTS idx_users_vip_status ON users(is_vip_user) WHERE is_vip_user = TRUE;