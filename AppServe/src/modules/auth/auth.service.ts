import {
  Injectable,
  UnauthorizedException,
  BadRequestException,
  ConflictException,
  NotFoundException,
} from "@nestjs/common";
import { JwtService } from "@nestjs/jwt";
import { ConfigService } from "@nestjs/config";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { hash, compare } from "bcryptjs";
import { createHash } from "crypto";
import { User } from "../users/entities/user.entity";
import { RefreshToken } from "../users/entities/refresh-token.entity";
import { SmsService } from "./services/sms.service";
import { OptimizedSessionManagerService } from "./services/session-manager-optimized.service";
import type { DeviceInfo } from "../../common/types/request.types";

// 客户端信息类型定义
interface ClientInfo {
  ip?: string;
  connection?: { remoteAddress?: string };
  socket?: { remoteAddress?: string };
  userAgent?: string;
  [key: string]: unknown;
}

export interface LoginResult {
  user: User;
  accessToken: string;
  refreshToken: string;
  isFirstLogin?: boolean;
}

export interface RegisterResult {
  user: User;
  accessToken: string;
  refreshToken: string;
}

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(RefreshToken)
    private readonly refreshTokenRepository: Repository<RefreshToken>,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly smsService: SmsService,
    private readonly sessionManager: OptimizedSessionManagerService,
  ) {}

  /**
   * 发送手机验证码 (增强安全版本)
   */
  async sendPhoneVerificationCode(phone: string, _request?: unknown) {
    // 验证手机号格式
    if (!this.isValidPhone(phone)) {
      throw new BadRequestException("手机号格式不正确");
    }

    const result = await this.smsService.sendVerificationCode(phone, undefined);

    return {
      success: result.success,
      message: result.message,
      // 开发环境提示：验证码已记录在控制台日志中
      ...(process.env.NODE_ENV === "development" && {
        hint: "开发环境：请查看控制台日志获取验证码",
      }),
    };
  }

  /**
   * 手机号+验证码登录/注册 (增强安全版本)
   */
  async loginWithPhone(
    phone: string,
    code: string,
    clientInfo: unknown = {},
  ): Promise<LoginResult> {
    // 验证手机号格式
    if (!this.isValidPhone(phone)) {
      throw new BadRequestException("手机号格式不正确");
    }

    // 提取客户端信息
    const extractedClientInfo: ClientInfo = this.extractClientInfo(clientInfo);

    // 验证短信验证码 (传递request对象用于设备验证)
    const isValidCode = await this.smsService.verifyCode(
      phone,
      code,
      undefined,
    );
    if (!isValidCode) {
      throw new UnauthorizedException("验证码错误");
    }

    // 查找或创建用户
    let user = await this.userRepository.findOne({ where: { phone } });
    let isFirstLogin = false;

    if (!user) {
      // 首次登录，创建新用户
      user = this.userRepository.create({
        phone,
        username: `用户${phone.slice(-4)}`, // 默认用户名
        isActive: true,
      });
      user = await this.userRepository.save(user);
      isFirstLogin = true;
    } else {
      // 检查账户是否被锁定
      if (user.isLocked()) {
        throw new UnauthorizedException("账户已被锁定，请稍后再试");
      }
    }

    // 记录登录信息 (提取IP地址)
    const clientIP =
      extractedClientInfo.ip ||
      extractedClientInfo.connection?.remoteAddress ||
      extractedClientInfo.socket?.remoteAddress;
    await this.handleSuccessfulLogin(user, clientIP);

    const tokens = await this.generateTokens(user, {
      ...extractedClientInfo,
      ip: clientIP,
    });

    return {
      user,
      ...tokens,
      isFirstLogin,
    };
  }

  /**
   * 邮箱+密码注册
   */
  async registerWithEmail(
    email: string,
    password: string,
    username?: string,
  ): Promise<RegisterResult> {
    // 验证邮箱格式
    if (!this.isValidEmail(email)) {
      throw new BadRequestException("邮箱格式不正确");
    }

    // 验证密码强度
    if (!this.isValidPassword(password)) {
      throw new BadRequestException("密码强度不够，至少8位包含字母和数字");
    }

    // 检查邮箱是否已存在
    const existingUser = await this.userRepository.findOne({
      where: { email },
    });
    if (existingUser) {
      throw new ConflictException({
        message: "邮箱已被注册",
        error: "EMAIL_ALREADY_EXISTS",
      });
    }

    // 创建用户
    const passwordHash = await this.hashPassword(password);
    const user = this.userRepository.create({
      email,
      passwordHash,
      username: username || `用户${Date.now()}`,
      isActive: true,
    });

    const savedUser = await this.userRepository.save(user);
    const tokens = await this.generateTokens(savedUser);

    return {
      user: savedUser,
      ...tokens,
    };
  }

  /**
   * 邮箱+密码登录
   */
  async loginWithEmail(
    email: string,
    password: string,
    clientIP?: string,
  ): Promise<LoginResult> {
    // 验证邮箱格式
    if (!this.isValidEmail(email)) {
      throw new BadRequestException("邮箱格式不正确");
    }

    // 查找用户
    const user = await this.userRepository.findOne({ where: { email } });
    if (!user) {
      throw new UnauthorizedException("邮箱或密码错误");
    }

    // 检查账户是否被锁定
    if (user.isLocked()) {
      throw new UnauthorizedException("账户已被锁定，请稍后再试");
    }

    if (!user.passwordHash) {
      throw new UnauthorizedException(
        "该账户尚未设置密码，请使用手机验证码登录",
      );
    }

    // 验证密码
    const isValidPassword = await this.verifyPassword(
      password,
      user.passwordHash,
    );
    if (!isValidPassword) {
      // 增加失败登录次数
      await this.handleFailedLogin(user);
      throw new UnauthorizedException("邮箱或密码错误");
    }

    // 登录成功，重置失败次数并记录登录信息
    await this.handleSuccessfulLogin(user, clientIP);

    const tokens = await this.generateTokens(user, { ip: clientIP });

    return {
      user,
      ...tokens,
    };
  }

  /**
   * 为已有用户设置邮箱和密码
   */
  async setEmailAndPassword(
    userId: string,
    email: string,
    password: string,
  ): Promise<User> {
    // 验证邮箱格式
    if (!this.isValidEmail(email)) {
      throw new BadRequestException("邮箱格式不正确");
    }

    // 验证密码强度
    if (!this.isValidPassword(password)) {
      throw new BadRequestException("密码强度不够，至少8位包含字母和数字");
    }

    // 检查邮箱是否已被其他用户使用
    const existingUser = await this.userRepository.findOne({
      where: { email },
      select: ["id"],
    });
    if (existingUser && existingUser.id !== userId) {
      throw new ConflictException("邮箱已被其他用户使用");
    }

    // 更新用户信息
    const passwordHash = await this.hashPassword(password);
    await this.userRepository.update(userId, {
      email,
      passwordHash,
    });

    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException("用户不存在");
    }
    return user;
  }

  /**
   * 刷新Token（集成会话验证）
   */
  async refreshToken(
    refreshToken: string,
  ): Promise<{ accessToken: string; refreshToken: string }> {
    try {
      // 1. 验证JWT签名
      const payload = this.jwtService.verify(refreshToken, {
        secret: this.configService.get<string>("JWT_REFRESH_SECRET"),
      });

      // 2. 验证会话有效性
      const refreshTokenHash = this.hashToken(refreshToken);
      const session =
        await this.sessionManager.validateSession(refreshTokenHash);

      if (!session) {
        throw new UnauthorizedException("会话已失效，请重新登录");
      }

      const user = await this.userRepository.findOne({
        where: { id: payload.sub },
      });
      if (!user || !user.isActive) {
        throw new UnauthorizedException("用户不存在或已被禁用");
      }

      // 3. 注销旧会话，生成新令牌
      await this.sessionManager.invalidateSession(refreshTokenHash);
      return this.generateTokens(user, session.deviceInfo);
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new UnauthorizedException("无效的刷新Token");
    }
  }

  /**
   * 验证JWT Token
   */
  async validateToken(token: string): Promise<User> {
    try {
      const payload = this.jwtService.verify(token);
      const user = await this.userRepository.findOne({
        where: { id: payload.sub },
      });

      if (!user || !user.isActive) {
        throw new UnauthorizedException("用户不存在或已被禁用");
      }

      return user;
    } catch (error) {
      throw new UnauthorizedException("无效的Token");
    }
  }

  /**
   * 用户注销（注销所有会话）
   */
  async logout(userId: string): Promise<{ message: string }> {
    await this.sessionManager.invalidateAllUserSessions(userId);
    return { message: "注销成功" };
  }

  /**
   * 注销特定会话
   */
  async logoutSession(refreshToken: string): Promise<{ message: string }> {
    const refreshTokenHash = this.hashToken(refreshToken);
    await this.sessionManager.invalidateSession(refreshTokenHash);
    return { message: "会话注销成功" };
  }

  /**
   * 获取用户会话信息（用于管理和监控）
   */
  async getUserSessions(userId: string) {
    return this.sessionManager.getUserSessionStats(userId);
  }

  /**
   * 生成JWT令牌（集成企业级会话管理）
   */
  private async generateTokens(
    user: User,
    deviceInfo?: DeviceInfo,
  ): Promise<{ accessToken: string; refreshToken: string }> {
    // 1. 验证并发会话限制
    await this.sessionManager.validateConcurrentSessions(user.id, deviceInfo);

    const payload = {
      sub: user.id,
      phone: user.phone,
      email: user.email,
      username: user.username,
    };

    // 2. 生成访问令牌和刷新令牌
    const [accessToken, refreshToken] = await Promise.all([
      this.jwtService.signAsync(payload, {
        secret: this.configService.get<string>("JWT_SECRET"),
        expiresIn: this.configService.get<string>("JWT_EXPIRES_IN"),
      }),
      this.jwtService.signAsync(payload, {
        secret: this.configService.get<string>("JWT_REFRESH_SECRET"),
        expiresIn: this.configService.get<string>("JWT_REFRESH_EXPIRES_IN"),
      }),
    ]);

    // 3. 创建会话记录
    const refreshTokenHash = this.hashToken(refreshToken);
    await this.sessionManager.createSession(user, refreshTokenHash, deviceInfo);

    return { accessToken, refreshToken };
  }

  /**
   * 哈希令牌（用于安全存储）
   */
  private hashToken(token: string): string {
    return createHash("sha256").update(token).digest("hex");
  }

  private async hashPassword(password: string): Promise<string> {
    const saltRounds = parseInt(
      this.configService.get<string>("BCRYPT_ROUNDS", "12"),
    );
    return hash(password, saltRounds);
  }

  private async verifyPassword(
    password: string,
    hashValue: string,
  ): Promise<boolean> {
    return compare(password, hashValue);
  }

  private isValidPhone(phone: string): boolean {
    // 中国手机号正则：1开头，第二位3-9，总共11位
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  private isValidPassword(password: string): boolean {
    // 至少8位，包含字母和数字
    const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/;
    return passwordRegex.test(password);
  }

  /**
   * 处理登录失败
   */
  private async handleFailedLogin(user: User): Promise<void> {
    user.failedLoginAttempts = (user.failedLoginAttempts || 0) + 1;

    // 如果失败次数达到阈值，锁定账户
    if (user.shouldLockAccount()) {
      user.lockedUntil = new Date(Date.now() + 15 * 60 * 1000); // 锁定15分钟
    }

    await this.userRepository.save(user);
  }

  /**
   * 处理登录成功
   */
  private async handleSuccessfulLogin(
    user: User,
    clientIP?: string,
  ): Promise<void> {
    user.failedLoginAttempts = 0;
    user.lockedUntil = null;
    user.lastLoginAt = new Date();
    user.lastLoginIp = clientIP || null;

    await this.userRepository.save(user);
  }

  /**
   * 提取客户端信息
   */
  private extractClientInfo(clientInfo: unknown): ClientInfo {
    if (typeof clientInfo === "object" && clientInfo !== null) {
      return clientInfo as ClientInfo;
    }
    return {};
  }
}
