import 'package:dio/dio.dart';
import '../../utils/app_logger.dart';

/// 错误处理拦截器
class ErrorInterceptor extends Interceptor {
  @override
  void onError(
    DioException err,
    ErrorInterceptorHandler handler,
  ) {
    // 记录错误日志
    AppLogger.e('网络请求错误', error: err, stackTrace: err.stackTrace);

    // 打印详细错误信息（仅开发环境）
    AppLogger.d('''
请求路径: ${err.requestOptions.path}
请求方法: ${err.requestOptions.method}
请求参数: ${err.requestOptions.queryParameters}
请求数据: ${err.requestOptions.data}
错误类型: ${err.type}
错误信息: ${err.message}
响应状态: ${err.response?.statusCode}
响应数据: ${err.response?.data}
''');

    handler.next(err);
  }

  @override
  void onResponse(
    Response response,
    ResponseInterceptorHandler handler,
  ) {
    // 处理业务错误码
    final data = response.data;
    
    if (data is Map<String, dynamic>) {
      // 检查业务状态码
      final success = data['success'] ?? true;
      final code = data['code'];
      
      // 如果业务状态表示失败，转换为DioException
      if (!success && code != null && code != 0) {
        final error = DioException(
          requestOptions: response.requestOptions,
          response: response,
          type: DioExceptionType.badResponse,
          error: data['message'] ?? '请求失败',
        );
        
        return handler.reject(error);
      }
    }

    handler.next(response);
  }
}