# YGS三大核心系统产品规范 V1.0.0

## 📋 文档信息

**文档版本**: V1.0.0  
**创建时间**: 2025-07-24  
**维护团队**: YGS Product Team  
**文档性质**: 核心系统产品规范  
**适用范围**: 故事系统、人物系统、点亮系统的完整产品定义

---

## 🎯 产品核心理念

YGS（有故事）是一个基于真实社交关系的故事分享平台。通过独创的"人物点亮系统"，将虚拟的故事内容与真实的人物身份相结合，建立了一种全新的社交验证机制，创造了可信的内容生态。

### 三大系统的关系架构

```
┌─────────────────────────────────────────────────────────────┐
│                     YGS 三大核心系统架构                       │
├─────────────────────────────────────────────────────────────┤
│                                                              │
│    ┌──────────────┐      ┌──────────────┐                  │
│    │  故事系统     │ ←──→ │  人物系统     │                  │
│    │              │      │              │                  │
│    │ • 内容创作   │      │ • 人物档案   │                  │
│    │ • 权限控制   │      │ • 关系设定   │                  │
│    │ • 内容审核   │      │ • 状态管理   │                  │
│    └──────────────┘      └──────────────┘                  │
│            ↑                    ↓                           │
│            └────────┬───────────┘                           │
│                     ↓                                        │
│            ┌──────────────┐                                 │
│            │  点亮系统     │                                 │
│            │              │                                 │
│            │ • 身份验证   │                                 │
│            │ • 关系建立   │                                 │
│            │ • 权益管理   │                                 │
│            └──────────────┘                                 │
│                                                              │
└─────────────────────────────────────────────────────────────┘
```

### 核心业务逻辑

1. **故事系统**创作内容，**人物系统**管理角色，两者通过故事-人物关联建立联系
2. **点亮系统**验证真实身份，连接故事中的虚拟人物与现实中的真实用户
3. 三大系统相互依存，共同构建了YGS独特的社交内容生态

---

## 📖 第一部分：故事系统详解

### 1.1 系统定位

故事系统是YGS的内容基础，负责用户创作、发布、管理故事内容，是整个平台的内容源头。

### 1.2 核心功能

#### 1.2.1 故事创作与管理

**功能描述**：
- 支持图文混排的故事创作（最多9张图片）
- 草稿自动保存，防止内容丢失
- 故事状态管理：草稿→发布→归档/删除

**业务规则**：
- 标题长度：1-100字符
- 内容长度：10-10000字符
- 图片限制：单张最大5MB，支持JPG/PNG/WebP格式
- 每用户每天最多发布10个故事

#### 1.2.2 六层权限控制体系

**权限层级**（从高到低）：
1. **CHARACTERS_ONLY（仅人物可见）**：只有故事中被点亮的人物用户可见
2. **PRIVATE（私密）**：仅作者自己可见
3. **FRIENDS（好友可见）**：互相关注的好友可见
4. **FOLLOWERS（关注者可见）**：关注作者的用户可见
5. **GROUP_VISIBLE（分组可见）**：指定分组的用户可见
6. **PUBLIC（公开）**：所有用户可见（默认）

**权限验证流程**：
```typescript
// 权限验证优先级
if (user === author) return ALLOWED; // 作者永远可见
if (permission === CHARACTERS_ONLY) return checkLighting();
if (permission === PRIVATE) return DENIED;
if (permission === FRIENDS) return checkFriendship();
if (permission === FOLLOWERS) return checkFollowing();
if (permission === GROUP_VISIBLE) return checkGroupMembership();
return permission === PUBLIC;
```

#### 1.2.3 内容安全机制

**双重审核流程**：
1. **AI预检**：实时内容安全检测，分为低/中/高风险
2. **人工复审**：中风险内容进入人工审核队列

**违规处理**：
- 低风险：自动通过
- 中风险：人工审核决定
- 高风险：自动拒绝发布

### 1.3 功能清单

| 功能名称 | API接口 | 功能描述 | 权限要求 |
|---------|---------|----------|----------|
| 创建故事 | POST /stories | 创建新故事（草稿） | 登录用户 |
| 发布故事 | PUT /stories/{id}/publish | 发布草稿故事 | 故事作者 |
| 更新故事 | PUT /stories/{id} | 编辑故事内容 | 故事作者 |
| 删除故事 | DELETE /stories/{id} | 软删除故事 | 故事作者 |
| 故事详情 | GET /stories/{id} | 获取故事详情 | 权限验证 |
| 故事列表 | GET /stories | 获取故事流 | 登录用户 |
| 我的故事 | GET /users/me/stories | 获取个人故事 | 故事作者 |
| 归档故事 | PUT /stories/{id}/archive | 归档已发布故事 | 故事作者 |
| 上传图片 | POST /stories/{id}/images | 上传故事图片 | 故事作者 |
| 权限设置 | PUT /stories/{id}/permissions | 更新访问权限 | 故事作者 |

### 1.4 业务流程

#### 1.4.1 故事生命周期

```
创建草稿 → 编辑内容 → AI安全检测 → 发布/保存 → 权限控制 → 用户访问
    ↓                                           ↓
  自动保存                                    归档/删除
```

#### 1.4.2 权限验证流程

```
用户访问 → 身份验证 → 权限检查 → 内容展示/拒绝访问
           ↓
       记录访问日志
```

---

## 🎭 第二部分：人物系统详解

### 2.1 系统定位

人物系统是连接虚拟故事与真实身份的桥梁，管理故事中的人物角色，为点亮系统提供基础。

### 2.2 核心概念

#### 2.2.1 人物集与点亮集

**人物集**：作者创建的所有人物档案
- 展示内容：人物头像、姓名、关系、点亮状态
- 权限控制：通过个人主页展示设置控制是否公开

**点亮集**：用户成功点亮的所有人物
- 展示内容：人物信息、故事作者、出现故事数
- 特殊标识：已点亮人物在界面上高亮显示

### 2.3 核心功能

#### 2.3.1 人物创建与管理

**功能描述**：
- 创建故事人物档案，设定关系属性
- 支持预设头像库和自定义头像上传
- 人物可在多个故事中重复使用

**业务规则**：
- 人物姓名：1-20字符，同一作者下唯一
- 关系类型：预设关系+自定义关系
- 编辑限制：被点亮后关系锁定，仅可修改描述
- 数量限制：每用户最多创建100个人物

#### 2.3.2 关系锁定机制

**锁定规则**：
- 人物被点亮前：所有属性可自由编辑
- 人物被点亮后：关系字段锁定，保护已建立的社交关系
- 锁定范围：姓名、关系、性别等核心属性
- 可编辑内容：描述信息、标签等非核心属性

**设计理由**：
保护已验证的真实关系不被随意更改，维护平台信任基础。

#### 2.3.3 多故事管理

**人物复用**：
- 同一人物可出现在作者的多个故事中
- 每个故事中的人物保持身份一致性
- 点亮状态跨故事共享（任一故事点亮即全局点亮）

**状态聚合**：
```typescript
// 人物点亮状态计算
isLighted = lightings.filter(l => l.status === 'active').length > 0;
lightingCount = lightings.filter(l => l.status === 'active').length;
```

### 2.4 功能清单

| 功能名称 | API接口 | 功能描述 | 业务规则 |
|---------|---------|----------|----------|
| 创建人物 | POST /characters | 创建人物档案 | 姓名唯一性检查 |
| 更新人物 | PUT /characters/{id} | 编辑人物信息 | 点亮后部分锁定 |
| 删除人物 | DELETE /characters/{id} | 软删除人物 | 未关联故事可删 |
| 人物详情 | GET /characters/{id} | 获取人物详情 | 包含点亮信息 |
| 人物列表 | GET /characters | 获取人物列表 | 支持筛选排序 |
| 人物搜索 | GET /characters/search | 搜索人物 | 多维度搜索 |
| 验证姓名 | POST /characters/validate-name | 姓名唯一性验证 | 实时验证 |
| 上传头像 | POST /characters/upload-avatar | 自定义头像 | 最大2MB |
| 人物统计 | GET /characters/statistics | 人物数据统计 | 点亮率分析 |
| 添加到故事 | POST /stories/{id}/characters | 关联人物到故事 | 作者权限 |

### 2.5 业务流程

#### 2.5.1 人物创建流程

```
创建人物 → 设定属性 → 保存档案 → 添加到故事 → 等待点亮
    ↓                               ↓
姓名验证                        可在多个故事使用
```

#### 2.5.2 编辑限制流程

```
检查点亮状态 → 未点亮：全部可编辑
            ↓
            → 已点亮：仅描述可编辑，关系锁定
```

---

## ✨ 第三部分：点亮系统详解

### 3.1 系统定位

点亮系统是YGS的核心创新，通过真实身份验证建立故事人物与现实用户的连接，创造可信的社交关系。

### 3.2 核心机制

#### 3.2.1 双重排他性约束

**约束一：人物排他性**
- 每个人物只能被一个用户点亮
- 一旦点亮关系建立，其他用户无法再申请

**约束二：作者排他性**
- 每个用户对每个故事作者只能点亮一个人物
- 防止一个用户占用同一作者的多个人物角色

**设计理由**：
确保身份唯一性，避免身份混乱，维护真实性。

#### 3.2.2 故事级点亮申请

**申请机制**：
- 点亮申请必须指定具体故事ID
- 同一人物在不同故事中需要分别申请
- 申请时展示故事上下文，帮助作者判断

**灵活性设计**：
- 用户可选择性参与不同故事
- 避免一次点亮永久绑定所有故事
- 保护用户隐私选择权

#### 3.2.3 手机号验证机制

**当前实现**：
- 自动获取用户注册手机号
- 用户可选择是否在申请中包含手机号
- 手机号脱敏显示（138****8000）

**验证流程**：
```typescript
// 手机号验证和脱敏
if (includePhone && user.phone) {
  phoneVerification = maskPhone(user.phone); // 138****8000
  // 存储脱敏后的手机号，不存储完整号码
}
```

### 3.3 核心功能

#### 3.3.1 点亮申请

**功能描述**：
- 人物用户发现自己在故事中，申请点亮验证身份
- 申请包含理由说明和可选的手机号验证
- 申请有效期7天，过期自动作废

**申请条件**：
1. 人物必须存在于指定故事中
2. 申请者不能是故事作者
3. 申请者不能是人物创建者
4. 同一人物每个用户只能申请一次
5. 需要符合双重排他性约束

#### 3.3.2 确认机制

**确认流程**：
- 故事作者收到申请通知
- 查看申请者信息和申请理由
- 确认或拒绝申请（7天内）

**确认后果**：
- 建立点亮关系，人物状态更新
- 申请者获得点亮权益
- 人物关系锁定，不可再编辑

#### 3.3.3 情感保护机制

**正面通知策略**：
- 确认通知：发送成功消息给申请者
- 拒绝处理：不发送任何通知（冷处理）
- 取消通知：双方都不收到负面通知

**设计理由**：
保护用户情感体验，避免负面情绪传递，营造积极社区氛围。

#### 3.3.4 已点亮人物的新故事通知

**场景描述**：
当已被点亮的人物出现在同一作者的新故事中时，系统自动识别并通知。

**通知机制**：
1. 新故事发布时，系统检测已点亮关系
2. 向相关用户发送确认通知
3. 通知格式："您点亮过的人物X，出现在作者A的新故事B中，是否确认点亮？"
4. 用户确认后跳过作者审批，直接建立新的点亮关系

**业务价值**：
- 简化重复验证流程
- 保持用户选择权
- 提升用户体验

### 3.4 点亮权益

#### 3.4.1 身份标识权益

- **点亮集展示**：在个人主页展示已点亮人物
- **特殊标识**：UI界面上的独特视觉标识
- **身份认证**：平台认证的真实身份背书

#### 3.4.2 内容访问权益

- **最高权限访问**：永久访问"仅人物可见"的故事
- **故事引用权**：可以引用被点亮的故事到个人引用集
- **优先展示**：在相关推荐中优先展示

#### 3.4.3 社交互动权益

- **评论优先**：评论显示特殊标识
- **互动加权**：点赞等互动行为权重更高
- **推荐优势**：在社交推荐中优先级更高

### 3.5 功能清单

| 功能名称 | API接口 | 功能描述 | 业务规则 |
|---------|---------|----------|----------|
| 提交申请 | POST /stories/{storyId}/characters/{characterId}/light-request | 申请点亮人物 | 多重验证 |
| 申请列表 | GET /light-requests | 查看待处理申请 | 故事作者 |
| 我的申请 | GET /my-requests | 查看个人申请记录 | 申请者 |
| 确认申请 | PUT /light-requests/{id}/confirm | 确认点亮申请 | 故事作者 |
| 拒绝申请 | PUT /light-requests/{id}/reject | 拒绝点亮申请 | 无通知 |
| 点亮状态 | GET /characters/{id}/lighting-status | 查询点亮状态 | 实时聚合 |
| 取消点亮 | DELETE /characters/{id}/lighting/{lightingId} | 取消点亮关系 | 双方可操作 |
| 点亮统计 | GET /users/{id}/lighting-stats | 用户点亮数据 | 统计分析 |
| 检查资格 | GET /characters/{id}/can-apply | 检查申请资格 | 预检查 |
| 点亮集 | GET /users/{id}/lighted-characters | 获取点亮集 | 公开展示 |

### 3.6 业务流程

#### 3.6.1 完整点亮流程

```
发现故事中的自己 → 提交点亮申请 → 作者收到通知 → 审核确认/拒绝
                                              ↓
                                          建立点亮关系
                                              ↓
                                          获得点亮权益
```

#### 3.6.2 状态管理流程

```
人物创建（未点亮）→ 首次点亮（状态更新）→ 多故事点亮（计数累加）
                                    ↓
                              取消所有点亮（恢复未点亮）
```

---

## 🔄 第四部分：三大系统的协同工作

### 4.1 系统间的数据流转

#### 4.1.1 创作阶段
```
故事系统创建内容 → 人物系统提供角色 → 建立故事-人物关联
```

#### 4.1.2 点亮阶段
```
用户发现故事 → 识别人物身份 → 点亮系统验证 → 更新人物状态
```

#### 4.1.3 权益阶段
```
点亮关系建立 → 故事系统识别权限 → 人物系统更新展示 → 用户获得权益
```

### 4.2 关键业务场景

#### 4.2.1 场景一：新用户首次点亮

1. 用户A创作故事，添加人物"小明"
2. 真实的小明注册平台，发现故事
3. 小明申请点亮，提供手机号验证
4. 用户A确认是真实的小明
5. 点亮关系建立，小明获得特殊身份

#### 4.2.2 场景二：多故事人物管理

1. 用户A的多个故事都有"小明"
2. 小明在故事1中已被点亮
3. 用户A发布新故事2，包含小明
4. 系统通知小明确认新故事
5. 小明确认后自动点亮，无需A再次审核

#### 4.2.3 场景三：权限控制展示

1. 用户A设置故事为"仅人物可见"
2. 只有被点亮的人物用户可以访问
3. 其他用户无法看到故事内容
4. 点亮用户拥有永久访问权

### 4.3 数据一致性保证

#### 4.3.1 事务一致性
- 点亮确认使用数据库事务
- 状态更新保证原子性
- 失败自动回滚

#### 4.3.2 缓存一致性
- 使用Redis缓存热点数据
- 更新时同步刷新缓存
- 设置合理的过期时间

#### 4.3.3 分布式锁
- 防止并发点亮确认
- 保证状态更新串行化
- 避免数据竞争

---

## 📊 第五部分：产品数据指标

### 5.1 核心业务指标

#### 5.1.1 故事系统指标
- 日均创作故事数
- 故事平均阅读量
- 权限使用分布
- 内容审核通过率

#### 5.1.2 人物系统指标
- 人均创建人物数
- 人物点亮率
- 关系类型分布
- 人物复用率

#### 5.1.3 点亮系统指标
- 点亮申请成功率
- 申请响应时间
- 月活跃点亮用户数
- 人均点亮数

### 5.2 用户体验指标

- 点亮流程完成率
- 用户操作错误率
- 功能使用深度
- 用户满意度评分

### 5.3 技术性能指标

- API响应时间（<200ms）
- 系统可用性（>99.9%）
- 并发处理能力（>1000 TPS）
- 数据一致性（>99.9%）

---

## 🚀 第六部分：未来演进方向

### 6.1 功能扩展

1. **AI智能推荐**：基于点亮关系的内容推荐
2. **视频故事**：支持短视频形式的故事
3. **群体故事**：多人协作创作故事
4. **故事接龙**：基于已有故事的续写

### 6.2 商业化探索

1. **认证服务**：官方认证的人物身份
2. **推广服务**：故事和人物的推广展示
3. **数据服务**：故事数据分析服务
4. **企业服务**：企业故事和员工认证

### 6.3 技术升级

1. **区块链存证**：点亮关系的不可篡改存证
2. **联邦学习**：保护隐私的推荐算法
3. **边缘计算**：就近内容分发和处理
4. **Web3集成**：去中心化的身份认证

---

## 📋 总结

YGS通过故事系统、人物系统、点亮系统三大核心系统的有机结合，创造了一个基于真实身份验证的可信内容社区。这种创新的产品设计不仅解决了网络内容真实性的痛点，还建立了独特的社交连接机制，具有巨大的商业价值和社会意义。

三大系统相辅相成：
- **故事系统**提供内容基础
- **人物系统**连接虚拟与现实
- **点亮系统**验证真实身份

通过精心设计的产品机制和严谨的技术实现，YGS正在打造一个"有故事、有温度、有信任"的社交内容平台。

---

**文档维护**: YGS产品团队  
**最后更新**: 2025-07-24  
**版本号**: V1.0.0  
**文档状态**: 正式发布