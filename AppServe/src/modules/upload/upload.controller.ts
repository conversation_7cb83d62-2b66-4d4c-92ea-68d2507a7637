import {
  Controller,
  Post,
  UseGuards,
  Request,
  BadRequestException,
} from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiConsumes,
  ApiBody,
} from "@nestjs/swagger";

import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { UploadService } from "./upload.service";
import type { AuthRequest } from "../../common/types/request.types";
import type { UploadResult } from "./upload.service";
import { UploadType } from "./upload.service";
import { ApiResponse as ApiResponseDto } from "../../common/dto/api-response.dto";

@ApiTags("文件上传")
@Controller("upload")
@ApiBearerAuth()
export class UploadController {
  constructor(private readonly uploadService: UploadService) {}

  @Post("single")
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: "上传单个文件",
    description: `
      上传单个文件到阿里云OSS存储
      
      支持的文件类型：
      - 图片: image/jpeg, image/png, image/gif, image/webp
      - 文档: application/pdf, text/plain
      - 音频: audio/mpeg, audio/wav, audio/mp3
      - 视频: video/mp4, video/avi, video/mov
      
      文件限制：
      - 最大文件大小: 10MB
      - 文件名长度: 最多100个字符
      - 上传后生成唯一文件名防止冲突
      
      上传类型说明：
      - avatar: 用户头像 (存储于 avatar/ 目录)
      - story: 故事封面 (存储于 story/ 目录)
      - character: 人物头像 (存储于 character/ 目录)
      - storyContent: 故事内容图片 (存储于 story-content/ 目录)
      - temp: 临时文件 (存储于 temp/ 目录，自动清理)
      
      返回的URL是带签名的私有访问链接，有效期24小时
    `,
  })
  @ApiConsumes("multipart/form-data")
  @ApiBody({
    description: "文件上传请求体 - 使用 multipart/form-data 格式",
    schema: {
      type: "object",
      properties: {
        file: {
          type: "string",
          format: "binary",
          description: "要上传的文件",
        },
      },
      required: ["file"],
    },
  })
  @ApiResponse({
    status: 200,
    description: "文件上传成功",
    schema: {
      type: "object",
      properties: {
        success: { type: "boolean", example: true },
        message: { type: "string", example: "文件上传成功" },
        data: {
          type: "object",
          properties: {
            url: {
              type: "string",
              example:
                "https://ygs-storage.oss-cn-hangzhou.aliyuncs.com/temp/2025/07/12/uuid-filename.jpg?Expires=1720800000&OSSAccessKeyId=xxx&Signature=xxx",
              description: "文件访问链接（带签名，24小时有效）",
            },
            filename: {
              type: "string",
              example: "temp/2025/07/12/uuid-filename.jpg",
              description: "OSS中的文件路径",
            },
            size: {
              type: "number",
              example: 1024000,
              description: "文件大小（字节）",
            },
            mimeType: {
              type: "string",
              example: "image/jpeg",
              description: "文件MIME类型",
            },
          },
        },
        statusCode: { type: "number", example: 200 },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: "文件上传失败",
    schema: {
      type: "object",
      properties: {
        success: { type: "boolean", example: false },
        message: { type: "string", example: "文件上传失败: 文件类型不支持" },
        error: { type: "string", example: "INVALID_FILE_TYPE" },
        statusCode: { type: "number", example: 400 },
      },
    },
  })
  @ApiResponse({
    status: 413,
    description: "文件过大",
    schema: {
      type: "object",
      properties: {
        success: { type: "boolean", example: false },
        message: { type: "string", example: "文件大小超过限制(10MB)" },
        error: { type: "string", example: "FILE_TOO_LARGE" },
        statusCode: { type: "number", example: 413 },
      },
    },
  })
  async uploadSingle(
    @Request()
    req: AuthRequest & {
      file: () => Promise<{
        buffer: Buffer;
        mimetype: string;
        originalname: string;
        size: number;
        filename?: string;
        encoding?: string;
        toBuffer?: () => Promise<Buffer>;
      }>;
    },
  ): Promise<ApiResponseDto<UploadResult>> {
    try {
      console.log("开始处理文件上传...");

      // 使用Fastify原生multipart处理
      const data = await req.file();

      if (!data) {
        console.log("没有找到文件数据");
        throw new BadRequestException("请选择要上传的文件");
      }

      console.log("文件信息:", {
        filename: data.filename || data.originalname,
        mimetype: data.mimetype,
        encoding: data.encoding || "unknown",
      });

      // 读取文件数据
      const buffer = data.toBuffer ? await data.toBuffer() : data.buffer;
      console.log("文件缓冲区大小:", buffer.length);

      const userId = req.user?.id || "anonymous";
      console.log("用户ID:", userId);

      const result = await this.uploadService.uploadFile(
        buffer,
        data.filename || data.originalname,
        data.mimetype,
        UploadType.TEMP, // Assuming default type is TEMP for single upload
        userId,
      );

      return new ApiResponseDto(true, "文件上传成功", result, 200);
    } catch (error) {
      console.error("上传失败错误:", error);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        "文件上传失败: " +
          (error instanceof Error ? error.message : String(error)),
      );
    }
  }
}
