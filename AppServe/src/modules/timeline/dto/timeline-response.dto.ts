import type {
  EventType,
  EventStatus,
  EventVisibility,
  EventImportance,
} from "../entities/timeline-event.entity";

/**
 * 时间线事件响应DTO
 */
export class TimelineEventResponseDto {
  id: string;
  userId: string;
  userName: string;
  title: string;
  description?: string;
  eventType: EventType;
  status: EventStatus;
  visibility: EventVisibility;
  importance: EventImportance;
  eventDate: Date;
  endDate?: Date;
  location?: string;
  relatedPeople?: string[];
  tags?: string[];
  relatedStoryId?: string;
  relatedStoryTitle?: string;
  images?: string[];
  attachments?: string[];
  color?: string;
  icon?: string;
  isMilestone: boolean;
  isPinned: boolean;
  likesCount: number;
  commentsCount: number;
  viewCount: number;
  customData?: {
    category?: string;
    subCategory?: string;
    duration?: string;
    outcome?: string;
    impact?: string;
    lessons?: string[];
    linkedEvents?: string[];
  };
  metadata?: Record<string, unknown>;
  createdAt: Date;
  updatedAt: Date;

  // 权限相关
  canEdit: boolean;
  canDelete: boolean;
  canComment: boolean;
}

/**
 * 时间线统计响应DTO
 */
export class TimelineStatsResponseDto {
  userId: string;
  totalEvents: number;
  milestoneEvents: number;
  publicEvents: number;
  privateEvents: number;
  totalYears: number;
  eventsThisYear: number;
  eventsThisMonth: number;

  // 按类型统计
  typeStats: Array<{
    eventType: EventType;
    count: number;
    percentage: number;
  }>;

  // 按重要性统计
  importanceStats: Array<{
    importance: EventImportance;
    count: number;
    percentage: number;
  }>;

  // 热门标签
  topTags: Array<{
    tag: string;
    count: number;
    recentUse: Date;
  }>;

  // 年度分布
  yearlyDistribution: Array<{
    year: number;
    count: number;
    milestones: number;
  }>;

  // 最近活动
  recentEvents: Array<{
    eventId: string;
    title: string;
    eventType: EventType;
    eventDate: Date;
    isMilestone: boolean;
  }>;

  // 即将到来的事件（如果有结束日期）
  upcomingEvents: Array<{
    eventId: string;
    title: string;
    eventType: EventType;
    eventDate: Date;
    endDate?: Date;
  }>;
}

/**
 * 更新时间线事件DTO
 */
export class UpdateTimelineEventDto {
  title?: string;
  description?: string;
  eventType?: EventType;
  visibility?: EventVisibility;
  importance?: EventImportance;
  eventDate?: string;
  endDate?: string;
  location?: string;
  relatedPeople?: string[];
  tags?: string[];
  relatedStoryId?: string;
  images?: string[];
  attachments?: string[];
  color?: string;
  icon?: string;
  isMilestone?: boolean;
  isPinned?: boolean;
  customData?: {
    category?: string;
    subCategory?: string;
    duration?: string;
    outcome?: string;
    impact?: string;
    lessons?: string[];
    linkedEvents?: string[];
  };
  metadata?: Record<string, unknown>;
}
