/**
 * ImageModule 单元测试
 * 测试图片处理模块的依赖注入和模块配置
 */

import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { CACHE_MANAGER } from "@nestjs/cache-manager";
import { ImageModule } from "./image.module";
import { ImageService } from "./image.service";
import { SecureImageService } from "./secure-image.service";

describe("ImageModule", () => {
  let module: TestingModule;

  const mockCacheManager = {
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
    reset: jest.fn(),
    store: {
      name: "memory",
      isCacheable: jest.fn(),
      get: jest.fn(),
      set: jest.fn(),
      del: jest.fn(),
      reset: jest.fn(),
      mget: jest.fn(),
      mset: jest.fn(),
      mdel: jest.fn(),
      keys: jest.fn(),
      ttl: jest.fn(),
    },
  };

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [ImageModule],
    })
      .overrideProvider(CACHE_MANAGER)
      .useValue(mockCacheManager)
      .compile();
  });

  afterEach(async () => {
    if (module) {
      await module.close();
    }
  });

  it("should be defined", () => {
    expect(module).toBeDefined();
  });

  describe("服务提供者", () => {
    it("should provide ImageService", () => {
      const imageService = module.get<ImageService>(ImageService);
      expect(imageService).toBeDefined();
      expect(imageService).toBeInstanceOf(ImageService);
    });

    it("should provide SecureImageService", () => {
      const secureImageService =
        module.get<SecureImageService>(SecureImageService);
      expect(secureImageService).toBeDefined();
      expect(secureImageService).toBeInstanceOf(SecureImageService);
    });
  });

  describe("依赖注入", () => {
    it("should inject CACHE_MANAGER", () => {
      const cacheManager = module.get(CACHE_MANAGER);
      expect(cacheManager).toBeDefined();
      expect(cacheManager).toBe(mockCacheManager);
    });
  });

  describe("模块配置", () => {
    it("should export ImageService for other modules", () => {
      const imageService = module.get<ImageService>(ImageService);
      expect(imageService).toBeDefined();
    });

    it("should export SecureImageService for other modules", () => {
      const secureImageService =
        module.get<SecureImageService>(SecureImageService);
      expect(secureImageService).toBeDefined();
    });
  });

  describe("图片处理功能", () => {
    it("should be able to check image access", async () => {
      const imageService = module.get<ImageService>(ImageService);

      // 验证图片服务具有访问检查功能
      expect(imageService).toBeDefined();
      expect(typeof imageService.checkImageAccess).toBe("function");
    });

    it("should be able to handle secure image operations", async () => {
      const secureImageService =
        module.get<SecureImageService>(SecureImageService);

      // 验证安全图片服务具有安全处理功能
      expect(secureImageService).toBeDefined();
      expect(typeof secureImageService.validateAccess).toBe("function");
    });
  });
});
