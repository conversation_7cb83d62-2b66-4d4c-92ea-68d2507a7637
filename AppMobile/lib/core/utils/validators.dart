/// 验证器工具类
class Validators {
  Validators._();

  /// 手机号正则表达式
  static final RegExp _phoneRegExp = RegExp(r'^1[3-9]\d{9}$');
  
  /// 邮箱正则表达式
  static final RegExp _emailRegExp = RegExp(
    r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
  );
  
  /// 用户名正则表达式（字母数字下划线，2-20位）
  static final RegExp _usernameRegExp = RegExp(r'^[a-zA-Z0-9_]{2,20}$');

  /// 验证手机号
  static String? validatePhone(String? value) {
    if (value == null || value.isEmpty) {
      return '请输入手机号';
    }
    if (!_phoneRegExp.hasMatch(value)) {
      return '请输入正确的手机号';
    }
    return null;
  }

  /// 验证邮箱
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return '请输入邮箱';
    }
    if (!_emailRegExp.hasMatch(value)) {
      return '请输入正确的邮箱格式';
    }
    return null;
  }

  /// 验证密码
  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return '请输入密码';
    }
    if (value.length < 6) {
      return '密码至少6位';
    }
    if (value.length > 20) {
      return '密码最多20位';
    }
    return null;
  }

  /// 验证确认密码
  static String? validateConfirmPassword(String? value, String password) {
    if (value == null || value.isEmpty) {
      return '请再次输入密码';
    }
    if (value != password) {
      return '两次输入的密码不一致';
    }
    return null;
  }

  /// 验证用户名
  static String? validateUsername(String? value) {
    if (value == null || value.isEmpty) {
      return '请输入用户名';
    }
    if (value.length < 2) {
      return '用户名至少2位';
    }
    if (value.length > 20) {
      return '用户名最多20位';
    }
    if (!_usernameRegExp.hasMatch(value)) {
      return '用户名只能包含字母、数字和下划线';
    }
    return null;
  }

  /// 验证验证码
  static String? validateSmsCode(String? value, {int length = 6}) {
    if (value == null || value.isEmpty) {
      return '请输入验证码';
    }
    if (value.length != length) {
      return '请输入$length位验证码';
    }
    if (!RegExp(r'^\d+$').hasMatch(value)) {
      return '验证码只能是数字';
    }
    return null;
  }

  /// 验证非空
  static String? validateRequired(String? value, String fieldName) {
    if (value == null || value.isEmpty) {
      return '$fieldName不能为空';
    }
    return null;
  }

  /// 验证最小长度
  static String? validateMinLength(String? value, int minLength, String fieldName) {
    if (value == null || value.isEmpty) {
      return '$fieldName不能为空';
    }
    if (value.length < minLength) {
      return '$fieldName至少$minLength个字符';
    }
    return null;
  }

  /// 验证最大长度
  static String? validateMaxLength(String? value, int maxLength, String fieldName) {
    if (value != null && value.length > maxLength) {
      return '$fieldName最多$maxLength个字符';
    }
    return null;
  }

  /// 验证长度范围
  static String? validateLengthRange(
    String? value, 
    int minLength, 
    int maxLength, 
    String fieldName,
  ) {
    if (value == null || value.isEmpty) {
      return '$fieldName不能为空';
    }
    if (value.length < minLength) {
      return '$fieldName至少$minLength个字符';
    }
    if (value.length > maxLength) {
      return '$fieldName最多$maxLength个字符';
    }
    return null;
  }
}