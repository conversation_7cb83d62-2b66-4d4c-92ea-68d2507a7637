/**
 * 企业级数据播种器接口定义
 *
 * 用途: 为前后端联调提供完整的测试数据
 * 场景: 模拟多用户角色业务流程，支持人物点亮系统测试
 */

export interface UserSeedData {
  id?: string;
  username: string;
  email: string;
  phone: string;
  passwordHash: string;
  nickname: string;
  bio?: string;
  avatarUrl?: string;
  birthDate?: Date;
  ygsNumber: string;
  profileDisplaySettings: {
    showBirthday: boolean;
    showBio: boolean;
    showStatistics: boolean;
    showCharacters: boolean;
    showTimeline: boolean;
  };
  status: "active" | "inactive" | "suspended";
  anomalyStatus: "normal" | "warning" | "restricted" | "suspended";
  isActive: boolean;
  isVerified: boolean;
  lastLoginAt?: Date;
}

export interface StorySeedData {
  id?: string;
  title: string;
  content: string;
  excerpt?: string;
  permissionLevel: "private" | "friends" | "public" | "characters_only";
  mood?: string;
  authorId: string;
  authorUsername: string; // 用于关联查找
  isPublished: boolean;
  publishedAt?: Date;
  occurredAt?: Date; // 故事发生时间
  metadata?: {
    location?: string;
    timeframe?: string;
    genre?: string;
    tags?: string[];
    theme?: string;
  };
  viewCount: number;
  shareCount: number;
}

export interface CharacterSeedData {
  id?: string;
  name: string;
  description?: string;
  phone: string; // 对应真实用户手机号
  targetUsername: string; // 目标点亮用户的username
  storyId: string;
  storyTitle: string; // 用于关联查找
  authorUsername: string; // 故事作者username
  isLighted: boolean;
  lighterUserId?: string;
  lightingCount: number;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface LightRequestSeedData {
  id?: string;
  storyId: string;
  characterId: string;
  requesterId: string;
  requesterUsername: string;
  authorId: string;
  authorUsername: string;
  phone: string;
  message?: string;
  status: "pending" | "approved" | "rejected" | "expired";
  requestedAt: Date;
  respondedAt?: Date;
  expiresAt: Date;
}

/**
 * 数据播种统计信息
 */
export interface SeedStats {
  users: number;
  stories: number;
  characters: number;
  lightRequests: number;
  successfulLightings: number;
  pendingRequests: number;
}

/**
 * 数据播种配置
 */
export interface SeedConfig {
  clearExistingData: boolean;
  generateImages: boolean;
  createLightingRequests: boolean;
  autoApproveSomeRequests: boolean;
  userCount: number;
  storyCount: number;
  characterCount: number;
  environment: "development" | "testing" | "staging";
}

/**
 * 用户角色定义
 */
export interface UserRole {
  type: "author" | "character_user" | "regular_user" | "vip_tester";
  description: string;
  permissions: string[];
  testScenarios: string[];
}
