import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import type { ExecutionContext } from "@nestjs/common";
import { UnauthorizedException } from "@nestjs/common";
import { JwtAuthGuard } from "./jwt-auth.guard";
import { AuthService } from "../auth.service";
import type { User } from "../../users/entities/user.entity";
// import type { AuthenticatedRequest } from "../../../common/types";

// 测试用的请求类型
interface TestRequest {
  headers: {
    authorization?: string;
  };
  user?: {
    id: string;
    username: string;
    nickname: string;
    email: string;
    phone: string;
    isVerified: boolean;
    permissions: string[];
    role: string;
  };
}

describe("JwtAuthGuard", () => {
  let guard: JwtAuthGuard;
  let authService: AuthService;

  // Mock 数据
  const mockUser: Partial<User> = {
    id: "test-user-id",
    username: "testuser",
    nickname: "Test User",
    email: "<EMAIL>",
    phone: "13800138000",
    isActive: true,
    passwordHash: "hashed_password",
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockRequest: TestRequest = {
    headers: {},
    user: undefined,
  };

  // 完全重新设计ExecutionContext Mock
  let mockExecutionContext: ExecutionContext;

  beforeEach(async () => {
    const mockAuthService = {
      validateToken: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        JwtAuthGuard,
        {
          provide: AuthService,
          useValue: mockAuthService,
        },
      ],
    }).compile();

    guard = module.get<JwtAuthGuard>(JwtAuthGuard);
    authService = module.get<AuthService>(AuthService);

    // 重置mock request并创建新的ExecutionContext
    mockRequest.headers = {};
    mockRequest.user = undefined;

    // 每次都重新创建ExecutionContext mock
    mockExecutionContext = {
      switchToHttp: jest.fn().mockReturnValue({
        getRequest: jest.fn().mockReturnValue(mockRequest),
      }),
    } as unknown as ExecutionContext;
  });

  afterEach(() => {
    // 只清除authService相关的mock
    (authService.validateToken as jest.Mock).mockClear();
  });

  describe("canActivate", () => {
    it("should allow access with valid Bearer token", async () => {
      const validToken = "valid.jwt.token";
      mockRequest.headers.authorization = `Bearer ${validToken}`;

      (authService.validateToken as jest.Mock).mockResolvedValue(
        mockUser as User,
      );

      const result = await guard.canActivate(mockExecutionContext);

      expect(result).toBe(true);
      expect(authService.validateToken).toHaveBeenCalledWith(validToken);
      expect(mockRequest.user).toEqual({
        id: mockUser.id,
        username: mockUser.username,
        nickname: mockUser.nickname,
        email: mockUser.email,
        phone: mockUser.phone,
        isVerified: true,
        permissions: [],
        role: "user",
      });
    });

    it("should use nickname as username when username is null", async () => {
      const userWithoutUsername = {
        ...mockUser,
        username: null,
        nickname: "Nickname User",
      } as unknown as User;

      const validToken = "valid.jwt.token";
      mockRequest.headers.authorization = `Bearer ${validToken}`;

      (authService.validateToken as jest.Mock).mockResolvedValue(
        userWithoutUsername,
      );

      const result = await guard.canActivate(mockExecutionContext);

      expect(result).toBe(true);
      expect(mockRequest.user?.username).toBe("Nickname User");
    });

    it('should use "unknown" as username when both username and nickname are null', async () => {
      const userWithoutNames = {
        ...mockUser,
        username: null,
        nickname: null,
      } as unknown as User;

      const validToken = "valid.jwt.token";
      mockRequest.headers.authorization = `Bearer ${validToken}`;

      (authService.validateToken as jest.Mock).mockResolvedValue(
        userWithoutNames,
      );

      const result = await guard.canActivate(mockExecutionContext);

      expect(result).toBe(true);
      expect(mockRequest.user?.username).toBe("unknown");
    });

    it("should handle user with isActive as false", async () => {
      const inactiveUser = {
        ...mockUser,
        isActive: false,
      } as unknown as User;

      const validToken = "valid.jwt.token";
      mockRequest.headers.authorization = `Bearer ${validToken}`;

      (authService.validateToken as jest.Mock).mockResolvedValue(inactiveUser);

      const result = await guard.canActivate(mockExecutionContext);

      expect(result).toBe(true);
      expect(mockRequest.user?.isVerified).toBe(false);
    });

    it("should handle user with isActive as null/undefined", async () => {
      const userWithNullActive = {
        ...mockUser,
        isActive: null,
      } as unknown as User;

      const validToken = "valid.jwt.token";
      mockRequest.headers.authorization = `Bearer ${validToken}`;

      (authService.validateToken as jest.Mock).mockResolvedValue(
        userWithNullActive,
      );

      const result = await guard.canActivate(mockExecutionContext);

      expect(result).toBe(true);
      expect(mockRequest.user?.isVerified).toBe(false);
    });

    it("should throw UnauthorizedException when no authorization header", async () => {
      mockRequest.headers = {}; // 没有authorization header

      await expect(guard.canActivate(mockExecutionContext)).rejects.toThrow(
        new UnauthorizedException("访问令牌不存在"),
      );

      expect(authService.validateToken).not.toHaveBeenCalled();
    });

    it("should throw UnauthorizedException when authorization header is empty", async () => {
      mockRequest.headers.authorization = "";

      await expect(guard.canActivate(mockExecutionContext)).rejects.toThrow(
        new UnauthorizedException("访问令牌不存在"),
      );

      expect(authService.validateToken).not.toHaveBeenCalled();
    });

    it("should throw UnauthorizedException when authorization header has no Bearer prefix", async () => {
      mockRequest.headers.authorization = "Basic dXNlcjpwYXNz";

      await expect(guard.canActivate(mockExecutionContext)).rejects.toThrow(
        new UnauthorizedException("访问令牌不存在"),
      );

      expect(authService.validateToken).not.toHaveBeenCalled();
    });

    it("should throw UnauthorizedException when Bearer token is empty", async () => {
      mockRequest.headers.authorization = "Bearer ";

      await expect(guard.canActivate(mockExecutionContext)).rejects.toThrow(
        new UnauthorizedException("访问令牌不存在"),
      );

      expect(authService.validateToken).not.toHaveBeenCalled();
    });

    it("should throw UnauthorizedException when only Bearer keyword without token", async () => {
      mockRequest.headers.authorization = "Bearer";

      await expect(guard.canActivate(mockExecutionContext)).rejects.toThrow(
        new UnauthorizedException("访问令牌不存在"),
      );

      expect(authService.validateToken).not.toHaveBeenCalled();
    });

    it("should throw UnauthorizedException when token validation fails", async () => {
      const invalidToken = "invalid.jwt.token";
      mockRequest.headers.authorization = `Bearer ${invalidToken}`;

      (authService.validateToken as jest.Mock).mockRejectedValue(
        new Error("Token expired"),
      );

      await expect(guard.canActivate(mockExecutionContext)).rejects.toThrow(
        new UnauthorizedException("无效的访问令牌"),
      );

      expect(authService.validateToken).toHaveBeenCalledWith(invalidToken);
      expect(mockRequest.user).toBeUndefined();
    });

    it("should handle tokens with extra whitespace", async () => {
      const validToken = "valid.jwt.token";
      mockRequest.headers.authorization = `Bearer ${validToken}`;

      (authService.validateToken as jest.Mock).mockResolvedValue(
        mockUser as User,
      );

      const result = await guard.canActivate(mockExecutionContext);

      expect(result).toBe(true);
      expect(authService.validateToken).toHaveBeenCalledWith(validToken);
    });

    it("should handle malformed authorization header", async () => {
      mockRequest.headers.authorization = "InvalidFormat";

      await expect(guard.canActivate(mockExecutionContext)).rejects.toThrow(
        new UnauthorizedException("访问令牌不存在"),
      );

      expect(authService.validateToken).not.toHaveBeenCalled();
    });

    it("should handle authorization header with multiple spaces", async () => {
      const validToken = "valid.jwt.token";
      mockRequest.headers.authorization = `Bearer ${validToken}`;

      (authService.validateToken as jest.Mock).mockResolvedValue(
        mockUser as User,
      );

      const result = await guard.canActivate(mockExecutionContext);

      expect(result).toBe(true);
      expect(authService.validateToken).toHaveBeenCalledWith(validToken);
    });

    it("should handle case-sensitive Bearer keyword", async () => {
      const validToken = "valid.jwt.token";

      // Test different cases
      const testCases = ["bearer", "BEARER", "Bearer", "bEaReR"];

      for (const bearerCase of testCases) {
        mockRequest.headers.authorization = `${bearerCase} ${validToken}`;

        if (bearerCase === "Bearer") {
          (authService.validateToken as jest.Mock).mockResolvedValue(
            mockUser as User,
          );

          const result = await guard.canActivate(mockExecutionContext);
          expect(result).toBe(true);
        } else {
          await expect(guard.canActivate(mockExecutionContext)).rejects.toThrow(
            new UnauthorizedException("访问令牌不存在"),
          );
        }

        jest.clearAllMocks();
      }
    });
  });

  describe("extractTokenFromHeader", () => {
    it("should extract token from valid Bearer authorization header", () => {
      const token = "valid.jwt.token";
      const request = {
        headers: {
          authorization: `Bearer ${token}`,
        },
      } as TestRequest;

      const extractedToken = (
        guard as unknown as {
          extractTokenFromHeader: (req: TestRequest) => string | undefined;
        }
      ).extractTokenFromHeader(request);
      expect(extractedToken).toBe(token);
    });

    it("should return undefined for missing authorization header", () => {
      const request = {
        headers: {},
      } as TestRequest;

      const extractedToken = (
        guard as unknown as {
          extractTokenFromHeader: (req: TestRequest) => string | undefined;
        }
      ).extractTokenFromHeader(request);
      expect(extractedToken).toBeUndefined();
    });

    it("should return undefined for non-Bearer authorization header", () => {
      const request = {
        headers: {
          authorization: "Basic dXNlcjpwYXNz",
        },
      } as TestRequest;

      const extractedToken = (
        guard as unknown as {
          extractTokenFromHeader: (req: TestRequest) => string | undefined;
        }
      ).extractTokenFromHeader(request);
      expect(extractedToken).toBeUndefined();
    });

    it("should return undefined for empty authorization header", () => {
      const request = {
        headers: {
          authorization: "",
        },
      } as TestRequest;

      const extractedToken = (
        guard as unknown as {
          extractTokenFromHeader: (req: TestRequest) => string | undefined;
        }
      ).extractTokenFromHeader(request);
      expect(extractedToken).toBeUndefined();
    });

    it("should return undefined for Bearer without token", () => {
      const request = {
        headers: {
          authorization: "Bearer",
        },
      } as TestRequest;

      const extractedToken = (
        guard as unknown as {
          extractTokenFromHeader: (req: TestRequest) => string | undefined;
        }
      ).extractTokenFromHeader(request);
      expect(extractedToken).toBeUndefined();
    });
  });

  it("should be defined", () => {
    expect(guard).toBeDefined();
  });
});
