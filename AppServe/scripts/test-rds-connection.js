#!/usr/bin/env node

/**
 * 阿里云RDS PostgreSQL连接测试脚本
 * 用于验证本地开发环境是否能正常连接到云端数据库
 */

const { Client } = require('pg');
require('dotenv').config();

// 颜色定义
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    dim: '\x1b[2m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m'
};

// 日志函数
const log = {
    info: (msg) => console.log(`${colors.blue}[INFO]${colors.reset} ${msg}`),
    success: (msg) => console.log(`${colors.green}[SUCCESS]${colors.reset} ${msg}`),
    warning: (msg) => console.log(`${colors.yellow}[WARNING]${colors.reset} ${msg}`),
    error: (msg) => console.log(`${colors.red}[ERROR]${colors.reset} ${msg}`),
    debug: (msg) => console.log(`${colors.cyan}[DEBUG]${colors.reset} ${msg}`)
};

// 数据库配置
const dbConfig = {
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT) || 5432,
    database: process.env.DB_NAME || process.env.DB_DATABASE,
    user: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    ssl: process.env.DB_SSL === 'true' ? {
        rejectUnauthorized: false,
        sslmode: 'require'
    } : false,
    connectionTimeoutMillis: 60000,
    idleTimeoutMillis: 10000,
    query_timeout: 30000,
};

// 检查配置
function checkConfig() {
    log.info('检查数据库配置...');
    
    const required = ['host', 'database', 'user', 'password'];
    const missing = required.filter(key => !dbConfig[key]);
    
    if (missing.length > 0) {
        log.error(`缺少必要配置: ${missing.join(', ')}`);
        log.error('请检查 .env.cloud 文件中的数据库配置');
        process.exit(1);
    }
    
    log.debug('数据库配置:');
    log.debug(`  主机: ${dbConfig.host}`);
    log.debug(`  端口: ${dbConfig.port}`);
    log.debug(`  数据库: ${dbConfig.database}`);
    log.debug(`  用户: ${dbConfig.user}`);
    log.debug(`  SSL: ${dbConfig.ssl ? '启用' : '禁用'}`);
    
    log.success('配置检查通过');
}

// 测试基础连接
async function testBasicConnection() {
    log.info('测试基础数据库连接...');
    
    const client = new Client(dbConfig);
    
    try {
        await client.connect();
        log.success('数据库连接成功');
        
        // 测试基本查询
        const result = await client.query('SELECT version(), current_database(), current_user;');
        const row = result.rows[0];
        
        log.success('数据库信息:');
        log.success(`  版本: ${row.version}`);
        log.success(`  数据库: ${row.current_database}`);
        log.success(`  用户: ${row.current_user}`);
        
        return true;
    } catch (error) {
        log.error(`连接失败: ${error.message}`);
        return false;
    } finally {
        await client.end();
    }
}

// 测试表结构
async function testTableStructure() {
    log.info('检查数据库表结构...');
    
    const client = new Client(dbConfig);
    
    try {
        await client.connect();
        
        // 查询现有表
        const result = await client.query(`
            SELECT table_name, table_type 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            ORDER BY table_name;
        `);
        
        if (result.rows.length > 0) {
            log.success(`发现 ${result.rows.length} 个表:`);
            result.rows.forEach(row => {
                log.success(`  - ${row.table_name} (${row.table_type})`);
            });
        } else {
            log.warning('数据库中没有找到表，可能需要运行数据库迁移');
        }
        
        return true;
    } catch (error) {
        log.error(`表结构检查失败: ${error.message}`);
        return false;
    } finally {
        await client.end();
    }
}

// 测试权限
async function testPermissions() {
    log.info('测试数据库权限...');
    
    const client = new Client(dbConfig);
    
    try {
        await client.connect();
        
        // 创建测试表
        await client.query(`
            CREATE TABLE IF NOT EXISTS test_permissions (
                id SERIAL PRIMARY KEY,
                test_data VARCHAR(50),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        `);
        
        // 插入测试数据
        await client.query(`
            INSERT INTO test_permissions (test_data) VALUES ('connection_test');
        `);
        
        // 查询测试数据
        const result = await client.query(`
            SELECT * FROM test_permissions WHERE test_data = 'connection_test';
        `);
        
        if (result.rows.length > 0) {
            log.success('数据库读写权限正常');
        }
        
        // 清理测试数据
        await client.query(`
            DELETE FROM test_permissions WHERE test_data = 'connection_test';
        `);
        
        await client.query(`
            DROP TABLE test_permissions;
        `);
        
        return true;
    } catch (error) {
        log.error(`权限测试失败: ${error.message}`);
        return false;
    } finally {
        await client.end();
    }
}

// 测试连接池
async function testConnectionPool() {
    log.info('测试连接池性能...');
    
    const clients = [];
    const maxConnections = 5;
    
    try {
        // 创建多个连接
        for (let i = 0; i < maxConnections; i++) {
            const client = new Client(dbConfig);
            await client.connect();
            clients.push(client);
        }
        
        // 并发查询测试
        const promises = clients.map(async (client, index) => {
            const start = Date.now();
            await client.query('SELECT pg_sleep(0.1);');
            const duration = Date.now() - start;
            return { connection: index + 1, duration };
        });
        
        const results = await Promise.all(promises);
        
        log.success(`成功创建 ${maxConnections} 个并发连接`);
        results.forEach(result => {
            log.success(`  连接${result.connection}: ${result.duration}ms`);
        });
        
        return true;
    } catch (error) {
        log.error(`连接池测试失败: ${error.message}`);
        return false;
    } finally {
        // 关闭所有连接
        for (const client of clients) {
            await client.end();
        }
    }
}

// 网络延迟测试
async function testNetworkLatency() {
    log.info('测试网络延迟...');
    
    const client = new Client(dbConfig);
    
    try {
        await client.connect();
        
        const tests = [];
        const testCount = 5;
        
        for (let i = 0; i < testCount; i++) {
            const start = Date.now();
            await client.query('SELECT 1;');
            const duration = Date.now() - start;
            tests.push(duration);
        }
        
        const avgLatency = tests.reduce((a, b) => a + b) / tests.length;
        const minLatency = Math.min(...tests);
        const maxLatency = Math.max(...tests);
        
        log.success(`网络延迟测试结果 (${testCount} 次测试):`);
        log.success(`  平均延迟: ${avgLatency.toFixed(2)}ms`);
        log.success(`  最小延迟: ${minLatency}ms`);
        log.success(`  最大延迟: ${maxLatency}ms`);
        
        if (avgLatency > 100) {
            log.warning('网络延迟较高，可能影响应用性能');
        }
        
        return true;
    } catch (error) {
        log.error(`网络延迟测试失败: ${error.message}`);
        return false;
    } finally {
        await client.end();
    }
}

// 主测试函数
async function runTests() {
    log.info('开始阿里云RDS PostgreSQL连接测试...');
    log.info('=====================================');
    
    try {
        // 1. 检查配置
        checkConfig();
        
        // 2. 测试基础连接
        const basicTest = await testBasicConnection();
        if (!basicTest) {
            log.error('基础连接测试失败，终止后续测试');
            process.exit(1);
        }
        
        // 3. 测试表结构
        await testTableStructure();
        
        // 4. 测试权限
        await testPermissions();
        
        // 5. 测试连接池
        await testConnectionPool();
        
        // 6. 测试网络延迟
        await testNetworkLatency();
        
        log.info('=====================================');
        log.success('所有测试完成！RDS连接配置正常');
        
        // 显示启动提示
        log.info('');
        log.info('现在可以启动云端开发环境:');
        log.info('  npm run cloud:start');
        log.info('');
        log.info('或者手动启动:');
        log.info('  1. npm run env:cloud');
        log.info('  2. npm run cloud:redis');
        log.info('  3. npm run migration:run');
        log.info('  4. npm run start:dev');
        
    } catch (error) {
        log.error(`测试过程中发生错误: ${error.message}`);
        process.exit(1);
    }
}

// 运行测试
if (require.main === module) {
    runTests();
}

module.exports = { runTests };