import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  Req,
  HttpStatus,
} from "@nestjs/common";
import type { Request } from "express";
import { StoryReferencesService } from "./story-references.service";
import {
  CreateReferenceDto,
  CreateCollectionDto,
  ReferenceQueryDto,
  CollectionQueryDto,
} from "./dto";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { ApiResponse } from "../../common/dto/api-response.dto";
import type { PaginatedResponseDto } from "../../common/dto/paginated-response.dto";
import type {
  ReferenceResponseDto,
  CollectionResponseDto,
  ReferenceStatsResponseDto,
} from "./dto";

/**
 * 故事引用控制器
 * 提供故事引用和引用集合管理的API接口
 */
@Controller("story-references")
export class StoryReferencesController {
  constructor(
    private readonly storyReferencesService: StoryReferencesService,
  ) {}

  // ==================== 故事引用管理 ====================

  /**
   * 创建故事引用
   * POST /story-references
   */
  @Post()
  @UseGuards(JwtAuthGuard)
  async createReference(
    @Body() createReferenceDto: CreateReferenceDto,
    @Req() req: Request & { user: { id: string } },
  ): Promise<ApiResponse<ReferenceResponseDto>> {
    const reference = await this.storyReferencesService.createReference(
      createReferenceDto,
      req.user.id,
    );

    return ApiResponse.success(reference, "创建引用成功", HttpStatus.CREATED);
  }

  /**
   * 获取用户的引用列表
   * GET /story-references/user/:userId
   */
  @Get("user/:userId")
  @UseGuards(JwtAuthGuard)
  async getUserReferences(
    @Param("userId") userId: string,
    @Query() queryDto: ReferenceQueryDto,
    @Req() req: Request & { user: { id: string } },
  ): Promise<ApiResponse<PaginatedResponseDto<ReferenceResponseDto>>> {
    const result = await this.storyReferencesService.getUserReferences(
      userId,
      queryDto,
      req.user.id,
    );

    return ApiResponse.success(result, "获取引用列表成功", HttpStatus.OK);
  }

  /**
   * 获取引用详情
   * GET /story-references/:referenceId
   */
  @Get(":referenceId")
  @UseGuards(JwtAuthGuard)
  async getReferenceById(
    @Param("referenceId") referenceId: string,
    @Req() req: Request & { user: { id: string } },
  ): Promise<ApiResponse<ReferenceResponseDto>> {
    const reference = await this.storyReferencesService.getReferenceById(
      referenceId,
      req.user.id,
    );

    return ApiResponse.success(reference, "获取引用详情成功", HttpStatus.OK);
  }

  // ==================== 引用集合管理 ====================

  /**
   * 创建引用集合
   * POST /story-references/collections
   */
  @Post("collections")
  @UseGuards(JwtAuthGuard)
  async createCollection(
    @Body() createCollectionDto: CreateCollectionDto,
    @Req() req: Request & { user: { id: string } },
  ): Promise<ApiResponse<CollectionResponseDto>> {
    const collection = await this.storyReferencesService.createCollection(
      createCollectionDto,
      req.user.id,
    );

    return ApiResponse.success(collection, "创建集合成功", HttpStatus.CREATED);
  }

  /**
   * 获取用户的引用集合列表
   * GET /story-references/collections/user/:userId
   */
  @Get("collections/user/:userId")
  @UseGuards(JwtAuthGuard)
  async getUserCollections(
    @Param("userId") userId: string,
    @Query() queryDto: CollectionQueryDto,
    @Req() req: Request & { user: { id: string } },
  ): Promise<ApiResponse<PaginatedResponseDto<CollectionResponseDto>>> {
    const result = await this.storyReferencesService.getUserCollections(
      userId,
      queryDto,
      req.user.id,
    );

    return ApiResponse.success(result, "获取集合列表成功", HttpStatus.OK);
  }

  /**
   * 获取引用统计数据
   * GET /story-references/stats/:userId
   */
  @Get("stats/:userId")
  @UseGuards(JwtAuthGuard)
  async getReferenceStats(
    @Param("userId") userId: string,
  ): Promise<ApiResponse<ReferenceStatsResponseDto>> {
    const stats = await this.storyReferencesService.getReferenceStats(userId);

    return ApiResponse.success(stats, "获取引用统计成功", HttpStatus.OK);
  }
}
