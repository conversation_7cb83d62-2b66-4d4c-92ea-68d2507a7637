/**
 * 企业级通知系统种子数据
 *
 * 基于 YGS v1.0.0 情绪保护机制设计
 * 只包含正面通知，体现平台的人文关怀
 */

import { USER_ROLES } from "./enhanced-user.seeder";
import { STORY_TITLES } from "./enhanced-story.seeder";
import { CHARACTER_NAMES } from "./enhanced-character.seeder";

export interface EnhancedNotificationSeedData {
  recipientUsername: string;
  type:
    | "lighting_success"
    | "story_like"
    | "comment_reply"
    | "story_publish"
    | "follow_new"
    | "lighting_confirmed"
    | "comment_like"
    | "system_welcome"
    | "achievement";
  title: string;
  content: string;
  data: object;
  isRead: boolean;
  createdDaysAgo: number;
  priority: "high" | "medium" | "low";
}

export const ENHANCED_NOTIFICATION_SEED_DATA: EnhancedNotificationSeedData[] = [
  // ========== 张明远的通知 - 创作者视角 ==========
  {
    recipientUsername: USER_ROLES.ZHANG_MINGYUAN,
    type: "lighting_confirmed",
    title: "🎉 您的人物被成功点亮！",
    content:
      '李小红 成功点亮了您故事《那年夏天，我们约定十年后再聚》中的人物"李明"。这是一次真实身份的确认，恭喜您找到了故事中的朋友！',
    data: {
      storyTitle: STORY_TITLES.UNIVERSITY_PROMISE,
      characterName: CHARACTER_NAMES.LIMING_UNIVERSITY,
      lighterUsername: USER_ROLES.LI_XIAOHONG,
      lighterNickname: "小红姐姐",
    },
    isRead: false,
    createdDaysAgo: 28,
    priority: "high",
  },
  {
    recipientUsername: USER_ROLES.ZHANG_MINGYUAN,
    type: "lighting_confirmed",
    title: "🌟 又一个人物被点亮！",
    content:
      '李小红 成功点亮了您故事《李明，还记得那个雨夜吗》中的人物"明哥"。您的故事触动了真实的人，这是写作的最高荣誉！',
    data: {
      storyTitle: STORY_TITLES.RAINY_NIGHT,
      characterName: CHARACTER_NAMES.LIMING_RAINY,
      lighterUsername: USER_ROLES.LI_XIAOHONG,
      lighterNickname: "小红姐姐",
    },
    isRead: true,
    createdDaysAgo: 24,
    priority: "high",
  },
  {
    recipientUsername: USER_ROLES.ZHANG_MINGYUAN,
    type: "story_like",
    title: "❤️ 您的故事获得了点赞",
    content:
      "您的故事《那年夏天，我们约定十年后再聚》获得了 236 个点赞！读者们被您的真挚情感深深打动。",
    data: {
      storyTitle: STORY_TITLES.UNIVERSITY_PROMISE,
      likeCount: 236,
      latestLiker: "芳芳爱读书",
    },
    isRead: true,
    createdDaysAgo: 2,
    priority: "medium",
  },
  {
    recipientUsername: USER_ROLES.ZHANG_MINGYUAN,
    type: "comment_reply",
    title: "💬 您收到了新的评论回复",
    content:
      "小红姐姐 回复了您在故事《那年夏天，我们约定十年后再聚》下的评论：期待我们也能有机会见面聊聊这些年的变化。",
    data: {
      storyTitle: STORY_TITLES.UNIVERSITY_PROMISE,
      commenterNickname: "小红姐姐",
      replyContent: "谢谢你的点亮！没想到能在这里找到你...",
    },
    isRead: false,
    createdDaysAgo: 3,
    priority: "medium",
  },
  {
    recipientUsername: USER_ROLES.ZHANG_MINGYUAN,
    type: "follow_new",
    title: "👥 您有新的关注者",
    content: "陈总 开始关注你了！您的故事吸引了更多读者，继续加油！",
    data: {
      followerUsername: USER_ROLES.CHEN_VIP,
      followerNickname: "陈总",
    },
    isRead: true,
    createdDaysAgo: 5,
    priority: "low",
  },

  // ========== 李小红的通知 - 点亮者视角 ==========
  {
    recipientUsername: USER_ROLES.LI_XIAOHONG,
    type: "lighting_success",
    title: "🎊 恭喜！您成功点亮了人物",
    content:
      '您成功点亮了张明远故事《那年夏天，我们约定十年后再聚》中的人物"李明"！您现在可以在个人主页的点亮集中查看这个珍贵的连接。',
    data: {
      storyTitle: STORY_TITLES.UNIVERSITY_PROMISE,
      characterName: CHARACTER_NAMES.LIMING_UNIVERSITY,
      authorUsername: USER_ROLES.ZHANG_MINGYUAN,
      authorNickname: "明远说",
    },
    isRead: true,
    createdDaysAgo: 28,
    priority: "high",
  },
  {
    recipientUsername: USER_ROLES.LI_XIAOHONG,
    type: "lighting_success",
    title: "✨ 再次成功点亮！",
    content:
      '您成功点亮了张明远故事《李明，还记得那个雨夜吗》中的人物"明哥"！同一个人在不同故事中的多次点亮，体现了深厚的友谊。',
    data: {
      storyTitle: STORY_TITLES.RAINY_NIGHT,
      characterName: CHARACTER_NAMES.LIMING_RAINY,
      authorUsername: USER_ROLES.ZHANG_MINGYUAN,
      authorNickname: "明远说",
    },
    isRead: true,
    createdDaysAgo: 24,
    priority: "high",
  },
  {
    recipientUsername: USER_ROLES.LI_XIAOHONG,
    type: "lighting_success",
    title: "🌈 第三次点亮成功！",
    content:
      '您成功点亮了故事《那些年，我们一起追过的剧》中的人物"晓晓"！您在平台上的点亮成就越来越丰富了。',
    data: {
      storyTitle: STORY_TITLES.DRAMA_MEMORIES,
      characterName: CHARACTER_NAMES.XIAOXIAO,
      authorNickname: "作者昵称",
    },
    isRead: false,
    createdDaysAgo: 9,
    priority: "high",
  },
  {
    recipientUsername: USER_ROLES.LI_XIAOHONG,
    type: "comment_like",
    title: "👍 您的评论获得了点赞",
    content:
      "您在故事《那年夏天，我们约定十年后再聚》下的评论获得了 23 个点赞！您的分享引起了很多人的共鸣。",
    data: {
      storyTitle: STORY_TITLES.UNIVERSITY_PROMISE,
      commentContent: "看到这个故事，眼泪都出来了...",
      likeCount: 23,
    },
    isRead: true,
    createdDaysAgo: 4,
    priority: "medium",
  },
  {
    recipientUsername: USER_ROLES.LI_XIAOHONG,
    type: "achievement",
    title: "🏆 恭喜获得成就：点亮达人",
    content:
      "您已成功点亮了 3 个人物！这是一个了不起的成就，说明您在平台上建立了真实而珍贵的人际连接。",
    data: {
      achievementType: "lighting_master",
      lightingCount: 3,
      nextMilestone: 5,
    },
    isRead: false,
    createdDaysAgo: 9,
    priority: "high",
  },

  // ========== 王建国的通知 - 教师创作者视角 ==========
  {
    recipientUsername: USER_ROLES.WANG_JIANGUO,
    type: "lighting_confirmed",
    title: "🎓 您的学生找到您了！",
    content:
      '周发现 成功点亮了您故事《小雨，你现在过得还好吗》中的人物"小雨"！师生重逢，这是教育最美好的回报。',
    data: {
      storyTitle: STORY_TITLES.STUDENT_XIAOYU,
      characterName: CHARACTER_NAMES.XIAOYU,
      lighterUsername: USER_ROLES.ZHOU_FAXIAN,
      lighterNickname: "发现自己",
    },
    isRead: false,
    createdDaysAgo: 14,
    priority: "high",
  },
  {
    recipientUsername: USER_ROLES.WANG_JIANGUO,
    type: "story_like",
    title: "📚 您的教育故事深受好评",
    content:
      "您的故事《小雨，你现在过得还好吗》获得了 567 个点赞！您的教育故事感动了无数读者。",
    data: {
      storyTitle: STORY_TITLES.STUDENT_XIAOYU,
      likeCount: 567,
      category: "教育故事",
    },
    isRead: true,
    createdDaysAgo: 10,
    priority: "medium",
  },
  {
    recipientUsername: USER_ROLES.WANG_JIANGUO,
    type: "comment_reply",
    title: "💝 学生给您留言了",
    content:
      "发现自己 在您的故事下留言：王老师，我就是小雨！看到这个故事真的很感动，您还记得我...",
    data: {
      storyTitle: STORY_TITLES.STUDENT_XIAOYU,
      commenterNickname: "发现自己",
      isStudentReply: true,
    },
    isRead: false,
    createdDaysAgo: 7,
    priority: "high",
  },

  // ========== 周发现的通知 - 点亮达人视角 ==========
  {
    recipientUsername: USER_ROLES.ZHOU_FAXIAN,
    type: "lighting_success",
    title: "👨‍🎓 师生重逢成功！",
    content:
      '您成功点亮了王建国老师故事《小雨，你现在过得还好吗》中的人物"小雨"！与恩师的重逢，是人生最珍贵的礼物。',
    data: {
      storyTitle: STORY_TITLES.STUDENT_XIAOYU,
      characterName: CHARACTER_NAMES.XIAOYU,
      authorUsername: USER_ROLES.WANG_JIANGUO,
      authorNickname: "老王讲故事",
      connectionType: "teacher_student",
    },
    isRead: true,
    createdDaysAgo: 14,
    priority: "high",
  },
  {
    recipientUsername: USER_ROLES.ZHOU_FAXIAN,
    type: "lighting_success",
    title: "🎯 您成功点亮了大学同学",
    content:
      '您成功点亮了故事《那年夏天，我们约定十年后再聚》中的人物"赵华"！大学友谊的重新连接，让青春回忆更加珍贵。',
    data: {
      storyTitle: STORY_TITLES.UNIVERSITY_PROMISE,
      characterName: CHARACTER_NAMES.ZHAOHUA_UNIVERSITY,
      connectionType: "university_friend",
    },
    isRead: true,
    createdDaysAgo: 19,
    priority: "high",
  },
  {
    recipientUsername: USER_ROLES.ZHOU_FAXIAN,
    type: "achievement",
    title: "🌟 恭喜获得成就：点亮大师",
    content:
      "您已成功点亮了 5 个人物！您在发现真实连接方面表现卓越，是平台的点亮大师！",
    data: {
      achievementType: "lighting_grandmaster",
      lightingCount: 5,
      specialConnection: "teacher_student",
    },
    isRead: false,
    createdDaysAgo: 14,
    priority: "high",
  },

  // ========== 赵小芳的通知 - 普通读者视角 ==========
  {
    recipientUsername: USER_ROLES.ZHAO_XIAOFANG,
    type: "system_welcome",
    title: "🎉 欢迎来到有故事社区！",
    content:
      "欢迎您加入有故事！这里每个故事都可能是真实的回忆，每个人物都可能找到自己的主人。开始您的故事之旅吧！",
    data: {
      welcomeType: "new_user",
      platformFeatures: [
        "story_reading",
        "character_lighting",
        "community_interaction",
      ],
    },
    isRead: true,
    createdDaysAgo: 30,
    priority: "medium",
  },
  {
    recipientUsername: USER_ROLES.ZHAO_XIAOFANG,
    type: "comment_like",
    title: "💖 您的评论很受欢迎",
    content:
      "您在故事《那年夏天，我们约定十年后再聚》下的评论获得了 12 个点赞！您的感悟引发了读者们的共鸣。",
    data: {
      storyTitle: STORY_TITLES.UNIVERSITY_PROMISE,
      commentContent: "好温暖的故事！友情真的是人生最珍贵的财富...",
      likeCount: 12,
    },
    isRead: false,
    createdDaysAgo: 1,
    priority: "low",
  },

  // ========== 陈总的通知 - VIP用户视角 ==========
  {
    recipientUsername: USER_ROLES.CHEN_VIP,
    type: "system_welcome",
    title: "👑 VIP会员权益已激活！",
    content:
      "恭喜您成为VIP会员！您现在享有每日50次AI创作助手配额、优先客服支持、专属内容推荐等特权。感谢您对平台的支持！",
    data: {
      membershipType: "VIP",
      aiQuota: 50,
      specialFeatures: [
        "ai_assistant",
        "priority_support",
        "exclusive_content",
      ],
    },
    isRead: true,
    createdDaysAgo: 45,
    priority: "high",
  },
  {
    recipientUsername: USER_ROLES.CHEN_VIP,
    type: "story_like",
    title: "✨ 您关注的创作者有新作品",
    content:
      "您关注的 明远说 发布了新故事《创业路上，感谢有你们》，这个故事已经获得了很多读者的喜爱！",
    data: {
      authorNickname: "明远说",
      storyTitle: STORY_TITLES.STARTUP_JOURNEY,
      notificationType: "following_new_story",
    },
    isRead: true,
    createdDaysAgo: 15,
    priority: "medium",
  },

  // ========== 刘小新的通知 - 新用户视角 ==========
  {
    recipientUsername: USER_ROLES.LIU_XIAOXIN,
    type: "system_welcome",
    title: "🌱 欢迎新朋友！",
    content:
      "欢迎您注册有故事！建议您先浏览一些热门故事，了解平台的特色功能。期待看到您的第一个故事！",
    data: {
      welcomeType: "new_registration",
      recommendedActions: [
        "browse_stories",
        "try_lighting",
        "write_first_story",
      ],
    },
    isRead: false,
    createdDaysAgo: 7,
    priority: "medium",
  },
  {
    recipientUsername: USER_ROLES.LIU_XIAOXIN,
    type: "follow_new",
    title: "🤝 您有新的关注者",
    content:
      "小红姐姐 开始关注您了！作为新用户能获得资深用户的关注，说明您很有潜力哦！",
    data: {
      followerUsername: USER_ROLES.LI_XIAOHONG,
      followerNickname: "小红姐姐",
      followerType: "experienced_user",
    },
    isRead: false,
    createdDaysAgo: 3,
    priority: "low",
  },

  // ========== 系统用户的通知 - 测试账号视角 ==========
  {
    recipientUsername: USER_ROLES.SYSTEM_TEST,
    type: "system_welcome",
    title: "🔧 系统测试账号已激活",
    content:
      "系统测试账号已成功激活，具备所有功能的测试权限。请遵循测试规范，确保平台功能正常运行。",
    data: {
      accountType: "system_test",
      permissions: ["unlimited_ai", "admin_features", "test_mode"],
    },
    isRead: true,
    createdDaysAgo: 60,
    priority: "low",
  },
];

// 导出通知统计数据
export const NOTIFICATION_STATS = {
  total: ENHANCED_NOTIFICATION_SEED_DATA.length,
  unread: ENHANCED_NOTIFICATION_SEED_DATA.filter((n) => !n.isRead).length,
  read: ENHANCED_NOTIFICATION_SEED_DATA.filter((n) => n.isRead).length,

  // 按类型统计
  byType: {
    lighting_success: ENHANCED_NOTIFICATION_SEED_DATA.filter(
      (n) => n.type === "lighting_success",
    ).length,
    lighting_confirmed: ENHANCED_NOTIFICATION_SEED_DATA.filter(
      (n) => n.type === "lighting_confirmed",
    ).length,
    story_like: ENHANCED_NOTIFICATION_SEED_DATA.filter(
      (n) => n.type === "story_like",
    ).length,
    comment_reply: ENHANCED_NOTIFICATION_SEED_DATA.filter(
      (n) => n.type === "comment_reply",
    ).length,
    comment_like: ENHANCED_NOTIFICATION_SEED_DATA.filter(
      (n) => n.type === "comment_like",
    ).length,
    follow_new: ENHANCED_NOTIFICATION_SEED_DATA.filter(
      (n) => n.type === "follow_new",
    ).length,
    achievement: ENHANCED_NOTIFICATION_SEED_DATA.filter(
      (n) => n.type === "achievement",
    ).length,
    system_welcome: ENHANCED_NOTIFICATION_SEED_DATA.filter(
      (n) => n.type === "system_welcome",
    ).length,
  },

  // 按优先级统计
  byPriority: {
    high: ENHANCED_NOTIFICATION_SEED_DATA.filter((n) => n.priority === "high")
      .length,
    medium: ENHANCED_NOTIFICATION_SEED_DATA.filter(
      (n) => n.priority === "medium",
    ).length,
    low: ENHANCED_NOTIFICATION_SEED_DATA.filter((n) => n.priority === "low")
      .length,
  },

  // 情绪保护机制体现
  positiveOnlyDesign: {
    description: "所有通知都是正面的，体现平台的情绪保护机制",
    noNegativeNotifications: "拒绝申请、取消关注等负面操作不发送通知",
    emotionalCare: "通知用词温暖，突出成就和连接的价值",
  },
};
