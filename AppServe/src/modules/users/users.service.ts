import {
  Injectable,
  NotFoundException,
  ConflictException,
  ForbiddenException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, Raw } from "typeorm";
import { User } from "./entities/user.entity";
import { UserRelationship } from "./entities/user-relationship.entity";
import type {
  UpdateUserDto,
  UpdateDisplaySettingsDto,
  UserStatisticsDto,
  UserProfileDto,
} from "./dto/update-user.dto";

/**
 * 用户服务
 * 提供用户管理、个人资料、统计数据等企业级功能
 */
@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(UserRelationship)
    private relationshipRepository: Repository<UserRelationship>,
  ) {}

  /**
   * 根据ID查找用户
   */
  async findById(id: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id },
      select: [
        "id",
        "userNumber",
        "nickname",
        "username",
        "avatarUrl",
        "birthDate",
        "bio",
        "profileDisplaySettings",
        "createdAt",
        "status",
      ],
    });

    if (!user) {
      throw new NotFoundException("用户不存在");
    }

    return user;
  }

  /**
   * 根据用户号查找用户
   */
  async findByUserNumber(userNumber: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { userNumber },
      select: [
        "id",
        "userNumber",
        "nickname",
        "username",
        "avatarUrl",
        "birthDate",
        "bio",
        "profileDisplaySettings",
        "createdAt",
        "status",
      ],
    });

    if (!user) {
      throw new NotFoundException("用户不存在");
    }

    return user;
  }

  /**
   * 搜索用户（支持昵称、用户名、用户号）
   */
  async searchUsers(query: string, limit: number = 20): Promise<User[]> {
    const queryBuilder = this.userRepository.createQueryBuilder("user");

    // 如果是6位数字，优先按用户号精确匹配
    if (/^\d{6}$/.test(query)) {
      queryBuilder.where("user.userNumber = :userNumber", {
        userNumber: query,
      });
    } else {
      // 否则按昵称和用户名模糊搜索
      queryBuilder.where(
        "user.nickname ILIKE :query OR user.username ILIKE :query",
        { query: `%${query}%` },
      );
    }

    return queryBuilder
      .select([
        "user.id",
        "user.userNumber",
        "user.nickname",
        "user.username",
        "user.avatarUrl",
      ])
      .where("user.status = :status", { status: "active" })
      .limit(limit)
      .getMany();
  }

  /**
   * 更新用户资料
   */
  async updateProfile(
    userId: string,
    updateData: UpdateUserDto,
  ): Promise<User> {
    const user = await this.findById(userId);

    // 检查昵称重复（如果提供了昵称）
    if (updateData.nickname && updateData.nickname !== user.nickname) {
      const existingUser = await this.userRepository.findOne({
        where: { nickname: updateData.nickname },
      });
      if (existingUser && existingUser.id !== userId) {
        throw new ConflictException("昵称已被使用");
      }
    }

    // 检查用户名重复（如果提供了用户名）
    if (updateData.username && updateData.username !== user.username) {
      const existingUser = await this.userRepository.findOne({
        where: { username: updateData.username },
      });
      if (existingUser && existingUser.id !== userId) {
        throw new ConflictException("用户名已被使用");
      }
    }

    // 更新数据
    await this.userRepository.update(userId, {
      ...updateData,
      birthDate: updateData.birthDate
        ? new Date(updateData.birthDate)
        : undefined,
    });

    return this.findById(userId);
  }

  /**
   * 更新展示设置
   */
  async updateDisplaySettings(
    userId: string,
    settingsData: UpdateDisplaySettingsDto,
  ): Promise<User> {
    await this.userRepository.update(userId, {
      profileDisplaySettings: settingsData.profileDisplaySettings,
    });

    return this.findById(userId);
  }

  /**
   * 获取用户统计数据
   */
  async getUserStatistics(userId: string): Promise<UserStatisticsDto> {
    // 使用原生查询获取统计数据（性能优化）
    const result = await this.userRepository.query(
      `
      SELECT 
        (SELECT COUNT(*) FROM stories WHERE user_id = $1 AND status = 2) as story_count,
        (SELECT COUNT(*) FROM characters WHERE creator_id = $1 AND is_active = true) as character_count,
        (SELECT COUNT(*) FROM character_lightings WHERE lighter_user_id = $1 AND status = 'active') as lighting_count,
        (SELECT COUNT(*) FROM user_relationships WHERE user_id = $1 AND status = 1) as following_count,
        (SELECT COUNT(*) FROM user_relationships WHERE related_user_id = $1 AND status = 1) as followers_count
    `,
      [userId],
    );

    const stats = result[0];
    return {
      storyCount: parseInt(stats.story_count) || 0,
      characterCount: parseInt(stats.character_count) || 0,
      lightingCount: parseInt(stats.lighting_count) || 0,
      followingCount: parseInt(stats.following_count) || 0,
      followersCount: parseInt(stats.followers_count) || 0,
    };
  }

  /**
   * 获取用户完整资料（根据权限过滤）
   */
  async getUserProfile(
    userId: string,
    viewerId?: string,
  ): Promise<UserProfileDto> {
    const user = await this.findById(userId);
    const isOwner = viewerId === userId;

    // 基础资料
    const profile: UserProfileDto = {
      id: user.id,
      userNumber: user.userNumber,
      nickname: user.nickname,
      username: user.username,
      avatarUrl: user.avatarUrl,
      profileDisplaySettings: user.profileDisplaySettings,
      createdAt: user.createdAt,
    };

    // 根据展示设置决定是否显示敏感信息
    if (isOwner || user.profileDisplaySettings.showBio) {
      profile.bio = user.bio;
    }

    if (isOwner || user.profileDisplaySettings.showBirthday) {
      profile.birthDate = user.birthDate;
    }

    // 获取统计数据
    if (isOwner || user.profileDisplaySettings.showStatistics) {
      profile.statistics = await this.getUserStatistics(userId);
    }

    return profile;
  }

  /**
   * 检查用户是否为朋友
   */
  async isFriend(userId: string, targetId: string): Promise<boolean> {
    const relationship = await this.relationshipRepository.findOne({
      where: { userId, relatedUserId: targetId, status: 1 },
    });
    return !!relationship;
  }

  /**
   * 添加朋友关系
   */
  async addFriend(userId: string, friendId: string): Promise<void> {
    if (userId === friendId) {
      throw new ForbiddenException("不能添加自己为朋友");
    }

    // 检查朋友是否存在
    await this.findById(friendId);

    // 检查是否已经是朋友
    const existingRelationship = await this.relationshipRepository.findOne({
      where: { userId, relatedUserId: friendId },
    });

    if (existingRelationship) {
      if (existingRelationship.status === 1) {
        throw new ConflictException("已经是朋友关系");
      } else {
        // 重新激活关系
        await this.relationshipRepository.update(existingRelationship.id, {
          status: 1,
        });
      }
    } else {
      // 创建新关系
      await this.relationshipRepository.save({
        userId,
        relatedUserId: friendId,
        relationType: "friend",
        intimacyLevel: 1,
        status: 1,
      });
    }
  }

  /**
   * 移除朋友关系
   */
  async removeFriend(userId: string, friendId: string): Promise<void> {
    const relationship = await this.relationshipRepository.findOne({
      where: { userId, relatedUserId: friendId, status: 1 },
    });

    if (!relationship) {
      throw new NotFoundException("朋友关系不存在");
    }

    await this.relationshipRepository.update(relationship.id, { status: 2 });
  }

  /**
   * 获取朋友列表
   */
  async getFriendsList(
    userId: string,
    page: number = 1,
    limit: number = 20,
  ): Promise<{
    friends: User[];
    total: number;
    hasNext: boolean;
  }> {
    const offset = (page - 1) * limit;

    const [relationships, total] =
      await this.relationshipRepository.findAndCount({
        where: { userId, status: 1 },
        relations: ["relatedUser"],
        skip: offset,
        take: limit,
        order: { createdAt: "DESC" },
      });

    const friends = relationships.map((rel) => ({
      id: rel.relatedUser.id,
      userNumber: rel.relatedUser.userNumber,
      nickname: rel.relatedUser.nickname,
      username: rel.relatedUser.username,
      avatarUrl: rel.relatedUser.avatarUrl,
      createdAt: rel.relatedUser.createdAt,
    })) as User[];

    return {
      friends,
      total,
      hasNext: total > offset + limit,
    };
  }

  /**
   * 删除用户（软删除）
   */
  async deleteUser(userId: string): Promise<void> {
    await this.userRepository.update(userId, {
      status: "deleted",
      isActive: false,
    });
  }

  /**
   * 用户验证
   * 支持多种验证方式：手机号、邮箱、身份认证、安全验证
   */
  async verifyUser(
    userId: string,
    verificationData: {
      verificationType: "phone" | "email" | "identity" | "security";
      verificationCode?: string;
      phone?: string;
      email?: string;
      identityNumber?: string;
      additionalData?: Record<string, unknown>;
    },
  ): Promise<{
    verificationType: string;
    verificationStatus: string;
    verifiedAt: string;
    nextSteps: string[];
  }> {
    const user = await this.findById(userId);

    if (!user) {
      throw new NotFoundException("用户不存在");
    }

    const now = new Date().toISOString();

    // 根据验证类型执行不同的验证逻辑
    switch (verificationData.verificationType) {
      case "phone":
        return await this.verifyPhone(user, verificationData, now);

      case "email":
        return await this.verifyEmail(user, verificationData, now);

      case "identity":
        return await this.verifyIdentity(user, verificationData, now);

      case "security":
        return await this.verifySecurity(user, verificationData, now);

      default:
        throw new ConflictException("不支持的验证类型");
    }
  }

  /**
   * 手机号验证
   */
  private async verifyPhone(
    user: User,
    verificationData: { phone?: string; verificationCode?: string },
    now: string,
  ): Promise<{
    verificationType: string;
    verificationStatus: string;
    verifiedAt: string;
    nextSteps: string[];
  }> {
    if (!verificationData.phone || !verificationData.verificationCode) {
      throw new ConflictException("手机号和验证码不能为空");
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(verificationData.phone)) {
      throw new ConflictException("手机号格式不正确");
    }

    // 验证码验证逻辑（这里简化实现）
    if (verificationData.verificationCode.length !== 6) {
      throw new ConflictException("验证码格式不正确");
    }

    // 更新用户手机号验证状态
    await this.userRepository.update(user.id, {
      phone: verificationData.phone,
      phoneVerifiedAt: new Date(),
    });

    return {
      verificationType: "phone",
      verificationStatus: "verified",
      verifiedAt: now,
      nextSteps: ["手机号验证成功", "可以继续使用其他功能"],
    };
  }

  /**
   * 邮箱验证
   */
  private async verifyEmail(
    user: User,
    verificationData: { email?: string; verificationCode?: string },
    now: string,
  ): Promise<{
    verificationType: string;
    verificationStatus: string;
    verifiedAt: string;
    nextSteps: string[];
  }> {
    if (!verificationData.email || !verificationData.verificationCode) {
      throw new ConflictException("邮箱和验证码不能为空");
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(verificationData.email)) {
      throw new ConflictException("邮箱格式不正确");
    }

    // 验证码验证逻辑（这里简化实现）
    if (verificationData.verificationCode.length !== 6) {
      throw new ConflictException("验证码格式不正确");
    }

    // 更新用户邮箱验证状态
    await this.userRepository.update(user.id, {
      email: verificationData.email,
      emailVerifiedAt: new Date(),
    });

    return {
      verificationType: "email",
      verificationStatus: "verified",
      verifiedAt: now,
      nextSteps: ["邮箱验证成功", "可以用邮箱进行登录和找回密码"],
    };
  }

  /**
   * 身份认证验证
   */
  private async verifyIdentity(
    user: User,
    verificationData: { identityNumber?: string },
    now: string,
  ): Promise<{
    verificationType: string;
    verificationStatus: string;
    verifiedAt: string;
    nextSteps: string[];
  }> {
    if (!verificationData.identityNumber) {
      throw new ConflictException("身份证号码不能为空");
    }

    // 身份证号码格式验证
    const idCardRegex =
      /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
    if (!idCardRegex.test(verificationData.identityNumber)) {
      throw new ConflictException("身份证号码格式不正确");
    }

    // 检查身份证号是否已被其他用户使用
    const existingUser = await this.userRepository.findOne({
      where: {
        identityNumber: verificationData.identityNumber,
      },
    });

    if (existingUser && existingUser.id !== user.id) {
      throw new ConflictException("该身份证号已被其他用户使用");
    }

    // 更新用户身份认证状态
    await this.userRepository.update(user.id, {
      identityNumber: verificationData.identityNumber,
      identityVerifiedAt: new Date(),
    });

    return {
      verificationType: "identity",
      verificationStatus: "verified",
      verifiedAt: now,
      nextSteps: ["身份认证成功", "账户安全等级提升", "可以使用高级功能"],
    };
  }

  /**
   * 安全验证
   */
  private async verifySecurity(
    user: User,
    verificationData: { additionalData?: Record<string, unknown> },
    now: string,
  ): Promise<{
    verificationType: string;
    verificationStatus: string;
    verifiedAt: string;
    nextSteps: string[];
  }> {
    // 安全验证可以包含多种方式：密码、生物识别、硬件令牌等
    const securityVerifications = [];

    if (verificationData.additionalData?.password) {
      // 密码验证逻辑（这里简化实现）
      securityVerifications.push("password");
    }

    if (verificationData.additionalData?.biometric) {
      // 生物识别验证逻辑
      securityVerifications.push("biometric");
    }

    if (verificationData.additionalData?.twoFactor) {
      // 双因子认证验证逻辑
      securityVerifications.push("twoFactor");
    }

    if (securityVerifications.length === 0) {
      throw new ConflictException("至少需要一种安全验证方式");
    }

    // 更新用户安全验证状态
    await this.userRepository.update(user.id, {
      securityLevel: securityVerifications.length,
      lastSecurityVerification: new Date(),
    });

    return {
      verificationType: "security",
      verificationStatus: "verified",
      verifiedAt: now,
      nextSteps: [
        "安全验证成功",
        `已启用 ${securityVerifications.length} 种安全验证方式`,
        "账户安全性大幅提升",
      ],
    };
  }

  /**
   * 生成6位有故事号
   */
  async generateUserNumber(): Promise<string> {
    const MIN_NUMBER = 200001;
    const MAX_NUMBER = 999999;

    // 获取最后一个分配的号码
    const lastUser = await this.userRepository.findOne({
      where: { userNumber: Raw((alias) => `${alias} IS NOT NULL`) },
      order: { userNumber: "DESC" },
    });

    let nextNumber = lastUser ? parseInt(lastUser.userNumber) + 1 : MIN_NUMBER;

    // 确保在范围内
    if (nextNumber > MAX_NUMBER) {
      throw new ConflictException("用户号码已用完");
    }

    // 跳过敏感数字组合
    while (this.isSensitiveNumber(nextNumber)) {
      nextNumber++;
      if (nextNumber > MAX_NUMBER) {
        throw new ConflictException("用户号码已用完");
      }
    }

    // 检查号码是否已被使用
    const existingUser = await this.userRepository.findOne({
      where: { userNumber: nextNumber.toString().padStart(6, "0") },
    });

    if (existingUser) {
      // 递归查找下一个可用号码
      return this.generateUserNumber();
    }

    return nextNumber.toString().padStart(6, "0");
  }

  /**
   * 检查是否为敏感数字
   */
  private isSensitiveNumber(number: number): boolean {
    const str = number.toString();
    const sensitivePatterns = [
      "666",
      "888",
      "000",
      "123",
      "111",
      "222",
      "333",
      "444",
      "555",
      "777",
      "999",
    ];
    return sensitivePatterns.some((pattern) => str.includes(pattern));
  }
}
