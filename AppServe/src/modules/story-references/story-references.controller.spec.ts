/* eslint-disable @typescript-eslint/no-explicit-any, @typescript-eslint/no-unused-vars */
import type { TestingModule } from "@nestjs/testing";
import { Test } from "@nestjs/testing";
import { HttpStatus } from "@nestjs/common";

import { StoryReferencesController } from "./story-references.controller";
import { StoryReferencesService } from "./story-references.service";
import type { CreateReferenceDto } from "./dto/create-reference.dto";
import type { CreateCollectionDto } from "./dto/create-collection.dto";
import type {
  ReferenceQueryDto,
  CollectionQueryDto,
} from "./dto/reference-query.dto";
import {
  ReferenceStatus,
  ReferenceVisibility,
} from "./entities/story-reference.entity";
import {
  CollectionType,
  CollectionStatus,
  CollectionVisibility,
} from "./entities/reference-collection.entity";

describe("StoryReferencesController - 企业级单元测试", () => {
  let controller: StoryReferencesController;
  let mockService: jest.Mocked<StoryReferencesService>;

  // Mock数据 - 故事引用
  const mockReference = {
    id: "test-reference-id",
    userId: "test-user-id",
    userName: "测试用户",
    storyId: "test-story-id",
    storyTitle: "测试故事",
    storyAuthorName: "测试作者",
    collectionId: "test-collection-id",
    collectionName: "测试集合",
    title: "测试引用",
    annotation: "测试评注",
    quotedSegments: [
      {
        chapterId: "chapter-1",
        startPosition: 0,
        endPosition: 100,
        text: "测试引用文本",
        note: "测试片段备注",
      },
    ],
    status: ReferenceStatus.ACTIVE,
    visibility: ReferenceVisibility.PRIVATE,
    tags: ["测试", "引用"],
    importance: 3,
    likesCount: 5,
    commentsCount: 2,
    storySnapshot: {
      title: "测试故事",
      summary: "测试故事摘要",
      authorId: "author-id",
      authorName: "测试作者",
      publishedAt: new Date("2025-01-01"),
      chapterCount: 10,
    },
    lastReadAt: new Date(),
    readCount: 10,
    metadata: {},
    createdAt: new Date(),
    updatedAt: new Date(),
    isLiked: false,
    canEdit: true,
    canDelete: true,
  };

  // Mock数据 - 引用集合
  const mockCollection = {
    id: "test-collection-id",
    name: "测试集合",
    description: "测试集合描述",
    ownerId: "test-user-id",
    ownerName: "测试用户",
    ownerAvatar: "https://example.com/avatar.jpg",
    collectionType: CollectionType.GENERAL,
    status: CollectionStatus.ACTIVE,
    visibility: CollectionVisibility.PRIVATE,
    coverImage: "https://example.com/cover.jpg",
    colorTheme: "blue",
    tags: ["测试", "集合"],
    referenceCount: 5,
    followersCount: 10,
    likesCount: 15,
    sortOrder: 0,
    isFeatured: false,
    allowCollaboration: true,
    settings: {
      autoAddSimilar: false,
      notifyOnNewReference: true,
      allowPublicContribution: false,
      moderationRequired: false,
    },
    lastContentUpdate: new Date(),
    statistics: {
      totalViews: 100,
      monthlyViews: 20,
      totalShares: 5,
      averageRating: 4.5,
      topTags: ["测试", "集合"],
    },
    metadata: {},
    createdAt: new Date(),
    updatedAt: new Date(),
    isFollowing: false,
    isLiked: false,
    canEdit: true,
    canContribute: true,
    recentReferences: [
      {
        id: "ref-1",
        title: "最新引用1",
        storyTitle: "故事1",
        createdAt: new Date(),
      },
    ],
  };

  // Mock数据 - 统计信息
  const mockStats = {
    userId: "test-user-id",
    totalReferences: 10,
    totalCollections: 3,
    publicReferences: 5,
    privateReferences: 5,
    totalLikes: 25,
    totalViews: 100,
    totalShares: 15,
    collectionStats: [
      {
        collectionType: CollectionType.GENERAL,
        count: 2,
        referenceCount: 8,
      },
    ],
    importanceStats: [
      { importance: 5, count: 2, percentage: 20 },
      { importance: 4, count: 3, percentage: 30 },
    ],
    topTags: [{ tag: "测试", count: 5, recentUse: new Date() }],
    topCollections: [
      {
        collectionId: "collection-1",
        collectionName: "测试集合1",
        referenceCount: 5,
        likesCount: 10,
        lastUpdate: new Date(),
      },
    ],
    recentActivity: [
      { type: "reference_created" as const, date: new Date(), count: 3 },
    ],
    readingStats: {
      totalReadCount: 50,
      averageImportance: 3.5,
      mostReadReference: {
        referenceId: "ref-1",
        title: "最常读的引用",
        readCount: 15,
      },
      readingTrend: [{ date: new Date(), readCount: 5 }],
    },
  };

  // Mock用户请求
  const mockRequest = {
    user: {
      id: "test-user-id",
      username: "testuser",
      role: "user",
    },
  };

  beforeEach(async () => {
    const mockServiceMethods = {
      createReference: jest.fn(),
      getUserReferences: jest.fn(),
      getReferenceById: jest.fn(),
      createCollection: jest.fn(),
      getUserCollections: jest.fn(),
      getReferenceStats: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [StoryReferencesController],
      providers: [
        {
          provide: StoryReferencesService,
          useValue: mockServiceMethods,
        },
      ],
    }).compile();

    controller = module.get<StoryReferencesController>(
      StoryReferencesController,
    );
    mockService = module.get(StoryReferencesService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("POST /references - 创建故事引用", () => {
    const createReferenceDto: CreateReferenceDto = {
      storyId: "test-story-id",
      collectionId: "test-collection-id",
      title: "测试引用",
      annotation: "测试评注",
      quotedSegments: [
        {
          chapterId: "chapter-1",
          startPosition: 0,
          endPosition: 100,
          text: "测试引用文本",
          note: "测试片段备注",
        },
      ],
      visibility: ReferenceVisibility.PRIVATE,
      tags: ["测试", "引用"],
      importance: 3,
      metadata: {},
    };

    it("应该成功创建故事引用", async () => {
      // Arrange
      mockService.createReference.mockResolvedValue(mockReference);

      // Act
      const result = await controller.createReference(
        mockRequest,
        createReferenceDto,
      );

      // Assert
      expect(result).toEqual({
        success: true,
        message: "引用创建成功",
        data: mockReference,
      });
      expect(mockService.createReference).toHaveBeenCalledWith(
        mockRequest.user.id,
        createReferenceDto,
      );
    });

    it("应该处理创建引用时的服务异常", async () => {
      // Arrange
      mockService.createReference.mockRejectedValue(new Error("创建失败"));

      // Act & Assert
      await expect(
        controller.createReference(mockRequest, createReferenceDto),
      ).rejects.toThrow("创建失败");
      expect(mockService.createReference).toHaveBeenCalledWith(
        mockRequest.user.id,
        createReferenceDto,
      );
    });

    it("应该验证必需的字段", async () => {
      // Arrange
      const incompleteDto = {
        storyId: "test-story-id",
        // 缺少title字段
      } as CreateReferenceDto;

      mockService.createReference.mockResolvedValue(mockReference);

      // Act
      const result = await controller.createReference(
        mockRequest,
        incompleteDto,
      );

      // Assert
      expect(mockService.createReference).toHaveBeenCalledWith(
        mockRequest.user.id,
        incompleteDto,
      );
    });

    it("应该正确传递用户ID", async () => {
      // Arrange
      const customUserId = "custom-user-id";
      const customRequest = {
        user: { id: customUserId, username: "customuser", role: "user" },
      };
      mockService.createReference.mockResolvedValue(mockReference);

      // Act
      await controller.createReference(customRequest, createReferenceDto);

      // Assert
      expect(mockService.createReference).toHaveBeenCalledWith(
        customUserId,
        createReferenceDto,
      );
    });
  });

  describe("GET /references - 获取用户引用列表", () => {
    const mockPaginatedResult = {
      data: [mockReference],
      total: 1,
      page: 1,
      limit: 10,
      totalPages: 1,
    };

    it("应该成功获取引用列表", async () => {
      // Arrange
      const query: ReferenceQueryDto = {
        page: 1,
        limit: 10,
        status: ReferenceStatus.ACTIVE,
      };
      mockService.getUserReferences.mockResolvedValue(mockPaginatedResult);

      // Act
      const result = await controller.getUserReferences(mockRequest, query);

      // Assert
      expect(result).toEqual({
        success: true,
        message: "获取引用列表成功",
        data: mockPaginatedResult.data,
        pagination: {
          total: 1,
          page: 1,
          limit: 10,
          totalPages: 1,
        },
      });
      expect(mockService.getUserReferences).toHaveBeenCalledWith(
        mockRequest.user.id,
        query,
      );
    });

    it("应该处理分页参数", async () => {
      // Arrange
      const query: ReferenceQueryDto = {
        page: 2,
        limit: 20,
      };
      const paginatedResult = {
        data: [mockReference],
        total: 25,
        page: 2,
        limit: 20,
        totalPages: 2,
      };
      mockService.getUserReferences.mockResolvedValue(paginatedResult);

      // Act
      const result = await controller.getUserReferences(mockRequest, query);

      // Assert
      expect(result.pagination).toEqual({
        total: 25,
        page: 2,
        limit: 20,
        totalPages: 2,
      });
      expect(mockService.getUserReferences).toHaveBeenCalledWith(
        mockRequest.user.id,
        query,
      );
    });

    it("应该处理筛选参数", async () => {
      // Arrange
      const query: ReferenceQueryDto = {
        page: 1,
        limit: 10,
        collectionId: "test-collection-id",
        status: ReferenceStatus.ACTIVE,
        visibility: ReferenceVisibility.PUBLIC,
        tags: ["测试", "引用"],
        minImportance: 4,
        search: "测试关键词",
        sortBy: "importance",
        sortOrder: "DESC",
      };
      mockService.getUserReferences.mockResolvedValue(mockPaginatedResult);

      // Act
      const result = await controller.getUserReferences(mockRequest, query);

      // Assert
      expect(mockService.getUserReferences).toHaveBeenCalledWith(
        mockRequest.user.id,
        query,
      );
      expect(result.success).toBe(true);
    });

    it("应该处理空结果", async () => {
      // Arrange
      const query: ReferenceQueryDto = { page: 1, limit: 10 };
      const emptyResult = {
        data: [],
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0,
      };
      mockService.getUserReferences.mockResolvedValue(emptyResult);

      // Act
      const result = await controller.getUserReferences(mockRequest, query);

      // Assert
      expect(result.data).toEqual([]);
      expect(result.pagination.total).toBe(0);
    });

    it("应该处理服务异常", async () => {
      // Arrange
      const query: ReferenceQueryDto = { page: 1, limit: 10 };
      mockService.getUserReferences.mockRejectedValue(new Error("查询失败"));

      // Act & Assert
      await expect(
        controller.getUserReferences(mockRequest, query),
      ).rejects.toThrow("查询失败");
    });
  });

  describe("GET /references/:id - 获取引用详情", () => {
    it("应该成功获取引用详情", async () => {
      // Arrange
      const referenceId = "test-reference-id";
      mockService.getReferenceById.mockResolvedValue(mockReference);

      // Act
      const result = await controller.getReferenceById(
        mockRequest,
        referenceId,
      );

      // Assert
      expect(result).toEqual({
        success: true,
        message: "获取引用详情成功",
        data: mockReference,
      });
      expect(mockService.getReferenceById).toHaveBeenCalledWith(
        referenceId,
        mockRequest.user.id,
      );
    });

    it("应该处理引用不存在的情况", async () => {
      // Arrange
      const referenceId = "non-existent-id";
      mockService.getReferenceById.mockRejectedValue(new Error("引用不存在"));

      // Act & Assert
      await expect(
        controller.getReferenceById(mockRequest, referenceId),
      ).rejects.toThrow("引用不存在");
      expect(mockService.getReferenceById).toHaveBeenCalledWith(
        referenceId,
        mockRequest.user.id,
      );
    });

    it("应该正确传递用户ID以进行权限检查", async () => {
      // Arrange
      const referenceId = "test-reference-id";
      const customUserId = "custom-user-id";
      const customRequest = {
        user: { id: customUserId, username: "customuser", role: "user" },
      };
      mockService.getReferenceById.mockResolvedValue(mockReference);

      // Act
      await controller.getReferenceById(customRequest, referenceId);

      // Assert
      expect(mockService.getReferenceById).toHaveBeenCalledWith(
        referenceId,
        customUserId,
      );
    });

    it("应该包含用户相关状态信息", async () => {
      // Arrange
      const referenceId = "test-reference-id";
      const referenceWithUserState = {
        ...mockReference,
        isLiked: true,
        canEdit: true,
        canDelete: false,
      };
      mockService.getReferenceById.mockResolvedValue(referenceWithUserState);

      // Act
      const result = await controller.getReferenceById(
        mockRequest,
        referenceId,
      );

      // Assert
      expect(result.data.isLiked).toBe(true);
      expect(result.data.canEdit).toBe(true);
      expect(result.data.canDelete).toBe(false);
    });
  });

  describe("POST /collections - 创建引用集合", () => {
    const createCollectionDto: CreateCollectionDto = {
      name: "测试集合",
      description: "测试集合描述",
      collectionType: CollectionType.GENERAL,
      visibility: CollectionVisibility.PRIVATE,
      coverImage: "https://example.com/cover.jpg",
      colorTheme: "blue",
      tags: ["测试", "集合"],
      sortOrder: 0,
      allowCollaboration: true,
      settings: {
        autoAddSimilar: false,
        notifyOnNewReference: true,
        allowPublicContribution: false,
        moderationRequired: false,
      },
      metadata: {},
    };

    it("应该成功创建引用集合", async () => {
      // Arrange
      mockService.createCollection.mockResolvedValue(mockCollection);

      // Act
      const result = await controller.createCollection(
        mockRequest,
        createCollectionDto,
      );

      // Assert
      expect(result).toEqual({
        success: true,
        message: "集合创建成功",
        data: mockCollection,
      });
      expect(mockService.createCollection).toHaveBeenCalledWith(
        mockRequest.user.id,
        createCollectionDto,
      );
    });

    it("应该处理创建集合时的服务异常", async () => {
      // Arrange
      mockService.createCollection.mockRejectedValue(new Error("创建集合失败"));

      // Act & Assert
      await expect(
        controller.createCollection(mockRequest, createCollectionDto),
      ).rejects.toThrow("创建集合失败");
      expect(mockService.createCollection).toHaveBeenCalledWith(
        mockRequest.user.id,
        createCollectionDto,
      );
    });

    it("应该验证必需的字段", async () => {
      // Arrange
      const incompleteDto = {
        // 缺少name字段
        description: "测试集合描述",
      } as CreateCollectionDto;

      mockService.createCollection.mockResolvedValue(mockCollection);

      // Act
      const result = await controller.createCollection(
        mockRequest,
        incompleteDto,
      );

      // Assert
      expect(mockService.createCollection).toHaveBeenCalledWith(
        mockRequest.user.id,
        incompleteDto,
      );
    });

    it("应该正确传递集合设置", async () => {
      // Arrange
      const customSettings = {
        autoAddSimilar: true,
        notifyOnNewReference: false,
        allowPublicContribution: true,
        moderationRequired: true,
      };
      const dtoWithSettings = {
        ...createCollectionDto,
        settings: customSettings,
      };
      mockService.createCollection.mockResolvedValue({
        ...mockCollection,
        settings: customSettings,
      });

      // Act
      const result = await controller.createCollection(
        mockRequest,
        dtoWithSettings,
      );

      // Assert
      expect(mockService.createCollection).toHaveBeenCalledWith(
        mockRequest.user.id,
        dtoWithSettings,
      );
      expect(result.data.settings).toEqual(customSettings);
    });
  });

  describe("GET /collections - 获取用户集合列表", () => {
    const mockCollectionPaginatedResult = {
      data: [mockCollection],
      total: 1,
      page: 1,
      limit: 10,
      totalPages: 1,
    };

    it("应该成功获取集合列表", async () => {
      // Arrange
      const query: CollectionQueryDto = {
        page: 1,
        limit: 10,
        status: CollectionStatus.ACTIVE,
      };
      mockService.getUserCollections.mockResolvedValue(
        mockCollectionPaginatedResult,
      );

      // Act
      const result = await controller.getUserCollections(mockRequest, query);

      // Assert
      expect(result).toEqual({
        success: true,
        message: "获取集合列表成功",
        data: mockCollectionPaginatedResult.data,
        pagination: {
          total: 1,
          page: 1,
          limit: 10,
          totalPages: 1,
        },
      });
      expect(mockService.getUserCollections).toHaveBeenCalledWith(
        mockRequest.user.id,
        query,
      );
    });

    it("应该处理集合类型筛选", async () => {
      // Arrange
      const query: CollectionQueryDto = {
        page: 1,
        limit: 10,
        collectionType: CollectionType.THEMATIC,
        visibility: CollectionVisibility.PUBLIC,
      };
      mockService.getUserCollections.mockResolvedValue(
        mockCollectionPaginatedResult,
      );

      // Act
      const result = await controller.getUserCollections(mockRequest, query);

      // Assert
      expect(mockService.getUserCollections).toHaveBeenCalledWith(
        mockRequest.user.id,
        query,
      );
      expect(result.success).toBe(true);
    });

    it("应该处理标签和精选筛选", async () => {
      // Arrange
      const query: CollectionQueryDto = {
        page: 1,
        limit: 10,
        tags: ["科幻", "悬疑"],
        isFeatured: true,
        search: "测试搜索",
        sortBy: "referenceCount",
        sortOrder: "DESC",
      };
      mockService.getUserCollections.mockResolvedValue(
        mockCollectionPaginatedResult,
      );

      // Act
      const result = await controller.getUserCollections(mockRequest, query);

      // Assert
      expect(mockService.getUserCollections).toHaveBeenCalledWith(
        mockRequest.user.id,
        query,
      );
      expect(result.success).toBe(true);
    });

    it("应该处理空集合结果", async () => {
      // Arrange
      const query: CollectionQueryDto = { page: 1, limit: 10 };
      const emptyResult = {
        data: [],
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0,
      };
      mockService.getUserCollections.mockResolvedValue(emptyResult);

      // Act
      const result = await controller.getUserCollections(mockRequest, query);

      // Assert
      expect(result.data).toEqual([]);
      expect(result.pagination.total).toBe(0);
    });

    it("应该处理服务异常", async () => {
      // Arrange
      const query: CollectionQueryDto = { page: 1, limit: 10 };
      mockService.getUserCollections.mockRejectedValue(
        new Error("查询集合失败"),
      );

      // Act & Assert
      await expect(
        controller.getUserCollections(mockRequest, query),
      ).rejects.toThrow("查询集合失败");
    });
  });

  describe("GET /stats - 获取引用统计信息", () => {
    it("应该成功获取统计信息", async () => {
      // Arrange
      mockService.getReferenceStats.mockResolvedValue(mockStats);

      // Act
      const result = await controller.getReferenceStats(mockRequest);

      // Assert
      expect(result).toEqual({
        success: true,
        message: "获取统计信息成功",
        data: mockStats,
      });
      expect(mockService.getReferenceStats).toHaveBeenCalledWith(
        mockRequest.user.id,
      );
    });

    it("应该包含完整的统计数据结构", async () => {
      // Arrange
      const detailedStats = {
        ...mockStats,
        collectionStats: [
          {
            collectionType: CollectionType.GENERAL,
            count: 2,
            referenceCount: 8,
          },
          {
            collectionType: CollectionType.THEMATIC,
            count: 1,
            referenceCount: 5,
          },
          {
            collectionType: CollectionType.RESEARCH,
            count: 1,
            referenceCount: 2,
          },
        ],
        importanceStats: [
          { importance: 5, count: 2, percentage: 20 },
          { importance: 4, count: 3, percentage: 30 },
          { importance: 3, count: 4, percentage: 40 },
          { importance: 2, count: 1, percentage: 10 },
        ],
        topTags: [
          { tag: "科幻", count: 8, recentUse: new Date() },
          { tag: "悬疑", count: 5, recentUse: new Date() },
          { tag: "历史", count: 3, recentUse: new Date() },
        ],
      };
      mockService.getReferenceStats.mockResolvedValue(detailedStats);

      // Act
      const result = await controller.getReferenceStats(mockRequest);

      // Assert
      expect(result.data.collectionStats).toHaveLength(3);
      expect(result.data.importanceStats).toHaveLength(4);
      expect(result.data.topTags).toHaveLength(3);
      expect(result.data.totalReferences).toBe(mockStats.totalReferences);
      expect(result.data.totalCollections).toBe(mockStats.totalCollections);
    });

    it("应该处理统计数据为零的情况", async () => {
      // Arrange
      const emptyStats = {
        ...mockStats,
        totalReferences: 0,
        totalCollections: 0,
        publicReferences: 0,
        privateReferences: 0,
        collectionStats: [],
        importanceStats: [],
        topTags: [],
        topCollections: [],
        recentActivity: [],
      };
      mockService.getReferenceStats.mockResolvedValue(emptyStats);

      // Act
      const result = await controller.getReferenceStats(mockRequest);

      // Assert
      expect(result.data.totalReferences).toBe(0);
      expect(result.data.totalCollections).toBe(0);
      expect(result.data.collectionStats).toHaveLength(0);
      expect(result.data.importanceStats).toHaveLength(0);
      expect(result.data.topTags).toHaveLength(0);
    });

    it("应该包含阅读统计信息", async () => {
      // Arrange
      const statsWithReading = {
        ...mockStats,
        readingStats: {
          totalReadCount: 150,
          averageImportance: 4.2,
          mostReadReference: {
            referenceId: "most-read-ref",
            title: "最受欢迎的引用",
            readCount: 25,
          },
          readingTrend: [
            { date: new Date("2025-01-01"), readCount: 10 },
            { date: new Date("2025-01-02"), readCount: 15 },
            { date: new Date("2025-01-03"), readCount: 8 },
          ],
        },
      };
      mockService.getReferenceStats.mockResolvedValue(statsWithReading);

      // Act
      const result = await controller.getReferenceStats(mockRequest);

      // Assert
      expect(result.data.readingStats.totalReadCount).toBe(150);
      expect(result.data.readingStats.averageImportance).toBe(4.2);
      expect(result.data.readingStats.mostReadReference?.title).toBe(
        "最受欢迎的引用",
      );
      expect(result.data.readingStats.readingTrend).toHaveLength(3);
    });

    it("应该处理服务异常", async () => {
      // Arrange
      mockService.getReferenceStats.mockRejectedValue(
        new Error("获取统计失败"),
      );

      // Act & Assert
      await expect(controller.getReferenceStats(mockRequest)).rejects.toThrow(
        "获取统计失败",
      );
      expect(mockService.getReferenceStats).toHaveBeenCalledWith(
        mockRequest.user.id,
      );
    });

    it("应该正确传递用户ID", async () => {
      // Arrange
      const customUserId = "custom-user-id";
      const customRequest = {
        user: { id: customUserId, username: "customuser", role: "user" },
      };
      mockService.getReferenceStats.mockResolvedValue(mockStats);

      // Act
      await controller.getReferenceStats(customRequest);

      // Assert
      expect(mockService.getReferenceStats).toHaveBeenCalledWith(customUserId);
    });
  });

  describe("错误处理和边界条件", () => {
    it("应该处理无效的用户ID", async () => {
      // Arrange
      const invalidRequest = {
        user: { id: "", username: "", role: "user" },
      };
      mockService.createReference.mockRejectedValue(new Error("无效用户"));

      // Act & Assert
      await expect(
        controller.createReference(invalidRequest, {} as CreateReferenceDto),
      ).rejects.toThrow("无效用户");
    });

    it("应该处理无效的引用ID", async () => {
      // Arrange
      const invalidReferenceId = "invalid-id";
      mockService.getReferenceById.mockRejectedValue(new Error("无效的引用ID"));

      // Act & Assert
      await expect(
        controller.getReferenceById(mockRequest, invalidReferenceId),
      ).rejects.toThrow("无效的引用ID");
    });

    it("应该处理网络或数据库异常", async () => {
      // Arrange
      mockService.getUserReferences.mockRejectedValue(
        new Error("数据库连接失败"),
      );

      // Act & Assert
      await expect(
        controller.getUserReferences(mockRequest, { page: 1, limit: 10 }),
      ).rejects.toThrow("数据库连接失败");
    });

    it("应该处理权限不足的情况", async () => {
      // Arrange
      mockService.getReferenceById.mockRejectedValue(new Error("权限不足"));

      // Act & Assert
      await expect(
        controller.getReferenceById(mockRequest, "protected-reference-id"),
      ).rejects.toThrow("权限不足");
    });
  });

  describe("响应格式验证", () => {
    it("创建引用的响应应该符合ApiResponse格式", async () => {
      // Arrange
      mockService.createReference.mockResolvedValue(mockReference);

      // Act
      const result = await controller.createReference(mockRequest, {
        storyId: "test-story-id",
        title: "测试引用",
      } as CreateReferenceDto);

      // Assert
      expect(result).toHaveProperty("success", true);
      expect(result).toHaveProperty("message", "引用创建成功");
      expect(result).toHaveProperty("data");
      expect(result.data).toEqual(mockReference);
    });

    it("获取引用列表的响应应该包含分页信息", async () => {
      // Arrange
      const paginatedResult = {
        data: [mockReference],
        total: 25,
        page: 2,
        limit: 10,
        totalPages: 3,
      };
      mockService.getUserReferences.mockResolvedValue(paginatedResult);

      // Act
      const result = await controller.getUserReferences(mockRequest, {
        page: 2,
        limit: 10,
      });

      // Assert
      expect(result).toHaveProperty("success", true);
      expect(result).toHaveProperty("message", "获取引用列表成功");
      expect(result).toHaveProperty("data");
      expect(result).toHaveProperty("pagination");
      expect(result.pagination).toEqual({
        total: 25,
        page: 2,
        limit: 10,
        totalPages: 3,
      });
    });

    it("统计信息的响应应该包含完整的数据结构", async () => {
      // Arrange
      mockService.getReferenceStats.mockResolvedValue(mockStats);

      // Act
      const result = await controller.getReferenceStats(mockRequest);

      // Assert
      expect(result).toHaveProperty("success", true);
      expect(result).toHaveProperty("message", "获取统计信息成功");
      expect(result).toHaveProperty("data");
      expect(result.data).toHaveProperty("userId");
      expect(result.data).toHaveProperty("totalReferences");
      expect(result.data).toHaveProperty("totalCollections");
      expect(result.data).toHaveProperty("collectionStats");
      expect(result.data).toHaveProperty("importanceStats");
      expect(result.data).toHaveProperty("topTags");
      expect(result.data).toHaveProperty("readingStats");
    });
  });

  describe("业务逻辑验证", () => {
    it("应该验证创建引用时的业务逻辑", async () => {
      // Arrange
      const businessDto: CreateReferenceDto = {
        storyId: "story-id",
        title: "业务测试引用",
        importance: 5,
        visibility: ReferenceVisibility.PUBLIC,
        tags: ["重要", "公开"],
      };
      mockService.createReference.mockResolvedValue({
        ...mockReference,
        importance: 5,
        visibility: ReferenceVisibility.PUBLIC,
      });

      // Act
      const result = await controller.createReference(mockRequest, businessDto);

      // Assert
      expect(result.data.importance).toBe(5);
      expect(result.data.visibility).toBe(ReferenceVisibility.PUBLIC);
      expect(mockService.createReference).toHaveBeenCalledWith(
        mockRequest.user.id,
        businessDto,
      );
    });

    it("应该验证创建集合时的业务逻辑", async () => {
      // Arrange
      const businessDto: CreateCollectionDto = {
        name: "业务测试集合",
        collectionType: CollectionType.RESEARCH,
        visibility: CollectionVisibility.FRIENDS_ONLY,
        allowCollaboration: false,
      };
      mockService.createCollection.mockResolvedValue({
        ...mockCollection,
        collectionType: CollectionType.RESEARCH,
        visibility: CollectionVisibility.FRIENDS_ONLY,
        allowCollaboration: false,
      });

      // Act
      const result = await controller.createCollection(
        mockRequest,
        businessDto,
      );

      // Assert
      expect(result.data.collectionType).toBe(CollectionType.RESEARCH);
      expect(result.data.visibility).toBe(CollectionVisibility.FRIENDS_ONLY);
      expect(result.data.allowCollaboration).toBe(false);
    });

    it("应该正确处理查询参数的业务逻辑", async () => {
      // Arrange
      const businessQuery: ReferenceQueryDto = {
        page: 1,
        limit: 5,
        minImportance: 4,
        visibility: ReferenceVisibility.PUBLIC,
        tags: ["重要"],
        sortBy: "importance",
        sortOrder: "DESC",
      };
      mockService.getUserReferences.mockResolvedValue({
        data: [mockReference],
        total: 1,
        page: 1,
        limit: 5,
        totalPages: 1,
      });

      // Act
      await controller.getUserReferences(mockRequest, businessQuery);

      // Assert
      expect(mockService.getUserReferences).toHaveBeenCalledWith(
        mockRequest.user.id,
        businessQuery,
      );
    });
  });
});
