import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 创建新主题卡片
///
/// 按照设计稿重新实现，尺寸与其他主题卡片一致 (345.w × 240.h)
/// 只显示主标题，不显示副标题
class CreateNewThemeCard extends StatefulWidget {
  final VoidCallback? onTap;

  const CreateNewThemeCard({
    super.key,
    this.onTap,
  });

  @override
  State<CreateNewThemeCard> createState() => _CreateNewThemeCardState();
}

class _CreateNewThemeCardState extends State<CreateNewThemeCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    _animationController.forward();
  }

  void _handleTapUp(TapUpDetails details) {
    _animationController.reverse();
  }

  void _handleTapCancel() {
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      onTapDown: _handleTapDown,
      onTapUp: _handleTapUp,
      onTapCancel: _handleTapCancel,
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              width: 345.w,
              height: 240.h, // 与其他主题卡片一致
              decoration: const BoxDecoration(
                color: Color(0xFFFFFFFF), // 白色背景
                borderRadius: BorderRadius.all(Radius.circular(16)),
              ),
              clipBehavior: Clip.hardEdge,
              child: Stack(
                children: [
                  // 按设计稿比例计算装饰圆的位置和大小
                  // 设计稿: 690×390 → 实际: 345×240
                  // 宽度缩放: 345/690 = 0.5
                  // 高度缩放: 240/390 = 0.615

                  // Ellipse 501 - 右上角装饰圆
                  Positioned(
                    top: -65.h, // -106 * 0.615 ≈ -65
                    right: -65.w, // (560-690) * 0.5 = -65
                    child: Container(
                      width: 115.w, // 231 * 0.5 = 115.5
                      height: 115.w,
                      decoration: const BoxDecoration(
                        shape: BoxShape.circle,
                        color: Color(0xFFFFA6A6),
                      ),
                      child: BackdropFilter(
                        filter: ImageFilter.blur(sigmaX: 50.0, sigmaY: 50.0),
                        child: Container(
                          decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                            color: Color.fromRGBO(255, 166, 166, 0.6),
                          ),
                        ),
                      ),
                    ),
                  ),

                  // Ellipse 500 - 左下角装饰圆
                  Positioned(
                    bottom: -35.h, // (390-252-190) * 0.615 ≈ -35
                    left: -36.w, // -73 * 0.5 = -36.5
                    child: Container(
                      width: 95.w, // 190 * 0.5 = 95
                      height: 95.w,
                      decoration: const BoxDecoration(
                        shape: BoxShape.circle,
                        color: Color(0xFFFEE7E7),
                      ),
                      child: BackdropFilter(
                        filter: ImageFilter.blur(sigmaX: 37.5, sigmaY: 37.5),
                        child: Container(
                          decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                            color: Color.fromRGBO(254, 231, 231, 0.6),
                          ),
                        ),
                      ),
                    ),
                  ),

                  // icon-add-02 图标
                  Positioned(
                    left: 149.w, // 298 * 0.5 = 149
                    top: 44.h, // 72 * 0.615 ≈ 44
                    child: Container(
                      width: 47.w, // 94 * 0.5 = 47
                      height: 47.w,
                      decoration: BoxDecoration(
                        color: Colors.transparent,
                        borderRadius: BorderRadius.circular(8.r), // 16 * 0.5 = 8
                        border: Border.all(
                          color: const Color(0xFF151523),
                          width: 2.w, // 4 * 0.5 = 2
                        ),
                      ),
                      child: Stack(
                        children: [
                          // 垂直线
                          Positioned(
                            left: 22.5.w, // 居中
                            top: 13.5.h, // 居中
                            child: Container(
                              width: 2.w,
                              height: 20.h, // 40 * 0.5 = 20
                              color: const Color(0xFF151523),
                            ),
                          ),
                          // 水平线
                          Positioned(
                            left: 13.5.w, // 居中
                            top: 22.5.h, // 居中
                            child: Container(
                              width: 20.w, // 40 * 0.5 = 20
                              height: 2.h,
                              color: const Color(0xFF151523),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // 主标题 "创建新主题" - 只显示主标题，不显示副标题
                  Positioned(
                    left: 97.5.w, // 居中: (345-150)/2 = 97.5
                    top: 114.h, // 186 * 0.615 ≈ 114
                    child: SizedBox(
                      width: 150.w,
                      height: 26.h, // 42 * 0.615 ≈ 26
                      child: Text(
                        '创建新主题',
                        style: TextStyle(
                          fontFamily: 'PingFang SC',
                          fontSize: 18.sp, // 30 * 0.6 = 18，适配卡片尺寸
                          fontWeight: FontWeight.w500,
                          color: const Color(0xFF151523),
                          height: 1.4, // 调整行高
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
